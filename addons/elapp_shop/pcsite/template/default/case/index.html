{template 'common/_header'}
<div class="lynn-banner" style="background: {$casebanner['background']}">
    <div class="lynn-banner-info">
        <img src="{php echo pctomedia($casebanner['casebanner'])}" alt="">
    </div>
</div>
<div class="list-container" style="padding: 0 0 56px;">
    <div class="lynn-case-menu">
        <ul class="lynn-case-menu-ul">
            <li><a href="{php echo webUrl(array('case/index'))}" class="{if empty($_GPC['cate'])}active{/if}">全部</a></li>
            {loop $category $index $item}
            <li><a href="{php echo webUrl(array('case/index','cate'=>$item['id']))}" class="{if $_GPC['cate'] == $item['id']}active{/if}">{$item['name']}</a></li>
            {/loop}
        </ul>
    </div>
    <div class="lynn-case-list">
        <ul class="lynn-case-list-ul">
            {loop $articles $index $item}
            <li>
                <a href="javascript:void(0);">
                    <img src="{php echo pctomedia($item['thumb'])}" class="case-img" alt="{$item['title']}">
                    <p>{$item['title']}</p>
                    <div class="lynn-case-slider">
                        {$item['description']}
                    </div>
                    <span class="rcode">
                        <img src="{php echo pctomedia($item['qr'])}" width="142" height="142" alt="{$item['title']}">
                        <p>扫面二维码关注店铺</p>
                    </span>
                </a>
            </li>
            {/loop}
        </ul>
        {if !$articles}暂无内容！{/if}
        {$pager}
    </div>
</div>
{template 'common/_footer'}
<script type="text/javascript">
    $(function(){
        $(".lynn-case-list-ul li a").on("mouseenter",function(){
            $(this).find(".rcode").show()
        }).on("mouseleave",function(){
            $(this).find(".rcode").hide()
        })
    })
</script>
</body>
</html>