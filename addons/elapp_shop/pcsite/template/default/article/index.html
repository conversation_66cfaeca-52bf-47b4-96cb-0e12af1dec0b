{template 'common/_header'}
<div class="list-container">
    <div class="list-menu">
        <div class="list-menu-left">
            <ul class="list-menu-left-ul">
                <li><a href="{php echo webUrl(array('article/index'))}" class="{if empty($_GPC['cate'])}active{/if}">全部</a></li>
                {loop $category $index $item}
                <li><a href="{php echo webUrl(array('article/index','cate'=>$item['id']))}" class="{if $_GPC['cate'] == $item['id']}active{/if}">{$item['name']}</a></li>
                {/loop}
            </ul>
        </div>
        <div class="list-menu-right">
            <form action="" method="post">
                <input type="search" class="list-menu-search" name="keyword" placeholder="快速检索" value="">
            </form>
        </div>
        <div style="clear:both;"></div>
    </div>
    <div class="list-news">
        {if $articles}
        <ul class="list-news-ul">
            {loop $articles $index $item}
            <li onclick="javascript:window.open('{php echo webUrl(array('article/detail','id'=>$item['id']))}','','')">
                <div class="col-lg-1 list-news-li-left"><strong>{php echo date('d', $item['createtime'])}</strong><span>{php echo date('Y-m', $item['createtime'])}</span></div>
                <div class="col-lg-11 list-news-li-right">
                    <h3>{$item['title']}</h3>
                    <p>{php echo mb_substr(strip_tags(htmlspecialchars_decode($item['content'])),0,140,'utf-8')}...</p>
                </div>
                <div style="clear:both;"></div>
            </li>
            {/loop}
        </ul>
        {else}
        暂无内容！
        {/if}
        {$pager}
    </div>
</div>
{template 'common/_footer'}
</body>
</html>