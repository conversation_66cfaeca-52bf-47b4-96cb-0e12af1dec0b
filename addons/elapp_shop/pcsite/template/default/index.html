{template 'common/_header'}
<div id="myCarousel" class="carousel slide">
    <!-- 轮播（Carousel）指标 -->
    <ol class="carousel-indicators">
        {loop $banner $index $item}
        <li data-target="#myCarousel" data-slide-to="{$index}" class="{if $index == 0}active{/if}"></li>
        {/loop}
    </ol>
    <!-- 轮播（Carousel）项目 -->
    <div class="carousel-inner">
        {loop $banner $index $item}
        <div class="item {if $index == 0}active{/if}" {if $item['url']}onclick="window.open('{$item['url']}')"{/if}>
            <img src="{php echo pctomedia($item['thumb'])}" width="100%" alt="{$item['title']}">
        </div>
        {/loop}
    </div>
</div>
<script type="text/javascript">
    $('.carousel').carousel(5000)
</script>
{if $casus}
<div class="lynn-case-index">
    <div class="lynn-article-top">
        <i></i>
        <h3>案例展示</h3>
        <p>{$basicset['casesubtitle']}</p>
    </div>
    <div class="lynn-case-info">
        <div class="slider multiple-items">
            {loop $casus $index $item}
            <div class="slider-img">
                <img src="{php echo pctomedia($item['thumb'])}" alt="{$item['title']}">
                <span class="rcode">
                    <img src="{php echo pctomedia($item['qr'])}" width="142" height="142" alt="{$item['title']}">
                    <p>扫面二维码关注店铺</p>
                </span>
            </div>
            {/loop}
        </div>
        <div style="clear:both;"></div>
    </div>
</div>
{/if}
{if $companyArticle}
<div class="lynn-news">
    <div class="lynn-article-top">
        <i></i>
        <h3>新闻动态</h3>
        <p>{$basicset['newsubtitle']}</p>
    </div>
    <div class="lynn-news-index">
        <ul class="lynn-news-index-ul">
            {loop $companyArticle $index $item}
            <li onclick="javascript:window.open('{php echo webUrl(array('news/detail','id'=>$item['id']))}')">
                <div class="lynn-news-index-li-left">
                    <span>{php echo date('M',$item['createtime'])}</span><strong>{php echo date('d',$item['createtime'])}</strong>
                </div>
                <div class="lynn-news-index-li-right">
                    <div class="lynn-news-index-li-right-top">
                        <i>+</i>
                        <h3>{$item['title']}</h3>
                        <p><span>{$item['name']}</span></p>
                    </div>
                    <div class="lynn-news-index-li-right-bot">
                        {php echo mb_substr(strip_tags(htmlspecialchars_decode($item['content'])),0,30,'utf-8')}
                    </div>
                </div>
                <div style="clear:both;"></div>
            </li>
            {/loop}
        </ul>
        <div style="clear:both;"></div>
    </div>
</div>
{/if}
{if $article}
<div class="lynn-article-index">
    <div class="lynn-article-top">
        <i></i>
        <h3>最新文章</h3>
        <p>{$basicset['articlesubtitle']}</p>
    </div>
    <div class="lynn-article-bot">
        <ul class="lynn-article-bot-ul">
            {loop $article $index $item}
            <li onclick="javascript:window.open('{php echo webUrl(array('article/detail','id'=>$item['id']))}')">
                <h3>{$item['title']}</h3>
                <span>{$item['name']}</span>|<span>{php echo date('Y-m-d',$item['createtime'])}</span>
                <p>
                    {php echo mb_substr(strip_tags(htmlspecialchars_decode($item['content'])),0,60,'utf-8')}
                </p>
                <a href="{php echo webUrl(array('article/detail','id'=>$item['id']))}" style="display: none;">more>></a>
            </li>
            {/loop}
        </ul>
        <div style="clear:both;"></div>
    </div>
</div>
{/if}
{if $link}
<div class="lynn-link-index">
    <div class="lynn-article-top">
        <i></i>
        <h3>友情链接</h3>
        <p>{$basicset['linksubtitle']}</p>
    </div>
    <div class="lynn-flink-info">
        <div class="lynn-flink-info-top">
            <a href="javascript:void(0);" id="flink-prev"><</a>
            <a href="javascript:void(0);" id="flink-next">></a>
        </div>
        <div class="lynn-flink-info-bot">
            {loop $link $index $item}
            <a href="{$item['url']}" target="_blank"><img src="{php echo pctomedia($item['thumb'])}" alt="{$item['title']}"></a>
            {/loop}
        </div>
        <div style="clear:both;"></div>
    </div>
    <div style="clear:both;"></div>
</div>
{/if}
<div class="lynn-contact-index">
    <div class="lynn-article-top">
        <i></i>
        <h3>联系我们</h3>
        <p>{$basicset['contactsubtitle']}</p>
    </div>
    <div class="lynn-contact-bottom">
        <form id="guestbookform">
            <div class="lynn-contact-form-top">
                <input type="text" name="nickname" placeholder="姓名" class="lynn-input-name" value=""><input name="mobile" type="text" class="lynn-input-tel" placeholder="电话" value=""><input name="email" class="lynn-input-email" type="text" placeholder="邮箱" value="">
            </div>
            <div class="lynn-contact-form-bottom">
                <textarea name="content" id="" cols="30" rows="10" class="lynn-textarea" placeholder="留言内容"></textarea>
                <span style="color:red;font-size: 12px;display: inline-block;" id="content-span"></span>
            </div>
            <input type="button" id="subguestbook" class="lynn-btn-submit" value="提交留言">
        </form>
    </div>
</div>
<script type="text/javascript">
    $(function(){
        /*案例展示*/
        $('.multiple-items').slick({
            dots: true,
            infinite: true,
            slidesToShow: 4,
            slidesToScroll: 4
        });
        $(".slider-img").on("mouseenter",function(){
            $(this).find(".rcode").show()
        }).on("mouseleave",function(){
            $(this).find(".rcode").hide()
        })
        /*新闻动态*/
        $(".lynn-news-index-ul li").mouseenter(function(){
            $(this).addClass("active")
        }).mouseleave(function(){
            $(this).removeClass("active")
        })
        /*友情链接*/
        var flinkcount = $(".lynn-flink-info-bot a").length;
        if(flinkcount>=14){
            $("#flink-prev").on("click",function(){
                $(".lynn-flink-info-bot a:lt(2)").insertAfter(".lynn-flink-info-bot a:last")
            })
            $("#flink-next").on("click",function(){
                flinkcount = flinkcount -3;
                $(".lynn-flink-info-bot a:gt("+flinkcount+")").insertBefore(".lynn-flink-info-bot a:first")
            })
        }
        /*联系我们*/
        $("#subguestbook").on("click",function(e){
            e.preventDefault();
            $.post('{php echo webUrl(array("home/ajaxguestbook"))}',$("#guestbookform").serialize(),
                    function(data){
                        console.log(data);
                        if(data.status == 'success')
                        {
                            $(this).attr('placeholder',data.message)
                            $("#content-span").html(data.message);
                        }else
                        {
                            $("[name='"+data.type+"']").attr('placeholder',data.message).css("background-color","#f6d064")
                        }
                    }, "json");
        })
    })
</script>
{template 'common/_footer'}
</body>
</html>