{php global $_W;}
{php $setting = $_W['setting'];}
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <title>{$title}</title>
    <meta name="keywords" content="{$basicset['keywords']}" />
    <meta name="description" content="{$basicset['description']}" />
    <!-- 新 Bootstrap 核心 CSS 文件 -->
    <link rel="stylesheet" href="//cdn.bootcss.com/bootstrap/3.3.5/css/bootstrap.min.css">
    <link rel="stylesheet" href="/static/pcsite/css/style.css">
    <script src="//cdn.bootcss.com/jquery/1.11.3/jquery.min.js"></script>
    <script src="//cdn.bootcss.com/bootstrap/3.3.5/js/bootstrap.min.js"></script>
    <script type="text/javascript" src="/static/pcsite/js/slick.js"></script>
</head>
<body>
<header id="header">
    <div class="lynn-head">
        <h1 id="lynn-logo"><a href="{php echo webUrl()}"><img src="{php echo pctomedia($basicset['logo'])}" width="300" height="100" alt="logo"></a></h1>
        <nav id="lynn-nav">
            <ul class="lynn-nav-ul">
                <li><a href="{php echo webUrl()}" class="active">首页</a></li>
                <li><a href="{php echo webUrl('case/index')}">案例展示</a></li>
                <li><a href="{php echo webUrl('news/index')}">新闻中心</a></li>
                <li><a href="{php echo webUrl('article/index')}">最新文章</a></li>
                <li><a href="{php echo webUrl('contact/index')}">联系我们</a></li>
            </ul>
        </nav>
        {if isset($_GET['c']) && ($_GET['c']=='register' || $_GPC['c']=='login')}
        {else}
        <div id="lynn-login">

            |
            {if !isset($_GET['c']) || $_GET['c']=='home'}
            <a href="javascript:void(0);" id="lynn-login-header">登录</a>
            {else}
            <a href="{php echo webUrl('login/index')}" id="lynn-login-header">登录</a>
            {/if}
            {if !empty($setting['register']['open'])}<a href="{php echo webUrl('register/index')}" class="active">注册</a>{/if}
        </div>
        {/if}

        <div style="clear:both;"></div>
        {if !isset($_GET['c']) || $_GET['c']=='home'}
        <div class="index-login">
            <i class="index-login-top"></i>
            <a href="javascript:void(0);" class="index-login-close close glyphicon glyphicon-remove"></a>
            <h3 class="index-login-title">登录</h3>
            <div class="index-login-main">
                <form action="">
                    <ul class="index-login-main-ul">
                        <li><i class="glyphicon glyphicon-user"></i><input type="text" name="username" class="index-login-input" placeholder="邮箱/手机号/用户名" value=""></li>
                        <li><i class="glyphicon glyphicon-lock"></i><input type="password" name="password" class="index-login-input" placeholder="请输入密码" value=""></li>
                        <li style="display: none;"><input type="checkbox" class="index-login-checkbox">记住用户名</li>
                        <li><input type="button" id="index-login-submit" class="index-login-submit" value="立即登录"></li>
                        <li class="index-register">没有帐号？<a href="{php echo webUrl('register/index')}">立即注册</a></li>
                    </ul>
                </form>
            </div>
        </div>
        <script type="text/javascript">
            $(function(){

                $(":input[name='username'],:input[name='password']").keydown(function(e){
                    var e = e || event,
                            keycode = e.which || e.keyCode;
                    if (keycode==13) {
                        $("#index-login-submit").trigger("click");
                    }
                });

                $("#lynn-login-header").on("click",function(){
                    /*if($(".index-login").css("display")=="block"){
                     $(".index-login").fadeOut()
                     }*/
                    $(".index-login").fadeToggle()
                })
                $(".index-login-close").on("click",function(){
                    $(".index-login").fadeOut()
                })
                $("#index-login-submit").on("click",function(){
                    var username = $("input[name='username']").val();
                    var password = $("input[name='password']").val();
                    if(username == ''){
                        alert("用户名不能为空！");
                    }
                    if(password == ''){
                        alert("密码不能为空！");
                    }
                    if(username != '' && password != ''){
                        $.ajax({
                            url:"{php echo webUrl('login/check')}",// 跳转到 action
                            data:{
                                username : username,
                                password : password
                            },
                            type:'post',
                            cache:false,
                            dataType:'json',
                            success:function(data) {
                                if(data.msg =="true" ){
                                    window.location.href=data.url;
                                }else{
                                    alert(data.msg);
                                }
                            },
                            error : function() {
                                alert("异常！");
                            }
                        });
                    }
                })
            })
        </script>
        {/if}
    </div>
</header>
