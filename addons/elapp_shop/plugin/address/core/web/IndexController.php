<?php
namespace web\controller\address;

use app\controller\activity\Activity9800ActivateSDM;
use app\controller\activity\Activity9800Copartner;
use app\controller\activity\Activity980Clerk;
use app\controller\activity\ActivityEggTuanGift;
use app\controller\activity\BaseActivity;
use app\core\model\sandimeng;
use app\model;
use app\model\Activity2Model;
use app\plugin\activity\core\config\Link;
use app\plugin\activity\core\config\Radio;
use app\plugin\activity\core\config\Tab;
use app\plugin\activity\core\config\Text;
use app\plugin\activity\core\config\Textarea;
use app\plugin\activity\core\config\Title;
use app\plugin\activity\core\validate\ActivityValidate;
use think\Request;
use web\controller\PluginWebPage;

class IndexController extends PluginWebPage
{
    public function main(Request $request)
    {
        $pcode = $request->param('pcode');
        $keyword = $request->param('keyword');
        $status = $request->param('status');

        $where =  [];

        if ($keyword) {
            $where[] = ['name' , 'like', '%' .$keyword . '%'];
        }

        if ($pcode) {
            $where[] = ['pcode' , '=', $pcode];
        }
        if (empty($where)) {
            $where = ['level' => 1];
        }
        $list =model\AreaCodeModel::where($where)
            ->select()->toArray();
        include $this->template('address/index');
    }
    function generate() {
        // 创建目录
        $path = IA_ROOT . '/public/static/js/dist/area/list';

        if (!is_dir($path)) {
            mkdir($path, 0777, true);
        }

        $where = ['level' => 1];
        $list =model\AreaCodeModel::where($where)
            ->select()->toArray();
        $data = [];
        foreach ($list as $item) {
            // 取code头两位生成目录
            $code = substr($item['code'], 0, 2);
            $file = $path . '/' . $code;
            if (!is_dir($file)) {
                mkdir($file, 0777, true);
            }

            $data[] = [
                'text'=> $item['name'],
                'value'=> ''.$item['code'],
                'children' => []
            ];

            $where = ['level' => 2, 'pcode' => $item['code']];
            $list2 =model\AreaCodeModel::where($where)
                ->select()->toArray();
            foreach ($list2 as $item2) {
                $data[count($data) - 1]['children'][] = [
                    'text'=> $item2['name'],
                    'value'=> ''.$item2['code'],
                    'children' => []
                ];

                $where = ['level' => 3, 'pcode' => $item2['code']];
                $list3 =model\AreaCodeModel::where($where)
                    ->select()->toArray();
                foreach ($list3 as $item3) {
                    $data[count($data) - 1]['children'][count($data[count($data) - 1]['children']) - 1]['children'][] = [
                        'text'=> $item3['name'],
                        'value'=> ''.$item3['code'],
                    ];
                }
            }

            // 生成 xml
            foreach ($list2 as $item2) {
                $xml = '<?xml version="1.0" encoding="utf-8"?>';
                $xml .= '<address>';
                $xml .= '<city name="' . $item2['name'] . '" code="' . $item2['code'] . '">';

                $where = ['level' => 3, 'pcode' => $item2['code']];
                $list3 =model\AreaCodeModel::where($where)
                    ->select()->toArray();
                foreach ($list3 as $item3) {
                    $xml   .= '<county name="' . $item3['name'] . '" code="' . $item3['code'] . '">';
                    $where = ['level' => 4, 'pcode' => $item3['code']];

                    $list4 =model\AreaCodeModel::where($where)
                        ->select()->toArray();
                    foreach ($list4 as $item4) {
                        $xml .= '<street name="' . $item4['name'] . '" code="' . $item4['code'] . '" />';
                    }
                    // 增加其他街道，防止找不到街道
                    $xml .= '<street name="其他" code="' . $item3['code'] . '999" />';

                    $xml .= '</county>';
                }
                $xml .= '</city>';
                $xml .= '</address>';
                file_put_contents($file . '/' . $item2['code'] . '.xml', $xml);
            }
        }


        $data_str = 'var FoxUICityDataNew = ' . json_encode($data, JSON_UNESCAPED_UNICODE|JSON_PRETTY_PRINT) . ';';
        file_put_contents(IA_ROOT . '/public/static/application/shop/js/dist/foxui/js/foxui.citydatanew.min.js',
                          $data_str);
        // 据目前所知是以下和用户地址相关的文件
        // public/static/application/shop/js/dist/foxui/js/foxui.citydatanew.min.js
        // public/static/js/dist/area/list

        show_json(1, '生成成功');
    }

    public function set()
    {
        $how = '1. 更新数据' . "\n";
        $how.= '1.1 https://github.com/adyliu/china_area 下载最新的sql文件（如：area_code_2024.sql.gz），导入数据库，表名大概为：area_code_2024' . "\n";
        $how.= '1.2. 如果导入数据库中出现提示 Unknown collation: \'utf8mb4_0900_ai_ci\'' . "\n";
        $how.= '可执行命令：sed -i.bak \'s/utf8mb4_0900_ai_ci/utf8mb4_general_ci/g\' area_code_2024.sql' . "\n";
        $how.= '将sql文件中的utf8mb4_0900_ai_ci替换为utf8mb4_general_ci' . "\n";
        $how.= '1.3. 导入港澳台数据库，文件：sql/港澳台地区数据.sql' . "\n";
        $how .= '注: 也可以手工更新表 area_code_2024 的数据，按要求更新市县区的数据，数据相关规范查看github仓库' . "\n";
        $how.= '2. 生成数据' . "\n";
        $how.= '2.1 手动删除 /public/static/js/dist/area/list 下的数据' . "\n";
        $how.= '2.2 访问 http://localhost/web.php/address.index/main?i=1 页面点击 重新生成 按钮生成数据' . "\n";
        $how.= '3. 生成数据会更新js,地址xml文件，注意提交代码更新' . "\n";

        $configComponents =  [
            new Tab('基本', 'basic', true, [
                new Link('省市区地址数据代码来源', 'https://github.com/adyliu/china_area'),
                new Link('国家统计局公开数据来源', 'https://www.stats.gov.cn/sj/tjbz/tjyqhdmhcxhfdm/2023/'),
                new Textarea('如何更新地址数据', 'title', $how, ''),
            ]),
        ];

        include $this->template('address/set');
    }
}
