<?php
namespace app\model;

use app\controller\activity\OrderCreateAction;
use app\controller\activity\OrderPayAction;
use app\controller\activity\OrderRefundApplyAction;
use app\controller\activity\OrderSignOffAction;
use app\controller\activity\SingleRefundAction;
use app\controller\activity\OrderRefundAction;
use app\controller\activity\Action;
use app\controller\activity\SingleRefundApplyAction;

if (!class_exists('AddressModel')) {
    class AddressModel extends PluginBaseModel
    {
    }
}
