{template '_header'}
<link rel="stylesheet" type="text/css" href="/static/application/shop/plugin/about/static/css/common.css?v={time()}">
<div class='fui-page  fui-page-current'>
    <div class="fui-header">
        <div class="fui-header-left">
            <a class="back"></a>
        </div>
        <div class="title">{php echo $set['page_title']?$set['page_title']:m('plugin')->getPluginName('about');}</div>
        <div class="fui-header-right">
            <a class="icon icon-home external" href="{php echo mobileUrl()}"></a>
        </div>
    </div>
    <div class='fui-content'>

        {if !empty($advs)}
        <div class='fui-swipe' data-transition="500" data-gap="1">
            <div class='fui-swipe-wrapper'>
                {loop $advs $adv}
                <a class='fui-swipe-item' href="{if !empty($adv['link'])}{$adv['link']}{else}javascript:;{/if}"  data-nocache="true"><img src="{php echo tomedia($adv['thumb'])}" /></a>
                {/loop}
            </div>
            <div class='fui-swipe-page'></div>
        </div>
        {/if}

        <form action="{php echo mobileUrl('about/index/content')}" method="post">
            <div class="fui-searchbar bar">
                <div class="searchbar center">
                    <input type="submit" class="searchbar-cancel searchbtn" value="搜索" />
                    <div class="search-input">
                        <i class="icon icon-search"></i>
                        <input type="search" placeholder="输入关键字..." class="search" name="keyword">
                    </div>
                </div>
            </div>
        </form>

        {if count($category)>0}
            <div class="fui-cell-group about-title">
                <div class="fui-cell">
                    <div class="fui-cell-text">推荐分类</div>
                    <a class="fui-cell-remark" href="{php echo mobileUrl('about/index/category')}" data-nocache="true">全部</a>
                </div>
            </div>
            <div class="fui-icon-group col-4 noborder">
                {loop $category $item}
                    <a class="fui-icon-col" href="{php echo mobileUrl('about/index/content', array('cate'=>$item['id']))}" data-nocache="true">
                        <div class="icon">
                            <img src="{php echo tomedia($item['thumb'])}"/>
                        </div>
                        <div class="text">{$item['name']}</div>
                    </a>
                {/loop}
            </div>
        {/if}

        <div class="fui-cell-group about-title content-title hide">
            <div class="fui-cell">
                <div class="fui-cell-text">常见内容</div>
                <a class="fui-cell-remark" href="{php echo mobileUrl('about/index/content')}" data-nocache="true">全部</a>
            </div>
        </div>
        <div class="fui-list-group" id="container"></div>
    </div>

    {if empty($set['showtype'])}
        <script type="text/html" id="tpl_list">
            <%each list as item%>
            <div class="fui-according">
                <div class="fui-according-header">
                    <span class="text"><%item.title%></span>
                    <span class="remark"></span>
                </div>
                <div class="fui-according-content">
                    <div class="content-block"><%=item.content%></div>
                </div>
            </div>
            <%/each%>
        </script>
    {else}
        <script type="text/html" id="tpl_list">
                <%each list as item%>
                <a class="fui-list" href="{php echo mobileUrl('about/index/detail')}&id=<%item.id%>" data-nocache="true">
                    <div class="fui-list-inner">
                        <div class="title"><%item.title%></div>
                    </div>
                    <div class="fui-list-angle">
                        <div class="angle"></div>
                    </div>
                </a>
                <%/each%>
        </script>
    {/if}
    <script language="javascript">
        require(['/static/application/shop/plugin/about/static/js/common.js'],function(modal){
            modal.init({cate: '', keyword: '', isrecommand: 1});
        });
    </script>
</div>
{template '_footer'}