{template '_header'}
<link rel="stylesheet" type="text/css" href="/static/application/shop/plugin/about/static/css/common.css?v={time()}">
<div class='fui-page  fui-page-current'>
    <div class="fui-header">
        <div class="fui-header-left">
            <a class="back"></a>
        </div>
        <div class="title">{php echo $set['page_title']?$set['page_title']:m('plugin')->getPluginName('about');}</div>
        <div class="fui-header-right">
            <a class="icon icon-home external" href="{php echo mobileUrl('about')}"></a>
        </div>
    </div>
    <div class='fui-content'>
        <div class="fui-cell-group about-title content-title" style="display: none">
            <div class="fui-cell">
                <div class="fui-cell-text">{if !empty($category['name'])}{$category['name']}{else}全部内容{/if}</div>
            </div>
        </div>
        <div class="fui-message empty">
            <div class="icon ">
                <i class="icon icon-information"></i>
            </div>
            <div class="content">内容为空!</div>
            <div class="button">
                <a href="javascript:history.back(-1);" class="btn btn-default  external block">确认</a>
            </div>
        </div>
        {if empty($set['showtype'])}
        <div class="fui-according-group" id="container"></div>
        {else}
        <div class="fui-list-group" id="container"></div>
        {/if}
    </div>
    {if empty($set['showtype'])}
    <script type="text/html" id="tpl_list">
        <%each list as item%>
        <div class="fui-according">
            <div class="fui-according-header">
                <span class="text"><%item.title%></span>
                <span class="remark"></span>
            </div>
            <div class="fui-according-content">
                <div class="content-block" style="padding-left: 1rem;padding-right: 1rem;"><%=item.content%></div>
            </div>
        </div>
        <%/each%>
    </script>
    {else}
    <script type="text/html" id="tpl_list">
        <%each list as item%>
        <a class="fui-list" href="{php echo mobileUrl('about/index/detail')}&id=<%item.id%>" data-nocache="true">
            <div class="fui-list-inner">
                <div class="title"><%item.title%></div>
            </div>
            <div class="fui-list-angle">
                <div class="angle"></div>
            </div>
        </a>
        <%/each%>
    </script>
    {/if}
    <script language="javascript">
        require(['/static/application/shop/plugin/about/static/js/common.js'],function(modal){
            modal.initList({cate: "{$_GPC['cate']}", keyword:"{$_GPC['keyword']}"});
        });
    </script>
</div>
{template '_footer'}