<ul class="menu-head-top">
	<li {if $_GPC['r']=='about'} class="active"{/if}><a href="{php echo webUrl('about')}">{php echo $this->plugintitle} <i class="fa fa-caret-right"></i></a></li>
</ul>

{ifp 'about.adv'}
<div class='menu-header'>幻灯片</div>
<ul>
	<li {if $_GPC['r']=='about.adv' || $_GPC['r']=='about.adv.add' || $_GPC['r']=='about.adv.edit'} class="active"{/if}><a href="{php echo webUrl('about/adv')}">幻灯片管理</a></li>
</ul>
{/if}

{ifp 'about.content'}
	<div class='menu-header'>内容管理</div>
	<ul>
		<li {if $_GPC['r']=='about.content' || $_GPC['r']=='about.content.edit'} class="active"{/if}><a href="{php echo webUrl('about/content')}">内容管理</a></li>
		{ifp 'about.content.add'}
		<li {if $_GPC['r']=='about.content.add'} class="active"{/if}><a href="{php echo webUrl('about/content/add')}">添加内容</a></li>
		{/if}
	</ul>
{/if}

{ifp 'about.category'}
	<div class='menu-header'>分类管理</div>
	<ul>
		<li {if $_GPC['r']=='about.category' || $_GPC['r']=='about.category.add' || $_GPC['r']=='about.category.edit'} class="active"{/if}><a href="{php echo webUrl('about/category')}" style="cursor: pointer;">内容分类</a></li>
	</ul>
{/if}

{ifp 'about.set'}
	<div class='menu-header'>设置</div>
	<ul>
		<li {if $_GPC['r']=='about.set'} class="active"{/if}><a href="{php echo webUrl('about/set')}">基础设置</a></li>
	</ul>
{/if}
