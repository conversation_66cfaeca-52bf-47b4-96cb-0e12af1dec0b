{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">{if !empty($item['id'])}编辑{else}添加{/if}内容分类 <small>{if !empty($item['id'])}修改【{$item['name']}】{/if}</small></span>
</div>
<div class="page-content">
    <form  {ife 'about.category' $item}action="" method="post"{/if} class="form-horizontal form-validate" enctype="multipart/form-data" >
 
                {if !empty($item)}
                <div class="form-group">
                    <label class="col-lg control-label">分类链接(点击复制)</label>
                    <div class="col-sm-9 col-xs-12">
                        <p class='form-control-static'>
                            <a href='javascript:;' title='点击复制链接' class="js-clip" data-url="{php echo mobileUrl('about/content',array('cate'=>$item['id']), true)}">
                                {php echo mobileUrl('about/content',array('cate'=>$item['id']), true)}
                            </a>
                            <span style="cursor: pointer;" data-toggle="popover" data-trigger="hover" data-html="true"
                                  data-content="<img src='{$qrcode}' width='130' alt='链接二维码'>" data-placement="auto right">
                                <i class="glyphicon glyphicon-qrcode"></i>
                            </span>
                        </p>
                    </div>
                </div>
                {/if}
                <div class="form-group">
                    <label class="col-lg control-label">排序</label>
                    <div class="col-sm-9 col-xs-12">
                        {ife 'about.category' $item}
                        	<input type="text" name="displayorder" class="form-control" value="{$item['displayorder']}" />
                        {else}
                        	<div class='form-control-static'>{$item['displayorder']}</div>
                        {/if}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg control-label must">分类名称</label>
                    <div class="col-sm-9 col-xs-12">
                        {ife 'about.category' $item}
                        	<input type="text" name="catename" class="form-control" value="{$item['name']}" data-rule-required="true" />
                        {else}
                        	<div class='form-control-static'>{$item['name']}</div>
                        {/if}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg control-label">分类图片</label>
                    <div class="col-sm-9 col-xs-12">
                        {ife 'about.category' $item}
	                        {php echo tpl_form_field_image2('thumb', $item['thumb'])}
	                        <span class="help-block">建议尺寸: 100*100，或正方型图片 </span>
                        {else}
	                        {if !empty($item['thumb'])}
	                        <a href='{php echo tomedia($item['thumb'])}' target='_blank'>
	                           <img src="{php echo tomedia($item['thumb'])}" style='width:100px;border:1px solid #ccc;padding:1px' />
	                        </a>
	                        {/if}
                        {/if}
                    </div>
                </div>
                <div class="form-group">
                        <label class="col-lg control-label">是否首页推荐</label>
                        <div class="col-sm-9 col-xs-12">
                             {ife 'about.goods' $item}
                                <label class="radio-inline">
                                    <input type="radio" name='isrecommand' value="1" {if $item['isrecommand']==1}checked{/if} /> 是
                                </label>
	                            <label class="radio-inline">
	                                <input type="radio" name='isrecommand' value="0" {if empty($item['isrecommand'])}checked{/if} /> 否
	                            </label>
                             {else}
                             	<div class='form-control-static'>{if empty($item['isrecommand'])}是{else}否{/if}</div>
                             {/if}
                        </div>
                 </div>
                
                <div class="form-group">
                    <label class="col-lg control-label">是否显示</label>
                    <div class="col-sm-9 col-xs-12">
                        {ife 'about.category' $item}
	                        <label class='radio-inline'>
	                            <input type='radio' name='enabled' value=1' {if $item['enabled']==1}checked{/if} /> 是
	                        </label>
	                        <label class='radio-inline'>
	                            <input type='radio' name='enabled' value=0' {if $item['enabled']==0}checked{/if} /> 否
	                        </label><span class="help-block"> 提示：分类不显示，其分类下内容也将不显示。</span>
                        {else}
                        	<div class='form-control-static'>{if empty($item['enabled'])}否{else}是{/if}</div>
                        {/if}
                    </div>
                </div>

                <div class="form-group"></div>
                <div class="form-group">
                    <label class="col-lg control-label"></label>
                    <div class="col-sm-9 col-xs-12">
                         {ife 'about.category' $item}
                            <input type="submit" value="提交" class="btn btn-primary"  />
                        {/if}
                       <input type="button" name="back" onclick='history.back()' {ifp 'about.category.add|about.category.edit'}style='margin-left:10px;'{/if} value="返回列表" class="btn btn-default" />
                    </div>
                </div>

 

    </form>
</div>
{template '_footer'}


