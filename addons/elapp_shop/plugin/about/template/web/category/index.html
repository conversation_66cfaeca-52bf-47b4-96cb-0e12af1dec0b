{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">内容分类</span>
</div>

<div class="page-content">
    <div class="page-sub-toolbar">
        <span class=''>
            {ifp 'about.category.add'}
                <a class="btn btn-primary btn-sm" href="{php echo webUrl('about/category/add')}"><i class="fa fa-plus"></i> 添加分类</a>
            {/if}
        </span>
    </div>
    {if count($list)>0}
    <form action="" method="post">
        <table class="table table-hover table-responsive">
        <thead class="navbar-inner">
            <tr>
                <th style="width:50px;">ID</th>
                <th style='width:70px'>显示顺序</th>
                <th>分类名称</th>
                <th style="width: 50px; text-align: center;">推荐</th>
                <th style="width: 80px; text-align: center;">状态</th>
                <th style="width: 75px;">操作</th>
            </tr>
        </thead>
        <tbody id="sort">
            {loop $list $row}
            <tr>
                <td>{$row['id']}</td>
                <td>
                    {ifp 'about.category.edit'}
                    	<a href='javascript:;' data-toggle='ajaxEdit' data-href="{php echo webUrl('about/category/displayorder',array('id'=>$row['id']))}" >{$row['displayorder']}</a>
                    {else}
                    	{$row['displayorder']} 
                    {/if}
                </td>

                <td><img src='{php echo tomedia($row['thumb'])}' style='width:30px;height:30px;padding:1px;border:1px solid #ccc' onerror="this.src='/static/application/shop/images/nopic.png'"/> {$row['name']}</td>
                <td style="text-align: center;">
                    <span class='label {if $row['isrecommand']==1}label-success{else}label-default{/if}'
                    {ifp 'about.content.edit'}
                    data-toggle='ajaxSwitch'
                    data-switch-value='{$row['isrecommand']}'
                    data-switch-value0='0|否|label label-default|{php echo webUrl('about/category/isrecommand',array('isrecommand'=>1,'id'=>$row['id']))}'
                    data-switch-value1='1|是|label label-success|{php echo webUrl('about/category/isrecommand',array('isrecommand'=>0,'id'=>$row['id']))}'
                    {/if}
                    >
                    {if $row['isrecommand']==1}是{else}否{/if}</span>
                </td>
                <td style="text-align: center;">
                    <span class='label {if $row['enabled']==1}label-primary{else}label-default{/if}'
                          {ifp 'about.category.edit'}
	                          data-toggle='ajaxSwitch' 
	                          data-switch-value='{$row['enabled']}'
	                          data-switch-value0='0|隐藏|label label-default|{php echo webUrl('about/category/enabled',array('enabled'=>1,'id'=>$row['id']))}'  
	                          data-switch-value1='1|显示|label label-primary|{php echo webUrl('about/category/enabled',array('enabled'=>0,'id'=>$row['id']))}'
                          {/if}
                          >
                          {if $row['enabled']==1}显示{else}隐藏{/if}</span>
                    </td>
                    <td style="text-align:left;">
                        {ifp 'about.category.view|about.category.edit'}
	                        <a href="{php echo webUrl('about/category/edit', array('id' => $row['id']))}" class="btn btn-default btn-sm btn-op btn-operation" title="{ifp 'about.category.edit'}修改{else}查看{/if}">
                                <span data-toggle="tooltip" data-placement="top" title="" data-original-title="{ifp 'about.category.edit'}修改{else}查看{/if}">
                                      {ifp 'about.category.edit'}
                                        <i class="icow icow-bianji2"></i>
                                        {else}
                                        <i class="icow icow-chakan-copy"></i>
                                        {/if}
                                     </span>
	                        </a>
                        {/if}
                        {ifp 'about.category.delete'} 
                        	<a data-toggle='ajaxRemove' href="{php echo webUrl('about/category/delete', array('id' => $row['id']))}"class="btn btn-default btn-sm btn-op btn-operation" data-confirm="确认删除此分类?" title="删除">
                               <span data-toggle="tooltip" data-placement="top" title="" data-original-title="删除">
                                   <i class="icow icow-shanchu1"></i>
                               </span>
                            </a>
                        {/if}
                    </td>
                </tr>
                {/loop} 
            </tbody>
        </table>
        {$pager}
    </form>
    {else}
    <div class='panel panel-default'>
        <div class='panel-body' style='text-align: center;padding:30px;'>
            暂时没有任何商品分类
        </div>
    </div>
    {/if}
</div>
{template '_footer'}