{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">基础设置</span>
</div>

<div class="page-content">
    <form action="" method="post" class="form-horizontal form-validate" enctype="multipart/form-data" novalidate="novalidate">
        <div class="form-group">
            <label class="col-lg control-label">页面标题</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <input type="text" class="form-control valid" name="page_title" value="{$set['page_title']}" placeholder="">
                {else}
                <div class="form-control-static">{$set['page_title']}</div>
                {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label">个人中心显示</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <label class="radio-inline"><input type="radio" name="showmember" value="1" {if !empty($set['showmember'])}checked{/if}> 是</label>
                <label class="radio-inline"><input type="radio" name="showmember" value="0" {if empty($set['showmember'])}checked{/if}> 否</label>
                {else}
                <div class="form-control-static">{if empty($set['showmember'])} 否{else}是{/if}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">显示类型</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <label class="radio-inline"><input type="radio" name="showtype" value="0" {if empty($set['showtype'])}checked{/if}> 展开内容</label>
                <label class="radio-inline"><input type="radio" name="showtype" value="1" {if !empty($set['showtype'])}checked{/if}> 跳转详情页</label>
                {else}
                <div class="form-control-static">{if empty($set['showtype'])} 展开内容{else}跳转详情页{/if}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">分享设置</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <label class="radio-inline"><input type="radio" name="share" value="0" {if empty($set['share'])}checked{/if}> 商城首页</label>
                <label class="radio-inline"><input type="radio" name="share" value="1" {if !empty($set['share'])}checked{/if}> 当前页面</label>
                {else}
                <div class="form-control-static">{if empty($set['share'])} 商城首页{else}当前页面{/if}</div>
                {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label">页面链接(点击复制)</label>
            <div class="col-sm-9 col-xs-12">
                <div class="form-control-static">
                    <a data-href="{php echo mobileUrl('about', null, true)}" class="js-clip">{php echo mobileUrl('about', null, true)}</a>
                    <span style="cursor: pointer;" data-toggle="popover" data-trigger="hover" data-html="true"
                          data-content="<img src='{$qrcode}' width='130' alt='链接二维码'>" data-placement="auto right">
                        <i class="glyphicon glyphicon-qrcode"></i>
                    </span>
                </div>
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">入口关键字</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <input type="text" class="form-control valid" name="keyword" value="{$set['keyword']}" placeholder="">
                {else}
                <div class="form-control-static">{$set['keyword']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">入口标题</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <input type="text" class="form-control valid" name="enter_title" value="{$set['enter_title']}" placeholder="">
                {else}
                <div class="form-control-static">{$set['enter_title']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">入口图片</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                {php echo tpl_form_field_image2('enter_img', $set['enter_img'])}
                {else}
                <img width="150" class="img-responsive img-thumbnail" onerror="this.src='/static/application/web/resourceimages/nopic.jpg'; this.title='图片未找到.'" src="{php echo tomedia($set['enter_img'])}">
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">入口介绍</label>
            <div class="col-sm-9 col-xs-12">
                {ifp 'about.set.save'}
                <textarea name="enter_desc" class="form-control valid" rows="5" placeholder="" style="padding: 5px;" >{$set['enter_desc']}</textarea>
                {else}
                <div class="form-control-static">{$set['enter_desc']}</div>
                {/if}
            </div>
        </div>

        {ifp 'about.set.save'}
        <div class="form-group">
            <label class="col-lg control-label"></label>
            <div class="col-sm-9">
                <input type="submit" value="提交" class="btn btn-primary">
            </div>
        </div>
        {/if}

    </form>
</div>
{template '_footer'}
