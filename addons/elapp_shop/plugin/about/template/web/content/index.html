{template '_header'}

<div class="page-header">
    当前位置：<span class="text-primary">内容管理 </span>
</div>

<div class="page-content">
    <form action="" method="get" class="form-horizontal" plugins="form">
        <div class="page-toolbar  m-b-sm m-t-sm">
            <div class="col-sm-4">
                 <span class=''>
                    {ifp 'about.content.add'}
                        <a class="btn btn-primary btn-sm" href="{php echo webUrl('about/content/add')}"><i class="fa fa-plus"></i> 添加内容</a>
                    {/if}
                </span>
            </div>
            <div class="col-sm-6 pull-right">
                <div class="input-group">
                    <div class="input-group-select">
                        <select name="status" class="form-control" >
                            <option value="">显示</option>
                            <option value="1" {if $_GPC['status']=='1'} selected{/if}>是</option>
                            <option value="0" {if $_GPC['status']=='0'} selected{/if}>否</option>
                        </select>
                    </div>
                    <div class="input-group-select">
                        <select name="isrecommand" class="form-control ">
                            <option value="">推荐</option>
                            <option value="1" {if $_GPC['isrecommand']=='1'} selected{/if}>是</option>
                            <option value="0" {if $_GPC['isrecommand']=='0'} selected{/if}>否</option>
                        </select>
                    </div>
                    <div class="input-group-select">
                        <select name="cate" class="form-control ">
                            <option value="">内容分类</option>
                            {loop $category $item}
                            <option value="{$item['id']}" {if intval($_GPC['cate'])==$item['id']} selected{/if}>{$item['name']}</option>
                            {/loop}
                        </select>
                    </div>
                    <input type="text" class=" form-control" name="keyword" value="{$_GPC['keyword']}" placeholder="请输入关键词"> <span class="input-group-btn">
                    <button class="btn  btn-primary" type="submit"> 搜索</button> </span>
                </div>
            </div>
        </div>
    </form>

{if count($list)>0}
<form action="" method="post">
    <div class="page-table-header">
        <input type="checkbox">
        <div class="btn-group">
            {ifp 'qr.content.edit'}
            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch" data-href="{php echo weburl('about/content/status', array('status'=>0))}" disabled="disabled">
                <i class="icow icow-yincang"></i> 隐藏
            </button>
            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch" data-href="{php echo weburl('about/content/status', array('status'=>1))}" disabled="disabled">
                <i class="icow icow-xianshi"></i> 显示
            </button>
            {/if}
            {ifp 'qr.content.delete'}
            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch-remove" data-confirm="确认要删除吗?" data-href="{php echo weburl('about/content/delete')}" disabled="disabled">
                <i class="icow icow-shanchu1"></i> 删除
            </button>
            {/if}
        </div>
    </div>
    <table class="table table-hover table-responsive">
        <thead class="navbar-inner">
            <tr>
                <th style="width:25px;">
                </th>
                <th style="width: 100px;">排序</th>
                <th >内容分类</th>
                <th>内容名称</th>
                <th style=" text-align: center;">推荐</th>
                <th style="width:70px; text-align: center;">状态</th>
                <th style="width: 125px;">操作</th>
            </tr>
        </thead>
        <tbody id="sort">
            {loop $list $row}
            <tr>
                <td><input type="checkbox" value="{$row['id']}"></td>
                <td>
                    {ifp 'about.content.edit'}
                    	<a href='javascript:;' data-toggle='ajaxEdit' data-href="{php echo webUrl('about/content/displayorder',array('id'=>$row['id']))}" >{$row['displayorder']}</a>
                    {else}
                    	{$row['displayorder']} 
                    {/if}
                </td>
                <td>{if $row['cate']==0}未分类{else}{$row['catename']}{/if}</td>
                <td>{$row['title']}</td>
                <td style="text-align: center;">
                    <span class='label {if $row['isrecommand']==1}label-success{else}label-default{/if}'
                    {ifp 'about.content.edit'}
                    data-toggle='ajaxSwitch'
                    data-switch-value='{$row['isrecommand']}'
                    data-switch-value0='0|否|label label-default|{php echo webUrl('about/content/isrecommand',array('isrecommand'=>1,'id'=>$row['id']))}'
                    data-switch-value1='1|是|label label-success|{php echo webUrl('about/content/isrecommand',array('isrecommand'=>0,'id'=>$row['id']))}'
                    {/if}
                    >
                    {if $row['isrecommand']==1}是{else}否{/if}</span>
                </td>
                <td style="text-align: center;">
                    <span class="label {if $row['status']==1}label-primary{else}label-default{/if}"
                          {ifp 'about.content.edit'}
	                          data-toggle='ajaxSwitch' 
	                          data-switch-value='{$row['status']}'
	                          data-switch-value0='0|隐藏|label label-default|{php echo webUrl('about/content/status',array('status'=>1,'id'=>$row['id']))}'
	                          data-switch-value1='1|显示|label label-primary|{php echo webUrl('about/content/status',array('status'=>0,'id'=>$row['id']))}'
                          {/if}
                          >
                          {if $row['status']==1}显示{else}隐藏{/if}</span>
                    </td>
                    <td style="text-align:left;">
                        {ifp 'about.content.view|about.content.edit'}
	                        <a href="{php echo webUrl('about/content/edit', array('id' => $row['id']))}" class="btn btn-default btn-sm btn-op btn-operation" title="{ifp 'about.content.edit'}编辑{else}查看{/if}">
                                <span data-toggle="tooltip" data-placement="top" title="" data-original-title="  {ifp 'about.content.edit'}修改{else}查看{/if}">
                                    {ifp 'about.content.edit'}
                                        <i class="icow icow-bianji2"></i>
                                    {else}
                                        <i class="icow icow-chakan-copy"></i>
                                    {/if}
                                </span>
	                        </a>
                        {/if}
                        {ifp 'about.content.delete'} 
                        	<a data-toggle='ajaxRemove' href="{php echo webUrl('about/content/delete', array('id' => $row['id']))}"class="btn btn-default btn-sm btn-op btn-operation" data-confirm="确认删除此分类?" title="删除">
                                <span data-toggle="tooltip" data-placement="top" title="" data-original-title="删除">
                                    <i class="icow icow-shanchu1"></i>
                                 </span>
                            </a>
                        {/if}
                        <a class="btn btn-default btn-sm js-clip btn-op btn-operation" title="" data-href="{php echo mobileUrl('about/index/detail', array('id'=>$row['id']), true)}">
                             <span data-toggle="tooltip" data-placement="top" title="" data-original-title="复制链接">
                                <i class="icow icow-lianjie2"></i>
                             </span>
                        </a>

                        <a href="javascript:void(0);" class="btn btn-default btn-sm btn-op btn-operation" data-toggle="popover" data-trigger="hover" data-html="true" data-content="<img src='{$row['qrcode']}' width='130' alt='链接二维码'>" data-placement="auto right">
                            <i class="icow icow-erweima3"></i>
                        </a>
                    </td>
                </tr>
                {/loop} 
            </tbody>
            <tfoot>
                <tr>
                    <td><input type="checkbox"></td>
                    <td colspan="2">
                        <div class="btn-group">
                            {ifp 'qr.content.edit'}
                            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch" data-href="{php echo weburl('about/content/status', array('status'=>0))}" disabled="disabled">
                                <i class="icow icow-yincang"></i> 隐藏
                            </button>
                            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch" data-href="{php echo weburl('about/content/status', array('status'=>1))}" disabled="disabled">
                                <i class="icow icow-xianshi"></i> 显示
                            </button>
                            {/if}
                            {ifp 'qr.content.delete'}
                            <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle="batch-remove" data-confirm="确认要删除吗?" data-href="{php echo weburl('about/content/delete')}" disabled="disabled">
                                <i class="icow icow-shanchu1"></i> 删除
                            </button>
                            {/if}
                        </div>
                    </td>
                    <td colspan="4" class="text-right"> {$pager}</td>
                </tr>
            </tfoot>
        </table>
        {else}
        <div class='panel panel-default'>
            <div class='panel-body' style='text-align: center;padding:30px;'>
                未找到相关内容
            </div>
        </div>
        {/if}
    </form>
</div>
{template '_footer'}


