{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">{if !empty($item['id'])}编辑{else}添加{/if}内容 <small>{if !empty($item['id'])}修改【{$item['title']}】{/if}</small></span>
</div>
<div class="page-content">
    <form  {ife 'about.content' $item}action="" method="post"{/if} class="form-horizontal form-validate" enctype="multipart/form-data" >

        <div class="form-group">
            <label class="col-lg control-label">排序</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                    <input type="text" name="displayorder" class="form-control" value="{$item['displayorder']}" />
                {else}
                    <div class='form-control-static'>{$item['displayorder']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label must">内容标题</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                <input type="text" name="title" class="form-control" value="{$item['title']}" data-rule-required="true" />
                {else}
                <div class='form-control-static'>{$item['title']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">作者</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'qa.question' $item}
                <input type="text" name="author" class="form-control" value="{$item['author']}" />
                {else}
                <div class='form-control-static'>{$item['author']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">编辑</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'qa.question' $item}
                <input type="text" name="editor" class="form-control" value="{$item['editor']}" />
                {else}
                <div class='form-control-static'>{$item['editor']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">内容关键字</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                <input type="text" name="keywords" class="form-control" value="{$item['keywords']}" />
                <div class="help-block">内容关键字提供更精准的搜索推送服务, 并非入口关键字, 多个请以半角逗号隔开</div>
                {else}
                <div class='form-control-static'>{$item['keywords']}</div>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">问题描述</label>
            <div class="col-sm-9 col-xs-12">
                <textarea class="form-control" rows="5" name="desc">{$item['desc']}</textarea>
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">封面图片</label>
            <div class="col-sm-9 col-xs-12">
                {php echo tpl_form_field_image2('thumb',$item['thumb'])}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label must">内容内容</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                {php echo tpl_ueditor('content',$item['content'],array('height'=>'400'))}
                {else}
                <textarea id='contentcontent' style='display:none;'>{$item['content']}</textarea>
                <a href='javascript:preview_html("#contentcontent")' class="btn btn-default">查看内容</a>
                {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label">内容分类</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                <select name="cate" class="form-control" data-rule-required="true" >
                    {loop $category $cate}
                    <option value="{$cate['id']}" {if $cate['id']==$item['cate']}selected{/if}>{$cate['name']}</option>
                    {/loop}
                </select>
                {else}
                <div class='form-control-static'>
                    {loop $category $cate}
                        {if $cate['id']==$item['cate']}
                            {$cate['name']}
                        {/if}
                    {/loop}
                </div>
                {/if}
            </div>
        </div>
        <div class="form-group">
                <label class="col-lg control-label">是否推荐</label>
                <div class="col-sm-10 col-xs-12">
                        {ife 'about.goods' $item}
                    <label class="radio-inline">
                        <input type="radio" name='isrecommand' value="1" {if $item['isrecommand']==1}checked{/if} /> 是
                    </label>
                        <label class="radio-inline">
                            <input type="radio" name='isrecommand' value="0" {if empty($item['isrecommand'])}checked{/if} /> 否
                        </label>
                        {else}
                        <div class='form-control-static'>{if empty($item['isrecommand'])}是{else}否{/if}</div>
                        {/if}
                </div>
            </div>
        
        <div class="form-group">
            <label class="col-lg control-label">是否显示</label>
            <div class="col-sm-10 col-xs-12">
                {ife 'about.content' $item}
                <label class='radio-inline'>
                    <input type='radio' name='status' value=1' {if $item['status']==1}checked{/if} /> 是
                </label>
                    <label class='radio-inline'>
                        <input type='radio' name='status' value=0' {if $item['status']==0}checked{/if} /> 否
                    </label>
                {else}
                    <div class='form-control-static'>{if empty($item['status'])}否{else}是{/if}</div>
                {/if}
            </div>
        </div>

        <div class="form-group"></div>
        <div class="form-group">
            <label class="col-lg control-label"></label>
            <div class="col-sm-10 col-xs-12">
                    {ife 'about.content' $item}
                    <input type="submit" value="提交" class="btn btn-primary"  />
                {/if}
                <input type="button" name="back" onclick='history.back()' {ifp 'about.content.add|about.content.edit'}style='margin-left:10px;'{/if} value="返回列表" class="btn btn-default" />
            </div>
        </div>
    </form>
</div>
{template '_footer'}


