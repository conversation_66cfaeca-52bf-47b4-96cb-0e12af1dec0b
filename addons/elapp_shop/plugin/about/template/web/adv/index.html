{template '_header'}

<div class="page-header">
    当前位置：
    <span class="text-primary">幻灯片管理</span>
</div>

<div class="page-content">
    <form action="" method="get" class="form-horizontal form-search" role="form">
        <input type="hidden" name="i" value="{$_GPC['i']}" />
        <div class="page-toolbar">
            <div class="col-sm-4">
                <span class=''>
                    {ifp 'about.adv.add'}
                        <a class='btn btn-primary btn-sm' href="{php echo webUrl('about/adv/add')}"><i class='fa fa-plus'></i> 添加幻灯片</a>
                    {/if}
                </span>
            </div>
            <div class="col-sm-6 pull-right">
                <div class="input-group">
                    <div class="input-group-select">
                        <select name="enabled" class='form-control'>
                            <option value="" {if $_GPC['enabled'] == ''} selected{/if}>状态</option>
                            <option value="1" {if $_GPC['enabled']== '1'} selected{/if}>显示</option>
                            <option value="0" {if $_GPC['enabled'] == '0'} selected{/if}>隐藏</option>
                        </select>
                    </div>
                    <input type="text" class="input-sm form-control" name='keyword' value="{$_GPC['keyword']}" placeholder="请输入关键词"> <span class="input-group-btn">
                        <button class="btn btn-primary" type="submit"> 搜索</button> </span>
                </div>

            </div>
        </div>
    </form>

    <form action="" method="post">
        {if count($list)>0}
        <div class="page-table-header">
            <input type="checkbox">
            <div class="btn-group">
                {ifp 'about.adv.edit'}
                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch' data-href="{php echo webUrl('about/adv/enabled',array('enabled'=>1))}">
                    <i class='icow icow-xianshi'></i> 显示
                </button>
                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch'  data-href="{php echo webUrl('about/adv/enabled',array('enabled'=>0))}">
                    <i class='icow icow-yincang'></i> 隐藏
                </button>
                {/if}
                {ifp 'about.adv.delete'}
                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch-remove' data-confirm="确认要删除?" data-href="{php echo webUrl('about/adv/delete')}">
                    <i class='icow icow-shanchu1'></i> 删除
                </button>
                {/if}
            </div>
        </div>
        <table class="table table-responsive table-hover" >
            <thead class="navbar-inner">
                <tr>
                    <th style="width:25px;"></th>
                    <th style='width:50px'>顺序</th>
                    <th >标题</th>
                    <th>链接</th>
                    <th style='width:60px'>显示</th>
                    <th style="width: 75px;">操作</th>
                </tr>
            </thead>
            <tbody>
                {loop $list $row}
                <tr>
                    <td>
                        <input type='checkbox'   value="{$row['id']}"/>
                    </td>
                    <td>
                        {ifp 'about.adv.edit'}
                            <a href='javascript:;' data-toggle='ajaxEdit' data-href="{php echo webUrl('about/adv/displayorder',array('id'=>$row['id']))}" >{$row['displayorder']}</a>
                        {else}
                            {$row['displayorder']}
                        {/if}
                    </td>

                    <td>{$row['advname']}</td>
                    <td>{$row['link']}</td>
                    <td>

                        <span class='label {if $row['enabled']==1}label-primary{else}label-default{/if}'
                              {ifp 'about.adv.edit'}
                                  data-toggle='ajaxSwitch'
                                  data-switch-value='{$row['enabled']}'
                                  data-switch-value0='0|隐藏|label label-default|{php echo webUrl('about/adv/enabled',array('enabled'=>1,'id'=>$row['id']))}'
                                  data-switch-value1='1|显示|label label-primary|{php echo webUrl('about/adv/enabled',array('enabled'=>0,'id'=>$row['id']))}'
                              {/if}
                              >
                              {if $row['enabled']==1}显示{else}隐藏{/if}</span>

                        </td>
                        <td style="text-align:left;">
                            {ifp 'about.adv.view|about.adv.edit'}
                                <a href="{php echo webUrl('about/adv/edit', array('id' => $row['id']))}" class="btn btn-default btn-sm btn-op  btn-operation">
                                     <span data-toggle="tooltip" data-placement="top" title="" data-original-title=" {ifp 'about.adv.edit'}修改{else}查看{/if}">
                                       {ifp 'about.adv.edit'}
                                        <i class="icow icow-bianji2"></i>
                                        {else}
                                        <i class="icow icow-chakan-copy"></i>
                                        {/if}
                                     </span>
                                </a>
                            {/if}
                            {ifp 'about.adv.delete'}
                                <a data-toggle='ajaxRemove' href="{php echo webUrl('about/adv/delete', array('id' => $row['id']))}"class="btn btn-default btn-sm btn-op  btn-operation" data-confirm='确认要删除此幻灯片吗?'>
                                    <span data-toggle="tooltip" data-placement="top" title="" data-original-title="删除">
                                        <i class="icow icow-shanchu1"></i>
                                     </span>
                                </a>
                            {/if}
                        </td>
                    </tr>
                    {/loop}
                </tbody>
                <tfoot>
                    <tr>
                        <td><input type="checkbox"></td>
                        <td colspan="2">
                            <div class="btn-group">
                                {ifp 'about.adv.edit'}
                                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch' data-href="{php echo webUrl('about/adv/enabled',array('enabled'=>1))}">
                                    <i class='icow icow-xianshi'></i> 显示
                                </button>
                                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch'  data-href="{php echo webUrl('about/adv/enabled',array('enabled'=>0))}">
                                    <i class='icow icow-yincang'></i> 隐藏
                                </button>
                                {/if}
                                {ifp 'about.adv.delete'}
                                <button class="btn btn-default btn-sm btn-operation" type="button" data-toggle='batch-remove' data-confirm="确认要删除?" data-href="{php echo webUrl('about/adv/delete')}">
                                    <i class='icow icow-shanchu1'></i> 删除
                                </button>
                                {/if}
                            </div>
                        </td>
                        <td colspan="3" class="text-right"> {$pager}</td>
                    </tr>
                </tfoot>
            </table>
            {else}
            <div class='panel panel-default'>
                <div class='panel-body' style='text-align: center;padding:30px;'>
                    暂时没有任何幻灯片!
                </div>
            </div>
            {/if}
        </form>
</div>
{template '_footer'}