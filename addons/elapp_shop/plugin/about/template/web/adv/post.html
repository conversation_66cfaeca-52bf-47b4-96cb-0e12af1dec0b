{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">{if !empty($item['id'])}编辑{else}添加{/if}幻灯片 <small>{if !empty($item['id'])}修改【{$item['advname']}】{/if}</small></span>
</div>
 
<div class="page-content">
    <div class="page-sub-toolbar">
        <span class=''>
        {ifp 'about.adv.add'}
            <a class="btn btn-primary btn-sm" href="{php echo webUrl('about/adv/add')}">添加新幻灯片</a>
        {/if}
        </span>
    </div>
    <form {ife 'about.adv' $item} action="" method="post"{/if} class="form-horizontal form-validate" enctype="multipart/form-data">
        <input type="hidden" name="id" value="{$item['id']}" />
        <div class="form-group">
            <label class="col-lg control-label">排序</label>
            <div class="col-sm-9 col-xs-12">
                {ife 'about.adv' $item}
                    <input type="text" name="displayorder" class="form-control" value="{$item['displayorder']}" />
                    <span class='help-block'>数字越大，排名越靠前</span>
                {else}
                    <div class='form-control-static'>{$item['displayorder']}</div>
                {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label must">幻灯片标题</label>
            <div class="col-sm-9 col-xs-12 ">
                {ife 'about.adv' $item}
                    <input type="text" id='advname' name="advname" class="form-control" value="{$item['advname']}" data-rule-required="true" />
                {else}
                    <div class='form-control-static'>{$item['advname']}</div>
                {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label">幻灯片图片</label>
            <div class="col-sm-9 col-xs-12">
            {ife 'about.adv' $item}
                {php echo tpl_form_field_image2('thumb', $item['thumb'])}
                <span class='help-block'>建议尺寸:640 * 350 , 请将所有幻灯片图片尺寸保持一致</span>
            {else}
                {if !empty($item['thumb'])}
                    <a href='{php echo tomedia($item['thumb'])}' target='_blank'>
                        <img src="{php echo tomedia($item['thumb'])}" style='width:100px;border:1px solid #ccc;padding:1px' />
                    </a>
                {/if}
            {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label">幻灯片链接</label>
            <div class="col-sm-9 col-xs-12">
                {ife 'about.adv' $item}
                <div class="input-group">
                    <input type="text" name="link" class="form-control" value="{$item['link']}" id="advlink" />
                    <span class="input-group-addon btn btn-default" data-toggle="selectUrl" data-input="#advlink">选择链接</span>
                </div>
                {else}
                <div class='form-control-static'>{$item['link']}</div>
                {/if}
            </div>
        </div>
        <div class="form-group">
            <label class="col-lg control-label">状态</label>
            <div class="col-sm-9 col-xs-12">
            {ife 'about.adv' $item}
                <label class='radio-inline'>
                    <input type='radio' name='enabled' value=1' {if $item['enabled']==1}checked{/if} /> 显示
                </label>
                <label class='radio-inline'>
                    <input type='radio' name='enabled' value=0' {if $item['enabled']==0}checked{/if} /> 隐藏
                </label>
            {else}
                <div class='form-control-static'>{if empty($item['enabled'])}隐藏{else}显示{/if}</div>
            {/if}
            </div>
        </div>

        <div class="form-group">
            <label class="col-lg control-label"></label>
            <div class="col-sm-9 col-xs-12">
                {ife 'about.adv' $item}
                <input type="submit" value="提交" class="btn btn-primary"  />
                {/if}
               <input type="button" name="back" onclick='history.back()' {ifp 'about.adv'}style='margin-left:10px;'{/if} value="返回列表" class="btn btn-default" />
            </div>
        </div>
    </form>
</div>
<script language='javascript'>
    function formcheck() {
        if ($("#advname").isEmpty()) {
            Tip.focus("advname", "请填写幻灯片名称!");
            return false;
        }
        return true;
    }
</script>
{template '_footer'}