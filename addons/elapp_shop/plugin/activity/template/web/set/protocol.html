<input type="hidden"  value="980_user_plan_a" name="data[key_0]"/>
<input type="hidden"  value="plan_a1" name="data[name_0]"/>
<input type="hidden"  value="方案A1" name="data[title_0]">

<input type="hidden"  value="980_user_plan_a" name="data[key_1]"/>
<input type="hidden"  value="plan_a2" name="data[name_1]"/>
<input type="hidden"  value="方案A2" name="data[title_1]">

<input type="hidden"  value="980_user_plan_a" name="data[key_3]"/>
<input type="hidden"  value="plan_a3" name="data[name_3]"/>
<input type="hidden"  value="方案A3" name="data[title_3]">

<input type="hidden"  value="980_user_plan_b" name="data[key_2]"/>
<input type="hidden"  value="plan_b" name="data[name_2]"/>
<input type="hidden"  value="方案B" name="data[title_2]">


{loop $plans $key $plan}
    <div class="form-group">
        <label class="col-lg control-label">{$plan['title']}关联商品ID</label>
        <div class="col-sm-6 col-xs-6">
            <input type='text' class='form-control' name='data[goods_id_{$key}]' value="{$plan['goods']['id']}" />
        </div>
    </div>
    <div class="form-group">
        <label class="col-lg control-label">协议显示</label>
        <div class="col-sm-8">
            {ifp 'settle.set.edit'}
            <label class="radio-inline"><input type="radio"  name="data[must_check_{$key}]" value="1" {if $plan['must_check'] ==1} checked="checked"{/if} /> 开启</label>
            <label class="radio-inline"><input type="radio"  name="data[must_check_{$key}]" value="0" {if $plan['must_check'] ==0} checked="checked"{/if} /> 关闭</label>
            <div class='help-block'></div>
            {else}
            {if $plan['must_check'] ==0}关闭{else}开启{/if}
            {/if}
        </div>
    </div>
    <div class="form-group">
        <label class="col-lg control-label">{$plan['title']}协议标题</label>
        <div class="col-sm-9 col-xs-12">
            {ifp 'clerk.set.edit'}
            <input type='text' class='form-control' name='data[980_protocol_title_{$key}]' value="{$plan['980_protocol_title']}" />
            {else}
            <div class='form-control-static'>{$plan['980_protocol_title']}</div>
            {/if}
        </div>
    </div>

    <div class="form-group">
        <label class="col-lg control-label">{$plan['title']}协议内容</label>
        <div class="col-sm-9 col-xs-12">
            {ifp 'clerk.set.edit'}
            {php echo tpl_ueditor("data[980_protocol_content_".$key."]", $plan['980_protocol_content'], array('height'=>200))}
            {else}
            <textarea id='980_protocol_content_{$name}' style='display:none'>{$plan['980_protocol_content']}</textarea>
            <a href='javascript:preview_html("#980_protocol_content_{$name}")' class="btn btn-default">查看内容</a>
            {/if}
        </div>
    </div>
{/loop}

