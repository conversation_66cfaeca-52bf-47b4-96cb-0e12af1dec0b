<!--<div class="form-group">-->
<!--    <label class="col-lg control-label">提现方式</label>-->
<!--    <div class="col-sm-9 col-xs-12" >-->
<!--        {ifp 'copartner.set.edit'}-->
<!--        <label for="cashcredit" class="checkbox-inline">-->
<!--            <input type="checkbox" name="data[cashcredit]" value="1" id="cashcredit" {if !empty($data['cashcredit'])}checked="true"{/if} /> 提现到商城余额-->
<!--        </label>-->
<!--        <label for="cashweixin" class="checkbox-inline">-->
<!--            <input type="checkbox" name="data[cashweixin]" value="1" id="cashweixin" {if !empty($data['cashweixin'])}checked="true"{/if} /> 提现到微信钱包-->
<!--        </label>-->
<!--        <label for="cashother" class="checkbox-inline">-->
<!--            <input type="checkbox" name="data[cashother]" value="1" id="cashother" {if !empty($data['cashother'])}checked="true"{/if} /> 其他提现方式-->
<!--        </label>-->
<!--        <div class='help-block'>提示: 提现方式支持多选</div>-->
<!--        {else} <div class='form-control-static'>-->
<!--        {if $data['cashcredit']==1}提现到商城余额; {/if}-->
<!--        {if $data['cashweixin']==1}提现到微信钱包; {/if}-->
<!--        {if $data['cashother']==1}其他提现方式; {/if}-->
<!--    </div>-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group cashother-group" {if empty($data['cashother'])}style="display: none;"{/if}>-->
<!--    <label class="col-lg control-label">其他提现方式</label>-->
<!--    <div class="col-sm-9 col-xs-12" >-->
<!--        {ifp 'copartner.set.edit'}-->
<!--        <label for="cashalipay" class="checkbox-inline">-->
<!--            <input type="checkbox" name="data[cashalipay]" value="1" id="cashalipay" {if !empty($data['cashalipay'])}checked="true"{/if} /> 手动提现到支付宝-->
<!--        </label>-->
<!--        <label for="cashcard" class="checkbox-inline">-->
<!--            <input type="checkbox" name="data[cashcard]" value="1" id="cashcard" {if !empty($data['cashcard'])}checked="true"{/if} /> 手动提现到银行卡-->
<!--        </label>-->
<!--        <div class='help-block'>支持的银行请到<a href='{php echo webUrl('copartner/bank')}' target='_blank'>【银行设置】</a>进行设置,使用手动提现到银行卡必须设置支持的银行</div>-->
<!--        {else} <div class='form-control-static'>-->
<!--        {if $data['cashalipay']==1}手动提现到支付宝; {/if}-->
<!--        {if $data['cashweixin']==1}手动提现到银行卡; {/if}-->
<!--    </div>-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group">
    <label class="col-lg control-label">开启提现到余额</label>
    <div class="col-sm-9 col-xs-12">
        {ifp 'copartner.set.edit'}
        <label class="radio-inline"><input type="radio"  name="data[closetocredit]" value="0" {if $data['closetocredit'] ==0} checked="checked"{/if} /> 开启</label>
        <label class="radio-inline"><input type="radio"  name="data[closetocredit]" value="1" {if $data['closetocredit'] ==1} checked="checked"{/if} /> 关闭</label>
        {else}
        {if $data['closetocredit']==0}开启{else}关闭{/if}
        {/if}
        <span class="help-block">是否允许用户佣金提现到余额，否则只允许微信提现</span>
    </div>
</div>-->

<!--<div class="form-group">-->
<!--    <label class="col-lg control-label">提现额度</label>-->
<!--    <div class="col-sm-9 col-xs-12 fixmore-input-group">-->
<!--        {ifp 'copartner.set.edit'}-->
<!--        <input type="text" name="data[withdraw]" class="form-control" value="{php echo empty($data['withdraw'])?1:$data['withdraw']}"  />-->
<!--        <span class="help-block">合伙人的佣金达到此额度时才能提现,最低1元</span>-->
<!--        {else}-->
<!--        {php echo empty($data['withdraw'])?1:$data['withdraw']}-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group">-->
<!--    <label class="col-lg control-label">佣金计算方式</label>-->
<!--    <div class="col-sm-9 col-xs-12">-->
<!--        {ifp 'copartner.set.edit'}-->
<!--        <label class="radio-inline"><input type="radio"  name="data[commissiontype]" value="0" {if $data['commissiontype'] ==0} checked="checked"{/if} /> 默认方式</label>-->
<!--        <label class="radio-inline"><input type="radio"  name="data[commissiontype]" value="1" {if $data['commissiontype'] ==1} checked="checked"{/if} /> 实际支付方式</label>-->
<!--        <span class="help-block">默认方式: 除运费和会员折扣(或促销折扣)外其他所有费用全部计算佣金<br>实际支付方式: 只计算实际支付和余额抵扣部分的佣金(包括余额支付)</span>-->
<!--        {else}-->
<!--        {if $data['commissiontype']==0}开启{else}关闭{/if}-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group">-->
<!--    <label class="col-lg control-label"></label>-->
<!--    <div class="col-sm-5">-->
<!--        {ifp 'clerk.set.edit'}-->
<!--        <span class="help-block">是否扣除商品成本</span>-->
<!--        <label class="radio-inline"><input type="radio"  name="data[isReduceGoodsCostprice]" value="0" {if $data['isReduceGoodsCostprice'] ==0} checked="checked"{/if} /> 不扣除</label>-->
<!--        <label class="radio-inline"><input type="radio"  name="data[isReduceGoodsCostprice]" value="1" {if $data['isReduceGoodsCostprice'] ==1} checked="checked"{/if} /> 扣除</label>-->
<!--        <span class="help-block">是否扣除商品成本</span>-->
<!--        {else}-->
<!--            {if $data['isReduceGoodsCostprice']==0}不需要扣除{else}需要扣除{/if}-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group">-->
<!--    <label class="col-lg control-label">佣金提现手续费</label>-->
<!--    <div class="col-sm-9 col-xs-12">-->
<!--        {ifp 'copartner.set.edit'}-->
<!--        <div class="input-group fixmore-input-group">-->
<!--            <input type="text" name="data[withdrawcharge]" class="form-control" value="{$data['withdrawcharge']}" />-->
<!--            <div class="input-group-addon">%</div>-->
<!--        </div>-->
<!--        <span class="help-block">佣金提现时,扣除的提现手续费.空为不扣除提现手续费</span>-->
<!--        {else}-->
<!--        {php echo empty($data['withdrawcharge'])?1:$data['withdrawcharge']}-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<!--<div class="form-group">-->
<!--    <label class="col-lg control-label">免提现手续费金额区间</label>-->
<!--    <div class="col-sm-9 col-xs-12">-->
<!--        {ifp 'sysset.trade.edit'}-->
<!--        <div class='input-group fixmore-input-group'>-->
<!--            <span class='input-group-addon'>开始金额￥</span>-->
<!--            <input type="text" name="data[withdrawbegin]" class="form-control" value="{$data['withdrawbegin']}" />-->
<!--            <span class='input-group-addon'>结束金额￥</span>-->
<!--            <input type="text" name="data[withdrawend]" class="form-control" value="{$data['withdrawend']}" />-->
<!--        </div>-->
<!--        <span class='help-block'>当提现手续费金额在此区间内时,不扣除提现手续费. 结束金额 必须大于 开始金额才能生效</span>-->
<!--        <span class='help-block'>例如 设置开始金额0元 结束金额5元,只有提现手续费金额高于5元时,才扣除</span>-->
<!--        {else}-->
<!--        <input type="hidden" name="data[withdrawbegin]" value="{$data['withdrawbegin']}"/>-->
<!--        <input type="hidden" name="data[withdrawend]" value="{$data['withdrawend']}"/>-->
<!--        <div class='form-control-static'>-->
<!--            {$data['withdrawbegin']} 元 - {$data['withdrawend']}元-->
<!--        </div>-->
<!--        {/if}-->
<!--    </div>-->
<!--</div>-->

<div class="form-group">
    <label class="col-lg control-label">商品订单结算天数</label>
    <div class="col-sm-9 col-xs-12 fixmore-input-group">
        {ifp 'copartner.set.edit'}
        <input type="text" name="data[goods_settle_days]" class="form-control" value="{$data['goods_settle_days']}"  />
        <span class="help-block">当订单完成后的n天后，佣金才能申请提现,设置0或空，订单完成就可以结算<br>建议设置的结算天数大于【设置-交易设置-退款申请】中设置的天数，否则会出现佣金结算后商品被维权的情况</span>
        {else}
        {$data['goods_settle_days']}
        {/if}
    </div>
</div>

<div class="form-group">
    <label class="col-lg control-label">会员卡结算天数</label>
    <div class="col-sm-9 col-xs-12 fixmore-input-group">
        {ifp 'copartner.set.edit'}
        <input type="text" name="data[membercard_settle_days]" class="form-control" value="{$data['membercard_settle_days']}"  />
        <span class="help-block">当订单完成后的n天后，佣金才能申请提现,设置0或空，订单完成就可以结算<br>建议设置的结算天数大于【设置-交易设置-退款申请】中设置的天数，否则会出现佣金结算后商品被维权的情况</span>
        {else}
        {$data['membercard_settle_days']}
        {/if}
    </div>
</div>

<div class="form-group">
    <label class="col-lg control-label">服务费结算天数</label>
    <div class="col-sm-9 col-xs-12 fixmore-input-group">
        {ifp 'copartner.set.edit'}
        <input type="text" name="data[servicefee_settle_days]" class="form-control" value="{$data['servicefee_settle_days']}"  />
        <span class="help-block">当订单完成后的n天后，佣金才能申请提现,设置0或空，订单完成就可以结算<br>建议设置的结算天数大于【设置-交易设置-退款申请】中设置的天数，否则会出现佣金结算后商品被维权的情况</span>
        {else}
        {$data['servicefee_settle_days']}
        {/if}
    </div>
</div>
