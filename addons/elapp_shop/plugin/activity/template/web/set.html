{template '_header'}
<style>
    .col-lger {
        float: left;
        padding-right: 10px;
        padding-left: 10px;
        position: relative;
        width: 212px;
    }
</style>
<div class="page-header">
    当前位置：<span class="text-primary">基础设置</span>
</div>

<div class="page-content">
    <form id="setform"  {ifp 'copartner.set.edit'}action="" method="post"{/if} class="form-horizontal form-validate">

        <input type="hidden" id="tab" name="tab" value="#tab_basic" />
        <div class="tabs-container>
         <div class="tabs-left">
         <ul class="nav nav-tabs" id="myTab">
            <li  {if empty($_GPC['tab']) || $_GPC['tab']=='basic'}class="active"{/if}><a href="#tab_basic">基本</a></li>
            <li {if $_GPC['tab']=='money'}class="active"{/if} ><a href="#tab_money">结算</a></li>
            <li {if $_GPC['tab']=='style'}class="active"{/if} ><a href="#tab_style">样式/文字</a></li>
            <li {if $_GPC['tab']=='protocol'}class="active"{/if} ><a href="#tab_protocol">协议</a></li>
        </ul>
        <div class="tab-content ">
            <div class="tab-pane   {if empty($_GPC['tab']) || $_GPC['tab']=='basic'}active{/if}" id="tab_basic"><div class="panel-body">{template 'activity/set/basic'}</div></div>
            <div class="tab-pane {if $_GPC['tab']=='money'}active{/if}" id="tab_money"> <div class="panel-body">{template 'activity/set/money'}</div></div>
            <div class="tab-pane {if $_GPC['tab']=='style'}active{/if}" id="tab_style"> <div class="panel-body">{template 'activity/set/style'}</div></div>
            <div class="tab-pane {if $_GPC['tab']=='protocol'}active{/if}" id="tab_protocol"> <div class="panel-body">{template 'activity/set/protocol'}</div></div>
        </div>
        </div>
    {ifp 'copartner.set.edit'}
        <div class="form-group">
        <label class="col-lg control-label"></label>
        <div class="col-sm-9 col-xs-12">
            <input type="submit"  value="提交" class="btn btn-primary" />
        </div>
        </div>
    {/if}

    </form>
</div>
<script language='javascript'>
        require(['bootstrap'], function () {
            $('#myTab a').click(function (e) {
                $('#tab').val($(this).attr('href'));
                e.preventDefault();
                $(this).tab('show');
            })
        });
        $(function () {
            $('.open_apply').click(function () {
                var type = $(".open_apply:checked").val();
                if (type == '1') {
                    $('.protocol-group').show();
                } else {
                    $('.protocol-group').hide();
                }
            })
        });
        function showBecome(obj) {
            var $this = $(obj);
            $('.become').hide();
            $('.becomeconsume').hide();

            if ($this.val() == '1') {
                $('.protocol-group').show();
            } else {
                $('.protocol-group').hide();
            }

            if ($this.val() == '2') {
                $('.become2').show();
                $('.becomeconsume').show();
            } else if ($this.val() == '3') {
                $('.become3').show();
                $('.becomeconsume').show();
            } else if ($this.val() == '4') {
                $('.become4').show();
                $('.becomeconsume').show();
            }

        }
        $('#cashother').click(function () {
            if ($(this).prop('checked')) {
                $(".cashother-group").show();
            }
            else {
                $(".cashother-group").hide();
            }
        })
        //显示佣金文字
        $(document).on("click", '[name="data[cansee]"]',function(){
            var cansee = $('[name="data[cansee]"]:checked').val();
            if (cansee ==1){
                $("#seetitle").attr("style","");
            }else{
                $("#seetitle").attr("style","display: none");
            }
        })
        //邀请码
        $(document).on("click", '[name="data[hideicode]"]',function(){
            var hideicode = $('[name="data[hideicode]"]:checked').val();
            if (hideicode ==0){
                $("#showicodeType").attr("style","");
            }else{
                $("#showicodeType").attr("style","display: none");
            }
        })
        $('form').submit(function () {
            var become_child = $(":radio[name='data[become_child]']:checked").val();
            if (become_child == '1' || become_child == '2') {
                if ($(":radio[name='data[become]']:checked").val() == '0') {
                    $('form').attr('stop', 1), tip.msgbox.err('成为下级条件选择了首次下单/首次付款，成为合伙人条件不能选择无条件!');

                    return false;
                }
            }
            $('form').removeAttr('stop');
            return true;
        })
</script>
{template '_footer'}
