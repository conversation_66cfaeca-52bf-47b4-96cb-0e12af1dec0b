{template '_header'}
<style>
    .style i{
        vertical-align: middle;
    }
</style>
<style type='text/css'>
    .popover{
        width:250px;
        font-size:12px;
        line-height: 21px;
        color: #0d0706;
    }
    .popover span{
        color: #b9b9b9;
    }
    .nickname{
        display: inline-block;
        max-width:200px;
        overflow: hidden;
        text-overflow:ellipsis;
        white-space: nowrap;
        vertical-align: middle;
    }
    .tooltip-inner{
        border:none;
    }
    .moresearch { padding:0px 10px;}
    .moresearch .col-sm-2 {
        padding:0 5px
    }
    .info{
        height: 100%;
        width:315px;
        float:left;
        border-right:1px solid #efefef;
        border-bottom: 1px solid #efefef;
        padding: 20px 20px;
        line-height: 25px;
    }
    .info i{
        display: inline-block;
        width:80px;
        text-align: right;
        color: #999;
    }
    ._cover {
        color: #337ab7 !important;
    }
</style>
<div class="page-header">当前位置：<span class="text-primary">活动列表</span></div>
<div class="page-content">
    <form action="" method="get" class="form-horizontal table-search" role="form" id="form1">
        <input type="hidden" name="i" value="{$_GPC['i']}" />
        <div class="page-toolbar">
            <div class="input-group">
                <span class="input-group-select">
                    <select name="status" class="form-control"   style="width:75px;"  >
                        <option value="" {if $status===''}selected{/if}>状态</option>
                        <option value="1" {if $status==='1'}selected{/if}>启用</option>
                        <option value="0" {if $status==='0'}selected{/if}>禁用</option>
                    </select>
                </span>
                <span class="input-group-select">
                    <select name="searchfield"  class="form-control"   style="width:100px;"  >
                        <option value="order_sn" {if $_GPC['searchfield']=='order_sn'}selected{/if}>活动名称</option>
<!--                        <option value="member" {if $_GPC['searchfield']=='member'}selected{/if}>会员信息</option>-->
                    </select>
                </span>
                <input type="text" class="form-control"  name="keyword" value="{$keyword}" placeholder="请输入关键词" />
                <span class="input-group-btn">
                    <button class="btn  btn-primary" type="submit"> 搜索</button>
                    {ifp 'finance.log.recharge.export'}
<!--                        <button type="submit" name="export" value="1" class="btn btn-success ">导出</button>-->
                    {/if}
                </span>
            </div>
        </div>
    </form>
    {if empty($list)}
    <div class="panel panel-default">
        <div class="panel-body empty-data">未查询到相关数据</div>
    </div>
    {else}
    <div class="row">
        <div class="col-md-12">
            <table class="table">
                <colgroup>
                    <col style="width:2.5%;">
                    <col style="width:25%;">
                    <col style="width:15%;">
                    <col style="width:15%;">
                    <col style="width:5%;">
                </colgroup>
                <thead>
                <tr>
                    <th>ID</th>
                    <th>活动名称</th>
                    <th>活动唯一编码</th>
<!--                    <th style="text-align: center;">查看</th>-->
                    <th>状态</th>
                    <th style="text-align: center;">操作</th>
                </tr>
                </thead>
                <tbody>
                {loop $list $row}
                <tr>
                    <td>{$row['id']}</td>
                    <td>{$row['title']}</td>
                    <td>
                        {$row['key']}
                    </td>
                    <td>
                        {if $row['status']==0}
                        <span class='text-danger'>禁用</span>
                        {else if $row['status']==1}
                        <span class='text-success'>启用</span>
                        {/if}
                    </td>
                    <td>
                        <a class='btn btn-default btn-sm btn-op btn-operation' href="{php echo webUrl('activity/index/detail', array('id' => $row['id']))}">
                            <span data-toggle="tooltip" data-placement="top" title="" data-original-title="编辑">
                                 <i class='fa fa-edit'></i> 编辑
                            </span>
                        </a>
                    </td>
                </tr>
                <tr style="display: none;border-bottom:none;background:#f9f9f9;">
                    <td colspan='6' style='text-align:left'>
                        备注:<span class="text-info">{$row['remark']}</span>
                    </td>
                </tr>
                {/loop}
                </tbody>
                <tfoot>
                <tr>
                    </td>
                    <td colspan="5" style="text-align: right">
                        {$pager}
                    </td>
                </tr>
                </tfoot>
            </table>
        </div>
    </div>
    {/if}
</div>
<script>
    require(['bootstrap'], function () {
        $("[rel=pop]").popover({
            trigger: 'manual',
            placement: 'right',
            title: $(this).data('title'),
            html: 'true',
            content: $(this).data('content'),
            animation: false
        }).on("mouseenter", function () {
            var _this = this;
            $(this).popover("show");
            $(this).siblings(".popover").on("mouseleave", function () {
                $(_this).popover('hide');
            });
        }).on("mouseleave", function () {
            var _this = this;
            setTimeout(function () {
                if (!$(".popover:hover").length) {
                    $(_this).popover("hide")
                }
            }, 100);
        });
    });
</script>
{template '_footer'}