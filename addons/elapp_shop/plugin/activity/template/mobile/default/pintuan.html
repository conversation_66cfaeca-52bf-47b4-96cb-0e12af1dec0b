{template '_header'}
<style type="text/css">
	.info{
		background:#fff;
		line-height: 1.2rem;
		padding:10px 20px;
	}
	.info .title{
		font-size: 18px;
		font-weight: 500;
		color: #1a1a1a;
	}
	.info .tuan{
		border:1px solid #ff8020;
		border-radius: 20px;
		margin-left: 20px;
		padding:0 10px;
	}
	.info .text{
		color: #a5a5a5;
		font-size: 14px;
		margin-top:10px;
	}
	.info .content{
		color: #a5a5a5;
		font-size: 14px;
		padding-bottom: 50px;
	}
	.flex{
		display: flex;
		align-items: center;
	}
	.flex-1{
		flex:1;
	}
	.foot{
		position: fixed;
		bottom: 0;
		width: 100%;
		max-width: 750px;
	}
	.foot button{
		background: #ff8020;
		color: #fff;
		text-align: center;
		height: 52px;
		border:none;
		width: 100%;
		/*font-size: 20px;*/
		font-size: 0.8rem;
		line-height: 52px;
	}
	.tuanlist{

	}
	.tuanlist .item {
		background: #fff;
		padding:10px 20px;
		/*margin-top: 10px;*/
	}
	.tuanlist .item span{
		font-size: 0.7rem;
	}
	.tuanlist .item span.text1{
		color:#ff8020;
	}
	.tuanlist .item span.text2{
		color:#1a1a1a;
		padding-left: 20px;
	}
	.tuanlist .item .member{
		position: relative;
		margin-right: 15px;
	}
	.tuanlist .item .member .touxiang{
		width: 50px;
		height: 50px;
		background-size: cover;
		border-radius: 100%;
		background-position: center;
		background-repeat: no-repeat;
		background-color: #f3f3f3;
		margin:0 auto 5px auto;
		line-height: 50px;
		text-align: center;
	}
	.tuanlist .item .member .touxiang.empty{
		border: 1px dotted #ead2a2;
		color: #927979;
	}
	.tuanlist .item .member .name{
		text-align: center;
		color: #fff;
		background: #ec632c;
		border-radius: 24px;
		font-size: 12px;
		display: block;		
		margin:0 auto;
		height: 18px;
		line-height: 18px;
	}
	.nobg{
		background: none !important;
	}
	.lingqu{
		text-align: right;
	}
	.lingqu button{
		background: #ff8020;
		color: #fff;
		font-size: 14px;
		border:none;
		/*padding: 5px 10px;*/
		border-radius: 15px;
	}
</style>
<div class='fui-page fui-page-current' id="activity_app">
	<div class="fui-header">
		<div class="fui-header-left">
			<a class="back" @click="back"></a>
		</div>
		<div class="title">分享好友下单免费送鸡蛋</div>
		<div class="fui-header-right"></div>
	</div>
	<div class="fui-content">
		<div class='fui-swipe' data-speed="5000">
		    <div class='fui-swipe-wrapper'>
		        {loop $banner $banneritem}
		            <a class='fui-swipe-item' href="{$banneritem['linkurl']}" data-nocache="true"><img src="{php echo tomedia($banneritem['imgurl'])}" style="display: block; width: 100%; height: auto;"/></a>
		        {/loop}
		    </div>
		    <style>
		        .fui-swipe-page .fui-swipe-bullet { opacity: 1;}
		        .fui-swipe-page .fui-swipe-bullet.active {opacity: 1;}
		    </style>
		    <div class="fui-swipe-page center round" style="padding: 0 10px; bottom: 0px;"></div>
		</div>
		<div class="info">
			<div class="flex">
				<div class="title">{$activity_info['index_page_title']}</div>
				<!-- <div class="tuan">3人团</div> -->
			</div>
<!--			<div class="text">邀请{$activity_info['target_count']}个好友下单可获得一张免单券</div>-->
			<div class="text">{$activity_info['index_page_desc']}</div>
		</div>

		<div id="app">
			<div class="tabs nav nav-tabs">
				<button class="tab nav-item nav-link" :class="{ active: activeTab === 'users' }" data-tab="users" @click="switchTab('users')">会员卡推广</button>
				<button class="tab nav-item nav-link" :class="{ active: activeTab === 'goods' }" data-tab="goods" @click="switchTab('goods')">鸡蛋推广</button>
			</div>

			<div class="tuanlist">
				<div class="item" v-for="(item, index) in tuanlist" :key="index">
					<div class="flex">
						<span> {{ item.create_time }}&nbsp;</span>
						<span v-if="item.status==0" class="text1" style="color:orange">进行中</span>
						<span v-if="item.status==1" class="text1" style="color:green">已完成</span>
						<span class="text2">已有{{ item.current_count }}人参加</span>
					</div>

					<div class="flex" style="margin-top:10px">
						
						<div class="member" v-for="(order, midx) in item.data.orders">
							<div class="touxiang" :style="{backgroundImage: 'url(' + order.avatar + ')'}"></div>
<!--													<div class="name nobg"></div>-->
							<div class="name">{{ order.nickname }}</div>
						</div>


						<div v-if="item.status == 0 && item.current_count==item.target_count" class="lingqu flex-1" style="margin-top:-15px">
							<button data-id="{$item['id']}" class="btn btn-default">待完成</button>
						</div>

						<div v-if="item.status == 0 && item.current_count!=item.target_count" class="lingqu flex-1" style="margin-top:-15px">
							<button data-id="{$item['id']}" class="btn yaoqing" @click="invite('')">立即邀请</button>
						</div>

						<div v-if="item.status == 1 && item.coupon_status == 0" class="lingqu flex-1" style="margin-top:-15px">
							<button data-id="{$item['id']}" class="btn lingquBtn" @click="redeemCoupon(item)" style="background: #ff8020;color:#fff;">领取免单券</button>
						</div>

						<div v-if="item.status == 1 && item.coupon_status == 1" class="lingqu flex-1">
							<button data-id="{$item['id']}" class="btn btn-default" disabled>已领取</button>
						</div>
					</div>
				</div>

				<div v-if="tuanlist.length === 0 && !requesting" class="info" style="margin-top:10px">
					<div class="title"></div>
					<div class="content" style="text-align: center">
						<!--					<p>分享商品给好友，{$activity_info['target_count']}个好友下单后即可领取一张免单券。</p>-->
						<p>{$activity_info['index_page_empty_data_text']}</p>
					</div>
				</div>

				<div v-if="tuanlist.length === 0 && requesting" class="info" style="margin-top:10px">
					<div class="title"></div>
					<div class="content" style="text-align: center">
						<!--					<p>分享商品给好友，{$activity_info['target_count']}个好友下单后即可领取一张免单券。</p>-->
						<p>数据加载中...</p>
					</div>
				</div>
			</div>

			<div class="info" style="margin-top:10px">
				<div class="title">活动规则</div>
				<div class="content">
					{$activity_info['index_page_rule_text']}
					<!--				<p>分享商品给好友，每{$activity_info['target_count']}个好友下单后即可领取一张免单券。</p>-->
					<!--				<p>最终解释权贵平台所有</p>-->
				</div>
			</div>
			<div class="foot">
				{if $isvip}
				<button class="yaoqing" @click="invite('users')">立即邀请</button>
				{else}
				<button class="yaoqing" @click="go_buy_member_card">您还不是VIP会员，无法参与活动，立即前往购买</button>
				{/if}
			</div>
		</div>

		<style>
			.tabs {
				display: flex;
				justify-content: space-around;
				/*margin-bottom: 20px;*/
			}

			.tabs .tab {
				padding: 10px 20px;
				background-color: #f5f5f5;
				border: none;
				cursor: pointer;
				transition: background-color 0.3s ease;
				width: 100%;
			}

			.tabs .tab:hover {
				background-color: #e0e0e0;
			}

			.tabs .tab.active {
				background-color: #ff8020;
				color: white;
			}
		</style>
		<script>
			$('.tabs .tab').click(function(){
				$('.tabs .tab').removeClass('active');
				$(this).addClass('active');
				$('.tabcontent').hide();
				$('.'+$(this).data('tab')).show();
			});
			// document.querySelectorAll('.tab').forEach(function(tab) {
			// 	tab.addEventListener('click', function() {
			// 		var type = this.getAttribute('data-tab');
			// 		fetchList(type);
			// 	});
			// });
		</script>
		<script>
			const { reactive, provide, inject, computed, createApp, ref,shallowRef   } = Vue;

			const app = createApp({
				data() {
					return {
						requesting: false,
						tuanlist: [],
						activeTab: 'users'
					}
				},
				mounted() {
					this.getList();
				},
				methods: {
					invite(type) {
						if (type == 'users' || this.activeTab == 'users') {
							location.href="{php echo mobileUrl('membercard/qrcode/main',['id'=>$activity_info['index_page_member_card_id'], 'poster_id'=>$activity_info['index_page_poster_id'],'k'=>0,'type'=>'all'])}"
						} else {
							var member_id = "{$mid}";
							var goodsid = "{$goods_info['id']}"
							location.href="{php echo mobileUrl('goods/detail')}&id=" + goodsid + "&mid=" + member_id;
						}
					},
					go_buy_member_card() {
						location.href="{php echo mobileUrl('membercard/detail/main',['id'=>$activity_info['index_page_member_card_id'],'k'=>0,'type'=>'all'])}"
					},
					getList() {
						this.requesting = true;
						this.tuanlist = [];
						url ="{php echo mobileUrl('activity/pintuan/get_list')}&type=" + this.activeTab;
						fetch(url)
							.then(response => response.json())
							.then(data => {
								this.tuanlist = data.data;
							}).catch((error) => {
							}).finally(() => {
								this.requesting = false;
							});
					},
					// 领取免单券
					redeemCoupon(item) {
						// 发送请求到服务器来领取免单券
						fetch("{php echo mobileUrl('activity/pintuan/redeemCoupon')}&id=" + item.id)
							.then(response => response.json())
							.then(data => {
								// 处理响应
								console.log(data);
								if (data.code == 0) {
									item.coupon_status = 1;
									// alert("免单券已领取");
									FoxUI.toast.show('领取成功');
								} else {
									FoxUI.toast.show(data.msg);
									//alert("领取失败: " + data.message);
								}
							}).catch(error => {
								FoxUI.toast.show('领取失败, 请重试');
							});
					},
					switchTab(tab) {
						this.activeTab = tab;  // 切换当前激活的tab
						this.getList();
					}
				},
				setup(){
					document.title ="全民供享药房";
					const keyword = ref('');
					const showDate = ref(false)
					const data = reactive({
						hello: "world"
					})

					var timer = null
					// function confirm(e){
					// 	dateVal.value.value = e.selectedValue[0]
					// 	// showDate.value = false;
					// 	// $(".container").empty();
					// 	// require(['/static/application/shop/plugin/copartner/static/js/my_team_list.js'],function(modal){
					// 	//     modal.init({dateVal : e.selectedValue[0],page:1});
					// 	// });
					// 	showDate.value = false;
					// 	debounceSearch()
					// }
					// function initList(){
					// 	var aa = dateVal.value.value
					// 	require(['/static/application/shop/plugin/copartner/static/js/clerk_my_team_list.js'],function(modal){
					// 		modal.init({dateVal : aa});
					// 	});
					// }
					function search() {

					}
					function debounceSearch() {
						clearTimeout(timer); // 清除之前的计时器
						timer = setTimeout(() => {
							search(); // 执行搜索操作
						}, 300); // 延迟 100 毫秒
					}

					return {
						data,
						timer,
						keyword,
						search,
						debounceSearch
					}
				}
			})
			app.use(nutui).mount("#app");
		</script>
	</div>
</div>
<script type="text/javascript">
	var goodsid = "{$goods_info['id']}"
	require(['/static/application/shop/plugin/activity/js/mobile.js'], function(modal){
        modal.init({
        	goodsid : goodsid,
        });
    });
</script>
{template '_footer'}
