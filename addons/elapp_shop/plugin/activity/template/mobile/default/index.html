{template '_header'}
<style type="text/css">
	.fui-navbar .buybtn {
	    background: #ed6d0f;
	}
	.fui-content{
/*		padding-bottom: 85px;*/
	}
	.fui-page {
/*		background: #fff;*/
		background: linear-gradient(180deg, #EB5C20 0%, #EB5C20 10%, #EB5D21 14%, #ED652A 25%, #EB5C20 100%);
	}
	.fui-navbar .buybtn {
		background: #EB5C20;
	}
	.fui-navbar, .fui-footer{
		width: auto;
		margin: 20px;
		background: none;
		box-shadow:none;
	}
	.fui-navbar .nav-item.btn{
		border:none;
		border-radius: 40px;
		font-weight: 700;
		font-size: 16px;
	}
	.tab_box{
/*		background-color: #f00;*/
/*		padding-top: 10px;*/
	}
	.tab_content{
		display: flex;
		align-items: center;
		margin:0 3.2%;
		justify-content: end;
	}
	.tab_content div.flex1{
		flex: 1;
	}
	.tab_content div{
		height: 52px;
		position: relative;
		text-align: center;
		line-height: 60px;
		font-size: 18px;
		font-weight: 700;
	}
	.tab_content div.item1{
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
	}

	.tab_content div.active{
		background: #fff;
		color: #EB5C20;
	}


	.tab_content div.flex1 div.text1{
		height: 47px;
		line-height: 47px;
		margin-top:5px;
		background: #f2f2f2;
		border-top-left-radius: 10px;
		border-bottom-right-radius: 10px;
	}

	.tab_content div.flex1 div.text2{
		height: 47px;
		line-height: 47px;
		margin-top:5px;
		background: #f2f2f2;
		border-bottom-left-radius: 10px;
		border-top-right-radius: 10px;
	}

	.tab_content div.item2.active{
		border-top-left-radius: 10px;
		border-top-right-radius: 10px;
	}

	.tab_content div.item1.active div.text1,
	.tab_content div.item2.active div.text2{
		background: #fff;
	}

	.tab_box .content{
		background: #fff;
		min-height: 400px;
/*		margin:0px 3.2% 0 3.2%;*/
		/*border-bottom-left-radius: 15px;
		border-bottom-right-radius: 15px;*/
/*		border-radius: 15px;*/
		display: none;/*
		padding:0 4px;
		padding-top:10px;*/
	}
	.tab_box #content_2{
		padding-top:25px;
	}
	.tab_content div.active .item-line {
	    position: absolute;
	    left: 0;
	    bottom: 0px;
	    right: auto;
	    top: 43px;
	    width: 100%;
	    height: 4px;
	    background-color: #EB5C20;
	    border-radius: 20px;
	    display: block;
	    z-index: 0;
	    transform-origin: center center;
	    transform: scaleX(0.2);
	}
</style>
<div class='fui-page fui-page-current' id="activity_app">
	<div class="fui-header">
		<div class="fui-header-left">
			<a class="back" @click="back"></a>
		</div>
		<div class="title">{$activity_config['title']}</div>
		<div class="fui-header-right"></div>
	</div>
	<div class="fui-content">
		<img src="https://file.snkdyf.com/images/1/2024/02/OGtJGSO4b98OjI46iw63bbi93wB3g4.png" style="width:100%;display: block;" data-lazyloaded="true" />
		<div class="tab_box">
			<div class="tab_content" style="display: none;">
				<div class="flex1 item1 active" onclick="tabClick(1)">
					<div class="text1">A多赚方案</div>
					<div class="item-line"></div>
				</div>
				<div class="flex1 item2" onclick="tabClick(2)">
					<div class="text2">B保本方案</div>
					<div class="item-line"></div>
				</div>
			</div>
			<div class="content" id="content_1" style="display: block;" v-show="tabIndex == 1">
				<a onclick="goToBuy(1)" class="block-1">
					<img src="https://file.snkdyf.com/images/1/2024/02/Pf66jF9aAnbmi11l97tBt6xMlEek79.png" style="width:100%;display: block;" data-lazyloaded="true" />
					<img src="https://file.snkdyf.com/images/1/2024/02/qs7906Tq69706K9DF7ft7S7fDjS676.png" style="width:100%;display: block;" data-lazyloaded="true" />
					<img src="https://file.snkdyf.com/images/1/2024/02/axZ5NBxJjP6phxHQbezz55Pe5r4KPb.png" style="width:100%;display: block;" data-lazyloaded="true" />
				</a>
			</div>
			<div class="content" id="content_2" style="padding-bottom: 20px;" v-show="tabIndex == 2">
				<a onclick="goToBuy(2)" class="block-1"><img src="https://file.snkdyf.com/images/1/2023/11/o1PvQ3199V9V1kEVKpca3iC3m93Wm4.png" style="width:100%;display: block;" data-lazyloaded="true" /></a>
				<img src="https://file.snkdyf.com/images/1/2023/11/o1PvQ3199V9V1kEVKpca3iC3m93Wm4.png" style="width:100%;display: block;" data-lazyloaded="true" />
			</div>
		</div>
		<!-- <div style="height:10px; background-color: #EB5C20;"></div> -->
		<img src="https://file.snkdyf.com/images/1/2024/02/wmFmZ7W066YkYl6cq6xclkx6WuQcqj.png" style="width:100%;display:block;" data-lazyloaded="true"/> 
	</div>
</div>
<script type="text/javascript">
	/*var plans = [{php echo json_encode($activity_config['config']['plans'])}]
	var data = []
	for (var r in plans[0]){
		data.push(plans[0][r])
	};*/
	var data = {php echo json_encode($data)};
	function tabClick(index){
		$('.tab_content .flex1').removeClass('active').end().find('.item'+index).addClass('active')
		$('.content').hide();
		$('#content_'+index).show();
	}
	function goToBuy(index){
		//var goods = data[index]['goods']
		$.router.load(core.getUrl('goods/detail', {
		    id: 4929,
		    //iswholesale: 1,
		}), true)
	}
</script>
<script type="module">
	import { ref,reactive,createVNode,createApp } from 'vue';
	import { Dialog,Button } from '@nutui/nutui';

	require(['core'],function(core){
		return false;
		const app = createApp({
	        setup(){
	        	const dialogVisible = ref(false)
	        	const list = ref(data)
	        	const index = ref(0)
	        	const tabIndex = ref(1)
	        	const back = ()=>{
	        		history.go(-1)
	        	}
	        	const gobuy = ()=>{
	        		dialogVisible.value = true
	        	}
	        	const changePlan = (i)=>{
	        		index.value = i
	        	}
	        	const onCancel = ()=>{
	        	}
	        	const onOk = ()=>{
	        		var goods = list.value[index.value]['goods']
	        		$.router.load(core.getUrl('goods/detail', {
	        		    id: goods['id'],
	        		    //iswholesale: 1,
	        		}), true)
	        	}
	        	const goToBuy = (index)=>{
	        		var goods = list.value[index]['goods']
	        		window.location.href = core.getUrl('goods/detail', {
	        		    id: goods['id'],
	        		    //iswholesale: 1,
	        		})
	        		// $.router.load(core.getUrl('goods/detail', {
	        		//     id: goods['id'],
	        		//     //iswholesale: 1,
	        		// }), true)
	        	}
	        	const tabClick =(index)=>{
	        		tabIndex.value = index
	        	}
	            return {
	                back,
	                gobuy,
	                changePlan,
	                dialogVisible,
	                list,
	                index,
	                onOk,
	                onCancel,
	                tabClick,
	                tabIndex,
	                goToBuy
	            } 
	        }
	    })
	    app.use(Dialog).use(Button).mount("#activity_app");
	})
	
</script>
{template '_footer'}
