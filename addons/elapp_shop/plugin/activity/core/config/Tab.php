<?php
namespace app\plugin\activity\core\config;

class Tab implements ConfigComponent {
    private $title;
    public $name;
    public $active;
    private $children = [];

    public function __construct($title, $name, $active = false, $children = []) {
        $this->title = $title;
        $this->name = $name;
        $this->active = $active;
        $this->children = $children;
    }

    public function getChildren()
    {
        return $this->children;
    }

    public function build($recurse = true)
    {
        if ($this->active) {
            $class = 'active';
        } else {
            $class = '';
        }
        $html = <<<HTML
<li class="{$class}"><a href="#tab_{$this->name}">{$this->title}</a></li>
HTML;
        return $html;
    }
}