<?php
namespace app\plugin\activity\core\config;

class Text implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;

    public function __construct($title, $name, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
    }

    public function build($recurse = true)
    {
        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8">
        <input type="text" class="form-control" name="{$this->name}" value="{$this->value}" />
        <div class="help-block">{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }
}