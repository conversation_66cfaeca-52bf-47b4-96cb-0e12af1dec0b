<?php
namespace app\plugin\activity\core\config;
/**
 * 商品选择
 * 此插件会提交 <name> 和 <name>_text 两个参数，所以参数校验时需要同时校验这两个参数
 */
class Goods implements ConfigComponent {
    private $title;
    private $name;
    private $text;
    private $value;
    private $desc;

    public function __construct($title, $name, $text, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->text = $text;
        $this->desc = $desc;
    }

    public function build($recurse = true)
    {
        $goods = pdo_getall('elapp_shop_goods', ['id'=>$this->value], ['id', 'title','thumb']);
        $selector = tpl_selector($this->name, array(
                'preview'=>1,
                'multi'=>1,
                'autosearch'=>0,
                'value'=> $this->text,
                'url'=>webUrl('goods/query/main'),
                'items' => $goods,
                'buttontext'=>'选择商品',
                'placeholder'=>'请选择商品')
        );
        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label" >{$this->title}</label>
    <div class="col-sm-9 col-xs-12">
        $selector
    <div class='help-block'>{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }
}