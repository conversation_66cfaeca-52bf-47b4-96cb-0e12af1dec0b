<?php
namespace app\plugin\activity\core\config;

class Textarea implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;

    public function __construct($title, $name, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
    }

    public function build($recurse = true)
    {
        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8">
        <textarea name="desc" name="{$this->name}" class="form-control textarea">{$this->value}</textarea>
         <div class="help-block">{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }
}