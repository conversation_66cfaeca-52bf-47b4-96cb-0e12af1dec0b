<?php
namespace app\plugin\activity\core\config;

class Radio implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;
    private $options = [];

    public function __construct($title, $name, $value, $desc, $options) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
        $this->options = $options;
    }

    public function build($recurse = true)
    {
        $opts = '';
        foreach ($this->options as $opt) {
            $checked = '';
            if ($this->value == $opt['value']) {
                $checked = 'checked="checked"';
            }
            $opts .= '<label class="radio-inline"><input type="radio"  name="'.$this->name.'" value="'.$opt['value'].'" '.$checked.' />'.$opt['label'].'</label>';
        }

        $html = <<<HTML
<div class="form-group">
        <label class="col-lg control-label">{$this->title}</label>
        <div class="col-sm-8">
            $opts
            <div class="help-block">{$this->desc}</div>
        </div>
    </div>
HTML;
        return $html;
    }
}