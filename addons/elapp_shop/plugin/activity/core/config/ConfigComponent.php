<?php
namespace app\plugin\activity\core\config;

/**
 * 此接口用于实现配置项html代码的生成，组件内部需要实现build方法，返回组件的html代码
 * 同时组件需要在构造函数内提供必要的参数，用于组件的渲染时候填入相关数据
 *
 * 组件类型：控件组件，业务组件
 * 基础组件：用于实现单一简单配置的控件，如输入框，下拉框，单选框等不带业务逻辑的组件
 * 业务组件：将基础组件进行组合或增强，实现特定业务逻辑，如商品的选择，优惠券的选择，会员的选择，店长升级条件设置等带有特定业务逻辑的组件
 *
 * 如果当前组件依赖系统业务数据，可自行实现数据获取或通过构造函数传入：
 * 建议基础组件通过一致的数据结构传入数据，以便维护，如 Checkbox, Select, Radio 传入同样的数据结构 options 来实现选项的渲染
 * 建议业务组件，自行实现数据获取，以便调用方不需要关心数据的结构，来源等问题，如 Goods<商品选择> 组件
 *
 * 目前来说，各个组件都是独立的，但是需要注意一点，目前有一个特殊组件 tab，用于实现配置项的分组，tab组件内部可以包含其他组件
 * 所以这个组件的实现有点和其他组件不一样，需要实现getChildren方法，返回tab内部的子组件
 * 当然后续实现了多级联动的组件，也需要实现这个方法，具体实现以后用到在考虑，不过不会是所有组件都有这个方法
 * 一般情况下只有关联配置或容器类的组件才会有子组件
 * 如 ：实现店长升级功能，提供选项，1.购买特定物品，2.用户消费金额>N 3. 用户推广会员>N 这些都是扩展配置，依赖于前面的配置
 *
 *
 * todo [plan] 实现children组件，实现配置的多级联动修改
 * todo [plan] 实现自定义文字配置
 * todo [plan] 订单事件处理 => [ 给谁（订单所有者，店长，合伙人），做什么（设置等级，赠送积分，添加余额，赠送优惠券），怎么做（等级+1，赠送积分100，赠送优惠券001）]
 * todo [plan] 选择会员卡，服务费，商品组件
 */
interface ConfigComponent {
    /**
     * 返回组件的html代码，用于后台配置项的渲染
     * @param bool $recurse 是否递归渲染子组件
     * @return string
     */
    public function build($recurse);
}
