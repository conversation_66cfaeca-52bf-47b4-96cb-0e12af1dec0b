<?php
namespace app\plugin\activity\core\config;

class Editor implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;

    public function __construct($title, $name, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
    }

    public function build($recurse = true)
    {
        $editor = tpl_ueditor("{$this->name}", "{$this->value}", array('height'=>200));
        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8 col-xs-12">
        {$editor}
        <div class='help-block'>{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }
}