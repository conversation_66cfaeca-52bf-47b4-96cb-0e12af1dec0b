<?php
namespace app\plugin\activity\core\config;

class Link implements ConfigComponent {
    private $title;
    private $url;
    private $style;

    public function __construct($title, $url, $style = 1) {
        $this->title = $title;
        $this->url = $url;
        $this->style = $style;
    }

    public function build($recurse = true)
    {
        return $this->style == 2 ? $this->style_2():$this->style_1();
    }

    public function style_1()
    {
        $qrcode_credit = m('qrcode')->createQrcode($this->url);

        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8 col-xs-12">
        <input type="text" class="form-control" value="{$this->url}" readonly>
<!--        <div class="form-control-static js-clip text-primary" data-url="oyDB759VIfK8jTxg318teF6dq-5s">oyDB759VIfK8jTxg318teF6dq-5s    </div>-->
        <a href="{$this->url}" class="btn btn-default" target="_blank">打开</a>
        <a href="#" class="btn btn-default js-clip" data-url="{$this->url}">复制</a>
        <span style="cursor: pointer;" data-toggle="popover" data-trigger="hover" data-html="true"
              data-content="<img src='{$qrcode_credit}' width='130' alt='链接二维码'>" data-placement="auto right">
            <i class="glyphicon glyphicon-qrcode"></i>
        </span>
    </div>
    <div class="col-sm-1">
    </div>
</div>
HTML;
        return $html;
    }

    public function style_2()
    {
        $qrcode_credit = m('qrcode')->createQrcode($this->url);

        $html =<<<HTML
<div class="form-group">
        <label class="col-lg control-label">{$this->title}</label>
        <div class="col-sm-9 col-xs-12">
            <p class='form-control-static'>
                <a href='javascript:;' class="js-clip" title='点击复制链接' data-url="{$this->url}" >
                    {$this->url}
                </a>
                &nbsp;<a href="{$this->url}" target="_blank">打开</a>&nbsp;
                <span style="cursor: pointer;" data-toggle="popover" data-trigger="hover" data-html="true"
                      data-content="<img src='{$qrcode_credit}' width='130' alt='链接二维码'>" data-placement="auto right">
                    <i class="glyphicon glyphicon-qrcode"></i>
                </span>
            </p>
        </div>
    </div>
HTML;
        return $html;
    }
}