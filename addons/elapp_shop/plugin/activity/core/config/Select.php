<?php
namespace app\plugin\activity\core\config;

class Select implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;
    private $options = [];
    private $style;

    public function __construct($title, $name, $value, $desc, $options, $style=1) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
        $this->options = $options;
        $this->style = $style;
    }

    public function build($recurse = true)
    {
        if ($this->style == 1) {
            return $this->style_1();
        } else {
            return $this->style_2();
        }
    }

    public function style_1()
    {
        $opts = '';
        foreach ($this->options as $opt) {
            $selected = '';
            if ($this->value == $opt['value']) {
                $selected = 'selected="selected"';
            }
            $opts .= '<option value="'.$opt['value'].'" '.$selected.'>'.$opt['label'].'</option>';
        }

        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8 col-xs-12">
        <select name="{$this->name}" class='form-control select2'>
            $opts
        </select>
        <div class='help-block'>{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }

    public function style_2()
    {
        $opts = '';
        foreach ($this->options as $opt) {
            $selected = '';
            if ($this->value == $opt['value']) {
                $selected = 'selected="selected"';
            }
            $opts .= '<option value="'.$opt['value'].'" '.$selected.'>'.$opt['label'].'</option>';
        }

        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8">
        <select class="form-control" name="{$this->name}">
            $opts
        </select>
        <div class='help-block'>{$this->desc}</div>
    </div>
</div>
HTML;
        return $html;
    }
}
