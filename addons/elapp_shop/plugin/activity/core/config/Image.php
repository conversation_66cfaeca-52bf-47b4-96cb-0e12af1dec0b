<?php
namespace app\plugin\activity\core\config;

class Image implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;

    public function __construct($title, $name, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
    }

    public function build($recurse = true)
    {
        $editor = tpl_form_field_image2("{$this->name}", "{$this->value}", '');
        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-9 col-xs-12">
            {$editor}
            <span class="help-block">{$this->desc}</span>
    </div>
</div>
HTML;
        return $html;
    }
}