<?php
namespace app\plugin\activity\core\config;

/**
 * 业务组件
 * 订单相关角色选择
 */
class OrderRelatedRoles implements ConfigComponent {
    private $title;
    private $name;
    private $value;
    private $desc;
    private $options = [];

    public function __construct($title, $name, $value, $desc) {
        $this->title = $title;
        $this->name = $name;
        $this->value = $value;
        $this->desc = $desc;
        $this->options = [
            ['value' => 'member', 'label' => '用户'],
            ['value' => 'on_member', 'label' => '推荐用户'],
            ['value' => 'clerk', 'label' => '店长'],
            ['value' => 'copartner', 'label' => '机构'],
        ];
    }

    public function build($recurse = true)
    {
        $opts = '';
        foreach ($this->options as $opt) {
            $selected = '';
            if ($this->value == $opt['value']) {
                $selected = 'selected="selected"';
            }
            $opts .= '<option value="'.$opt['value'].'" '.$selected.'>'.$opt['label'].'</option>';
        }

        $html = <<<HTML
<div class="form-group">
    <label class="col-lg control-label">{$this->title}</label>
    <div class="col-sm-8">
        <select class="form-control" name="{$this->name}" id="id_{$this->name}">
            $opts
        </select>
        <div class='help-block'>{$this->desc}</div>
    </div>
    <div class="col-sm-8">
        <div class="sub-option member" style="display: none;">
            <div class="form-group">
                <label class="col-lg control-label">用户</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="member" value="">
                </div>
            </div>
        </div>
        <div class="sub-option on_member" style="display: none;">
            <div class="form-group">
                <label class="col-lg control-label">推荐用户</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="on_member" value="">
                </div>
            </div>
        </div>
        <div class="sub-option clerk" style="display: none;">
            <div class="form-group">
                <label class="col-lg control-label">店长</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="clerk" value="">
                </div>
            </div>
        </div>
        <div class="sub-option copartner" style="display: none;">
            <div class="form-group">
                <label class="col-lg control-label">机构</label>
                <div class="col-sm-8">
                    <input type="text" class="form-control" name="copartner" value="">
                </div>
            </div>
        </div>
    </div>
</div>
HTML;
        $html .= $this->javascript();
        return $html;
    }

    private function javascript()
    {
        $id="id_{$this->name}";

        $html = <<<HTML
<script type="text/javascript">
    // ...其他代码...

    $(function () {
        $('#{$id}').change(function () {
            var selectedRole = $(this).val();
            var className = selectedRole;
            // 隐藏所有子选项
            $('.sub-option').hide();
            // 显示选定角色的子选项
            $('.' + className).show();
        });
    });

    function getSubOptionsForRole(role) {
        // 这个函数应该返回给定角色的子选项。具体实现取决于子选项的来源。
        // 例如，如果子选项嵌入在HTML中，这个函数可以从HTML中获取子选项；
        // 如果子选项通过AJAX请求获取，这个函数可以发送AJAX请求并返回结果。
    }

    function updateSubOptions(subOptions) {
        // 这个函数应该更新子选项的显示。具体实现取决于你的HTML结构。
        // 例如，如果子选项是一个选择框，这个函数可以清空选择框并添加新的选项。
    }

    // ...其他代码...
</script>
HTML;
        return $html;

    }
}