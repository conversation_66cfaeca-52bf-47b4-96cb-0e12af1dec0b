<?php
namespace app\model;

use app\controller\activity\OrderCreateAction;
use app\controller\activity\OrderPayAction;
use app\controller\activity\OrderRefundApplyAction;
use app\controller\activity\OrderSignOffAction;
use app\controller\activity\SingleRefundAction;
use app\controller\activity\OrderRefundAction;
use app\controller\activity\Action;
use app\controller\activity\SingleRefundApplyAction;

if (!class_exists('ActivityModel')) {
    class ActivityModel extends PluginBaseModel
    {
        public function __construct()
        {
            parent::__construct();
        }


        public function hook($action, $data)
        {
            $actionObject = null;
            $result = false;
            // 安装具体的 hook 类型执行具体的 action 任务

            if ($action == Action::ORDER_SINGLE_REFUND) {
                $actionObject = new SingleRefundAction();
            } else if ($action == Action::ORDER_REFUND) {
                $actionObject = new OrderRefundAction();
            } else if ($action == Action::ORDER_SINGLE_REFUND_APPLY) {
                $actionObject = new SingleRefundApplyAction();
            } else if ($action == Action::ORDER_REFUND_APPLY) {
                $actionObject = new OrderRefundApplyAction();
            } else if ($action == Action::ORDER_CREATE) {
                $actionObject = new OrderCreateAction();
            } else if ($action == Action::ORDER_PAY) {
                $actionObject = new OrderPayAction();
            } else if ($action == Action::ORDER_SIGNOFF) {
                $actionObject = new OrderSignOffAction();
            }

            if ($actionObject !== null) {
                $result = $actionObject->run($data);
            }

            return $result;
        }

        // todo 980 优化
        public function settle_role_key($role_key)
        {
            switch ($role_key) {
                case SettleModel::ROLE_KEY_CLERK:
                    $role_type = 'activity_1_1';
                    break;
                case SettleModel::ROLE_KEY_CLERK_2:
                    $role_type = 'activity_1_2';
                    break;
                default:
                    $role_type = $role_key;
            }
            return $role_type;
        }

        public function getActivityCommissionName($key, $role_type)
        {
            // 如果key 存在 activity_1_1
            if (strpos($key, 'activity_1_1') !== false) {
                $commission_name = '老带新直推收益';
            } else if (strpos($key, 'activity_1_2') !== false) {
                $commission_name = '老带新间推收益';
            } else {
                $commission_name = '活动收益';
            }

            $role_type == SettleModel::ROLE_TYPE_ACTIVITY_REFUND && $commission_name .= '（退款）';

            return $commission_name;
        }

        // 这个人能不能结算980收益
        public function canSettle($role_type, $order) {
            // 如果商品非活动商品，不需要判断，直接返回
            if ($order['activity_id'] == 0) {
                return true;
            }

            if ($order['clerk_id'] == 0) {
                return false;
            }

            if ($role_type == SettleModel::ROLE_TYPE_CLERK) {
                $member_id = $order['clerk_id'];
            } else if ($role_type == SettleModel::ROLE_TYPE_CLERK_2) {
                // 获取店员上级店员
                $sql = 'select id,openid from '.tablename('elapp_shop_member').' where id in (select clerk_id from ims_elapp_shop_member where id = :clerk_id)';
                $member = pdo_fetch($sql, [':clerk_id' => $order['clerk_id']]);
                $member_id = $member['id'];
            } else {
                return false;
            }

            $is = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $member_id, DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
            return $is == 1;
        }

        /**
         * 980活动订单能否签收
         * @param $order
         * @return bool
         */
        public function orderCanReceive($order)
        {
            $member = m('member')->getMember($order['member_id']);

            $finish = false;

            // 如果用户是活动用户（23-10-25 这里不明白为什么要先判断用户是不是活动用户，按道理只需要判断order是活动订单）
            if ($this->isActivityMemberByOrder($member['id'])) {
                $finish = $this->isMemberFinishActivity($member);
            }

            // 如果是活动方案A，也允许签收
            $plan = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_ORDER, $order['id'], DiyattrsEnums::KEY_ACTIVITY_980_ORDER_PLAN);
            if ($plan == DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_A) {
                return true;
            }

            return $finish;
        }

        /**
         * 用户已完成980活动要求
         * @param $member
         * @return bool
         */
        public function isMemberFinishActivity($member)
        {
            return true;
            // 获取用户收益
            $over_num = 980;
            if (empty($member)) return false;

            $commission = $this->getCommission($member['id']);

            if (empty($commission)) return false;

            return bccomp($commission, $over_num, 2) >= 0;
        }

        /**
         * 获取用户分润金额
         * @param $mid
         * @return int|mixed
         */
        function getCommission($mid) {
            $sql = "SELECT ifnull(SUM(commission - commission_wait),0) AS total_amount FROM " . tablename('elapp_shop_settle_order');
            $sql .= " where belong_to =:mid and status = 1 and role_type != " . SettleModel::ROLE_TYPE_COPARTNER;
            $params = [':mid'=>$mid];

            $value = pdo_fetch($sql, $params);

            return $value['total_amount'] ?? 0;
        }

        public function isActivityMemberByOrder($member_id)
        {
            return 1 == (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $member_id, DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
        }

        public function getActivityConfig($id, $copartner_id = 0)
        {
            
            // $result = [
            //     "id"      => "1",
            //     "uniacid" => 1,
            //     'title'   => '980元一部手机开药房',
            //     'key'     => 'key',
            //     'status'  => 1,
            //     // custom
            //     'config'  => [
            //         'content' => '<img alt="详情页.png" style="max-width: 100%" src="https://file.snkdyf.com/images/1/2023/10/zPoJVUvj133o22JD56010Mj2m2jRkW.png" data-lazyloaded="true">',
            //         'plans'   => [
            //             4787 => [
            //                 'key'                  => '980_user_plan_a',
            //                 'name'                 => 'plan_a',
            //                 'title'                => '方案A',
            //                 'goods'                => ['id' => 4787, 'title' => '980活动A商品名称'],
            //                 '980_protocol_title'   => 'A方案协议',
            //                 '980_protocol_content' => '980活动A协议内容',
            //                 'must_check'           => true,
            //                 'show'                 => true,
            //             ],
            //             4788 => [
            //                 'key'                  => '980_user_plan_b',
            //                 'name'                 => 'plan_b',
            //                 'title'                => '方案B',
            //                 'goods'                => ['id' => 4788, 'title' => '980活动B商品名称'],
            //                 '980_protocol_title'   => 'B方案协议',
            //                 '980_protocol_content' => '980活动B协议内容',
            //                 'must_check'           => true,
            //                 'show'                 => true,
            //             ],
            //         ],
            //     ],
            // ];


            // return $result;


            $sql = 'select * from ' . tablename('elapp_shop_activity') . ' where id = :id';
            $activity = pdo_fetch($sql, [':id' => $id]);

            if (empty($activity)) {
                return false;
            }

            $activity['config'] = json_decode($activity['config'], true);

            // 如果是合伙人，获取合伙人的配置
            if ($copartner_id > 0) {
                $sql = 'select * from ims_elapp_shop_activity_copartner where activity_id = :id and copartner_id = :copartner_id';
                $copartner = pdo_fetch($sql, [':id' => $id, ':copartner_id' => $copartner_id]);

                if (!empty($copartner)) {
                    $activity['config'] = array_merge($activity['config'], json_decode($copartner['config'], true));
                }
            }
            return $activity;
        }

        public function getActivityList()
        {
            global $_W;

            $activities = pdo_fetchall('select id,title from ' . tablename('elapp_shop_activity') .
                ' where status = 1 and uniacid = :uniacid', [':uniacid' => $_W['uniacid']]);

            return $activities;
        }
    }
}
