<?php
namespace app\controller\activity;


use app\model\ClerkModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\MemberModel;
use app\plugin\activity\core\common\Operate\CreditOperate;
use app\plugin\clerk\core\logic\ClerkLevelLogic;

/**
 * 订单支付
 */
class OrderPayAction extends Action
{
    /**
     * @param $data OrderPayData
     */
    private function getOrder($data)
    {
        $tables = [
            OrderPayData::ORDER_TYPE_MEMBER_CARD => 'elapp_shop_member_card_order',
            OrderPayData::ORDER_TYPE_GOODS => 'elapp_shop_order',
            OrderPayData::ORDER_TYPE_SERVICE_FEE => 'elapp_shop_member_servicefee_order',
        ];
        $order = pdo_get($tables[$data->getOrderType()], ['id' => $data->getOrderId()]);
        return $order;
    }

    /**
     * @param $data OrderPayData
     * @return void
     */
    public function run($data)
    {
        // 活动数据处理
        // todo [优化] 后期提取活动抽象，不同活动执行不同的处理
        $order = $this->getOrder($data);
        $this->writeActivityData($order, $data);

        $actionResult = null;

        if ($order['activity_id']) {
            $action = $this->getActionWithActivity($order['activity_id']);
            if ($action instanceof Activity) {
                $actionResult = $action->pay($data);
            }
        }

        // 读取订单信息，获取订单商品信息
        // 遍历商品，获取商品绑定事件处理
        // todo 执行时间的条件判断 => (已经购买过不赠送，已经赠送过不再赠送，普通会员不赠送等) 不过目前可以忽略这个问题
        // 执行事件处理


        // 改谁的值 （用户，上级用户，机构，上级机构，推荐用户）
        // 改谁的什么值 （积分，余额，优惠券，会员卡）
        // 怎么改  （增加，减少，设置固定值）
        // 日志记录
        // 通用流程：不同商品的流程是可能是一样的，所以需要抽象出来，比如（分润、订单通知、赠送积分、赠送优惠券、赠送会员卡、赠送余额）

        // 配置读取类处理，后台配置和这里同时需要用到

        // todo 问题：针对赠送积分的抽象没有做，需要从配置或参数中获取，而非写死 => 抽象配置读取
        // 赠送积分
//        $credit = 10;
//        (new CreditOperate())->setCredit($data->getMemberId(), $credit, "支付订单赠送积分 {$credit}");

        if ($data->getOrderType() == OrderPayData::ORDER_TYPE_GOODS) {
            /** @var MemberModel $model */
            $model = m('member');
            $openid = $model->getOpenid($data->getMemberId());

            $goodsids = pdo_getall('elapp_shop_order_goods', ['orderid'=>$data->getOrderId()], ['goodsid']);

            foreach ($goodsids as $goodsid) {
                $events = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_GOODS, $goodsid, DiyattrsEnums::EVENT_CONFIG);
                if ($events && is_array($events) && isset($events[Action::ORDER_PAY])) {
                    //(new MemberCardOperate())->sendMemberCard($data->getMemberId(), $goodsid, "购买商品赠送会员卡");
                    $event_send_member_card_id = $events[Action::ORDER_PAY]['send_member_card'];

                    //赠送会员卡权益
                    //检测会员是否已有会员卡
                    $checkmcard = p('membercard')->check_Hasget($event_send_member_card_id, $openid);
                    //如果当前存在会员卡ID等于要赠送的会员卡则不执行操作
                    // 永久有效 和 使用中 则不送
                    if (($checkmcard['using'] != 2 && $checkmcard['using'] != 1) && !empty($openid)) {
                        //执行系统发付费会员卡
                        $Result = p('membercard')->systemCardIssuing($openid, $event_send_member_card_id, true);
                    }

                    //赠送店员资格权益
    //                if (empty($member['is_clerk'])) {
    //                    $clerk_update = array(
    //                        'is_clerk' => 1,
    //                        'clerk_create_time' => time(),
    //                        'clerk_status' => 1
    //                    );
    //                    pdo_update('elapp_shop_member', $clerk_update, array('id' => $member['id']));
    //                    //消息通知店员
    //                    $clerk_plugin->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'clerk_create_time' => $clerk_update['clerk_create_time']), TM_CLERK_BECOME);
    //                }
                }
            }
        }

        // 购买商品赠送会员卡

        return $actionResult;
    }

    /**
     * @param $order
     * @param $data OrderPayData
     * @return void
     */
    private function writeActivityData($order, $data)
    {
        // todo 这里进行调用支付完成的方法
        if ($order['activity_id'] == 1 && $data->getOrderType() == OrderPayData::ORDER_TYPE_GOODS) {
            // 980 活动用户写入
            (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER, 1);
            (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE, 1);

            // 写入用户购买活动方案 A|B
            $plan = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_ORDER, $order['id'], DiyattrsEnums::KEY_ACTIVITY_980_ORDER_PLAN);
            if ($plan && in_array($plan, [DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_A,DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_B])) {
                (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], $plan, 1);
            }

            // 根据AB方案的推广用户，设置分润
            $clerkIsPlanB = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $order['clerk_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_B);
            // 如果上级店员是B方案的，那么更改上级店员分润
            if ($clerkIsPlanB) {
                $this->activity980PlanBCommission($order);
            }
        }
    }

    /**
     * 980活动方案分润
     * @param $order
     * @return void
     */
    private function activity980PlanBCommission($order)
    {
        /** @var ClerkModel $clerkplugin */
        $clerkplugin = p('clerk');

        $levels = app(ClerkLevelLogic::class)->getClerkLevels();

        // todo [优化] 存在不需要的字段，需要优化
        $goods = pdo_fetchall(
            "select og.id,og.realprice,og.costprice,og.total,g.hasoption,g.medicineClassID,g.medicineAttributeID,og.goodsid,og.optionid,g.hasClerkCommission,g.noClerkCommission, g.clerkCommission1_rate,g.clerkCommission1_pay,g.clerkCommission2_rate,g.clerkCommission2_pay,g.clerkCommission3_rate,g.clerkCommission3_pay,g.clerkCommission,og.clerkCommissions,og.seckill,og.seckill_taskid,og.seckill_timeid from " . tablename('elapp_shop_order_goods') . '  og '
            . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id = og.goodsid'
            . ' where og.orderid=:orderid and og.uniacid=:uniacid and g.activity_id=1', array(':orderid' => $order['id'], ':uniacid' => $order['uniacid']));

        foreach ($goods as $cinfo) {
            $cinfo['clerkCommission1'] = array('default' => 200);
            $cinfo['clerkCommission2'] = array('default' => 0);
            $cinfo['clerkCommission3'] = array('default' => 0);

            foreach ($levels['data'] as $level) {
                $cinfo['clerkCommission1']['level' . $level['id']] = 200;
                $cinfo['clerkCommission2']['level' . $level['id']] = 0;
                $cinfo['clerkCommission3']['level' . $level['id']] = 0;
            }

            $cinfo['commissions'] = iunserializer($cinfo['clerkCommissions']);

            $commissions = array(
                'level1' => 200,
                'level2' => 0,
                'level3' => 0,
                'level1_level_id' => $cinfo['commissions']['level1_level_id'],
                'level2_level_id' => $cinfo['commissions']['level2_level_id'],
                'level3_level_id' => $cinfo['commissions']['level3_level_id'],
            );

            pdo_update('elapp_shop_order_goods', array(
                'clerkCommission1' => iserializer($cinfo['clerkCommission1']),
                'clerkCommission2' => iserializer($cinfo['clerkCommission2']),
                'clerkCommission3' => iserializer($cinfo['clerkCommission3']),
                'clerkCommissions' => iserializer($commissions),
            ), array('id' => $cinfo['id']));
        }
    }
}