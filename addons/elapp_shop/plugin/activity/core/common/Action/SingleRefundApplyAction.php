<?php
namespace app\controller\activity;

use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;

class SingleRefundApplyAction extends Action
{
    /**
     * @param $data OrderSingleRefundApplyData
     * @return mixed|void
     */
    public function run($data)
    {
        // todo yh 判断订单来源活动，调用相关活动处理类
        // todo yh 目前只有一个活动且时间短，暂时不写相关处理直接编写处理
        $actionResult = null;
        $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()]);

        if (!empty($order['activity_id'])) {
            $action = $this->getActionWithActivity($order['activity_id']);
            if ($action instanceof Activity) {
                $actionResult = $action->singleRefundApply($data);
            }
        }

        return $actionResult;
    }
}