<?php
namespace app\controller\activity;

class Action
{
    // 订单单品退款
    const ORDER_SINGLE_REFUND = 'order_single_refund';
    // 订单退款
    const ORDER_REFUND        = 'order_refund';
    // 订单单品退款申请
    const ORDER_SINGLE_REFUND_APPLY = 'order_single_refund_apply';
    // 订单退款申请
    const ORDER_REFUND_APPLY  = 'order_refund_apply';
    // 订单创建
    const ORDER_CREATE        = 'order_create';
    // 订单支付
    const ORDER_PAY           = 'order_pay';
    // 订单签收
    const ORDER_SIGNOFF       = 'ORDER_SIGNOFF';

    public function getActionWithActivity(int $activity_id)
    {
        $map = [
            '1'=> Activity980Clerk::class,
            '2'=> Activity9800Copartner::class,
            '3'=> ActivityEggTuanGift::class,
            '4'=> Activity9800ActivateSDM::class
        ];
        $class = $map[$activity_id] ?? null;
        if (!$class || !class_exists($class)) {
            return null;
        }
        return new $class();
    }
}