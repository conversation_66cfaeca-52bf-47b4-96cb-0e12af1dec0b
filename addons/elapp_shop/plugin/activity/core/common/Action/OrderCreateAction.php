<?php
namespace app\controller\activity;


use app\model\ActivityModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\SettleModel;

/**
 * 订单创建
 */
class OrderCreateAction extends Action
{
    private $activity_id = 0;
    /**
     * @param $data OrderCreateData
     * @return void
     */
    public function run($data)
    {
        // 写入订单活动记录
        // todo 订单行为
        // todo 针对商品的行为
        // todo 针对用户的行为
        // 写入订单活动类型
        $this->writeOrderActivity($data);
        // 修改订单类型
        $this->writeOrderGoodsType($data);

        if ($this->activity_id) {
            $action = $this->getActionWithActivity($this->activity_id);
            if ($action instanceof Activity) {
                $action->create($data);
            }
        }

        // 读取订单信息，获取订单商品信息
        // 遍历商品，获取商品绑定事件处理
        // 执行事件处理
        // 赠送积分
    }
    private function getGoods($orderid)
    {
        $og = tablename('elapp_shop_order_goods');
        $g = tablename('elapp_shop_goods');
        $sql = "select og.id,goodsid,g.activity_id from {$og} og left join {$g} g on og.goodsid=g.id where og.orderid=:orderid";
        $cond = [':orderid' => $orderid];
        $goods = pdo_fetchall($sql, $cond);
        return $goods;
    }

    private function getMemberCards($orderid)
    {
        $og = tablename('elapp_shop_member_card_order_card');
        $g = tablename('elapp_shop_member_card');
        $sql = "select og.id,cardsid,g.activity_id from {$og} og left join {$g} g on og.cardsid=g.id where og.orderid=:orderid";
        $cond = [':orderid' => $orderid];
        $ogs = pdo_fetchall($sql, $cond);
        return $ogs;
    }

    private function writeOrderActivity(OrderCreateData $data)
    {
        if ($data->getOrderType() == OrderCreateData::ORDER_TYPE_GOODS) {
            // 商品订单
            $this->writeGoodsOrderActivity($data);
        } else if ($data->getOrderType() == OrderCreateData::ORDER_TYPE_MEMBER_CARD) {
            // 会员卡订单
            $this->writeMemberCardOrderActivity($data);
        } else if ($data->getOrderType() == OrderCreateData::ORDER_TYPE_SERVICE_FEE) {
            // todo 服务费订单
        }
    }

    private function writeOrderGoodsType(OrderCreateData $data)
    {
        if ($data->getOrderType() == OrderCreateData::ORDER_TYPE_GOODS) {
            $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()], ['id','activity_id']);
            $type = (new SettleModel())->getOrderGoodsTypeString($order);
            $goods_type = (new SettleModel())->getOrderGoodsTypeID($type);
            pdo_update('elapp_shop_order', ['goods_type' => $goods_type], ['id' => $data->getOrderId()]);
        }
    }

    private function writeGoodsOrderActivity(OrderCreateData $data)
    {
        // 读取所有商品
        $goods = $this->getGoods($data->getOrderId());

        foreach ($goods as $good) {
            // 读取商品绑定的活动
            if ($good['activity_id'] != 0) {
                $this->activity_id = intval($good['activity_id']);

                // 写入订单活动记录
                pdo_update('elapp_shop_order', ['activity_id' => $good['activity_id']], ['id' => $data->getOrderId()]);

                // 记录用户参与的980活动计划类型
                $activityModel = new ActivityModel();
                $member =  m('member')->getMember($data->getMemberId());
                $config = $activityModel->getActivityConfig($good['activity_id'], $member['copartner_id']);
                $activity_plan = $config['config']['plans'][$good['goodsid']] ?? [];
                if ($activity_plan && $activity_plan['key']) {
                    (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_ORDER, $data->getOrderId(),DiyattrsEnums::KEY_ACTIVITY_980_ORDER_PLAN, $activity_plan['key']);
                }
                break;
            }
        }
    }

    private function writeMemberCardOrderActivity(OrderCreateData $data)
    {
        // 读取订单会员卡
        $cards = $this->getMemberCards($data->getOrderId());
        foreach ($cards as $item) {
            // 读取商品绑定的活动
            if ($item['activity_id'] != 0) {
                $this->activity_id = intval($item['activity_id']);
                // 写入订单活动记录
                pdo_update('elapp_shop_member_card_order', ['activity_id' => $this->activity_id], ['id' => $data->getOrderId()]);
            }
        }
    }
}