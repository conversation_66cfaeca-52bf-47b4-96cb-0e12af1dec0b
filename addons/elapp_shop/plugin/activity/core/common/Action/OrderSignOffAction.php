<?php
namespace app\controller\activity;


use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;

/**
 * 订单签收
 */
class OrderSignOffAction extends Action
{
    /**
     * @param $data OrderSignOffData
     * @return void
     */
    public function run($data)
    {
        $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()]);
        $actionResult = null;

        if ($order['activity_id']) {
            $action = $this->getActionWithActivity($order['activity_id']);
            if ($action instanceof Activity) {
                $actionResult = $action->signOff($data);
            }
        }
        
        return $actionResult;
    }
}