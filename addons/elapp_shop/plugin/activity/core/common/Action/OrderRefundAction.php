<?php
namespace app\controller\activity;


use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;

class OrderRefundAction extends Action
{
    /**
     * @param $data OrderRefundData
     * @return void
     */
    public function run($data)
    {
        // todo 判断订单来源活动，调用相关活动处理类
        // todo 目前只有一个活动且时间短，暂时不写相关处理直接编写处理
        $actionResult = null;

        // 获取订单
        $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()]);
        if ($order && $order['activity_id']) {
            // handleresult = 退款成功
            if ($order['activity_id'] == 1 && $data->getHandleResult() == 1) {

                // todo 980 调用相关活动处理类
                $clerk = p('clerk');
                if ($clerk) {
                    // todo 980 处理用户实际已经升级了为店员，不需要降级清理数据
                    $member = m('member')->getMember($order['openid']);
                    if ($member['is_clerk'] == 1 && $member['clerk_level'] == 0) {
                        $clerk->clearClerkIdentity($member['id']);
                    }
                }

                // todo 逻辑重复了，建议抽象出来
                $count = pdo_count('elapp_shop_order', ['member_id' => $order['member_id'], 'activity_id'=>1, 'id !='=>$order['id'], 'status in' => [1,2,3]]);
                if ($count<1) {
                    // 清理980活动用户身份
                    (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
                    (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE);
//                    (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_A);
//                    (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_B);

                    // 获取订单计划类型，当不存在该类型的所有订单，那么删除用户的相关方案记录
                    $plan_type = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_ORDER, $order['id'], DiyattrsEnums::KEY_ACTIVITY_980_ORDER_PLAN);
                    $count = pdo_count('elapp_shop_order', ['member_id' => $order['member_id'], 'activity_id'=>1, 'id !='=>$order['id'], 'status in' => [1,2,3]]);
                    if ($count<1) {
                        (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], $plan_type);
                    } else {
                        // 如果存在订单，那么循环遍历，判断是否存在该类型的订单，如果不存在，那么删除用户的相关方案记录
                        $orders = pdo_getall('elapp_shop_order', ['member_id' => $order['member_id'], 'activity_id'=>1, 'id !='=>$order['id'], 'status in' => [1,2,3]]);
                        foreach ($orders as $o) {
                            $order_plan = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_ORDER, $o['id'], DiyattrsEnums::KEY_ACTIVITY_980_ORDER_PLAN);
                            if ($order_plan == $plan_type) {
                                (new DiyattrsModel())->deleteValue(DiyattrsEnums::TYPE_USER, $order['member_id'], $plan_type);
                                break;
                            }
                        }
                    }
                }
            }
        }

        if (!empty($order['activity_id'])) {
            $action = $this->getActionWithActivity($order['activity_id']);
            if ($action instanceof Activity) {
                $actionResult = $action->refund($data);
            }
        }

        return $actionResult;
    }
}