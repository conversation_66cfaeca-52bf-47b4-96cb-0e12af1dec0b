<?php
namespace app\controller\activity;

use app\controller\settle\common\checker\condition\IsActivityOpenChecker;
use app\controller\settle\common\checker\condition\IsNOrderPayedChecker;
use app\controller\settle\common\checker\condition\IsSeckillOrderChecker;
use app\controller\settle\common\checker\param\OrderCheckParameter;
use app\model\Activity2Model;
use app\model\ClerkModel;
use app\model\CopartnerModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\plugin\activity\core\config\Checkbox;
use app\plugin\activity\core\config\Editor;
use app\plugin\activity\core\config\Goods;
use app\plugin\activity\core\config\Link;
use app\plugin\activity\core\config\Radio;
use app\plugin\activity\core\config\Select;
use app\plugin\activity\core\config\Tab;
use app\plugin\activity\core\config\Text;
use app\plugin\activity\core\config\Title;
use app\plugin\activity\core\validate\Activity3ConfigValidate;

class ActivityEggTuanGift extends BaseActivity
{
    protected $activity_id = 3;

    protected $name = '推荐送鸡蛋';

    protected $configValidate = Activity3ConfigValidate::class;

    protected $config;

    private function _coupons()
    {
        global $_W;
        $coupons = pdo_getall('elapp_shop_coupon', ['uniacid'=>$_W['uniacid']]);
        // 从coupons提取 id,couponname 组成新数组
        $coupons = array_map(function($coupon){
            return [
                'label'=>$coupon['couponname'],
                'value'=>$coupon['id']
            ];
        }, $coupons);
        return  array_merge([['label'=>'请选择', 'value'=>0]], $coupons);
    }
    public function configComponents()
    {
        $model = Activity2Model::find($this->activity_id);
        if ($model && $model['config']) {
            $this->config = $model['config'];
        }

        $coupons = $this->_coupons();
        $member_cards = $this->_member_cards();

        return [
            new Tab('基本', 'basic', true, [
                new Link('活动连接', mobileUrl('activity/Pintuan/main',null,true)),
                // ----
                new Title('活动公共配置'),
                new Text('活动标题', 'title', $model['title'] ?? '', ''),
                new Radio('活动状态', 'status', $model['status'] ?? 0, '', [
                    ['label'=>'启用', 'value' => 1],
                    ['label'=>'禁用', 'value' => 0],
                ]),
            ]),
            new Tab('活动内部配置', 'config', false, [
                new Title('活动内部配置'),
                new Select('赠送优惠券', 'coupon_id', $this->config['coupon_id'] ?? 0, '', $coupons),
                new Text('成团人数', 'target_count', $this->config['target_count'] ?? 3, ''),
                new Checkbox('参与活动的会员类型', 'member_card_ids[]', $this->config['member_card_ids'] ?? [], '', $member_cards),
            ]),
            new Tab('首页配置', 'index_page', false, [
                new Text('标题', 'index_page_title', $this->config['index_page_title'] ?? '', ''),
                new Text('描述', 'index_page_desc', $this->config['index_page_desc'] ?? '', ''),
                new Text('海报ID', 'index_page_poster_id', $this->config['index_page_poster_id'] ?? 0, '跳转的分享海报ID'),
                new Select('会员卡购买跳转', 'index_page_member_card_id', $this->config['index_page_member_card_id'] ?? 0, '用户无权限时点击会员卡购买时跳转的会员卡', $member_cards),
                new Goods('活动商品', 'index_page_goods', $this->config['index_page_goods_text'] ?? '', $this->config['index_page_goods'] ?? [], '此为商品推荐时跳转的商品，仍需要到指定商品配置所属活动'),
                new Text('订单数据为空时显示文本', 'index_page_empty_data_text', $this->config['index_page_empty_data_text'] ?? '', ''),
                new Editor('活动规则', 'index_page_rule_text', $this->config['index_page_rule_text'] ?? '', ''),
            ]),
        ];
    }

    public function pay(OrderPayData $data)
    {
        if (!$this->isEnabled()) return;

        $member = m('member')->getMember($data->getMemberId());
        if (empty($member['onmid'])) {
            return;
        }
        $onmid_member = m('member')->getMember($member['onmid']);

        $logic = new EggTuanGiftLogic();
        if (!$logic->hasEggTuanPermission($onmid_member['id'])) {
            return;
        }

        if ($data->getOrderType() == OrderPayData::ORDER_TYPE_GOODS) {
            $goods = pdo_getall('elapp_shop_order_goods', ['orderid'=>$data->getOrderId()], ['goodsid', 'title']);
            $goods_id = $goods[0]['goodsid'];
            $goods_title = $goods[0]['goodsid'];
            $order_type = 0;
        } else if ($data->getOrderType() == OrderPayData::ORDER_TYPE_MEMBER_CARD) {
            $goods = pdo_getall('elapp_shop_member_card_order_card', ['orderid'=>$data->getOrderId()], ['cardsid']);
            $goods_id = $goods[0]['cardsid'];
            $goods_title = pdo_getcolumn('elapp_shop_member_card', ['id' => $goods_id], 'name');
            $order_type = 1;
        } else {
            return;
        }

        // 加入推荐团
        $logic->joinTuan($data->getOrderId(), $order_type, $member, $goods_id, $goods_title, $onmid_member['id'], $this->activity_id);
    }

    public function refund(OrderRefundData $data)
    {
        $this->_refund($data->getOrderId(), $data->getHandleResult());
    }

    public function singleRefund(OrderSingleRefundData $data)
    {
        $this->_refund($data->getOrderId(), $data->getHandleResult());
    }

    /**
     * 退款审核完成
     * 1. 退款数量-1
     *
     * @param $order_id
     * @return void
     */
    private function _refund($order_id, $handleResult)
    {
        $logic = new EggTuanGiftLogic();
        $tuan = $logic->getTuanRecordByOrderId($order_id);

        if (empty($tuan)) {
            return;
        }

        // 如果已经领券，那么不做任何处理
        if ($tuan['coupon_status'] == 1) {
            return;
        }

        // 无论如何退款处理完成后，当前退款数量都会扣除表示退款处理完成
        $logic->increasingCount($tuan['id'], 'refund_count', -1);

        if ($handleResult == OrderRefundData::REFUND_AGREE) {//退款成功
            // remove finish_ids, finish_count--,current_count--
            $logic->delete_order_data($tuan, $order_id);

        } else if ($handleResult == OrderRefundData::REFUND_REJECT||$handleResult == OrderRefundData::REFUND_CANCEL) {// 驳回申请或取消退款
            $logic->finish_tuan($tuan['id']);
        }
    }

    public function refundApply(OrderRefundApplyData $data)
    {
        $this->_refund_apply($data->getOrderId());
    }

    public function singleRefundApply(OrderSingleRefundApplyData $data)
    {
        $this->_refund_apply($data->getOrderId());
    }

    /**
     * 退款申请
     * 0. 如果用户已经领券，那么不做任何处理，否则将继续处理
     * 1. 将退款数量+1
     * 2. 如果团状态设置为未完成，防止退款中途领券
     * @param $order_id
     * @return void
     */
    private function _refund_apply($order_id)
    {
        $logic = new EggTuanGiftLogic();
        $tuan = $logic->getTuanRecordByOrderId($order_id);

        // 如果已经领券，那么不做任何处理
        if ($tuan['coupon_status'] == 1) {
            return;
        }

        // 退款数量+1
        $logic->increasingCount($tuan['id'], 'refund_count', 1);
        // 订单未完成，不需要变更状态
        if ($tuan['status'] == 1) {
            $logic->updateTuanStatus($tuan['id'], 0);
        }
    }

    public function signOff(OrderSignOffData $data)
    {
        file_put_contents('/www/wwwroot/qmgxyf.com/runtime/log/orderSignOff.log', "signOff:before" . $data->getOrderId() . "\n", FILE_APPEND);
        if (!$this->isEnabled()) return;
        file_put_contents('/www/wwwroot/qmgxyf.com/runtime/log/orderSignOff.log', "signOff:after" . $data->getOrderId() . "\n", FILE_APPEND);

        $logic = new EggTuanGiftLogic();
        $record = $logic->getTuanRecordByOrderId($data->getOrderId());
        $logic->orderSignOff($record, $data->getOrderId());
    }

    private function _member_cards()
    {
        $list = pdo_getall('elapp_shop_member_card', [], ['id', 'name','name2']);
        $result = [];
        foreach ($list as $item) {
            $result[] = ['label'=> $item['id'] . '-' . $item['name'] . ($item['name2']?"【{$item['name2']}】":''), 'value'=>$item['id']];
        }
        return $result;
    }
}