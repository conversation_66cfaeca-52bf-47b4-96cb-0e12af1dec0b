<?php
namespace app\controller\activity;

use app\controller\copartner\common\users\ApprovalCopartnerUsers;
use app\controller\org\common\users\CreateCopartnerUsers;
use app\controller\settle\common\checker\condition\IsSeckillOrderChecker;
use app\controller\settle\common\checker\param\OrderCheckParameter;
use app\model\CopartnerModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\MentorModel;
use app\model\OrgModel;

class Activity9800ActivateSDM extends BaseActivity
{
    public $activity_id = 4;

    public $name = '9800激活SDM创业大礼包';

    /**
     * 第N单分润9800
     *
     * @param OrderPayData $data
     * @return mixed|void
     */
    public function pay(OrderPayData $data)
    {
        if ($data->getOrderType() != OrderPayData::ORDER_TYPE_GOODS) return;
        $config = $this->getConfig();
        if (!$config['enabled']) {
            return;
        }

        $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()]);
        if (!$order) {
            return;
        }

        $member = m('member')->getMember($order['openid']);
        // 创建合伙人/分公司账号reg & user
        $createUser_result = (new CreateCopartnerUsers())->create($member);
        // 审批合伙人/分公司账号
        if ($createUser_result['code'] == 0 && $createUser_result['data']['reg_id']) {
            // 1.审批reg表
            $orgSet = m('common')->getPluginset('org');
            $approvalUsers_reg_result = (new ApprovalCopartnerUsers())->approvalReg($createUser_result['data']['reg_id'], 1, $orgSet['template_copartner_id'] ?? 0);
            if ($approvalUsers_reg_result['code'] == 0) {
                // 2.审批user表并创建account表
                $approvalUsers_user_result = (new ApprovalCopartnerUsers())->approvalUser($approvalUsers_reg_result['data']['user_id'], 1);
                if ($approvalUsers_user_result['code'] == 1) {
                    plog('org.account.activateApprovalUser', 'MID:[' . $member['id'] . ']' . $member['nickname'] . '<br>审批账号失败<br>' . 'errMsg:' . $approvalUsers_user_result['msg']);
                }
            } else {
                plog('org.account.activateApprovalReg', 'MID:[' . $member['id'] . ']' . $member['nickname'] . '<br>审批注册失败<br>' . 'errMsg:' . $approvalUsers_reg_result['msg']);
            }
        } else {
            plog('org.account.activateCreateUser', 'MID:[' . $member['id'] . ']' . $member['nickname'] . '<br>创建账号失败<br>' . 'errMsg:' . $createUser_result['msg']);
        }

        // 跳转到合伙人登录页
        $return = ['redirect_url' => mobileUrl('copartner/login/main')];

        // 9800,980 活动用户写入
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER, 1);
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE, 1);
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_9800_USER, 1);

        $param = new OrderCheckParameter($order);
        $isSeck = IsSeckillOrderChecker::newInstance()->check($param);
        // 如果是秒杀订单，需求变更分润300
        if ($isSeck->getResult()) {
            $this->activity980PlanCommission($order, 300);
        } else {
//            $result = IsNOrderPayedChecker::newInstance($this->activity_id, $config['n_order_index'])->check($param);
//            // 独立分润获取
//            $commission = $config['n_order_commission'];
//            // todo 更新分润
//            $this->activity980PlanBCommission($order, $commission);
        }

        // todo 等待后期优化调整结构
        return $return;
    }

    public function getConfig()
    {
        $value = pdo_getcolumn('elapp_shop_activity', ['id' => $this->activity_id], 'config');
        if ($value) {
            $value = json_decode($value, true);
            return $value;
        } else {
            // N单分润，N单索引
            return ["enabled"=>true];
        }
    }

    private function mergeCommission($copartnerCommissionString, $levels, $commission)
    {
        // 生成新分润数组
        $new = array('default' => $commission);
        foreach ($levels as $level) {
            $new['level' . $level['id']] = $commission; // 填充每level的分润
        }

//        $cinfo['copartnerCommission1'] = $cinfo['copartnerCommission1'] ? iunserializer($cinfo['copartnerCommission1']): [];
//            $cinfo['copartnerCommission2'] = $cinfo['copartnerCommission2'] ? iunserializer($cinfo['copartnerCommission2']): [];
//            $cinfo['copartnerCommission3'] = $cinfo['copartnerCommission3'] ? iunserializer($cinfo['copartnerCommission3']): [];
//            $cinfo['copartnerCommission1'] = array_merge($cinfo['copartnerCommission1'], $Commission1);
//            $cinfo['copartnerCommission2'] = array_merge($cinfo['copartnerCommission2'], $Commission2);
//            $cinfo['copartnerCommission3'] = array_merge($cinfo['copartnerCommission3'], $Commission3);

        $commissionArr = $copartnerCommissionString ? iunserializer($copartnerCommissionString): [];
        $commissionArr = array_merge($commissionArr, $new);

        return $commissionArr;
    }
    /**
     * 980活动方案分润
     * @param $order
     * @param $commission int|string 分润金额
     * @return void
     */
    private function activity980PlanCommission($order, $commission)
    {
        $goods = pdo_fetchall(
            "select og.id,og.realprice,og.costprice,og.total,g.hasoption,g.medicineClassID,g.medicineAttributeID,og.goodsid,og.optionid,g.hascopartnerCommission,g.nocopartnerCommission, og.copartnerCommission1,og.copartnerCommission2,og.copartnerCommission3,og.copartnerCommissions from " . tablename('elapp_shop_order_goods') . '  og '
            . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id = og.goodsid'
            . ' where og.orderid=:orderid and og.uniacid=:uniacid and g.activity_id=:activity_id', array(':orderid' => $order['id'], ':uniacid' => $order['uniacid'], ':activity_id' => $this->activity_id));

        /** @var CopartnerModel $copartnerModel */
        $copartnerModel = p('copartner');
        $levels = $copartnerModel->getLevels();

        $mentorModel = new MentorModel();
        $set = $mentorModel->getSet();
        $goodRatio = $set['ratio'];

        foreach ($goods as $cinfo) {
            // 将新的分润覆盖到原来的分润中, 2,3级不分润
            $commission1 = $this->mergeCommission($cinfo['copartnerCommission1'], $levels, $commission);
            $commission2 = $this->mergeCommission($cinfo['copartnerCommission2'], $levels, 0);
            $commission3 = $this->mergeCommission($cinfo['copartnerCommission3'], $levels, 0);

            $cinfo['commissions'] = iunserializer($cinfo['copartnerCommissions']);

            if (empty($cinfo['commissions'])) {
                $commissions = array(
                    'level1' => $commission,
                    'level2' => 0,
                    'level3' => 0,
                    'level1_level_id' => $cinfo['commissions']['level1_level_id'] ?? 0,
                    'level2_level_id' => $cinfo['commissions']['level2_level_id'] ?? 0,
                    'level3_level_id' => $cinfo['commissions']['level3_level_id'] ?? 0,
                );
            } else {
                $commissions = $cinfo['commissions'];
                $commissions['level1'] = $commission;
                $commissions['level2'] = 0;
                $commissions['level3'] = 0;
            }

            pdo_update('elapp_shop_order_goods', array(
                'copartnerCommission1' => iserializer($commission1),
                'copartnerCommission2' => iserializer($commission2),
                'copartnerCommission3' => iserializer($commission3),
                'copartnerCommissions' => iserializer($commissions),
            ), array('id' => $cinfo['id']));

            // 将数据填充回订单商品记录，用于计算帮扶分润
            $cinfo['copartnerCommissions'] = iserializer($commissions);
            $cinfo['copartnerCommission1'] = iserializer($commission1);
            $cinfo['copartnerCommission2'] = iserializer($commission2);
            $cinfo['copartnerCommission3'] = iserializer($commission3);

            $deducts = [
                [
                    'copartnerCommission' => $commission,
                    'info'=>$cinfo,
                ]
            ];

            // 计算帮扶分润
            $copartnerMentorCommission = $mentorModel->calcuteCopartnerMentorDeduct('elapp_shop_order_goods', 0, $deducts, $goodRatio, $order['id'], $order['uniacid'], 0, 1);
        }

        // 更新order mentor
        ##订单帮扶分红计算
        $update = $mentorModel->getCheckConfirmUpdate(0, 0, 0, $order, false,
            0, 0, $commission,
            0, 1, 0, $copartnerMentorCommission, $goodRatio);
        pdo_update('elapp_shop_order', $update, array('id' => $order['id']));
    }


}