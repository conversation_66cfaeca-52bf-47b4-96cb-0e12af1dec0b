<?php
namespace app\controller\activity;

use app\plugin\activity\core\config\Editor;
use app\plugin\activity\core\config\Link;
use app\plugin\activity\core\config\OrderRelatedRoles;
use app\plugin\activity\core\config\Radio;
use app\plugin\activity\core\config\Select;
use app\plugin\activity\core\config\Tab;
use app\plugin\activity\core\config\Text;
use app\plugin\activity\core\config\Title;

class Activity980Clerk extends BaseActivity
{
    public $activity_id = 1;
    public function create(OrderCreateData $data)
    {
        // TODO: Implement create() method.
    }

    public function pay(OrderPayData $data)
    {
        // TODO: Implement pay() method.
    }

    public function refund(OrderRefundData $data)
    {
        // TODO: Implement refund() method.
    }

    public function singleRefund(OrderSingleRefundData $data)
    {
        // TODO: Implement singleRefund() method.
    }

    public function signOff(OrderSignOffData $data)
    {
        // TODO: Implement signOff() method.
    }

    public function configComponents()
    {
        $tabs = parent::configComponents();

        $mytabs = [
            new Tab('配置', 'config', false, [
                new OrderRelatedRoles('订单关联角色', 'order_related_roles', '', ''),
            ])
        ];

        return array_merge($tabs, $mytabs);
    }
}