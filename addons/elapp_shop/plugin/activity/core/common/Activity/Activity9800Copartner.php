<?php
namespace app\controller\activity;

use app\controller\settle\common\checker\condition\IsActivityOpenChecker;
use app\controller\settle\common\checker\condition\IsNOrderPayedChecker;
use app\controller\settle\common\checker\condition\IsSeckillOrderChecker;
use app\controller\settle\common\checker\param\OrderCheckParameter;
use app\model\ClerkModel;
use app\model\CopartnerModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;

class Activity9800Copartner extends BaseActivity
{
    public $activity_id = 2;

    public $name = '9800创业大礼包';

    public function create(OrderCreateData $data)
    {

    }

    /**
     * 第N单分润9800
     *
     * @param OrderPayData $data
     * @return mixed|void
     */
    public function pay(OrderPayData $data)
    {
        if ($data->getOrderType() != OrderPayData::ORDER_TYPE_GOODS) return;

        $config = $this->getConfig();
        if ($config['enabled'] != true) {
            return;
        }

        $order = pdo_get('elapp_shop_order', ['id' => $data->getOrderId()]);
        if (!$order) {
            return;
        }

        $return = ['redirect_url' => mobileUrl('copartner/reg')];

        // 9800,980 活动用户写入
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER, 1);
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE, 1);
        (new DiyattrsModel())->setValue(DiyattrsEnums::TYPE_USER, $order['member_id'], DiyattrsEnums::KEY_IS_ACTIVITY_9800_USER, 1);

        $param = new OrderCheckParameter($order);
        $isSeck = IsSeckillOrderChecker::newInstance()->check($param);
        // 如果是秒杀订单，需求变更分润300
        if ($isSeck->getResult()) {
            $this->activity980PlanBCommission($order, 300);
        } else {
//            $result = IsNOrderPayedChecker::newInstance($this->activity_id, $config['n_order_index'])->check($param);
//            // 独立分润获取
//            $commission = $config['n_order_commission'];
//            // todo 更新分润
//            $this->activity980PlanBCommission($order, $commission);
        }

        // todo 等待后期优化调整结构
        return $return;
    }

    public function refund(OrderRefundData $data)
    {
        // todo 读取这张单分了多少钱
        // todo 遍历所有分润，插入退款分润处理
    }

    public function singleRefund(OrderSingleRefundData $data)
    {
        // TODO: Implement singleRefund() method.
    }

    public function signOff(OrderSignOffData $data)
    {
        // TODO: Implement signOff() method.
    }

    public function getConfig()
    {
        $value = pdo_getcolumn('elapp_shop_activity', ['id' => $this->activity_id], 'config');
        if ($value) {
            $value = json_decode($value, true);
            return $value;
        } else {
            // N单分润，N单索引
            return ["n_order_commission"=>9800, "n_order_index"=>0, "enabled"=>true];
        }
    }

    private function mergeCommission($copartnerCommissionString, $levels, $commission)
    {
        // 生成新分润数组
        $new = array('default' => $commission);
        foreach ($levels as $level) {
            $new['level' . $level['id']] = $commission; // 填充每level的分润
        }

//        $cinfo['copartnerCommission1'] = $cinfo['copartnerCommission1'] ? iunserializer($cinfo['copartnerCommission1']): [];
//            $cinfo['copartnerCommission2'] = $cinfo['copartnerCommission2'] ? iunserializer($cinfo['copartnerCommission2']): [];
//            $cinfo['copartnerCommission3'] = $cinfo['copartnerCommission3'] ? iunserializer($cinfo['copartnerCommission3']): [];
//            $cinfo['copartnerCommission1'] = array_merge($cinfo['copartnerCommission1'], $Commission1);
//            $cinfo['copartnerCommission2'] = array_merge($cinfo['copartnerCommission2'], $Commission2);
//            $cinfo['copartnerCommission3'] = array_merge($cinfo['copartnerCommission3'], $Commission3);

        $commissionArr = $copartnerCommissionString ? iunserializer($copartnerCommissionString): [];
        $commissionArr = array_merge($commissionArr, $new);

        return $commissionArr;
    }
    /**
     * 980活动方案分润
     * @param $order
     * @param $commission int|string 分润金额
     * @return void
     */
    private function activity980PlanBCommission($order, $commission)
    {
        $goods = pdo_fetchall(
            "select og.id,og.realprice,og.costprice,og.total,g.hasoption,g.medicineClassID,g.medicineAttributeID,og.goodsid,og.optionid,g.hascopartnerCommission,g.nocopartnerCommission, og.copartnerCommission1,og.copartnerCommission2,og.copartnerCommission3,og.copartnerCommissions from " . tablename('elapp_shop_order_goods') . '  og '
            . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id = og.goodsid'
            . ' where og.orderid=:orderid and og.uniacid=:uniacid and g.activity_id=:activity_id', array(':orderid' => $order['id'], ':uniacid' => $order['uniacid'], ':activity_id' => $this->activity_id));

        /** @var CopartnerModel $copartnerModel */
        $copartnerModel = p('copartner');
        $levels = $copartnerModel->getLevels();

        foreach ($goods as $cinfo) {
            // 将新的分润覆盖到原来的分润中, 2,3级不分润
            $commission1= $this->mergeCommission($cinfo['copartnerCommission1'], $levels, $commission);
            $commission2 = $this->mergeCommission($cinfo['copartnerCommission2'], $levels, 0);
            $commission3 = $this->mergeCommission($cinfo['copartnerCommission3'], $levels, 0);

            $cinfo['commissions'] = iunserializer($cinfo['copartnerCommissions']);

            if (empty($cinfo['commissions'])) {
                $commissions = array(
                    'level1' => $commission,
                    'level2' => 0,
                    'level3' => 0,
                    'level1_level_id' => $cinfo['commissions']['level1_level_id'] ?? 0,
                    'level2_level_id' => $cinfo['commissions']['level2_level_id'] ?? 0,
                    'level3_level_id' => $cinfo['commissions']['level3_level_id'] ?? 0,
                );
            } else {
                $commissions = $cinfo['commissions'];
                $commissions['level1'] = $commission;
                $commissions['level2'] = 0;
                $commissions['level3'] = 0;
            }

            pdo_update('elapp_shop_order_goods', array(
                'copartnerCommission1' => iserializer($commission1),
                'copartnerCommission2' => iserializer($commission2),
                'copartnerCommission3' => iserializer($commission3),
                'copartnerCommissions' => iserializer($commissions),
            ), array('id' => $cinfo['id']));
        }
    }
}