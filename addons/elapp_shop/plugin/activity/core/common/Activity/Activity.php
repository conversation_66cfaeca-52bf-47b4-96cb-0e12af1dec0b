<?php
namespace app\controller\activity;

// todo yh 待修复问题： 代码中没有明确return的类型，甚至代码没有处理返回值，要必要考录是否需要返回值或添加返回值类型
interface Activity
{
    public function create(OrderCreateData $data);
    public function pay(OrderPayData $data);

    public function refund(OrderRefundData $data);

    public function singleRefund(OrderSingleRefundData $data);

    public function refundApply(OrderRefundApplyData $data);

    public function singleRefundApply(OrderSingleRefundApplyData $data);

    public function signOff(OrderSignOffData $data);
}