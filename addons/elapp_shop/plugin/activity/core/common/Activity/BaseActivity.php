<?php
namespace app\controller\activity;

use app\common\validate\BaseValidate;
use app\model\Activity2Model;
use app\plugin\activity\core\config\Link;
use app\plugin\activity\core\config\Radio;
use app\plugin\activity\core\config\Tab;
use app\plugin\activity\core\config\Text;
use app\plugin\activity\core\config\Title;
use think\Validate;

abstract class BaseActivity implements Activity
{
    protected $activity_id;
    protected $name;

    protected $configValidate;

    public function isEnabled()
    {
        $model = Activity2Model::find($this->activity_id);

        return $model && $model['status'] == 1;
    }

    /**
     * 获取活动配置config验证器
     * @return BaseValidate
     * @throws \Exception
     */
    public function getConfigValidate()
    {
        $configValidate = null;

        if ($this->configValidate && class_exists($this->configValidate)) {
            $configValidate = new $this->configValidate();
            if (!$configValidate instanceof BaseValidate) {
                throw new \Exception('configValidate 必须继承 BaseValidate');
            }
        }

        return $configValidate;
    }

    // 获取活动配置
    public function getConfig()
    {
        if (empty($this->activity_id)) {
            throw new \Exception('activity_id 未设置');
        }

        $value = pdo_getcolumn('elapp_shop_activity', ['id' => $this->activity_id], 'config');
        if ($value) {
            $value = json_decode($value, true);
            return $value;
        }
    }

    public function configComponents() {
        $model = Activity2Model::find($this->activity_id);
        if ($model && $model['config']) {
            $this->config = $model['config'];
        }

        return [
            new Tab('基本', 'basic', true, [
                // ----
                new Title('活动公共配置'),
                new Text('活动标题', 'title', $model['title'] ?? '', ''),
                new Radio('活动状态', 'status', $model['status'] ?? 0, '', [
                    ['label'=>'启用', 'value' => 1],
                    ['label'=>'禁用', 'value' => 0],
                ]),
            ])
        ];
    }

    // 订单创建
    public function create(OrderCreateData $data)
    {
    }

    // 订单支付
    public function pay(OrderPayData $data)
    {
    }

    // 订单退款
    public function refund(OrderRefundData $data)
    {
    }

    // 订单单品退款
    public function singleRefund(OrderSingleRefundData $data)
    {
    }

    // 订单退款申请
    public function refundApply(OrderRefundApplyData $data)
    {
    }

    // 订单单品退款申请
    public function singleRefundApply(OrderSingleRefundApplyData $data)
    {
    }

    // 订单签收
    public function signOff(OrderSignOffData $data)
    {
    }
}