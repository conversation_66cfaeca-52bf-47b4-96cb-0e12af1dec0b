<?php
namespace app\controller\activity;

class OrderRefundData
{
    const REFUND_AGREE = 1;
    const REFUND_REJECT = 2;
    const REFUND_CANCEL = 3;

    private $order_id;

    // 处理结果 1. 同意退款 2. 驳回申请|拒绝退款 3. 取消退款
    private $handleResult;

    public function __construct($order_id, $handleResult)
    {
        $this->order_id = $order_id;
        $this->handleResult = $handleResult;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * @param mixed $order_id
     */
    public function setOrderId($order_id): void
    {
        $this->order_id = $order_id;
    }

    /**
     * @return mixed
     */
    public function getHandleResult()
    {
        return $this->handleResult;
    }
}