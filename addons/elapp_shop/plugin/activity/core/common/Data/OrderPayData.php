<?php
namespace app\controller\activity;

class OrderPayData
{
    const ORDER_TYPE_GOODS = 'goods'; // 商品订单
    const ORDER_TYPE_MEMBER_CARD = 'member_card'; // 会员卡
    const ORDER_TYPE_SERVICE_FEE = 'service_fee'; // 服务费

    private $order_id;
    /**
     * @var string 订单类型 ORDER_TYPE_* 常量
     */
    private $order_type;
    /**
     * @var int 会员id
     */
    private $member_id;

    public function __construct($order_id, $order_type, $member_id)
    {
        $this->order_id = $order_id;
        $this->order_type = $order_type;
        $this->member_id = $member_id;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * @param mixed $order_id
     */
    public function setOrderId($order_id): void
    {
        $this->order_id = $order_id;
    }

    /**
     * @return mixed
     */
    public function getOrderType()
    {
        return $this->order_type;
    }

    /**
     * @param mixed $order_type
     */
    public function setOrderType($order_type): void
    {
        $this->order_type = $order_type;
    }

    public function getMemberId(): int
    {
        return $this->member_id;
    }

    public function setMemberId(int $member_id): void
    {
        $this->member_id = $member_id;
    }
}