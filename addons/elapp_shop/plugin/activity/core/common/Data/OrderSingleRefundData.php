<?php
namespace app\controller\activity;


class OrderSingleRefundData
{
    const REFUND_AGREE = 1;
    const REFUND_REJECT = 2;
    const REFUND_CANCEL = 3;

    private $order_goods_id;
    private $order_id;

    // 处理结果 1. 退款成功 2. 驳回申请 3. 取消退款 4. 拒绝退款
    private $handleResult;

    public function __construct($order_id, $order_goods_id, $handleResult)
    {
        $this->order_id = $order_id;
        $this->order_goods_id = $order_goods_id;
        $this->handleResult = $handleResult;
    }

    /**
     * @return mixed
     */
    public function getOrderGoodsId()
    {
        return $this->order_goods_id;
    }

    /**
     * @param mixed $order_goods_id
     */
    public function setOrderGoodsId($order_goods_id): void
    {
        $this->order_goods_id = $order_goods_id;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * @param mixed $order_id
     */
    public function setOrderId($order_id): void
    {
        $this->order_id = $order_id;
    }

    /**
     * @return mixed
     */
    public function getHandleResult()
    {
        return $this->handleResult;
    }

    /**
     * @param mixed $handleResult
     */
    public function setHandleResult($handleResult): void
    {
        $this->handleResult = $handleResult;
    }

}