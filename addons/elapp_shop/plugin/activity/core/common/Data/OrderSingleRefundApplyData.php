<?php
namespace app\controller\activity;


class OrderSingleRefundApplyData
{
    private $order_goods_id;
    private $order_id;

    public function __construct($order_id, $order_goods_id)
    {
        $this->order_id = $order_id;
        $this->order_goods_id = $order_goods_id;
    }

    /**
     * @return mixed
     */
    public function getOrderGoodsId()
    {
        return $this->order_goods_id;
    }

    /**
     * @param mixed $order_goods_id
     */
    public function setOrderGoodsId($order_goods_id): void
    {
        $this->order_goods_id = $order_goods_id;
    }

    /**
     * @return mixed
     */
    public function getOrderId()
    {
        return $this->order_id;
    }

    /**
     * @param mixed $order_id
     */
    public function setOrderId($order_id): void
    {
        $this->order_id = $order_id;
    }
}