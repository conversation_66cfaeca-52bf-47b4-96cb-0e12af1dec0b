<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\plugin\activity\core\validate;

use app\common\validate\BaseValidate;

/**
 * 活动校验器
 */
class ActivityValidate extends BaseValidate
{
    protected $rule = [
        'status' => ['require', 'in:0,1'],
        'title' => ['require', 'max:30'],
    ];

    protected $message = [
        'status.require' => 'status参数缺失',
        'status.in' => 'status参数错误',
        'title.require' => 'title参数缺失',
        'title.max' => 'title参数超出限制：30',
    ];
}