<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\plugin\activity\core\validate;

use app\common\validate\BaseValidate;

/**
 * @desc 活动3配置校验器
 * Class OrderValidate
 * @package app\com\validate
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/26 10:13
 */
class Activity3ConfigValidate extends BaseValidate
{
    protected $rule = [
        'coupon_id'    => ['require', 'number'],
        'target_count' => ['require', 'number'],
        'index_page_title' => ['require', 'max:100'],
        'index_page_desc' => ['require','max:100'],
        'index_page_empty_data_text' => ['require','max:100'],
        'index_page_rule_text' => ['require', 'max:10240'],
        'index_page_poster_id' => ['require', 'number'],
        'index_page_member_card_id' => ['require', 'number'],
        'index_page_goods' => ['array'],
        'index_page_goods_text' => ['max:1024'],
        'member_card_ids' => ['array'],
    ];

    protected $message = [
        'coupon_id'    => 'coupon_id参数错误',
        'target_count' => 'target_count参数错误',
    ];
}