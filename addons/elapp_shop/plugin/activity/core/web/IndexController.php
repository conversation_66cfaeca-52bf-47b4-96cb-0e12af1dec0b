<?php
namespace web\controller\activity;

use app\controller\activity\Activity9800ActivateSDM;
use app\controller\activity\Activity9800Copartner;
use app\controller\activity\Activity980Clerk;
use app\controller\activity\ActivityEggTuanGift;
use app\controller\activity\BaseActivity;
use app\core\model\sandimeng;
use app\model;
use app\model\Activity2Model;
use app\plugin\activity\core\validate\ActivityValidate;
use think\Request;
use web\controller\PluginWebPage;

class IndexController extends PluginWebPage
{
    public function main(Request $request)
    {
        $keyword = $request->param('keyword');
        $status = $request->param('status');

        $where =  [];

        if ($keyword) {
            $where[] = ['title' , 'like', '%' .$keyword . '%'];
        }
        if ($status != '') {
            $where[] = ['status' , '=', $status];
        }

        $list = Activity2Model::where($where)->select()->toArray();
        include $this->template('activity/index');
    }

    /**
     * @param $id
     * @return BaseActivity
     */
    private function getActivityObject($id)
    {
        switch ($id) {
            case 1:
                return new Activity980Clerk();
            case 2:
                return new Activity9800Copartner();
            case 3:
            case 5:
                return new ActivityEggTuanGift();
            case 4:
                return new Activity9800ActivateSDM();
        }
    }
    /**
     * 结算单位详情
     *
     */
    public function detail(Request $request)
    {
        global $_W, $_GPC;
        $id = $request->param('id');
        $data = pdo_get('elapp_shop_activity',['id'=>$id]);
        $activity = Activity2Model::find($id);
        $activityObject = $this->getActivityObject($id);
        $configComponents = $activityObject->configComponents();

        if (empty($activity)) {
            $this->message('抱歉，活动不存在!');
        }
        if (empty($activityObject)) {
            $this->message('抱歉，活动类未找到!');
        }

        $data = $activity['config'];
        if (is_array($data)) {
            $data['status'] = $activity->status;
            $data['title'] = $activity->title;
        }

        if ($request->isPost()) {
            $validate = new ActivityValidate();
            $config_validate = $activityObject->getConfigValidate();

            $all = $request->param();

            // 过滤规则之外的字段
            $params = $request->only(array_keys($validate->getRules()));
            $config_params = [];

            if (!$validate->check($params)) {
                show_json(0, $validate->getError());
            }

            if ($config_validate) {
                $config_params = $request->only(array_keys($config_validate->getRules()));
                if (!$config_validate->check($config_params)) {
                    show_json(0, $config_validate->getError());
                }
            }

            $activity->config = $config_params;

            $activity->save($params);
            show_json(1);
        }

        include $this->template('activity/detail');
    }

    //更新copartner 层级关系
    public function updateCopartnerRelation()
    {
        die('非法执行');
        set_time_limit(0);
        // show all error
        error_reporting(E_ALL);
        ini_set('display_errors', '1');

        global $_W, $_GPC;
        session_start();
        $id = $_GPC['id'] ?? ($_SESSION['update_CopartnerRelation_id'] ?? 0);

        $uniacid        = $_W['uniacid'];
        $SanDiMengModel = new \app\core\model\member\sandimeng\MemberModel();
        $where          = [];
        $where[]        = ['sm.member_id', '>', $id];
        //$where[]        = ['sm.id', '=', '5'];
        $SanDiMengList = $SanDiMengModel->all($where);
        $lastid = 0;
        if (empty($SanDiMengList)) {
            echo "已经执行完毕";
            die;
        }

        if ($SanDiMengList && count($SanDiMengList)) {
            foreach ($SanDiMengList as $value) {
                $lastid = $value['mid'];

                $parent_result = $SanDiMengModel->getParentsRelationByCardNo($value['card_no']); //查找父级
                if ($value['card_no'] == $parent_result['card_no']) {
                    //父级为自己; 找顶级member_id
                    if($value['card_no'] == '88888888'){
                        $data = [];
                        $data['member_id']  = $value['member_id'];
                        $data['onmid']      = 0;
                        $data['mentor_id']  = 0;
                        $data['openid']     = $value['openid'];
                        $data['is_org_sub'] = 1;
                        $SanDiMengModel->updateRelationData($data);
                    }
                } else {
                    $copartner_user = pdo_fetch("SELECT cu.id as copartner_id,m.openid,m.id as member_id FROM " . tablename("elapp_shop_copartner_user") . " as cu left join ims_elapp_shop_member as m on m.openid = cu.openid WHERE m.`uniacid`=:uniacid and m.`id` = :mid  LIMIT 1", [":uniacid" => $uniacid, ":mid" => $parent_result['member_id']]);
                    if ($copartner_user) {
                        $data = [];
                        $data['member_id']  = $value['member_id'];
                        $data['onmid']      = $copartner_user['member_id'];
                        $data['mentor_id']  = $copartner_user['copartner_id'];
                        $data['openid']     = $value['openid'];
                        $data['is_org_sub'] = $value['level'] <= 1? 1:0;
                        $SanDiMengModel->updateRelationData($data);
                    }
                }
            }
        }
        $_SESSION['update_CopartnerRelation_id'] = intval($lastid)+1;
        dump($_SESSION['update_CopartnerRelation_id']);
    }

    public function getReferRelationByCardNo(){
        $SanDiMengModel = new \app\core\model\member\sandimeng\MemberModel();
        $data = $SanDiMengModel->getReferRelationByCardNo('07203130');
        print_r($data);die;
    }

    //更新层级关系
    public function updateUserPath()
    {
        die('非法执行');
        // show all error
        error_reporting(E_ALL);
        ini_set('display_errors', '1');
        global $_W, $_GPC;
        session_start();
        $id = $_GPC['id'] ?? ($_SESSION['update_user_path_id'] ?? 0);

        pdo_begin();
        $uniacid        = $_W['uniacid'];
        $SanDiMengModel = new \app\core\model\member\sandimeng\MemberModel();
        $where          = [];
        $where[]        = ['sm.member_id', '>', $id];

        //$where[]        = ['sm.id', '=', 1]; //测试的用户id，可删除
        $SanDiMengList = $SanDiMengModel->all($where);
        $lastid = 0;
        if (empty($SanDiMengList)) {
            echo "已经执行完毕";
            exit();
        }
        foreach ($SanDiMengList as $value) {
            $lastid = $value['mid'];
            $relationship = $SanDiMengModel->getReferRelationByCardNo($value['card_no']);
            if ($relationship) {                
                $data = $SanDiMengModel->recursiveGetIds($relationship);
                $self_path = implode('/', array_reverse($data));
                // 删除最后一位元素
                array_shift($data);
                $parent_path = implode('/', array_reverse($data));
                $SanDiMengModel->updatePath($value['openid'], $self_path, $parent_path);
            }
        }
        $_SESSION['update_user_path_id'] = intval($lastid)+1;
        dump($_SESSION['update_user_path_id']);

//        $where[] = ['sm.card_no','=','88888888'];
//        $SanDiMengList = $SanDiMengModel->all($where);
//        $data = $SanDiMengModel->recursiveGetIds($SanDiMengList[0]);
//        $SanDiMengModel->updatePath($SanDiMengList[0]['openid'], $data[0], '');
        pdo_commit();
    }

    public function updateOrders()
    {
        die();
        $orders = pdo_getall('elapp_shop_order', ['activity_id'=>4,'status >='=>0], ['id','member_id']);
        $mems = array_column($orders, 'member_id');
        pdo_begin();
        foreach ($mems as $mid) {
            $m = pdo_get('elapp_shop_member', ['id'=>$mid], ['id','clerk_id','mentor_id']);
            $p = pdo_get('elapp_shop_member', ['id'=>$m['mentor_id']], ['id','clerk_id','mentor_id']);
            pdo_update('elapp_shop_order', ['clerk_id'=>$p['id'], 'mentor_id'=>$p['mentor_id']], ['org_id'=>1, 'member_id'=>$mid,'activity_id'=>4]);
        }
        pdo_commit();
    }

    //导入三迪梦数据
    public function daoru(){
        $SanDiMengModel = new \app\core\model\member\sandimeng\MemberModel();
        $SanDiMengModel->daoru();
    }

    //清洗数据
    public function cleanData(){
        $SanDiMengModel = new \app\core\model\member\sandimeng\MemberModel();
        $data = $SanDiMengModel->cleanData();
        echo '<pre>';
        foreach($data as $value){
            echo '无上级：&nbsp;&nbsp;<font color="blue">:' . $value['name'] . '</fornt>&nbsp;&nbsp;<font color="red">' . $value['card_no'] . '</font><br/>';
        }
    }

    public function set()
    {
        global $_W, $_GPC;
        $activityModel = new model\ActivityModel();
        $activity_data = $activityModel->getActivityConfig(1);
        $params        = ((is_array($_GPC['data']) ? $_GPC['data'] : []));

        if ($_W['ispost']) {
            $params = ((is_array($_GPC['data']) ? $_GPC['data'] : []));
            $plans  = [];
            for ($i = 0; $i <= 3; $i++) {
                $plan                        = $this->get_plan($params, $i);
                $plans[$plan['goods']['id']] = $plan;
            }
            if ($activity_data) {
                $activity_data['config']['plans'] = $plans;
            } else {
                $activity_data['id']      = 1;
                $activity_data['uniacid'] = $_W['uniacid'];
                $activity_data['title']   = '980元一部手机开药房';
                $activity_data['key']     = 'activity_980';
                $activity_data['status']  = 1;
                $config['content']        = "";
                $config['plans']          = $plans;
                $activity_data['config']  = $config;
            }
            $this->save_activity_data_new($activity_data);

            show_json(1, ['url' => webUrl('activity/index/set', ['tab' => str_replace('#tab_', '', $_GPC['tab'])])]);
        }
        $styles    = [];
        $plans     = [];
        $goods_ids = [];
        if ($activity_data) {
            if (isset($activity_data['config']['plans']) && is_array($activity_data['config']['plans'])) {
                foreach ($activity_data['config']['plans'] as $value) {
                    $plans[] = $value;
                }
            }
        }
        $data = m('common')->getPluginset('activity');
        include $this->template();
    }

    private function get_plan($params, $index)
    {
        $plan = [
            'key'                  => $params['key_' . $index] ?? '',
            'name'                 => $params['name_' . $index] ?? '',
            'title'                => $params['title_' . $index] ?? '',
            'goods'                => ['id' => $params['goods_id_' . $index] ?? 0],
            '980_protocol_title'   => $params['980_protocol_title_' . $index] ?? '',
            '980_protocol_content' => $params['980_protocol_content_' . $index] ?? '',
            'must_check'           => $params['must_check_' . $index] ? true : false,
            'show'                 => $params['must_check_' . $index] ? true : false,
        ];

        return $plan;
    }

    private function save_activity_data_new($activity_data = [])
    {
        $w = [
            'id' => $activity_data['id'],
        ];
        $data                    = pdo_get('elapp_shop_activity', $w);
        $activity_data['config'] = json_encode($activity_data['config']);
        if ($data) {
            pdo_update('elapp_shop_activity', $activity_data, $w);
        } else {
            pdo_insert('elapp_shop_activity', $activity_data);
        }
    }

    private function save_activity_data($uniacid, $title, $key, $value)
    {
        $w = [
            'activity_id' => 1,
            'uniacid'     => $uniacid,
            'key'         => $key,
        ];
        $data = pdo_get('elapp_shop_activity_data', $w);
        if ($data) {
            $data['value']       = $value;
            $data['update_time'] = time();
            pdo_update('elapp_shop_activity_data', $data, $w);
        } else {
            $data = [
                'activity_id' => 1,
                'uniacid'     => $uniacid,
                'title'       => $title,
                'key'         => $key,
                'value'       => $value,
                'create_time' => time(),
                'update_time' => time(),
            ];
            pdo_insert('elapp_shop_activity_data', $data);
        }
    }
}
