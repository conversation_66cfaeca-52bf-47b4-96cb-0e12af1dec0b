<?php

namespace web\controller\activity;

use app\core\model\sandimeng;
use app\model;
use app\model\SettleModel;
use web\controller\PluginWebPage;
use app\controller\activity;

class TempController extends PluginWebPage
{
    public function main()
    {
        global $_W, $_GPC;
        $action = $_GPC['action'] ?? null;
        // 如果以action的方法存在，则调用
        if (method_exists($this, $action)) {
            $this->$action($_GPC);
        }

        $params = $_GPC;
        include $this->template('activity/temp/main');
    }

    public function checkOrderConfirm()
    {
        global $_GPC;
        $ids = $_GPC['ids'];
        $ids = explode(',', $ids);
        dump($ids);
        $_SESSION['abcefg'] = 1;

        if (!empty($ids)) {
            $c = p('clerk');
            $cop = p('copartner');

            foreach ($ids as $id) {
                $c->checkOrderConfirm($id);
                $cop->checkOrderConfirm($id);
            }
        }
    }

    public function a()
    {
        // 读取部分980订单无合伙人收益的，创建100合伙人收益记录
        die;
        $sql = "select * from ims_elapp_shop_settle_order_handle_record where orderid in (
select id from ims_elapp_shop_order where activity_id=1 and org_id=2 and status>0
)";
        $all =  pdo_fetchall($sql);
        $a100 = [];
        foreach ($all as $item) {
            $data = json_decode($item['data'],1);
            $item ['data'] = $data;
            if ($data['copartner']['commission'] == '0') {
                $a100[] = $item;
            }
        }

        foreach ($a100 as $a) {
            // 获取合伙人
            $copid = $a['data']['copartner']['belong_to'];
            $cop = pdo_get('elapp_shop_copartner_user', ['id'=>$copid]);

            if ($a['orderid'] == 12699) {
//                pdo_query("SET GLOBAL GENERAL_LOG = 'ON'");

                pdo_begin();
                if ($a['data']['copartner']['commission'] == 0) {
                    // 修改分润
                    $money = $cop['levelid'] == 1 ? '150.00':'100.00';

                    $a['data']['copartner']['commission'] = $money;
                    $a['data']['copartner_team']['commission'] = $money;
                    $a['data']['team']['commission'] = bcadd($a['data']['team']['commission'], $money, 2);
                    dump($a);
                    // todo save to db ims_elapp_shop_settle_order_handle_record
                    pdo_update('elapp_shop_settle_order_handle_record', ['data'=>json_encode($a['data'])], ['id'=>$a['id']]);

                    // 从这里找到结算单id ims_elapp_shop_settle_order_oids
                    $oids = pdo_get('elapp_shop_settle_order_oids', ['order_id'=>$a['orderid']]);
                    if ($oids['ids']) {
                        $ids = explode(',', $oids['ids']);
                        $sos =  pdo_getall('elapp_shop_settle_order', ['id'=>$ids]);
                        $openid = ($sos[0]['openid']);
                        $date = ($sos[0]['settle_date']);
                        dump($openid);
                        $sos =  pdo_getall('elapp_shop_settle_order', ['settle_date'=>$date, 'openid'=>$openid]);

                        $soss = array_column($sos, 'role_id');
                        if (!in_array('copartner', $soss)) {
                            $data = [
                                'uniacid' => 1,
                                'order_sn' => 'SE'.str_replace('-','',$date). date('His'). random(8),
                                'openid' => $openid,
                                'no_settle_user' => 0,
                                'role_type' => 1,
                                'role_id' => 'copartner',
                                'belong_to' => $copid,
                                'settle_date' => $date,
                                'commission' => 0,
                                'commission_wait' => 0.00,
                                'commissions' => '',
                                'orders' => '',
                                'order_commissions' => '',
                                'extra' => '',
                                'status' => 0,
                                'hide' => 0,
                                'org_id' => 2,
                                'create_time' => time(),
                            ];
                            pdo_insert('elapp_shop_settle_order', $data);
                        }
                        if (!in_array('copartner_team', $soss)) {
                            $data = [
                                'uniacid' => 1,
                                'order_sn' => 'SE'.str_replace('-','',$date). date('His'). random(8),
                                'openid' => $openid,
                                'no_settle_user' => 0,
                                'role_type' => 15,
                                'role_id' => 'copartner_team',
                                'belong_to' => $copid,
                                'settle_date' => $date,
                                'commission' => 0,
                                'commission_wait' => 0.00,
                                'commissions' => '',
                                'orders' => '',
                                'order_commissions' => '',
                                'extra' => '',
                                'status' => 1,
                                'hide' => 1,
                                'org_id' => 2,
                                'create_time' => time(),
                            ];
                            pdo_insert('elapp_shop_settle_order', $data);
                        }

                        $sos =  pdo_getall('elapp_shop_settle_order', ['settle_date'=>$date, 'openid'=>$openid]);
                        // 找到copartner,copartner_team, team，将他们的未结算金额加进去
                        foreach ($sos as $so) {
                            // 如果是clerk,跳过
                            if ($so['role_id'] == 'clerk'|| $so['role_id'] == 'clerk_team') {
                                continue;
                            }

                            $commissions = json_decode($so['commissions'], true);
                            $so['commission'] = bcadd($money, $so['commission'], 2);
                            $so['commission_wait'] = $so['hide'] == 0 ? $money : 0;
                            $so['status'] = $so['hide'] == 1 ? 1 : 0;

                            $so['commissions'] = json_decode($so['commissions'], true);
                            $so['commissions']['goods'] = bcadd($commissions['goods'], $money, 4);
                            $so['commissions'] = json_encode($so['commissions']);

                            $so['orders'] = json_decode($so['orders'], true);
                            if (empty($so['orders']['goods'])) {
                                $so['orders']['goods'] = [$a['orderid']];
                            } else {
                                $so['orders']['goods'][] = $a['orderid'];
                                $so['orders']['goods'] = array_unique($so['orders']['goods']);
                            }
                            $so['orders'] = json_encode($so['orders']);

                            $so['order_commissions'] = json_decode($so['order_commissions'], true);
                            if (empty($so['order_commissions']['goods'])) {
                                $so['order_commissions']['goods'] = [$a['orderid']=>$money];
                            } else {
                                $so['order_commissions']['goods'][$a['orderid']] = $money;
                            }
                            $so['order_commissions'] = json_encode($so['order_commissions']);

                            // todo save to db ims_elapp_shop_settle_order
                            pdo_update('elapp_shop_settle_order', $so, ['id'=>$so['id']]);

                            $oids = pdo_get('elapp_shop_settle_order_oids', ['order_id'=>$a['orderid']]);
                            $oids['ids'] = explode(',', $oids['ids']);
                            $oids['ids'] = array_unique(array_merge($oids['ids'], [$so['id']]));
                            $oids['ids'] = implode(',', $oids['ids']);
                            // todo save to ims_elapp_shop_settle_order_oids
                            pdo_update('elapp_shop_settle_order_oids', ['ids'=>$oids['ids']], ['order_id'=>$a['orderid']]);
                        }
                    }
                }

                pdo_commit();

//                pdo_query("SET GLOBAL GENERAL_LOG = 'OFF'");

            }
        }


        // INSERT INTO `ims_elapp_shop_settle_order`  VALUES
        //(null, 1, 'SE2024042300065480628742', 'oyDB75-NWiYU-gg-fwVGqurexAVU', 0, 15, 'copartner_team', 759, '2024-04-22', 100.00, 0.00, '{\"goods\":100}', '{\"goods\":[\"14262\"]}', '{\"goods\":{\"14262\":100}}', '', 1, 1, 2, 1713802014),
        //(null, 1, 'SE2024042300065449844342', 'oyDB75-NWiYU-gg-fwVGqurexAVU', 0, 1, 'copartner', 759, '2024-04-22', 100.00, 0.00, '{\"goods\":100}', '{\"goods\":[\"14262\"]}', '{\"goods\":{\"14262\":100}}', '', 1, 0, 2, 1713802014);
    }

    private function credit3($params)
    {
        $this->checkPwd($params);
        $m = m('member')->getMember($params['member_id']);
        if ($m) {
            m('member')->setCredit($m['openid'], 'credit3', $params['money'], $params['reason']??'后台手动添加收益');
            dump('更新成功');
        } else {
            dump('用户不存在');
        }
    }

    private function order_commissions($params) {
        if ($params['order_sn']) {
            $order = pdo_get('elapp_shop_order', ['ordersn' => $params['order_sn']]);

            if ($order) {
                $commissions = (new SettleModel())->getOrderSettleCommissions($order['id']);
                $otype = (new SettleModel())->getOrderGoodsTypeString($order);
                $commissions_data = (new SettleModel())->getOrderCommissionByOrderType($commissions, $otype);
                $commissions_data['id'] = $order['id'];
                $commissions_data['ordersn'] = $order['ordersn'];
                dump('已结算分润');
                $this->map_dump($this->map(), $commissions_data);
            }
        }
    }

    /**
     * 删除订单分润
     * @param $params
     * @return void
     */
    private function delete_order_settle($params)
    {
        $this->checkPwd($params);

        $order = pdo_get('elapp_shop_order', ['id' => $params['orderid']]);
        $oids = pdo_get('elapp_shop_settle_order_oids', ['order_id'=>$params['orderid'], 'order_type'=>1]);
        if ($oids) {
            $oids = explode(',', $oids['ids']);
            // 获取结算单
            $orders =  pdo_getall('elapp_shop_settle_order', ['id'=> $oids]);

            // 获取合伙人
            $copartner = null;
            $copartner_origin = 0;
            $copartner_new = 0;
            // 获取店长
            $clerk = null;
            $clerk_origin = 0;
            $clerk_new = 0;

            dump("结算单记录");
            pdo_begin();
            foreach ($orders as $item) {
                $goods_order_ids = json_decode($item['orders'],true)['goods'];
                // 从中移除 订单id 12345
                $goods_order_ids = array_diff($goods_order_ids, [$params['orderid']]);
                $item['new'] = implode(',', $goods_order_ids);

                $records = pdo_getall('elapp_shop_settle_order_handle_record', ['orderid'=>$params['orderid'], 'order_type'=>'goods']);
                $money = 0;

                if ($records) {
                    foreach ($records as $record) {
                        $record['data'] = json_decode($record['data'], true);
                        $money = bcadd($money, $record['data'][$item['role_id']]['commission'], 2) ?? 0;
                    }
                }
                $item['new_money'] = $money;

                $this->map_dump(['id'=>'id','role_id'=>'role_id','orders' => '订单ids', 'new'=>'新订单ids','commission'=>'结算金额','new_money'=>'新结算金额'], $item);

                $commissions = json_decode($item['commissions'], true);
                $commissions['goods'] = bcsub($commissions['goods'], $money, 2);
                $commissions = json_encode($commissions);


                $orders_arr = json_decode($item['orders'],true);
                $orders_arr['goods'] = array_values($goods_order_ids);
                // $params['orderid'] = $item['id'];
                $orders_json = json_encode($orders_arr);
                $new_money = bcsub($item['commission'], $money, 2);

                // 1. 更新结算单订单ids
                pdo_update('elapp_shop_settle_order', ['commissions' => $commissions, 'orders' => $orders_json, 'commission'=>$new_money], ['id'=>$item['id']]);
                dump('更新结算单');
                dump(['commissions' => $commissions, 'orders' => $orders_json, 'commission'=>$new_money]);

                if ($item['role_id'] == 'copartner') {
                    $copartner = pdo_get('elapp_shop_copartner_user', ['id' => $item['belong_to']]);
                    $copartner_origin = $copartner['settle_money'];

                    if ($money>0) {
                        pdo_run('update ' . tablename('elapp_shop_copartner_user') . ' set settle_money = settle_money - ' . $money . ' where id = ' . $item['belong_to']);
                        dump('更新合伙人余额');
                        dump($money);
                    }
                    $copartner_new = pdo_get('elapp_shop_copartner_user', ['id' => $item['belong_to']])['settle_money'] ?? 0;
                }
                if ($item['role_id'] == 'clerk') {
                    $clerk = m('member')->getMember($item['belong_to']);
                    $clerk_origin = m('member')->getCredit($clerk['openid'], 'credit3');

                    if ($money>0) {
                        m('member')->setCredit($item['openid'], 'credit3', -$money, "订单结算 回滚 {$item['role_id']} {$item['settle_date']}");
                        dump('更新店长余额');
                        dump($money);
                    }

                    $clerk_new = m('member')->getCredit($clerk['openid'], 'credit3');
                }
            }

//            // 2. 删除结算单处理记录
            pdo_delete('elapp_shop_settle_order_handle_record', ['orderid'=> $params['orderid'], 'order_type'=>'goods']);
            dump('删除结算单处理记录');
            dump( ['orderid'=> $params['orderid'], 'order_type'=>'goods']);

//            // 删除订单关联结算单ids记录
            pdo_delete('elapp_shop_settle_order_oids', ['order_id'=> $params['orderid'], 'order_type'=>1]);
            dump('删除订单关联结算单ids记录');
            dump( ['order_id'=> $params['orderid'], 'order_type'=>1]);

//            $orders =  pdo_getall('elapp_shop_settle_order', ['id'=> $oids]);

            pdo_commit();

            dump('cop old:' . $copartner_origin);
            dump('cop new:' . $copartner_new);
            dump('clerk old:' . $clerk_origin);
            dump('clerk new:' . $clerk_new);

        } else {
            echo "no oids";die;
        }
    }

    private function getMember($uid)
    {
        if (strlen($uid)>5) {
            $sdm = pdo_get('elapp_shop_sandimeng_member', ['card_no' => $uid]);
            if ($sdm && $sdm['member_id']) {
                $member = m('member')->getMember($sdm['member_id']);
                if (!$member) {
                    echo "uid: {$uid} SDM 关联的用户不存在";die;
                }
            } else {
                echo "uid: {$uid} SDM关系记录不存在";die;
            }
        } else {
            $member = m('member')->getMember($uid);
            if (!$member) {
                echo "uid: {$uid} 关联的用户不存在";die;
            }
        }

        return $member;
    }

    private function change_user_data_2($params)
    {
        $uid = $params['uid'];
        $pid = $params['pid'];
        $is_org_sub = intval($params['is_org_sub']);

        $um = $this->getMember($uid);
        $pm = $this->getMember($pid);
        $ucop = pdo_get('elapp_shop_copartner_user', ['id' => $um['copartner_id']]);
        //$pcop = pdo_get('elapp_shop_copartner_user', ['id'=>$pm['copartner_id']]);

        $map = ['id'=>'id','nickname'=>'nickname','clerk_id'=>'店长id','mentor_id'=>'帮扶id','copartner_id'=>'copid'];
        $this->map_dump($map, $um);
        $this->map_dump($map, $pm);

        pdo_begin();
        $update = [];
        $change = [];
        $batch = uniqid();
        // member.clerk = mentor
        if ($um['clerk_id'] != $pm['id'] || $um['mentor_id'] != $pm['id']) {
            $update['clerk_id'] = $pm['id'];
            $change['clerk_id'] = ['old'=>$um['clerk_id'], 'new'=>$pm['id']];
            $update['mentor_id'] = $pm['id'];
            $change['mentor_id'] = ['old'=>$um['mentor_id'], 'new'=>$pm['id']];
            $update['onmid'] = $pm['id'];
            $change['onmid'] = ['old'=>$um['onmid'], 'new'=>$pm['id']];

            $orders = pdo_getall('elapp_shop_order', ['clerk_id'=>$um['id'],'status >'=>0]);
            foreach ($orders as $o) {
                // 修改用户下的订单的帮扶id
                // update order set mentor_id = clerk.id where clerk_id = member.id
                if ($o['mentor_id'] != $pm['id']) {
                    $o['mentor_id'] = $pm['id'];
                    pdo_update('elapp_shop_order', ['mentor_id'=>$pm['id']], ['id'=>$o['id']]);

                    $log = ['batch_no'=>$batch, 'type'=>2, 'member_id'=>$o['member_id'], 'message'=>'修改订单'.$o['id'].' [ '.$o['ordersn'].' ] 数据: ','created_at'=>time()];
                    $log['message'] .= " mentor_id: {$o['mentor_id']} => {$pm['id']}";
                    pdo_insert('elapp_shop_member_logs', $log);
                }
            }

            // 修改980订单
            // order.copartner_id = clerk.copartner
            // order.clerk_id = clerk.id
            // order.mentor_id = clerk.mentor_id
            $orders = pdo_getall('elapp_shop_order', ['activity_id'=>4, 'member_id'=>$um['id']]);
            foreach ($orders as $o) {
                if ($o['copartner_id'] != $pm['copartner_id']) {
                    pdo_update('elapp_shop_order', ['copartner_id' => $pm['copartner_id'], 'clerk_id'=>$pm['id'],'mentor_id'=>$pm['mentor_id']], ['id'=>$o['id']]);

                    $log = ['batch_no'=>$batch, 'type'=>2, 'member_id'=> $o['member_id'], 'message'=>'修改SDM激活订单 '.$o['id'].' [ '.$o['ordersn'].' ] 数据: ','created_at'=>time()];
                    $log['message'] .= " copartner_id: {$o['copartner_id']} => {$pm['copartner_id']}, ";
                    $log['message'] .= " clerk_id: {$o['clerk_id']} => {$pm['id']}, ";
                    $log['message'] .= " mentor_id: {$o['mentor_id']} => {$pm['mentor_id']}";
                    pdo_insert('elapp_shop_member_logs', $log);
                }
            }
        }

        if ($ucop['mentor_id'] != $pm['copartner_id']) {
            pdo_update('elapp_shop_copartner_user', ['mentor_id'=>$pm['copartner_id']], ['id'=>$ucop['id']]);

            $log = ['batch_no'=>$batch, 'type'=>3, 'member_id'=> $ucop['mentor_id'], 'message'=>'修改合伙人信息: ', 'created_at'=>time()];
            $log['message'] .= " mentor_id: {$ucop['mentor_id']} => {$pm['copartner_id']}";
            pdo_insert('elapp_shop_member_logs', $log);
        }

        if ($ucop['is_org_sub'] != $is_org_sub) {
            pdo_update('elapp_shop_copartner_user', ['is_org_sub'=>$is_org_sub], ['id'=>$ucop['id']]);

            $log = ['batch_no'=>$batch, 'type'=>3, 'member_id'=> $ucop['mid'], 'message'=>'修改合伙人信息: ', 'created_at'=>time()];
            $log['message'] .= " is_org_sub: {$ucop['is_org_sub']} => {$is_org_sub}";
            pdo_insert('elapp_shop_member_logs', $log);
        }

        if (!empty($update)) {
            pdo_update('elapp_shop_member', $update, ['id'=>$um['id']]);

            $log = ['batch_no'=>$batch, 'type'=>1, 'member_id'=>$um['id'], 'message'=>'修改用户数据: ','created_at'=>time()];
            foreach ($change as $k=>$v) {
                $log['message'] .= " {$k}: {$v['old']} => {$v['new']}, ";
            }
            $log['message'] = substr($log['message'], 0, -2);
            pdo_insert('elapp_shop_member_logs', $log);
        }

        pdo_commit();
    }

    private function change_user_data($params)
    {
        $id = $params['id'];
        $data = [];
        $keys = [
            'clerk_id', 'mentor_id', 'copartner_id', 'onmid', 'copartner_user_path',
        ];
        foreach ($keys as $key) {
            if (!empty($params[$key])) {
                $data[$key] = $params[$key];
            }
        }

        if (!empty($data)) {
            $member = m('member')->getMember($id);
            if ($member) {
                $change = [];
                foreach ($data as $k =>$v) {
                    $change[$k] = ['old' => $member[$k], 'new' => $v];
                }

                $log = ['type'=>1,'batch_no'=>uniqid(), 'member_id'=>$id, 'message'=>'修改用户数据: ','created_at'=>time()];
                foreach ($change as $k=>$v) {
                    $log['message'] .= "{$k}: {$v['old']} => {$v['new']}, ";
                }
                // 删除最后的 ", "
                $log['message'] = substr($log['message'], 0, -2);
                pdo_insert('elapp_shop_member_logs', $log);

                pdo_update('elapp_shop_member', $data, ['id' => $id]);

                // 找到该用户的980订单，将订单修改为上级的订单
                // clerk_id, mentor_id, copartner_id
                dump('更新成功');
            }
        }
    }

    private function change_relation($params)
    {
        $u1 = m('member')->getMember($params['u1']);
        $u2 = m('member')->getMember($params['u2']);

        if (empty($u1)) {
            dump("用户1不存在");
            return;
        }
        if (empty($u2)) {
            dump("用户2不存在");
            return;
        }

        $u1cop = pdo_get('elapp_shop_copartner_user', ['openid'=>$u1['openid']]);
        $u2cop = pdo_get('elapp_shop_copartner_user', ['openid'=>$u2['openid']]);
        if (empty($u1cop)) {
            dump("用户1合伙人不存在");
            return;
        }
        if (empty($u2cop)) {
            dump("用户1合伙人不存在");
            return;
        }

        $update = [
            'clerk_id'     => $u2['id'],
            'mentor_id'    => $u2['id'],
            'onmid'        => $u2['id'],
            'copartner_id' => $u1cop['id'],
            'copartner_user_path' => $u2cop['copartner_user_path'] . '/' . $u1cop['id'],
        ];
        $update_cop = [
            'mentor_id'=>$u2cop['id'],
            'copartner_user_path'=> $u2cop['copartner_user_path'] . '/' . $u1cop['id'],
        ];

        // 记录修改的日志
        $change = [];
        $log = ['batch_no'=>uniqid(), 'type'=>1, 'member_id'=>$u1['id'], 'message'=> '修改用户关系: ','created_at'=>time()];

        foreach ($update as $k =>$v) {
//            $change[$k] = ['old' => $member[$k], 'new' => $v];
            if ($u1[$k] != $v) {
                $change[$k] = ['old' => $u1[$k], 'new' => $v];
            }
        }
        foreach ($change as $k=>$v) {
            $log['message'] .= "{$k}: {$v['old']} => {$v['new']}, ";
        }
        // 删除最后的 ", "
        $log['message'] = substr($log['message'], 0, -2);
        pdo_insert('elapp_shop_member_logs', $log);

        pdo_update('elapp_shop_member', $update , ['id'=>$params['u1']]);
        pdo_update('elapp_shop_copartner_user', $update_cop , ['id'=>$u1cop['id']]);

        dump('修改成功');
    }

    private function map_dump($map, $data) {
        $string=[];
        foreach ($map as $k=>$v) {
            $string[$v] = $data[$k];
        }
        dump($string);
    }

    private function map_dump_arr($map, $arr) {
        $string=[];
        foreach ($arr as $data) {
            foreach ($map as $k=>$v) {
                $string[$v] = $data[$k];
            }
            dump($string);
        }
    }

    private function order($params)
    {
        $order = pdo_get('elapp_shop_order', ['id'=>$params['id']]);
        if (empty($order)) {
            $order = pdo_get('elapp_shop_order', ['ordersn'=>$params['id']]);
            if (empty($order)) {
                dump('订单不存在');
                return;
            }
        }

        $map = [
            'id'=>'id',
            'ordersn'=>'sn',
            'member_id'=> '用户id',
            'openid'=>'openid',
            'onmid' => '推荐会员',
            'clerk_id' => '店长id',
            'mentor_id' => '帮扶店长id',
            'copartner_id' => '合伙人id',
            'copartner_account_id' => '招商经理id',
            'copartner' => '机构',
            'org_id' => 'org',
            'status' => 'status',
            'activity_id' => '活动id',
            'member' => '用户信息',
        ];

        if ($order) {
            $member = pdo_get('elapp_shop_member', ['id' => $order['member_id']]);
            if ($member) {
                $keysToKeep = ['id', 'clerk_id','mentor_id','copartner_id','onmid','is_clerk']; // 想要保留的键
//                // 使用 array_map 遍历数组并应用 array_intersect_key
//                $result = array_map(function ($item) use ($keysToKeep) {
//                    // 将想要保留的键转换为数组的键
//                    $filteredKeys = array_flip($keysToKeep);
//                    // 只保留 $item 中存在于 $filteredKeys 中的键值对
//                    return array_intersect_key($item, $filteredKeys);
//                }, [$member]);

                // 将想要保留的键转换为数组的键
                $filteredKeys = array_flip($keysToKeep);
                // 只保留 $item 中存在于 $filteredKeys 中的键值对

                $order['member'] =  array_intersect_key($member, $filteredKeys);;
            }
            $copartner = pdo_get('elapp_shop_copartner_user', ['id' => $order['copartner_id']]);
            $order['copartner'] = $copartner['mcnname'] ?? 'NULL';
        }

//        if ($order['mentor_id']) {
//            $mentor = pdo_get('elapp_shop_member', ['id' => $order['mentor_id']]);
//            $order['mentor_copartner_id'] = $mentor['copartner_id'];
//        }
//        if ($order['onmid']) {
//            $onuser = pdo_get('elapp_shop_member', ['id' => $order['onmid']]);
//            $order['onmid_copartner_id'] = $onuser['copartner_id'];
//        }

        $this->map_dump($map, $order);
    }
    private function keys_to_map($keys)
    {
        $map = [];
        foreach ($keys as $k) {
            $map[$k] = $k;
        }
        return $map;
    }

    private function copartner($params) {
        $copartner_id = $params['copartner_id'];
        $cop = pdo_get('elapp_shop_copartner_user', ['id' => $copartner_id]);
        $keys = ['id','mcnname','mentor_id','settle_money','status','del_at','levelid'];
        $this->map_dump($this->keys_to_map($keys), $cop);
        dump([$cop]);
    }

    private function member($params) {
        if ($params['id']) {
            $mem = $this->getMember($params['id']);
            //$mem = m('member')->getMember($params['id']);
            if ($mem) {
                dump('用户信息');
                $map = [
                    'id'=>'id',
                    'nickname'=>'昵称',
                    'realname'=>'实名',
                    'openid'=>'openid',
                    'is_clerk'=> '店长身份',
                    'onmid' => '推荐会员',
                    'onmid_copartner_id' => '推荐会员机构id',
                    'clerk_id' => '店长id',
                    'clerk_copartner_id'=> '店长机构id',
                    'mentor_id' => '帮扶店长id',
                    'mentor_copartner_id' => '帮扶店长机构id',
                    'copartner_id' => '合伙人id',
                    'copartner_account_id' => '招商经理id',
                    'copartner' => '机构',
                    'sdmid'  => '三迪梦卡号',
                    'org_id' => 'org',
                    'orders' => '订单',
                    'credit3' => '可提现余额',
                ];
                $mem['is_clerk'] = ($mem['is_clerk'] && $mem['clerk_status']) ? '是' : '否';
                $mem['mentor_id'] = $mem['mentor_id'] ? $mem['mentor_id'] : '无上级';
                $copartner = pdo_get('elapp_shop_copartner_user', ['id' => $mem['copartner_id']]);
                //$mem['copartner'] = $copartner ? ($copartner['mcnname'] . "({$copartner['id']})" . ($copartner['is_org_sub']?"<分公司>":'')) : 'NULL';
                $mem['sdmid'] = $this->sdmids($mem['id']);
                $mem['orders'] = $this->orders($mem['id']);
                $mem['credit3'] = m('member')->getCredit($mem['openid'], 'credit3');

                if ($mem['clerk_id']) {
                    $clerk = pdo_get('elapp_shop_member', ['id' => $mem['clerk_id']]);
                    $mem['clerk_id'] = $mem['clerk_id'] . " => {$clerk['nickname']}";
                    $mem['clerk_copartner_id'] = $clerk['copartner_id'];
                }
                if ($mem['mentor_id']) {
                    $mentor = pdo_get('elapp_shop_member', ['id' => $mem['mentor_id']]);
                    $mem['mentor_id'] = $mem['mentor_id'] . " => {$mentor['nickname']}";
                    $mem['mentor_copartner_id'] = $mentor['copartner_id'];
                }
                if ($mem['onmid']) {
                    $onuser = pdo_get('elapp_shop_member', ['id' => $mem['onmid']]);
                    $mem['onmid'] = $mem['onmid'] . " => {$onuser['nickname']}";
                    $mem['onmid_copartner_id'] = $onuser['copartner_id'];
                }

                $this->map_dump($map, $mem);
            } else {
                dump("无用户信息: " . $params['id']);
            }
        }
    }

    private function orders($mid)
    {
        $list = pdo_getall('elapp_shop_order', ['member_id' => $mid,'status >'=>0,'refundstate'=>0, 'refundtime'=>0]);
        $orders=[];
        foreach ($list as $k => $v) {
            $orders[] = [
                'id'=>$v['id'],
                'activity_id'=>$v['activity_id'],
                'status'=>$v['status'],
                'refundtime'=> $v['refundtime'] == 0 ? '': date('Y-m-d H:i:s', $v['refundtime']),
                'refundstate'=>$v['refundstate'],
            ];
        }
        return $orders;
    }

    private function sdmids($id)
    {
        $ms = pdo_getall('elapp_shop_sandimeng_member', ['member_id'=>$id]);
        if ($ms) {
            return array_column($ms, null, 'card_no');
        }
        return [];
    }

    private function map()
    {
        return [
            'id'=>'订单id',
            'ordersn' => '订单号',
            "clerk_vip_commission" => "[店长]VIP订单提成",
            "clerk_goods_commission" => "[店长]商品订单提成",
            "clerk_activity_commission" => "[店长]礼包提成",
            "clerk_mentor_commission" => "[店长] 团队收益|帮扶",
            "copartner_vip_commission" => "[合伙人] VIP订单提成",
            "copartner_goods_commission" => "[合伙人] 商品订单提成",
            "copartner_activity_commission" => "[合伙人] 礼包提成",
            "copartner_mentor_commission" => "[合伙人] 团队收益|帮扶",
//            "org_sub_commission" => "0.00",
//            "org_sub_mentor_commission" => "0.00",
        ];
    }

    private function update_path($params)
    {
        $this->checkPwd($params);

        if ($params['id'] == 0) {
            pdo_query('update ims_elapp_shop_member set clerk_id=id,mentor_id=0, copartner_user_path=copartner_id where org_id=2');
            pdo_query('update ims_elapp_shop_copartner_user set copartner_user_path=id,mentor_id=0 where org_id=2');
        }

        dump("done");
        $update = [
            'copartner_user_path' => '',
        ];
        // 找出所有三迪梦的用户
        // 删除 path
        // 删除 cop path


        // 所有三迪梦的人
        // 找出他的有效上级
        // 1. 找出他的有效上级
        // 2. 更新他的上级为有效上级
        // 更新他的上级为有效上级
    }

    private function delete_settle($params)
    {
        die;
        $this->checkPwd($params);

        $sql = "select * from ims_elapp_shop_settle_order where id >= 1640";
        $orders = pdo_fetchall($sql);
        foreach ($orders as $order) {
            if ($order['no_settle_user'] == 0) {
                if ($orders['role_id'] == 'copartner'||$orders['role_id'] == 'activity_2_fist_gift'||$orders['role_id'] == 'copartner_mentor') {
                    pdo_run('update ' . tablename('elapp_shop_copartner_user') . ' set settle_money = settle_money - ' . $order['commission'] . ' where id = ' . $order['belong_to']);
                    file_put_contents(__DIR__ . '/debug.txt', "订单结算 回滚:" . 'update ' . tablename('elapp_shop_copartner_user') . ' set settle_money = settle_money - ' . $order['commission'] . ' where id = ' . $order['belong_to']. "\n", FILE_APPEND);
                } else if ($orders['role_id'] == 'clerk'||$orders['role_id'] == 'mentor'||$orders['role_id'] == 'activity_1_1'||$orders['role_id'] == 'clerk_2') {
                    m('member')->setCredit($order['openid'], 'credit3', -$order['commission'], "订单结算 回滚 {$orders['role_id']} {$order['settle_date']} {$order['commission']}");
                }
//                copartner_platform_service_fee
//                clerk_platform_service_fee
//                org_sub
//                org_sub_mentor

                $os = json_decode($order['orders'],true);
                if (!empty($os['goods'])) {
                    pdo_update('elapp_shop_order', ['is_settle'=>0], ['id' => $os['goods']]);
                    pdo_delete('elapp_shop_settle_order_handle_record', ['order_type'=>'goods', 'orderid'=> $os['goods']]);
                    pdo_delete('elapp_shop_settle_order_oids', ['order_type'=>1, 'order_id'=> $os['goods']]);
                }
                if (!empty($os['membercard'])) {
                    pdo_update('elapp_shop_member_card_order', ['is_settle'=>0], ['id' => $os['membercard']]);
                    pdo_delete('elapp_shop_settle_order_handle_record', ['order_type'=>'membercard', 'orderid'=> $os['membercard']]);
                    pdo_delete('elapp_shop_settle_order_oids', ['order_type'=>2, 'order_id'=> $os['goods']]);

                }
                if (!empty($os['servicefee'])) {
                    pdo_update('elapp_shop_member_servicefee_order', ['is_settle'=>0], ['id' => $os['servicefee']]);
                    pdo_delete('elapp_shop_settle_order_handle_record', ['order_type'=>'servicefee', 'orderid'=> $os['servicefee']]);
                    pdo_delete('elapp_shop_settle_order_oids', ['order_type'=>3, 'order_id'=> $os['goods']]);

                }
                pdo_delete('elapp_shop_settle_order', ['id'=>$order['id']]);
            }
        }
    }

    private function delete_money($params)
    {
        die;
        global $_W;
        $this->checkPwd($params);
        $_W['uniacid'] = 1;

        $members = pdo_getall('elapp_shop_member', ['org_id'=>2], ['id','openid']);
        foreach ($members as $member) {
            $credit3 = m("member")->getCredit($member['openid'], "credit3");
            // 通过bc方法比对，如果大于0则执行更新
            if ($credit3 != '0.00') {
                $result = m('member')->setCredit($member['openid'], 'credit3', -$credit3,  '结算回滚，收益清零');
            }
        }
    }
    private function update_handle_record($params)
    {
        $this->checkPwd($params);

        pdo_begin();
        $record = pdo_getall('elapp_shop_settle_order_handle_record');
        foreach ($record as $r) {
            $update = false;
            $data = json_decode($r['data'],true);
            if (isset($data['clerk_origin'])) {
                $update = true;
                $data['clerk_team'] = $data['clerk_origin'];
                unset($data['clerk_origin']);
            }
            if (isset($data['copartner_origin'])) {
                $update = true;
                $data['copartner_team'] = $data['copartner_origin'];
                unset($data['copartner_origin']);
            }
            if ($update) {
                $data = json_encode($data);
                pdo_update('elapp_shop_settle_order_handle_record', ['data'=>$data], ['id'=>$r['id']]);
                dump('update');
            }
        }
        pdo_commit();
    }

    public function users()
    {

        // 修复订单 mentor_id 为 0
//        $order = pdo_get('elapp_shop_order', ['id' => 13671]);
//        if ($order['mentor_id'] == 0) {
//            if ($order['clerk_id']!=0) {
//                $clerk = m('member')->getMember($order['clerk_id']);
//                if ($clerk) {
//                    $mentor_id = $clerk['mentor_id'];
//                    if ($mentor_id) {
//                        $msg = "'订单{$order['id']}:{$order['ordersn']}, 将mentor_id从{$order['mentor_id']}更新为{$mentor_id}'";
//                        $data = ['type'=>1, 'member_id'=>$order['member_id'], 'message'=> $msg ,'created_at'=>time()];
//                        pdo_insert('elapp_shop_member_logs', $data);
//                        pdo_update('elapp_shop_order', ['mentor_id'=>$mentor_id], ['id' => $order['id']]);
//                    }
//                }
//            }
//        }


        // 1. is clerk no mentor
//        $users = pdo_getall('elapp_shop_member', ['org_id'=>2,'id >'=>11492,'is_clerk'=>1,'mentor_id'=>0], ['id','nickname','openid','clerk_id','onmid','mentor_id','is_clerk','copartner_id']);
//        $map = ['id'=> 'id','clerk_id'=>'clerk_id','mentor_id'=>'mentor_id','copartner_id'=>'copartner_id'];
//        $this->map_dump_arr($map, $users);
//        foreach ($users as $user) {
//            $data = ['type'=>0, 'member_id'=>$user['id'], 'message'=>'用户没有关联上级, mentor_id=0','created_at'=>time()];
//            $cop = pdo_get('elapp_shop_copartner_user', ['id' => $user['copartner_id']]);
//            if (empty($cop)) {
//                $data['message'] .= ', 合伙人 ' . $user['copartner_id'] . '不存在';
//            }
//            pdo_insert('elapp_shop_member_logs', $data);
//        }
//        die;

        // 订单 clerk_id 和用户mentor_id不匹配
        $orders = pdo_getall('elapp_shop_order', ['activity_id'=>4, 'status >'=>0], ['id','member_id','openid','clerk_id', 'mentor_id','copartner_id']);
        foreach ($orders as $o) {
            $m = m('member')->getMember($o['openid']);
            if ($o['clerk_id'] != $m['mentor_id'] || $m['mentor_id']==0) {
                $data = ['type'=>0, 'member_id'=>$m['id'], 'message'=>'订单clerk_id和用户mentor_id不匹配','created_at'=>time()];
                $data['message'] .= ', 订单clerk_id: ' . $o['clerk_id'] . ', 用户mentor_id: ' . $m['mentor_id'];
                pdo_insert('elapp_shop_member_logs', $data);
            }
        }

        die;


        // 2. is buy activity 4 but no copartner
//        $orders = pdo_getall('elapp_shop_order', ['activity_id'=>4], ['id','member_id','openid']);
//        foreach ($orders as $order) {
//            $o = pdo_get('elapp_shop_copartner_user', ['openid' => $order['openid']]);
//            if (empty($o)) {
//                $data = ['member_id'=>$order['member_id'], 'message'=>'用户购买了订单创建合伙人', 'created_at'=>time()];
//                pdo_insert('elapp_shop_member_logs', $data);
//            }
//        }

        // 用户订单 clerk_id

        $users = pdo_getall('elapp_shop_member', ['org_id'=>2,'id >'=>11492], ['id','nickname','openid','clerk_id','onmid','mentor_id','is_clerk','copartner_id']);
        $map = ['id'=> 'id','clerk_id'=>'clerk_id','mentor_id'=>'mentor_id','copartner_id'=>'copartner_id'];
        $this->map_dump_arr($map, $users);
        foreach ($users as $user) {
            $data = ['member_id'=>$user['id'], 'message'=>'用户没有关联上级, mentor_id=0','created_at'=>time()];
            $cop = pdo_get('elapp_shop_copartner_user', ['id' => $user['copartner_id']]);
            if (empty($cop)) {
                $data['message'] .= ', 合伙人 ' . $user['copartner_id'] . '不存在';
            }
            pdo_insert('elapp_shop_member_logs', $data);
        }
        die;

        // clerk_id
        /** @var model\CopartnerModel $cop */
        $cop = p('copartner');
        $users = array_chunk($users, 10);

        foreach ($users[0] as $user) {
            $is = $cop->isCopartnerAccount($user['openid']);
            $order = pdo_getall('elapp_shop_order', ['member_id'=>$user['id'], 'activity_id >'=>0,'status >'=>0]);

            if ($is) {
                dump($user);
                $f = ['id'=> 'id','clerk_id'=>'clerk_id','mentor_id'=>'mentor_id','copartner_id'=>'copartner_id','activity_id'=>'activity_id','status'=>'status'];
                $this->map_dump_arr($f, $order);
                dump($order);

                echo "<hr>";
            }

        }


//        dump($users);
    }


    /**
     * 补鸡蛋活动记录
     * @param $params
     * @return void
     */
    public function egg($params)
    {
        $id_or_no = $params['id_or_no'];
        $order_name = $params['type'] == 'card' ? 'elapp_shop_member_card_order' : 'elapp_shop_order';
        $order_sn = $params['type'] == 'card' ? 'orderno' : 'ordersn';
        $order_type = $params['type'] == 'card' ? 1 : 0;
        $og = $params['type'] == 'card' ? 'elapp_shop_member_card_order_card' : 'elapp_shop_order_goods';
        $ogid = $params['type'] == 'card' ? 'cardsid' : 'goodsid';
        $goods = $params['type'] == 'card' ? 'elapp_shop_member_card' : 'elapp_shop_goods';
        $goods_name = $params['type'] == 'card' ? 'name' : 'title';

        $logic = new \app\controller\activity\EggTuanGiftLogic();

        $order = pdo_get($order_name, ['id'=>$id_or_no]);
        if (empty($order)) {
            $order = pdo_get($order_name, [$order_sn=>$id_or_no]);
        }
        if (empty($order)) {
            dump('订单不存在');
            return;
        }
        $goods_id = pdo_get($og, ['orderid'=>$order['id']]);
        if (empty($goods_id[$ogid])) {
            dump('商品不存在');
            return;
        } else {
            $goods_id = $goods_id[$ogid];
            $goods_title = pdo_getcolumn($goods, ['id' => $goods_id], $goods_name);
        }

        // 判断订单是否已经参团，如果已经参团则不再参团
        $member = m('member')->getMember($order['member_id']);
        if (empty($member)) {
            dump('用户不存在');
            return;
        }

        if (empty($member['onmid'])) {
            dump('用户onmid不存在');
            return;
        }

        $onmid_member = m('member')->getMember($member['onmid']);
        if (empty($onmid_member)) {
            dump('推荐人不存在');
            return;
        }

        // 加入推荐团
        $logic->joinTuan($order['id'], $order_type, $member, $goods_id, $goods_title, $onmid_member['id'], 3);
    }

    private function getParent_sdm()
    {
        // 所有的三迪梦会员查询出来,判断关系 [done]
        $sdms = pdo_getall('elapp_shop_sandimeng_member', ['member_id >' => 0]);
        foreach ($sdms as $sdm) {
            $parent = $this->getSdmParent($sdm, $sdm['member_id']);
            $member = m('member')->getMember($sdm['member_id']);
            if ($member['mentor_id']!=$parent['member_id']) {
                dump("{$member['id']}: 三迪梦上级: " . $parent['member_id'] . ' 当前用户上级: ' . $member['mentor_id']);
            }
        }
    }
    private function getParent_Member()
    {
        $members = pdo_getall('elapp_shop_member', [ 'id >'=>17089, 'org_id'=>2], ['id','nickname','openid','clerk_id','mentor_id','copartner_id']);
        foreach ($members as $m) {

            if ($m['is_clerk'] == 1 && $m['mentor_id'] == 0) {
                dump("{$m['id']}: mentor_id = 0");
            }

            // copartner_id 如果自己有机构，那么则等于自己，如果没有则等于mentor_id的机构id
            $copartner = pdo_get('elapp_shop_copartner_user', ['del_at'=>0, 'mid' => $m['id']]);
            if ($copartner) {
                if ($m['copartner_id'] != $copartner['id']) {
                    dump("用户： {$m['nickname']} {$m['id']}: copartner_id 应该是 {$copartner['id']}, 当前是：{$m['copartner_id']}, 依据合伙人判断");
                }
            } else {

                if ($m['mentor_id']) {
                    $mentor = m('member')->getMember($m['mentor_id']);
                    if (empty($mentor)) {
                        dump("用户： {$m['nickname']} {$m['id']}: mentor_id {$m['mentor_id']} 的用户不存在");
                    }
                    if (@$mentor['copartner_id'] != $m['copartner_id']) {
                        dump("用户： {$m['nickname']} {$m['id']}: copartner_id 应该是 mentor {$m['mentor_id']} 的copartner_id: {$mentor['copartner_id']}, 当前是：{$m['copartner_id']}");
                    }
                } else if ($m['clerk_id']) {
                    if ($m['clerk_id']!=$m['id']) {
                        $clerk = m('member')->getMember($m['clerk_id']);
                        if (empty($clerk)) {
                            dump("用户： {$m['nickname']} {$m['id']}: clerk_id {$m['clerk_id']} 的用户不存在");
                        }
                        if (@$clerk['copartner_id'] != $m['copartner_id']) {
                            dump("用户： {$m['nickname']} {$m['id']}: copartner_id 应该是 clerk {$m['clerk_id']} 的copartner_id: {$clerk['copartner_id']}, 当前是：{$m['copartner_id']}");
                            echo "<a target='_blank' href='/web.php/activity.temp/main?action=member&id={$m['id']}&copartner_id={$clerk['copartner_id']}'>查看</a>";
                        }
                    }
                } else if ($m['onmid']) {
                    if ($m['onmid']!=$m['id']) {
                        $onuser = m('member')->getMember($m['onmid']);
                        if (empty($onuser)) {
                            dump("用户： {$m['nickname']} {$m['id']}: onmid {$m['onmid']} 的用户不存在");
                        }
                        if (@$onuser['copartner_id'] != $m['copartner_id']) {
                            dump("用户： {$m['nickname']} {$m['id']}: copartner_id 应该是 onuser {$m['onmid']} 的copartner_id: {$onuser['copartner_id']}, 当前是：{$m['copartner_id']}");
                        }
                    }
                }

            }

            // copartner_id, mentor_id, clerk_id




        }
    }

    private function getParent_orders()
    {
        // 获取org_id=1的用户的订单
        $orders = pdo_getall('elapp_shop_order', [ 'id !='=>12195, 'activity_id'=>4, 'status >'=>0,'refundstate'=>0, 'refundtime'=>0, 'org_id' => 2]);

        foreach ($orders as $order) {
            $m = pdo_get('elapp_shop_member', ['id' => $order['member_id']]);
            if ($order['clerk_id'] != $m['clerk_id']) {
                dump("用户的订单： {$order['id']} clerk_id 应该是 {$m['clerk_id']}, 当前是：{$order['clerk_id']}");
            }

            $clerk = pdo_get('elapp_shop_member', ['id' => $order['clerk_id']]);

            if ($order['copartner_id'] != $clerk['copartner_id']) {
                dump("用户的订单： {$order['id']} copartner_id 应该是 {$clerk['copartner_id']}, 当前是：{$order['copartner_id']}");
            }

//            if ($order['mentor_id'] != $clerk['mentor_id']) {
//                dump("用户的订单： {$order['id']} mentor_id 应该是 {$clerk['mentor_id']}, 当前是：{$order['mentor_id']}");
//            }
        }
    }

    public function getParent()
    {
        global $_W, $_GPC;
        $mid = intval($_GPC['mid']);
//        $sdm = pdo_get('elapp_shop_sandimeng_member', ['member_id' => $mid]);

//        $this->getParent_sdm();
//
//        // 从member角度看检查会员关系
//        $this->getParent_Member();
//
//        $this->getParent_orders();

        $this->checkOrders();
    }
    private function checkOrders()
    {
        $orders = pdo_getall('elapp_shop_order', ['activity_id'=>[4,1], 'status >'=>0, 'refundstate'=>0, 'refundtime'=>0]);
        foreach ($orders as $order) {
            $member = m('member')->getMember($order['member_id']);
            $clerk = m('member')->getMember($order['clerk_id']);
            $copartner = pdo_get('elapp_shop_copartner_user', ['id' => $order['copartner_id']]);
            // 如果店长和用户的店长不一致
            if ($clerk['id'] != $member['clerk_id']) {
                dump("订单 {$order['id']} clerk_id 应该是 {$member['clerk_id']}, 当前是：{$clerk['id']}");
            }
            if ($clerk['copartner_id'] != $order['copartner_id']) {
                dump("订单 {$order['id']} copartner_id 应该是 {$clerk['copartner_id']}, 当前是：{$order['copartner_id']}");
            }
        }
    }

    private function getSdmParent($sdm, $mid, $deep = 0)
    {
        if ($deep>25) {
            dump("mid: $mid 层次太多了");
            return false;
        }

        if (!$sdm) {
            return false;
        }
        // 最顶级
        if ($sdm['card_no'] == '36072270') {
            return false;
        }

        $parent = pdo_get('elapp_shop_sandimeng_member', ['card_no' => $sdm['refer_id']]);

        if ($parent && $parent['member_id'] == $mid) {
            dump("mid: $mid 取出上级是自己了");
            // 取出上级是自己了
            return false;
        }

        if ($this->isValidSmdMember($parent)) {
            return $parent;
        }

        return $this->getSdmParent($parent, $mid, $deep+1);
    }

    private function isValidSmdMember($sdm)
    {
        if ($sdm['member_id']==0) {
            return false;
        }

        $order = pdo_get('elapp_shop_order', ['activity_id'=>4, 'status >'=>0,'refundstate'=>0, 'refundtime'=>0, 'member_id' => $sdm['member_id']]);
        return $order ? true : false;
    }

    private function checkPwd($params)
    {
        // 如果不是post请求则拒绝
        if ($_SERVER['REQUEST_METHOD'] != 'POST') {
            dump('请求错误');die;
        }

        $flag =  $params['pwd'] == '123';
        if (!$flag) {
            dump('密码错误');die;
        }
    }

    public function createEggTuan(){
        global $_W, $_GPC;
        $orderid = intval($_GPC['orderid']);
        $member_id = intval($_GPC['member_id']);
        $postdata = new activity\OrderPayData($orderid,activity\OrderPayData::ORDER_TYPE_GOODS,$member_id);
        $do = (new activity\ActivityEggTuanGift)->pay($postdata);
        dump('创建成功');
    }

    private function add_settle_money($params)
    {
        $role_id = $params['role_id'];
        $belong_to = $params['belong_to'];
        $money = $params['money'];
        $orderid = $params['orderid'];
    }
}
