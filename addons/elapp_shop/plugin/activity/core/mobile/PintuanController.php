<?php

namespace app\controller\activity;

use app\controller\activity\ActivityEggTuanGift;
use app\controller\activity\EggTuanGiftLogic;
use app\controller\MobileLoginPage;
use app\model\GoodsModel;
use app\model\MemberModel;

class PintuanController extends MobileLoginPage
{
    public function main()
    {
        global $_W, $_GPC;
        $openid              = $_W['openid'];
        $_W['shopset']['shop']['name'] = "全民供享药房";
        $mid = $this->memberId;
        if (empty($this->memberId)) {
            $member = m('member')->getMember($_W['openid']);
            $mid = $member['id'] ?? 0;
        }

        $EggTuanGiftLogic = new EggTuanGiftLogic();
        $result = $EggTuanGiftLogic->getEggIndexInfo($mid);
        $info = $result['data'];
        if ($result['code'] !== 0) {
            show_message($result['msg'], '', 'error');
        }

        $banner = $info['banner'];
        $isvip  = $info['isvip'];
        $activity_info = $info['activity_info'];
        $goods_info = $info['goods_info'];

        include $this->template("activity/pintuan");
    }

    public function get_list()
    {
        global $_GPC;
        $goodsid = $_GPC['goodsid'] ?? 0;
        $type = $_GPC['type'];
        $EggTuanGiftLogic = new EggTuanGiftLogic();
        $list             = $EggTuanGiftLogic->getList($this->memberId, $type == 'users'? 1:0, $goodsid, 1, 1000);

        return $this->succeed($type, $list);
    }

    public function redeemCoupon()
    {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);

        $logic = app(EggTuanGiftLogic::class);
        $result = $logic->redeemCoupon($id, $this->memberId);

        if ($result['code'] === 0) {
            return $this->succeed('领取成功');
        } else {
            return $this->fail($result['msg'] ?? '领取失败', $result['data'] ?? [], $result['code'] ?? 1);
        }
    }
}
