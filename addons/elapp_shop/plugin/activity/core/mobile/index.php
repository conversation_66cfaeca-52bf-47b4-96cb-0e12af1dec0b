<?php
namespace app\controller\activity;
use app\controller\PluginMobilePage;
use app\model\ActivityModel;

class IndexController extends PluginMobilePage
{
	public function main()
	{
        /** @var ActivityModel $activityPlugin */
		$activityPlugin= p('activity');
        $activity_config = $activityPlugin->getActivityConfig(1);
        if($activity_config && count($activity_config)){
        	$data = array_values($activity_config['config']['plans']);        	
        }
        $_W['shopset']['shop']['name'] = "全民供享药房";
		include $this->template();
	}
}