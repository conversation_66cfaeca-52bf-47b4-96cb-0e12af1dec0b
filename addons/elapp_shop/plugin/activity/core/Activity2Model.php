<?php
namespace app\model;

use app\BaseModel;

class Activity2Model extends BaseModel
{
    protected $name = 'activity';

    public static function onAfterRead($model)
    {
        if (!empty($model['config'])) {
            $model['config'] = json_decode($model['config'], true);
        } else {
            $model['config'] = [];
        }
    }

    public static function onBeforeUpdate($model)
    {
        if (is_array($model['config'])) {
            $model['config'] = json_encode($model['config']);
        }
    }
    public static function onBeforeInsert($model)
    {
        self::onBeforeUpdate($model);
    }
}