<?php
namespace app\controller\activity;
// 鸡蛋推荐送礼活动
use app\controller\settle\common\checker\condition\IsNotClerkChecker;
use app\controller\settle\common\checker\param\MemberCheckParameter;
use app\model\Activity2Model;
use app\model\ActivityModel;
use app\model\GoodsModel;

class EggTuanGiftLogic
{
    // 用户是否拥有鸡蛋拼团权限 (普通会员，不是clerk)
    public function hasEggTuanPermission(int $member_id)
    {
        $m = m('member')->getMember($member_id);

        $a = new ActivityEggTuanGift();

        try {
            $config = $a->getConfig();
        } catch (\Exception $e) {
            return false;
        }
        // 获取参与活动的会员卡ids
        $member_card_ids = $config['member_card_ids'] ?? [];

        // 读取配置允许的会员卡数量
        foreach ($member_card_ids as $member_card_id) {
            // errno 0 已购卡 1 未开通 2 缺少参数
            // using 2 永久 1 有效期内 -1 已过期
            $checkmcard = p('membercard')->check_Hasget($member_card_id, $m['openid']);
            // 检测会员是否已购卡且在有效期内
            if ($checkmcard && $checkmcard['errno'] == 0 && in_array($checkmcard['using'], [2,1])) {
                return true;
            }
        }

        return false;
//        // 如果用户不是vip会员不参与活动
//        $level = pdo_get('elapp_shop_member_level', ['id' => $m['level']]);
//        return $level && $level['level'] > 0;

//        $params = new MemberCheckParameter($m);
//
//        // 检查会员不是店员
//        // todo 抽象通用可组合的检查器获取的方法
//        $have = IsNotClerkChecker::newInstance()->check($params)->getResult();
//
//        // 注册时间 > 一年的用户才允许
//        $have = $have && $m['createtime'] < time() - 365 * 24 * 3600;
//
//        return $have;
    }

    // 获取当前用户的推荐团列表

    /**
     * @param int $member_id
     * @param int $order_type 0:goods, 1: member_card
     * @param int $goodsid
     * @param int $page
     * @param $pagesize
     * @return array|false|mixed
     */
    public function getList(int $member_id,int $order_type, int $goodsid = 0, int $page = 1, $pagesize = 10)
    {
        $page = min($page, 1);
        if ($goodsid) {
            $list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_tuan_record') .
                ' WHERE member_id=:member_id and order_type=:order_type order by id desc limit ' .
                ($page - 1) * $pagesize . ',' . $pagesize, array(':member_id' => $member_id, ':order_type' => $order_type));
        } else {
            $list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_tuan_record') .
                ' WHERE member_id=:member_id and order_type=:order_type order by id desc limit ' .
                ($page - 1) * $pagesize . ',' . $pagesize, array(':member_id' => $member_id, ':order_type' => $order_type));
        }
        if (!empty($list)) {
            $list = array_map(function ($item) {
                $item['create_time'] = date('Y-m-d H:i:s', $item['create_time']);
                $item['data'] = json_decode($item['data'], true);
                if (!empty($item['data']['orders'])) {
                    foreach ($item['data']['orders'] as &$order) {
                        $order['avatar'] = tomedia(m('member')->getMember($order['member_id'])['avatar']);
                    }
                }
                return $item;
            }, $list);
        }
        return $list;
    }

    //获取tuan信息
    public function getInfo( int $id, int $member_id)
    {
        $list = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_tuan_record') . ' WHERE member_id=:member_id and id = :id ', array(':member_id' => $member_id,':id'=>$id));
        return $list;
    }

    // 领取礼物 （发券）
    public function sendCoupon($tuan_id, $coupon_id)
    {
        $tuan = pdo_get('elapp_shop_tuan_record', ['id' => $tuan_id]);

        $status = $tuan['status'] == 1 && $tuan['coupon_status'] == 0;
        if (!$status) {
            return false;
        }

        $result = p('membercard')->send_coupon($this->member_info['openid'], $coupon_id, 1);
        if(count($result['error']) > 0){
            return false;
        }else{
            pdo_update('elapp_shop_tuan_record', array('coupon_status' => 1, 'coupon_time' => time()), array('id' => $tuan_id));
            return true;
        }
    }

    // 获取能加入的团
    public function getCanJoinedTuan($order_type, $goods_id, $owner_id)
    {
        $sql = 'select * from ' . tablename('elapp_shop_tuan_record') . ' where order_type=:order_type and member_id=:member_id and status=0 and current_count<target_count';
        $data = pdo_fetch($sql, [':order_type' => $order_type, ':member_id' => $owner_id]);
        $data = $this->prepareData($data);

        return $data;
    }

    private function join($tuan, $order_id, $member_id, $nickname)
    {
        $tuan['data']['orders'][$order_id] = [
            'order_id' => $order_id,
            'create_time' => time(),
            'member_id' => $member_id,
            'nickname' => $nickname,
        ];
        $ids = implode(',', array_keys($tuan['data']['orders']));

        $sql = 'update ' . tablename('elapp_shop_tuan_record') . ' set data=:data,order_ids=:ids,current_count=current_count+1 where current_count<=target_count and id=:id';
        pdo_query($sql, [':id' => $tuan['id'], 'data'=>json_encode($tuan['data']), 'ids'=> $ids]);
    }

    private function createTuan($order_type, $goods_id, $goods_name, $owner_id, $activity_id)
    {
        $act = new ActivityEggTuanGift();
        $target_count = $act->getConfig()['target_count'] ?? 3;

        $d = [
            'orders'=> [],
            'goods_name' => $goods_name,
        ];
        $data = [
            'order_type' => $order_type,
            'goods_id' => $goods_id,
            'member_id' => $owner_id,
            'activity_id' => $activity_id,
            'status' => 0,
            'data'=> json_encode($d),
            'order_ids' => '',
            'finish_ids' => '',
            'current_count' => 0,
            'target_count' => $target_count,
            'create_time' => time(),
        ];
        pdo_insert('elapp_shop_tuan_record', $data);
        $data['id'] = pdo_insertid();
        $data['data'] = $d;
        return $data;
    }

    /**
     * @param $order_id
     * @param $order_type int 0:goods, 1: member_card
     * @param $member
     * @param $goods_id
     * @param $goods_name
     * @param $owner_id
     * @param $activity_id
     * @return void
     */
    public function joinTuan($order_id, $order_type, $member, $goods_id, $goods_name, $owner_id, $activity_id)
    {
        // 判断订单是否已经参与团，通过find_in_set order_ids字段判断
        $sql = 'select * from ' . tablename('elapp_shop_tuan_record') . ' where order_type=:type and find_in_set(:oid, order_ids)';
        $tuan = pdo_fetch($sql, [':oid' => $order_id, ':type'=>$order_type]);
        if (!empty($tuan)) {
            // 如果已经参与团，那么直接返回
            return;
        }

        $tuan = $this->getCanJoinedTuan($order_type, $goods_id, $owner_id);
        if (!$tuan) {
            $tuan = $this->createTuan($order_type, $goods_id, $goods_name, $owner_id, $activity_id);
        }

        $this->join($tuan, $order_id, $member['id'], $member['nickname']);

        // 如果是会员卡订单，那么直接调用签收逻辑, 因为会员卡订单不需要签收
        if ($order_type == 1) {
            $this->orderSignOff($tuan, $order_id);
        }
    }

    public function getTuanRecordById($id)
    {
        $sql = 'select * from ' . tablename('elapp_shop_tuan_record') . ' where id=:id';
        $record = pdo_fetch($sql, [':id' => $id]);
        return $this->prepareData($record);
    }
    public function getTuanRecordByOrderId($order_id)
    {
        $sql = 'select * from ' . tablename('elapp_shop_tuan_record') . ' where find_in_set(:id, order_ids)';
        $record = pdo_fetch($sql, [':id' => $order_id]);
        return $this->prepareData($record);
    }

    // 订单签收处理
    public function orderSignOff($tuan, $order_id)
    {
        @file_put_contents('/www/wwwroot/qmgxyf.com/runtime/log/orderSignOff.log', json_encode($order_id) . "\n", FILE_APPEND);
        // 获取订单
        if ($tuan['status'] == 1) {
            return;
        }

        // 签收就把完成订单数+1，并把订单id写入已完成ids
        $sql = 'update ' . tablename('elapp_shop_tuan_record') . ' set finish_ids = CASE WHEN finish_ids = "" THEN :oid ELSE CONCAT(finish_ids, ",", :oid) END, finish_count=finish_count+1 where id=:id and find_in_set(:oid, order_ids) and find_in_set(:oid, finish_ids)=0';
        pdo_query($sql, [':id' => $tuan['id'], 'oid'=>$order_id]);

        $this->finish_tuan($tuan['id']);
    }

    /**
     * 更新推荐团状态，当它达到条件的时候将状态改成已完成
     * @param $tuan_id
     * @return void
     */
    public function finish_tuan($tuan_id)
    {
        // 当订单完成数 = 目标数，且无退款，那么就完成团
        $w = 'where id=:id and target_count=finish_count and target_count=current_count and refund_count=0';
        $sql = 'update ' . tablename('elapp_shop_tuan_record') . ' set status=1 ' . $w;
        pdo_query($sql, [':id' => $tuan_id]);
    }

    private function prepareData($data)
    {
        if (is_array($data) && !empty($data['data'])) {
            $data['data'] = json_decode($data['data'], true);
        }

        return $data;
    }

    public function increasingCount($id, $field, $count = 1)
    {
        $fields = [
            'current_count',
            'finish_count',
            'refund_count',
        ];
        if (!in_array($field, $fields) || $count == 0) {
            return;
        }

        $sql = 'update ' . tablename('elapp_shop_tuan_record') . " set $field=$field + :count where id=:id";
        pdo_query($sql, [':id' => $id, ':count' => $count]);
    }

    public function updateTuanStatus($id, int $int)
    {
        $sql = 'update ' . tablename('elapp_shop_tuan_record') . ' set status=:status where id=:id';
        pdo_query($sql, [':id' => $id, ':status' => $int]);
    }

    private function remove_from_ids($ids, $remove_id)
    {
        $ids = explode(',', $ids);
        $ids = array_diff($ids, [$remove_id]);
        return implode(',', $ids);
    }
    private function is_in_ids($id, $ids)
    {
        if (is_string($ids)) {
            $ids = explode(',', $ids);
        }

        return in_array($id, $ids, true);
    }

    // 删除订单数据
    public function delete_order_data($tuan, $order_id)
    {
        unset($tuan['data']['orders'][$order_id]);
        $ids = implode(',', array_keys($tuan['data']['orders']));
//        $finish_ids = $tuan['finish_ids'] ? $tuan['finish_ids'] . ',' . $order_id : $order_id;
        $dec = 'current_count=current_count-1';
        if ($this->is_in_ids($order_id, $tuan['finish_ids'])) {
            $finish_ids = $this->remove_from_ids($tuan['finish_ids'], $order_id);
            $dec .= ',finish_count=finish_count-1';
        } else {
            $finish_ids = $tuan['finish_ids'];
        }
        $sql = 'update ' . tablename('elapp_shop_tuan_record') . " set data=:data,order_ids=:ids,finish_ids=:finish_ids,$dec where id=:id";
        pdo_query($sql, [':id' => $tuan['id'], 'data'=>json_encode($tuan['data']), 'ids'=> $ids, 'finish_ids' => $finish_ids]);
    }

    public function redeemCoupon($id, $member_id)
    {
        if (!$this->lockWithRedis('egg.take.coupon.' . $id)) {
            return result(1, '操作过快');
        }

        $info = $this->getInfo($id, $member_id);

        if (!$info) {
            return result(1, '记录不存在');
        }
        if ($info['status'] == 0) {
            return result(1, '还没完成活动');
        }
        if ($info['coupon_status']) {
            return result(1, '已领取券,请勿重复领取');
        }

        if (!$this->hasEggTuanPermission($member_id)) {
            return result(0, '您还不是VIP会员，无法参与活动。');
        }
        $ActivityEggTuanGift = new ActivityEggTuanGift();
        $config = $ActivityEggTuanGift->getConfig();
        $coupon_id = $config['coupon_id'];
        if ($coupon_id) {
            $result = $this->sendCoupon($info['id'], $coupon_id);
            if ($result) {
                return result(0, '领取成功');
            } else {
                return result(1,  '领取失败');
            }
        } else {
            return result(1, '活动未开启或已关闭，请联系商家');
        }
    }

    function lockWithRedis($lockKey, $expire = 3) {
        // 锁定3秒，防止高并发下重复领券
        $open_redis = function_exists('redis') && !is_error(redis());
        if( $open_redis ) {
            $redis = redis();
            if (!is_error($redis)) {
                if($redis->get($lockKey)) {
                    return false;
                }
                $rand = uniqid();
                $redis->setex($lockKey, $expire, $rand);

                return $redis->get($lockKey) == $rand;
            }
        }

        return true;
    }

    /**
     * 获取鸡蛋活动首页信息
     * @param $member_id
     * @return array
     * @throws \Exception
     */
    public function getEggIndexInfo($member_id) {
        $banner = [
            ['linkurl' => '', 'imgurl' => STATIC_ROOT . 'shop/plugin/activity/images/banner1.jpg'],
            ['linkurl' => '', 'imgurl' => STATIC_ROOT . 'shop/plugin/activity/images/banner2.jpg'],
        ];

        $EggTuanGiftLogic = app(EggTuanGiftLogic::class);
        $ActivityEggTuanGift = new ActivityEggTuanGift();

        $activity_info    = $ActivityEggTuanGift->getConfig();
        if ($activity_info) {
            if (!empty($activity_info['index_page_rule_text'])) {
                $activity_info['index_page_rule_text'] = html_entity_decode($activity_info['index_page_rule_text']);
            }
        }

        $GoodsModel       = new GoodsModel();
        // 没传id时，则通过活动id获取商品id
        $goodsid = $activity_info['index_page_goods'][0] ?? 0;
        if ($goodsid) {
            $goods_info       = $GoodsModel->info('activity_id = 3 and id=' . intval($goodsid) . $this->activity_info['id'], 'id');
        } else {
            $goods_info       = $GoodsModel->info('activity_id = 3', 'id');
        }

        if (!$goods_info) {
            return result(1, '活动不存在');
        }

        $data = [
            'banner'=> $banner,
            'isvip' => $EggTuanGiftLogic->hasEggTuanPermission($member_id),
            'activity_info' => $activity_info,
            'goods_info' => $goods_info
        ];

        return result(0, '', $data);
    }
}