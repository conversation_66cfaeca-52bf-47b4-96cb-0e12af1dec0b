<?php

error_reporting(0);
require "../../../../../extend/framework/bootstrap.inc.php";
require dirname(__DIR__) . "/../../defines.php";
require "../../../../../extend/framework/common/function.php";
global $_W;
global $_GPC;
ignore_user_abort();
set_time_limit(0);


//print_r($_W['uniacid']);
//
///** @var SettleModel $plugin */
//$plugin = p('settle');
//if ($plugin) {
//    error_reporting(E_ALL ^ E_NOTICE);
//    ini_set('display_errors', '1');
//
//    $_W['uniacid'] = $_GPC['uniacid'];
//
//    $plugin->settleOrdersTask($_GPC['uniacid']);
//    $plugin->settleOrderAutoCheck();
//}
