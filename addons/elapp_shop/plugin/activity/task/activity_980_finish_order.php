<?php
error_reporting(0);
require "../../../../../extend/framework/bootstrap.inc.php";
require dirname(__DIR__) . "/../../defines.php";
require "../../../../../extend/framework/common/function.php";
global $_W;
global $_GPC;

ignore_user_abort();
set_time_limit(0);


// todo 980
// 自动签收收益达到980的活动订单
// 1. 获取90天后的活动订单未签收
function getOrders($_W) {
    $days_time =  7776000;
//    $days = 7776000; // 90天
    $w = [
        'uniacid' =>  $_W['uniacid'],
        'finishtime' => 0,
        'status' => 2,
        //'createtime <'=> time() - $days_time,
        'activity_id >' =>0
    ];
    $fields = ['id','openid'];
    $orders = pdo_getall('elapp_shop_order', $w, $fields);
    return $orders;
}

// 2. 遍历判断订单所有者是否收益达到980
function isOver980($member) {
    if (empty($member)) return false;

    $finish = p('activity')->isMemberFinishActivity($member);

    return $finish;
}

// 3. 已经达到则自动签收
function finishOrder($order) {
    $time = time();
    $orderid = $order['id'];
    pdo_query("update " . tablename('elapp_shop_order') . " set status=3,finishtime=:time where id=:orderid",array(':time'=>$time,':orderid'=>$orderid) );
    if ($order['isparent'] == 1) {
        return;
    }
    $order = pdo_get('elapp_shop_order', ['id'=>$order['id']]);

    m('member')->upgradeLevel($order['openid'], $orderid);
    m('order')->setGiveBalance($orderid, 1);
    m('notice')->sendOrderMessage($orderid);
    m('order')->fullback($orderid);
    m('order')->setStocksAndCredits($orderid, 3);

    $pcoupon = com('coupon');
    if ($pcoupon) {
        if (!empty($order['couponid'])) {
            $pcoupon->backConsumeCoupon($order['id']);
        }
        $pcoupon->sendcouponsbytask($order['id']);
    }

    $p = p('commission');
    $plugin_clerk = p('clerk');
    $plugin_vrshop = p('vrshop');
    $plugin_copartner = p('copartner');
    $plugin_mentor = p('mentor');
    $plugin_doctor = p('doctor');
    $pcoupon = com('coupon');

    if ($p) {
        $p->checkOrderFinish($orderid);
    }
    //虚店店长检测
    if($plugin_vrshop){
        $plugin_vrshop->checkOrderFinish($orderid);
    }
    //虚店店员检测
    if($plugin_clerk){
        $plugin_clerk->checkOrderFinish($orderid);
    }
    //虚店合伙人检测
    if($plugin_copartner){
        $plugin_copartner->checkOrderFinish($orderid);
    }
    //虚店扶植分红检测
    if($plugin_mentor){
        $plugin_mentor->checkOrderFinish($orderid);
    }
    //医生检测
    if($plugin_doctor){
        $plugin_doctor->checkOrderFinish($orderid);
    }
    //会员分享检测
    if (p('userpromote')) {
        //会员分享积分检测
        p('userpromote')->setCredits($order['onmid'], $orderid);
        //会员消费积分检测
        p('userpromote')->shopSetcredits($orderid);
    }

    if(p('lottery') && $order['merchid'] == 0){
        $res = p('lottery')->getLottery($order['openid'],1,array('money'=>$order['price'],'paytype'=>2));
        if($res){
            p('lottery')->getLotteryList($order['openid'],array('lottery_id'=>$res));
        }
    }
}

$uniacids = pdo_fetchall("SELECT uniacid FROM " . tablename("uni_account"));
if ($uniacids && is_array($uniacids)) {
    foreach ($uniacids as $uni) {
        $_W['uniacid'] = $uni['uniacid'];

        $orders = getOrders($_W);
        foreach ($orders as $order) {
            $member = m('member')->getMember($order['openid']);

            if (isOver980($member)) {
                finishOrder($order);
            }
        }
    }
}


