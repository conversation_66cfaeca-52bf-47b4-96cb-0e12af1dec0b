<?php
error_reporting(0);
require "../../../../../extend/framework/bootstrap.inc.php";
require dirname(__DIR__) . "/../../defines.php";
require "../../../../../extend/framework/common/function.php";
global $_W;
global $_GPC;

use app\controller\settle\CommonSettleHandler;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\SettleModel;

set_time_limit(0);

$table_settle_order = tablename('elapp_shop_settle_order');
$talbe_diyattrs_data = tablename('elapp_shop_diyattrs_data');

$type_user = \app\model\DiyattrsEnums::TYPE_USER;
$key = \app\model\DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE;


// 获取所有的机构设置
$cpuser = pdo_getall('elapp_shop_copartner_user', ['del_at'=>0], ['id','money']);

$sfids = [];

// 遍历所有机构，并解析money字段获取机构的提现限制,获取有限制servicefeeid的机构
foreach ($cpuser as $item) {
    $money = @json_decode($item['money'], true);
    if (!is_array($money) || empty($money)) {
        continue;
    }
    $set = $money['clerk'] ?? [];
    if (isset($set['hasMoney']) && $set['hasMoney'] == 1) {
        if (isset($set['withdrawlimittype']) && ($set['withdrawlimittype'] === 1 or $set['withdrawlimittype'] === '1') && $set['withdrawlimitservicefeeid']) {
            $sfids[$item['id']] = $set['withdrawlimitservicefeeid'];
        }
    }
}

$copids = array_keys($sfids);
if (!is_array($copids) || empty($copids)) {
    return;
}

// 1. 通过diyattr获取需要付服务费的用户
// 2. 通过 settle_order 获取收益满足条件的用户 进一步过滤用户
$sql = "select belong_to mid,uniacid from $table_settle_order where role_id not in ('copartner')
 and belong_to in (SELECT object_id FROM $talbe_diyattrs_data WHERE type=$type_user and `key`='$key')
 group by belong_to having sum(commission-commission_wait) > 0";
//$all = pdo_getslice('elapp_shop_diyattrs_data', ['key'=>\app\model\DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE], ['value']);
$mids = pdo_fetchall($sql);
if (!is_array($mids) || empty($mids)) {
    return;
}

$activity = new \app\model\ActivityModel();
/** @var \app\model\ServicefeeModel $servicefee */
$servicefee = p('servicefee');
$diyattrsModel = new DiyattrsModel();
foreach ($mids as $m) {

    $_W['uniacid'] = $m['uniacid'];
    $member = m('member')->getMember($m['mid']);

    // 用户不需要缴纳服务费，跳过
    if ($member['no_servicefee_pay'] == 1) {
        continue;
    }

//    planB 用户如果未达标，不自动购买服务费
    $isPlanB = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $member['id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_PLAN_B);
    if ($isPlanB && !$activity->isMemberFinishActivity($member)) {
        continue;
    }
    // planA 或 达标用户，自动购买首年服务费

    // 服务费
    $sf = $servicefee->getServiceFee($sfids[$member['copartner_id']]);
    if (!$sf) {
        continue;
    }

    // 如果用户收益余额小于服务费，则跳过
    $credit3 = m("member")->getCredit($member['openid'], "credit3");
    if (bccomp($credit3, $sf['price'], 2) < 0) { // $credit3 < $sf['price']
//        echo "用户{$member['id']}  收益余额：$credit3 小于服务费：{$sf['price']}，跳过\n";
        continue;
    }

    $check = $servicefee->check_Hasget($sfids[$member['copartner_id']], $member['openid']);
    if ($check['errno'] == 0) {// == 0 表示已经买过该服务费,删除未购买参数
        $diyattrsModel->deleteValue(DiyattrsEnums::TYPE_USER, $member['id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE);
        continue;
    }

    // 如果用户提现金额小于设置金额，则不扣除
    $apply_money = pdo_fetchcolumn('select ifnull(sum(apply_money),0) from ' . tablename('elapp_shop_settle_withdraw_apply') . ' where money_type = 0 and openid=:openid and status=1', [':openid' => $member['openid']]);
    $tradeSet = m('common')->getSysset('trade', 1);//获取交易设置
    $tradeSet['total_withdraw_money'] = $tradeSet['total_withdraw_money'] ?? 1000;
    // 如果用户累计提现金额小于设置金额，则不扣除
    if (bccomp($apply_money, $tradeSet['total_withdraw_money'], 2) < 0) {
        continue;
    }

    pdo_begin();
    $diyattrsModel->deleteValue(DiyattrsEnums::TYPE_USER, $member['id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER_NOT_BUY_SERVICE_FEE);

    // todo 购买服务费
    $servicefeeModel =new \app\model\ServicefeeModel();
    $result = $servicefeeModel->createOrder($member, $sf);
    if ($result['result'] === false) {
        pdo_rollback();
        continue;
    }
    $order = $result['data']['order'];
    // 支付订单处理
    $servicefeeModel->payFinishAfter($order);
    $data = array('paytype' => 'settle', 'status' => 1, 'paytime' => time(), 'finishtime' => time());
    pdo_update('elapp_shop_member_servicefee_order', $data, array('id' => $order['id']));

    // todo 创建服务费扣除结算记录
    $handler = new CommonSettleHandler();
    $sid = $handler->createOrUpdateSettleOrderWithCommission(-$order['total'],
        SettleModel::ROLE_TYPE_SYSTEM_DEDUCTION,
        SettleModel::ROLE_TYPE_MEMBER,
        'clerk', $order, SettleModel::ORDER_TYPE_SERVICEFEE, date('Y-m-d'));
    if ($sid) {
        (new SettleModel())->settleOrderAutoCheck($sid);
    } else {
        pdo_rollback();
    }

    pdo_commit();
}

