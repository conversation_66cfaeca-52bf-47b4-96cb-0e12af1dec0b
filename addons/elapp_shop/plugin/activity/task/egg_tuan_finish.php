<?php
error_reporting(0);
require "../../../../../extend/framework/bootstrap.inc.php";
require dirname(__DIR__) . "/../../defines.php";
require "../../../../../extend/framework/common/function.php";
global $_W;
global $_GPC;

ignore_user_abort();
set_time_limit(0);

// todo 980
// 自动签收收益达到980的活动订单
// 1. 获取90天后的活动订单未签收
function getOrders()
{
    $orders = pdo_getall('elapp_shop_tuan_record', ['status' => 0]);
    return $orders;
}

$tuans = getOrders();

foreach ($tuans as $t) {
    $orderids      = explode(',', $t['order_ids']);
    $all_finish    = false;
    $no_refund     = false;
    $success_count = 0;
    foreach ($orderids as $oid) {
        $result = pdo_get('elapp_shop_order', ['id' => $oid, 'activity_id' => 3, 'status' => 3], ['id', 'status', 'refundtime', 'refundstate']);
        if ($result && $result['refundtime'] == 0 && $result['refundstate'] == 0) {
            $success_count += 1;
            (new \app\controller\activity\EggTuanGiftLogic())->orderSignOff($t, $oid);
        }
    }
    if ($success_count >= 3) {
        $all_finish = true;
        $no_refund  = true;
        (new \app\controller\activity\EggTuanGiftLogic())->finish_tuan($t['id']);
    }
}
