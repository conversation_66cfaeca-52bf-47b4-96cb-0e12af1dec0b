{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">结算单信息
        <small>
            {if $data['paytype']==2}<label class="label label-warning">按周结算</label>{else}<label
                class="label label-primary ">按月结算</label>{/if}
            结算单号: {$data['billno']} 订单数: <span class="text text-danger">{$data['ordercount']}</span>  订单金额: <span class="text text-danger">{$data['ordermoney']}</span></small></span>
</div>
<div class="page-content">
    <div class="page-sub-toolbar">
        <span class=''>
            {ifp 'abonus.bonus.build'}
                <a class="btn btn-primary btn-sm" href="{php echo webUrl('abonus/bonus/build')}">创建结算单</a>
            {/if}
        </span>
    </div>
    <div class="row">
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">省级分红(元)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column text-danger">
                        <h2 class="no-margins yesterday-count"><span id="bonusmoney_pay1">{$data['bonusmoney_pay1']}</span></h2>
                        应分红: {$data['bonusmoney1']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">市级分红(元)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column text-danger" >
                        <h2 class="no-margins yesterday-count"><span id="bonusmoney_pay2" >{$data['bonusmoney_pay2']}</span></h2>
                        应分红: {$data['bonusmoney2']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">区级分红(元)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column text-danger">
                        <h2 class="no-margins yesterday-count"><span id="bonusmoney_pay3">{$data['bonusmoney_pay3']}</span></h2>
                        应分红: {$data['bonusmoney3']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">总分红(元)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column text-danger">
                        <h2 class="no-margins yesterday-count"><span id="bonusmoney_paytotal">{php echo $data['bonusmoney_pay1'] + $data['bonusmoney_pay2'] + $data['bonusmoney_pay3']}</span></h2>
                        应分红: {php echo $data['bonusmoney1'] + $data['bonusmoney2'] +$data['bonusmoney3']}
                    </div >
                </div>
            </div>
        </div>
    </div>


    <div class="row">
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">已结算省级代理(个)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column">
                        <h2 class="no-margins seven-count"><span id="aagentcount11">{$data['aagentcount11']}</span></h2>
                        省级代理数量: {$data['aagentcount1']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">已结算市级代理(个)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column">
                        <h2 class="no-margins seven-count"><span id="aagentcount22">{$data['aagentcount22']}</span></h2>
                        市级代理数量: {$data['aagentcount2']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">已结算区级代理(个)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column">
                        <h2 class="no-margins seven-count"><span id="aagentcount33">{$data['aagentcount33']}</span></h2>
                        区级代理数量: {php echo $data['aagentcount11'] + $data['aagentcount22'] + $data['aagentcount33']}
                    </div >
                </div>
            </div>
        </div>
        <div class="col-sm-3">
            <div style="border: 1px solid #e7eaec" class="summary_box float-e-margins">
                <div class="summary_title">
                    <span class="text-default title_inner">代理已结算(个)</span>
                </div>
                <div class="summary flex">
                    <div class="flex1 flex column">
                        <h2 class="no-margins seven-count"><span id="aagentcount44">{$data['aagentcount44']}</span></h2>
                        代理数量: {$data['aagentcount3']}
                    </div >
                </div>
            </div>
        </div>

    </div>

    <form action="" method="get" class="form-horizontal" role="form" id="form1">
        <div class="page-toolbar row m-b-sm m-t-sm">

            <div class="col-sm-12">
                {ifp 'abonus.bonus.confirm'}
                {if empty($data['status'])}

                <div class="input-group">
                    <div class="input-group-select">
                        <select id='paymoneylevel' class='form-control  input-sm select-md' style="width:105px;float:left;">
                            <option value=''>等级</option>
                            <option value='0'>{php echo empty($set['levelname'])?'普通等级': $set['levelname']}</option>
                            {loop $levels $level}
                            <option value='{$level['id']}'>{$level['levelname']}</option>
                            {/loop}
                        </select>
                    </div>
                    <input type="text" class="form-control input-sm" id="paymoney1" value="" placeholder="最终省级分红" style="float:left;width:210px;"/>
                    <input type="text" class="form-control input-sm" id="paymoney2" value="" placeholder="最终市级分红" style="float:left;width:210px;"/>
                    <input type="text" class="form-control input-sm" id="paymoney3" value="" placeholder="最终区级分红" style="float:left;width:210px;"/>
                         <span class="input-group-btn">
                           <button class="btn btn-sm btn-primary" id="btnset" type="button"> 统一设置分红</button>
                        </span>
                </div>
                {/if}
                {/if}
            </div>

        </div>
        <div class="page-toolbar row m-b-sm m-t-sm">
            <div class="col-sm-12 pull-right">
                <div class="input-group">
                    <div class="input-group-select">
                        <select name='aagenttype' class='form-control  input-sm select-md' style="width:105px;">
                            <option value=''>类型</option>
                            <option value='1' {if $_GPC['aagenttype']=='1'}selected{/if}>省级代理</option>
                            <option value='2' {if $_GPC['aagenttype']=='2'}selected{/if}>市级代理</option>
                            <option value='3' {if $_GPC['aagenttype']=='3'}selected{/if}>区级代理</option>
                        </select>
                    </div>
                    <div class="input-group-select">
                        <select name='level' class='form-control  input-sm select-md' style="width:100px;">
                            <option value=''>等级</option>
                            <option value='0' {if $_GPC['level']=='0'}selected{/if}>{php echo empty($set['levelname'])?'普通等级': $set['levelname']}</option>
                            {loop $levels $level}
                            <option value='{$level['id']}' {if $_GPC['level']==$level['id']}selected{/if}>{$level['levelname']}</option>
                            {/loop}
                        </select>
                    </div>
                    <div class="input-group-select">
                        <select name='status' class='form-control  input-sm select-md' style="width:100px;">
                            <option value='' {if $_GPC['status']==''}selected{/if}>状态</option>
                            <option value='0' {if $_GPC['status']=='0'}selected{/if}>未打款</option>
                            <option value='1' {if $_GPC['status']=='1'}selected{/if}>已打款</option>
                        </select>
                    </div>
                    <input type="text" class="form-control input-sm" name="keyword" value="{$_GPC['keyword']}"
                           placeholder="区域代理昵称/姓名/手机号"/>
                     <span class="input-group-btn">

                                            <button class="btn btn-primary" type="submit"> 搜索</button>
                                                                                        {ifp 'abonus.bonus.detail.export'}
                            <button type="submit" name="export" value="1" class="btn btn-success ">导出</button>
                            {/if}
                    </span>
                </div>

            </div>
        </div>


    </form>

    <table class="table table-hover  table-responsive ">
        <thead class="navbar-inner">
        <tr>
            {if empty($data['status']) || $data['status']==1 }
            <th style="width:25px;"><input type='checkbox' class="checkall"/></th>
            {/if}

            <th style='width:160px;'>单号</th>
            <th style='width:100px;'>姓名/手机</th>
            <th style='width:100px;'>等级/分红比例</th>
            <th style='width:120px;'>分红</th>
            <th style='width:70px;'>状态</th>
        </tr>
        </thead>
    </table>

    <div style="max-height:500px;overflow:auto;border:none; overflow-x:hidden;">
        <table class="table table-hover  table-responsive " style="table-layout: fixed;border:none;">
            <tbody>
            {loop $list $row}
            <tr>
                {if (empty($data['status']) || $data['status']==1)}
                <td style="width:25px;">
                    {if $row['status']!=1}
                    <input type='checkbox' class="checkitem" value="{$row['id']}"/>
                    {/if}
                </td>
                {/if}

                <td style='width:160px;' data-toggle='tooltip' title='{$row['nickname']}' style='width:80px;'>
                {$row['payno']} <br/>
                {if $row['aagenttype']==1}
                <span class="label label-primary">省级</span>
                {elseif $row['aagenttype']==2}
                <span class="label label-success">市级</span>
                {elseif $row['aagenttype']==3}
                <span class="label label-warning">区级</span>
                {/if}
                {ifp 'member.list.view'}
                <a href="{php echo webUrl('member/list/detail',array('id' => $row['mid']));}" target='_blank'>
                    <img src='{$row['avatar']}' style='width:30px;height:30px;padding1px;border:1px solid #ccc' />
                    {$row['nickname']}
                </a>
                {else}
                <img src='{$row['avatar']}' style='width:30px;height:30px;padding1px;border:1px solid #ccc' />
                {$row['nickname']}
                {/if}


                </td>

                <td style='width:100px;'>{$row['realname']}<br/>{$row['mobile']}</td>

                <td style='width:100px;'>
                    {if empty($row['levelname'])}
                    {php echo empty($set['levelname'])?'普通等级': $set['levelname']}
                    {else}{$row['levelname']}
                    {/if}<br/>
                    {if $row['aagenttype']<=1}<span class="label label-primary">省级：{$row['bonus1']}%</span><br />{/if}
                    {if $row['aagenttype']<=2}<span class="label label-success">市级：{$row['bonus2']}%</span><br />{/if}
                    <span class="label label-warning">区级：{$row['bonus3']}%</span>
                </td>
                <td style='width:120px;'>
                    {ifp 'abonus.bonus.confirm'}
                    {if empty($data['status'])}

                    {if $row['aagenttype']<=1}
                    省级：<a class="paymoney-{$row['aagenttype']}-set-{$row['partnerlevel']}" href='javascript:;' data-toggle='ajaxEdit'
                          data-href="{php echo webUrl('abonus/bonus/paymoney_aagent',array('id'=>$row['billid'],'baid'=>$row['id'],'paymoneytype'=>1 ))}">{$row['paymoney1']}</a> <br/>
                    {/if}
                    {if $row['aagenttype']<=2}
                    市级：<a class="paymoney-{$row['aagenttype']}-set-{$row['partnerlevel']}" href='javascript:;' data-toggle='ajaxEdit'
                          data-href="{php echo webUrl('abonus/bonus/paymoney_aagent',array('id'=>$row['billid'],'baid'=>$row['id'],'paymoneytype'=>2 ))}">{$row['paymoney2']}</a> <br/>
                    {/if}
                    区级：<a class="paymoney-{$row['aagenttype']}-set-{$row['partnerlevel']}" href='javascript:;' data-toggle='ajaxEdit'
                          data-href="{php echo webUrl('abonus/bonus/paymoney_aagent',array('id'=>$row['billid'],'baid'=>$row['id'],'paymoneytype'=>3 ))}">{$row['paymoney3']}</a>


                    {else}

                    {if $row['aagenttype']<=1}省级：{$row['paymoney1']}<br />{/if}
                    {if $row['aagenttype']<=2}市级：{$row['paymoney2']}<br />{/if}
                    区级：{$row['paymoney3']}
                    {/if}
                    {else}

                    {if $row['aagenttype']<=1}省级：{$row['paymoney1']}<br />{/if}
                    {if $row['aagenttype']<=2}市级：{$row['paymoney2']}<br />{/if}
                    区级：{$row['paymoney3']}
                    {/if}
                </td>
                <td style='width:70px;'>

                    {if empty($row['status'])}
                    <span class="label label-default">等待</span>
                    {elseif $row['status']==-1}
                    <span class="label label-danger">失败</span> <a data-toggle='tooltip' title='{$row['reason']}'><i
                        class="fa fa-question-circle"></i></a>
                    {elseif $row['status']==1}
                    <span class="label label-primary">成功</span>
                    {/if}

                </td>
            </tr>
            {/loop}
            </tbody>
        </table>
    </div>

    <div class="form-group" style="margin-top:20px;">
        <div class="col-sm-12" style="">

            {ifp 'abonus.bonus.confirm'}
            {if $data['status']==0}
            <input type="button" id="btnconfirm" value="确认结算单" class="btn btn-success"/>
            {/if}
            {/if}

            {ifp 'abonus.bonus.pay'}
            {if $data['status']==1}
            <input type="button" id="btnpay" value="开始结算" class="btn btn-primary"/>
            {/if}
            {/if}
            <a class="btn btn-default  btn-sm" href="{php echo webUrl('abonus/bonus/status'.$data['status'])}">返回列表</a>

        </div>
    </div>
</div>

<script>
    window.aagents = [];
    window.current = 0;

    $(function () {

        $('.checkall').click(function () {
            var checked = $(this).prop('checked');
            $('.checkitem').prop('checked', checked);
        });

        $('#btnpay').click(function () {
            pay();
        });
        $('#btnset').click(function () {
            setpay();
        });
        $('#btnconfirm').click(function () {
            confirm();
        });
    });

    function confirm(){

        tip.confirm('结算单确认后，无法进行修改，确认吗？',function(){

            $('#btnconfirm').button('loading');
            $.ajax({
                url: "{php echo webUrl('abonus/bonus/confirm')}",
                type: 'post',
                dataType: 'json',
                data: {
                    id: "{$data['id']}"
                },
                success: function (ret) {
                    $('#btnconfirm').button('reset');
                    var result = ret.result;
                    if (ret.status != 1) {
                        tip.msgbox.err(result.message);
                        return;
                    }
                    location.href = biz.url('abonus/bonus/status0');
                }
            });

        });

    }

    function setpay() {

        var level = $('#paymoneylevel').val();
        var paymoney1 = $('#paymoney1').val();
        var paymoney2 = $('#paymoney2').val();
        var paymoney3 = $('#paymoney3').val();

        if (level == '') {
            tip.msgbox.err("请选择要设置的代理商等级!");
            return;
        }

        $('#btnset').button('loading');
        $.ajax({
            url: "{php echo webUrl('abonus/bonus/paymoney_level')}",
            type: 'post',
            dataType: 'json',
            data: {
                id: "{$data['id']}", level: level, paymoney1:paymoney1,paymoney2:paymoney2,paymoney3:paymoney3
            },
            success: function (ret) {
                $('#btnset').button('reset');
                var result = ret.result;
                if (ret.status != 1) {

                    tip.msgbox.err(result.message);
                    return;
                }
                //$('.paymoney' + type+'set-' + level).html(value);
                tip.msgbox.suc('设置成功!');
            }
        });

    }
    function pay() {

        if ($('.checkitem:checked').length<=0) {
            tip.msgbox.err('请选择要结算的代理商 !') ;
            return;
        }
        $('.checkitem:checked').each(function(){
             window.aagents.push( $(this).val() );
        });
        window.current = 0;
        tip.confirm('确认要进行分红结算?', function () {

            $('#btnpay').attr('disabled', true).val('正在进行结算...');
            $('.checkitem,.checkall').attr('disabled',true);
            $.ajax({
                url: "{php echo webUrl('abonus/bonus/pay')}",
                type: 'post',
                dataType: 'json',
                data: {
                    id: "{$data['id']}"
                },
                success: function (ret) {

                    var result = ret.result;
                    if (ret.status != 1) {
                        $('.checkitem,.checkall').removeAttr('disabled',true);
                        $('#btnpay').removeAttr('disabled').val('确认发放分红');
                        tip.msgbox.err(result.message);
                        return;
                    }
                    payp();
                }
            });
        });
    }
    function payp() {
        $.ajax({
            url: "{php echo webUrl('abonus/bonus/payp')}",
            type: 'post',
            dataType: 'json',
            data: {
                id: "{$data['id']}", 'baid': window.aagents[window.current]
            },
            success: function (ret) {
                var result = ret.result;
                $('#aagentcount11').html(result.aagentcount11);
                $('#aagentcount22').html(result.aagentcount22);
                $('#aagentcount33').html(result.aagentcount33);
                $('#aagentcount44').html(result.aagentcount44);

                window.current++;
                if (window.current > window.aagents.length - 1) {
                    if (result.full) {
                        $('.checkitem,.checkall').removeAttr('disabled',true);
                        tip.alert('所有区域代理分红发送成功!', function () {
                            location.href = biz.url('abonus/bonus/status1');
                            return;
                        });
                    } else {

                        tip.alert('部分区域代理分红发送成功，请查看发放失败原因重新发放!', function () {
                            $('.checkitem,.checkall').removeAttr('disabled',true);
                            $('#btnpay').removeAttr('disabled').val('开始结算');
                            location.reload();
                        });
                    }
                    return;
                }
                payp();
            }
        });

    }

</script>


{template '_footer'}
