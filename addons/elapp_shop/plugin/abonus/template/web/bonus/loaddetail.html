<div class="form-group-title">分红统计</div>
<div class="form-group">
    <label class="col-sm-2 control-label">时间段</label>
    <div class="col-sm-9 col-xs-12">
        <div class="form-control-static"><span id="times">{php echo date('Y-m-d',$data['starttime'])} - {php echo date('Y-m-d',$data['endtime'])}</span></div>
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">订单数量</label>
    <div class="col-sm-9 col-xs-12">
        <div class="form-control-static"><span id="ordercount" style="color:red">{$data['ordercount']}</span> 个</div>
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">订单金额</label>
    <div class="col-sm-9 col-xs-12">
        <div class="form-control-static"><span id="ordermoney" style="color:red">{$data['ordermoney']}</span> 元</div>
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">代理商数量</label>
    <div class="col-sm-9 col-xs-12">
        <div class="form-control-static">
            <span class="label label-default">总数：{php echo $data['aagentcount1'] + $data['aagentcount2'] + $data['aagentcount3']} 个</span>
            <span class="label label-primary">省级：{$data['aagentcount1']} 个</span>
            <span class="label label-success">市级：{$data['aagentcount2']} 个</span>
            <span class="label label-warning">区级：{$data['aagentcount3']} 个</span>
        </div>
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">预计分红</label>
    <div class="col-sm-9 col-xs-12">
        <div class="form-control-static">
            <span class="label label-default">总额：{php echo $data['bonusmoney1'] + $data['bonusmoney2'] +$data['bonusmoney3']} 元</span>
            <span class="label label-primary">省级：{$data['bonusmoney1']} 元</span>
            <span class="label label-success">市级：{$data['bonusmoney2']} 元</span>
            <span class="label label-warning">区级：{$data['bonusmoney3']} 元</span>

        </div>
    </div>
</div>

<div class="form-group">
    <label class="col-sm-2 control-label">最终省级分红</label>
    <div class="col-sm-9 col-xs-12">
        <input type="text" id='bonusmoney1' name="bonusmoney1" class="form-control"  data-rule-required='true'
               {if $data['old']}
               value="{$data['bonusmoney_send1']}" disabled="true"
        {else}
        value="{$data['bonusmoney1']}"
        {/if}
        />
        <span class="help-block">如果您的最终省级分红和预计省级分红不一致，则实际给代理商的分红会按照预计分红及最终分红比例进行分红</span>
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">最终市级分红</label>
    <div class="col-sm-9 col-xs-12">
        <input type="text" id='bonusmoney2' name="bonusmoney2" class="form-control"  data-rule-required='true'
               {if $data['old']}
        value="{$data['bonusmoney_send2']}" disabled="true"
        {else}
        value="{$data['bonusmoney2']}"
        {/if}
        />
        <span class="help-block">如果您的最终市级分红和预计市级分红不一致，则实际给代理商的分红会按照预计分红及最终分红比例进行分红</span>
    </div>
</div>
<div class="form-group">
    <label class="col-sm-2 control-label">最终区级分红</label>
    <div class="col-sm-9 col-xs-12">
        <input type="text" id='bonusmoney3' name="bonusmoney3" class="form-control"  data-rule-required='true'
               {if $data['old']}
        value="{$data['bonusmoney_send3']}" disabled="true"
        {else}
        value="{$data['bonusmoney3']}"
        {/if}
        />
        <span class="help-block">如果您的最终区级分红和预计区级分红不一致，则实际给代理商的分红会按照预计分红及最终分红比例进行分红</span>
    </div>
</div>


{if !$data['old']}
<div class="form-group">
    <label class="col-sm-2 control-label"></label>
    <div class="col-sm-9 col-xs-12">
        <input type="button" id="btn" value="生成分红结算单" class="btn btn-primary" />
    </div>
</div>
{else}
<div class="form-group">
    <label class="col-sm-2 control-label"></label>
    <div class="col-sm-9 col-xs-12">
       <div class="form-control-static"><span style="color:red">此时间段已经生成了结算单，请到明细查看</span><br/>
       <a href="{php echo webUrl('abonus/bonus/detail',array('id'=>$data['billid']))}" class="btn btn-warning">立即查看</a>
       </div>
    </div>
</div>


{/if}
