{template '_header'}
<style>
    .select2{
        margin:0;
        width:100%;
        height:34px;
        border-radius: 3px;
        border-color: rgb(229, 230, 231);
    }
    .select2 .select2-choice{
        height: 34px;
        line-height: 32px;
        border-radius: 3px;
        border-color: rgb(229, 230, 231);
    }
    .select2 .select2-choice .select2-arrow{
        background: #fff;
    }
    .form-group .radio-inline{
        padding-top: 0px;;
    }
</style>
<div class="page-header">
    当前位置：<span class="text-primary">通知设置</span>
    <div class="pull-right">
            <strong>高级模式</strong>
            {ifp 'abonus.notice.edit'}
            <input class="js-switch small advanced" type="checkbox" {if !empty($data['tm']['is_advanced'])}checked{/if}/>
            {else}
            {if !empty($data['tm']['is_advanced'])}开启{else}关闭{/if}
            {/if}
    </div>
</div>
<div class="page-content">
    <form id="setform"  {ifp 'abonus.notice.edit'}action="" method="post"{/if} class="form-horizontal form-validate">
        <input type="hidden" value="{php echo intval($data['tm']['is_advanced'])}" name='data[is_advanced]' />
        {ifp 'abonus.notice.edit'}
        <div class='alert alert-warning' id="advanced_alert">
            <h3>注意：</h3>
            <p>请将公众平台模板消息所在行业选择为：<b> IT科技/互联网|电子商务 其他|其他</b>，所选行业不一致将会导致模板消息不可用。</p>
            <p>点击模板消息后方的开关按钮<img src="/static/application/shop/images/on-off.png"  onerror="this.src='/static/application/shop/images/nopic.png'"  />即可<b>开启模板消息</b>，无需进行额外设置。</p>
            <p>如需进行消息推送<b>个性化消息</b>，<a href="{php echo webUrl('sysset/tmessage/main')}" title="模板消息库">点击进入自定义消息库</a>{if $opensms}，<a href="{php echo webUrl('sysset/sms')}" title="短信消息库">点击进入短信消息库</a>{/if}</p>
        </div>
        <div class='alert alert-primary' id="normal_alert">
            默认为全部开启，用户在会员中心可自行设置是否开启, 模板消息自动替换变量
        </div>
        {/if}
        <div id="normal">
            <div class="form-group">
                <label class="col-lg control-label">任务处理通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[templateid]" class="form-control" value="{$data['tm']['templateid']}" />
                    <div class="help-block">公众平台模板消息编号: OPENTM200605630 </div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['templateid']}</div>
                    {/if}
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg control-label">成为区域代理通知</label>
                <div class="col-sm-9 col-xs-12">

                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[becometitle]" class="form-control" value="{$data['tm']['becometitle']}" />
                    <div class="help-block">标题，默认"成为区域代理通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['becometitle']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[become]" class="form-control" rows="5" >{$data['tm']['become']}</textarea>
                    模板变量: [昵称] [时间] [代理级别] [代理区域]
                    {else}
                    <div class='form-control-static'>{$data['tm']['become']}</div>
                    {/if}

                </div>
            </div>



            <div class="form-group">
                <label class="col-lg control-label">省级代理分红发放通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[paytitle1]" class="form-control" value="{$data['tm']['paytitle1']}" />
                    <div class="help-block">标题，默认"省级代理分红发放通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['paytitle1']}</div>
                    {/if}
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[pay1]" class="form-control"  rows="5">{$data['tm']['pay1']}</textarea>
                    模板变量 [昵称] [打款方式] [省级分红金额] [市级分红金额] [区级分红金额]  [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['pay1']}</div>
                    {/if}
                </div>
            </div>


            <div class="form-group">
                <label class="col-lg control-label">市级代理分红发放通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[paytitle2]" class="form-control" value="{$data['tm']['paytitle2']}" />
                    <div class="help-block">标题，默认"市级代理分红发放通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['paytitle2']}</div>
                    {/if}
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[pay2]" class="form-control"  rows="5">{$data['tm']['pay2']}</textarea>
                    模板变量 [昵称] [打款方式] [市级分红金额] [区级分红金额]  [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['pay2']}</div>
                    {/if}
                </div>
            </div>


            <div class="form-group">
                <label class="col-lg control-label">区级代理分红发放通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[paytitle3]" class="form-control" value="{$data['tm']['paytitle3']}" />
                    <div class="help-block">标题，默认"区级代理分红发放通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['paytitle3']}</div>
                    {/if}
                </div>
            </div>
            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[pay3]" class="form-control" rows="5" >{$data['tm']['pay3']}</textarea>
                    模板变量 [昵称] [打款方式] [区级分红金额]  [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['pay3']}</div>
                    {/if}
                </div>
            </div>




            <div class="form-group">
                <label class="col-lg control-label">省级代理等级升级通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[upgradetitle]" class="form-control" value="{$data['tm']['upgradetitle']}" />
                    <div class="help-block">标题，默认"省级代理等级升级通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgradetitle']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[upgrade1]" class="form-control"  rows="5">{$data['tm']['upgrade1']}</textarea>
                    模板变量: [昵称] [旧等级]  [旧省级分红比例] [旧市级分红比例] [旧区级分红比例]  [新等级] [新省级分红比例] [新市级分红比例] [新区级分红比例] [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgrade1']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label">市级代理等级升级通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[upgradetitle2]" class="form-control" value="{$data['tm']['upgradetitle2']}" />
                    <div class="help-block">标题，默认"市级代理等级升级通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgradetitle2']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[upgrade2]" class="form-control"  rows="5">{$data['tm']['upgrade2']}</textarea>
                    模板变量: [昵称] [旧等级]  [旧市级分红比例] [旧区级分红比例]  [新等级] [新市级分红比例] [新区级分红比例] [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgrade2']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label">区级代理等级升级通知</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <input type="text" name="data[upgradetitle3]" class="form-control" value="{$data['tm']['upgradetitle3']}" />
                    <div class="help-block">标题，默认"区级代理等级升级通知"</div>
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgradetitle3']}</div>
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.notice.edit'}
                    <textarea  name="data[upgrade3]" class="form-control"  rows="5">{$data['tm']['upgrade3']}</textarea>
                    模板变量: [昵称] [旧等级]  [旧区级分红比例]  [新等级] [新区级分红比例] [时间]
                    {else}
                    <div class='form-control-static'>{$data['tm']['upgrade3']}</div>
                    {/if}
                </div>
            </div>

        </div>
        <div id="advanced">
            <table class="table table-responsive">
                <thead style="background: #ededed">
                <th>买家通知-成为区域代理</th>
                <th class="w200">模板消息</th>
                <th class="w60 is_advanced"><!--<input class="js-switch small checkall" data-type="tpl-advanced" type="checkbox" />--></th>
                </thead>
                <tbody>
                    <tr>
                        <td>成为区域代理通知</td>
                        <td>
                            <select class="select2" {ifp 'abonus.notice.edit'}name="data[abonus_become_advanced]"{else}disabled{/if}>
                            <option value=''>[默认]成为区域代理通知</option>
                            {loop $template_list['abonus_become'] $template_val}
                            <option value="{$template_val['id']}" {if $data['tm']['abonus_become_advanced'] == $template_val['id'] }selected{/if}>{$template_val['title']}</option>
                            {/loop}
                            </select>
                        </td>
                        <td style="text-align: right;" class="is_advanced">
                            <label class="notice-default">
                                <input type="hidden" name="data[abonus_become_close_advanced]" value="{php echo intval($data['tm']['abonus_become_close_advanced'])}" />
                                <input class="js-switch small checkone" data-type="tpl-advanced"   data-tag="abonus_become" type="checkbox" value="{php echo intval($data['tm']['abonus_become_close_advanced'])}" {if empty($data['tm']['abonus_become_close_advanced'])}checked{/if}/>
                            </label>
                            <label style="display: none;">
                                <img src="/static/application/shop/images/loading.gif" width="20" alt="" onerror="this.src='/static/application/shop/images/nopic.png'" />
                            </label>
                        </td>
                    </tr>
                </tbody>
            </table>

            <table class="table table-responsive">
                <thead style="background: #ededed">
                <th>买家通知-区域等级升级</th>
                <th class="w200">模板消息</th>
                <th class="w60 is_advanced"><!--<input class="js-switch small checkall" data-type="tpl-advanced" type="checkbox" />--></th>
                </thead>
                <tbody>
                <tr>
                    <td>省级代理等级升级通知</td>
                    <td>
                        <select class="select2" {ifp 'abonus.notice.edit'}name="data[abonus_upgrade1_advanced]"{else}disabled{/if}>
                        <option value=''>[默认]省级代理等级升级通知</option>
                        {loop $template_list['abonus_upgrade1'] $template_val}
                        <option value="{$template_val['id']}" {if $data['tm']['abonus_upgrade1_advanced'] == $template_val['id'] }selected{/if}>{$template_val['title']}</option>
                        {/loop}
                        </select>
                    </td>
                    <td style="text-align: right;" class="is_advanced">
                        <label class="notice-default">
                            <input type="hidden" name="data[abonus_upgrade1_close_advanced]" value="{php echo intval($data['tm']['abonus_upgrade1_close_advanced'])}" />
                            <input class="js-switch small checkone" data-type="tpl-advanced"   data-tag="abonus_upgrade1" type="checkbox" value="{php echo intval($data['tm']['abonus_upgrade1_close_advanced'])}" {if empty($data['tm']['abonus_upgrade1_close_advanced'])}checked{/if}/>
                        </label>
                        <label style="display: none;">
                            <img src="/static/application/shop/images/loading.gif" width="20" alt="" onerror="this.src='/static/application/shop/images/nopic.png'" />
                        </label>
                    </td>
                </tr>
                <tr>
                    <td>市级代理等级升级通知</td>
                    <td>
                        <select class="select2" {ifp 'abonus.notice.edit'}name="data[abonus_upgrade2_advanced]"{else}disabled{/if}>
                        <option value=''>[默认]市级代理等级升级通知</option>
                        {loop $template_list['abonus_upgrade2'] $template_val}
                        <option value="{$template_val['id']}" {if $data['tm']['abonus_upgrade2_advanced'] == $template_val['id'] }selected{/if}>{$template_val['title']}</option>
                        {/loop}
                        </select>
                    </td>
                    <td style="text-align: right;" class="is_advanced">
                        <label class="notice-default">
                            <input type="hidden" name="data[abonus_upgrade2_close_advanced]" value="{php echo intval($data['tm']['abonus_upgrade2_close_advanced'])}" />
                            <input class="js-switch small checkone" data-type="tpl-advanced"   data-tag="abonus_upgrade2" type="checkbox" value="{php echo intval($data['tm']['abonus_upgrade2_close_advanced'])}" {if empty($data['tm']['abonus_upgrade2_close_advanced'])}checked{/if}/>
                        </label>
                        <label style="display: none;">
                            <img src="/static/application/shop/images/loading.gif" width="20" alt="" onerror="this.src='/static/application/shop/images/nopic.png'" />
                        </label>
                    </td>
                </tr>
                <tr>
                    <td>区级代理等级升级通知</td>
                    <td>
                        <select class="select2" {ifp 'abonus.notice.edit'}name="data[abonus_upgrade3_advanced]"{else}disabled{/if}>
                        <option value=''>[默认]区级代理等级升级通知</option>
                        {loop $template_list['abonus_upgrade3'] $template_val}
                        <option value="{$template_val['id']}" {if $data['tm']['abonus_upgrade3_advanced'] == $template_val['id'] }selected{/if}>{$template_val['title']}</option>
                        {/loop}
                        </select>
                    </td>
                    <td style="text-align: right;" class="is_advanced">
                        <label class="notice-default">
                            <input type="hidden" name="data[abonus_upgrade3_close_advanced]" value="{php echo intval($data['tm']['abonus_upgrade3_close_advanced'])}" />
                            <input class="js-switch small checkone" data-type="tpl-advanced"   data-tag="abonus_upgrade3" type="checkbox" value="{php echo intval($data['tm']['abonus_upgrade3_close_advanced'])}" {if empty($data['tm']['abonus_upgrade3_close_advanced'])}checked{/if}/>
                        </label>
                        <label style="display: none;">
                            <img src="/static/application/shop/images/loading.gif" width="20" alt="" onerror="this.src='/static/application/shop/images/nopic.png'" />
                        </label>
                    </td>
                </tr>
                </tbody>
            </table>

            <table class="table table-responsive">
                <thead style="background: #ededed">
                <th>买家通知-区域代理分红</th>
                <th class="w200">模板消息</th>
                <th class="w60 is_advanced"><!--<input class="js-switch small checkall" data-type="tpl-advanced" type="checkbox" />--></th>
                </thead>
                <tbody>
                <tr>
                    <td>区域代理分红发放通知</td>
                    <td>
                        <select class="select2" {ifp 'abonus.notice.edit'}name="data[abonus_pay_advanced]"{else}disabled{/if}>
                        <option value=''>[默认]区域代理分红发放通知</option>
                        {loop $template_list['abonus_pay_advanced'] $template_val}
                        <option value="{$template_val['id']}" {if $data['tm']['abonus_pay_advanced'] == $template_val['id'] }selected{/if}>{$template_val['title']}</option>
                        {/loop}
                        </select>
                    </td>
                    <td style="text-align: right;" class="is_advanced">
                        <label class="notice-default">
                            <input type="hidden" name="data[abonus_pay_close_advanced]" value="{php echo intval($data['tm']['abonus_pay_close_advanced'])}" />
                            <input class="js-switch small checkone" data-type="tpl-advanced"   data-tag="abonus_pay" type="checkbox" value="{php echo intval($data['tm']['abonus_pay_close_advanced'])}" {if empty($data['tm']['abonus_pay_close_advanced'])}checked{/if}/>
                        </label>
                        <label style="display: none;">
                            <img src="/static/application/shop/images/loading.gif" width="20" alt="" onerror="this.src='/static/application/shop/images/nopic.png'" />
                        </label>
                    </td>
                </tr>
                </tbody>
            </table>
        </div>
    <div class="form-group splitter"></div>
    {ifp 'abonus.notice.edit'}
    <div class="form-group">
        <div class="col-sm-12 col-xs-12">
            <input type="submit" value="提交" class="btn btn-primary" />
        </div>
    </div>
    {/if}
    </form>
</div>
<script>
    $(function () {

        $(".advanced").click(function(){
            $(":input[name='data[is_advanced]']").val( this.checked ?1:0);
            var next = $(this).next();
            if(next.hasClass('checked')){
                $("#advanced,#advanced_alert").show();
                $("#normal,#normal_alert").hide();
            }else{
                $("#advanced,#advanced_alert").hide();
                $("#normal,#normal_alert").show();
            }
        });

        $(".js-switch").not(".checkhi").click(function () {
            var next = $(this).next();
            if(next.hasClass('checked')){
                $(this).val("1").prev().val("0");
            }else{
                $(this).val("0").prev().val("1");
            }
        });

        if($(":input[name='data[is_advanced]']").val() == 1)
        {
            $("#advanced,#advanced_alert").show();
            $("#normal,#normal_alert").hide();
        }
        else
        {
            $("#advanced,#advanced_alert").hide();
            $("#normal,#normal_alert").show();
        }

        //开启通知
        $(".checkone").click(function () {
            var _this =$(this);
            var type = _this.data('type');
            var val = _this.val();

            var tag = _this.data('tag');
            var stop = _this.data('stop');

            if(stop==1)
            {
                return;
            }

            //判断是否开启模板通知
            if(tag != '' && val==1&&type=='tpl-advanced') {
                $(this).data('stop', 1);
                $(this).parent().hide().next().show();

                var data = {
                    'tag': tag,
                    checked:val
                };
                //申请微信模板,并将模板ID更新至数据库.
                $.ajax({
                    url: "{php echo webUrl('sysset/settemplateid')}",
                    type:'get',
                    dataType:'json',
                    timeout : 3000, //超时时间设置，单位毫秒
                    data:data,
                    success:function(ret){
                        var _this = $(".checkone[data-tag='"+ret.result.tag+"']");
                        if (ret.result.status == '0') {
                            this.value=0;
                            _this.prev().val(1);
                            _this.next().removeClass('checked');

                            console.log(ret.result.messages);
                            alert(ret.result.messages);
                        }

                        $(_this).data('stop', 0);
                        $(_this).parent().show().next().hide();
                    },
                    error: function(XMLHttpRequest, textStatus, errorThrown) {
                        $(".table").each(function () {
                            var _this = $(this);
                            _this.find(".checkone[data-type='tpl-advanced']").each(function () {
                                $(this).data('stop', 0);
                                $(this).parent().show().next().hide();
                            });
                        });
                    }
                });
            }


            var type = $(this).data('type');
            var val = $(this).val();
            if(val==0){
                $(this).attr("checked","false").val("1").next().removeClass("checked");
                $(this).closest(".table").find(".checkall[data-type='"+type+"']").val("1").attr("checked","false").next().removeClass("checked");
            }else{
                $(this).attr("checked","true").val("0").next().addClass("checked");
                var all = true;
                $(this).closest(".table").find(".checkone[data-type='"+type+"']").each(function () {
                    var val = $(this).val();
                    if(val!='on' && val==1){
                        all = false;
                        return;
                    }
                });
                if(all){
                    $(this).closest(".table").find(".checkall[data-type='"+type+"']").val("0").attr("checked","true").next().addClass("checked");
                }
            }

        });

        $(".table").each(function () {
            var _this = $(this);
            var all_tpl_normal = true;
            var all_tpl_advanced = true;
            var all_sms = true;
            _this.find(".checkone[data-type='tpl-advanced']").each(function () {
                var val = $(this).val();
                if(val!='on' && val==1){
                    all_tpl_advanced = false;
                    return;
                }
            });
            _this.find(".checkone[data-type='sms']").each(function () {
                var val = $(this).val();
                if(val!='on' && val==1){
                    all_sms = false;
                    return;
                }
            });
            if(all_tpl_normal){
                _this.find(".checkall[data-type='tpl-normal']").val("0").attr("checked","true").next().addClass("checked");
            }
            if(all_tpl_advanced){
                _this.find(".checkall[data-type='tpl-advanced']").val("0").attr("checked","true").next().addClass("checked");
            }
            if(all_sms){
                _this.find(".checkall[data-type='sms']").val("0").attr("checked","true").next().addClass("checked");
            }
        });
    })
</script>
{template '_footer'}