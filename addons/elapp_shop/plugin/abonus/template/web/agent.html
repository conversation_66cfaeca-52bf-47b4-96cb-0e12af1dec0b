{template '_header'}
<style>
    .popover{
        width:170px;
        font-size:12px;
        line-height: 21px;
        color: #0d0706;
    }
    .popover span{
        color: #b9b9b9;
    }
    .nickname{
        display: inline-block;
        max-width:200px;
        overflow : hidden;
        text-overflow: ellipsis;
        display: -webkit-box;
        -webkit-line-clamp: 1;
        -webkit-box-orient: vertical;
    }
    .tooltip-inner{
        border:none;
    }
    .page-content{
        overflow: visible;
    }
</style>
<div class="page-header"> 当前位置：<span class="text-primary">代理商管理</span> </div>
<div class="page-content">
   <form action="" method="get" class="form-horizontal" role="form" id="form1">
<div class="page-toolbar  m-b-sm m-t-sm">
                            <div class="col-sm-4">

                                {php echo tpl_daterange('time', array('sm'=>true, 'placeholder'=>'成为区域代理时间'),true);}
                               </div>
			 
                            <div class="col-sm-8 pull-right">
                                <div class="input-group">
                                        <div class="input-group-select">
                                            <select name='aagentstatus' class='form-control  input-sm select-md' style="width:100px;"  >
                                                <option value=''>状态</option>
                                                <option value='0' {if $_GPC['aagentstatus']=='0'}selected{/if}>未审核</option>
                                                <option value='1' {if $_GPC['aagentstatus']=='1'}selected{/if}>已审核</option>
                                            </select>
                                        </div>
                                    <div class="input-group-select">
                                        <select name='followed' class='form-control  input-sm select-md' style="width:140px">
                                            <option value=''>关注</option>
                                            <option value='0' {if $_GPC['followed']=='0'}selected{/if}>未关注</option>
                                            <option value='1' {if $_GPC['followed']=='1'}selected{/if}>已关注</option>
                                            <option value='2' {if $_GPC['followed']=='2'}selected{/if}>取消关注</option>
                                        </select>
                                    </div>
                                    <div class="input-group-select">
                                        <select name='aagenttype' class='form-control  input-sm select-md' style="width:140px;"  >
                                            <option value=''>类型</option>
                                            <option value='1' {if $_GPC['aagenttype']==1}selected{/if}>省级代理</option>
                                            <option value='2' {if $_GPC['aagenttype']==2}selected{/if}>市级代理</option>
                                            <option value='3' {if $_GPC['aagenttype']==3}selected{/if}>区级代理</option>
                                        </select>
                                    </div>
                                    <div class="input-group-select">
                                        <select name='aagentblack'  class='form-control  input-sm select-md'   style="width:140px;"  >
                                            <option value=''>黑名单</option>
                                            <option value='0' {if $_GPC['aagentblack']=='0'}selected{/if}>否</option>
                                            <option value='1' {if $_GPC['aagentblack']=='1'}selected{/if}>是</option>
                                        </select>
                                    </div>
                                          <input type="text" class="form-control input-sm"  name="keyword" value="{$_GPC['keyword']}" placeholder="区域代理昵称/姓名/手机号"/>
                                         <span class="input-group-btn">

                                                                <button class="btn btn-primary" type="submit"> 搜索</button>
                                                                                                                {ifp 'abonus.agent.export'}
                                                <button type="submit" name="export" value="1" class="btn btn-success">导出</button>
                                                {/if}
                                        </span>
                                </div>
								
                            </div>
</div>
  </form>
{if count($list)>0}
        <div class="page-table-header">
            <input type="checkbox">
           <div class="btn-group">
               {ifp 'abonus.agent.edit'}
               <a class='btn btn-default btn-operation' data-toggle='batch' data-href="{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>1))}" data-confirm='确认要设置黑名单?'>
                   <i class="icow icow-heimingdan2"></i>  设置黑名单</a>
               <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>0))}" data-confirm='确认要取消黑名单?'>
                   <i class="icow icow-yongxinyonghu"></i> 取消黑名单</a>
               <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/check',array('status'=>1))}"  data-confirm='确认要审核通过?' disabled>
                   <i class="icow icow-shenhetongguo"></i>审核通过</a>
               <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/check',array('status'=>0))}" data-confirm='确认要取消审核?' disabled>
                   <i class="icow icow-yiquxiao"></i> 取消审核</a>
               {/if}
               {ifp 'abonus.agent.delete'}
               <a class="btn btn-default btn-operation" type="button" data-toggle='batch-remove' data-confirm="确认要删除?" data-href="{php echo webUrl('abonus/agent/delete')}">
                   <i class='icow icow-shanchu1'></i> 删除</a>
               {/if}
           </div>
        </div>
        <table class="table table-hover table-responsive">
            <thead class="navbar-inner" >
            <tr>
                 <th style="width:45px;"></th>
                <th style="width: 250px;">粉丝</th>
                <th >姓名<br/>手机号码</th>
                <th>等级</th>
                <th >累计分红</th>
                <th >注册时间</br>审核时间</th>
                <!--<th style='width:90px;'></th>-->
                <th style='width:90px;'>状态</th>
                <th style='width:150px;'>操作</th>
            </tr>
            </thead>
            <tbody>
            {loop $list $row}
           <tr>
               <td>
                        <input type='checkbox'   value="{$row['id']}"/>
                </td>
               
                <td style="overflow: visible">
                    <div  rel="pop" style="display: flex"  data-content="
                    <span>ID：</span>{$row['id']}</br>
                   <span>推荐人：</span>
                    {if empty($row['agentid'])}
				        {if $row['isagent']==1}
				        总店
				         {else}
				       暂无
				         {/if}
				    {else}
                    	{if !empty($row['parentavatar'])}
                         <img src='{$row['parentavatar']}' style='width:20px;height:20px;padding1px;border:1px solid #ccc' onerror='this.src='/static/application/shop/images/nopic.png''/>
                         {/if}
                        [{$row['agentid']}]{if empty($row['parentname'])}未更新{else}{$row['parentname']}{/if}
					   {/if}</br>
                        <span> 是否关注：</span>
                         {if empty($row['followed'])}
                    {if empty($row['uid'])}
                    <span class='text-default'>未关注</span>
                    {else}
                    <span class='text-default'>取消关注</span>
                    {/if}
                    {else}
                    <span class='text-default'>已关注</span>
                    {/if}
					   ">

                        {ifp 'member.list.view'}
                        <a href="{php echo webUrl('member/list/detail',array('id' => $row['id']));}" title='会员信息' target='_blank' style="display: flex;">
                            {if !empty($row['avatar'])}
                            <img class="radius50" src='{$row['avatar']}' style='width:36px;height:36px;padding1px;border:1px solid #ccc;vertical-align: middle' onerror="this.src='/static/application/shop/images/noface.png'" />
                            {/if}
                       <span style="display: flex;flex-direction: column;justify-content: center;align-items: flex-start;padding-left: 5px">
                           <span class="nickname">
                                {if $row['aagenttype']==1}
                                <span class="label label-primary">省级</span>
                                {elseif $row['aagenttype']==2}
                                <span class="label label-success">市级</span>
                                {elseif $row['aagenttype']==3}
                                <span class="label label-warning">区级</span>
                                {/if}
                               {if empty($row['nickname'])}未更新{else}{$row['nickname']}{/if}
                           </span>
                         {if $row['aagentblack']==1}<span class="text-danger">黑名单<i class="icow icow-heimingdan1"style="color: #db2228;margin-left: 2px;font-size: 13px;"></i></span>{/if}
                       </span>
                        </a>
                        {else}
                        {if !empty($row['avatar'])}
                        <img src='{$row['avatar']}' style='width:30px;height:30px;padding1px;border:1px solid #ccc' />
                        {/if}
                       <span style="display: flex;flex-direction: column;justify-content: center;align-items: flex-start;padding-left: 5px">
                           <span class="nickname">
                               {if empty($row['nickname'])}未更新{else}{$row['nickname']}{/if}
                           </span>
                         {if $row['aagentblack']==1}<span class="text-danger">黑名单<i class="icow icow-heimingdan1"style="color: #db2228;margin-left: 2px;font-size: 13px;"></i></span>{/if}
                       </span>
                        {/if}

                    </div>
                </td>

                <td>{$row['realname']} <br/> {$row['mobile']}</td>
                <td>{if empty($row['levelname'])} {php echo empty($this->set['levelname'])?'普通等级':$this->set['levelname']}{else}{$row['levelname']}{/if}</td>
             
                <td class="text-warning">{$row['bonus']}</td>
                       <td>{php echo date('Y-m-d',$row['createtime'])} {php echo date('H:i',$row['createtime'])}
                           <br/>
                           {if !empty($row['aagenttime'])}
                           {php echo date('Y-m-d',$row['aagenttime'])} {php echo date('H:i',$row['aagenttime'])}
                           {else}
                           -
                           {/if}
                       </td>
                <td>
             
                   
                    <span class='label {if $row['aagentstatus']==1}label-primary{else}label-default{/if}'
										  {ifp 'abonus.agent.check'}
										  data-toggle='ajaxSwitch' 
                                          data-confirm ='确认要{if $row['aagentstatus']==1}取消审核{else}审核通过{/if}?'
										  data-switch-value='{$row['aagentstatus']}'
										  data-switch-value0='0|未审核|label label-default|{php echo webUrl('abonus/agent/check',array('status'=>1,'id'=>$row['id']))}'
										  data-switch-value1='1|已审核|label label-primary|{php echo webUrl('abonus/agent/check',array('status'=>0,'id'=>$row['id']))}'
										  {/if}
										>
										  {if $row['aagentstatus']==1}已审核{else}未审核{/if}</span>
                    <br/>
                    
                    
                       <!--<span class='label {if $row['aagentblack']==0}label-success{else}label-default{/if}' -->
										  <!--{ifp 'abonus.agent.aagentblack'}-->
										  <!--data-toggle='ajaxSwitch' -->
                                                                                       <!--data-confirm ='确认要{if $row['aagentblack']==1}取消黑名单{else}设置黑名单{/if}?'-->
										  <!--data-switch-value='{$row['aagentblack']}'-->
										  <!--data-switch-value0='0|正常|label label-success|{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>1,'id'=>$row['id']))}'-->
										  <!--data-switch-value1='1|黑名单|label label-default|{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>0,'id'=>$row['id']))}'-->
										  <!--{/if}-->
										<!--&gt;-->
										  <!--{if $row['aagentblack']==1}黑名单{else}正常{/if}</span>-->
                     </td>
             
        
                <td  style="overflow:visible;">

                    <div class="btn-group ">
                            {ifp 'order.list'}
                            <a class="btn  btn-op btn-operation" href="{php echo webUrl('order/list/main',array('agentid' => $row['id']));}"  target='_blank'>
                                        <span data-toggle="tooltip" data-placement="top" title="" data-original-title="推广订单">
                                            <i class='icow icow-tuiguang'></i>
                                        </span>
                            </a>
                            {/if}
                            {ifp 'commission.agent.user'}
                                    <a class="btn  btn-op btn-operation" href="{php echo webUrl('commission/agent/user',array('id' => $row['id']));}"   target='_blank'>
                                         <span data-toggle="tooltip" data-placement="top" title="" data-original-title="推广下级">
                                           <i class='icow icow-cengjiguanli'></i>
                                        </span>
                                    </a>
                            {/if}
                            {ifp 'order'}
                                <a class="btn  btn-op btn-operation" href="{php echo webUrl('order/list/main', array('searchfield'=>'member', 'keyword'=>$row['nickname']))}"  target='_blank'>
                                             <span data-toggle="tooltip" data-placement="top" title="" data-original-title="会员订单">
                                                <i class='icow icow-dingdan2'></i>
                                            </span>
                                </a>
                            {/if}
                            {ifp 'finance.recharge.credit1|finance.recharge.credit2'}
                                <a class="btn  btn-op btn-operation" data-toggle="ajaxModal" href="{php echo webUrl('finance/recharge/main', array('type'=>'credit1','id'=>$row['id']))}" >
                                            <span data-toggle="tooltip" data-placement="top" title="" data-original-title="充值">
                                               <i class='icow icow-31'></i>
                                            </span>
                                </a>
                            {/if}
                            {ifp 'abonus.agent.delete'}
                            <a class="btn  btn-op btn-operation" data-toggle='ajaxRemove' href="{php echo webUrl('abonus/agent/delete',array('id' => $row['id']));}" data-confirm="确定要删除该区域代理吗？">
                                        <span data-toggle="tooltip" data-placement="top" title="" data-original-title="删除区域代理">
                                               <i class='icow icow-shanchu1'></i>
                                            </span>
                            </a>
                            {/if}
                    </div>


                </td>
            </tr>
            {/loop}
            </tbody>
            <tfoot>
                <tr>
                    <td><input type="checkbox"></td>
                    <td colspan="4">
                        <div class="btn-group">
                            {ifp 'abonus.agent.edit'}
                            <a class='btn btn-default btn-operation' data-toggle='batch' data-href="{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>1))}" data-confirm='确认要设置黑名单?'>
                                <i class="icow icow-heimingdan2"></i>  设置黑名单</a>
                            <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/aagentblack',array('aagentblack'=>0))}" data-confirm='确认要取消黑名单?'>
                                <i class="icow icow-yongxinyonghu"></i> 取消黑名单</a>
                            <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/check',array('status'=>1))}"  data-confirm='确认要审核通过?' disabled>
                                <i class="icow icow-shenhetongguo"></i>审核通过</a>
                            <a class='btn btn-default btn-operation'  data-toggle='batch' data-href="{php echo webUrl('abonus/agent/check',array('status'=>0))}" data-confirm='确认要取消审核?' disabled>
                                <i class="icow icow-yiquxiao"></i> 取消审核</a>
                            {/if}
                            {ifp 'abonus.agent.delete'}
                            <a class="btn btn-default btn-operation" type="button" data-toggle='batch-remove' data-confirm="确认要删除?" data-href="{php echo webUrl('abonus/agent/delete')}">
                                <i class='icow icow-shanchu1'></i> 删除</a>
                            {/if}
                        </div>
                    </td>
                    <td colspan="3" class="text-right"> {$pager}</td>
                </tr>
            </tfoot>
        </table>

        
                {else}
<div class='panel panel-default'>
	<div class='panel-body' style='text-align: center;padding:30px;'>
		 暂时没有任何区域代理!
	</div>
</div>
{/if}
</div>
    <script language="javascript">
			require(['bootstrap'],function(){
        $("[rel=pop]").popover({
            trigger:'manual',
            placement : 'right',
            title : $(this).data('title'),
            html: 'true', 
            content : $(this).data('content'),
            animation: false
        }).on("mouseenter", function () {
                    var _this = this;
                    $(this).popover("show"); 
                    $(this).siblings(".popover").on("mouseleave", function () {
                        $(_this).popover('hide');
                    });
                }).on("mouseleave", function () {
                    var _this = this;
                    setTimeout(function () {
                        if (!$(".popover:hover").length) {
                            $(_this).popover("hide")
                        }
                    }, 100);
                });
 
	 
	   });
 
			   
</script> 
 
{template '_footer'}
