 <div class="form-group">
                <label class="col-lg control-label">会员中心区域代理入口</label>
                <div class="col-sm-9 col-xs-12">
                	{ifp 'abonus.set.edit'}
                    <label class="radio-inline"><input type="radio"  name="data[openmembercenter]" value="1" {if $data['openmembercenter'] ==1} checked="checked"{/if} /> 显示</label>
                	<label class="radio-inline"><input type="radio"  name="data[openmembercenter]" value="0" {if empty($data['openmembercenter'])} checked="checked"{/if} /> 关闭</label>
                    <span class="help-block">会员中心是否显示区域代理中心入口</span>
                    {else}
                    	{if empty($data['openorderdetail'])}关闭{else}显示{/if}
                    {/if}

                </div> 
           </div>
	   <div class="form-group">
                <label class="col-lg control-label">分销中心区域代理入口</label>
                <div class="col-sm-9 col-xs-12">
                	{ifp 'abonus.set.edit'}
                	<label class="radio-inline"><input type="radio"  name="data[closecommissioncenter]" value="0" {if empty($data['closecommissioncenter'])} checked="checked"{/if} /> 显示</label>
                    <label class="radio-inline"><input type="radio"  name="data[closecommissioncenter]" value="1" {if $data['closecommissioncenter'] ==1} checked="checked"{/if} /> 关闭</label>
                    <span class="help-block">分销中心是否显示区域代理中心入口</span>
                    {else}
                    	{if !empty($data['closecommissioncenter'])}关闭{else}显示{/if}
                    {/if}

                </div> 
           </div>

 <div class="form-group">
     <label class="col-lg control-label">区域代理须知</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
           <textarea class="form-control" name="data[centerdesc]" rows="5">{$data['centerdesc']}</textarea>
         <span class="help-block">在区域代理中心显示</span>
         {else}
            {$data['centerdesc']}
         {/if}
     </div>
 </div>


 <div class="form-group">
     <label class="col-lg control-label">申请说明</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <label class="radio-inline"><input type="radio"  name="data[register_bottom]" value="0" {if empty($data['register_bottom'])} checked="checked"{/if} /> 默认</label>
         <label class="radio-inline"><input type="radio"  name="data[register_bottom]" value="1" {if $data['register_bottom'] ==1} checked="checked"{/if} /> 模式1(标题和内容替换)</label>
         <label class="radio-inline"><input type="radio"  name="data[register_bottom]" value="2" {if $data['register_bottom'] ==2} checked="checked"{/if} /> 模式2(整体替换)</label>
         {else}
         {if empty($data['register_bottom'])}否{else}是{/if}
         {/if}
         <span class="help-block"></span>
     </div>
 </div>

 <div class="r-group12" {if empty($data['register_bottom'])}style="display: none"{/if}>
 <div class="col-sm-5">
     <img src="/static/application/shop/plugin/abonus/static/images/register_example.jpg" height="100%" width="100%"/>
 </div>
 </div>


 <div class="col-sm-7 r-group1" {if $data['register_bottom']!=1}style="display: none"{/if}>

 <div class="form-group">
     <label class="col-lg control-label"></label>
     <div class="col-sm-9 col-xs-12">
         图中的小图标不可替换
     </div>
 </div>

 <div class="form-group">
     <label class="col-lg control-label">标题1</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <input type='text' class='form-control' name='data[register_bottom_title1]' value="{$data['register_bottom_title1']}" />
         {else}
         {$data['register_bottom_title1']}
         {/if}
     </div>
 </div>

 <div class="form-group">
     <label class="col-lg control-label">内容1</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <textarea class='form-control' name="data[register_bottom_content1]" rows="3">{$data['register_bottom_content1']}</textarea>
         {else}
         {$data['register_bottom_content1']}
         {/if}
     </div>
 </div>

 <div class="form-group">
     <label class="col-lg control-label">标题2</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <input type='text' class='form-control' name='data[register_bottom_title2]' value="{$data['register_bottom_title2']}" />
         {else}
         {$data['register_bottom_title2']}
         {/if}
     </div>
 </div>

 <div class="form-group">
     <label class="col-lg control-label">内容2</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <textarea class='form-control' name="data[register_bottom_content2]" rows="3">{$data['register_bottom_content2']}</textarea>
         {else}
         {$data['register_bottom_content2']}
         {/if}
     </div>
 </div>

 <div class="form-group">
     <label class="col-lg control-label">说明</label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         <textarea class='form-control' name="data[register_bottom_remark]" rows="6">{$data['register_bottom_remark']}</textarea>
         {else}
         {$data['register_bottom_remark ']}
         {/if}
     </div>
 </div>
 </div>

 <div class="col-sm-7 r-group2" {if $data['register_bottom']!=2}style="display: none"{/if}>
 <div class="form-group">
     <label class="col-lg control-label"></label>
     <div class="col-sm-9 col-xs-12">
         {ifp 'abonus.set.edit'}
         {php echo tpl_ueditor('data[register_bottom_content]',$data['register_bottom_content'],array('height'=>200))}
         {else}
         <textarea id='register_bottom_content' style='display:none'>{$data['register_bottom_content']}</textarea>
         <a href='javascript:preview_html("#register_bottom_content")' class="btn btn-default">查看内容</a>
         {/if}
     </div>
 </div>
 </div>

 <script>
     $(function () {
         $(":radio[name='data[register_bottom]']").on('click',function (e) {
             var $this = $(this);

             if($this.val()==0){
                 $(".r-group12").hide();
                 $(".r-group1").hide();
                 $(".r-group2").hide();
             } else if($this.val()==1){
                 $(".r-group12").show();
                 $(".r-group1").show();
                 $(".r-group2").hide();
             } else if($this.val()==2){
                 $(".r-group12").show();
                 $(".r-group1").hide();
                 $(".r-group2").show();
             }
         })
     });
 </script>
