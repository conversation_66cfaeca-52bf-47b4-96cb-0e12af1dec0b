<div class="form-group">
    <label class="col-lg control-label">结算周期</label>
    <div class="col-sm-9 col-xs-12">
        {ifp 'abonus.set.edit'}
        <label class="radio radio-inline" >
            <input type="radio" name="data[paytype]" value="1" {if $data['paytype']==1}checked{/if}/> 按月发放
        </label>
        <label class="radio radio-inline">
            <input type="radio" name="data[paytype]" value="2" {if $data['paytype']==2}checked{/if}/> 按周发放
        </label>

        <span class="help-block">分红结算周期</span>
        {else}
        {if $data['paytype']==1 || empty($data['paytype'])}按月结算{/if}
        {if $data['paytype']==2}按周结算{/if}
        {/if}
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label">结算形式</label>
    <div class="col-sm-9 col-xs-12">
        {ifp 'abonus.set.edit'}
        <label class="radio radio-inline" >
            <input type="radio" name="data[moneytype]" value="0" {if empty($data['moneytype'])}checked{/if}/> 余额
        </label>
        <label class="radio radio-inline">
            <input type="radio" name="data[moneytype]" value="1" {if $data['moneytype']==1}checked{/if}/> 微信钱包
        </label>

        <span class="help-block">结算打款形式, 如果选择了微信钱包形式，某个区域代理结算金额不足1元，会自动结算到该区域代理的商城余额</span>
        {else}
        {if  empty($data['moneytype'])}余额{/if}
        {if $data['moneytype']==1}微信钱包{/if}
        {/if}
    </div>
</div>

<div class="form-group">
    <label class="col-lg control-label">订单结算天数</label>
    <div class="col-sm-9 col-xs-12 fixsingle-input-group">
        {ifp 'abonus.set.edit'}
        <input type="text" name="data[settledays]" class="form-control" value="{$data['settledays']}"  />
        <span class="help-block">当订单完成后的n天后才纳入到分红结算, 设置空或0则收货就进行结算</span>
        {else}
        {$data['settledays']}
        {/if}
    </div>
</div>

<div class="form-group">
    <label class="col-lg control-label">分红提现手续费</label>
    <div class="col-sm-9 col-xs-12">
        {ifp 'abonus.set.edit'}
        <div class="input-group fixsingle-input-group">
            <input type="text" name="data[paycharge]" class="form-control" value="{$data['paycharge']}" />
            <div class="input-group-addon">%</div>
        </div>
        <span class="help-block">发放分红时,扣除的提现手续费.空为不扣除提现手续费</span>
        {else}
        {php echo empty($data['paycharge'])?"0":$data['paycharge']}%
        {/if}
    </div>
</div>

<div class="form-group">
    <label class="col-lg control-label">免提现手续费金额区间</label>
    <div class="col-sm-9 col-xs-12">
        {ifp 'sysset.trade.edit'}
        <div class='input-group fixmore-input-group'>
            <span class='input-group-addon'>开始金额￥</span>
            <input type="text" name="data[paybegin]" class="form-control" value="{$data['paybegin']}" />
            <span class='input-group-addon'>结束金额￥</span>
            <input type="text" name="data[payend]" class="form-control" value="{$data['payend']}" />
        </div>
        <span class='help-block'>当提现手续费金额在此区间内时,不扣除提现手续费. 结束金额 必须大于 开始金额才能生效</span>
        <span class='help-block'>例如 设置开始金额0元 结束金额5元,只有提现手续费金额高于5元时,才扣除</span>
        {else}
        <input type="hidden" name="data[paybegin]" value="{$data['paybegin']}"/>
        <input type="hidden" name="data[payend]" value="{$data['payend']}"/>
        <div class='form-control-static'>
            {$data['paybegin']} 元 - {$data['payend']}元
        </div>
        {/if}
    </div>
</div>

