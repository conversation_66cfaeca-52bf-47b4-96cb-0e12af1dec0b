

            <div class="form-group">
                <label class="col-lg control-label">成为代理商条件</label>
                <div class="col-sm-9 col-xs-12">
                	{ifp 'commission.set.edit'}
                        <label class="radio-inline"><input type="radio"  name="data[become]" value="0" {if $data['become'] ==0} checked="checked"{/if} data-needcheck="0" onclick="showBecome(this)"/> 后台指定</label>
	                    <label class="radio-inline"><input type="radio"  name="data[become]" value="1" {if $data['become'] ==1} checked="checked"{/if} data-needcheck="1" onclick="showBecome(this)"/> 申请</label>
					{else}
                        {if $data['become'] ==0}后台指定{/if}
						{if $data['become'] ==1}申请{/if}
					{/if}
                </div>
            </div>

            <div class="form-group protocol-group" {if $data['become'] !=1}style="display: none;"{/if}>
                <label class="col-lg control-label">显示申请协议</label>
                <div class="col-sm-8">
                    {ifp 'commission.set.edit'}
                    <label class="radio-inline"><input type="radio"  name="data[open_protocol]" value="1" {if $data['open_protocol'] ==1} checked="checked"{/if} /> 显示</label>
                    <label class="radio-inline"><input type="radio"  name="data[open_protocol]" value="0" {if $data['open_protocol'] ==0} checked="checked"{/if} /> 隐藏</label>
                    {else}
                    {if $data['open_protocol'] ==0}隐藏{else}显示{/if}
                    {/if}
                </div>
            </div>

            <div class="form-group">
                <label class="col-lg control-label">指定区域代理说明</label>
                <div class="col-sm-9 col-xs-12">
                    {ifp 'abonus.set.edit'}
                    <textarea class="form-control" name="data[noregdesc]" rows="5">{$data['noregdesc']}</textarea>
                    <span class="help-block">当“成为代理商条件”选择指定条件时，非代理商的提示文字, 默认显示为：想成为区域代理商吗？请立即联系我们！</span>
                    {else}
                    {$data['centerdesc']}
                    {/if}
                </div>
            </div>
