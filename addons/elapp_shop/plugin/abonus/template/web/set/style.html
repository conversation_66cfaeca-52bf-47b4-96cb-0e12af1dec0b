<div class="form-group">
    <label class="col-lg control-label">模板选择</label>
    <div class="col-sm-9 col-xs-12">
    {ifp 'abonus.set.edit'}
		<select class='form-control' name='data[style]'>
		    {loop $styles  $style}
		    <option value='{$style}' {if $style==$data['style']}selected{/if}>{$style}</option>
		    {/loop}
		</select>
	{else}
		{$data['style']}
	{/if}
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label">申请头部图片</label>
    <div class="col-sm-9 col-xs-12">
    	{ifp 'abonus.set.edit'}
			{php echo tpl_form_field_image2('data[regbg]',$data['regbg'],'/static/application/shop/plugin/abonus/static/images/bg.png')}
		{else}
			{if empty($data['regbg'])}
				<img src="/static/application/shop/plugin/abonus/static/images/bg.png" onerror="this.src='/static/application/shop/plugin/abonus/static/images/bg.png'; this.title='图片未找到.'" class="img-responsive img-thumbnail" width="150">
			{else}
				<img src="{php echo tomedia($data['regbg'])}" onerror="this.src='/static/application/shop/plugin/abonus/static/images/bg.png'; this.title='图片未找到.'" class="img-responsive img-thumbnail" width="150">
			{/if}
		{/if}
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">区域代理名称</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[aagent]" class="form-control" value="{php echo empty($data['texts']['aagent'])?'区域代理':$data['texts']['aagent']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['aagent'])?'区域代理':$data['texts']['aagent']}</div>
	    {/if}
	</div>
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">区域代理中心</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[center]" class="form-control" value="{php echo empty($data['texts']['center'])?'区域代理中心':$data['texts']['center']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['center'])?'区域代理中心':$data['texts']['center']}</div>
	    {/if}	
	</div>
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">成为区域代理</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[become]" class="form-control" value="{php echo empty($data['texts']['become'])?'成为区域代理':$data['texts']['become']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['become'])?'成为区域代理':$data['texts']['become']}</div>
	    {/if}
	</div>
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">分红</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[bonus]" class="form-control" value="{php echo empty($data['texts']['bonus'])?'分红':$data['texts']['bonus']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['bonus'])?'分红':$data['texts']['bonus']}</div>
	    {/if}
	</div>
    </div>
</div>
<div class="form-group">
	<label class="col-lg control-label"></label>
	<div class="col-sm-9 col-xs-12">
		<div class="input-group">
			<div class="input-group-addon">累计分红</div>
			{ifp 'abonus.set.edit'}
			<input type="text" name="texts[bonus_total]" class="form-control" value="{php echo empty($data['texts']['bonus_total'])?'累计分红':$data['texts']['bonus_total']}"  />
			{else}
			<div class="form-control valid">{php echo empty($data['texts']['bonus_total'])?'累计分红':$data['texts']['bonus_total']}</div>
			{/if}
		</div>
	</div>
</div>
 <div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">待结算分红</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[bonus_lock]" class="form-control" value="{php echo empty($data['texts']['bonus_lock'])?'待结算分红':$data['texts']['bonus_lock']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['bonus_lock'])?'待结算分红':$data['texts']['bonus_lock']}</div>
	    {/if}
	</div>
    </div>
</div>
<div class="form-group">
	<label class="col-lg control-label"></label>
	<div class="col-sm-9 col-xs-12">
		<div class="input-group">
			<div class="input-group-addon">预计分红</div>
			{ifp 'abonus.set.edit'}
			<input type="text" name="texts[bonus_wait]" class="form-control" value="{php echo empty($data['texts']['bonus_wait'])?'预计分红':$data['texts']['bonus_wait']}"  />
			{else}
			<div class="form-control valid">{php echo empty($data['texts']['bonus_wait'])?'预计分红':$data['texts']['bonus_wait']}</div>
			{/if}
		</div>
	</div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">已结算分红</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[bonus_pay]" class="form-control" value="{php echo empty($data['texts']['bonus_pay'])?'已结算分红':$data['texts']['bonus_pay']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['bonus_pay'])?'已结算分红':$data['texts']['bonus_pay']}</div>
	    {/if}
	</div>
    </div>
</div>
<div class="form-group">
	<label class="col-lg control-label"></label>
	<div class="col-sm-9 col-xs-12">
		<div class="input-group">
			<div class="input-group-addon">分红明细</div>
			{ifp 'abonus.set.edit'}
			<input type="text" name="texts[bonus_detail]" class="form-control" value="{php echo empty($data['texts']['bonus_detail'])?'分红明细':$data['texts']['bonus_detail']}"  />
			{else}
			<div class="form-control valid">{php echo empty($data['texts']['bonus_detail'])?'分红明细':$data['texts']['bonus_detail']}</div>
			{/if}
		</div>
	</div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
        <div class="input-group">
            <div class="input-group-addon">扣除提现手续费</div>
            {ifp 'abonus.set.edit'}
            <input type="text" name="texts[bonus_charge]" class="form-control" value="{php echo empty($data['texts']['bonus_charge'])?'扣除提现手续费':$data['texts']['bonus_charge']}"  />
            {else}
            <div class="form-control valid">{php echo empty($data['texts']['bonus_charge'])?'扣除提现手续费':$data['texts']['bonus_charge']}</div>
            {/if}
        </div>
    </div>
</div>
<div class="form-group">
    <label class="col-lg control-label"></label>
    <div class="col-sm-9 col-xs-12">
	<div class="input-group">
	    <div class="input-group-addon">分红明细</div>
	    {ifp 'abonus.set.edit'}
	    	<input type="text" name="texts[bonus_detail]" class="form-control" value="{php echo empty($data['texts']['bonus_detail'])?'分红明细':$data['texts']['bonus_detail']}"  />
	    {else}
	    	<div class="form-control valid">{php echo empty($data['texts']['bonus_detail'])?'分红明细':$data['texts']['bonus_detail']}</div>
	    {/if}
	</div>
    </div>
</div>


