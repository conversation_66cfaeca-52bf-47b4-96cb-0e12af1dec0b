{template '_header'}
<div class="page-header">当前位置：<span class="text-primary">代理中心入口设置</span></div>
<div class="page-content">
    <form id="setform"  {ifp 'abonus.cover.edit'}action="" method="post"{/if} class="form-horizontal form-validate" >

               <div class="form-group">
                    <label class="col-lg control-label">直接链接</label>
                    <div class="col-sm-9 col-xs-12">
                        <p class='form-control-static'>
                            <a href='javascript:;' class="js-clip" title='点击复制链接' data-url="{php echo mobileUrl('abonus',null,true)}" >
                                {php echo mobileUrl('abonus',null,true)}
                            </a>
                            <span style="cursor: pointer;" data-toggle="popover" data-trigger="hover" data-html="true"
                                  data-content="<img src='{$qrcode}' width='130' alt='链接二维码'>" data-placement="auto right">
                                <i class="glyphicon glyphicon-qrcode"></i>
                            </span>
                        </p>
                    </div>
                </div>

               <div class="form-group">
                    <label class="col-lg control-label must" >关键词</label>
                    <div class="col-sm-9 col-xs-12">
                        {ifp 'abonus.cover.edit'}
                         <input type='text' class='form-control' name='cover[keyword]' value="{$keyword['content']}" data-rule-required="true" />
                        {else}
                        <div class="form-control-static">{$keyword['content']}</div>
                        {/if}
                    </div>
                </div>

                <div class="form-group">
                    <label class="col-lg control-label">封面标题</label>
                    <div class="col-sm-9 col-xs-12">
                        {ifp 'abonus.cover.edit'}
                         <input type='text' class='form-control' name='cover[title]' value="{$cover['title']}" />
                        {else}
                        <div class="form-control-static">{$cover['title']}</div>
                        {/if}
                    </div>
                </div>
                <div class="form-group">
                    <label class="col-lg control-label">封面图片</label>
                    <div class="col-sm-9 col-xs-12">
                        {ifp 'abonus.cover.edit'}
                            {php echo tpl_form_field_image2('cover[thumb]',$cover['thumb'])}
                        {else}
                            <div class="form-control-static">
                                <img src="{php echo tomedia($cover['thumb'])}" onerror="this.src='/static/application/web/resource/images/nopic.jpg'; this.title='图片未找到.'" class="img-responsive img-thumbnail" width="150">
                            </div>
                        {/if}
                    </div>
                </div>
                  <div class="form-group">
                    <label class="col-lg control-label">封面描述</label>
                    <div class="col-sm-9 col-xs-12">
                        {ifp 'abonus.cover.edit'}
                        <textarea name='cover[desc]' class='form-control' rows="5">{$cover['description']}</textarea>
                        {else}
                        <div class="form-control-static">{$cover['description']}</div>
                        {/if}
                    </div>
                </div>
                   <div class="form-group">
                    <label class="col-lg control-label">状态</label>
                    <div class="col-sm-9">
                        {ifp 'abonus.cover.edit'}
                        <label class="radio-inline">
                            <input type="radio" name="cover[status]" value="1" {if $rule['status']==1} checked="checked"{/if}/>启用
                        </label>
                        <label class="radio-inline">
                            <input type="radio" name="cover[status]" value="0" {if empty($rule['status'])} checked="checked"{/if}/>禁用
                        </label>
                        {else}
                            <div class="form-control-static">{if empty($rule['status'])} 禁用{else}启用 {/if}</div>
                        {/if}
                    </div>
                </div>

    {ifp 'abonus.cover.edit'}
             <div class="form-group">
                <label class="col-lg control-label"></label>
                <div class="col-sm-9">
                    <input type="submit" value="提交" class="btn btn-primary" />

                </div>
            </div>
     {/if}
    </form>
</div>
{template '_footer'}
