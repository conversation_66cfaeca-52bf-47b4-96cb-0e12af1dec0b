{template '_header'}
<div class="page-header">当前位置：<span class="text-primary">基础设置</span></div>

<div class="page-content">
    <form id="setform"  {ifp 'abonus.set.edit'}action="" method="post"{/if} class="form-horizontal form-validate">

    <input type="hidden" id="tab" name="tab" value="#tab_{$_GPC['tab']}" />
    <div class="tabs-container>
         <div class="tabs-left">
    <ul class="nav nav-tabs" id="myTab">
        <li  {if empty($_GPC['tab']) || $_GPC['tab']=='basic'}class="active"{/if}><a href="#tab_basic">基本</a></li>
        <li  {if $_GPC['tab']=='relate'}class="active"{/if} ><a href="#tab_relate">代理商资格</a></li>
        <li {if $_GPC['tab']=='money'}class="active"{/if} ><a href="#tab_money">结算</a></li>
        <li {if $_GPC['tab']=='level'}class="active"{/if} ><a href="#tab_level">代理商升级</a></li>
        <li {if $_GPC['tab']=='center'}class="active"{/if} ><a href="#tab_center">区域代理中心</a></li>
        <li {if $_GPC['tab']=='style'}class="active"{/if} ><a href="#tab_style">样式/文字</a></li>
        <li  {if $_GPC['tab']=='protocol'}class="active"{/if}><a href="#tab_protocol">申请协议</a></li>
    </ul>
    <div class="tab-content ">
        <div class="tab-pane   {if empty($_GPC['tab']) || $_GPC['tab']=='basic'}active{/if}" id="tab_basic"><div class="panel-body">{template 'abonus/set/basic'}</div></div>
        <div class="tab-pane  {if $_GPC['tab']=='relate'}active{/if}" id="tab_relate"> <div class="panel-body">{template 'abonus/set/relate'}</div></div>
        <div class="tab-pane {if $_GPC['tab']=='money'}active{/if}" id="tab_money"> <div class="panel-body">{template 'abonus/set/money'}</div></div>
        <div class="tab-pane {if $_GPC['tab']=='level'}active{/if}" id="tab_level"> <div class="panel-body">{template 'abonus/set/level'}</div></div>
        <div class="tab-pane {if $_GPC['tab']=='center'}active{/if}" id="tab_center"> <div class="panel-body">{template 'abonus/set/center'}</div></div>
        <div class="tab-pane {if $_GPC['tab']=='style'}active{/if}" id="tab_style"> <div class="panel-body">{template 'abonus/set/style'}</div></div>
        <div class="tab-pane {if $_GPC['tab']=='protocol'}active{/if}" id="tab_protocol"> <div class="panel-body">{template 'abonus/set/protocol'}</div></div>
    </div>
    </div>
    {ifp 'abonus.set.edit'}
    <div class="form-group">
        <label class="col-lg control-label"></label>
        <div class="col-sm-9 col-xs-12">
            <input type="submit"  value="提交" class="btn btn-primary" />
        </div>
    </div>
    {/if}

    </form>
</div>
<script language='javascript'>
    require(['bootstrap'], function () {
        $('#myTab a').click(function (e) {
            $('#tab').val($(this).attr('href'));
            e.preventDefault();
            $(this).tab('show');
        })
    });
    function showBecome(obj) {
        var $this = $(obj);
        $('.become').hide();
        $('.becomeconsume').hide();
        $('.becomecheck').hide();

        if ($this.val() == '1') {
            $('.protocol-group').show();
        } else {
            $('.protocol-group').hide();
        }

        if ($this.val() == '2') {
            $('.become2').show();
            $('.becomeconsume').show();
        } else if ($this.val() == '3') {
            $('.become3').show();
            $('.becomeconsume').show();
        } else if ($this.val() == '4') {
            $('.become4').show();
            $('.becomeconsume').show();
        }

        if($this.data('needcheck')==1){
            $('.becomecheck').show();
        }
    }

</script>
{template '_footer'}
