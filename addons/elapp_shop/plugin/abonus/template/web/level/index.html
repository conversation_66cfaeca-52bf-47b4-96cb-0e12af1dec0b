{template '_header'}
<div class="page-header">
    当前位置：<span class="text-primary">区域代理等级</span>
</div>
<div class="page-content">
    <div class="page-sub-toolbar">
            <span class=''>
		 {ifp 'abonus.level.add'}
                            <a class='btn btn-primary btn-sm' data-toggle='ajaxModal' href="{php echo webUrl('abonus/level/add')}"><i class="fa fa-plus"></i> 添加新等级</a>
		 {/if}

	</span>
    </div>
    <table class="table table-responsive table-hover">
        <thead>
        <tr>
            <th style='width:160px;'>等级名称</th>
            <th >省级分红比例</th>
            <th>市级分红比例</th>
            <th >区级分红比例</th>
            <th>升级条件</th>
            <th style="width: 65px;">操作</th>
        </tr>
        </thead>
        <tbody>
        {loop $list $row}
        <tr {if $row['id']=='default'}style='background:#f2f2f2'{/if}>
        <td>{$row['levelname']}{if $row['id']=='default'}【默认等级】{/if}</td>
        <td>{php echo number_format((float)$row['bonus1'],4)}%</td>
        <td>{php echo number_format((float)$row['bonus2'],4)}%</td>
        <td>{php echo number_format((float)$row['bonus3'],4)}%</td>
        <td>	{if $row['id']!='default'}
            {if $leveltype==0}{if $row['ordermoney']>0}分销订单金额满 {$row['ordermoney']} 元 {else}不自动升级{/if}{/if}
            {if $leveltype==1}{if $row['ordermoney']>0}一级分销订单金额满 {$row['ordermoney']} 元 {else}不自动升级{/if}{/if}
            {if $leveltype==2}{if $row['ordercount']>0}分销订单数量满 {$row['ordercount']} 个 {else}不自动升级{/if}{/if}
            {if $leveltype==3}{if $row['ordercount']>0}一级分销订单数量满 {$row['ordercount']} 个 {else}不自动升级{/if}{/if}
            {if $leveltype==4}{if $row['ordermoney']>0}自购订单金额满 {$row['ordermoney']} 元 {else}不自动升级{/if}{/if}
            {if $leveltype==5}{if $row['ordercount']>0}自购订单数量满 {$row['ordercount']} 个 {else}不自动升级{/if}{/if}

            {if $leveltype==6}{if $row['downcount']>0}下级总人数满 {$row['downcount']} 个（分销商+非分销商） {else}不自动升级{/if}{/if}
            {if $leveltype==7}{if $row['downcount']>0}一级下级人数满 {$row['downcount']} 个（分销商+非分销商） {else}不自动升级{/if}{/if}

            {if $leveltype==8}{if $row['downcount']>0}团队总人数满 {$row['downcount']} 个（分销商） {else}不自动升级{/if}{/if}
            {if $leveltype==9}{if $row['downcount']>0}一级团队人数满 {$row['downcount']} 个（分销商） {else}不自动升级{/if}{/if}


            {if $leveltype==10}{if $row['commissionmoney']>0}已提现佣金总金额满 {$row['commissionmoney']} 元{else}不自动升级{/if}{/if}
            {if $leveltype==11}{if $row['bonusmoney']>0}已发放分红总金额满 {$row['bonusmoney']} 元{else}不自动升级{/if}{/if}
            {else}
            默认等级
            {/if}
        </td>
        <td>
            {ifp 'abonus.level.edit'}
            <a class='btn btn-default btn-sm btn-op btn-operation' data-toggle='ajaxModal'  href="{php echo webUrl('abonus/level/edit', array('id' => $row['id']))}" >
            <span data-toggle="tooltip" data-placement="top" data-original-title="{ifp 'abonus.level.edit'}编辑{else}查看{/if}">
                {ifp 'abonus.level.edit'}
                <i class='icow icow-bianji2'></i>
                {else}
               <i class='icow icow-chakan-copy'></i>
                {/if}

            </span>

            </a>
            {/if}
            {if $row['id']!='default'}
            {ifp 'abonus.level.delete'}
            <a class='btn btn-default btn-sm btn-op btn-operation' data-toggle='ajaxRemove'  href="{php echo webUrl('abonus/level/delete', array('id' => $row['id']))}" data-confirm="确认删除此等级吗？">
                 <span data-toggle="tooltip" data-placement="top" title="" data-original-title="删除">
                   <i class='icow icow-shanchu1'></i>
                </span>
            </a></td>
        {/if}
        {/if}

        </tr>
        {/loop}

        </tbody>
    </table>
</div>
{template '_footer'}


