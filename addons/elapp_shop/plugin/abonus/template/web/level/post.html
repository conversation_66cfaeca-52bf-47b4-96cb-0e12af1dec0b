<form action="" {ife 'abonus.level' $level}action="" method="post"{/if}  class="form-horizontal form-validate" enctype="multipart/form-data">
<input type="hidden" name="id" value="{$level['id']}" />
<input type="hidden" name="r" value="abonus.level.{if empty($level['id'])}add{else}edit{/if}" />
<div class="modal-dialog">
	<div class="modal-content">
		<div class="modal-header">
			<button data-dismiss="modal" class="close" type="button">×</button>
			<h4 class="modal-title">{if !empty($level['id'])}编辑{else}添加{/if}区域代理等级</h4>
		</div>
		<div class="modal-body">
			<div class="form-group">
				<label class="col-lg control-label must">等级名称</label>
				<div class="col-sm-9 col-xs-12">
					{ife 'abonus.level' $level}
					<input type="text" name="levelname" class="form-control" value="{$level['levelname']}" data-rule-required='true'/>
					{else}
					<div class='form-control-static'>{$level['levelname']}</div>
					{/if}
				</div>
			</div>
			<div class="form-group">
				<label class="col-lg control-label">省级分红比例</label>
				<div class="col-sm-9 col-xs-12">
					{ife 'abonus.level' $level}
					<div class='input-group'>
						<input type="text" name="bonus1" class="form-control" value="{$level['bonus1']}" />
						<div class='input-group-addon'>%</div>
					</div>
					<span class='help-block'>支持小数点后四位</span>
					{else}
					<div class='form-control-static'>{$level['bonus1']}%</div>
					{/if}
				</div>
			</div>
			<div class="form-group">
				<label class="col-lg control-label">市级分红比例</label>
				<div class="col-sm-9 col-xs-12">
					{ife 'abonus.level' $level}
					<div class='input-group'>
						<input type="text" name="bonus2" class="form-control" value="{$level['bonus2']}" />
						<div class='input-group-addon'>%</div>
					</div>
					<span class='help-block'>支持小数点后四位</span>
					{else}
					<div class='form-control-static'>{$level['bonus2']}%</div>
					{/if}
				</div>
			</div>
			<div class="form-group">
				<label class="col-lg control-label">区级分红比例</label>
				<div class="col-sm-9 col-xs-12">
					{ife 'abonus.level' $level}
					<div class='input-group'>
						<input type="text" name="bonus3" class="form-control" value="{$level['bonus3']}" />
						<div class='input-group-addon'>%</div>
					</div>
					<span class='help-block'>支持小数点后四位</span>
					{else}
					<div class='form-control-static'>{$level['bonus3']}%</div>
					{/if}
				</div>
			</div>
			{if $level['id']!='default'}
			<div class="form-group">
				<label class="col-lg control-label">升级条件</label>
				<div class="col-sm-9 col-xs-12">
					{ife 'abonus.level' $level}
					<div class='input-group'>
						{if $leveltype==0}
						<span class='input-group-addon'>分销订单金额满</span>
						<input type="text" name="ordermoney" class="form-control" value="{$level['ordermoney']}" />
						<span class='input-group-addon'>元</span>

						{/if}

						{if $leveltype==1}
						<span class='input-group-addon'>一级分销订单金额满</span>
						<input type="text" name="ordermoney" class="form-control" value="{$level['ordermoney']}" />
						<span class='input-group-addon'>元</span>
						{/if}


						{if $leveltype==2}
						<span class='input-group-addon'>分销订单数量满</span>
						<input type="text" name="ordercount" class="form-control" value="{$level['ordercount']}" />
						<span class='input-group-addon'>个</span>
						{/if}

						{if $leveltype==3}
						<span class='input-group-addon'>一级分销订单数量满</span>
						<input type="text" name="ordercount" class="form-control" value="{$level['ordercount']}" />
						<span class='input-group-addon'>个</span>
						{/if}

						{if $leveltype==4}
						<span class='input-group-addon'>自购订单金额满</span>
						<input type="text" name="ordermoney" class="form-control" value="{$level['ordermoney']}" />
						<span class='input-group-addon'>元</span>
						{/if}

						{if $leveltype==5}
						<span class='input-group-addon'>自购订单数量满</span>
						<input type="text" name="ordercount" class="form-control" value="{$level['ordercount']}" />
						<span class='input-group-addon'>个</span>
						{/if}
						{if $leveltype==6}
						<span class='input-group-addon'>下级总人数满</span>
						<input type="text" name="downcount" class="form-control" value="{$level['downcount']}" />
						<span class='input-group-addon'>个（分销商+非分销商）</span>
						{/if}
						{if $leveltype==7}
						<span class='input-group-addon'>一级下级人数满</span>
						<input type="text" name="downcount" class="form-control" value="{$level['downcount']}" />
						<span class='input-group-addon'>个（分销商+非分销商）</span>
						{/if}
						{if $leveltype==8}
						<span class='input-group-addon'>团队总人数满</span>
						<input type="text" name="downcount" class="form-control" value="{$level['downcount']}" />
						<span class='input-group-addon'>个（分销商）</span>
						{/if}
						{if $leveltype==9}
						<span class='input-group-addon'>一级团队人数满</span>
						<input type="text" name="downcount" class="form-control" value="{$level['downcount']}" />
						<span class='input-group-addon'>个（分销商）</span>
						{/if}

						{if $leveltype==10}
						<span class='input-group-addon'>已提现佣金总金额满</span>
						<input type="text" name="commissionmoney" class="form-control" value="{$level['commissionmoney']}" />
						<span class='input-group-addon'>元</span>
						{/if}
						{if $leveltype==11}
						<span class='input-group-addon'>已发放分红总金额满</span>
						<input type="text" name="bonusmoney" class="form-control" value="{$level['bonusmoney']}" />
						<span class='input-group-addon'>元</span>
						{/if}


					</div>
					<span class='help-block'>区域代理升级条件，不填写默认为不自动升级</span>

					{else}

					{if $leveltype==0}
					分销订单金额满 {$level['ordermoney']} 元
					{/if}

					{if $leveltype==1}
					一级分销订单金额满 {$level['ordermoney']} 元
					{/if}
					{if $leveltype==2}
					分销订单数量满 {$level['ordercount']} 个
					{/if}

					{if $leveltype==3}
					一级分销订单数量满 {$level['ordercount']} 个
					{/if}

					{if $leveltype==4}
					自购订单金额满 {$level['ordermoney']} 元
					{/if}

					{if $leveltype==5}
					自购订单数量满 {$level['ordercount']} 个
					{/if}
					{if $leveltype==6}
					下级总人数满 {$level['downcount']} 个（分销商+非分销商）

					{/if}
					{if $leveltype==7}
					一级下级人数满 {$level['downcount']} 个（分销商+非分销商）

					{/if}
					{if $leveltype==8}
					团队总人数满 {$level['downcount']} 个（分销商）
					{/if}
					{if $leveltype==9}
					一级团队人数满 {$level['downcount']} 个（分销商）
					{/if}

					{if $leveltype==10}
					已提现佣金总金额满 {$level['commissionmoney']} 元
					{/if}
					{if $leveltype==11}
					已发放分红总金额满 {$level['bonusmoney']} 元
					{/if}

					{/if}
				</div>
			</div>
			{/if}


		</div>
		<div class="modal-footer">
			<button class="btn btn-primary" type="submit">提交</button>
			<button data-dismiss="modal" class="btn btn-default" type="button">取消</button>
		</div>
	</div>
	</form>

