<?php
namespace app\controller\abonus;
use app\controller\PluginMobilePage;

class AbonusMobilePage extends PluginMobilePage
{
	public function __construct()
	{
		parent::__construct();
		global $_W, $_GPC;

		if (($_W['action'] != 'register') && ($_W['action'] != 'myshop') && ($_W['action'] != 'share')) {
			$member = m('member')->getMember($_W['openid']);
			if (empty($member['isagent']) || empty($member['status'])) {
				header('location: ' . mobileUrl('commission/register'));
				exit();
			}

			if (empty($member['isaagent']) || empty($member['aagentstatus'])) {
				header('location: ' . mobileUrl('abonus/register'));
				exit();
			}
		}
	}

	public function footerMenus($diymenuid = NULL)
	{
		global $_W, $_GPC;
		include $this->template('abonus/_menu');
	}
}