<?php

namespace web\controller\abonus;

use web\controller\PluginWebPage;

class IndexController extends PluginWebPage
{
    public function main()
    {
        global $_W;
        if (cv('abonus.agent')) {
            header('location: ' . webUrl('abonus/agent/main'));
            exit();
            return;
        }
        if (cv('abonus.level')) {
            header('location: ' . webUrl('abonus/level/main'));
            exit();
            return;
        }
        if (cv('abonus.bonus')) {
            header('location: ' . webUrl('abonus/bonus'));
            exit();
            return;
        }
        if (cv('abonus.bonus.send')) {
            header('location: ' . webUrl('abonus/bonus/send'));
            exit();
            return;
        }
        if (cv('abonus.notice')) {
            header('location: ' . webUrl('abonus/index/notice'));
            exit();
            return;
        }
        if (cv('abonus.set')) {
            header('location: ' . webUrl('abonus/index/set'));
            exit();
        }
    }

    public function notice()
    {
        global $_W, $_GPC;
        if ($_W['ispost']) {
            $data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
            m('common')->updatePluginset(array('abonus' => array('tm' => $data)));
            plog('abonus.notice.edit', '修改通知设置');
            show_json(1);
        }
        $data = m('common')->getPluginset('abonus');
        $template_list = pdo_fetchall('SELECT id,title FROM ' . tablename('elapp_shop_member_message_template') . ' WHERE uniacid=:uniacid ', array(':uniacid' => $_W['uniacid']));
        include $this->template();
    }

    public function set()
    {
        global $_W, $_GPC;
        if ($_W['ispost']) {
            $data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
            if (!empty($data['withdrawcharge'])) {
                $data['withdrawcharge'] = trim($data['withdrawcharge']);
                $data['withdrawcharge'] = floatval(trim($data['withdrawcharge'], '%'));
            }
            $data['withdrawbegin'] = floatval(trim($data['withdrawbegin']));
            $data['withdrawend'] = floatval(trim($data['withdrawend']));
            $data['register_bottom_content'] = m('common')->html_images($data['register_bottom_content']);
            $data['applycontent'] = m('common')->html_images($data['applycontent']);
            $data['regbg'] = save_media($data['regbg']);
            $data['become_goodsid'] = intval($_GPC['become_goodsid']);
            $data['texts'] = ((is_array($_GPC['texts']) ? $_GPC['texts'] : array()));
            m('common')->updatePluginset(array('abonus' => $data));
            m('cache')->set('template_' . $this->pluginname, $data['style']);
            $selfbuy = (($data['selfbuy'] ? '开启' : '关闭'));
            switch ($data['become']) {
                case '0':
                case '1':
                    $become = '申请';
                    break;
                case '2':
                    $become = '消费次数';
                    break;
                case '3':
                    $become = '消费金额';
                    break;
                case '4':
                    $become = '购买商品';
                    break;
            }
            plog('abonus.set.edit', '修改基本设置<br>' . '内购分红 -- ' . $selfbuy . ' <br>成为分销商条件 -- ' . $become);
            show_json(1, array('url' => webUrl('abonus/index/set', array('tab' => str_replace('#tab_', '', $_GPC['tab'])))));
        }
        $styles = array();
        $dir = IA_ROOT . '/addons/elapp_shop/plugin/' . $this->pluginname . '/template/mobile/';
        if ($handle = opendir($dir)) {
            while (($file = readdir($handle)) !== false) {
                if (($file != '..') && ($file != '.')) {
                    if (is_dir($dir . '/' . $file)) {
                        $styles[] = $file;
                    }
                }
            }
            closedir($handle);
        }
        $data = m('common')->getPluginset('abonus');
        include $this->template();
    }
}