<?php

namespace app\controller\app\faqun;

use app\controller\AppMobilePage;
use app\model\FaqunProductModel;
use app\model\FaqunUserStoreModel;
use app\model\FaqunBalanceOrderModel;
use FaqunSDK;

class RechargeController extends AppMobilePage
{
    public function products()
    {
        $model = new FaqunProductModel();
        $list = $model->where('status', 1)->order('amount', 'asc')->order('id', 'desc')
            ->select()
            ->toArray();
        return app_json([
            'code' => 0,
            'msg' => 'ok',
            'data' => [
                'list' => $list,
            ],
        ]);
    }
    

    /**
     * 一步创建充值订单并返回支付地址
     *
     * 请求参数（POST/GET 均可，建议 POST）：
     * - store_id int    门店ID（必填）。用于在远端系统标识门店/商户主体。
     * - product_id int  充值产品ID（必填）。由远端产品库定义。
     * - pay_type int    支付方式（必填）。示例：1=微信，2=支付宝（以后端定义为准）。
     * - remark string   备注（可选）。用于业务记录或对账。
     *
     * 成功返回：
     * {
     *   code: 0,
     *   msg: "ok",
     *   order_sn: string,      // 订单号
     *   pay_url: string,       // 支付跳转地址（可能为空，取决于渠道返回）
     * }
     * 失败返回：{ code: 1, msg: "错误原因" }
     *
     * @return \think\response\Json
     */
    public function create()
    {
        global $_GPC, $_W;

        $sdk = new \FaqunSDK();
        $services = $sdk->services();

        // 确保当前用户有门店（若不存在则远端创建并回填本地）
        $storeRecord = $this->ensureUserStore($services);
        if (!$storeRecord || empty($storeRecord['store_id'])) {
            return app_json(['code' => 1, 'msg' => '创建或获取门店失败，请页面退出重试']);
        }

        // return app_json([
        //     'code' => 0,
        //     'msg' => 'ok',
        //     'order_sn' => 'BR2025082516015439493851',
        //     'pay_url' => 'https://qr.alipay.com/bax05648mm3dqf6hmoq085c8',
        //     'create' => [
        //         'code' => 0,
        //         'msg' => 'OK',
        //         'data' => [
        //             'order_sn' => 'BR2025082516015439493851'
        //         ],
        //         'cost' => '28.898556ms'
        //     ],
        //     'payment' => [
        //         'code' => 0,
        //         'msg' => 'OK',
        //         'data' => [
        //             'code_url' => 'https://qr.alipay.com/bax05648mm3dqf6hmoq085c8'
        //         ],
        //         'cost' => '449.577196ms'
        //     ],
        // ]);

        try {
            // 基础参数读取与校验
            $storeId = $storeRecord['store_id'];
            $productId = intval($_GPC['product_id'] ?? 0); // 充值产品ID（必填）
            $payType = 1;     // 支付方式（必填）：1=支付宝
            $remark = trim((string)($_GPC['remark'] ?? ''));// 备注（可选）

            if ($storeId <= 0) {
                return app_json(['code' => 1, 'msg' => '缺少或非法的 store_id']);
            }
            if ($productId <= 0) {
                return app_json(['code' => 1, 'msg' => '缺少或非法的 product_id']);
            }
            if ($payType <= 0) {
                return app_json(['code' => 1, 'msg' => '缺少或非法的 pay_type']);
            }

            // 1) 创建订单
            $create = $services->recharge()->createOrder($storeId, $productId, $payType, $remark);
            if (!is_array($create) || intval($create['code'] ?? 1) !== 0) {
                $msg = is_array($create) ? (string)($create['msg'] ?? '创建订单失败') : '创建订单失败';
                return app_json(['code' => 1, 'msg' => $msg, 'resp' => $create]);
            }
            $orderSn = (string)($create['data']['order_sn'] ?? '');
            if ($orderSn === '') {
                return app_json(['code' => 1, 'msg' => '创建订单失败：缺少订单号', 'resp' => $create]);
            }

            // 2) 发起支付，获取支付地址
            $pay = $services->recharge()->pay($storeId, $orderSn);
            if (!is_array($pay) || intval($pay['code'] ?? 1) !== 0) {
                $msg = is_array($pay) ? (string)($pay['msg'] ?? '获取支付信息失败') : '获取支付信息失败';
                return app_json(['code' => 1, 'msg' => $msg, 'order_sn' => $orderSn, 'resp' => $pay]);
            }

            $payUrl = $pay['data']['code_url'] ?? '';

            // 3) 保存本地订单（幂等：按 order_sn upsert）
            try {
                $orderModel = new FaqunBalanceOrderModel();
                // $existing = $orderModel->where('order_sn', $orderSn)->find();

                // // 从本地产品表取金额（若存在）
                // $amountFen = 0; $priceFen = 0;
                try {
                    $product = (new FaqunProductModel())->where('product_id', $productId)->find();
                    if ($product) {
                        $amountFen = intval(bcmul((string)$product['amount'], '100'));
                        $priceFen = intval(bcmul((string)$product['price'], '100'));
                    }
                } catch (\Throwable $e) {}

                // $dataNow = time();
                $payload = [
                    'user_id' => $this->member['id'],
                    'store_id' => $storeId,
                    'product_id' => $productId,
                    'order_sn' => $orderSn,
                    'pay_type' => $payType,
                    'amount' => $amountFen,
                    'price' => $priceFen,
                    'status' => 10, // 已创建、待支付
                    'remark' => $remark,
                    'pay_url' => $payUrl,
                    // 'third_create_resp' => json_encode($create, JSON_UNESCAPED_UNICODE),
                    // 'third_pay_resp' => json_encode($pay, JSON_UNESCAPED_UNICODE),
                ];
                (new FaqunBalanceOrderModel())->create($payload);
            } catch (\Throwable $e) {
                // 持久化异常不影响主流程
            }

            $data = $pay['data'] ?? [];

            return app_json([
                'code' => 0,
                'msg' => 'ok',
                'order_sn' => $orderSn,
                'pay_url' => $payUrl,
                'create' => $create,
                'payment' => $pay,
            ]);
        } catch (\Throwable $e) {
            return app_json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 确保当前用户拥有远端门店并在本地有记录。
     * 逻辑：
     * 1) 查询本地表是否存在用户门店记录；存在则返回
     * 2) 远端尝试创建门店（name=realname|nickname|id，remark="{name}|默认门店"，并传 user_id）
     * 3) 远端列表查询（按 user_id 和 keyword=name 过滤），遍历匹配 name+remark，取出 store_id
     * 4) 将匹配到的 store_id 写入本地表并返回
     */
    protected function ensureUserStore($services)
    {
        $userId = intval($this->member['id']);
        $storeModel = new FaqunUserStoreModel();
        $exist = $storeModel->where('user_id', $userId)->find();
        if (!$exist) {
            return null;
        }
        if ($exist && !empty($exist['store_id'])) {
            return $exist->toArray();
        }

        // 构造 name 与 remark（尽量稳定可匹配）
        $name = $userId;
        $remark = $name . '|默认门店';

        // 1) 远端尝试创建（忽略其是否直接返回 store_id）
        try {
            $services->store()->createStore([
                'name' => $name,
                'remark' => $remark,
                // 'user_id' => $userId,
            ]);
        } catch (\Throwable $e) {
            // 远端创建失败也继续尝试列表匹配（防止幂等或短暂失败）
        }

        // 2) 列表遍历匹配（name+remark），支持多页检索
        $matched = null;
        $perPage = 100;
        $maxPages = 10; // 安全阈值，最多查 10 页（可按需调整或改为读取 total/page_count）
        for ($page = 1; $page <= $maxPages; $page++) {
            try {
                $filters = ['user_id' => $userId, 'keyword' => $name];
                $resp = $services->store()->getStoreList($page, $perPage, $filters);
                if (!is_array($resp) || intval($resp['code'] ?? 1) !== 0) {
                    break; // 本页请求失败即停止，避免无意义重试
                }
                $data = $resp['data']['list'] ?? [];
                $total = $resp['data']['total'] ?? 0;
                if (!is_array($data) || empty($data)) {
                    break; // 无数据，提前结束
                }
                foreach ($data as $row) {
                    if ((string)($row['name'] ?? '') === $name && (string)($row['remark'] ?? '') === $remark) {
                        $matched = $row; break 2; // 命中，跳出双层循环
                    }
                }
                // 若本页数量小于 perPage，说明没有更多页
                if ($total == 0) {
                    break;
                }
            } catch (\Throwable $e) {
                // 单页异常忽略并中止循环（防止陷入异常重试）
                break;
            }
        }

        if (!$matched || empty($matched['store_id'])) {
            return null;
        }

        // 3) 写入本地表并返回
        $record = [
            'user_id' => $userId,
            'store_id' => intval($matched['store_id']),
            'name' => (string)$matched['name'],
            'remark' => (string)$matched['remark'],
        ];
        if ($exist) {
            $storeModel->where('id', $exist['id'])->update($record);
            $record['id'] = $exist['id'];
        } else {
            $new = $storeModel->create($record);
            if ($new) { $record['id'] = $new['id'] ?? null; }
        }
        return $record;
    }

    /**
     * 查询支付/充值订单详情
     *
     * 请求参数：
     * - id int           充值记录ID（与 order_sn 二选一，优先使用 id）
     * - order_sn string  充值订单号（当未提供 id 时，将通过列表接口按关键词反查 id）
     * - store_id int     门店ID（可选）。远端接口允许可选传入
     * - remark string    备注（可选）。将原样返回，便于调用方对账
     *
     * @return \think\response\Json
     */
    public function query()
    {
        global $_GPC;
        try {
            $id = intval($_GPC['id'] ?? 0);
            $orderSn = trim((string)($_GPC['order_sn'] ?? ''));
            $storeId = isset($_GPC['store_id']) ? intval($_GPC['store_id']) : null;
            $remark = trim((string)($_GPC['remark'] ?? ''));

            if ($id <= 0 && $orderSn === '') {
                return app_json(['code' => 1, 'msg' => '请提供 id 或 order_sn']);
            }

            $sdk = new FaqunSDK();
            $services = $sdk->services();

            // 若未提供 id，则基于 order_sn 反查 id
            if ($id <= 0 && $orderSn !== '') {
                try {
                    $params = [
                        'page' => 1,
                        'per_page' => 1,
                        'keyword' => $orderSn,
                    ];
                    if (!is_null($storeId)) { $params['store_id'] = $storeId; }
                    $list = $services->recharge()->getRechargeList($params);
                    if (is_array($list) && intval($list['code'] ?? 1) === 0) {
                        $rows = $list['data']['list'] ?? ($list['data'] ?? []);
                        if (is_array($rows) && !empty($rows)) {
                            $first = $rows[0];
                            $id = intval($first['id'] ?? 0);
                        }
                    }
                } catch (\Throwable $e) {
                    // 忽略列表查询异常，继续向下走
                }
            }

            if ($id <= 0) {
                return app_json(['code' => 1, 'msg' => '无法确定查询ID']);
            }

            $detail = $services->recharge()->getRechargeDetail($id, $storeId);
            if (!is_array($detail)) {
                return app_json(['code' => 1, 'msg' => '查询失败：响应格式异常']);
            }
            if (intval($detail['code'] ?? 1) !== 0) {
                return app_json(['code' => 1, 'msg' => (string)($detail['msg'] ?? '查询失败'), 'resp' => $detail]);
            }

            return app_json([
                'code' => 0,
                'msg' => 'ok',
                'id' => $id,
                'order_sn' => $orderSn,
                'remark' => $remark,
                'data' => $detail['data'] ?? [],
                'resp' => $detail,
            ]);
        } catch (\Throwable $e) {
            return app_json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }

    /**
     * 获取当前用户关联门店余额
     *
     * 说明：
     * - 通过远端接口 GET /api/v1/store/info 获取门店信息，解析余额返回
     * - 可选参数 remark 将原样回传，方便调用方对账
     */
    public function accountBalance()
    {
        global $_GPC;
        $sdk = new FaqunSDK();
        $services = $sdk->services();

        // $remark = trim((string)($_GPC['remark'] ?? ''));
        $storeModel = new FaqunUserStoreModel();
        $exist = $storeModel->where('user_id', $this->member['id'])->find();
        $storeId = $exist['store_id'] ?? 0;
        if (!$exist || empty($exist['store_id'])) {
            // 创建门店
            $resp = $services->store()->createStore([
                'name' => $this->member['id'],
                'remark' => $this->member['id'],
            ]);

            // 获取远程门店列表
            for ($i=0;$i<10;$i++) {
                $resp = $services->store()->getStoreList(1, 100, []);

                $list = $resp['data']['list'] ?? [];
                foreach ($list as $row) {
                    $eq = $row['name'] == $this->member['id'] && $row['remark'] == $this->member['id'];

                    if ($eq) {
                        $exist = $storeModel->where('user_id', $this->member['id'])->where('store_id', $row['store_id'])->find();
                        if (!$exist) {
                            $storeModel->create([
                                'user_id' => $this->member['id'],
                                'store_id' => $row['store_id'],
                                'name' => $row['name'],
                                'remark' => $row['remark'],
                            ]);
                            $storeId = $row['store_id'];
                        }
                        break;
                    }
                }
                
                if ($resp['code'] == 0) {
                    break;
                }
                if (isset($resp['data']['total']) && $resp['data']['total'] == 0) {
                    break;
                }
            }
        }

        try {
            
            if (!$storeId) {
                return app_json([
                    'code' => 0,
                    'msg' => 'ok',
                    'data' => ['balance' => 0],
                ]);
            }

            $resp = $services->store()->getStoreInfo($storeId);
            file_put_contents('/tmp/store', json_encode($resp));
            if (!is_array($resp)) {
                return app_json(['code' => 1, 'msg' => '查询失败：响应格式异常']);
            }
            if (intval($resp['code'] ?? 1) !== 0) {
                return app_json(['code' => 1, 'msg' => (string)($resp['msg'] ?? '查询失败'), 'resp' => $resp]);
            }

            $data = $resp['data'] ?? [];
            // 兼容不同字段位置的余额
            $balance = $data['available balance'] ?? 0;
            //$data['balance'] = 1234;
            $data = [
                'balance' => $balance,
            ];

            return app_json([
                'code' => 0,
                'msg' => 'ok',
                // 'remark' => $remark,
                // 'balance' => $balance,
                'data' => $data,
                'order' => $order_resp,
                // 'resp' => $resp,
            ]);
        } catch (\Throwable $e) {
            return app_json(['code' => 1, 'msg' => $e->getMessage()]);
        }
    }
}
