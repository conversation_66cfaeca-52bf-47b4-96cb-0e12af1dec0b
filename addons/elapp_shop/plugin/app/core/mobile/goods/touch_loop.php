<?php

namespace app\controller\app\goods;

use app\controller\AppMobilePage;
use app\model\TouchloopModel;

class TouchLoopController extends AppMobilePage
{
    public function main()
    {
        if (p('touchloop')) {
            return $this->getposter();
        }
        show_json(0, '插件配置错误');
    }

    public function getposter()
    {
        global $_W, $_GPC;
        $openid = $_W['openid'];
        /**
         * @var $plugin TouchLoopModel
         */
        $plugin = p('touchloop');
        $goods = pdo_get('elapp_shop_goods', array('id' => 10318));
        $member = m('member')->getInfo($_W['openid']);
        $set = $plugin->getSet();
        $item = ($set['poster']);
        $poster['bg'] = $item['bg'];
        $version = rand(1, 10);
        $md5 = md5(json_encode(array(
            'siteroot' => $_W['siteroot'],
            'openid' => $member['openid'],
            'goodstitle' => $goods['title'],
            'goodprice' => $goods['minprice'],
            'goodsoldprice' => $goods['productprice'],
            'version' => $version,
            'goodsid' => $goods['id']
        )));
        $plugin->deleteImage(array(
            'siteroot' => $_W['siteroot'],
            'openid' => $member['openid'],
            'goodstitle' => $goods['title'],
            'goodprice' => $goods['minprice'],
            'goodsoldprice' => $goods['productprice'],
            'version' => $version,
            'goodsid' => $goods['id']
        ));
        $filename = $md5 . '.png';
        $path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . "/toouch_loop/code/" . $_W['uniacid'] . '/';
        $target = $path . $filename;
        @mkdirs($path);
        if (is_file($target)) {
            $file = $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . "/toouch_loop/code/" . $_W['uniacid'] . "/" . $filename . '?v=1.0';
            app_json(array('status' => 1, 'filename' => $file));
        }
        $poster['data'] = json_decode(str_replace('&quot;', "'", $item['data']), true);
        $image = imagecreatetruecolor(640, 1008);
        $bg = imagecreatefromstring(file_get_contents(tomedia($poster['bg'])));
        imagecopy($image, $bg, 0, 0, 0, 0, 640, 1008);
        imagedestroy($bg);
        $data = $poster['data'];
        /**
         * @var $plugin TouchLoopModel
         */
        if (empty($plugin)) {
            return false;
        }
        $plugin->image = $image;
        $plugin->goods = $goods;
        $plugin->member = $member;
        foreach ($data as $item) {
            if (isset($item['type']) && strlen($item['type']) > 0) {
                $func = 'build' . ucfirst($item['type']);
                call_user_func_array(array($plugin, $func), array('params' => $item));
            }
        }
        imagepng($plugin->image, $target);
        imagedestroy($bg);
        if (is_file($target)) {
            app_json(array('status' => 1, 'filename' => $file));
        }
    }

}