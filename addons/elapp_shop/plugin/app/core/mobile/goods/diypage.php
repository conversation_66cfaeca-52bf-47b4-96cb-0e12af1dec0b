<?php
namespace app\controller\app\goods;

use app\controller\AppMobilePage;
use app\model;

class DiypageController extends AppMobilePage
{
    //获取自定义页面的产品列表 by sx 2023-11-29
    public function get_diy_goods_list()
    {
        global $_GPC;
        global $_W;
        $item             = [];
        $item['params']   = $_GPC;
        $item['data']     = [];
        $pagesize         = !isset($_GPC['pagesize']) ? 10 :($_GPC['pagesize'] > 20 ? 20 : $_GPC['pagesize']);
        $page             = intval($_GPC['page']);
        if ($page <= 0) {
            $page = 1;
        }
        $limit         = ($page - 1) * $pagesize;
        $limit         = $limit . ',' . $pagesize;
        $creditshop    = !empty($item['params']['goodstype']) ? true : false;
        $item['total'] = 0;
        $goods         = [];
        $DiypageModel  = new model\DiypageModel();
        if (p('clerk')) {
            $clerk = p('clerk');
        }
        // 判断浏览权限
        if ($item['params']['goodsdata'] == '0') {
            if (!empty($item['data']) && is_array($item['data'])) {
                $goodsids = [];
                foreach ($item['data'] as $index => $data) {
                    if (!empty($data['gid'])) {
                        $goodsids[] = $data['gid'];
                    }
                }
                if (!empty($goodsids) && is_array($goodsids)) {
                    $newgoodsids = implode(',', $goodsids);
                    if ($creditshop) {
                        $goods         = pdo_fetchall("select id, showlevels, showgroups, showOrgs from " . tablename('elapp_shop_creditshop_goods') . " where id in( $newgoodsids ) and status=1 and deleted=0 and uniacid=:uniacid order by displayorder desc ", [':uniacid' => $_W['uniacid']]);
                        $goods_count   = pdo_count('elapp_shop_creditshop_goods', "id in ($newgoodsids) and status =1 and `deleted`=0 and uniacid={$_W['uniacid']}");
                        $item['total'] = $goods_count;
                    } else {
                        $goods         = pdo_fetchall("select id, showlevels, showOrgs, hascommission,nocommission,commission,commission1_rate,commission1_pay,marketprice,maxprice,showgroups from " . tablename('elapp_shop_goods') . " where id in( $newgoodsids ) and status=1 and is_cloud = 0 and deleted=0 and checked=0 and uniacid=:uniacid order by displayorder desc ", [':uniacid' => $_W['uniacid']]);
                        $goods_count   = pdo_count('elapp_shop_goods', "id in ($newgoodsids) and is_cloud = 0 and status =1 and `deleted`=0 and checked=0 and uniacid={$_W['uniacid']}");
                        $item['total'] = $goods_count;
                    }
                    if (!empty($goods) && is_array($goods)) {
                        foreach ($item['data'] as $childid => $childgoods) {
                            foreach ($goods as $index => $good) {
                                if ($good['id'] == $childgoods['gid']) {
                                    $showgoods = m('goods')->visit($good, $this->member);
                                    if (empty($showgoods)) {
                                        unset($item['data'][$childid]);
                                    }
                                }
                            }
                        }
                    }
                }
            }

        } elseif ($item['params']['goodsdata'] == '1') {
//按商品分类、含销量、价格升降排序// 根据条件读取商品分类里的商品 并进行临时赋值
            $cateid = $item['params']['cateid'];
            if (!empty($cateid)) {
                $orderby   = ' displayorder desc, createtime desc';
                $goodssort = $item['params']['goodssort'];
                if (!empty($goodssort)) {
                    if ($goodssort == 1) {
                        // 销量
                        $orderby = empty($item['params']['goodstype']) ? ' sales+salesreal desc, displayorder desc' : ' joins desc, displayorder desc';
                    } elseif ($goodssort == 2) {
                        // 价格降序
                        $orderby = empty($item['params']['goodstype']) ? ' minprice desc, displayorder desc' : ' minmoney desc, displayorder desc';
                    } elseif ($goodssort == 3) {
                        // 价格升序
                        $orderby = empty($item['params']['goodstype']) ? ' minprice asc, displayorder desc' : ' minmoney asc, displayorder desc';
                    }
                }
                if (empty($item['params']['goodstype'])) {
                    $goodslist = m('goods')->getList([
                        'cate'     => $cateid,
                        'pagesize' => $pagesize,
                        'page'     => 1,
                        'order'    => $orderby,
                        'merchid'  => intval($page['merch']),
                    ]);
                    $goods = $goodslist['list'];
                } else {
                    $goods = pdo_fetchall("select id, title, thumb, price as productprice, money as minprice, credit, stock, showlevels, showgroups, showOrgs,`type`,`goodsClassID`,medicineAttributeID, goodstype, merchid from " . tablename('elapp_shop_creditshop_goods') . " where cate=:cate and status=1 and deleted=0 and uniacid=:uniacid order by {$orderby} limit " . $limit, [':cate' => $cateid, ':uniacid' => $_W['uniacid']]);
                }
                $goods_count   = pdo_count('elapp_shop_creditshop_goods', "cate = $cateid and status =1 and `deleted`=0 and uniacid={$_W['uniacid']}");
                $item['total'] = $goods_count;
                $item['data']  = [];
                if (!empty($goods) && is_array($goods)) {

                    foreach ($goods as $key => $value) {
                        $vgood                              = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", [':id' => $value['id'], ':uniacid' => $_W['uniacid']]);
                        $goods[$key]['maxMemberLevelPrice'] = $maxMemberLevelPrice = m('goods')->getMemberPrice($vgood, $maxlevel); //最大会员等级价格

                        //商品多规格
                        if ($value['hasoption'] == 1) {
                            $pricemax = [];
                            $options  = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', [':goodsid' => $value['id'], ':uniacid' => $_W['uniacid']]);
                            foreach ($options as $k => $v) {
                                array_push($pricemax, $v['marketprice']);
                            }
                            $value['marketprice'] = empty($pricemax) ? 0 : max($pricemax); // 如果导致商品价格显示错误 开启此块注释
                        }
                        $goods[$key]['reclevel'] = com_run('sale::getReclevel', $value['reclevel']); //商品营销主推级别
                        //分销预计收益
                        if ($value['nocommission'] == 0) {
                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($value['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($value['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $goods[$key]['seecommission'] = $DiypageModel->getCommission($value, $level, $set);
                            if ($goods[$key]['seecommission'] > 0) {
                                $goods[$key]['seecommission'] = round($goods[$key]['seecommission'], 2);
                            }
                            $goods[$key]['cansee']   = $set['cansee'];
                            $goods[$key]['seetitle'] = $set['seetitle'];
                        } else {
                            $goods[$key]['seecommission'] = 0;
                            $goods[$key]['cansee']        = $set['cansee'];
                            $goods[$key]['seetitle']      = $set['seetitle'];
                        }
                        //店员预计收益
                        if ($value['noClerkCommission'] == 0) {
                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($value['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($value['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $goods[$key]['clerkSeecommission'] = $clerk->getGoodsCommission($value, $clerklevel, $options);
                            if ($goods[$key]['clerkSeecommission'] > 0) {
                                $goods[$key]['clerkSeecommission'] = round($goods[$key]['clerkSeecommission'], 2);
                            }
                            $goods[$key]['clerkCansee']     = $clerk_set['cansee'];
                            $goods[$key]['clerkSeetitle']   = $clerk_set['seetitle'];
                            $goods[$key]['is_show_revenue'] = $myshop['is_show_revenue'];
                        } else {
                            $goods[$key]['clerkSeecommission'] = 0;
                            $goods[$key]['clerkCansee']        = $clerk_set['cansee'];
                            $goods[$key]['clerkSeetitle']      = $clerk_set['seetitle'];
                            $goods[$key]['is_show_revenue']    = $myshop['is_show_revenue'];
                        }
                    }
                    foreach ($goods as $index => $good) {
                        $showgoods = m('goods')->visit($good, $this->member);
                        if (!empty($showgoods)) {
                            $childid                = rand(1000000000, 9999999999);
                            $childid                = 'C' . $childid;
                            $item['data'][$childid] = [
                                'thumb'               => $good['thumb'],
                                'title'               => $good['title'],
                                'subtitle'            => $good['subtitle'],
                                'price'               => $good['minprice'],
                                'maxMemberLevelPrice' => $good['maxMemberLevelPrice'],
                                'maxlevel'            => $maxlevel,
                                'gid'                 => $good['id'],
                                'stock'               => $good['stock'],
                                'bargain'             => $good['bargain'],
                                'productprice'        => $good['productprice'],
                                'credit'              => $good['credit'],
                                'ctype'               => $good['type'],
                                'gtype'               => $good['goodstype'],
                                'seecommission'       => $good['seecommission'],
                                'cansee'              => $good['cansee'],
                                'seetitle'            => $good['seetitle'],
                                'clerkSeecommission'  => $good['clerkSeecommission'],
                                'clerkCansee'         => $good['clerkCansee'],
                                'clerkSeetitle'       => $good['clerkSeetitle'],
                                'sales'               => $good['sales'] + intval($good['salesreal']), //销量
                                'is_show_revenue'     => $good['is_show_revenue'],
                                'medicineAttributeID' => $good['medicineAttributeID'], //药品属性
                                'goodsClassID'        => $good['goodsClassID'], //商品类目 hlei20210521
                                'reclevel'            => $good['reclevel'], //商品营销主推级别
                            ];
                        }
                    }
                }
            } else {
                $item['data'] = [];
            }

        } elseif ($item['params']['goodsdata'] == '2' && empty($item['params']['goodstype'])) {
//按商品分组、含销量、价格升降序排序
            // 根据条件读取商品分组里的商品 并进行临时赋值
            $groupid = intval($item['params']['groupid']);
            if (!empty($groupid)) {
                $group = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_goods_group') . " WHERE id=:id and uniacid=:uniacid limit 1 ", [':id' => $groupid, ':uniacid' => $_W['uniacid']]);
            }
            $item['data'] = [];

            if (!empty($group) && !empty($group['goodsids'])) {

                $orderby   = ' order by displayorder desc';
                $goodssort = $item['params']['goodssort'];
                if (!empty($goodssort)) {
                    if ($goodssort == 1) {
                        // 销量
                        $orderby = empty($item['params']['goodstype']) ? ' order by sales+salesreal desc, displayorder desc' : ' order by joins desc, displayorder desc';
                    } elseif ($goodssort == 2) {
                        // 价格降序
                        $orderby = empty($item['params']['goodstype']) ? ' order by  minprice desc, displayorder desc' : ' order by  minmoney desc, displayorder desc';
                    } elseif ($goodssort == 3) {
                        // 价格升序
                        $orderby = empty($item['params']['goodstype']) ? ' order by  minprice asc, displayorder desc' : ' order by  minmoney asc, displayorder desc';
                    }
                }

                $goodsids = $group['goodsids'];

                $commissionCondition = '';
                //查询店员商品分润表
                if (!empty($clerk_set['isopen']) && !empty($clerk_set['level'])) {
                    $commissionCondition .= ',hasClerkCommission,noClerkCommission,clerkCommission,clerkCommission1_rate,clerkCommission1_pay';
                }
                $goods = pdo_fetchall("select id, title, subtitle, thumb, `type`,`goodsClassID`,medicineAttributeID, minprice, sales, salesreal, stock, showlevels, showgroups, showOrgs, hascommission,nocommission,commission,commission1_rate,marketprice,commission1_pay,maxprice,bargain,productprice,ispresell,presellprice,isreclevel,reclevel,merchid" . $commissionCondition . " from " . tablename('elapp_shop_goods') . " where id in( $goodsids ) and is_cloud = 0 and  status=1 and `deleted`=0 and `status`=1 and uniacid=:uniacid " . $orderby . " limit {$limit}", [':uniacid' => $_W['uniacid']]);

                if (!empty($goods) && is_array($goods)) {
                    //获取商品的预售价格
                    if ($value['ispresell'] == 1) {
                        $goods[$key]['minprice'] = $value['presellprice'];
                    }

                    foreach ($goods as $key => $value) {
                        $vgood                              = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", [':id' => $value['id'], ':uniacid' => $_W['uniacid']]);
                        $goods[$key]['maxMemberLevelPrice'] = $maxMemberLevelPrice = m('goods')->getMemberPrice($vgood, $maxlevel); //最大会员等级价格
                        //商品多规格
                        if ($value['hasoption'] == 1) {
                            $pricemax = [];
                            $options  = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', [':goodsid' => $value['id'], ':uniacid' => $_W['uniacid']]);
                            foreach ($options as $k => $v) {
                                array_push($pricemax, $v['marketprice']);
                            }
                            $value['marketprice'] = empty($pricemax) ? 0 : max($pricemax); // 如果导致商品价格显示错误 开启此块注释
                        }
                        $goods[$key]['reclevel'] = com_run('sale::getReclevel', $value['reclevel']); //商品营销主推级别

                        //分销预计收益
                        if ($value['nocommission'] == 0) {

                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($value['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($value['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $goods[$key]['seecommission'] = $DiypageModel->getCommission($value, $level, $set);
                            if ($goods[$key]['seecommission'] > 0) {
                                $goods[$key]['seecommission'] = round($goods[$key]['seecommission'], 2);
                            }
                            $goods[$key]['cansee']   = $set['cansee'];
                            $goods[$key]['seetitle'] = $set['seetitle'];
                        } else {
                            $goods[$key]['seecommission'] = 0;
                            $goods[$key]['cansee']        = $set['cansee'];
                            $goods[$key]['seetitle']      = $set['seetitle'];
                        }

                        //店员预计收益
                        if ($value['noClerkCommission'] == 0) {
                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($value['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($value['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $goods[$key]['clerkSeecommission'] = $clerk->getGoodsCommission($value, $clerklevel, $options);
                            if ($goods[$key]['clerkSeecommission'] > 0) {
                                $goods[$key]['clerkSeecommission'] = round($goods[$key]['clerkSeecommission'], 2);
                            }
                            $goods[$key]['clerkCansee']     = $clerk_set['cansee'];
                            $goods[$key]['clerkSeetitle']   = $clerk_set['seetitle'];
                            $goods[$key]['is_show_revenue'] = $myshop['is_show_revenue'];
                        } else {
                            $goods[$key]['clerkSeecommission'] = 0;
                            $goods[$key]['clerkCansee']        = $clerk_set['cansee'];
                            $goods[$key]['clerkSeetitle']      = $clerk_set['seetitle'];
                            $goods[$key]['is_show_revenue']    = $myshop['is_show_revenue'];
                        }
                    }
                    foreach ($goods as $index => $good) {
                        $showgoods = m('goods')->visit($good, $this->member);
                        if (!empty($showgoods)) {
                            $childid                = rand(1000000000, 9999999999);
                            $childid                = 'C' . $childid;
                            $item['data'][$childid] = [
                                'thumb'               => $good['thumb'],
                                'title'               => $good['title'],
                                'subtitle'            => $good['subtitle'],
                                'price'               => $good['minprice'],
                                'maxMemberLevelPrice' => $good['maxMemberLevelPrice'],
                                'maxlevel'            => $maxlevel,
                                'gid'                 => $good['id'],
                                'stock'               => $good['stock'],
                                'ctype'               => $good['type'],
                                'bargain'             => $good['bargain'],
                                'seecommission'       => $good['seecommission'],
                                'cansee'              => $good['cansee'],
                                'seetitle'            => $good['seetitle'],
                                'clerkSeecommission'  => $good['clerkSeecommission'],
                                'clerkCansee'         => $good['clerkCansee'],
                                'clerkSeetitle'       => $good['clerkSeetitle'],
                                'productprice'        => $good['productprice'],
                                'sales'               => $good['sales'] + $good['salesreal'], //销量
                                'is_show_revenue'     => $good['is_show_revenue'],
                                'medicineAttributeID' => $good['medicineAttributeID'], //药品属性
                                'goodsClassID'        => $good['goodsClassID'], //商品类目
                                'reclevel'            => $good['reclevel'], //商品主推级别
                            ];
                        }
                    }
                }
            }
        } elseif ($item['params']['goodsdata'] > 2) {
//按商品类型：新品、热卖、促销、包邮、限时卖、推荐商品，包含子类型销量、升降排序
            $args = [
                'pagesize' => $item['params']['goodsnum'],
                'page'     => 1,
                'order'    => ' displayorder desc, createtime desc',
            ];

            $goodssort = $item['params']['goodssort'];
            if (!empty($goodssort)) {

                if ($goodssort == 1) {
                    // 销量
                    $args['order'] = empty($item['params']['goodstype']) ? ' sales desc, displayorder desc' : ' joins desc, displayorder desc';
                } elseif ($goodssort == 2) {
                    // 价格降序
                    $args['order'] = empty($item['params']['goodstype']) ? ' minprice desc, displayorder desc' : 'minmoney desc, mincredit desc, displayorder desc';
                } elseif ($goodssort == 3) {
                    // 价格升序
                    $args['order'] = empty($item['params']['goodstype']) ? ' minprice asc, displayorder desc' : 'minmoney asc, mincredit asc, displayorder desc';
                }
            }

            if (empty($item['params']['goodstype'])) {
                if ($item['params']['goodsdata'] == 3) {
                    $args['isnew'] = 1;
                } elseif ($item['params']['goodsdata'] == 4) {
                    $args['ishot'] = 1;
                } elseif ($item['params']['goodsdata'] == 5) {
                    $args['isrecommand'] = 1;
                } elseif ($item['params']['goodsdata'] == 6) {
                    $args['isdiscount'] = 1;
                } elseif ($item['params']['goodsdata'] == 7) {
                    $args['issendfree'] = 1;
                } elseif ($item['params']['goodsdata'] == 8) {
                    $args['istime'] = 1;
                }
                $args['merchid'] = $page['merch'];
                $goodslist       = m('goods')->getList($args);
                $goods           = $goodslist['list'];
            } else {
                $condition = " and status=1 and deleted=0 and uniacid=:uniacid ";
                $params    = [
                    ':uniacid' => $_W['uniacid'],
                ];
                if ($item['params']['goodsdata'] == 5) {
                    $condition .= " and isrecommand=1 and showlevels = '' and showOrgs = ''";
                } elseif ($item['params']['goodsdata'] == 9) {
                    $condition .= " and type=0 ";
                } elseif ($item['params']['goodsdata'] == 10) {
                    $condition .= " and type=1 ";
                }
                $goods = pdo_fetchall("select id, title, thumb, price as productprice, money as minprice, credit, stock, showlevels,showgroups, showOrgs, `type`, goodstype from " . tablename('elapp_shop_creditshop_goods') . " where 1 {$condition} order by {$args['order']} limit " . $args['pagesize'], $params);
            }

            $item['data'] = [];
            if (!empty($goods) && is_array($goods)) {
                unset($index);

                foreach ($goods as $key => $value) {
                    $vgood                              = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", [':id' => $value['id'], ':uniacid' => $_W['uniacid']]);
                    $goods[$key]['maxMemberLevelPrice'] = $maxMemberLevelPrice = m('goods')->getMemberPrice($vgood, $maxlevel); //最大会员等级价格

                    //商品多规格
                    if ($value['hasoption'] == 1) {
                        $pricemax = [];
                        $options  = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', [':goodsid' => $value['id'], ':uniacid' => $_W['uniacid']]);
                        foreach ($options as $k => $v) {
                            array_push($pricemax, $v['marketprice']);
                        }
                        $value['marketprice'] = empty($pricemax) ? 0 : max($pricemax); // 如果导致商品价格显示错误 开启此块注释
                    }
                    $goods[$key]['reclevel'] = com_run('sale::getReclevel', $value['reclevel']); //商品营销主推级别
                    //分销预计收益
                    if ($value['nocommission'] == 0) {
                        //seckill 秒杀--不参与分润
                        if (p('seckill')) {
                            if (p('seckill')->getSeckill($value['id'])) {
                                continue;
                            }
                        }
                        //bargain 砍价--不参与分润
                        if (p('bargain')) {
                            if ($value['bargain'] > 0) {
                                continue;
                            }
                        }
                        $goods[$key]['seecommission'] = $DiypageModel->getCommission($value, $level, $set);
                        if ($goods[$key]['seecommission'] > 0) {
                            $goods[$key]['seecommission'] = round($goods[$key]['seecommission'], 2);
                        }
                        $goods[$key]['cansee']   = $set['cansee'];
                        $goods[$key]['seetitle'] = $set['seetitle'];
                    } else {
                        $goods[$key]['seecommission'] = 0;
                        $goods[$key]['cansee']        = $set['cansee'];
                        $goods[$key]['seetitle']      = $set['seetitle'];
                    }
                    //店员预计收益
                    if ($value['noClerkCommission'] == 0) {
                        //seckill 秒杀--不参与分润
                        if (p('seckill')) {
                            if (p('seckill')->getSeckill($value['id'])) {
                                continue;
                            }
                        }
                        //bargain 砍价--不参与分润
                        if (p('bargain')) {
                            if ($value['bargain'] > 0) {
                                continue;
                            }
                        }
                        $goods[$key]['clerkSeecommission'] = $clerk->getGoodsCommission($value, $clerklevel, $options);
                        if ($goods[$key]['clerkSeecommission'] > 0) {
                            $goods[$key]['clerkSeecommission'] = round($goods[$key]['clerkSeecommission'], 2);
                        }
                        $goods[$key]['clerkCansee']     = $clerk_set['cansee'];
                        $goods[$key]['clerkSeetitle']   = $clerk_set['seetitle'];
                        $goods[$key]['is_show_revenue'] = $myshop['is_show_revenue'];
                    } else {
                        $goods[$key]['clerkSeecommission'] = 0;
                        $goods[$key]['clerkCansee']        = $clerk_set['cansee'];
                        $goods[$key]['clerkSeetitle']      = $clerk_set['seetitle'];
                        $goods[$key]['is_show_revenue']    = $myshop['is_show_revenue'];
                    }

                }
                foreach ($goods as $index => $good) {
                    $showgoods = m('goods')->visit($good, $this->member);
                    if (!empty($showgoods)) {
                        $childid                = rand(1000000000, 9999999999);
                        $childid                = 'C' . $childid;
                        $item['data'][$childid] = [
                            'thumb'               => $good['thumb'],
                            'title'               => $good['title'],
                            'subtitle'            => $good['subtitle'],
                            'price'               => $good['minprice'],
                            'maxMemberLevelPrice' => $good['maxMemberLevelPrice'],
                            'maxlevel'            => $maxlevel,
                            'gid'                 => $good['id'],
                            'stock'               => $good['stock'],
                            'bargain'             => $good['bargain'],
                            'productprice'        => $good['productprice'],
                            'credit'              => $good['credit'],
                            'seecommission'       => $good['seecommission'],
                            'cansee'              => $good['cansee'],
                            'seetitle'            => $good['seetitle'],
                            'clerkSeecommission'  => $good['clerkSeecommission'],
                            'clerkCansee'         => $good['clerkCansee'],
                            'clerkSeetitle'       => $good['clerkSeetitle'],
                            'ctype'               => $good['type'],
                            'gtype'               => $good['goodstype'],
                            'sales'               => $good['sales'] + intval($good['salesreal']), //销量
                            'is_show_revenue'     => $good['is_show_revenue'],
                            'medicineAttributeID' => $good['medicineAttributeID'], //药品属性
                            'goodsClassID'        => $good['goodsClassID'], //商品类目
                            'reclevel'            => $good['reclevel'], //商品主推级别
                        ];
                    }
                }
            }
        }
        $content = template('goods/diypage',TEMPLATE_FETCH,['diyitem'=>$item]);
        // 输出渲染的内容
        return app_json(array('total'=>count($item['data']),'content' => base64_encode($content)));
    }
}
