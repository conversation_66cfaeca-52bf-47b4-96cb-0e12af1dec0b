<?php
namespace app\controller\app\poster;

use app\com\validate\MemberValidate;
use app\controller\AppMobilePage;
use app\mobile\poster\Validate\ShareValidate;
use app\model\GoodsModel;
use app\model\MemberModel;
use app\model\PosterModel;
use app\model\QrcodeModel;
use app\model\UserpromoteModel;
use think\Request;

class ShareController extends AppMobilePage
{

    /**
     * 获取分享链接
     * @param Request $request
     * @return \think\response\Json
     */
    public function main(Request $request)
    {
        global $_W, $_GPC;
        $_W['container'] = 'wxapp';
        $_W['ispost'] = true;
        $_W['openid'] = $this->memberInfo['openid'];
        $_GPC['mid'] = $this->memberInfo['id'];

        $params = (new ShareValidate())->post()->goCheck('Main');

        switch ($params['type']) {
            case 'clerk':
                // 实例化QrcodeController类的对象
                $qrcodeController = new \app\controller\clerk\QrcodeController();
                // 调用clerk对象qrcode的main方法
                $result = $qrcodeController->main(true, true);
                break;
            case 'user':
//                // 实例化QrcodeController类的对象
//                $qrcodeController = new \app\controller\userpromote\QrcodeController();
//                // 调用clerk对象qrcode的main方法
//                $result = $qrcodeController->main(true, true);
                // 失败了因为 父类this->set = $this->model->getSet()调用的和源controller用的不是一个对象导致出错

                $p   = p('poster');
                $img = array();
                //使用超级海报的关注海报
                if ($p) {
                    $set = (new UserpromoteModel())->getSet();
                    if (!empty($set['qrcode'])) {
                        $posterid = $set['posterid'];
                    }
                    $p->setIsMiniApp(true);
                    $img = $p->createCommissionPoster($this->memberId, 0, 4, $posterid);//openid,商品ID，海报类型4关注海报
                }
                $result = array('img' => $img['img'] . "?t=" . TIMESTAMP);
                break;
            case 'goods':
                $id = $params['id'];
                $goods = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id limit 1", array(':id' => $id));
                if (empty($goods)) return $this->fail('商品不存在');

                $alllevels = m('member')->getLevels();
                foreach($alllevels as $v){
                    //获取最高的会员等级
                    $maxlevel = max($v,$v['level']);
                }

                $seckillinfo = false;
                $seckill  = p('seckill');
                if($seckill){
                    $time = time();
                    $seckillinfo = $seckill->getSeckill($goods['id'],0,false, $this->memberInfo['openid']);
                    if(!empty($seckillinfo)){
                        if($time >= $seckillinfo['starttime'] && $time<$seckillinfo['endtime']){
                            $seckillinfo['status'] = 0;
                        }elseif( $time < $seckillinfo['starttime'] ){
                            $seckillinfo['status'] = 1;
                        }else {
                            $seckillinfo['status'] = -1;
                        }
                    }
                }

                //商品二维码
                $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);//最大会员等级价格
                $controller = new \app\controller\goods\DetailController();
                $goodscode = $controller->get_code($maxMemberLevelPrice, $seckillinfo['price'], true);

                $result = ['img'=>$goodscode];
                break;
            case 'member_card':
                $id = $params['id'];
                $_GPC['poster_id'] = $params['poster_id'];
                // 实例化QrcodeController类的对象
                $qrcodeController = new \app\controller\membercard\QrcodeController();
                // 调用clerk对象qrcode的main方法
                $result = $qrcodeController->main(true, true);
                break;
            default:
                return $this->fail('参数错误');
        }

        if (isset($result['msg'])) return $this->fail($result['msg']);

        return $this->succeed('success', ['image'=>$result['img']]);
    }


    public function bind(Request $request)
    {
        $mid = request()->post('mid', 0);
        if (isset($mid) && is_int($mid)) {
            $params = (new ShareValidate())->post()->goCheck();
            $memberModel = new MemberModel();
            $memberModel->relationBinding($this->memberId, $params['mid']);   
        }

        return $this->succeed('success');
    }
}
