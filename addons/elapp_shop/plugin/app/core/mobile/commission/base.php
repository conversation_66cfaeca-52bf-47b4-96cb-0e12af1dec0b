<?php
namespace app\controller\app\commission;
use app\controller\AppMobilePage;
use app\controller\AppError;

class BaseController extends AppMobilePage
{
    public function __construct()
    {
        parent::__construct();
        global $_W, $_GPC;
        if ($_W["action"] != "commission.register" && $_W["action"] != "myshop" && $_W["action"] != "share") {
            $member = $this->member;
//            if ($member["isagent"] != 1 || $member["status"] != 1) {
//                exit(app_error(AppError::$CommissionReg, $_W["openid"] . "+" . $member["openid"]));
//            }
        }
        $this->model = p("commission");
        $this->set = $this->model->getSet();
    }
}