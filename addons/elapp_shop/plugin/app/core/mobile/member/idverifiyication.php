<?php
namespace app\controller\app\member;
use app\controller\AppMobilePage;
use app\core\model\member\MemberVerifyModel;

class IdverifyicationController extends AppMobilePage
{
    
    public function detail(){
        $param = request()->param();
        $memberVerifyResult = [];
        if (!empty($param['id'])) {
            if (empty($param['idcard'])) { //编辑进来只有id
                $memberVerifyResult = (new MemberVerifyModel())->where(['id'=>$param['id']])->findOrEmpty()->toArray();
            } else { //兼容由银行卡提现的情况
                $memberVerifyResult = $param;
            }
        }
        return app_json(array('info' => $memberVerifyResult));
    }

    function getList()
    {
        global $_W, $_GPC;        
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $memberVerifyResult = (new MemberVerifyModel())->page($pindex,$psize)->where(['member_id' => $this->memberId])->select()->toArray();
        $total = (new MemberVerifyModel())->where(['member_id' => $this->memberId])->count();
        return app_json(array('page' => $pindex, 'pagesize' => $psize, 'total' => $total, 'list' => $memberVerifyResult));
    }
}