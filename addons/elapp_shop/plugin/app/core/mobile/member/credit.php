<?php
namespace app\controller\app\member;

use app\controller\AppMobilePage;

class CreditController extends AppMobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $_GPC['type'] = intval($_GPC['type']);
        $openid = $this->openid;
        $member       = m('member')->getMember($_W['openid'], true);
        $showTransfer = false;
        if (p('transfer')) {
            $set = p('transfer')->getSet();
            if ($set['credit'] == 1) {
                $showTransfer = true;
            }
        }
        $credit1 = m('member')->getCredit($openid, 'credit1');
        return app_json(['data' => ['showTransfer' => $showTransfer, 'credit' => $credit1]]);
    }

    public function get_list()
    {
        global $_W, $_GPC;
        $type             = intval($_GPC['type']);
        $listType         = $_GPC['list_type'];
        $pindex           = max(1, intval($_GPC['page']));
        $psize            = 10;
        $credit_condition = ' and r.uniacid=' . $_W['uniacid'] . (' and r.credittype=\'credit1\' and r.openid = \'' . $_W['openid'] . '\'  ');

        if ($listType == 1) {
            $credit_condition .= ' and (r.num < 0) ';
        } else {
            $credit_condition .= ' and (r.num > 0) ';
        }

        $list  = pdo_fetchall('select m.uid,m.mobile,m.nickname,r.remark title,r.num money,r.createtime from ' . tablename('elapp_shop_member_credit_record') . 'r left join ' . tablename('elapp_shop_member') . ' m on m.openid = r.openid where 1 ' . $credit_condition . ' order by r.createtime desc  LIMIT ' . ($pindex - 1) * $psize . ',' . $psize);
        $total = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member_credit_record') . 'r left join ' . tablename('elapp_shop_member') . ' m on m.uid = r.uid where 1 ' . $credit_condition);

        foreach ($list as &$item) {
            $item['createtime']   = date('Y-m-d H:i:s', $item['createtime']);
            $item['rechargetype'] = 'credit';
        }

        unset($item);
        return app_json(['list' => $list, 'total' => $total, 'pagesize' => $psize, 'isopen' => $_W['shopset']['trade']['withdraw'], 'credittext' => $_W['shopset']['trade']['credittext']]);
    }
}
