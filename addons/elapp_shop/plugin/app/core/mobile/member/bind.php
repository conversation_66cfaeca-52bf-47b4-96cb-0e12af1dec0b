<?php

namespace app\controller\app\member;

use app\controller\AppMobilePage;
use app\controller\AppError;

class BindController extends AppMobilePage
{

    protected $member;

    function __construct()
    {
        global $_W;

        parent::__construct();

        $this->member = m('member')->getInfo($_W['openid']);

        if ($this->iswxapp) {
            $needbind = false;
            if ((empty($_W['shopset']['app']['isclose']) && !empty($_W['shopset']['app']['openbind'])) || !empty($_W['shopset']['wap']['open'])) {
                $needbind = true;
            }
            if (!$needbind) {
                return app_error(AppError::$BindNotOpen);
            }
        }
    }

    function main()
    {
        global $_W;
        $member = $this->member;
        $key_time = '__elapp_shop_member_verifycodesendtime_' . $_W['uniacid'];
        $sendtime = m('cache')->get($key_time);
        if (empty($sendtime) || $sendtime + 60 < time()) {
            $endtime = 0;
        } else {
            $endtime = 60 - (time() - $sendtime);
        }
        $memberArr = array(
            'mobile' => $member['mobile']
        );
        $wapset = m('common')->getSysset('wap');

        $verifycode_img = webUrl('app.sms.captcha') . '&t=' . time() . '&openid=' . $_W['openid'];
        return app_json(array(
            'member' => $memberArr,
            'binded' => !empty($member['mobile']) && !empty($member['mobileverify']) ? 1 : 0,
            'endtime' => $endtime,
            'smsimgcode' => $wapset['smsimgcode'],
            'verifycode_img' => $verifycode_img
        ));
    }

    function submit()
    {
        global $_W, $_GPC;

        if ($_W['ispost']) {
            @session_start();
            $member = $this->member;
            if(empty($member['id'])){
                return app_error(2000,'会员数据出错');
            }
            $wapset = m('common')->getSysset('wap');
            $appset = m('common')->getSysset('app');
            if(!p('threen')) {
                if (empty($wapset['open']) && !empty($appset['isclose'])) {
                    return app_error('未开启绑定设置');
                }
            }

            $bind = !empty($member['mobile']) && !empty($member['mobileverify']) ? 1 : 0;
            $mobile = trim($_GPC['mobile']);
            $verifyImgCode = trim($_GPC['verifycode']);
            $code = intval($_GPC['code']);
            $pwd = trim($_GPC['pwd']);
            $confirm = intval($_GPC['confirm']);

            if (!empty($wapset["smsimgcode"])) {
                if (empty($verifyImgCode)) {
                    return app_error(AppError::$ParamsError,"请输入图形验证码");
                }

                $verifyCodeKey = "sms_captcha_code_uniaicid_" . $_W["uniacid"] . "_openid_" . $_W["openid"];
                $verifyCode = m("cache")->get($verifyCodeKey);
                $imgcodehash = md5(strtolower($verifyImgCode) . $_W["config"]["setting"]["authkey"]);
                if ($imgcodehash != trim($verifyCode)) {
                    return app_error(AppError::$ParamsError, "图形验证码错误");
                }
                m('cache')->del($verifyCodeKey);
            }
            if(!$code){
                return app_error(AppError::$ParamsError,'请输入验证码');
            }
            $smskey = '__elapp_shop_member_verifycodesession_' . $_W['uniacid'] . '_' . $mobile;
            $verifysmscode = m("cache")->get($smskey);
            if(!$verifysmscode || $verifysmscode != $code){
                return app_error(AppError::$ParamsError,'验证码错误或已过期');
            }
            

            $member2 = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where mobile=:mobile and uniacid=:uniacid and mobileverify=1 limit 1', array(':mobile' => $mobile, ':uniacid' => $_W['uniacid']));

            if (empty($member2)) {
                $salt = m('account')->getSalt();
                m('bind')->update($member['id'], array('mobile' => $mobile, 'pwd' => md5($pwd . $salt), 'salt' => $salt, 'mobileverify' => 1));
                m('cache')->del($smskey);
                m('account')->setLogin($member['id']);

                // 绑定手机号送积分
                if($bind == 0){
                    m('bind')->sendCredit($member);
                }

                /* hlei ******** start-------------------> */
                if (empty($member['mobileverify']) && empty($member['mobileone'])) {
                    if (p('userpromote')) {
                        p('userpromote')->sendCredit($member);
                    }
                    m('bind')->update($member['id'], array('mobileone' => 1));
                }
                if (p('task')){//##任务中心
                    p('task')->checkTaskReward('member_info',1,$_W['openid']);
                }
                if (p('task')){//##任务中心
                    p('task')->checkTaskProgress(1,'info_phone');
                }

                return app_json();
            }


            if ($member['id'] == $member2['id']) {
                return app_error(AppError::$BindSelfBinded);
            }

            // 如果 两用户都是 微信用户
            if (m('bind')->iswxm($member) && m('bind')->iswxm($member2)) {
                if ($confirm) {
                    $salt = m('account')->getSalt();
                    m('bind')->update($member['id'], array('mobile' => $mobile, 'pwd' => md5($pwd . $salt), 'salt' => $salt, 'mobileverify' => 1));
                    m('bind')->update($member2['id'], array('mobileverify' => 0));
                    m('cache')->del($smskey);
                    m('account')->setLogin($member['id']);
                    if (p('task')){//##任务中心
                        p('task')->checkTaskReward('member_info',1,$_W['openid']);
                    }
                    if (p('task')){//##任务中心
                        p('task')->checkTaskProgress(1,'info_phone');
                    }
                    return app_json();
                } else {
                    return app_error(AppError::$BindWillRelieve, "此手机号已与其他帐号绑定, 如果继续将会解绑之前帐号, 确定继续吗？");
                }
            }

            // 判断 member2 不是是微信用户
            if (!m('bind')->iswxm($member2)) {
                if ($confirm) {
                    $result = m('bind')->merge($member2, $member);
                    if (empty($result['errno'])) {
                        return app_error(AppError::$BindError, $result['message']);
                    }
                    $salt = m('account')->getSalt();
                    m('bind')->update($member['id'], array('mobile' => $mobile, 'pwd' => md5($pwd . $salt), 'salt' => $salt, 'mobileverify' => 1));
                    m('cache')->del($smskey);
                    m('account')->setLogin($member['id']);
                    return app_json();
                } else {
                    return app_error(AppError::$BindWillMerge, "此手机号已通过其他方式注册, 如果继续将会合并账号信息, 确定继续吗？");
                }
            }

            // 判断 member 不是微信用户
            if (!m('bind')->iswxm($member)) {
                if ($confirm) {
                    $result = m('bind')->merge($member, $member2);
                    if (empty($result['errno'])) {
                        return app_error(AppError::$BindError, $result['message']);
                    }
                    $salt = m('account')->getSalt();
                    m('bind')->update($member2['id'], array('mobile' => $mobile, 'pwd' => md5($pwd . $salt), 'salt' => $salt, 'mobileverify' => 1));
                    m('cache')->del($smskey);
                    m('account')->setLogin($member2['id']);
                    return app_json();
                } else {
                    return app_error(AppError::$BindWillMerge, "此手机号已通过其他方式注册, 如果继续将会合并账号信息, 确定继续吗？");
                }
            }
        }
        return app_json();
    }

    function imageChange()
    {
        global $_W, $_GPC;
        $verifycode_img = webUrl('app.sms.captcha') . '&t=' . time() . '&openid=' . $_W['openid'];
        return app_json(array(
            'verifycode_img' => $verifycode_img,
        ));
    }
}