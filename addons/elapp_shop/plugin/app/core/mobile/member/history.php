<?php

namespace app\controller\app\member;

use app\controller\AppMobilePage;
use app\controller\AppError;

class HistoryController extends AppMobilePage
{
    public function get_list()
    {
        global $_W, $_GPC;
        file_put_contents('/tmp/get_list.log', json_encode($_GPC) . "\n", FILE_APPEND);
        file_put_contents('/tmp/get_list.log', json_encode($_W) . "\n", FILE_APPEND);
        file_put_contents('/tmp/get_list.log', json_encode($_W['openid']) . "\n", FILE_APPEND);
        file_put_contents('/tmp/get_list.log', json_encode($_W['openid']) . "\n", FILE_APPEND);
        file_put_contents('/tmp/get_list.log', json_encode($this->memberInfo) . "\n", FILE_APPEND);
        file_put_contents('/tmp/get_list.log', json_encode($this->member) . "\n", FILE_APPEND);
        $pindex = max(1, intval($_GPC["page"]));
        $psize = 10;
        $condition = " and f.uniacid = :uniacid and f.openid=:openid and f.deleted=0";
        $params = array(":uniacid" => $_W["uniacid"], ":openid" => $_W["openid"]);
        $sql = "SELECT COUNT(*) FROM " . tablename("elapp_shop_member_history") . " f where 1 " . $condition;
        $total = pdo_fetchcolumn($sql, $params);
        $sql = "SELECT f.id,f.goodsid,g.title,g.thumb,g.marketprice,g.productprice,f.createtime,g.merchid FROM " . tablename("elapp_shop_member_history") . " f " . " left join " . tablename("elapp_shop_goods") . " g on f.goodsid = g.id " . " where 1 " . $condition . " ORDER BY `id` DESC LIMIT " . ($pindex - 1) * $psize . "," . $psize;
        $list = pdo_fetchall($sql, $params);
        $result = array("list" => array(), "total" => $total, "pagesize" => $psize);
        $merch_plugin = p("merch");
        $merch_data = m("common")->getPluginset("merch");
        if (!empty($list) && $merch_plugin && $merch_data["is_openmerch"]) {
            $merch_user = $merch_plugin->getListUser($list, "merch_user");
            $result["openmerch"] = 1;
        }

        foreach ($list as &$row) {
            $row["createtime"] = date("Y-m-d H:i:s", $row["createtime"]);
            $row["thumb"] = tomedia($row["thumb"]);
            $row["merchname"] = ($merch_user[$row["merchid"]]["merchname"] ? $merch_user[$row["merchid"]]["merchname"] : $_W["shopset"]["shop"]["name"]);
        }
        unset($row);
        $result["list"] = $list;
        return app_json($result);
    }

    public function remove()
    {
        global $_W, $_GPC;
        $ids = $_GPC["ids"];
        if (empty($ids) || !is_array($ids)) {
            return app_error(AppError::$ParamsError);
        }

        $sql = "update " . tablename("elapp_shop_member_history") . " set deleted=1 where openid=:openid and id in (" . implode(",", $ids) . ")";
        pdo_query($sql, array(":openid" => $_W["openid"]));
        return app_error();
    }

}