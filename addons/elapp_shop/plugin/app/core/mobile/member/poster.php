<?php
namespace app\controller\app\member;

use app\com\logic\copartner\CopartnerLogic;
use app\com\logic\MemberLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\model\CopartnerModel;
use app\model\MemberModel;
use app\model\UserpromoteModel;
use think\Request;

class PosterController extends AppMobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $member    = (new MemberModel())->getMember($this->memberId);

        $model = new UserpromoteModel();
        $set = $model->getSet();

        if (!empty($set['closed_qrcode'])) {
//            $this->message('没有开启分享海报!', mobileUrl('userpromote'), 'info');
            return JsonService::fail('没有开启分享海报!');
        }
        $posterid = '';
        if (!empty($set['qrcode'])) {
            $posterid = $set['posterid'];
        }

        $p   = p('poster');
        $img = array();
        //使用超级海报的关注海报
        if (!empty($set['qrcode'])) {
            if ($p) {
                $img = $p->createCommissionPoster($member['openid'], 0, 4, $posterid);//openid,商品ID，海报类型4关注海报
            }
        } else {//使用会员功能的独立海报
            $img = $this->model->createMyImage();
        }

        return $this->succeed('success', ['image'=>$img['img']]);
    }
}
