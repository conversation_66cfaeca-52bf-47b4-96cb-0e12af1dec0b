<?php

namespace app\controller\app\member;

use app\com\logic\MemberLogic;
use app\com\validate\MemberValidate;
use app\controller\AppMobilePage;
use app\controller\AppError;
use app\core\model\member\MemberVerifyModel;
use salary\Salary;

class IndexController extends AppMobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $member = $this->member;

        if (empty($member)) {
            return app_error(AppError::$UserNotFound);
        }


        $level = m('member')->getLevel($_W['openid']);
        $open_creditshop = p('creditshop') && $_W['shopset']['creditshop']['centeropen'];
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $statics = array('order_0' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and status=0  and uniacid=:uniacid limit 1', $params), 'order_1' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and status=1 and refundid=0 and uniacid=:uniacid limit 1', $params), 'order_2' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and status=2 and refundid=0 and uniacid=:uniacid limit 1', $params), 'order_4' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and refundstate=1 and uniacid=:uniacid limit 1', $params), 'cart' => pdo_fetchcolumn('select ifnull(sum(total),0) from ' . tablename('elapp_shop_member_cart') . ' where uniacid=:uniacid and openid=:openid and deleted=0 ', $params), 'favorite' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member_favorite') . ' where uniacid=:uniacid and openid=:openid and deleted=0 ', $params));
        } else {
            $statics = array('order_0' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and ismr=0 and status=0  and uniacid=:uniacid and isparent=0 limit 1', $params), 'order_1' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and ismr=0 and status=1 and refundid=0 and uniacid=:uniacid and isparent=0 limit 1', $params), 'order_2' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and ismr=0 and status=2 and refundid=0 and uniacid=:uniacid and isparent=0 limit 1', $params), 'order_4' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and ismr=0 and refundstate=1 and uniacid=:uniacid and isparent=0 limit 1', $params), 'cart' => pdo_fetchcolumn('select ifnull(sum(total),0) from ' . tablename('elapp_shop_member_cart') . ' where uniacid=:uniacid and openid=:openid and deleted=0 and selected = 1', $params), 'favorite' => pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member_favorite') . ' where uniacid=:uniacid and openid=:openid and deleted=0 and `type`=0', $params));
        }

        $hascoupon = false;
        $hascouponcenter = false;
        $plugin_coupon = com('coupon');

        if ($plugin_coupon) {
            $time = time();
            $sql = 'select count(*) from ' . tablename('elapp_shop_coupon_data') . ' d';
            $sql .= ' left join ' . tablename('elapp_shop_coupon') . ' c on d.couponid = c.id';
            $sql .= ' where d.openid=:openid and d.uniacid=:uniacid and  d.used=0 ';
            $sql .= ' and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=' . $time . ' ) )  or  (c.timelimit =1 and c.timestart<=' . $time . ' && c.timeend>=' . $time . ')) order by d.gettime desc';
            $statics['coupon'] = pdo_fetchcolumn($sql, array(':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']));
            $pcset = $_W['shopset']['coupon'];

            if (empty($pcset['closemember'])) {
                $hascoupon = true;
                $coupon_text = '领取优惠券';
            }


            if (empty($pcset['closecenter'])) {
                $hascouponcenter = true;
            }


            $couponcenter_text = '我的优惠券';
        }


        $hasglobonus = false;
        $plugin_globonus = p('globonus');

        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !(empty($plugin_globonus_set['open'])) && !(empty($plugin_globonus_set['openmembercenter']));
        }


        $hasabonus = false;
        $plugin_abonus = p('abonus');

        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !(empty($plugin_abonus_set['open'])) && !(empty($plugin_abonus_set['openmembercenter']));

            if ($hasabonus) {
                $abonus_text = m('plugin')->getPluginName('abonus');

                if (empty($abonus_text)) {
                    $abonus_text = '区域代理';
                }

            }

        }


        $hasqa = false;
        $plugin_qa = p('qa');

        if ($plugin_qa) {
            $plugin_qa_set = $plugin_qa->getSet();

            if (!(empty($plugin_qa_set['showmember']))) {
                $hasqa = true;
                $qa_text = m('plugin')->getPluginName('qa');

                if (empty($qa_text)) {
                    $qa_text = '帮助中心';
                }

            }

        }


        $hassign = false;
        $com_sign = p('sign');

        if ($com_sign) {
            $com_sign_set = $com_sign->getSet();

            if (!(empty($com_sign_set['iscenter']))) {
                $hassign = true;
                $sign_text = ((empty($_W['shopset']['trade']['credittext']) ? '积分' : $_W['shopset']['trade']['credittext']));
                $sign_text .= ((empty($com_sign_set['textsign']) ? '签到' : $com_sign_set['textsign']));
                $url_sign = mobileUrl('sign', NULL, true);
                $sign_url_arr = explode('?', $url_sign);
                $sign_url_domain = $sign_url_arr[0];
                $sign_url_params = urlencode($sign_url_arr[1]);

                if (empty($sign_text)) {
                    $sign_text = '积分签到';
                }

            }

        }


        $commission = false;
        $commission_text = '';
        $commission_url = '';
        if (p('commission') && intval(0 < $_W['shopset']['commission']['level']) && empty($_W['shopset']['app']['hidecom'])) {
            $commission = true;

            if (!($member['agentblack'])) {
                if (($member['isagent'] == 1) && ($member['status'] == 1)) {
                    $commission_url = '/pages/transfer/commission/index';
                    $commission_text = ((empty($_W['shopset']['commission']['texts']['center']) ? '分销中心' : $_W['shopset']['commission']['texts']['center']));
                } else {
                    $commission_url = '/pages/transfer/commission/index';
                    $commission_text = ((empty($_W['shopset']['commission']['texts']['become']) ? '成为分销商' : $_W['shopset']['commission']['texts']['become']));
                }
            }

        }


        $copyright = m('common')->getCopyright();
        $hasFullback = true;
        $ishidden = m('common')->getSysset('fullback');

        if ($ishidden['ishidden'] == true) {
            $hasFullback = false;
        }


        $haveverifygoods = m('verifygoods')->checkhaveverifygoods($_W['openid']);

        if (!(empty($haveverifygoods))) {
            $verifygoods = m('verifygoods')->getCanUseVerifygoods($_W['openid']);
        }


        $usemembercard = false;
        $hasmembercard = false;
        $hasbuycardnum = 0;
        $allcardnum = 0;
        $plugin_membercard = p('membercard') && m('plugin')->permission('membercard');

        if ($plugin_membercard) {
            $usemembercard = true;
            $card_condition = 'openid =:openid and uniacid=:uniacid and isdelete=0';
            $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
            $now_time = TIMESTAMP;
            $card_condition .= ' and (expire_time=-1 or expire_time>' . $now_time . ')';
            $card_history = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_member_card_history') . ' ' . "\n\t\t\t\t" . 'WHERE ' . $card_condition . ' limit 1', $params);

            if ($card_history) {
                $hasmembercard = true;
                $hasbuycardnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename('elapp_shop_member_card_history') . ' ' . "\n\t\t\t\t" . 'WHERE ' . $card_condition . ' limit 1', $params);
            }


            $allcard_condition = ' uniacid = :uniacid ';
            $allcard_params = array(':uniacid' => $_W['uniacid']);
            $allcard_condition .= ' and status=1 and isdelete=0';
            $allcardnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename('elapp_shop_member_card') . 'where ' . $allcard_condition, $allcard_params);
        }


        if ($usemembercard && ($hasbuycardnum == 0) && ($allcardnum == 0)) {
            $usemembercard = false;
        }


        $result = array('id' => $member['id'], 'avatar' => $member['avatar'], 'nickname' => $member['nickname'], 'moneytext' => $_W['shopset']['trade']['moneytext'], 'credittext' => $_W['shopset']['trade']['credittext'], 'credit1' => $member['credit1'], 'credit2' => $member['credit2'], 'mobile' => $member['mobile'], 'open_recharge' => (empty($_W['shopset']['trade']['closerecharge']) ? 1 : 0), 'open_creditshop' => intval($open_creditshop), 'open_withdraw' => intval($_W['shopset']['trade']['withdraw']), 'logtext' => ($_W['shopset']['trade']['withdraw'] == 1 ? $_W['shopset']['trade']['moneytext'] . '明细' : '充值记录'), 'levelurl' => ($_W['shopset']['shop']['levelurl'] == NULL ? '' : $_W['shopset']['shop']['levelurl']), 'levelname' => (empty($level['id']) ? ((empty($_W['shopset']['shop']['levelname']) ? '普通会员' : $_W['shopset']['shop']['levelname'])) : $level['levelname']), 'statics' => $statics, 'isblack' => $member['isblack'], 'haveverifygoods' => $haveverifygoods, 'verifygoods' => $verifygoods, 'hascoupon' => $hascoupon, 'hasFullback' => $hasFullback, 'fullbacktext' => m('sale')->getFullBackText(), 'coupon_text' => $coupon_text, 'hascouponcenter' => $hascouponcenter, 'couponcenter_text' => $couponcenter_text, 'usemembercard' => $usemembercard, 'hasmembercard' => $hasmembercard, 'hasbuycardnum' => $hasbuycardnum, 'allcardnum' => $allcardnum, 'hasabonus' => $hasabonus, 'abonus_text' => $abonus_text, 'commission' => $commission, 'commission_text' => $commission_text, 'commission_url' => $commission_url, 'hasqa' => $hasqa, 'qa_text' => $qa_text, 'hassign' => $hassign, 'sign_text' => $sign_text, 'sign_url_domain' => $sign_url_domain, 'sign_url_params' => $sign_url_params, 'hasrank' => intval($_W['shopset']['rank']['status']) == 1, 'rank_text' => '积分排行', 'hasorderrank' => intval($_W['shopset']['rank']['order_status']) == 1, 'orderrank_text' => '消费排行', 'copyright' => (!(empty($copyright)) && !(empty($copyright['copyright'])) ? $copyright['copyright'] : ''), 'customer' => intval($_W['shopset']['app']['customer']), 'phone' => intval($_W['shopset']['app']['phone']));

        if (!(empty($result['customer']))) {
            $result['customercolor'] = ((empty($_W['shopset']['app']['customercolor']) ? '#ed6d0f5' : $_W['shopset']['app']['customercolor']));
        }


        if (!(empty($result['phone']))) {
            $result['phonecolor'] = ((empty($_W['shopset']['app']['phonecolor']) ? '#ed6d0f' : $_W['shopset']['app']['phonecolor']));
            $result['phonenumber'] = ((empty($_W['shopset']['app']['phonenumber']) ? '#ed6d0f' : $_W['shopset']['app']['phonenumber']));
        }


        if (empty($member['mobileverify']) || empty($member['mobile'])) {
            if (!(empty($_W['shopset']['app']['openbind'])) || !(empty($_W['shopset']['wap']['open']))) {
                $result['needbind'] = 1;

                $result['bindtext'] = ((!(empty($_W['shopset']['app']['openbind'])) && !(empty($_W['shopset']['app']['bindtext'])) ? $_W['shopset']['app']['bindtext'] : '绑定手机号可合并或同步您其他账号数据'));
            }

        }


        $result['iscycelbuy'] = $this->pluginPermissions('cycelbuy');
        $plugin_bargain = p('bargain');
        $result['bargain'] = $plugin_bargain;
        $hasdividend = false;
        $dividend = p('dividend');

        if ($dividend) {
            $plugin_dividend_set = $dividend->getSet();

            if (!(empty($plugin_dividend_set['open'])) && !(empty($plugin_dividend_set['membershow']))) {
                $hasdividend = true;
            }

        }


        $result['hasdividend'] = $hasdividend;
        $result['isheads'] = $member['isheads'];
        $result['headsstatus'] = $member['headsstatus'];
        return app_json($result);
    }

    private function pluginPermissions($pluginName)
    {
        return m('plugin')->permission($pluginName);
    }

    public function get_member()
    {
        global $_W, $_GPC;
        $openid = $_GPC['shar_openid'];
        $member = pdo_get('elapp_shop_member', array('uniacid' => $_W['uniacid'], 'openid' => $openid));

        if (empty($member)) {
            $member = pdo_get('elapp_shop_member', array('uniacid' => $_W['uniacid'], 'openid' => 'sns_wa_' . $openid));
        }


        return app_json(array('member' => $member));
    }

    /**
     * 获取用户积分/余额
     */
    public function getUserCredit()
    {
        $member = $this->member;
        return app_json(array('member' => $member));
    }

    /**
     * 个人实名认证
     * @return false|float|int|mixed|\Services_JSON_Error|string
     */
    function userVerify()
    {
        if ($this->request->isPost()) {
            $return = [];
            $code = 1;
            $is_user_verify = $is_user_sign = 0;
            $params = (new MemberValidate())->post()->goCheck('UserVerify');
            $user_info = array_diff_key($params, ['is_sign' => $params['is_sign'], 'i' => $params['i']]);
            $isUserVerifyResult = MemberLogic::isUserVerify($this->memberId, $user_info);
            if (!empty($isUserVerifyResult['is_verify'])) { //是否已经实名验证
                $is_user_verify = 1;
            } else {
                $result = (new Salary())->userVerify($user_info);
                $result = json_decode($result, true);
                if (200 == $result['code']) {
                    $return['message'] = '实名认证成功，接下来请签署协议';
                    $is_user_verify = 1;
                    MemberVerifyModel::update(['is_verify' => 1, 'verify_time' => time()], ['idcard' => $params['idcard'], 'realname' => $params['realname']]);
                } else {
                    $return['message'] = '实名认证失败：' . $result['msg'];
                }
            }
            if (1 == $is_user_verify) {
                //是否已经签署协议
                $isUserContractSignResult = MemberLogic::isUserContractSign($params['realname'], $params['idcard']);
                if (!empty($isUserContractSignResult)) {
                    if ('complete' == $isUserContractSignResult['status']) {
                        $is_user_sign = 1;
                        $code = 0;
                        $return['message'] = '您已经实名认证并签署协议，无需再认证';
                    } else if (in_array($isUserContractSignResult['status'], ['waiting', 'dealinq']) || null == $isUserContractSignResult['status']) {
                        $user_sign_result = MemberLogic::userContractDetail(['sn' => $isUserContractSignResult['sn']]);
                        $user_sign_result = json_decode($user_sign_result, true);
                        if ('complete' == $user_sign_result['data']['status']) {
                            $is_user_sign = 1;
                            $code = 0;
                            $return['message'] = '您已经实名认证并签署协议，无需再认证';
                        } else if(in_array($user_sign_result['data']['status'], ['waiting', 'dealinq'])) {
                            $return['message'] = '实名认证成功，请签署协议';
                            $return['sign_url'] = $isUserContractSignResult['sign_url'];
                        }
                    } else {
                        $is_user_sign = -1;
                        $return['message'] = '签署协议已被取消或签署失败';
                    }
                } else {
                    //获取平台协议签署
                    $params['member_id'] = $this->memberId;
                    $userContractSignResult = MemberLogic::userContractSign($params);
                    $userContractSignResult = json_decode($userContractSignResult, true);
                    if (200 == $userContractSignResult['code']) {
                        $return['sign_url'] = $userContractSignResult['data']['sign_url'];
                        $return['sn'] = $userContractSignResult['data']['sn'];
                        $return['message'] = '实名认证成功，请签署协议';
                    } else {
                        $return['message'] = $userContractSignResult['msg'];
                        if ('合同已签署，无需重复签署' == $userContractSignResult['msg']) {
                            $is_user_sign = 1;
                            $code = 0;
                        }
                    }
                }
            }
            $return['is_user_verify'] = $is_user_verify;
            $return['is_user_sign'] = $is_user_sign;
            return $this->succeed($return['message'], $return, $code, 1);
        }
    }
}