<?php

namespace app\controller\app\member;

use app\controller\AppMobilePage;
use app\controller\AppError;

class WxrunController extends AppMobilePage
{
    /* 解密微信步数 */
    public function set_yesterday()
    {
        global $_W, $_GPC;
        $credit_data = m('common')->getSysset('app');
        $wxrunstatus = !empty($credit_data['wxrunstatus']) ? $credit_data['wxrunstatus'] : 0;
        $wxruncredit1 = !empty($credit_data['wxruncredit1']) ? $credit_data['wxruncredit1'] : 0;
        $yesterday = strtotime(date("Y-m-d", strtotime("-1 day")));
        $today = strtotime(date("Y-m-d"));
        if ($wxrunstatus == 1 && $wxruncredit1 > 0) {
            $yesterday_setps = pdo_fetch('select * from ' . tablename('elapp_shop_wxrun') . ' where openid=:openid and uniacid=:uniacid and runtime=:runtime limit 1 ', array(':openid' => $_W['openid'], ':uniacid' => $_W['uniacid'], ':runtime' => $yesterday));
            if ($yesterday_setps) {
                if ($yesterday_setps['status'] == 0) {
                    $type = 'credit1';
                    $num = floatval($yesterday_setps['runsetp'] * $wxruncredit1);
                    m('member')->setCredit($yesterday_setps['openid'], $type, $num, array($yesterday_setps['openid'], '后台会员充值积分'));
                    pdo_update('elapp_shop_wxrun', array('status' => 1, 'credit1' => $num, 'lintime' => time()), array('id' => $yesterday_setps['id'], 'uniacid' => $_W['uniacid']));
                    $yesterday_data = pdo_fetch('select * from ' . tablename('elapp_shop_wxrun') . ' where openid=:openid and uniacid=:uniacid and runtime=:runtime limit 1 ', array(':openid' => $_W['openid'], ':uniacid' => $_W['uniacid'], ':runtime' => $yesterday));
                    $today_data = pdo_fetch('select * from ' . tablename('elapp_shop_wxrun') . ' where openid=:openid and uniacid=:uniacid and runtime=:runtime limit 1 ', array(':openid' => $_W['openid'], ':uniacid' => $_W['uniacid'], ':runtime' => $today));
                    $all_credit1 = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where openid=:openid and uniacid=:uniacid limit 1 ', array(':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']));
                    $ke_credit1 = floatval($wxruncredit1 * $yesterday_data['runsetp']);
                    app_json(array('yesterday' => $yesterday_data, 'today' => $today_data, 'wxrunstatus' => $wxrunstatus, 'wxruncredit1' => $wxruncredit1, 'all_credit1' => $all_credit1['credit1'], 'ke_credit1' => $ke_credit1));
                    app_json("领取成功");
                } else {
                    app_error(AppError::$GoodsNotFound, "你已经领取过了");
                }
            } else {
                app_error(AppError::$GoodsNotFound, "没有获取到微信步数");
            }
        } else {
            app_error(AppError::$GoodsNotFound, "系统没有开启微信步数，请咨询客服");
        }
        app_json(0);
    }
}