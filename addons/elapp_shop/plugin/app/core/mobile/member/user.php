<?php

namespace app\controller\app\member;

use app\com\logic\MemberLogic;
use app\common\exception\ApiException;
use app\common\service\JsonService;
use app\controller\AppMobilePage;
use think\Request;

class UserController extends AppMobilePage
{
    /**
     * 用户信息
     */
    public function info()
    {
        global $_W, $_GPC;
        $_W['uniacid'] = $this->memberInfo['uniacid'];
        $member = $this->member;

        $memberInfo = app()->make(MemberLogic::class)
            ->get($this->memberId,[], ['level', 'copartnerAccount', 'carts']);

        $model = m('member');
        $m = $model->getMember($memberInfo['id']);
        $memberInfo['credit1'] = $m['credit1'];
        $memberInfo['credit2'] = $m['credit2'];
        $memberInfo['credit3'] = $m['credit3'];
        $memberInfo['birthdays'] = $member['birthyear'] . '-' . $member['birthmonth'] . '-' . $member['birthday'];

        // 过滤字段
        $keys = ['id', 'realname', 'nickname', 'mobile', 'weixin','birthdays', 'birthday', 'province', 'city', 'area',
                 'datavalue', 'mobileverify', 'avatar','credit1','credit2', 'credit3','level'];
        $info = array_intersect_key($memberInfo->toArray(), array_flip($keys));
        $list_membercard = p("membercard")->get_Mycard($this->memberId, 0, 100);
        $info['member_card_count'] = empty($list_membercard)? 0 : count($list_membercard);
        $info['cart_count'] = empty($memberInfo['carts'])? 0 : count($memberInfo['carts']);

        // 会员权限
        $info['center'] = [
            'clerk'     => $m['is_clerk'] && $m['clerk_status'],
            'copartner' => !empty($memberInfo['copartnerAccount']),
        ];
        $info['clerk_id'] = $memberInfo['clerk_id'];
        $info['copartner_id'] = $memberInfo['copartner_id'];
        $info['onmid'] = $memberInfo['onmid'];

        return $this->succeed('success', $info);
    }

    public function update(Request $request)
    {

        $birthday = $request->param('birthday', '', 'trim');
        // $birthmonth = $request->param('birthmonth', '', 'intval');
        // $birthyear = $request->param('birthyear', '', 'intval');
        $province = $request->param('province', '', 'trim');
        $city = $request->param('city', '', 'trim');
        $area = $request->param('area', '', 'trim');
        $datavalue = $request->param('datavalue', '', 'trim');
        $nickname = $request->param('nickname', '', 'trim');
        $realname = $request->param('realname', '', 'trim');
        $weixin = $request->param('weixin', '', 'trim');
        $avatar = $request->param('avatar', '', 'trim');

        $member = pdo_get("elapp_shop_member", array("id" => $this->memberId));
        if (empty($member)) {
            return JsonService::fail('用户数据异常');
        }

        if ((!is_string($birthday) && !is_numeric($birthday)) || false === strtotime($birthday)) {
            throw new ApiException("birthday参数错误");
        }
        $date = date_parse($birthday);

        // 判断年月日是否合法
        if (!checkdate($date['month'], $date['day'], $date['year'])) {
            return JsonService::fail('生日日期不合法');
        }

        $update = [
            'avatar' => $avatar,
            'weixin' => $weixin,
            'nickname' => $nickname,
            'realname' => $realname,
            'birthday' => $date['day'],
            'birthmonth' => $date['month'],
            'birthyear' => $date['year'],
            'province' => $province,
            'city' => $city,
            'area' => $area,
            'datavalue' => $datavalue,
        ];

        

        pdo_update('elapp_shop_member', $update, ['id' => $this->memberId]);

        return $this->succeed('success');
    }
}
