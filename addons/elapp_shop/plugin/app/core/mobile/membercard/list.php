<?php

namespace app\controller\app\membercard;

use app\controller\AppMobilePage;

class ListController extends AppMobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $uniacid = $_W["uniacid"];
        $cateid = intval($_GPC["category"]);
        $list = array();
        $pindex = max(1, intval($_GPC["page"]));
        $psize = 10;
        $condition = " and uniacid = :uniacid and status=1 and deleted=0";
        $params = array(":uniacid" => $_W["uniacid"]);
        if (!empty($cateid)) {
            $condition .= " and category = " . $cateid;
        }
        $keyword = trim($_GPC["keyword"]);
        if (!empty($keyword)) {
            $condition .= " AND title like :keyword ";
            $params[":keyword"] = "%" . $keyword . "%";
        }
        $sql = "SELECT COUNT(1) FROM " . tablename("elapp_shop_groups_goods") . " where 1 " . $condition;
        $total = pdo_fetchcolumn($sql, $params);
        if (!empty($total)) {
            $sql = "SELECT id,title,thumb,price,groupnum,groupsprice,category,isindex,goodsnum,units,sales,description FROM " . tablename("elapp_shop_groups_goods") . "\n\t\t\t\t\t\twhere 1 " . $condition . " ORDER BY displayorder DESC,id DESC LIMIT " . ($pindex - 1) * $psize . "," . $psize;
            $list = pdo_fetchall($sql, $params);
            $list = set_medias($list, "thumb");
        }
        return app_json(array("list" => $list, "pagesize" => $psize, "total" => $total));
    }
}