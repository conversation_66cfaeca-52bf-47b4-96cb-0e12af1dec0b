<?php

namespace app\controller\app\diypage;

use app\controller\AppMobilePage;
use app\controller\AppError;

class IndexController extends AppMobilePage
{
    public array $notNeedLogin = ['main','main2','getInfo'];
    function main()
    {
        global $_W, $_GPC;

        /*
        $diypage = p('diypage');
        if(!$diypage){
            return app_error(AppError::$PluginNotFound);
        }*/

        $pageid = intval($_GPC['id']);
        if (empty($pageid)) {
            $pageid = trim($_GPC['type']);
        }

        if (empty($pageid)) {
            return app_error(AppError::$PageNotFound);
        }

        $page = $this->model->getPage($pageid, true);

        if ($page === 'default') {

            return app_json(array(
                'diypage' => false
            ));
        }
        if (empty($page) || empty($page['data'])) {
            return app_error(AppError::$PageNotFound);
        }

        $startadv = array();
        if (is_array($page['data']['page']) && !empty($page['data']['page']['diyadv'])) {
            $startadvitem = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_wxapp_startadv') . ' WHERE id=:id AND uniacid=:uniacid', array(
                ':id' => intval($page['data']['page']['diyadv']),
                ':uniacid' => $_W['uniacid']
            ));
            if (!empty($startadvitem) && !empty($startadvitem['data'])) {
                $startadv = base64_decode($startadvitem['data']);
                $startadv = json_decode($startadv, true);
                $startadv['status'] = intval($startadvitem['status']);
                if (!empty($startadv['data'])) {
                    foreach ($startadv['data'] as $itemid => &$item) {
                        $item['imgurl'] = tomedia($item['imgurl']);
                    }
                    unset($itemid, $item);
                }
                if (is_array($startadv['params'])) {
                    $startadv['params']['style'] = 'small-bot';
                }
                if (is_array($startadv['style'])) {
                    $startadv['style']['opacity'] = '0.6';
                }
            }
        }

        $result = array(
            'diypage' => $page['data'],
            'startadv' => $startadv,

            // 客服按钮
            'customer' => intval($_W['shopset']['app']['customer']),
            'phone' => intval($_W['shopset']['app']['phone'])
        );
        if (!empty($result['customer'])) {
            $result['customercolor'] = empty($_W['shopset']['app']['customercolor']) ? '#ed6d0f' : $_W['shopset']['app']['customercolor'];
        }
        if (!empty($result['phone'])) {
            $result['phonecolor'] = empty($_W['shopset']['app']['phonecolor']) ? '#ed6d0f' : $_W['shopset']['app']['phonecolor'];
            $result['phonenumber'] = empty($_W['shopset']['app']['phonenumber']) ? '#ed6d0f' : $_W['shopset']['app']['phonenumber'];
        }

        return app_json($result);
    }

    function main2()
    {
        global $_W, $_GPC;

        $diypage = p('diypage');
        if (!$diypage) {
            return app_error(AppError::$PluginNotFound);
        }

        $pagetype = trim($_GPC['type']);
        // 如果接收到页面类型则查询类型对应的页面id，否则接受参数的页面id
        if (!empty($pagetype)) {
            $pageid = $this->type2Pageid($pagetype);
        } else {
            $pageid = intval($_GPC['id']);
        }

        if (empty($pageid)) {
            return app_error(AppError::$PageNotFound);
        }

        $page = $diypage->getPage($pageid, true);
        if (empty($page) || empty($page['data'])) {
            return app_error(AppError::$PageNotFound);
        }

        return app_json(array(
            'diypage' => $page['data']
        ));
    }

    /**
     * 根据type获取id
     * @param null $type
     * @return int
     */
    public function type2Pageid($type = null)
    {
        if (empty($type)) {
            return 0;
        }
        $set = m('common')->getPluginset('diypage');
        $pageset = $set['page'];
        $pageid = intval($pageset[$type . '_wxapp']);

        return $pageid;
    }

    //获取选项卡所需数据
    public function getInfo()
    {
        global $_GPC, $_W;
        $dataurl = $_GPC['dataurl'];
        $set = m('common')->getPluginset('commission');

        $openid = $_GPC['openid'];
        $member = m('member')->getMember($openid);
        $level = $this->getLevel($_W['openid']);
        //店员插件
        if (p('clerk')) {
            $clerk = p('clerk');
            $clerk_set = $clerk->getSet();
            $clerklevel = $clerk->getLevel($_W['openid']);
            $myshop = $clerk->getShop($member['id']);
        }
        $commissionCondition = '';
        //查询店员商品分润表
        if (!empty($clerk_set['isopen']) && !empty($clerk_set['level'])) {
            $commissionCondition .= ',hasClerkCommission,noClerkCommission,clerkCommission,clerkCommission1_rate,clerkCommission1_pay';
        }
        if (empty($dataurl)) {
            return app_json(array(
                'goods' => array(),
                'type' => 'stores',
            ));
        }

        if (!empty($_GPC['num']) && $_GPC['paramsType'] == 'stores') {
            $storenum = 6 + intval($_GPC['num']);
        } else {
            $storenum = 6;
        }
        if (!empty($_GPC['num']) && $_GPC['paramsType'] == 'goods') {
            $goodsnum = 20 + intval($_GPC['num']);
        } else {
            $goodsnum = 20;
        }
        if (!empty($dataurl)) {
            if (strpos($dataurl, '/pages/') === false) {
                $dataParams = explode('=', $dataurl);
                if ($dataParams[0] == 'category') {
                    $pcate = $dataParams[1];

                    $goodsql = 'SELECT id,displayorder,title,subtitle,thumb,marketprice,hascommission,nocommission,commission,commission1_rate,commission1_rate,marketprice,commission1_pay,maxprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,salesreal,stock,description,bargain,`type`,ispresell,`virtual`,hasoption,video,goodsClassID,medicineAttributeID' . $commissionCondition . ' FROM ' . tablename('elapp_shop_goods') . ' WHERE limitation_terminal_wechat_mmp = 1 AND FIND_IN_SET(' . $pcate . ',cates) AND status = 1 AND deleted = 0 AND uniacid =' . $_W['uniacid'] . ' ORDER BY displayorder DESC' . ' limit 0,' . $goodsnum;
                    $count = pdo_fetch('SELECT count(id) as count FROM ' . tablename('elapp_shop_goods') . ' WHERE FIND_IN_SET(' . $pcate . ',cates) AND status = 1 AND deleted = 0 AND uniacid =' . $_W['uniacid']);
                    $list['list'] = pdo_fetchall($goodsql);
                    $list['count'] = $count['count'];
                    foreach ($list['list'] as $k => $v) {
                        $list['list'][$k]['thumb'] = tomedia($v['thumb']);
                        if ($v['hasoption'] == 1) {
                            $pricemax = array();
                            $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and                               uniacid=:uniacid order by displayorder asc ', array(':goodsid' => $v['id'], ':uniacid' => $_W['uniacid']));
                            foreach ($options as $ke => $va) {
                                array_push($pricemax, $va['marketprice']);
                            }
                            $v['marketprice'] = max($pricemax);
                        }

                        if ($v['nocommission'] == 0) {
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($v['id'])) {
                                    //秒杀
                                    continue;
                                }
                            }
                            if ($v['bargain'] > 0) {
                                //bargain 砍价
                                continue;
                            }
                            $list['list'][$k]['seecommission'] = $this->getCommission($v, $level, $set);
                            if ($list['list'][$k]['seecommission'] > 0) {
                                $list['list'][$k]['seecommission'] = round($list['list'][$k]['seecommission'], 2);
                            }
                            $list['list'][$k]['cansee'] = $set['cansee'];
                            $list['list'][$k]['seetitle'] = $set['seetitle'];
                        } else {
                            $list['list'][$k]['seecommission'] = 0;
                            $list['list'][$k]['cansee'] = $set['cansee'];
                            $list['list'][$k]['seetitle'] = $set['seetitle'];
                        }

                        //店员预计收益
                        if ($v['noClerkCommission'] == 0) {
                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($v['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($v['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $list['list'][$k]['clerkSeecommission'] = $clerk->getGoodsCommission($v, $clerklevel, $options);
                            if ($list['list'][$k]['clerkSeecommission'] > 0) {
                                $list['list'][$k]['clerkSeecommission'] = round($list['list'][$k]['clerkSeecommission'], 2);
                            }
                            $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                            $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                            $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                        } else {
                            $list['list'][$k]['clerkSeecommission'] = 0;
                            $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                            $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                            $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                        }
                    }

                    //m('common')->sortArrayByKey($list['list'], 'displayorder');

                    return app_json(array(
                        'goods' => $list,
                        'type' => 'goods',
                    ));
                } elseif ($dataParams[0] == 'groups') {
                    $sql = 'SELECT * FROM ' . tablename('elapp_shop_goods_group') . ' WHERE id = :id AND uniacid = :uniacid';
                    $params = array(
                        ':uniacid' => $_W['uniacid'],
                        ':id' => $dataParams[1],
                    );
                    $groupsData = pdo_fetch($sql, $params);
                    $goodsid = $groupsData['goodsids'];
                    $goodsql = 'SELECT id,displayorder,title,subtitle,thumb,hascommission,nocommission,commission,commission1_rate,commission1_rate,marketprice,commission1_pay,maxprice,marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,salesreal,stock,description,bargain,`type`,ispresell,`virtual`,hasoption,video,goodsClassID,medicineAttributeID' . $commissionCondition . ' FROM ' . tablename('elapp_shop_goods') . ' WHERE id in(' . $goodsid . ') AND limitation_terminal_wechat_mmp = 1 AND status = 1 AND deleted = 0 AND uniacid =' . $_W['uniacid'] . ' ORDER BY displayorder DESC' . ' limit 0,' . $goodsnum;
                    $count = pdo_fetch('SELECT count(id) as count FROM ' . tablename('elapp_shop_goods') . ' WHERE id in(' . $goodsid . ') AND status = 1 AND deleted = 0 AND uniacid =' . $_W['uniacid']);
                    $list['list'] = pdo_fetchall($goodsql);
                    $list['count'] = $count['count'];
                    foreach ($list['list'] as $k => $v) {
                        $list['list'][$k]['thumb'] = tomedia($v['thumb']);
                        if ($v['hasoption'] == 1) {
                            $pricemax = array();
                            $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and                               uniacid=:uniacid order by displayorder asc', array(':goodsid' => $v['id'], ':uniacid' => $_W['uniacid']));
                            foreach ($options as $ke => $va) {
                                array_push($pricemax, $va['marketprice']);
                            }
                            $v['marketprice'] = max($pricemax);
                        }
                        //分销预计佣金
                        if ($v['nocommission'] == 0) {
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($v['id'])) {
                                    //秒杀
                                    continue;
                                }
                            }
                            if ($v['bargain'] > 0) {
                                //bargain 砍价
                                continue;
                            }
                            $list['list'][$k]['seecommission'] = $this->getCommission($v, $level, $set);
                            if ($list['list'][$k]['seecommission'] > 0) {
                                $list['list'][$k]['seecommission'] = round($list['list'][$k]['seecommission'], 2);
                            }
                            $list['list'][$k]['cansee'] = $set['cansee'];
                            $list['list'][$k]['seetitle'] = $set['seetitle'];
                        } else {
                            $list['list'][$k]['seecommission'] = 0;
                            $list['list'][$k]['cansee'] = $set['cansee'];
                            $list['list'][$k]['seetitle'] = $set['seetitle'];
                        }
                        //店员预计收益
                        if ($v['noClerkCommission'] == 0) {
                            //seckill 秒杀--不参与分润
                            if (p('seckill')) {
                                if (p('seckill')->getSeckill($v['id'])) {
                                    continue;
                                }
                            }
                            //bargain 砍价--不参与分润
                            if (p('bargain')) {
                                if ($v['bargain'] > 0) {
                                    continue;
                                }
                            }
                            $list['list'][$k]['clerkSeecommission'] = $clerk->getGoodsCommission($v, $clerklevel, $options);
                            if ($list['list'][$k]['clerkSeecommission'] > 0) {
                                $list['list'][$k]['clerkSeecommission'] = round($list['list'][$k]['clerkSeecommission'], 2);
                            }
                            $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                            $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                            $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                        } else {
                            $list['list'][$k]['clerkSeecommission'] = 0;
                            $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                            $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                            $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                        }
                    }
                    //m('common')->sortArrayByKey($list['list'], 'displayorder');
                    return app_json(array(
                        'goods' => $list,
                        'type' => 'goods',
                    ));
                } else if ($dataParams[0] == 'goodsids') {
                    $goodsids = explode(',', $dataParams[1]);
                    if (!empty($goodsids)) {
                        foreach ($goodsids as $gk => $gv) {
                            if ($gv == '') {
                                unset($goodsids[$gk]);
                            }
                        }
                        $goodsid = implode(',', $goodsids);
                        $sql = 'SELECT id,showlevels,showOrgs,displayorder,title,subtitle,thumb,marketprice,hascommission,nocommission,commission,commission1_rate,commission1_rate,marketprice,commission1_pay,maxprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,salesreal,stock,description,bargain,`type`,ispresell,`virtual`,hasoption,video,goodsClassID,medicineAttributeID' . $commissionCondition . ' FROM ' . tablename('elapp_shop_goods') . ' WHERE id in(' . $goodsid . ')  AND limitation_terminal_wechat_mmp = 1 AND uniacid =' . $_W['uniacid'] . ' ORDER BY displayorder DESC' . ' limit 0,' . $goodsnum;
                        $count = pdo_fetch('SELECT count(id) as count FROM ' . tablename('elapp_shop_goods') . ' WHERE id in(' . $goodsid . ') AND uniacid =' . $_W['uniacid']);
                        $list['list'] = pdo_fetchall($sql);
                        $list['count'] = $count['count'];
                        foreach ($list['list'] as $k => $v) {
                            $list['list'][$k]['thumb'] = tomedia($v['thumb']);
                            if ($v['hasoption'] == 1) {
                                $pricemax = array();
                                $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and                               uniacid=:uniacid order by displayorder asc', array(':goodsid' => $v['id'], ':uniacid' => $_W['uniacid']));
                                foreach ($options as $ke => $va) {
                                    array_push($pricemax, $va['marketprice']);
                                }
                                $v['marketprice'] = max($pricemax);
                            }
                            //分销预计收益
                            if ($v['nocommission'] == 0) {
                                if (p('seckill')) {
                                    if (p('seckill')->getSeckill($v['id'])) {
                                        //秒杀
                                        continue;
                                    }
                                }
                                if ($v['bargain'] > 0) {
                                    //bargain 砍价
                                    continue;
                                }
                                $list['list'][$k]['seecommission'] = $this->getCommission($v, $level, $set);
                                if ($list['list'][$k]['seecommission'] > 0) {
                                    $list['list'][$k]['seecommission'] = round($list['list'][$k]['seecommission'], 2);
                                }
                                $list['list'][$k]['cansee'] = $set['cansee'];
                                $list['list'][$k]['seetitle'] = $set['seetitle'];
                            } else {
                                $list['list'][$k]['seecommission'] = 0;
                                $list['list'][$k]['cansee'] = $set['cansee'];
                                $list['list'][$k]['seetitle'] = $set['seetitle'];
                            }

                            //店员预计收益
                            if ($v['noClerkCommission'] == 0) {
                                //seckill 秒杀--不参与分润
                                if (p('seckill')) {
                                    if (p('seckill')->getSeckill($v['id'])) {
                                        continue;
                                    }
                                }
                                //bargain 砍价--不参与分润
                                if (p('bargain')) {
                                    if ($v['bargain'] > 0) {
                                        continue;
                                    }
                                }
                                $list['list'][$k]['clerkSeecommission'] = $clerk->getGoodsCommission($v, $clerklevel, $options);
                                if ($list['list'][$k]['clerkSeecommission'] > 0) {
                                    $list['list'][$k]['clerkSeecommission'] = round($list['list'][$k]['clerkSeecommission'], 2);
                                }
                                $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                                $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                                $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                            } else {
                                $list['list'][$k]['clerkSeecommission'] = 0;
                                $list['list'][$k]['clerkCansee'] = $clerk_set['cansee'];
                                $list['list'][$k]['clerkSeetitle'] = $clerk_set['seetitle'];
                                $list['list'][$k]['is_show_revenue'] = $myshop['is_show_revenue'];
                            }

                            //会员登录时判断会员权限
                            if (!empty($member)) {
                                $levelid = intval($member['level']);
                                $groupid = intval($member['groupid']);
                                //判断会员权限
                                /*$list['list'][$key]['levelbuy'] = '1';
                                if ($value['buylevels'] != '') {
                                    $buylevels = explode(',', $value['buylevels']);
                                    if (!in_array($levelid, $buylevels)) {
                                        $list['list'][$key]['levelbuy'] = 0;
                                        $list['list'][$key]['canbuy']  = false;
                                        unset($list['list'][$key]);
                                        continue;
                                    }
                                }*/

                                //会员浏览权限
                                if ($v['showlevels'] != '') {
                                    $showlevels = explode(',', $v['showlevels']);
                                    if (!in_array($levelid, $showlevels)) {
                                        unset($list['list'][$k]);
                                        $list['count'] -= 1;
                                        continue;
                                    }

                                }

                                /*//会员组权限
                                $list['list'][$key]['groupbuy'] = '1';
                                if ($value['buygroups'] != '' && !empty($groupid)) {
                                    $buygroups = explode(',', $value['buygroups']);
                                    $intersect = array_intersect($groupid, $buygroups);
                                    if (empty($intersect)) {
                                        $list['list'][$key]['groupbuy'] = 0;
                                        $list['list'][$key]['canbuy']  = false;
                                        unset($list['list'][$key]);
                                        continue;
                                    }
                                }*/

                                //集团浏览权限
                                if ($v['showOrgs'] != '') {
                                    $showOrgs = explode(',', $v['showOrgs']);
                                    if (!in_array($levelid, $showOrgs)) {
                                        unset($list['list'][$k]);
                                        $list['count'] -= 1;
                                        continue;
                                    }

                                }
                            }
                        }

                        //m('common')->sortArrayByKey($list['list'], 'displayorder');

                        return app_json(array(
                            'goods' => $list,
                            'type' => 'goods',
                        ));
                    }
                } else if ($dataParams[0] == 'stores') {
                    $urlValue = explode('?', $dataParams[1]);
                    $storesids = explode(',', $urlValue[0]);
                    if (!empty($storesids)) {
                        foreach ($storesids as $gk => $gv) {
                            if ($gv == '') {
                                unset($storesids[$gk]);
                            }
                        }
                        $storesid = implode(',', $storesids);
                        $sql = 'SELECT id,storename,displayorder FROM ' . tablename('elapp_shop_store') . ' WHERE id in(' . $storesid . ') AND uniacid =' . $_W['uniacid'] . ' ORDER BY displayorder DESC' . ' limit 0,' . $storenum;
                        $count = pdo_fetch('SELECT count(id) as count FROM ' . tablename('elapp_shop_store') . ' WHERE id in(' . $storesid . ') AND uniacid =' . $_W['uniacid']);
                        $list['list'] = pdo_fetchall($sql);
                        $list['count'] = $count['count'];

                        //m('common')->sortArrayByKey($list['list'], 'displayorder');

                        return app_json(array(
                            'goods' => $list,
                            'type' => 'stores',
                        ));
                    }
                }

            }
        }
    }

    /**
     * 计算出此商品的佣金
     * @param type $goodsid
     * @return type
     */
    public function getCommission($goods, $level, $set)
    {

        global $_W;
        $commission = 0;
        if ($level == 'false') {
            return $commission;
        }

        if ($goods['hascommission'] == 1) {

            $price = $goods['maxprice'];
            $levelid = 'default';

            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['commission']) ? json_decode($goods['commission'], true) : array();

            if ($goods_commission['type'] == 0) {
                $commission = $set['level'] >= 1 ? ($goods['commission1_rate'] > 0 ? ($goods['commission1_rate'] * $goods['marketprice'] / 100) : $goods['commission1_pay']) : 0;
            } else {
                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    foreach ($value as $k => $v) {
                        if (strexists($v, '%')) {
                            array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                            continue;
                        }
                        array_push($price_all, $v);
                    }
                }
                $commission = max($price_all);
            }
        } else {
            if ($level != 'false' && !empty($level)) {
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            } else {
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            }
        }

        return $commission;
    }

    //获取分销商等级
    function getLevel($openid)
    {
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['isagent']) || $member['status'] == 0 || $member['agentblack'] == 1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_commission_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['agentlevel']));

        return $level;
    }
}