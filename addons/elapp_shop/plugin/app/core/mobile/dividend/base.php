<?php
namespace app\controller\app\dividend;
use app\controller\AppMobilePage;

class BaseController extends AppMobilePage
{
    public function __construct()
    {
        parent::__construct();
        global $_W, $_GPC;
        $this->model = p("dividend");
        $this->set = $this->model->getSet();
        if( empty($this->set["open"]) ) 
        {
            exit( app_error(1, "团队分红未开启") );
        }

        if( $_W["action"] != "dividend.register" && $_W["action"] != "share" ) 
        {
            $member = $this->member;
            if( $member["isheads"] != 1 || $member["headsstatus"] != 1 ) 
            {
                exit( app_error(AppError::$CommissionReg, $_W["openid"] . "+" . $member["openid"]) );
            }

        }

    }

}