<?php

namespace app\controller\app\creditshop;

use app\controller\AppError;
use app\controller\AppMobilePage;

class CommentController extends AppMobilePage
{
    public function __construct()
    {
        parent::__construct();
        $trade = m("common")->getSysset("trade");
        if (!empty($trade["closecomment"])) {
            $this->message("不允许评论!", "", "error");
            return app_error(AppError::$OrderCanNotComment, "不允许评论!");
        }

    }

    public function main()
    {
        global $_W, $_GPC;
        $uniacid = $_W["uniacid"];
        $openid = $_W["openid"];
        $goodsid = intval($_GPC["goodsid"]);
        $logid = intval($_GPC["logid"]);
        $merch_plugin = p("merch");
        $merch_data = m("common")->getPluginset("merch");
        if ($merch_plugin && $merch_data["is_openmerch"]) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }

        $merchid = intval($_GPC["merchid"]);
        $condition = " log.uniacid = " . $uniacid . " ";
        if (0 < $merchid) {
            $condition .= " and g.merchid = " . $merchid . " ";
        }

        $log = pdo_fetch("select log.id,log.status,log.goodsid,log.goods_num,log.merchid,g.goodstype,g.type,log.iscomment,log.optionid,g.thumb,o.title as optiontitle,g.title\r\n                ,g.credit,g.money\r\n                from " . tablename("elapp_shop_creditshop_log") . " as log\r\n                left join " . tablename("elapp_shop_creditshop_goods") . " as g on g.id = log.goodsid\r\n                left join " . tablename("elapp_shop_creditshop_option") . " o on o.id=log.optionid\r\n                where " . $condition . " and log.goodsid = " . $goodsid . " and log.id = " . $logid . " ");
        if (empty($log)) {
            return app_error(AppError::$ParamsError, "订单不存在或已删除!");
        }

        $log = set_medias($log, "thumb");
        $log["money"] = print_r($log["money"], 2);
        if ($log["goodstype"] == 0) {
            if ($log["status"] != 3) {
                return app_error(AppError::$OrderNndone, "订单未完成，不能评价!");
            }

        } else {
            if ($log["goodstype"] == 1) {
                if ($log["status"] != 3) {
                    return app_error(AppError::$OrderNndone, "订单未完成，不能评价!");
                }

            } else {
                if ($log["goodstype"] == 2) {
                    if ($log["status"] != 3) {
                        return app_error(AppError::$OrderNndone, "订单未完成，不能评价!");
                    }

                } else {
                    if ($log["goodstype"] == 3 && $log["status"] != 3) {
                        return app_error(AppError::$OrderNndone, "订单未完成，不能评价!");
                    }

                }

            }

        }

        if (2 <= $log["iscomment"]) {
            $this->message("", mobileUrl("creditshop/log/detail", array("id" => $logid)));
            return app_error(AppError::$OrderNndone, "您已经评价过了!");
        }

        return app_json(array("is_openmerch" => $is_openmerch, "log" => $log, "shop" => $_W["shopset"]["shop"]));
    }

    public function submit()
    {
        global $_W, $_GPC;
        $openid = $_W["openid"];
        $uniacid = $_W["uniacid"];
        $logid = intval($_GPC["logid"]);
        $merchid = intval($_GPC["merchid"]);
        $condition = " and uniacid=:uniacid ";
        if (0 < $merchid) {
            $condition .= " and merchid = " . $merchid . " ";
        }

        $log = pdo_fetch("select id,status,iscomment,logno from " . tablename("elapp_shop_creditshop_log") . " where id=:id " . $condition . " and openid=:openid limit 1", array(":id" => $logid, ":uniacid" => $uniacid, ":openid" => $openid));
        if (empty($log)) {
            return app_error(AppError::$RecordNotFound, "兑换记录未找到");
        }

        $member = m("member")->getMember($openid);
        $comments = $_GPC["comments"];
        if (!is_array($comments)) {
            return app_error(AppError::$RecordNotFound, "数据出错，请重试!");
        }

        $trade = m("common")->getSysset("trade");
        if (!empty($trade["commentchecked"])) {
            $checked = 0;
        } else {
            $checked = 1;
        }

        foreach ($comments as $c) {
            $old_c = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_creditshop_comment") . "\r\n            where uniacid=:uniacid and logid=:logid and goodsid=:goodsid limit 1", array(":uniacid" => $_W["uniacid"], ":goodsid" => $c["goodsid"], ":logid" => $logid));
            if (empty($old_c)) {
                $comment = array("uniacid" => $uniacid, "logid" => $logid, "logno" => $log["logno"], "goodsid" => $c["goodsid"], "level" => $c["level"], "content" => trim($c["content"]), "images" => (is_array($c["images"]) ? iserializer($c["images"]) : iserializer(array())), "openid" => $openid, "nickname" => $member["nickname"], "headimg" => $member["avatar"], "time" => time(), "checked" => $checked);
                pdo_insert("elapp_shop_creditshop_comment", $comment);
            } else {
                $comment = array("append_content" => trim($c["content"]), "append_images" => (is_array($c["images"]) ? iserializer($c["images"]) : iserializer(array())), "append_checked" => $checked, "append_time" => time());
                pdo_update("elapp_shop_creditshop_comment", $comment, array("uniacid" => $_W["uniacid"], "goodsid" => $c["goodsid"], "logid" => $logid));
            }

        }
        if ($log["iscomment"] <= 0) {
            $d["iscomment"] = 1;
        } else {
            $d["iscomment"] = 2;
        }

        pdo_update("elapp_shop_creditshop_log", $d, array("id" => $logid, "uniacid" => $uniacid));
        return app_json();
    }

}