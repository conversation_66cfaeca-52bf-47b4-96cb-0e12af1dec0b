<?php
namespace app\controller\app\creditshop;

use app\controller\AppMobilePage;
use think\facade\Cache;

class IndexController extends AppMobilePage
{
    public array $notNeedLogin = ['main'];

    // 公共配置
    const CACHE_TIME = 3600; // 1小时缓存
    const DEFAULT_PAGE_SIZE = 10;

    public function main()
    {
        try {
            global $_W, $_GPC;

            // 基础参数验证
            $uniacid = $_W['uniacid'] ?? 0;
            if (!$uniacid) {
                return app_error(400, '缺少必要参数uniacid');
            }

            // 初始化结果数组
            $result = [
                'advs' => [],
                'category' => [],
                'exchanges' => [],
                'coupons' => [],
                'balances' => [],
                'redbags' => [],
                'pagination' => [],
                'is_openmerch' => 0
            ];

            // 获取缓存实例
            $cache = Cache::store('redis'); // 根据实际配置调整

            // 处理多商户逻辑
            $this->handleMerchData($result);

            // 获取轮播广告（带缓存）
            $this->getAdvsData($uniacid, $result, $cache);

            // 获取分类导航（带缓存）
            $this->getCategoryData($uniacid, $result, $cache);

            // 获取各分类商品数据（带分页）
            $this->getGoodsData('exchanges', 0, $uniacid, $result);
            $this->getGoodsData('coupons', 1, $uniacid, $result);
            $this->getGoodsData('balances', 2, $uniacid, $result);
            $this->getGoodsData('redbags', 3, $uniacid, $result);

            return app_json($result);

        } catch (\Throwable $e) {
            // 记录完整错误日志
            error_log('[积分商城异常] '.date('Y-m-d H:i:s').' '.$e->__toString());
            return app_error(500, '系统繁忙，请稍后再试');
        }
    }

    /**
     * 处理多商户数据
     */
    private function handleMerchData(&$result)
    {
        global $_GPC;

        try {
            $merch_plugin = p("merch");
            $merch_data = m("common")->getPluginset("merch");
            $result['is_openmerch'] = ($merch_plugin && $merch_data["is_openmerch"]) ? 1 : 0;

            // 商户ID处理
            $merchid = intval($_GPC["merchid"] ?? 0);
            if ($merchid > 0) {
                $result['merchid'] = $merchid;
            }
        } catch (\Throwable $e) {
            error_log('[商户数据处理异常] '.$e->getMessage());
        }
    }

    /**
     * 获取轮播广告数据
     */
    private function getAdvsData($uniacid, &$result, $cache)
    {
        try {
            $cacheKey = "creditshop:advs:{$uniacid}";
            if ($data = $cache->get($cacheKey)) {
                $result['advs'] = $data;
                return;
            }

            global $_GPC;
            $contation = $this->buildBaseCondition();
            $advs = pdo_fetchall("SELECT id,advname,app_link as link,thumb 
                FROM " . tablename("elapp_shop_creditshop_adv") . " 
                WHERE {$contation} AND enabled=1 
                ORDER BY displayorder DESC",
                [':uniacid' => $uniacid]
            );

            // 图片处理
            $result['advs'] = array_map(function($item) {
                return [
                    'image' => $this->getMediaUrl($item['thumb']),
                    'title' => $item['advname'],
                    'link' => $item['link']
                ];
            }, set_medias($advs, "thumb"));

            $cache->set($cacheKey, $result['advs'], self::CACHE_TIME);

        } catch (\Throwable $e) {
            error_log('[轮播数据异常] '.$e->getMessage());
        }
    }

    /**
     * 获取分类数据
     */
    private function getCategoryData($uniacid, &$result, $cache)
    {
        try {
            $cacheKey = "creditshop:category:{$uniacid}";
            if ($data = $cache->get($cacheKey)) {
                $result['category'] = $data;
                return;
            }

            global $_GPC;
            $category = [];

            if (intval($_GPC["merchid"] ?? 0) > 0) {
                // 多商户分类处理
                $merch_category = p("merch")->getSet("merch_creditshop_category", $_GPC["merchid"]);
                foreach ($merch_category as $index => $row) {
                    if ($row > 0) {
                        $item = pdo_fetch("SELECT id,name,thumb,isrecommand 
                            FROM " . tablename("elapp_shop_creditshop_category") . " 
                            WHERE id = ? AND uniacid = ? AND enabled = 1",
                            [$index, $uniacid]
                        );
                        if ($item) {
                            $category[] = set_medias($item, "thumb");
                        }
                    }
                }
            } else {
                $category = pdo_fetchall("SELECT id,name,thumb,isrecommand 
                    FROM " . tablename("elapp_shop_creditshop_category") . " 
                    WHERE uniacid = ? AND enabled = 1 
                    ORDER BY displayorder DESC",
                    [$uniacid]
                );
                $category = set_medias($category, "thumb");
            }

            // 图片处理
            $result['category'] = array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'name' => $item['name'],
                    'thumb' => $this->processImage($item['thumb'])
                ];
            }, $category);

            $cache->set($cacheKey, $result['category'], self::CACHE_TIME);

        } catch (\Throwable $e) {
            error_log('[分类数据异常] '.$e->getMessage());
        }
    }

    /**
     * 获取商品数据（带分页）
     */
    private function getGoodsData($type, $goodstype, $uniacid, &$result)
    {
        try {
            global $_GPC;

            // 分页参数
            $page = max(1, intval($_GPC["{$type}_page"] ?? 1));
            $pageSize = min(20, intval($_GPC["{$type}_pagesize"] ?? self::DEFAULT_PAGE_SIZE));

            // 基础查询
            $contation = $this->buildBaseCondition();
            $sql = "SELECT SQL_CALC_FOUND_ROWS 
                id,title,subtitle,credit,money,thumb,goodstype,type,price,isrecommand,hasoption 
                FROM " . tablename("elapp_shop_creditshop_goods") . " 
                WHERE {$contation} 
                AND isrecommand = 1 
                AND goodstype = :goodstype 
                AND type = 0 
                AND status = 1 
                AND deleted = 0 
                ORDER BY displayorder DESC, id DESC 
                LIMIT " . ($page - 1) * $pageSize . "," . $pageSize;

            // 执行查询
            $list = pdo_fetchall($sql, [':uniacid' => $uniacid, ':goodstype' => $goodstype]);
            $total = pdo_fetchcolumn("SELECT FOUND_ROWS()");

            // 处理数据
            $result[$type] = array_map(function($item) {
                return [
                    'id' => $item['id'],
                    'title' => $item['title'],
                    'subtitle' => $item['subtitle'],
                    'credit' => $item['credit'],
                    'money' => price_format($item['money'], 2),
                    'thumb' => $this->processImage($item['thumb']),
                    'type' => $item['type'],
                    'goodstype' => $item['goodstype'],
                    'price' => $item['price'],
                    'isrecommand' => $item['isrecommand'],
                    'hasoption' => $item['hasoption']
                ];
            }, set_medias($list, "thumb"));

            // 分页信息
            $result['pagination'][$type] = [
                'total' => $total,
                'page' => $page,
                'pagesize' => $pageSize,
                'pagecount' => ceil($total / $pageSize)
            ];

        } catch (\Throwable $e) {
            error_log("[商品数据异常][{$type}] ".$e->getMessage());
            $result[$type] = [];
            $result['pagination'][$type] = [
                'total' => 0,
                'page' => $page,
                'pagesize' => $pageSize,
                'pagecount' => 0
            ];
        }
    }

    /**
     * 构建基础查询条件
     */
    private function buildBaseCondition()
    {
        global $_GPC;
        $conditions = ["uniacid = :uniacid"];

        if (intval($_GPC["merchid"] ?? 0) > 0) {
            $conditions[] = "merchid = " . intval($_GPC["merchid"]);
        }

        return implode(' AND ', $conditions);
    }

    /**
     * 图片处理（支持缩略图）
     */
    private function processImage($url, $width = 300, $height = 300)
    {
        if (empty($url)) return '';

        // 如果是相对路径转换为绝对路径
        $url = tomedia($url);

        // 添加缩略图参数（根据实际图片服务调整）
        return $url . "?imageView2/2/w/{$width}/h/{$height}";
    }
    // 新增媒体URL处理方法
    private function getMediaUrl($media)
    {
        if (empty($media)) return '';

        // 如果已经是完整URL直接返回
        if (strpos($media, 'http') === 0) {
            return $media;
        }

        // 系统媒体处理方法
        return tomedia($media);
    }
}