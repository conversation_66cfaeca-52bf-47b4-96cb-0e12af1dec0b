<?php

namespace app\controller\app\groups;

use app\controller\AppMobilePage;

class VerifyController extends AppMobilePage
{
    public function qrcode()
    {
        global $_W, $_GPC;
        $orderid = intval($_GPC["id"]);
        $verifycode = $_GPC["verifycode"];
        $query = array("id" => $orderid, "verifycode" => $verifycode);
        $url = mobileUrl("groups/verify/detail", $query, true);
        return app_json(array("url" => m("qrcode")->createQrcode($url)));
    }

}