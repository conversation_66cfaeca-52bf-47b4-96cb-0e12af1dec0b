<?php
namespace app\controller\app\clerk;

use app\com\logic\SettleLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\controller\settle\LogController;
use think\Request;

class SettleController extends AppMobilePage
{
    //收益结算累计（我的钱包）
    public function main()
    {
        global $_W;
        $_W['container'] = 'wxapp';
//        $_W['openid'] = $this->openid;
//        $_W['uniacid'] = $this->uniacid;
        $_W['openid'] = $this->memberInfo['openid'];
        $_W['uniacid'] = $this->memberInfo['uniacid'];
        $data = (new LogController())->getTotalDataJson($this->memberId);
        $data = json_decode($data, true);
        $data = array_column($data['head'], null, 'key');

        // 可提现收益
        $credit3 = m('member')->getCredit($this->openid, 'credit3');
        $data['can_withdraw_money'] = ['text' => '可提现收益', 'value' => $credit3, 'color' => 'red', 'key' => 'can_withdraw_money'];

        // 收益提示
        $settleset = m('common')->getPluginset('settle');
        $goods_settle_days = $settleset['goods_settle_days'] ?? 7; // 根据订单类型获取结算天数
        $data['tips'] = '订单收益结算时间为订单完成后' . $goods_settle_days . '天';

        return $this->succeed('success', $data);
    }

    /**
     * 钱包列表收益提现明细列表
     * type : 1, 收益明细   2 提现记录
     * @return [type] [description]
     */
    public function list(Request $request)
    {
        global $_GPC, $_W;
        $_W['container'] = 'wxapp';
        $type = $request->param('type', 0, 'intval');
        $page = $request->param('page', 1, 'intval');
        $pagesize = $request->param('page_size', 10, 'intval');

        $_GPC['type'] = $type;
        $_GPC['isCopartner'] = 0;
        $_GPC['page'] = $page;
        $_GPC['page_size'] = $pagesize;
        $_GPC['return'] = true;
        $_GPC['uniacid'] = $this->uniacid ?? $_GPC['uniacid'];
        if (empty($_W['openid'])) {
            $_W['openid'] = $this->memberInfo['openid'];
        }

        $list = (new LogController())->get_list();
        if (!empty($list)) {
            // unset openid
            $unsets = ['openid','apply_no','withdraw_service_fee','money_type','apply_type'];
            foreach ($list as &$item) {

                if (isset($item['apply_no'])) {
                    $item['order_sn'] = $item['apply_no'];
                }

                foreach ($unsets as $unset) {
                    unset($item[$unset]);
                }
            }
        }

        return $this->succeed('success', ['list' => $list]);
    }

    /**
     * 结算详情
     * @return [type] [description]
     */
    public function info(Request $request)
    {
        $id = $request->param('id', 0, 'intval');

        $info = app(SettleLogic::class)->getSettleOrderInfo($id, $this->memberId);
        if (empty($info)) return JsonService::fail(AppError::getError(AppError::$SettleOrderNotFound), [], AppError::$SettleOrderNotFound);

        return $this->succeed('success', $info);
    }
}
