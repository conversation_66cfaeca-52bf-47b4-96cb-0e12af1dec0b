<?php
namespace app\controller\app\clerk;

use app\com\logic\clerk\ClerkLogic;
use app\controller\AppMobilePage;
use app\model\SettleModel;
use think\Request;

class OrderController extends AppMobilePage
{
    /** @var ClerkLogic */
    private $clerkLogic;

    public function initialize()
    {
        parent::initialize();
        $this->clerkLogic = app(ClerkLogic::class);
    }

    //订单列表明细
    public function main(Request $request)
    {
        // '' 全部， 0待付款，1已付款，2待收货，4已完成, 5已关闭
        $status = $request->param('status', '');
        $page = $request->param('page', 1,'intval');
        $page_size = min($request->param('page_size', 10,'intval'),20);
        $clerk_id = $request->param('clerk_id', '');
        $member_id = $request->param('member_id', '');
        $keyword   = $request->param('keyword', '', 'trim');
        $goods_type = $request->param('goods_type', '');

        $opts = [
            'status'    => $status,
            '_total'    => false,
            'member_id' => $member_id,
            'clerk_id'  => $clerk_id,
            'keyword'   => $keyword,
            'goods_type'=> $goods_type
        ];
        // 内购订单逻辑，如果查询的member_id是自己，表示是查看内购订单，否则默认过滤掉自己下的订单
        if ($this->memberId != $member_id) {
            $opts['no_member_id'] = $this->memberId;
        }

        $result = $this->clerkLogic->getOrders($opts, $page, $page_size);

        return $this->succeed('success', ['list' => $result['list'], 'total' => $result['total']]);
    }

    public function mc_orders(Request $request)
    {
        $status = $request->param('status', '');
        $page = $request->param('page', 1,'intval');
        $page_size = min($request->param('page_size', 10,'intval'),20);
        $keyword   = $request->param('keyword', '', 'trim');
        $clerk_id = $request->param('clerk_id', '');
        $member_id = $request->param('member_id', '');

        $opts = [
            'status'    => $status,
            '_total'    => false,
            'keyword'   => $keyword,
            'clerk_id'  => $clerk_id,
            'member_id' => $member_id
        ];

        $result = $this->clerkLogic->getMcOrders($opts, $page, $page_size);

        return $this->succeed('success', ['list' => $result['list']]);
    }
}
