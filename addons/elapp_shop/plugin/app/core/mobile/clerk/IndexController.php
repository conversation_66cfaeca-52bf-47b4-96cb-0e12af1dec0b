<?php

namespace app\controller\app\clerk;

use app\com\logic\clerk\ClerkLogic;
use app\controller\AppMobilePage;
use app\model\ClerkModel;
use app\model\SettleModel;
use app\plugin\app\validate\ClerkValidate;
use think\Request;

class IndexController extends AppMobilePage
{
    /** @var ClerkLogic */
    private $clerkLogic;

    public function initialize()
    {
        parent::initialize();
        $this->clerkLogic = app(ClerkLogic::class);
    }

    public function main()
    {
        $data = $this->clerkLogic->getIndexData($this->memberId);
        $map = [
            'sales_commission' => [
                'name'=> '销售提成',
                'value'=> [
                    'self_order_commission'        => '自购商品',
                    'goods_order_commission'       => '销售商品',
                    'activity_order_commission'    => '销售礼包',
                    'member_cardorder_commission'  => '拓展VIP',
                ]
            ],
            // 隐藏代扣费用，服务费
            // 'deduct_fees' => [
            //     'name'=> '代扣费用',
            //     'value'=> [
            //         'servicefee_order_deduct' => '年度店铺服务费',
            //         'withdraw_freeze_fee'     => '消费预留金',
            //         'withdraw_personal_fee'   => '灵工平台服务费',
            //     ]
            // ],
            'team' => [
                'name'=> '团队管理',
                'value'=> [
                    'sub_clerks_count'  => '我的店长',
                    'sub_members_count' => '我的会员',
                ]
            ]
        ];
        $list = [];
        foreach ($map as $k =>$v) {
            $item = ['name'=>$v['name'],'key'=>$k,'list'=>[]];
            foreach ($v['value'] as $key => $value) {
                $item['list'][] = [
                    'key' => $key,
                    'name' => $value,
                    'value' => $data[$key] ?? '0.00',
                ];
            }
            $list[] = $item;
        }
        $result = [
            'list' => $list,
        ];

        return $this->succeed('success', $result);
    }

    /***
     * 代扣费用列表
     *
     * generic_fee 通用金额， type = 1 表示预留金， type =2 表示实缴金额， type = 3 表示代缴个税
     * withdraw 提现金额
     * */
    public function fees(Request $request)
    {
        $status = $request->param('status', '');
        $page = $request->param('page', 1,'intval');
        $page_size = $request->param('page_size', 10,'intval');
        // 1 消费预留金， 2 店铺服务费， 3 灵工平台服务费
        $type = $request->param('type', 1);

        switch ($type) {
            case 1:
            case 2:
                $price_key = $type == 1 ? 'withdraw_freeze_fee' : 'withdraw_personal_fee';
                list($fees, $total) = $this->clerkLogic->getSettleWithdrawApplyList($this->memberId, 0, $status, $page, $page_size, $price_key);
                $order_no_key = 'apply_no';
                break;
            case 3:
                $price_key = 'total';
                list($fees, $total) = $this->clerkLogic->getServiceFeeOrders($this->memberId, $status, $page, $page_size, $price_key);
                $order_no_key = 'orderno';
                break;
            default:
                return $this->fail('参数错误');
        }

        $list = [];
        foreach ($fees as &$fee) {
            $list[] = [
                'id'          => $fee['id'],
                'order_no'    => $fee[$order_no_key] ?? '',
                'status'      => $fee['status'] ?? 0,
                'create_time' => $fee['create_time'] ?? '',
                'apply_money' => $fee['apply_money'] ?? '0.00', // 申请金额
                'price'       => $fee[$price_key] ?? '0.00', // 消费预留金
                'pay_time'    => $fee['pay_time'] ?? '',
            ];
        }

        return $this->succeed('success', ['list' => $list, 'total_money'=>$total]);
    }

    /**
     * @params $params['member_id'] 会员ID
     * @return \think\response\Json
     */
    public function clerkInfo()
    {
        $params     = (new ClerkValidate())->get()->goCheck('clerkInfo');
        $clerk_info = (new ClerkModel())->getClerkInfo($params['member_id']);
        return $this->succeed('获取成功', $clerk_info);
    }
}
