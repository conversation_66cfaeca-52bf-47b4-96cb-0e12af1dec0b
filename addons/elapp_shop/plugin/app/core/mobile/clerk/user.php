<?php

namespace app\controller\app\clerk;

use app\com\logic\clerk\ClerkLogic;
use app\com\logic\LoginLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\plugin\app\validate\ClerkValidate;
use app\Request;

class UserController extends AppMobilePage
{
    public array $notNeedLogin = ['login'];
    /** @var ClerkLogic */
    private $clerkLogic;

    public function initialize()
    {
        parent::initialize();
        $this->clerkLogic = app(ClerkLogic::class);
    }

    public function main()
    {

    }

    /**
     * 用户信息
     */
    public function info()
    {
        $info = $this->clerkLogic->getClerkInfo($this->memberId);

        $result = [
            'realname'               => $this->memberInfo['realname'],
            'nickname'               => $this->memberInfo['nickname'],
            'mobile'                 => $this->memberInfo['mobile'],
            'avatar'                 => $this->memberInfo['avatar'],
            'mentor_name'            => '',
            'invite_code'            => $this->memberInfo['mobile'],
            'service_fee'            => []
        ];

        $result = array_merge($result, $info);
        unset($result['no_servicefee_pay']);

        return $this->succeed('success', $result);
    }

    public function member_info(Request $request)
    {
        $id = $request->param('id', 0, 'intval');

        $info = $this->clerkLogic->getMemberInfo($id);
        if (empty($info)) {
            if (empty($info)) return JsonService::fail(AppError::getError(AppError::$UserNotFound), [], AppError::$UserNotFound);
        }

        $result = [
            'realname'               => $this->memberInfo['realname'],
            'nickname'               => $this->memberInfo['nickname'],
            'mobile'                 => $this->memberInfo['mobile'],
            'avatar'                 => $this->memberInfo['avatar'],
            'mentor_name'            => '',
            'invite_code'            => $this->memberInfo['mobile'],
            'service_fee'            => []
        ];

        $result = array_merge($result, $info);
        unset($result['no_servicefee_pay']);

        return $this->succeed('success', $result);
    }

    /**
     * 我的店长
     * @return [type] [description]
     */
    public function members(Request $request)
    {
        global $_W, $_GPC;
        $page = $request->param('page', 1);
        $pagesize = $request->param('page_size', 10);
        $is_clerk = $request->param('is_clerk');
        $onmid = $request->param('onmid', '');
        //$clerk_id = $request->param('clerk_id', '');
        $keyword = $request->param('keyword', '', 'trim');

        // todo wait 判断权限
        list($members, $total) = $this->clerkLogic->getMembers($onmid, $this->memberId, $is_clerk, $keyword, $page, $pagesize, false);

        return $this->succeed('success', ['list' => $members, 'total'=>$total]);
    }

    /**
     * 店长工作台登录(短信)
     * @param Request $request
     * @return \think\response\Json
     */
    function login(Request $request)
    {
        $data = app(ClerkValidate::class)->post()->goCheck('login');
        $smskey = '__elapp_shop_member_verifycodesession_' . $_W['uniacid'] . '_' . $data['mobile'];
        $verifysmscode = m("cache")->get($smskey);
        if(!$verifysmscode || $verifysmscode != $data['verify_code']){
            return $this->fail('验证码错误或已过期', [], AppError::$ParamsError);
        }
        $data['terminal'] = $request->getFromType();
        $res = app(ClerkLogic::class)->login($data);
        if (0 !== $res['code']) {
            return $this->fail($res['msg']);
        }
        if (empty($res['data']['token'])) {
            return $this->fail('登陆失败', $res['data']);
        }
        return $this->succeed('登陆成功', $res['data']);
    }
}
