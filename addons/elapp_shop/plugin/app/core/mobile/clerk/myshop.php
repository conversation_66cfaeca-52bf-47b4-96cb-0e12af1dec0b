<?php
namespace app\controller\app\clerk;

use app\com\logic\copartner\CopartnerLogic;
use app\com\logic\MemberLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\controller\clerk\myshop\SetController;
use app\model\CopartnerModel;
use think\Request;

class MyshopController extends AppMobilePage
{
    /**
     * 店铺信息
     */
    public function info()
    {
        global $_W;

        $member = m('member')->getMember($this->memberId);
        $shop = pdo_fetch('select * from ' . tablename('elapp_shop_clerk_shop') . ' where mid=:mid limit 1', array(':mid' => $member['id']));
        $shop = set_medias($shop, array('img', 'logo'));
        $shop['set'] = json_decode($shop['set'],true);

        $data = ['shop'=>$shop];
        return $this->succeed('', $data);
    }

    /**
     * 更新店铺信息
     * @param Request $request
     * @return \think\response\Json
     */
    public function update(Request $request)
    {
        $name = $request->param('name', '', 'trim');
        $logo = $request->param('logo', '', 'trim');
        $img = $request->param('img', '', 'trim');
        $desc = $request->param('desc', '', 'trim');
        $is_show_revenue = $request->param('is_show_revenue', 0, 'intval');

        $shop = pdo_get('elapp_shop_clerk_shop', array('mid' => $this->memberId));

        if (empty($shop['id'])) {
            $shop = [
                'name' => $name,
                'logo' => $logo,
                'img' => $img,
                'desc' => $desc,
                'is_show_revenue' => $is_show_revenue,
                'mid' => $this->memberId,
                'uniacid' => $this->memberInfo['uniacid'],
                'selectgoods' => 0,
                'selectcategory' => 0,
                'goodsids' => '',
                'set' => '',
            ];

            pdo_insert('elapp_shop_clerk_shop', $shop);
        } else {
            $shop['name'] = $name;
            $shop['logo'] = $logo;
            $shop['img'] = $img;
            $shop['desc'] = $desc;
            $shop['is_show_revenue'] = $is_show_revenue;

            pdo_update('elapp_shop_clerk_shop', $shop, array('id' => $shop['id']));
        }

        return $this->succeed('success');
    }
}
