<?php

namespace app\controller\app\activity;

use app\controller\activity\ActivityEggTuanGift;
use app\controller\activity\EggTuanGiftLogic;
use app\controller\AppMobilePage;
use app\controller\AppError;
use app\model\GoodsModel;
use app\Request;

class EggController extends AppMobilePage
{

    public function main()
    {
        $logic = new EggTuanGiftLogic();
        $result = $logic->getEggIndexInfo($this->memberId);
        if ($result['code'] === 0) {
            return $this->succeed('success', $result['data']);
        } else {
            return $this->fail($result['msg'], $result['data'], $result['code']);
        }
    }

    /**
     * 鸡蛋活动邀请数据
     * @param Request $request
     * @return \think\response\Json
     */
    public function get_list(Request $request)
    {
        $goodsid          = $request->get('goodsid', 0);
        $type             = $request->get('type', 'users');
        $EggTuanGiftLogic = new EggTuanGiftLogic();
        $list             = $EggTuanGiftLogic->getList($this->memberId, $type == 'users' ? 1 : 0, $goodsid, 1, 1000);

        return $this->succeed("", $list);
    }

    /**
     * 领取优惠券
     * @return \think\response\Json
     */
    public function redeemCoupon(Request $request)
    {
        $id = $request->param('id');

        $logic = app(EggTuanGiftLogic::class);
        $result = $logic->redeemCoupon($id, $this->memberId);

        if ($result['code'] === 0) {
            return $this->succeed('领取成功');
        } else {
            return $this->fail($result['msg'] ?? '领取失败', $result['data'] ?? [], $result['code'] ?? 1);
        }
    }
}