<?php

namespace app\controller\app\live;

use app\controller\AppMobilePage;
use app\model\PolyvModel;

class PolyvChannelController extends AppMobilePage
{
    /**
     * 获取用户当前有效频道列表
     * @return \think\response\Json
     */
    public function channels()
    {
        // todo 怎么显示，先不显示，显示什么频道给用户，按照用户来控制了
        // 获取当前用户id
        global $_W;
        $mid = $_W['member']['mid'];

        // 暂时不需要实现时间权限，只要获取返回所有频道即可
        // $channels = PolyvChannelModel::where('status', 1)->select();
        $model = new PolyvModel();
        $channels = $model->getChannelListFromAPI('', '', 1, 20);
        // return app_json($channels);die;
        // $channels = array_keys($channels['data']);
        $channels['mid'] = $mid;
        $channels['pagesize'] = $channels['size'];
        $channels['list'] = $channels['data'];
        unset($channels['size']);
        unset($channels['data']);
        $list = [];
        foreach ($channels['list'] as $item) {
            $h = 600;//rand(320, 350);
            $w = 800;//rand(280, 320);
            $capture = $model->getLiveStreamCapture($item['channelId']);
            // 读取playback列表提取第一个视频填充回放    
            $mlist = $model->getPlaybackMListFromAPI($item['channelId'], '', 1, 1);
            if (isset($mlist['code']) && $mlist['code'] == 200) {
                $mlist = isset($mlist['data']['contents']) ? $mlist['data']['contents'] : [];
            } else {
                $mlist = [];
            }
            $fileid = $mlist[0]['fileId']??'';
            
            $list[] = [
                'mlist'=> $mlist,
                'fileid' => $fileid,
                'channelId' => $item['channelId'],
                'name' => $item['name'],
                'coverImg' => $item['coverImg'],
                'cap'=>$capture,
                'capture' => 'https://picsum.photos/'.$w.'/'.$h.'?random=' . rand(1, 15),
                'desc' => $item['desc'],
                
            ];
        }
        $channels['list'] = $list;

        return app_json($channels);
        // return app_json([]);
    }
}
