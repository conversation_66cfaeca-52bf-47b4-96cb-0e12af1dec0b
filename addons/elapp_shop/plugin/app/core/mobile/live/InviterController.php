<?php
namespace app\controller\app\live;

use app\controller\AppMobilePage;
use app\logic\PlaylistLogic;
use app\model\MemberModel;
use app\model\PlaylistAccessModel;

class InviterController extends AppMobilePage {
	public function main() {
        global $_W, $_GPC;
        // show_json(0, ['text'=>'您已经绑定过邀请码，请勿重复操作']);
        // return app_json(['text'=>'您已经绑定过邀请码，请勿重复操作']);
        // return $this->succeed('success', ['text'=>'您已经绑定过邀请码，请勿重复操作' . $this->member['id'], 'm'=>$this->member]);

        // $openid = $_W['openid'];
        $icode = $_GPC['invite_code'];

        // return $this->succeed('success', ['text'=>'您已经绑定过邀请码，请勿重复操作' . $this->member['id'], 'm'=>$this->member]);

   		if ($_W['ispost']) {
			if (!empty($this->member['clerk_id'])) {
                return $this->failed('您已经绑定过邀请码，请勿重复操作');
            }

            // 根据手机号
            if(m('verify')->isMobile($icode)) {
                $sql = "select * from ".tablename('elapp_shop_member')." where mobile = :mobile limit 1";
            	$inviter = pdo_fetch($sql, array(":mobile"=>$icode));
                if (!empty($inviter)) {
                    $mid = $inviter['id'];
                }else{
                    return $this->fail('手机号码错误，找不到邀请人！');
                }
            } else {//根据会员UID

                $model = new MemberModel();
                $inviter = $model->find(['id'=>$icode]);
                
                if (empty($inviter)) {
                    return $this->fail('邀请码错误，找不到推荐人！');
				}
                if ($this->member['id'] == $icode) {
                    return $this->fail('邀请码错误，推荐人不能为自己！');
                }
                $mid = $icode;
            }
            
			// 1.如果是会员  => member.onmid = $mid;
            // 1.2  且是店员 => member.onmid = $mid, member.clerk_id  = $mid // 通过openid判断当前的会员是不是店员
            $data = ['onmid'=> $mid];
            //邀请人为店员,且自身没有绑定clerk_id的时候，需要同时绑定clerk_id,copartner_id,copartner_account_id
            if (empty($member['clerk_id'])) {

                // 如果推荐用户是店员，则绑定clerk_id = 推荐用户id,copartner_id,copartner_account_id
                // 否则绑定推荐人的店员id(clerk_id),copartner_id,copartner_account_id
                if ($inviter['is_clerk'] == 1 && $inviter['clerk_status'] == 1) {
                    $data['clerk_id'] = $mid;
                    $p_copartner = p('copartner');//机构合伙人
                    if ($p_copartner) {
                        $copartner_account = $p_copartner->isCopartnerAccount($inviter['openid']);
                        if (!empty($copartner_account)) {
                            $data['copartner_account_id'] = $copartner_account['id'];
                            $data['copartner_id'] = $copartner_account['copartner_id'];
                        } else {
                            if(!empty($inviter['copartner_id'])){
                                $data['copartner_id'] = $inviter['copartner_id'];
                                $data['copartner_user_path'] = $inviter['copartner_user_path'];
                            }
                            if(!empty($inviter['copartner_account_id'])){
                                $data['copartner_account_id'] = $inviter['copartner_account_id'];
                            }
                        }
                    }

                } else {
                    $data['clerk_id'] = $inviter['clerk_id'];
                }

                // 绑定机构 id 及 机构子帐号 uid
                $coIdData = $this->getBindCopartnerIdData($data['clerk_id']);
                if (!empty($coIdData['copartner_id']) && !empty($coIdData['copartner_account_id'])) {
                    $data = array_merge($data, $coIdData);
                }
            }

            //$this->model->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'clerk_create_time' => $m_data['clerk_create_time']), TM_CLERK_BECOME_APPLY);
            pdo_update('elapp_shop_member', $data, array('id' => $this->member['id']));


            // dev polyv 获取当前用户是否有创建的播放列表（营期）
            (new PlaylistLogic())->insertUserRomotionPlaylistId($this->member['id']);

            //show_json(1, array('url'=>mobileUrl('membercard/index/main')));
            return $this->succeed('success', ['success']);
   		}

        return $this->fail('失败');
	}

	private function getBindCopartnerIdData($inviter)
    {
        $inviter = m('member')->getMember($inviter);
        $data = [];
        $t_account = tablename('elapp_shop_copartner_account');
        $coaccount = pdo_fetch('SELECT id, copartner_id, status FROM' . $t_account . 'WHERE openid=:openid AND uniacid=:uniacid and del_at=0 LIMIT 1', array(':openid' => $inviter['openid'], ':uniacid' => $inviter['uniacid']));

        // 邀请人属于某机构（创始人和子帐号）
        if (!empty($coaccount) && $coaccount['status']==1) {
            $data['copartner_id'] = $coaccount['copartner_id'];
            $data['copartner_user_path'] = $inviter['copartner_user_path'];
            $data['copartner_account_id'] = $coaccount['id'];
        } else { // 邀请人没有加入机构，取邀请人上级的机构，如果无则不设置
            if (!empty($inviter['copartner_id']) && !empty($inviter['copartner_account_id'])) {
                $data['copartner_id'] = $inviter['copartner_id'];
                $data['copartner_user_path'] = $inviter['copartner_user_path'];
                $data['copartner_account_id'] = $inviter['copartner_account_id'];
            }
        }
        return $data;
    }
}