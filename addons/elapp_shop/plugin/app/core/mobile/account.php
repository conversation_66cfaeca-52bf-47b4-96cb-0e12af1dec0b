<?php 
namespace app\controller\app;
use app\com\logic\LoginLogic;
use app\com\logic\MemberCancelLogic;
use app\com\service\wechat\MiniProgramService;
use app\com\validate\CancelAccountValidate;
use app\com\validate\WechatLoginValidate;
use app\controller\AppMobilePage;
use app\controller\AppError;
use app\com\service\MemberTokenService;
use app\core\com\logic\relation\MemberRelationBindingLogic;
use app\model\AppPromotionRecord;
use app\plugin\app\logic\MnpMemberLogic;
use app\plugin\app\validate\MnpMemberValidate;
use app\Request;
use app\model\MemberModel;
use app\core\com\logic\relation\RelationBindingChecker;
use app\logic\PlaylistLogic;


class AccountController extends AppMobilePage
{
    protected $key = "";
    protected $expire = 0;
    public array $notNeedLogin = ['login', 'register', 'mnpLogin', 'mnpMobileLogin', 'silentLogin'];

    public function __construct()
    {
        parent::__construct();
        global $_W;
        $this->authkey = $_W["setting"]["site"]["token"] . "_" . $_W["uniacid"];
        $this->expire = 3600 * 24 * 30;
    }

    public function main()
    {
        global $_W;
        $set = $_W["shopset"]["wap"];
        $result = array( "color" => $set["color"], "bg" => tomedia($set["bg"]), "logo" => tomedia($_W["shopset"]["shop"]["logo"]), "template" => $set["style"], "wx" => $set["sns"]["wx"], "qq" => $set["sns"]["qq"], "closecolor" => "#ffffff" );
        return app_json($result);
    }

    public function login()
    {
        global $_W, $_GPC;
        $mobile = trim($_GPC["mobile"]);
        $pwd = trim($_GPC["pwd"]);
        if (empty($mobile) || empty($pwd)) {
            return app_error(AppError::$ParamsError);
        }

        $member = pdo_fetch("select id,openid,mobile,pwd,salt,avatar,nickname,delete_time,is_cancel_account from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array(":mobile" => $mobile, ":uniacid" => $_W["uniacid"]));
        if (empty($member)) {
            return app_error(AppError::$UserLoginFail);
        }

        if (md5($pwd . $member["salt"]) !== $member["pwd"]) {
            return app_error(AppError::$UserLoginFail);
        }

        //丢入缓存
        $memberInfo = MemberTokenService::setToken($member["id"], 1);
        $_SESSION["openid"] = $_W["openid"] = $member["openid"];
        return $this->data($memberInfo);

        $token = base64_encode(authcode($member["id"] . "|" . $member["salt"], "ENCODE", $this->authkey, $this->expire));
        return app_json(array( "token" => $token, "expire" => $this->expire, "member" => array( "id" => $member["id"], "mobile" => $member["mobile"], "salt" => $member["salt"], "nickname" => $member["nickname"], "avatar" => $member["avatar"], "openid" => $member["openid"],'delete_time'=> $member['delete_time'],'is_cancel_account'=>$member['is_cancel_account'] ) ));
    }

    /**
     * 用户注册
     */

    public function register()
    {
        global $_W, $_GPC;
        // 入参获取与基础必填校验（手机号、密码、短信验证码）
        $mobile = trim($_GPC["mobile"]);
        $pwd = trim($_GPC["pwd"]);
        $verifycode = trim($_GPC["verifycode"]);
        $request_openid = trim($_GPC["openid"]); // 前端传递的 openid
        if (empty($mobile) || empty($pwd) || empty($verifycode)) {
            return app_error(AppError::$ParamsError);
        }

        @session_start();
        // 短信验证码：缓存键规则（与发送验证码时保持一致）
        $key = "__elapp_shop_member_verifycodesession_" . $_W["uniacid"] . "_" . $mobile;
        $key_time = "__elapp_shop_member_verifycodesendtime_" . $_W["uniacid"];
        $sendcode = m("cache")->get($key);
        $sendtime = m("cache")->get($key_time);
        // 校验验证码是否匹配；当前为开发模式（#dev_help_001）暂时放宽校验
        if( !isset($sendcode) || $sendcode !== $verifycode ) {
            $promotionRecord = (new AppPromotionRecord())->where('mobile', $mobile)->find();
            // dev polyv 兼容当前手机验证码可能被运营商拦截，设置读取推广记录中的验证码进行放行
            if (!$promotionRecord || $promotionRecord['verify_code'] != $verifycode) {
                return app_error(AppError::$VerifyCodeError); // #dev_help_001 注释这样忽略手机验证码
            }
        }

        // 校验验证码是否过期（默认 600s 有效期）
        if (!isset($sendtime) || 600 * 1000 < time() - $sendtime) {
            return app_error(AppError::$VerifyCodeTimeOut);
        }

        // 根据手机号查询已存在的会员；若不存在则创建
        $member = pdo_fetch("select id,openid,mobile,pwd,salt from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array(":mobile" => $mobile, ":uniacid" => $_W["uniacid"]));
        // 账号口令盐值：若不存在，为新/老用户生成全局唯一随机盐
        $salt = (empty($member) ? "" : $member["salt"]);
        if (empty($salt)) {
            $salt = random(16);
            while (1) {
                $count = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_member") . " where salt=:salt limit 1", array(":salt" => $salt));
                if ($count <= 0) {
                    break;
                }
                $salt = random(16);
            }
        }

        // openid 初始化：优先级顺序 - 请求参数 > 已绑定的 openid > 缓存中的 openid > WAP 占位 openid
        $openid = (empty($member) ? "" : $member["openid"]);
        $nickname = (empty($member) ? "" : $member["nickname"]);
        $openid_source = "none";

        // 记录调试信息
        file_put_contents('/tmp/register_debug.log', json_encode([
            'mobile' => $mobile,
            'request_openid' => $request_openid,
            'member_openid' => $openid,
            'cached_openid' => app('cache')->get('mnp_openid'),
            'time' => date('Y-m-d H:i:s')
        ]) . "\n", FILE_APPEND);

        // 1. 优先使用请求参数中的 openid（前端传递）
        if (!empty($request_openid)) {
            $openid = $request_openid;
            $openid_source = "request_param";
        }
        // 2. 如果没有请求参数，尝试从缓存中获取小程序登录时存储的 openid
        else if (empty($openid)) {
            $cached_openid = app('cache')->get('mnp_openid');
            if (!empty($cached_openid)) {
                $openid = $cached_openid;
                $openid_source = "cache";
                // 清除缓存，避免被其他用户使用
                app('cache')->delete('mnp_openid');
            }
        } else {
            $openid_source = "member_existing";
        }

        // 3. 最后生成 WAP 占位 openid
        if (empty($openid)) {
            $_SESSION["openid"] = $_W["openid"] = $openid = "wap_user_" . $_W["uniacid"] . "_" . $mobile;
            $nickname = substr($mobile, 0, 3) . "xxxx" . substr($mobile, 7, 4);
            $openid_source = "generated_wap";
        } else {
            $_SESSION["openid"] = $_W["openid"] = $openid;
        }

        // 记录最终使用的 openid
        file_put_contents('/tmp/register_debug.log', json_encode([
            'final_openid' => $openid,
            'openid_source' => $openid_source,
            'mobile' => $mobile
        ]) . "\n", FILE_APPEND);

        if (empty($member)) {
            // 新用户：插入基础资料（来源标记为 app_mobile），并开启手机号已验证标识
            $member = array("uniacid" => $_W["uniacid"], "mobile" => $mobile, "nickname" => $nickname, "openid" => $openid, "pwd" => md5($pwd . $salt), "salt" => $salt, "createtime" => time(), "mobileverify" => 1, "comefrom" => "app_mobile");
            pdo_insert("elapp_shop_member", $member);
            $member['id'] = pdo_insertid();
            if (method_exists(m("member"), "memberRadisCountDelete")) {
                m("member")->memberRadisCountDelete();
            }

            // 这里的数据哪里来？ -> app_promotion_record 是由用户推广的时候通过一个推广页面手动写入用户手机及推广信息，然后再下载app，所以预先就会有数据
            $appPromotionRecord = (new AppPromotionRecord())->where('mobile', $mobile)->find();
            if ($appPromotionRecord) {
                $_GPC['mid'] = $appPromotionRecord['invite_code'];
                $memberModel = new MemberModel();
                $m = $memberModel->findOrEmpty($member['id']);
                $referrer    = $memberModel->findOrEmpty($_GPC['mid']);
                if (!((new RelationBindingChecker())->checkIfLockRelation($m))) {
                    // app 注册绑定关系
                    $result = (new MemberRelationBindingLogic())->checkBeforeBinding($m, $referrer);
                    (new PlaylistLogic())->insertUserRomotionPlaylistId($m['id']);
                }
            }

        } else {
            // 老用户：同步 openid、salt、pwd，并标记已通过手机验证
            pdo_update("elapp_shop_member", array("openid" => $openid, "salt" => $salt, "pwd" => md5($pwd . $salt), "mobileverify" => 1), array("id" => $member["id"]));
        }

        if (p("commission")) {
            // 佣金插件：触发代理校验/初始化
            p("commission")->checkAgent($openid);
        }
        // 发放登录 token（走统一的 MemberTokenService），保持与 login() 行为一致
        $memberInfo = $member["id"] ? MemberTokenService::setToken($member["id"], 1) : [];
        return $this->data($memberInfo);

        $token = base64_encode(authcode($member["id"] . "|" . $salt, "ENCODE", $this->authkey, $this->expire));
        return app_json(array( "token" => $token, "expire" => $this->expire, "member" => array( "id" => $member["id"], "mobile" => $member["mobile"], "salt" => $member["salt"], "nickname" => $member["nickname"], "avatar" => $member["avatar"], "openid" => $member["openid"] ) ));
    }

    /**
     * 登录token验证
     */

    public function checktoken()
    {
        global $_GPC;
        $token = trim($_GPC["token"]);
        if( !empty($token) ) 
        {
            $token = authcode(base64_decode($token), "DECODE", $this->authkey, $this->expire);
            if( !empty($token) ) 
            {
                return app_json(array( "token" => $token ));
            }

            return app_error(AppError::$UserTokenFail);
        }

        return app_json();
    }

    /**
     * 验证手机号唯一
     */

    public function checkmobile()
    {
        global $_W, $_GPC;
        $mobile = trim($_GPC["mobile"]);
        $member = pdo_fetch("select id,openid,mobile,pwd,salt from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array( ":mobile" => $mobile, ":uniacid" => $_W["uniacid"] ));
        if( !empty($member) ) 
        {
            return app_error(AppError::$UserMobileExists);
        }

        return app_json();
    }

    /**
     * 获取修改密码信息
     */

    public function getchangepwd()
    {
        global $_W, $_GPC;
        $member = m("member")->getMember($_W["openid"]);
        if( empty($member["mobile"]) || empty($member["mobileverify"]) ) 
        {
            return app_error(AppError::$UserNotBindMobile, "不用通过手机号找回密码");
        }

        $key_time = "__elapp_shop_member_verifycodesendtime_" . $_W["uniacid"];
        $sendtime = m("cache")->get($key_time);
        if( empty($sendtime) || $sendtime + 60 < time() ) 
        {
            $endtime = 0;
        }
        else
        {
            $endtime = 60 - (time() - $sendtime);
        }

        return app_json(array( "mobile" => $member["mobile"], "endtime" => $endtime ));
    }

    /**
     * 执行修改密码
     */

    public function changepwd()
    {
        global $_W, $_GPC;
        $mobile = trim($_GPC["mobile"]);
        $pwd = trim($_GPC["pwd"]);
        $verifycode = trim($_GPC["verifycode"]);
        if( empty($mobile) || empty($pwd) || empty($verifycode) ) 
        {
            return app_error(AppError::$ParamsError);
        }

        $key = "__elapp_shop_member_verifycodesession_" . $_W["uniacid"] . "_" . $mobile;
        $key_time = "__elapp_shop_member_verifycodesendtime_" . $_W["uniacid"];
        $sendcode = m("cache")->get($key);
        $sendtime = m("cache")->get($key_time);
        if( !isset($sendcode) || $sendcode !== $verifycode ) 
        {
            return app_error(AppError::$VerifyCodeError);
        }

        if( !isset($sendtime) || 600 * 1000 < time() - $sendtime ) 
        {
            return app_error(AppError::$VerifyCodeTimeOut);
        }

        $member = pdo_fetch("select id,openid,mobile,pwd,salt from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array( ":mobile" => $mobile, ":uniacid" => $_W["uniacid"] ));
        if( empty($member) ) 
        {
            return app_error(AppError::$UserNotFound);
        }

        $salt = random(16);
        while( 1 ) 
        {
            $count = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_member") . " where salt=:salt limit 1", array( ":salt" => $salt ));
            if( $count <= 0 ) 
            {
                break;
            }

            $salt = random(16);
        }
        pdo_update("elapp_shop_member", array( "salt" => $salt, "pwd" => md5($pwd . $salt) ), array( "id" => $member["id"] ));
        return app_json(array( "salt" => $member["salt"] ));
    }

    /**
     * 忘记密码
     */

    public function forget()
    {
        $this->changepwd();
    }

    /**
     * 修改手机号
     */

    public function changemobile()
    {
        global $_W, $_GPC;
        $mobile = trim($_GPC["mobile"]);
        $newmobile = trim($_GPC["newmobile"]);
        $verifycode = trim($_GPC["verifycode"]);
        if( empty($mobile) || empty($pwd) || empty($verifycode) ) 
        {
            return app_error(AppError::$ParamsError);
        }

        $key = "__elapp_shop_member_verifycodesession_" . $_W["uniacid"] . "_" . $mobile;
        $key_time = "__elapp_shop_member_verifycodesendtime_" . $_W["uniacid"];
        $sendcode = m("cache")->get($key);
        $sendtime = m("cache")->get($key_time);
        if( !isset($sendcode) || $sendcode !== $verifycode ) 
        {
            return app_error(AppError::$VerifyCodeError);
        }

        if( !isset($sendtime) || 600 * 1000 < time() - $sendtime ) 
        {
            return app_error(AppError::$VerifyCodeTimeOut);
        }

        $member = pdo_fetch("select id,openid,mobile,pwd,salt from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array( ":mobile" => $mobile, ":uniacid" => $_W["uniacid"] ));
        if( empty($member) ) 
        {
            return app_error(AppError::$UserNotFound);
        }

        $newmember = pdo_fetch("select id,openid,mobile,pwd,salt from " . tablename("elapp_shop_member") . " where mobile=:mobile and uniacid=:uniacid limit 1", array( ":mobile" => $newmobile, ":uniacid" => $_W["uniacid"] ));
        if( empty($newmember) ) 
        {
            return app_error(AppError::$UserMobileExists);
        }

        pdo_update("elapp_shop_member", array( "mobile" => $newmobile ), array( "id" => $member["id"] ));
        return app_json(array( "mobile" => $newmobile ));
    }

    /**
     * SNS授权登录
     */

    public function sns()
    {
        global $_W, $_GPC;
        if( !$_W["ispost"] ) 
        {
        }

        $type = trim($_GPC["type"]);
        if( $type == "qq" ) 
        {
            if( empty($_GPC["openid"]) ) 
            {
                return app_error(AppError::$ParamsError, "参数错误(OPENID字段为空)");
            }

            if( empty($_GPC["userinfo"]) ) 
            {
                return app_error(AppError::$ParamsError, "参数错误(USERINFO字段为空)");
            }

        }
        else
        {
            if( $type == "wx" ) 
            {
                if( empty($_GPC["code"]) && empty($_GPC["token"]) ) 
                {
                    return app_error(AppError::$ParamsError, "参数错误(CODE、TOKEN为空)");
                }

            }
            else
            {
                return app_error(AppError::$ParamsError, "参数错误(SNS类型错误)");
            }

        }

        $mid = m("member")->checkMemberSNS($type);
        $member = m("member")->getMember($mid);
        $token = base64_encode(authcode($member["id"] . "|" . $member["salt"], "ENCODE", $this->authkey, $this->expire));
        return app_json(array( "token" => $token, "expire" => $this->expire, "member" => array( "id" => $member["id"], "mobile" => $member["mobile"], "salt" => $member["salt"], "nickname" => $member["nickname"], "avatar" => $member["avatar"], "openid" => $member["openid"] ) ));
    }

    /**
     * @desc 获取小程序用户手机号
     * @return string|bool
     * <AUTHOR>
     * @date 2024/1/20 13:35
     */
    function getMobileByMnp()
    {
        $params = (new MnpMemberValidate())->post()->goCheck('getMobileByMnp');
        $params['member_id'] = $this->memberId;
        $result = MnpMemberLogic::getMobileByMnp($params);
        if (false === $result) {
            return$this->fail(MnpMemberLogic::getError());
        }
        return $this->succeed('绑定成功', $result, 1, 1);
    }

    /**
     * @desc 绑定/变更手机号
     * @return string|bool
     * <AUTHOR>
     * @date 2024/1/20 13:37
     * @package app\api\controller\v1
     */
    function bindMobile()
    {
        $params = (new MnpMemberValidate())->post()->goCheck('bindMobile');
        $params['member_id'] = $this->memberId;
        $result = MnpMemberLogic::bindMobile($params);
        if (false === $result) {
            return$this->fail(MnpMemberLogic::getError());
        }
        return $this->succeed('绑定成功', $result, 1, 1);
    }

    /**
     * 手机号授权登录（废弃）
     * @return \think\response\Json
     */
    function mnpMobileLogin()
    {
        $params = (new WechatLoginValidate())->post()->goCheck('mnpLogin');
        $res = LoginLogic::mnpMobileLogin($params);
        if (false === $res) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->succeed('登陆成功', $res);
    }

    /**
     * @desc 小程序登录
     * @params array $params array('code' => '', 'i' => '');
     * @return \think\response\Json
     */
    public function mnpLogin() {
        $params = (new WechatLoginValidate())->post()->goCheck('mnpLogin');
        $res = LoginLogic::mnpLogin($params);
        if (false === $res) {
            return $this->fail(LoginLogic::getError());
        }
        if (empty($res['token'])) {
            return $this->fail('登陆失败', $res);
        }
        return $this->succeed('登陆成功', $res);
    }

    /**
     * 小程序静默登录（废弃）
     * @return \think\response\Json
     */
    function silentLogin()
    {
        $params = (new WechatLoginValidate())->post()->goCheck('mnpLogin');
        $res = LoginLogic::silentLogin($params);
        if (false === $res) {
            return $this->fail(LoginLogic::getError());
        }
        return $this->succeed('登陆成功', $res);
    }

    /**
     * 获取小程序用户openid
     * @return \think\response\Json
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    function getAuthOpenid()
    {
        $params = app(WechatLoginValidate::class)->post()->goCheck('WechatAuth');
        try {
            $res = app()->make(MiniProgramService::class, ['uniacid' => $params['i']])->getMnpResByCode($params['code']);
            if (false === $res) {
                return $this->fail(LoginLogic::getError());
            }
            return $this->succeed('success', $res);
        } catch (Exception $e) {
            return $this->fail($e->getMessage());
        }
    }

    function cancel_account()
    {
        $params = app(CancelAccountValidate::class)->post()->goCheck();
        if ($params['status'] == 0) {
            $res = MemberCancelLogic::rollbackCancleUserAccountApply($this->memberId);
            if (true === $res) {
                return $this->succeed('已经撤销注销帐号', $res);
            }
        } else {
            $res = MemberCancelLogic::cancleUserAccountApply($this->memberId, 1);
            if (true === $res) {
                return $this->succeed('申请成功', []);
            }
        }

        return $this->fail($res);
    }
}