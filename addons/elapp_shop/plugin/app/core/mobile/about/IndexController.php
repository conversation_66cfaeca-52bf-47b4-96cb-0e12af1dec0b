<?php
namespace app\controller\app\about;
use app\controller\AppMobilePage;
use app\controller\AppError;

class IndexController extends AppMobilePage
{
    public array $notNeedLogin = ['get_list','detail'];
    //多商户
    function get_list()
    {
        global $_W, $_GPC;
        $uniacid = $_W['uniacid'];
        $openid = $_W['openid'];
        if (empty($openid)) {
            //return app_error(AppError::$ParamsError);//不需要验证
        }

        $cate = intval($_GPC['cate']);
        $keyword = trim($_GPC['keyword']);
        $isrecommand = intval($_GPC['isrecommand']);
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $condition = ' q.uniacid=:uniacid and q.status=1 and c.enabled=1 ';

        if (!empty($cate)) {
            $condition .= ' and q.cate=' . $cate . ' ';
        }

        if (!empty($isrecommand)) {
            $condition .= ' and q.isrecommand=1 ';
        }

        if (!empty($keyword)) {
            $condition .= ' AND (q.title like \'%' . $keyword . '%\') or (q.keywords like \'%' . $keyword . '%\') ';
        }

        $params = array(':uniacid' => $_W['uniacid']);
        $sql = 'SELECT q.*, c.name as catename FROM ' . tablename('elapp_shop_about_content') . ' q left join' . tablename('elapp_shop_about_category') . ' `c` on c.id=q.cate and c.uniacid=q.uniacid where  1 and ' . $condition . ' ORDER BY q.displayorder DESC,q.id DESC LIMIT ' . (($pindex - 1) * $psize) . ',' . $psize;
        $list = pdo_fetchall($sql, $params);
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename('elapp_shop_about_content') . ' q left join' . tablename('elapp_shop_about_category') . ' c on c.id=q.cate and c.uniacid=q.uniacid where  1 and ' . $condition . ' ', $params);

        if (!empty($total)) {
            foreach ($list as &$item) {
                $item['content'] = iunserializer($item['content']);
                $item['content'] = htmlspecialchars_decode($item['content']);
                $item['content'] = m('ui')->lazy($item['content']);
            }

            unset($item);
        }

        return app_json(array('list' => $list, 'pagesize' => $psize, 'total' => $total, 'page' => $pindex));
    }

    function detail()
    {

        global $_W, $_GPC;
        $openid = $_W['openid'];
        $uniacid = $_W['uniacid'];
        $id = intval($_GPC['id']);
        $shopname = $_W['shopset']['shop']['name'];
        $shoplogo = tomedia($_W['shopset']['shop']['logo']);
        $param = array();
        $param[':uniacid'] = $_W['uniacid'];

        if (!empty($id)) {
            $item = pdo_fetch('select * from ' . tablename('elapp_shop_about_content') . ' where id=:id and status=1 and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));

            if (empty($item)) {
                return app_error(20001,'数据不存在');
            }

            $item['content'] = iunserializer($item['content']);
            $item['content'] = htmlspecialchars_decode($item['content']);
            //$item['content'] = m('ui')->lazy($item['content']);
        }

        $result = [];
        $result['detail'] = $item;
        // $shop = array(
        //     'name' => $shopname,
        //     'logo' => $shoplogo
        // );

        // $result = array(
        //     'shop' => $shop,
        //     // 客服按钮
        //     'customer' => intval($_W['shopset']['app']['customer']),
        //     'phone' => intval($_W['shopset']['app']['phone']),
        // );

        // //获取设置
        // $trade = m('common')->getSysset('trade');
        // $result['trade'] = $trade;

        // if (!empty($result['customer'])) {
        //     $result['customercolor'] = empty($_W['shopset']['app']['customercolor']) ? '#ed6d0f' : $_W['shopset']['app']['customercolor'];
        // }

        // if (!empty($result['phone'])) {
        //     $result['phonecolor'] = empty($_W['shopset']['app']['phonecolor']) ? '#ed6d0f' : $_W['shopset']['app']['phonecolor'];
        //     $result['phonenumber'] = empty($_W['shopset']['app']['phonenumber']) ? '#ed6d0f' : $_W['shopset']['app']['phonenumber'];
        // }

        return app_json($result);
    }

    protected function merchData()
    {
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }

        return array(
            'is_openmerch' => $is_openmerch,
            'merch_plugin' => $merch_plugin,
            'merch_data' => $merch_data
        );
    }
}