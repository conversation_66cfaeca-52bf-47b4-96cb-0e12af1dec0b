<?php
namespace app\controller\app\copartner;

use app\com\logic\copartner\CopartnerLogic;
use app\com\logic\MemberLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\model\CopartnerModel;
use app\plugin\app\validate\CopartnerValidate;
use think\Request;

class UserController extends AppMobilePage
{
    public array $notNeedLogin = ['login'];
    public function main()
    {
        return $this->succeed('success', []);
    }

    /**
     * 用户信息
     */
    public function info()
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $info = app(CopartnerLogic::class)->getCopartnerInfo($account['copartner_id']);
        return $this->succeed('success', $info);
    }

    /**
     * 我的会员
     * @param Request $request
     * @return \think\response\Json
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function members(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $page = $request->param('page', 1, 'intval');
        $pagesize = $request->param('page_size', 10,'intval');
        $is_clerk = $request->param('is_clerk');
        $clerk_id = $request->param('clerk_id', '');
        $keyword = $request->param('keyword', '', 'trim');

        $opts = [];
        // 搜索店员id不等于自身，默认认为查询团队推广的会员（即非我推广的会员）
        if ($this->memberId != $clerk_id) {
            $opts['no_clerk_id'] = $this->memberId;
        }
        // 非创始人(招商经理）只能查看自己的会员
        if ($account['isfounder'] != 1) {
            $opts['copartner_account_id'] = $account['id'];
        }
        list($members, $total) = $copartnerLogic->getMembers($account['copartner_id'], $opts, $clerk_id, $is_clerk, $keyword, $page, $pagesize, false);

        return $this->succeed('success', ['list' => $members, 'total'=>$total]);
    }

    /**
     * 招商经理列表
     */
    public function sub_accounts(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account) || $account['isfounder'] != 1) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $status = $request->param('status');
        $page = $request->param('page', 1, 'intval');

        $accounts = $copartnerLogic->getSubAccounts($page, $account['copartner_id'], $status);

        return $this->succeed('success', ['list' => $accounts]);

    }

    /**
     * 招商经理审核
     */
    public function sub_accounts_check(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account) || $account['isfounder'] != 1) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $id = $request->param('id', 0, 'intval');
        $status = $request->param('status');

        $result = $copartnerLogic->verifySubAccounts($id, $account, $status);
        if ($result !== true) {
            return JsonService::fail(AppError::getError($result), [], $result);
        }

        return $this->succeed('success', []);

    }

    function login(Request $request)
    {
        $data = app(CopartnerValidate::class)->post()->goCheck('login');
        if ('sms' == $data['type']) { //短信验证码登陆
            $smskey = '__elapp_shop_member_verifycodesession_' . $_W['uniacid'] . '_' . $data['mobile'];
            $verifysmscode = m("cache")->get($smskey);
            if(!$verifysmscode || $verifysmscode != $data['verify_code']){
                return $this->fail('验证码错误或已过期', [], AppError::$ParamsError);
            }
        }

        $data['terminal'] = $request->getFromType();
        $res = app(CopartnerLogic::class)->login($data);
        if (0 !== $res['code']) {
            return $this->fail($res['msg']);
        }
        if (empty($res['data']['token'])) {
            return $this->fail('登陆失败', $res['data']);
        }
        return $this->succeed('登陆成功', $res['data']);
    }
}
