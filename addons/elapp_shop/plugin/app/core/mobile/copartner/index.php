<?php

namespace app\controller\app\copartner;

use app\com\logic\copartner\CopartnerLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use think\Request;

class IndexController extends AppMobilePage
{
    //合伙人工作台 首页
    public function main()
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        // 合伙人和招商经理的逻辑不一样，分开写
        if ($account['isfounder'] == 1) {
            $data = $copartnerLogic->getIndexData($this->memberId, $this->memberInfo['openid'], $account['copartner_id']);
        } else {
            $data = $copartnerLogic->getIndexData($this->memberId, $this->memberInfo['openid'], $account['copartner_id'], $account['id']);
        }

        // 处理返回数据格式
        $result = $this->map_data($account, $data);

        return $this->succeed('success', $result);
    }

    private function map_data($account, $data) {
        $map = [
            'sales_commission' => [
                'name'=> '岗位绩效',
                'value'=> [
                    'member_cardorder_commission'  => '拓展VIP',
                    'goods_order_commission'       => '销售商品',
                    'activity_order_commission'    => '销售礼包',
                    'service_fee_order_commission' => '店铺服务费',
                ]
            ],
            'team' => [
                'name'=> '团队管理',
                'value'=> [
                    'sub_clerks_count'  => '我的店长',
                    'sub_members_count' => '我的会员',
                ]
            ]
        ];
        $copartner_map = [
            'deduct_fees' => [
                'name'=> '代扣费用',
                'value'=> [
//                    'servicefee_order_deduct' => '年度店铺服务费',
'withdraw_freeze_fee'     => '消费预留金',
'withdraw_personal_fee'   => '灵工平台服务费',
                ]
            ],
            'team' => [
                'value'=> [
                    'sub_account_count' => '招商经理',
                ]
            ]
        ];
        // 创始人能看到更多的数据
        if ($account['isfounder'] == 1) {
            $map = array_merge_recursive($map, $copartner_map);
        }

        $list = [];
        foreach ($map as $k =>$v) {
            $item = ['name'=>$v['name'],'key'=>$k,'list'=>[]];
            foreach ($v['value'] as $key => $value) {
                $item['list'][] = [
                    'key' => $key,
                    'name' => $value,
                    'value' => $data[$key] ?? '0.00',
                ];
            }
            $list[] = $item;
        }

        $_data = [
            'total_money'                => [
                'key'   => 'total_money',
                'name'  => '收益合计',
                'value' => bcadd($data['clerk_commission_total'] ?? '0.00', $data['copartner_commission_total'] ?? '0.00', 2)
            ],
            'clerk_commission_total'     => [
                'key'   => 'clerk_commission_total',
                'name'  => '销售提成',
                'value' => $data['clerk_commission_total'] ?? '0.00',
            ],
            'copartner_commission_total' => [
                'key'   => 'copartner_commission_total',
                'name'  => '岗位绩效',
                'value' => $data['copartner_commission_total'] ?? '0.00',
            ],
        ];

        $result = [
            'list' => $list,
            'data' => $_data
        ];

        return $result;
    }

    /***
     * 代扣费用列表
     *
     * generic_fee 通用金额， type = 1 表示预留金， type =2 表示实缴金额， type = 3 表示代缴个税
     * withdraw 提现金额
     * */
    public function fees(Request $request)
    {
        $status = $request->param('status', '');
        $page = $request->param('page', 1,'intval');
        $page_size = $request->param('page_size', 10,'intval');
        // 1 消费预留金， 2 店铺服务费， 3 灵工平台服务费
        $type = $request->param('type', 1);

        $logic = app(CopartnerLogic::class);

        switch ($type) {
            case 1:
            case 2:
                $price_key = $type == 1 ? 'withdraw_freeze_fee' : 'withdraw_personal_fee';
                list($fees, $total) = $logic->getSettleWithdrawApplyList($this->memberId, 1, $status, $page, $page_size, $price_key);
                $order_no_key = 'apply_no';
                break;
//            case 3:
//                $price_key = 'total';
//                list($fees, $total) = $logic->getServiceFeeOrders($this->memberId, $status, $page, $page_size, $price_key);
//                $order_no_key = 'orderno';
//                break;
            default:
                return $this->fail('参数错误');
        }

        $list = [];
        foreach ($fees as &$fee) {
            $list[] = [
                'id'          => $fee['id'],
                'order_no'    => $fee[$order_no_key] ?? '',
                'status'      => $fee['status'] ?? 0,
                'create_time' => $fee['create_time'] ?? '',
                'apply_money' => $fee['apply_money'] ?? '0.00', // 申请金额
                'price'       => $fee[$price_key] ?? '0.00', // 消费预留金
                'pay_time'    => $fee['pay_time'] ?? '',
            ];
        }

        return $this->succeed('success', ['list' => $list, 'total_money'=>$total]);
    }

    //合伙人信息设置
    public function info_set(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account) || $account['isfounder']!=1) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $logo = $request->param('logo', '', 'trim');
        $mobile = $request->param('mobile', '', 'trim');
        $realname = $request->param('realname', '', 'trim');
        $desc = $request->param('desc', '', 'trim');
        $mcnname = $request->param('mcnname', '', 'trim');
        $comname = $request->param('comname', '', 'trim');

        if (empty($mcnname)) {
            return JsonService::fail(AppError::getError(AppError::$ParamsError) . ',请填写机构名称', [], AppError::$ParamsError);
        }

        $data['mcnname'] = $mcnname;
        $data['comname'] = $comname;
        $data['desc'] = $desc;
        if (!empty($logo)) {
            $data['logo'] = $logo;
        }
        $data['mobile'] = $mobile;
        $data['realname'] = $realname;
        #更新
        pdo_update('elapp_shop_copartner_user', $data,array('id'=>$account['copartner_id']));

        return $this->succeed('success');
    }

    /**
     * 收益统计，合计
     * @avg | start_time 开始时间
     * @avg | end_time 结束时间
     */
    public function statistics(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $start_time = $request->param('start_time', 0, 'intval');
        $end_time   = $request->param('end_time', 0, 'intval');

        if (empty($start_time) && empty($end_time)) {
            // 如果时间取值异常，默认读取今天
            $start_time = strtotime(date('Y-m-d 00:00:00'));
            $end_time   = strtotime(date('Y-m-d 23:59:59'));
        }

        $result = $copartnerLogic->getStatistics( $start_time, $end_time, $account['copartner_id'], $account['isfounder'] == 1 ? 0 : $account['id']);

        $map = [
            'goods'       => ['商品', ['goods_order_count', 'goods_order_price']],
            'member_card' => ['会员卡', ['member_card_order_count', 'member_card_order_price']],
            'service_fee' => ['服务费', ['service_fee_order_count', 'service_fee_order_price']],
        ];

        $data = ['list'=>[], 'order_count'=>0, 'total_meony'=>0,'update_time'=>time()];

        // 返回数据格式处理
        foreach ($map as $k =>$v) {
            $data['list'][] = [
                'title' => $v[0], 'type' => $k, 'data' => [
                    ['value' => $result[$v[1][0]], 'name' => '订单数'],
                    ['value' => $result[$v[1][1]], 'name' => '成交额']
                ]
            ];
            $data['order_count'] += $result[$v[1][0]];
            $data['total_meony'] = bcadd($data['total_meony'], $result[$v[1][1]], 2);
        }

        return $this->succeed('success', $data);
    }
}
