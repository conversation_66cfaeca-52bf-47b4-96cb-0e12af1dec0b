<?php
namespace app\controller\app\copartner;

use app\com\logic\copartner\CopartnerLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\model\OrderModel;
use think\Request;

class OrderController extends AppMobilePage
{
    //订单列表明细
    public function main(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        // '' 全部， 0待付款，1已付款，2待收货，4已完成, 5已关闭
        $status = $request->param('status', '');
        $page = $request->param('page', 1,'intval');
        $page_size = min($request->param('page_size', 10,'intval'),20);
        $clerk_id = $request->param('clerk_id', '');
        $member_id = $request->param('member_id', '');
        $keyword   = $request->param('keyword', '', 'trim');
        $goods_type = $request->param('goods_type', '');

        $opts = [
            'status'       => $status,
            '_total'       => false,
            'member_id'    => $member_id,
            'clerk_id'     => $clerk_id,
            'keyword'      => $keyword,
            'goods_type'   => $goods_type,
            'copartner_id' => $account['copartner_id']
        ];

        // 非创始人(招商经理）只能查看自己的会员
        if ($account['isfounder'] != 1) {
            $opts['copartner_account_id'] = $account['id'];
            $opts['no_commissions_calc'] = true;
        }

        $result = $copartnerLogic->getOrders($opts, $page, $page_size);

        return $this->succeed('success', ['list' => $result['list'], 'total' => $result['total']]);
    }

    public function mc_orders(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $status = $request->param('status', '');
        $page      = $request->param('page', 1, 'intval');
        $page_size = min($request->param('page_size', 10, 'intval'), 20);
        $keyword   = $request->param('keyword', '', 'trim');
        $clerk_id  = $request->param('clerk_id', '');
        $member_id = $request->param('member_id', '');

        $opts = [
            'status'       => $status,
            '_total'       => 1,
            'keyword'      => $keyword,
            'clerk_id'     => $clerk_id,
            'member_id'    => $member_id,
            'copartner_id' => $account['copartner_id']
        ];
        // 非创始人(招商经理）只能查看自己的会员
        if ($account['isfounder'] != 1) {
            $opts['copartner_account_id'] = $account['id'];
            $opts['no_commissions_calc'] = true; // 招商经理不计算佣金
        }

        $result = $copartnerLogic->getMemberCardOrders($opts, $page, $page_size);

        return $this->succeed('success', ['list' => $result['list'], 'total'=>$result['total']]);
    }

    public function sf_orders(Request $request)
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $status = $request->param('status', '');
        $page      = $request->param('page', 1, 'intval');
        $page_size = min($request->param('page_size', 10, 'intval'), 20);
        $keyword   = $request->param('keyword', '', 'trim');
        $clerk_id  = $request->param('clerk_id', '');
        $member_id = $request->param('member_id', '');

        $opts = [
            'status'       => $status,
            '_total'       => 1,
            'keyword'      => $keyword,
            'clerk_id'     => $clerk_id,
            'member_id'    => $member_id,
            'copartner_id' => $account['copartner_id']
        ];
        // 非创始人(招商经理）只能查看自己的会员
        if ($account['isfounder'] != 1) {
            $opts['copartner_account_id'] = $account['id'];
            $opts['no_commissions_calc'] = true; // 招商经理不计算佣金
        }

        $result = $copartnerLogic->getServicefeeOrders($opts, $page, $page_size);

        return $this->succeed('success', ['list' => $result['list'], 'total'=>$result['total']]);
    }

    /**
     * 销售指标
     * @return [type] [description]
     */
    public function sales_indicator()
    {
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account)) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }
        $copartner_id = $account['copartner_id'];

//        $type = $request->param('type', 'goods');

        // 商品订单数 商品总金额
        // 会员卡订单数 会员卡总金额
        // 服务费订单数 服务费总金额
        // 总会员数 访问次数
        $t_member = tablename('elapp_shop_member');
        $t_order = tablename('elapp_shop_order');
        $t_order_mc = tablename('elapp_shop_member_card_order');
        $t_order_sf = tablename('elapp_shop_member_servicefee_order');
//        $t_goods = tablename('elapp_shop_goods');

        $w = ' and copartner_id = :copartner_id';
        $params = [':copartner_id'=> $copartner_id];
        if ($account['isfounder']!=1) {
            $w = ' and copartner_account_id=:copartner_account_id';
            $params[':copartner_account_id'] = $account['id'];
        }
        $where_og = $where_mc = $where_sf = 'and deleted=0';
        $where_m = '';
        $params_og = $params_mc = $params_sf = $params_m = [];

//        $mid_field = 'clerk_id';
//        if (@$account['copartner']['channel']['channeltype'] == 1) {
//            $mid_field = 'doctor_id';
//        }

//        if ($member_id) {
//            $where_og = " and {$mid_field}=:mid";
//            $params_og = [':mid' => $member_id];
//
//            $where_mc = "and openid in (select distinct openid from $t_member where $mid_field=:mid)";
//            $params_mc = [':mid' => $member_id];
//
//            $where_sf = "and openid in (select distinct openid from $t_member where $mid_field=:mid)";
//            $params_sf = [':mid' => $member_id];
//        }

        // 商品订单成交总金额
        $sql = "select ifnull(sum(price),0) sum,count(*) count from $t_order where  status>=1 $w $where_og";
        $c = (pdo_fetch($sql, array_merge($params, $params_og)));
        $order_price_goods = $c['sum'];
        $order_count_goods = $c['count'];

        // 商品订单总数
        $sql = "select ifnull(sum(price),0) sum,count(*) count from $t_order where 1 $w $where_og";
        $c = (pdo_fetch($sql, array_merge($params, $params_og)));
        $total_order_price_goods = $c['sum'];
        $total_order_count_goods = $c['count'];

        // 会员卡订单总金额
        $sql = "select ifnull(sum(price),0) sum,count(*) count from $t_order_mc where  status>=1 $w $where_mc";
        $c = pdo_fetch($sql,array_merge($params, $params_mc));
        $order_price_mc = $c['sum'];
        $order_count_mc = $c['count'];

        $sql = "select ifnull(sum(price),0) sum,count(*) count from $t_order_mc where  1 $w $where_mc";
        $c = pdo_fetch($sql,array_merge($params, $params_mc));
        $total_order_price_mc = $c['sum'];
        $total_order_count_mc = $c['count'];

        // 服务费订单总金额
        $sql = "select ifnull(sum(total),0) sum,count(*) count from $t_order_sf where  status>=1 $w $where_sf";
        $c = pdo_fetch($sql,array_merge($params, $params_sf));
        $order_price_sf = $c['sum'];
        $order_count_sf = $c['count'];

        $sql = "select ifnull(sum(total),0) sum,count(*) count from $t_order_sf where  1 $w $where_sf";
        $c = pdo_fetch($sql,array_merge($params, $params_sf));
        $total_order_price_sf = $c['sum'];
        $total_order_count_sf = $c['count'];

        // 商品订单成交用户数量
        $subsql = "select distinct member_id from $t_order where  status>=1 $w $where_og";
        $sql = "select count(*) from $t_member where  id in ($subsql)";
        $c = pdo_fetchcolumn($sql,array_merge($params, $params_og));
        $user_count_order_goods = intval($c);

        // mc订单成交用户数量
        $subsql = "select distinct member_id from $t_order_mc where  status>=1 $w $where_mc";
        $sql = "select count(*) from $t_member where  id in ($subsql)";
        $c = pdo_fetchcolumn($sql,array_merge($params, $params_mc));
        $user_count_order_mc = intval($c);

        // sf订单成交用户数量
        $subsql = "select distinct member_id from $t_order_sf where  status>=1 $w $where_sf";
        $sql = "select count(*) from $t_member where  id in ($subsql)";
        $c = pdo_fetchcolumn($sql,array_merge($params, $params_sf));
        $user_count_order_sf = intval($c);

        // 会员总数
        $sql = "select count(*) from $t_member where 1 $w $where_m";
        $c = pdo_fetchcolumn($sql,array_merge($params, $params_m));
        $total_user_count = intval($c);

        $types = [
            'goods'=> [
                'order_price'=> $order_price_goods, // 订单总金额
                'order_count'=> $order_count_goods, // 成交订单数
                'total_order_price'=> $total_order_price_goods,// 总订单金额
                'total_order_count'=> $total_order_count_goods, // 总订单数
                'user_count_order'=> $user_count_order_goods, // 成交会员数
                'total_user_count' => $total_user_count,
            ],
            'member_card'=> [
                'order_price'=> $order_price_mc,
                'order_count'=> $order_count_mc,
                'total_order_price'=> $total_order_price_mc,
                'total_order_count'=> $total_order_count_mc,
                'user_count_order'=> $user_count_order_mc,
                'total_user_count' => $total_user_count,
            ],
            'service_fee'=> [
                'order_price'=> $order_price_sf,
                'order_count'=> $order_count_sf,
                'total_order_price'=> $total_order_price_sf,
                'total_order_count'=> $total_order_count_sf,
                'user_count_order'=> $user_count_order_sf,
                'total_user_count' => $total_user_count,
            ],
        ];

        // 订单总金额 / 成交会员数 = 用户平均成交额
        // 订单总金额 / 成交订单数 = 平均订单金额
        // 成交订单数 / 订单总数   = 订单成交率
        // 成交会员数 / 总会员数  = 会员消费率
        // 总订单数 / 总会员数    = 人均订单数

        // 计算定义表达式 [计算方式，参数1，参数2，参数名称数组]
        $expressions = [
            // 用户平均成交额 = 订单总金额 / 成交会员数
            ['div', 'order_price', 'user_count_order', ['订单总金额', '成交会员数', '用户平均成交额']],
            ['div', 'order_price', 'order_count', ['订单总金额', '总订单数量', '订单平均金额']],
            ['div', 'total_order_count', 'total_user_count', ['总订单数', '总会员数', '人均订单数']],
            // 订单成交率 = 成交订单数 / 总订单数量 * 100%
            ['div_rate', 'order_count', 'total_order_count', ['成交订单数', '总订单数量', '订单成交率']],
            ['div_rate', 'user_count_order', 'total_user_count', ['成交会员数', '总会员数', '会员消费率']],
        ];

        $list = [];
        foreach ($types as $type => $data) {
            $result = [];
            foreach ($expressions as $expression) {

                $func = $expression[0];
                $p1 = $data[$expression[1]];
                $p2 = $data[$expression[2]];
                $names = $expression[3];

                $values = [$p1, $p2, $this->calcute($p1, $p2, $func)];
                // 将name和values数组每个子元素合并组成一个新数组
                $item = array_map(function($n, $p) {
                    return ['name' => $n, 'value' => $p];
                }, $names, $values);

                $result[] = $item;
            }
            $list[$type] = $result;
        }

        return $this->succeed('success', $list);
    }

    private function calcute($a, $b, $op)
    {
        $ops = [
            'div'=> function($a, $b) {
                // a / b
                return  $b == 0 ? '-' : bcdiv($a, $b, 2);
            },
            'div_rate' => function ($a, $b) {
                // a / b * 100%
                return $b == 0 ? '-' : bcmul(100, bcdiv($a, $b, 4), 2) . '%' ;
            }
        ];

        if (!isset($ops[$op])) {
            return '-';
        }

        return $ops[$op]($a, $b);
    }
}
