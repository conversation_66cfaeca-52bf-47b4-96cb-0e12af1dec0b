<?php
namespace app\controller\app\copartner;

use app\com\logic\copartner\CopartnerLogic;
use app\com\logic\MemberLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\controller\AppMobilePage;
use app\model\CopartnerModel;
use think\Request;

class PosterController extends AppMobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $_W['container'] = 'wxapp';
        $_W['openid'] = $this->memberInfo['openid'];
        $_GPC['mid'] = $this->memberInfo['id'];
        // 实例化QrcodeController类的对象
        $qrcodeController = new \app\controller\clerk\QrcodeController();
        // 调用clerk对象qrcode的main方法
        $result = $qrcodeController->main(true);
        return $this->succeed('success', ['image'=>$result['img']]);
    }
    // 招商经理入驻二维码
    public function copartnerQrcode()
    {
        global $_W;
        $copartnerLogic = app(CopartnerLogic::class);
        $account = $copartnerLogic->getCopartnerAccount($this->memberId);
        if(empty($account) || $account['isfounder']!=1) {
            return JsonService::fail(AppError::getError(AppError::$AccessDenied), [], AppError::$AccessDenied);
        }

        $img = (new CopartnerModel())->createCopartnerImage($this->memberId);

        return $this->succeed('success', ['image'=> $img]);
    }
}
