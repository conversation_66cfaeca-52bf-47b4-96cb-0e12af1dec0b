<?php
namespace app\controller\api\v1;

use app\BaseController;
use app\com\enum\member\MemberTerminalEnum;
use app\com\service\payment\AliPayService;
use app\com\service\payment\WxPayService;
use app\controller\AppMobilePage;
use app\controller\Page;

/**
 * 支付相关回调
 * Class PayController
 * @package app\controller\api\v1
 */
class PayController extends AppMobilePage
{
    public array $notNeedLogin = ['notify', 'refunded'];

    /**
     * 支付回调
     * @param string $type
     * @return string|\think\Response
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function notify(string $type) //todo 微信的暂时不区分支付方式，后期业务拓展可利用instance传参区分
    {
        switch (urldecode($type)) {
            case 'alipay':
                return AliPayService::handleNotify();
                break;
            case 'mini':
                return WxPayService::instance(MemberTerminalEnum::WECHAT_MMP)->handleNotify();
                break;
            case 'wechat':
                return WxPayService::instance(MemberTerminalEnum::WECHAT_OA)->handleNotify();
                break;
            case 'app':
                return WxPayService::instance(MemberTerminalEnum::ANDROID)->handleNotify();
                break;
        }
    }

    /**
     * 退款回调
     * @param string $type
     * @return \think\Response
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function refunded(string $type) //微信的暂时不区分方式
    {
        switch (urldecode($type)) {
            case 'alipay':

                break;
            case 'mini':
                return WxPayService::instance(MemberTerminalEnum::WECHAT_MMP)->handleRefundedNotify();
                break;
            case 'wechat':
                return WxPayService::instance(MemberTerminalEnum::WECHAT_OA)->handleRefundedNotify();
                break;
            case 'app':
                return WxPayService::instance(MemberTerminalEnum::ANDROID)->handleRefundedNotify();
                break;
        }
    }
}
