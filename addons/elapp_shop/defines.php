<?php

define('ELAPP_SHOP_DEBUG', false);
if (!defined("ELAPP_SHOP_MODULE_NAME")) {
    $dirPath = __DIR__;
    if (strstr($dirPath, "/") != false) {
        $dir = explode("/", $dirPath);
    } else {
        $dir = explode("\\", $dirPath);
    }
    $module = array_pop($dir);
    define("ELAPP_SHOP_MODULE_NAME", $module);
}
!(defined('ELAPP_SHOP_PATH')) && define('ELAPP_SHOP_PATH', IA_ROOT . '/addons/elapp_shop/');
!(defined('ELAPP_SHOP_CORE')) && define('ELAPP_SHOP_CORE', ELAPP_SHOP_PATH . 'core/');
!(defined('ELAPP_SHOP_DATA')) && define('ELAPP_SHOP_DATA', ELAPP_SHOP_PATH . 'data/');
!(defined('ELAPP_SHOP_VENDOR')) && define('ELAPP_SHOP_VENDOR', ELAPP_SHOP_PATH . 'vendor/');
!(defined('ELAPP_SHOP_CORE_WEB')) && define('ELAPP_SHOP_CORE_WEB', ELAPP_SHOP_CORE . 'web/');
!(defined('ELAPP_SHOP_CORE_MOBILE')) && define('ELAPP_SHOP_CORE_MOBILE', ELAPP_SHOP_CORE . 'mobile/');
!(defined('ELAPP_SHOP_CORE_SYSTEM')) && define('ELAPP_SHOP_CORE_SYSTEM', ELAPP_SHOP_CORE . 'system/');
!(defined('ELAPP_SHOP_PLUGIN')) && define('ELAPP_SHOP_PLUGIN', ELAPP_SHOP_PATH . 'plugin/');
!(defined('ELAPP_SHOP_PROCESSOR')) && define('ELAPP_SHOP_PROCESSOR', ELAPP_SHOP_CORE . 'processor/');
!(defined('ELAPP_SHOP_INC')) && define('ELAPP_SHOP_INC', ELAPP_SHOP_CORE . 'inc/');
!(defined('ELAPP_SHOP_URL')) && define('ELAPP_SHOP_URL', (isset($_W) && isset($_W['siteroot']) ? $_W['siteroot'] : '') . 'addons/elapp_shop/');
!(defined('ELAPP_SHOP_TASK_URL')) && define('ELAPP_SHOP_TASK_URL', (isset($_W) && isset($_W['siteroot']) ? $_W['siteroot'] : '') . 'task/');
!(defined('ELAPP_SHOP_LOCAL')) && define('ELAPP_SHOP_LOCAL', '../addons/elapp_shop/');
!(defined('ELAPP_SHOP_STATIC')) && define('ELAPP_SHOP_STATIC',  '/static/application/shop/');
!(defined('ELAPP_SHOP_PREFIX')) && define('ELAPP_SHOP_PREFIX', 'elapp_shop_');
define('HTTP_X_FOR', (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == '443') ? 'https://' : 'http://');
!(defined('ELAPP_SHOP_CSS_VERSION')) && define('ELAPP_SHOP_CSS_VERSION', '1.0.0');