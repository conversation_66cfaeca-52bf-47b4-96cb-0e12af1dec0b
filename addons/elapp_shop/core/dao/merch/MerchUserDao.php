<?php

namespace app\core\dao\merch;

use app\core\dao\BaseDao;
use app\core\model\merch\MerchUserModel;

/**
 * 多商户用户 数据层
 * Class MerchUserDao
 */
class MerchUserDao extends BaseDao
{
    protected function setModel(): string
    {
        return MerchUserModel::class;
    }

    /**
     * 多商户搜索器
     * @param array $where
     * @return \app\BaseModel|\think\Model
     */
    function search(array $where = [])
    {
        $where['deleted'] = 0;
        //todo 根据场景条件过滤查询条件
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

    /**
     * 多商户列表【可搜索】
     * @param array  $where
     * @param array  $field
     * @param int    $page
     * @param int    $limit
     * @param array  $with
     * @param string $order
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMerchUserList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {

        return $this->search($where)->field($field)->with(array_merge(['member'], $with))->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

    /**
     * 多商户详情
     * @param string $key 多商户标识 id mid openid
     * @param string $field 查询字段
     * @param array  $with 关联查询
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMerchUserDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->getOne(['id|mid|openid' => $key], $field, $with);
    }
}