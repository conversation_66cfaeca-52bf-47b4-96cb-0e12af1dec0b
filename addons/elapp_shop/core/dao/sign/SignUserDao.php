<?php

namespace app\core\dao\sign;

use app\core\dao\BaseDao;
use app\core\model\sign\SignUserModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 积分签到配置 数据层
 * Class SignUserDao
 * @package app\core\dao\sign
 * <AUTHOR>
 * @date 2024/07/08 21:26
 */
class SignUserDao extends BaseDao
{
    protected function setModel(): string
    {
        return SignUserModel::class;
    }

    /**
     * 获取签到记录详情
     * @param array $key
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getSignUserDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }
    /**
     * @desc 获取签到记录列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getSignUserList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}