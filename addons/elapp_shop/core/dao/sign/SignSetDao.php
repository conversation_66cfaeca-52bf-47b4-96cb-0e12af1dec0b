<?php

namespace app\core\dao\sign;

use app\core\dao\BaseDao;
use app\core\model\sign\SignSetModel;
use Services_JSON_Error;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 积分签到配置 数据层
 * Class SignSetDao
 * @package app\core\dao\sign
 * <AUTHOR>
 * @date 2024/07/08 21:26
 */
class SignSetDao extends BaseDao
{
    protected function setModel(): string
    {
        return SignSetModel::class;
    }

    /**
     * 获取积分签到配置信息
     * @param array $key
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getSignSet(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }
}