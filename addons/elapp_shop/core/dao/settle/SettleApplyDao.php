<?php

namespace app\core\dao\settle;

use app\core\dao\BaseDao;
use app\model\SettleWithdrawApplyModel;

class SettleApplyDao extends BaseDao
{

    function setModel(): string
    {
        return SettleWithdrawApplyModel::class;
    }

    /**
     * 获取列表
     * @param array $where
     * @param string $field
     * @param int $page
     * @param int $limit
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getList(array $where, string $field = '*', int $page = 0, int $limit = 0): array
    {
        return $this->search($where)->field($field)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->select()->toArray();
    }
}