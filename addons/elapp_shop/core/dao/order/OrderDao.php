<?php

namespace app\core\dao\order;

use app\core\dao\BaseDao;
use app\model\OrderModel;

/**
 * 商品订单
 */
class OrderDao extends BaseDao
{
    protected function setModel(): string
    {
        return OrderModel::class;
    }

    /**
     * 订单搜索器
     * @param array $where
     * @return \app\BaseModel|\think\Model
     */
    function search(array $where = [])
    {
        $where['deleted'] = 0;
        //todo 根据场景条件过滤查询条件
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

    /**
     * @desc 通过订单号获取订单信息
     */
    public function getOrderInfoByOrderId($orderId) {

        $order = $this->getModel()->with(['orderGoods.goods'])->find(['id' => $orderId]);
        return $order;
    }

    /**
     * 订单列表【可搜索】
     * @param array $where
     * @param array $field
     * @param int $page
     * @param int $limit
     * @param array $with
     * @param string $order
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {

        return $this->search($where)->field($field)->with(array_merge(['member', 'orderGoods'], $with))->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

    /**
     * 获取订单详情
     * @param string $key 订单的唯一标识 id ordersn transid
     * @param string $field
     * @param array $with
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->getOne(['ordersn|id' => $key], $field, $with);
    }
}