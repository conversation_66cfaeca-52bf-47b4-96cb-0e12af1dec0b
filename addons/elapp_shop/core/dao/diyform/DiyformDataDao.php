<?php

namespace app\core\dao\diyform;

use app\core\dao\BaseDao;
use app\core\model\diyform\DiyformDataModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 自定义表单用户收集数据 数据层
 * Class DiyformDataDao
 * @package app\core\dao\diyform
 * <AUTHOR>
 * @date 2024/07/29 17:26
 */
class DiyformDataDao extends BaseDao
{
    protected function setModel(): string
    {
        return DiyformDataModel::class;
    }

    /**
     * 获取[单条]用户收集数据记录
     * @param array $key
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDiyformDataDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }
    /**
     * @desc 获取[多条]用户收集数据列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDiyformDataList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

    /**
     * 获取表单用户收集数据相关的信息
     * @param array $where
     * @param string $field
     * @param array $withJoin
     * @return array|mixed|\think\db\BaseQuery|\think\Model
     */
    function getDataInfo(array $where, string $field = '*', array $withJoin = [])
    {
        return $this->getModel()->withoutGlobalScope(['uniacid'])->where($where)->field($field)->withJoin($withJoin, 'LEFT')->findOrEmpty();
        //dump($this->getModel()->getLastSql());
    }

    /**
     * @desc 获取表单用户收集数据列表相关信息 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $withJoin   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getDataList(array $where, array $field, int $page = 0, int $limit = 0, array $withJoin = [], string $order = 'id DESC')
    {
        return $this->getModel()->withoutGlobalScope(['uniacid'])->where($where)->field($field)->withJoin($withJoin, 'LEFT')->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
        //dump($this->getModel()->getLastSql());
    }
}