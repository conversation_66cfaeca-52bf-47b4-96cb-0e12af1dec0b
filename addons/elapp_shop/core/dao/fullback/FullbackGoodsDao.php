<?php

namespace app\core\dao\fullback;

use app\BaseModel;
use app\core\dao\BaseDao;
use app\core\model\fullback\FullbackGoodsModel;
use app\model\GoodsModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 商品表
 */
class FullbackGoodsDao extends BaseDao
{
    protected function setModel(): string
    {
        return FullbackGoodsModel::class;
    }

    /**
     * 全返商品搜索器
     * @param array $where
     * @return BaseModel|Model
     */
    function search(array $where = [])
    {
        //$where['deleted'] = 0;
        //todo 根据场景条件过滤查询条件
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

    /**
     * 获取[单个]全返商品信息
     * @param array $key id
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getFullbackGoods(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }

    /**
     * 获取[多个]全返商品列表【可搜索】
     * @param array $where
     * @param array $field
     * @param int $page
     * @param int $limit
     * @param array $with
     * @param string $order
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getFullbackGoodsList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC'): array
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}