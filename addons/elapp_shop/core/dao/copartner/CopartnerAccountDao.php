<?php

namespace app\core\dao\copartner;

use app\core\dao\BaseDao;
use app\core\model\copartner\CopartnerAccountModel;

/**
 * 合伙人 子账户 数据层
 * Class CopartnerAccountDao
 */
class CopartnerAccountDao extends BaseDao
{
    protected function setModel(): string
    {
        return CopartnerAccountModel::class;
    }

    /**
     * 合伙人搜索器
     * @param array $where
     * @return \app\BaseModel|\think\Model
     */
    function search(array $where = [])
    {
        $where['deleted'] = 0;
        //todo 根据场景条件过滤查询条件
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

    /**
     * 合伙人子账户列表【可搜索】
     * @param array  $where
     * @param array  $field
     * @param int    $page
     * @param int    $limit
     * @param array  $with
     * @param string $order
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCopartnerAccountList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {

        return $this->search($where)->field($field)->with(array_merge(['member'], $with))->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

    /**
     * 获取合伙人子账户详情
     * @param string $key 合伙人子账户唯一标识 id mid openid
     * @param string $field 查询字段
     * @param array  $with 关联查询
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getCopartnerAccountDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->getOne(['id|mid|openid' => $key], $field, $with);
    }

    /**
     * 获取与合伙人账户相关的信息
     * @param $where
     * @param $field
     * @param $withJoin
     * @return array|mixed|\think\db\BaseQuery|\think\Model
     */
    function getCopartnerAccount($where, $field = '*', $withJoin = [])
    {
        return $this->getModel()->withoutGlobalScope(['uniacid'])->where($where)->field($field)->withJoin($withJoin, 'LEFT')->findOrEmpty();
    }

    /**
     * 是否是机构合伙人
     * @param $mid
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function isCopartnerFounder($mid)
    {
        $res = $this->getOne(['mid' => $mid, 'isfounder' => 1, 'status' => 1, 'del_at' => 0], 'id');
        return !empty($res) ? true : false;
    }
}