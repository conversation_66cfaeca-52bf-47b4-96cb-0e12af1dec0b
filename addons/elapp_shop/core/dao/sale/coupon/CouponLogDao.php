<?php

namespace app\core\dao\sale\coupon;

use app\core\dao\BaseDao;
use app\core\model\sale\coupon\CouponLogModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 优惠券日志 数据层
 * Class CouponDataDao
 * @package app\core\dao\sale\coupon
 * <AUTHOR>
 * @date 2024/07/23 21:26
 */
class CouponLogDao extends BaseDao
{
    protected function setModel(): string
    {
        return CouponLogModel::class;
    }

    /**
     * 获取单条优惠券日志
     * @param array $key
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getCouponLogDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }
    /**
     * @desc 获取多条优惠券日志列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getCouponLogList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}