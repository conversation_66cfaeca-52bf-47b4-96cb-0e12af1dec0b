<?php

namespace app\core\dao\member;

use app\core\dao\BaseDao;
use app\model\MemberModel;

class MemberDao extends BaseDao
{
    protected function setModel(): string
    {
        return MemberModel::class;
    }

    /**
     * 获取会员信息
     * @param string $key
     * @param string $field
     * @param array $with
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMemberDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->getOne(['id|mobile|openid|openid_wa|openid_wx' => $key], $field, $with);
    }
}