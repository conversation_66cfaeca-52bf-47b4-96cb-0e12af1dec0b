<?php

namespace app\core\dao\member\favorite;

use app\core\dao\BaseDao;
use app\core\model\member\favorite\MemberFavoriteModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;

/**
 * 收藏 数据层
 * Class MemberFavoriteDao
 * @package app\core\dao\member\favorite
 * <AUTHOR>
 * @date 2024/07/03 21:26
 */
class MemberFavoriteDao extends BaseDao
{
    protected function setModel(): string
    {
        return MemberFavoriteModel::class;
    }

    /**
     * 获取单条收藏记录
     * @param array $key
     * @param string $field
     * @param array $with
     * @return array|Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getMemberFavoriteDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }
    /**
     * @desc 获取收藏列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getMemberFavoriteList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}