<?php

namespace app\core\dao\prescription;

use app\core\dao\BaseDao;
use app\core\model\prescription\PrescriptionSetModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\response\Json;

/**
 * 云处方配置
 *
 */
class PrescriptionSetDao extends BaseDao
{
    protected function setModel(): string
    {
        return PrescriptionSetModel::class;
    }
    /**
     * 获取云处方配置
     * @param int $merchid
     * @return array|false|string|Json|object
     * @throws DbException
     * @throws DataNotFoundException
     * @throws ModelNotFoundException
     */
    public function getSet(int $merchid = 0)
    {
        return $this->getModel()->where(['merchid' => $merchid])->find();
    }

    /**
     * 保存云处方配置
     * @param array $data
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function saveSet(array $data): bool
    {
        try {
            $this->getModel()->save($data);
            return true;
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

}