<?php

namespace app\core\dao\prescription;

use app\core\dao\BaseDao;
use app\core\model\prescription\PrescriptionOrderGoodsModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 处方订单商品表
 */
class PrescriptionOrderGoodsDao extends BaseDao
{
    protected function setModel(): string
    {
        return PrescriptionOrderGoodsModel::class;
    }

    /**
     * 获取处方订单商品详情
     * @param array $key 处方商品唯一标识 (goodsid && orderid)|id
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getOrderGoodsDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }

    /**
     * @desc 处方订单商品列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrderGoodsList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

}