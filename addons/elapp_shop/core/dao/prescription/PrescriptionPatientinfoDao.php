<?php

namespace app\core\dao\prescription;

use app\core\dao\BaseDao;
use app\core\model\prescription\PrescriptionPatientinfoModel;
use Services_JSON_Error;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 病历信息
 */
class PrescriptionPatientinfoDao extends BaseDao
{
    protected function setModel(): string
    {
        return PrescriptionPatientinfoModel::class;
    }

    /**
     * @desc 获取病历详情
     * @param array  $key    病历唯一标识 id
     * @param string $field  要获取的字段[字符串]
     * @param array  $with   要加载的关联模型[数组]
     * @return array|false|float|int|mixed|Services_JSON_Error|string|object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getPatientinfoDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with)->toArray();
    }

    /**
     * @desc 获取病历列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param int    $page   页码（默认为 0）
     * @param int    $limit  每页限制数量（默认为 0）
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getPatientinfoList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC')
    {
        return $this->search($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }

    /**
     * @desc 病历搜索器
     * @param array $where
     * @return \app\BaseModel|\think\Model
     */
    function search(array $where = [])
    {
        // 过滤查询条件
        $where['deleted'] = 0;
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

}