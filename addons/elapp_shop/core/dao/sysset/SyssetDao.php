<?php

namespace app\core\dao\sysset;

use app\core\dao\BaseDao;
use app\core\model\setting\SyssetModel;

class SyssetDao extends BaseDao
{

    protected function setModel(): string
    {
        return SyssetModel::class;
    }

    /**
     * 获取sets设置
     * @param $key
     * @param $value
     * @param $is_deep
     * @return array|mixed
     */
    function sysSetsConfig($key, $value = null, $is_deep = false)
    {
        $config = $this->getModel()->getSysSet('sets');
        $res = find_value_by_key_iterative($config, $key, $value, $is_deep);
        if (is_string($key)) {
            return $res[$key];
        }
        return $res;
    }

    /**
     * 获取plugins设置
     * @param $key
     * @param $value
     * @param $is_deep
     * @return array|mixed
     */
    function sysPluginsConfig($key, $value = null, $is_deep = false)
    {
        $config = $this->getModel()->getSysSet('plugins');
        $res = find_value_by_key_iterative($config, $key, $value, $is_deep);
        if (is_string($key)) {
            return $res[$key];
        }
        return $res;
    }

    /**
     * 获取sec设置
     * @param $key
     * @param $value
     * @param $is_deep
     * @return array|mixed
     */
    function sysSecConfig($key, $value = null, $is_deep = false)
    {
        $config = $this->getModel()->getSysSet('sec');
        $res = find_value_by_key_iterative($config, $key, $value, $is_deep);
        if (is_string($key)) {
            return $res[$key];
        }
        return $res;
    }
}