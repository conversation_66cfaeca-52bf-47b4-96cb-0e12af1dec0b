<?php

namespace app\core\dao\goods;

use app\core\dao\BaseDao;
use app\model\GoodsModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 商品表
 */
class GoodsDao extends BaseDao
{
    protected function setModel(): string
    {
        return GoodsModel::class;
    }

    /**
     * 商品搜索器
     * @param array $where
     * @return \app\BaseModel|\think\Model
     */
    function search(array $where = [])
    {
        $where['deleted'] = 0;
        //todo 根据场景条件过滤查询条件
        return parent::search($where)->when(1,$where)->when(2,$where);
    }

    /**
     * 获取单个商品信息
     * @param string $key 商品唯一标识 id | goodssn商品编码 | productsn 商品条码
     * @param string $field
     * @param array $with
     * @return array|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->getOne(['id|goodssn|productsn' => $key], $field, $with);
    }

    /**
     * 商品列表【可搜索】
     * @param array $where
     * @param array $field
     * @param int $page
     * @param int $limit
     * @param array $with
     * @param string $order
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC'): array
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}