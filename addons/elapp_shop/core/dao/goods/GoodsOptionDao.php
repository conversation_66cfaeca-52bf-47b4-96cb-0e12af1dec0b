<?php

namespace app\core\dao\goods;

use app\core\dao\BaseDao;
use app\model\GoodsOptionModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 商品规格表 数据层
 * Class GoodsOptionDao
 */
class GoodsOptionDao extends BaseDao
{
    protected function setModel(): string
    {
        return GoodsOptionModel::class;
    }

    /**
     * 获取商品规格信息
     * @param array $key 商品规格ID、商品ID
     * @param string $field
     * @param array $with
     * @return array|\think\Model|null
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsOptionDetail(array $key, string $field = '*', array $with = [])
    {
        return $this->getOne($key, $field, $with);
    }

    /**
     * 商品规格列表【可搜索】
     * @param array $where
     * @param array $field
     * @param int $page
     * @param int $limit
     * @param array $with
     * @param string $order
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsOptionList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC'): array
    {
        return $this->getModel()->where($where)->field($field)->with($with)->when($page && $limit, function ($query) use ($page, $limit) {
            $query->page($page, $limit);
        })->when(!$page && $limit, function ($query) use ($limit) {
            $query->limit($limit);
        })->order($order)->select()->toArray();
    }
}