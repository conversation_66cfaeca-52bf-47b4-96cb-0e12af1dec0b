<?php
namespace app\controller;

use framework\classes\account\WeModuleProcessor;
use think\exception\ErrorException;

require dirname(__DIR__) . '/../defines.php';
class PluginProcessor extends WeModuleProcessor {
	public $model;
	public $modulename;
	public $message;

	public function __construct($name = '')	{
        parent::__construct();
		global $_W;
		$this->modulename = 'elapp_shop';
		$this->pluginname = $name;
		$secure = $this->getIsSecureConnection();
		$http = $secure ? 'https' : 'http';
		$_W['siteroot'] = strexists($_W['siteroot'], 'https://') ? $_W['siteroot'] : str_replace('http', $http, $_W['siteroot']);
		//$this->loadModel();
        try {
            $classname = 'app\\model\\' . ucfirst($name) . 'Model';
            $this->model = new $classname($this->pluginname);
        } catch (ErrorException $e) {
            return json($e->getMessage());
        }
	}

	/**
     * 加载插件model
     */
	private function loadModel(){
		$modelfile = IA_ROOT . '/addons/' . $this->modulename . '/plugin/' . $this->pluginname . '/core/model.php';

		if (is_file($modelfile)) {
			$classname = ucfirst($this->pluginname) . 'Model';
			require $modelfile;
			$this->model = new $classname($this->pluginname);
		}
	}

	public function respond($obj = ''){
		$this->message = $this->message;
	}

	public function getIsSecureConnection(){
		if (isset($_SERVER['HTTPS']) && ('1' == $_SERVER['HTTPS'] || 'on' == strtolower($_SERVER['HTTPS']))) {
			return true;
		}

		if (isset($_SERVER['SERVER_PORT']) && '443' == $_SERVER['SERVER_PORT']) {
			return true;
		}

		return false;
	}
}
