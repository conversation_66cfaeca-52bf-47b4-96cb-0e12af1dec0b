<?php
namespace app\controller;

class MobilePage extends Page {
	public $footer = array();
	public $followBar = false;
	protected $merch_user = array();
	protected $copartner_user = array();
	protected $supply_user = array();//供应商

    protected array $memberInfo = [];
    protected int $memberId = 0;
    protected string $openid = '';

    function initialize()
    {
        parent::initialize();
        if (isset($this->request->memberInfo) && !empty($this->request->memberInfo)) {
            global $_W;
            $this->memberInfo = $this->request->memberInfo;
            $this->memberId = $this->request->memberId;
            $_W['openid'] = $this->openid = $this->request->memberInfo['openid'];
        }
    }

	public function __construct(){
        parent::__construct();
		global $_W, $_GPC;
		//检测商城是否关闭
		m('shop')->checkClose();
		$preview = intval($_GPC['preview']);
		$wap = m('common')->getSysset('wap');
		if(!empty($wap['open']) && !is_weixin() && empty($preview) && !$this->isNotNeedLogin()) {
			if ($this instanceof MobileLoginPage || $this instanceof PluginMobileLoginPage) {
				if (empty($_W['openid'])) {
					$_W['openid'] = m('account')->checkLogin();
				}
			}else{
				$_W['openid'] = m('account')->checkOpenid();
			}
		}else{
			if ($preview && !is_weixin()) {
				$_W['openid'] = 'oyDB75w7_WjEguyi5_02KJdl6u6Q';
			}

			if (ELAPP_SHOP_DEBUG){
				$_W['openid'] = 'oyDB75w7_WjEguyi5_02KJdl6u6Q';
			}
		}

		$member = m('member')->checkMember();
        $this->memberInfo = !empty($this->memberInfo) ? $this->memberInfo :($member['member_info'] ?? []);
        $this->memberId = !empty($this->memberId) ? $this->memberId : ($member['id'] ?? 0);
        $this->openid = !empty($this->openid) ? $this->openid : ($member['openid'] ?? '');
        $_W['openid'] = !empty($_W['openid']) ? $_W['openid'] : $this->openid;
		$_W['mid'] = !empty($member) ? $member['id'] : '';
		$_W['mopenid'] = !empty($member) ? $member['openid'] : '';
		//多商户
		$merch_plugin = p('merch');
		$merch_data = m('common')->getPluginset('merch');
		if (!empty($_GPC['merchid']) && ($merch_plugin && $merch_data['is_openmerch'])) {
			$this->merch_user = pdo_fetch('select * from ' . tablename('elapp_shop_merch_user') . ' where id=:id limit 1', array(':id' => intval($_GPC['merchid'])));
		}
	}

	public function followBar($diypage = false, $merch = false)	{
		global $_W, $_GPC;
		if (is_h5app() || !is_weixin()) {
			return NULL;
		}

		$openid = $_W['openid'];
		$followed = m('user')->followed($openid);
		$mid = intval($_GPC['mid']);
		$memberid = m('member')->getMid();

		if(p('diypage')) {
			if($merch && p('merch')){
				$diypagedata = p('merch')->getSet('diypage', $merch);
			}else{
				$diypagedata = m('common')->getPluginset('diypage');
			}
			$diyfollowbar = $diypagedata['followbar'];
		}

		if($diypage){
			$diyfollowbar['params']['isopen'] = 1;
		}

		@session_start();
		if (!$followed || (!empty($diyfollowbar['params']['showtype']) && !empty($diyfollowbar['params']['isopen']))) {
			$set = $_W['shopset'];
			$followbar = array(
				'followurl' => $set['share']['followurl'],
				'shoplogo' => tomedia($set['shop']['logo']), 
				'shopname' => $set['shop']['name'], 
				'qrcode' => tomedia($set['share']['followqrcode']), 
				'share_member' => false
			);

			$friend = false;
			if (!empty($mid) && $memberid != $mid) {
				if (!empty($_SESSION[ELAPP_SHOP_PREFIX . '_shareid']) && $_SESSION[ELAPP_SHOP_PREFIX . '_shareid'] == $mid) {
					$mid = $_SESSION[ELAPP_SHOP_PREFIX . '_shareid'];
				}
				$member = m('member')->getMember($mid);
				if (!empty($member)) {
					$_SESSION[ELAPP_SHOP_PREFIX . '_shareid'] = $mid;
					$friend = true;
					$followbar['share_member'] = array('id' => $member['id'], 'nickname' => $member['nickname'], 'realname' => $member['realname'], 'avatar' => $member['avatar']);
				}
			}

			$showdiyfollowbar = false;
			if(p('diypage')){
				if((!empty($diyfollowbar) && !empty($diyfollowbar['params']['isopen'])) || (!empty($diyfollowbar) && $diypage)){
					$showdiyfollowbar = true;
					if(!empty($followbar['share_member'])){
						if(!empty($diyfollowbar['params']['sharetext'])){
							$touser = m('member')->getMember($memberid);
							$diyfollowbar['text'] = str_replace('[商城名称]', '<span style="color:' . $diyfollowbar['style']['highlight'] . ';">' . $set['shop']['name'] . '</span>', $diyfollowbar['params']['sharetext']);
							$diyfollowbar['text'] = str_replace('[邀请人]', '<span style="color:' . $diyfollowbar['style']['highlight'] . ';">' . $followbar['share_member']['nickname'] . '</span>', $diyfollowbar['text']);
							$diyfollowbar['text'] = str_replace('[访问者]', '<span style="color:' . $diyfollowbar['style']['highlight'] . ';">' . $touser['nickname'] . '</span>', $diyfollowbar['text']);
						}else{
							$diyfollowbar['text'] = '来自好友<span class="text-danger">' . $followbar['share_member']['nickname'] . '</span>的推荐<br>' . '关注公众号，享专属服务';
						}
					}else{
						if(!empty($diyfollowbar['params']['defaulttext'])){
							$diyfollowbar['text'] = str_replace('[商城名称]', '<span style="color:' . $diyfollowbar['style']['highlight'] . ';">' . $set['shop']['name'] . '</span>', $diyfollowbar['params']['defaulttext']);
						}else{
							$diyfollowbar['text'] = '欢迎进入<span class="text-danger">' . $set['shop']['name'] . '</span><br>' . '关注公众号，享专属服务';
						}
					}
					$diyfollowbar['text'] = nl2br($diyfollowbar['text']);
					$diyfollowbar['logo'] = tomedia($set['shop']['logo']);
					if($diyfollowbar['params']['icontype'] == 1 && !empty($followbar['share_member'])) {
						$diyfollowbar['logo'] = tomedia($followbar['share_member']['avatar']);
					}else{
						if ($diyfollowbar['params']['icontype'] == 3 && !empty($diyfollowbar['params']['iconurl'])){
							$diyfollowbar['logo'] = tomedia($diyfollowbar['params']['iconurl']);
						}
					}

					if(empty($diyfollowbar['params']['btnclick'])){
						if(empty($diyfollowbar['params']['btnlinktype'])){
							$diyfollowbar['link'] = $set['share']['followurl'];
						}else{
							$diyfollowbar['link'] = $diyfollowbar['params']['btnlink'];
						}
					}else{
						if(empty($diyfollowbar['params']['qrcodetype'])){
                            $diyfollowbar['qrcode'] = tomedia($set['share']['followqrcode']);
						}else{
							$diyfollowbar['qrcode'] = tomedia($diyfollowbar['params']['qrcodeurl']);
						}
					}
				}
			}
			if($showdiyfollowbar){
				include $this->template('diypage/followbar');
			}else{
				include $this->template('_followbar');
			}
		}
	}

	public function MemberBar($diypage = false, $merch = false)	{
		global $_W, $_GPC;
		if(is_h5app() || !is_weixin()){
			return NULL;
		}
		$mid = intval($_GPC['mid']);

		$cmember_plugin = p('cmember');
		if (!$cmember_plugin) {
			return NULL;
		}

		$openid = $_W['openid'];
		$followed = m('user')->followed($openid);
		if (!$followed) {
			return NULL;
		}
		$check = $cmember_plugin->checkMember($openid);
		if (!empty($check)) {
			return NULL;
		}

		$data = m('common')->getPluginset('commission');
		if (!empty($data['become_goodsid'])) {
			$goods = pdo_fetch('select id,title,thumb from ' . tablename('elapp_shop_goods') . ' where id=:id and uniacid=:uniacid limit 1 ', array(':id' => $data['become_goodsid'], ':uniacid' => $_W['uniacid']));
		}else{
			return NULL;
		}
		$buy_member_url = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $mid));
		
		include $this->template('cmember/_memberbar');
	}

	public function footerMenus($diymenuid = NULL, $ismerch = false, $texts = array()){
		global $_W, $_GPC;
		$params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
		$cartcount = pdo_fetchcolumn('select ifnull(sum(total),0) from ' . tablename('elapp_shop_member_cart') . ' where uniacid=:uniacid and openid=:openid and deleted=0 and isnewstore=0  and selected =1', $params);
		//店员底部菜单 Hlei2023-02-27
		$clerk = array();
		if(p('clerk') && intval($_W['shopset']['clerk']['level']>0)){
			$member = m('member')->getMember($_W['openid']);
			if(!$member['clerk_black']) {
				if ($member['is_clerk'] == 1 && $member['clerk_status'] == 1) {
					$clerk = array(
						'url' => mobileUrl('clerk'), 
						'text' => empty($_W['shopset']['clerk']['texts']['center']) ? '店员工作台' : $_W['shopset']['clerk']['texts']['center']
					);
				} else {
					$clerk = array(
						'url' => mobileUrl('clerk/register'), 
						'text' => empty($_W['shopset']['clerk']['texts']['become']) ? '成为店员' : $_W['shopset']['clerk']['texts']['become']
					);
				}
			}
		}
		$commission = array();
		if(p('commission') && intval($_W['shopset']['commission']['level']>0)){
			$member = m('member')->getMember($_W['openid']);
			if(!$member['agentblack']) {
				if ($member['isagent'] == 1 && $member['status'] == 1) {
					$commission = array(
						'url' => mobileUrl('commission'), 
						'text' => empty($_W['shopset']['commission']['texts']['center']) ? '分销中心' : $_W['shopset']['commission']['texts']['center']
					);
				} else {
					$commission = array(
						'url' => mobileUrl('commission/register'), 
						'text' => empty($_W['shopset']['commission']['texts']['become']) ? '成为分销商' : $_W['shopset']['commission']['texts']['become']
					);
				}
			}
		}

		$showdiymenu = false;
		$routes = explode('.', $_W['routes']);
		$controller = $routes[0];
		if($controller == 'member' || $controller == 'cart' || $controller == 'order' || $controller == 'goods' || $controller == 'quick') {
			$controller = 'shop';
		}

		if(empty($diymenuid)) {
			$pageid = !empty($controller) ? $controller : 'shop';
			$pageid = $pageid == 'index' ? 'shop' : $pageid;
			if(!empty($_GPC['merchid']) && ($_W['routes'] == 'shop.category' || $_W['routes'] == 'goods')){
				$pageid = 'merch';
			}
			if($pageid == 'sale' && $_W['routes'] == 'sale.coupon.my.showcoupongoods') {
				$pageid = 'shop';
			}
			if($pageid == 'merch' && !empty($_GPC['merchid']) && p('merch')) {
				$merchdata = p('merch')->getSet('diypage', $_GPC['merchid']);
				if(!empty($merchdata['menu'])){
					$diymenuid = $merchdata['menu']['shop'];
					if(!is_weixin() || is_h5app()){
						$diymenuid = $merchdata['menu']['shop_wap'];
					}
				}
			}else {
				$diypagedata = m('common')->getPluginset('diypage');
				if(!empty($diypagedata['menu'])) {
					$diymenuid = $diypagedata['menu'][$pageid];
					if(!is_weixin() || is_h5app()){
						$diymenuid = $diypagedata['menu'][$pageid . '_wap'];
					}
				}
			}
		}

		if(!empty($diymenuid)) {
			$menu = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_diypage_menu') . ' WHERE id=:id and uniacid=:uniacid limit 1 ', array(':id' => $diymenuid, ':uniacid' => $_W['uniacid']));
			if(!empty($menu)) {
				$menu = $menu['data'];
				$menu = base64_decode($menu);
				$diymenu = json_decode($menu, true);
				$showdiymenu = true;
			}
		}
		if($showdiymenu){
			include $this->template('diypage/menu');
		}else{
			if($controller == 'commission' && $routes[1] != 'myshop'){
				include $this->template('commission/_menu');
			}else if($controller == 'creditshop'){
				include $this->template('creditshop/_menu');
			}else if($controller == 'groups'){
				include $this->template('groups/_groups_footer');
			}else if($controller == 'merch'){
				include $this->template('merch/_menu');
			}else if($controller == 'mr'){
				include $this->template('mr/_menu');
			}else if($controller == 'newmr'){
				include $this->template('newmr/_menu');
			}else if($controller == 'sign'){
				include $this->template('sign/_menu');
			}else if($controller == 'sns'){
				include $this->template('sns/_menu');
			}else if($controller == 'seckill'){
				include $this->template('seckill/_menu');
			}else if($controller == 'mmanage'){
				include $this->template('mmanage/_menu');
			}else if($ismerch){
				include $this->template('merch/_menu');
			}else if($controller == 'orderhelper'){
				include $this->template('orderhelper/_menu');
			}else if($controller == 'poster'){
				include $this->template('poster/_menu');
			}else{
				include $this->template('_menu');
			}
		}
	}

	public function shopShare()	{
		global $_W, $_GPC;
		$trigger = false;
		if (empty($_W['shopshare'])) {
			$set = $_W['shopset'];
			$_W['shopshare'] = array(
				'title' => empty($set['share']['title']) ? $set['shop']['name'] : $set['share']['title'], 
				'imgUrl' => empty($set['share']['icon']) ? tomedia($set['shop']['logo']) : tomedia($set['share']['icon']), 
				'desc' => empty($set['share']['desc']) ? $set['shop']['description'] : $set['share']['desc'], 
				'link' => empty($set['share']['url']) ? mobileUrl('', NULL, true) : $set['share']['url']
			);
			$plugin_commission = p('commission');
			$plugin_vrshop = p('vrshop');
			$plugin_clerk = p('clerk');			
			$plugin_userpromote = p('userpromote');
			if ($plugin_commission) {
				$set = $plugin_commission->getSet();
				if (!empty($set['level'])) {
					$openid = $_W['openid'];
					$member = m('member')->getMember($openid);
					if (!empty($member) && $member['status'] == 1 && $member['isagent'] == 1) {
						if (empty($set['closemyshop'])) {
							$myshop = $plugin_commission->getShop($member['id']);
							$_W['shopshare'] = array(
								'title' => $myshop['name'], 
								'imgUrl' => tomedia($myshop['logo']), 
								'desc' => $myshop['desc'], 
								'link' => mobileUrl('commission/myshop', 
								array('mid' => $member['id']), true)
							);
						}else {
							$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $member['id']), true) : $_W['shopset']['share']['url'];
						}

						if (empty($set['become_reg']) && (empty($member['realname']) || empty($member['mobile']))) {
							$trigger = true;
						}
					}else{
						if (!empty($_GPC['mid'])) {
							$m = m('member')->getMember($_GPC['mid']);
							if (!empty($m) && $m['status'] == 1 && $m['isagent'] == 1) {
								if (empty($set['closemyshop'])) {
									$myshop = $plugin_commission->getShop($_GPC['mid']);
									$_W['shopshare'] = array(
										'title' => $myshop['name'], 
										'imgUrl' => tomedia($myshop['logo']), 
										'desc' => $myshop['desc'], 
										'link' => mobileUrl('commission/myshop', array('mid' => $member['id']), true)
									);
								} else {
									$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $_GPC['mid']), true) : $_W['shopset']['share']['url'];
								}
							}else {
								$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $_GPC['mid']), true) : $_W['shopset']['share']['url'];
							}
						}
					}
				}
			}			
			//虚拟店铺 店员
			if ($plugin_clerk) {
				$clerkSet = $plugin_clerk->getSet();
				if (!empty($clerkSet['level'])) {
					$openid = $_W['openid'];
					$member = m('member')->getMember($openid);
					if (!empty($member) && $member['status'] == 1 && $member['is_clerk'] == 1) {
						if (empty($clerkSet['closemyshop'])) {
							$myshop = $plugin_clerk->getShop($member['id']);
							$_W['shopshare'] = array(
								'title' => $myshop['name'], 
								'imgUrl' => tomedia($myshop['logo']), 
								'desc' => $myshop['desc'], 
								'link' => mobileUrl('clerk/myshop', 
								array('mid' => $member['id']), true)
							);
						}else {
							$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $member['id']), true) : $_W['shopset']['share']['url'];
						}

						if (empty($set['become_reg']) && (empty($member['realname']) || empty($member['mobile']))) {
							$trigger = true;
						}
					}else{
						if (!empty($_GPC['mid'])) {
							$m = m('member')->getMember($_GPC['mid']);
							if (!empty($m) && $m['status'] == 1 && $m['is_owner'] == 1) {
								if (empty($set['closemyshop'])) {
									$myshop = $plugin_clerk->getShop($_GPC['mid']);
									$_W['shopshare'] = array(
										'title' => $myshop['name'], 
										'imgUrl' => tomedia($myshop['logo']), 
										'desc' => $myshop['desc'], 
										'link' => mobileUrl('clerk/myshop', array('mid' => $member['id']), true)
									);
								} else {
									$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $_GPC['mid']), true) : $_W['shopset']['share']['url'];
								}
							}else {
								$_W['shopshare']['link'] = empty($_W['shopset']['share']['url']) ? mobileUrl('', array('mid' => $_GPC['mid']), true) : $_W['shopset']['share']['url'];
							}
						}
					}
				}
			}
		}
		return $trigger;
	}

	public function diyPage($type) {
		global $_W, $_GPC;
		if(empty($type) || !p('diypage')) {
			return false;
		}
        $member = m('member')->getMember($_W['openid'], false);
		if(method_exists(m('plugin'),'permission')){
			//会员卡
			if (p('membercard') && m('plugin')->permission('membercard')) {
				$list_membercard = p('membercard')->get_Mycard('', 0, 100, 'all');
				$all_membercard = p('membercard')->get_Allcard(1, 100);
				if(p('membercard') && $list_membercard['total'] <= 0 && $all_membercard['total'] <= 0){
					$canmembercard = false;
				}else {
					$canmembercard = true;
				}
			}
		}

		$merch = intval($_GPC['merchid']);
		if ($merch && $type != 'member' && $type != 'commission'){
			if (!p('merch')) {
				return false;
			}
			$diypagedata = p('merch')->getSet('diypage', $merch);
		}else{
			$diypagedata = m('common')->getPluginset('diypage');
			if(p('commission')){
				$comm_set = p('commission')->getSet();
			}
		}
		if(!empty($merch)){
			$merchshop = p('merch')->getListUserOne($merch);
			//经营证照
			if (!empty($merchshop['license'])){
				$licenseData = iunserializer($merchshop['license']);
				$businessLicense = $licenseData['businessLicense'];
				$drugBusinessLicense = $licenseData['drugBusinessLicense'];
				$internetDrugLicense = $licenseData['internetDrugLicense'];
				$medicalDevicesLicense = $licenseData['medicalDevicesLicense'];
				$classIIMedicalDevicesLicense = $licenseData['classIIMedicalDevicesLicense'];
				$foodBusinessLicense = $licenseData['foodBusinessLicense'];
				$pharmacistLicense = $licenseData['pharmacistLicense'];            
			}
			$merchGoodsTotal = p('merch')->getGoodsTotalsAll($merch);//商户商品问题
		}
		$merchSysset = m('common')->getPluginset('merch');
		$membercardset = p("membercard")->getSet();
		//是否收藏了
        $isMerchfollow = p('merch')->isFollow($merch);
		$merchFollowTotal = p('merch')->getFollowTotal($merch);	//商户关注粉丝量	
		if (!empty($diypagedata)) {
			$diypageid = $diypagedata['page'][$type];
			if (!empty($diypageid)) {
				$page = p('diypage')->getPage($diypageid, true);
				if (!empty($page)) {
					p('diypage')->setShare($page);
					$diyitems = $page['data']['items'];
					$diyitem_search = array();
					$diy_topmenu = array();
					if (!empty($diyitems) && is_array($diyitems)) {
						$jsondiyitems = json_encode($diyitems);
						if(strexists($jsondiyitems, 'fixedsearch') || strexists($jsondiyitems, 'topmenu')) {
							foreach ($diyitems as $diyitemid => $diyitem) {
								if ($diyitem['id'] == 'fixedsearch') {
									$diyitem_search = $diyitem;
									unset($diyitems[$diyitemid]);
								}elseif ($diyitem['id'] == 'topmenu') {
										$diy_topmenu = $diyitem;
										//unset($diyitems[$diyitemid]);
									}
								}
								unset($diyitem);
							}
							// 富文本中添加会员数显示变量
							if(strexists($jsondiyitems, 'richtext')) {
								foreach ($diyitems as $diyitemid => &$diyitem) {
									if ($diyitem['id'] == 'richtext') {
										$content = base64_decode($diyitem['params']['content']);
										if (strpos($content, '#会员数#') !== false) {
											$diypagedata = m('common')->getPluginset('diypage');
											$bottombar = $diypagedata['member_num'];
											if (empty($bottombar['isopen']) && $bottombar['isopen1'] == 1) {
												$condition = " and dm.uniacid=:uniacid";
												$params = array(':uniacid' => $_W['uniacid']);
												$join = '';
												$open_redis = function_exists('redis') && !is_error(redis());
												if ($open_redis) {
													$redis_key = "elapp_{$_W['uniacid']}_member_list";
													$total = m('member')->memberRadisCount($redis_key);
													if (!$total) {
														$total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
														m('member')->memberRadisCount($redis_key, $total);
													}
												} else {
													$total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
												}
												$total = $bottombar['fakenum1'] ? $total + $bottombar['fakenum1'] : $total;
												$content = str_replace("#会员数#", $total, $content);
												$diyitem['params']['content'] = base64_encode($content);
											} else {
												unset($diyitems[$diyitemid]);
											}
										}
									}
								}
							unset($diyitem);
						}
						
					}
					$startadv = p('diypage')->getStartAdv($page['diyadv']);

					if($type == 'home') {
						$cpinfos = false;
						$sale_sql = 'SELECT * FROM ' . tablename('elapp_shop_sendticket') . ' WHERE uniacid = ' . intval($_W['uniacid']);
						$sale_set = pdo_fetch($sale_sql);
						if (!empty($sale_set) && $sale_set['status'] == 1){
							if(com('coupon')) {
								$cpinfos = com('coupon')->getInfo();
							}
						}
						//交易增强功能
						$trade = m('common')->getSysset('trade');												

						if(empty($trade['shop_strengthen'])) {
							$order = pdo_fetch('select id,price,`virtual`,createtime  from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and status = 0 and paytype<>3 and openid=:openid order by createtime desc limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
							if(!empty($order)){
								$close_time = 0;
								$mintimes = 0;
								//如果是虚拟卡密
								if (!empty($order['virtual'])) {

									if (isset($trade['closeorder_virtual']) && !empty($trade['closeorder_virtual'])) {
										$mintimes = 60 * intval($trade['closeorder_virtual']);
									} else {
										$mintimes = 60 * 15;
									}
								}else{
									//如果是普通订单
									$days = intval($trade['closeorder']);
									if($days > 0) {
										$mintimes = 86400 * $days;
									}
								}

								if(!empty($mintimes)){
									$close_time = intval($order['createtime']) + $mintimes;
								}

								$goods = pdo_fetchall('select g.*,og.total as totals  from ' . tablename('elapp_shop_order_goods') . ' og inner join ' . tablename('elapp_shop_goods') . ' g on og.goodsid = g.id   where og.uniacid=:uniacid    and og.orderid=:orderid  limit 3', array(':uniacid' => $_W['uniacid'], ':orderid' => $order['id']));
								$goodstotal = pdo_fetchcolumn('select COUNT(*)  from ' . tablename('elapp_shop_order_goods') . ' og inner join ' . tablename('elapp_shop_goods') . ' g on og.goodsid = g.id   where og.uniacid=:uniacid    and og.orderid=:orderid ', array(':uniacid' => $_W['uniacid'], ':orderid' => $order['id']));
							}

                            //获取没有收件地址的已付款订单
                            $no_address_order_result = m('order')->getNoAddressOrder($member['id']);
                            if ($no_address_order_result['code'] == 0) {
                                $no_address_order = $no_address_order_result['data']['order'];
                                $no_address_goods = $no_address_order_result['data']['goods'];
                                $no_address_goods_total = $no_address_order_result['data']['goodstotal'];
                                $no_address_order_days = intval($trade['closeorder']);
                                if ($no_address_order_days > 0) {
                                    $no_address_order_mintimes = 86400 * $no_address_order_days;
                                }
                                if (!empty($no_address_order_mintimes)) {
                                    $no_address_order_close_time = intval($order['createtime']) + $no_address_order_mintimes;
                                }
                            }
						}

                        //历史搜索记录
                        if ($redis = redis()) {
                            $redis_key = 'search-' . $_W['openid'];
                            $keywordsData = $redis->zRevRange($redis_key,0,9);
                        } else {
                            if (!empty($_COOKIE['keywords'])){
                                $keywordsData = explode(',', $_COOKIE['keywords']);
                            }
                        }

                        //没有推荐人不显示会员权益信息 默认显示
                        $memberSysset = m('common')->getSysset('member');
                        $is_not_show_member_equity = 0;
                        $not_show_member_equity = empty($memberSysset['not_show_member_equity']) ? 0 : $memberSysset['not_show_member_equity'];
                        if(empty($member['onmid']) && $not_show_member_equity == 1){
                            $is_not_show_member_equity = 1;
                        }
					}

					include $this->template('diypage');
					exit();
				}
			}
		}
	}

	public function diyLayer($v = false, $diy = false, $merch = false, $goods = array(), $order = array()) {
		global $_W, $_GPC;
		if(!p('diypage') || $diy) {
			return NULL;
		}

		if($merch){
			if(!p('merch')){
				return false;
			}
			$diypagedata = p('merch')->getSet('diypage', $merch);
		}else{
			$diypagedata = m('common')->getPluginset('diypage');
		}

		if(!empty($diypagedata)) {
			$diylayer = $diypagedata['layer'];
			if(empty($diylayer['params']['imgurl'])) return false;
			if(!$diylayer['params']['isopen'] && $v){
				return;
			}
			$matchCount = preg_match("/^((0\\d{2,3}-\\d{7,8})|(1[3584]\\d{9}))\$/", $diylayer["params"]["linkurl"]);
			if (!empty($goods) && $matchCount) {
				$diylayer['params']['linkurl'] .= '&goodsid=' . $goods['id'] . '&merch=' . $goods['merch'];
			}

			if (!empty($order) && $matchCount) {
				$diylayer['params']['linkurl'] .= '&orderid=' . $order['id'];
			}

			include $this->template('diypage/layer');
		}
		return;
	}

	public function diyGotop($v = false, $diy = false, $merch = false)	{
		global $_W, $_GPC;
		if(!p('diypage') || $diy) {
			return NULL;
		}

		if($merch){
			if(!p('merch')){
				return false;
			}
			$diypagedata = p('merch')->getSet('diypage', $merch);
			$page = p('diypage')->getPage($diypagedata['page']['home'], true);
		}else{
			$diypagedata = m('common')->getPluginset('diypage');
		}

		if(!empty($diypagedata)) {
			$diygotop = $diypagedata['gotop'];
			if($merch){
				if(!$page['data']['page']['diygotop']) return;
			}else{
				if(!$diygotop['params']['isopen'] && $v) return;
			}

			include $this->template('diypage/gotop');
		}
		return;
	}
	/**
	 * DIY弹幕
	 */
	public function diyDanmu($diy = false){
		global $_W,$_GPC;

		if(!p('diypage')){
			return;
		}
		$diypagedata = m('common')->getPluginset('diypage');
		$danmu = $diypagedata['danmu'];
		if(empty($danmu) || !$diy && empty($danmu['params']['isopen'])){
			return;
		}
		if(empty($danmu['params']['datatype'])){
			$condition = !empty($_W['openid']) ? ' AND openid!=\'' . $_W['openid'] . '\' ' : '';
			$danmu['data'] = pdo_fetchall('SELECT nickname, avatar as imgurl FROM' . tablename('elapp_shop_member') . ' WHERE uniacid=:uniacid AND nickname!=\'\' AND avatar!=\'\' ' . $condition . ' ORDER BY rand() LIMIT 10', array(':uniacid' => $_W['uniacid']));
			$randstart = !empty($danmu['params']['starttime']) ? intval($danmu['params']['starttime']) : 0;
			$randend = !empty($danmu['params']['endtime']) ? intval($danmu['params']['endtime']) : 0;

			if($randend <= $randstart){
				$randend = $randend + rand(100, 999);
			}
		}
		else if ($danmu['params']['datatype'] == 1) {
			$danmu['data'] = pdo_fetchall('SELECT m.nickname, m.avatar as imgurl, o.createtime as time FROM' . tablename('elapp_shop_order') . ' o LEFT JOIN ' . tablename('elapp_shop_member') . ' m ON m.openid=o.openid WHERE o.uniacid=:uniacid AND m.nickname!=\'\' AND m.avatar!=\'\' ORDER BY o.createtime DESC LIMIT 10', array(':uniacid' => $_W['uniacid']));
		}
		else {
			if ($danmu['params']['datatype'] == 2) {
				$danmu['data'] = set_medias($danmu['data'], 'imgurl');
			}
		}

		if(empty($danmu['data']) || !is_array($danmu['data'])) {
			return NULL;
		}

		foreach($danmu['data'] as $index => $item){
			if(strpos($item['nickname'], '\'') !== false){
				$danmu['data'][$index]['nickname'] = str_replace('\'', '`', $item['nickname']);
				//弹幕转义特殊字符
				$danmu['data'][$index]['nickname'] = str_replace('"', '`', $danmu['data'][$index]['nickname']);
				$danmu['data'][$index]['nickname'] = str_replace(PHP_EOL, '', $danmu['data'][$index]['nickname']);
			}

			if(empty($danmu['params']['datatype'])){
				$time = rand($randstart, $randend);
				$danmu['data'][$index]['time'] = p('diypage')->getDanmuTime($time);
			}
			else if ($danmu['params']['datatype'] == 1) {
				$danmu['data'][$index]['time'] = p('diypage')->getDanmuTime(time() - $item['time']);
			}
			else {
				if ($danmu['params']['datatype'] == 2) {
					$danmu['data'][$index]['time'] = p('diypage')->getDanmuTime($danmu['data'][$index]['time']);
				}
			}
		}

		include $this->template('diypage/danmu');
	}
    //添加功能
    public function diyBottombar($diy=false) {
        global $_W,$_GPC;
		
        if(!p('diypage')){
            return;
        }
        $diypagedata = m('common')->getPluginset('diypage');
        $bottombar = $diypagedata['member_num'];
		$condition = " and dm.uniacid=:uniacid";
		$params = array(':uniacid' => $_W['uniacid']);
		$join = '' ;
        if(empty($bottombar) || (!$diy && empty($bottombar['isopen']))){
            return;
        }
        $open_redis = function_exists('redis') && !is_error(redis());
        if($open_redis) {
                $redis_key = "elapp_{$_W['uniacid']}_member_list";
                $total = m('member')->memberRadisCount($redis_key);
                if(!$total){
                    $total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
                    m('member') -> memberRadisCount($redis_key,$total);
                }
        }else{
                $total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
        }
       $total = $bottombar['fakenum'] ? $total + $bottombar['fakenum']:$total;
        include $this->template('diypage/bottombar');
    }
	public function backliving() {
		global $_W, $_GPC;
		if(!p('live')){
			return;
		}
		if(strexists($_W['routes'], 'live')){
			return false;
		}
		$liveid = intval($_GPC['liveid']);
		if(empty($liveid)){
			return;
		}
		$living = p('live')->isLiving($liveid);
		if(!$living){
			return;
		}
		include $this->template('live/backliving');
	}

	public function wapQrcode() {
		global $_W;
		$currenturl = '';

		if (!is_mobile()) {
			$currenturl = $_W['siteroot'] . 'app.php/index?' . $_SERVER['QUERY_STRING'];
		}

		$shop = m('common')->getSysset('shop');
		$shopname = $shop['name'];
		include $this->template('_wapqrcode');
	}
	
}
