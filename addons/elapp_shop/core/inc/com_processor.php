<?php
namespace app\controller;

use framework\classes\account\WeModuleProcessor;

require dirname(__DIR__) . '/../defines.php';
class ComProcessor extends WeModuleProcessor {
	public $model;
	public $modulename;
	public $message;

	public function __construct($name = '')	{
		$this->modulename = 'elapp_shop';
		$this->pluginname = $name;
		$this->loadModel();
	}

	/**
     * 加载插件model
     */
	private function loadModel() {
		$modelfile = IA_ROOT . '/addons/' . $this->modulename . '/core/com/' . $this->pluginname . '.php';

		if (is_file($modelfile)) {
			$classname = ucfirst($this->pluginname) . '_ElappShopComModel';
			require $modelfile;
			$this->model = new $classname($this->pluginname);
		}
	}

	public function respond() {
		$this->message = $this->message;
	}
}
