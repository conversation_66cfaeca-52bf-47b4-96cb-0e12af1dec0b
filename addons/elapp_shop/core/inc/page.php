<?php
namespace app\controller;

use app\common\service\JsonService;
use framework\classes\account\WeModuleSite;
use app\plugin\faqun\core\logic\FaqunLogic;

/**
 * @notes 相当于全局的CommonBaseController
 */
class Page extends WeModuleSite {

    public array $notNeedLogin = [];

    public function runTasks() {
        global $_W;
        $ELAPP_SHOP_TASK_URL = $_W['siteroot'] . 'task/'; //ELAPP_SHOP_TASK_URL 常量初始化错误，这里从新命名一个变量 BY sixu ********
        load()->func('communication');
        $lasttime = strtotime(m('cache')->getString('receive', 'global'));
        $interval = m('common')->getSysset('task')['receive_time'];
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        
        //如果上次收货时间小
        $current = time();
        // 订单自动签收
        if ($lasttime + $interval <= $current) {
            m('cache')->set('receive', date('Y-m-d H:i:s', $current), 'global');
            (ihttp_request($ELAPP_SHOP_TASK_URL . "order/receive.php", null, null, 10));
        }
        
        //自动关闭订单
        $lasttime = strtotime(m('cache')->getString('closeorder', 'global'));
        $interval = m('common')->getSysset('task')['closeorder_time'];
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('closeorder', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "order/close.php", null, null, 10);
        }
        
        //自动关闭虚拟卡密订单
        $lasttime = strtotime(m('cache')->getString('closeorder_virtual', 'global'));
        $interval_v = intval(m('cache')->getString('closeorder_virtual_time', 'global'));
        if (empty($interval_v)) {
            $interval_v = 60;
        }
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval_v <= $current) {
            m('cache')->set('closeorder_virtual', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "order/close.php", array('uniacid'=>$_W['uniacid']), null, 10);
        }
        
        //自动商品全返
        $lasttime = strtotime(m('cache')->getString('fullback_receive', 'global'));
        $interval = m('common')->getSysset('task')['fullback_receive_time'];
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('fullback_receive', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "order/fullback.php", null, null, 10);
        }
        /*
         * 预售商品到期自动下架
         * */
        $lasttime = strtotime(m('cache')->getString('presell_status', 'global'));
        $interval = m('common')->getSysset('task')['presell_status_time'];
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('presell_status', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "goods/presell.php", null, null, 10);
        }
        
        /*
         * 商品自动上下架
         * */
        $lasttime = strtotime(m('cache')->getString('status_receive', 'global'));
        $interval = m('common')->getSysset('task')['status_receive_time'];
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('status_receive', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "goods/status.php", null, null, 10);
        }
        
        //即将关闭订单
        if (com('coupon')) {
            $lasttime = strtotime(m('cache')->getString('willcloseorder', 'global'));
            $interval = m('common')->getSysset('task')['willcloseorder_time'];
            if (empty($interval)) {
                $interval = 20;  //20
            }
            
            $interval *= 60;//60
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                
                m('cache')->set('willcloseorder', date('Y-m-d H:i:s', $current), 'global');
                //require_once ELAPP_SHOP_PATH.'core/task/order/willclose.php';
                ihttp_request($ELAPP_SHOP_TASK_URL . "order/willclose.php", null, null, 10);
            }
        }
        
        //优惠券自动返利
        if (com('coupon')) {
            $lasttime = strtotime(m('cache')->getString('couponback', 'global'));
            $interval = m('common')->getSysset('task')['couponback_time'];
            if (empty($interval)) {
                $interval = 60;
            }
            $interval *= 60;
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('couponback', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($ELAPP_SHOP_TASK_URL . "coupon/back.php", null, null, 10);
            }
        }
        
        //自动发送卖家通知
        $lasttime = strtotime(m('cache')->getString('sendnotice', 'global'));
        $interval = intval(m('cache')->getString('sendnotice_time', 'global'));
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('sendnotice', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "notice/sendnotice.php", array('uniacid' => $_W['uniacid']), null, 10);
        }
        
        //自动发送周期购卖家发货通知
        $lasttime = strtotime(m('cache')->getString('sendcycelbuy', 'global'));
        $interval = intval(m('cache')->getString('sendcycelbuy_time', 'global'));
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('sendcycelbuy', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "cycelbuy/sendnotice.php", array('uniacid' => $_W['uniacid']), null, 10);
        }
        
        //周期购每期自动收货
        $lasttime = strtotime(m('cache')->getString('cycelbuyreceive', 'global'));
        $interval = intval(m('cache')->getString('cycelbuyreceive_time', 'global'));
        if (empty($interval)) {
            $interval = 60;
        }
        $interval *= 60;
        //如果上次自动关闭时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('cycelbuyreceive', date('Y-m-d H:i:s', $current), 'global');
            ihttp_request($ELAPP_SHOP_TASK_URL . "cycelbuy/receive.php", array('uniacid' => $_W['uniacid']), null, 10);
        }
        
        if (p('groups')) {
            /*
             * 拼团未付款订单自动取消
             * */
            $groups_order_lasttime = strtotime(m('cache')->getString('groups_order_cancelorder', 'global'));
            $groups_order_interval = m('common')->getSysset('task')['groups_order_cancelorder_time'];
            if (empty($groups_order_interval)) {
                $groups_order_interval = 60;
            }
            
            $groups_order_interval *= 60;
            //如果上次自动关闭时间小
            $groups_order_current = time();
            if ($groups_order_lasttime + $groups_order_interval <= $groups_order_current) {
                m('cache')->set('groups_order_cancelorder', date('Y-m-d H:i:s', $groups_order_current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/groups/task/order.php", null, null, 10);
            }
            /*
             * 拼团失败自动退款
             * */
            $groups_team_lasttime = strtotime(m('cache')->getString('groups_team_refund', 'global'));
            $groups_team_interval = m('common')->getSysset('task')['groups_team_refund_time'];
            if (empty($groups_team_interval)) {
                $groups_team_interval = 60;
            }
            
            $groups_team_interval *= 60;
            //如果上次自动关闭时间小
            $groups_team_current = time();
            if ($groups_team_lasttime + $groups_team_interval <= $groups_team_current) {
                m('cache')->set('groups_team_refund', date('Y-m-d H:i:s', $groups_team_current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/groups/task/refund.php?uniacid={$_W['uniacid']}", null, null, 10);
            }
            /*
             * 拼团发货自动收货
             * */
            $groups_receive_lasttime = strtotime(m('cache')->getString('groups_receive', 'global'));
            $groups_receive_interval = m('common')->getSysset('task')['groups_receive_time'];
            if (empty($groups_receive_interval)) {
                $groups_receive_interval = 60;
            }
            
            $groups_receive_interval *= 60;
            //如果上次自动关闭时间小
            $groups_receive_current = time();
            if ($groups_receive_lasttime + $groups_receive_interval <= $groups_receive_current) {
                m('cache')->set('groups_receive', date('Y-m-d H:i:s', $groups_receive_current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/groups/task/receive.php", null, null, 10);
            }
        }

        if (p('seckill')) {
            $lasttime = strtotime(m('cache')->getString('seckill_delete_lasttime', 'global'));
            $interval = 5 * 60;
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('seckill_delete_lasttime', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/seckill/task/delete.php", null, null, 10);
            }
        }
        //卡密延迟60秒发送
        //ihttp_request($url=$ELAPP_SHOP_TASK_URL . "order/virtualsend.php", array('uniacid'=>$_W['uniacid'],'acid'=>$_W['acid']),null,1);
        //exit('run finished.');
        
        /**
         * 发送瓜分券失败通知
         */
        if (p('friendcoupon')) {
            $lasttime = strtotime(m('cache')->getString('friendcoupon_send_failed_message', 'global'));
            $interval = 60;
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('friendcoupon_send_failed_message', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/friendcoupon/task/sendMessage.php?uniacid={$_W['uniacid']}", null, null, 10);
            }
        }
        
        //多商户到期自动下架商品
        if (p('merch')) {
            $lasttime = strtotime(m('cache')->getString('merch_expire', 'global'));
            $interval = 5 * 60;
            //如果上次自动关闭时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('merch_expire', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($ELAPP_SHOP_TASK_URL . "plugin/merch.php", null, null, 10);
            }
        }
        //供应商到期自动下架商品 Hlei 2022230328
        if (p('supply')) {
            $lasttime = strtotime(m('cache')->getString('supply_expire', 'global'));
            $interval = 5 * 60;
            //如果上次自动关闭时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('supply_expire', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($ELAPP_SHOP_TASK_URL . "plugin/supply.php", null, null, 10);
            }
        }
        /** 检测核销订单是否快超过时间
         * Hlei
         */
        $lasttime = strtotime(m('cache')->getString('willcloseverifyorder', 'global'));
        $interval = m('common')->getSysset('task')['willcloseverifyorder_time'];
        if (empty($interval)) {
            $interval = 20;  //20
        }

        $interval *= 60;//60
        //如果上次执行时间小
        $current = time();
        if ($lasttime + $interval <= $current) {
            m('cache')->set('willcloseverifyorder', date('Y-m-d H:i:s', $current), 'global');
            //require_once ELAPP_SHOP_PATH.'core/task/order/willclose.php';
            ihttp_request($ELAPP_SHOP_TASK_URL . "order/willcloseverify.php", null, null, 10);
        }
        if (p("subaccount")) {
            $lasttime = strtotime(m("cache")->getString("sub_account_lasttime", "global"));
            $interval = 5 * 60;
            $current = time();
            if ($lasttime + $interval <= $current) {
                m("cache")->set("sub_account_lasttime", date("Y-m-d H:i:s", $current), "global");
                ihttp_request($ELAPP_SHOP_TASK_URL . "order/autosubaccount.php", array("uniacid" => $_W["uniacid"]), NULL, 10);
            }
        }

        if (p('membercard')) {
            //会员卡失效
            $lasttime = strtotime(m('cache')->getString('membercard_expire_lasttime', 'global'));
            $interval = 60;
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('membercard_expire_lasttime', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/membercard/task/expire.php", array("uniacid" => $_W["uniacid"]), null, 10);
            }
            //会员卡订单未支付自动关闭
            $lasttime = strtotime(m('cache')->getString('membercard_order_close_lasttime', 'global'));
            $interval = 60; //是否需要后台设置读取
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('membercard_order_close_lasttime', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/membercard/task/close.php", array("uniacid" => $_W["uniacid"]), null, 10);
            }
        }

        //服务费
        if (p('servicefee')) {
            //服务费订单未支付自动关闭
            $lasttime = strtotime(m('cache')->getString('servicefee_order_close_lasttime', 'global'));
            $interval = 60; //是否需要后台设置读取
            //如果上次执行时间小
            $current = time();
            if ($lasttime + $interval <= $current) {
                m('cache')->set('servicefee_order_close_lasttime', date('Y-m-d H:i:s', $current), 'global');
                ihttp_request($_W['siteroot'] . "plugin/servicefee/task/close.php", array("uniacid" => $_W["uniacid"]), null, 10);
            }
        }


//        $this->execute_job( '订单帮扶收益失效处理',
//            'mentor',
//            ['invalid_by_relation_status_time', 60],
//            'invalid_by_relation_status_time',
//            $_W['siteroot'] . 'plugin/mentor/task/invalid_by_relation_status.php',
//            ['uniacid'=>$_W['uniacid']],
//            null,
//            120);

        // todo 优化添加定时计划
//        if (date('H') < 4) { // 4点前内执行
            $this->execute_job( '订单结算处理',
                'settle',
                ['settle_order_execute_time', 30],
                'settle_order_execute_time',
                $_W['siteroot'] . 'plugin/settle/task/task.php',
                ['uniacid'=>$_W['uniacid']],
                null,
                120, true);
//        }
        $this->execute_job( '订单结算自动审核处理',
            'settle',
            ['settle_order_auto_check', 30],
            'settle_order_auto_check',
            $_W['siteroot'] . 'plugin/settle/task/auto_check.php',
            ['uniacid'=>$_W['uniacid']],
            null,
            120, true);

        if (date('H') < 8) {
        $this->execute_job( '平台奖励结算处理',
            'settle',
            ['platform_subsidy_execute_time', 500],
            'settle_order_execute_time',
            $_W['siteroot'] . 'plugin/settle/task/platform_subsidy.php',
            ['uniacid'=>$_W['uniacid']],
            null,
            120, true);
        }

        // 980活动自动签收处理
        $this->execute_job( '980活动自动签收处理',
                'activity',
                ['activity_980_finish_order', 60],
                'activity_980_finish_order',
                $_W['siteroot'] . 'plugin/activity/task/activity_980_finish_order.php',
                ['uniacid'=>$_W['uniacid']],
                null,
                120);

        $this->execute_job( '980活动用户达标后自动购买服务费',
            'activity',
            ['activity_980_finish_auto_buy_servicefee', 60],
            'activity_980_finish_auto_buy_servicefee',
            $_W['siteroot'] . 'plugin/activity/task/activity_980_finish_auto_buy_servicefee.php',
            ['uniacid'=>$_W['uniacid']],
            null,
            120);

        $this->execute_job( '自动更新用户合伙人字段',
            'copartner',
            ['member_is_copartner_update', 86000],
            'member_is_copartner_update',
            $_W['siteroot'] . 'plugin/copartner/task/member_is_copartner_update.php',
            ['uniacid'=>$_W['uniacid']],
            null,
            30);

        if (date('H') > 8) {
            $this->execute_job('自动审核已购买会员的店长申请',
                'clerk',
                ['auto_pass_clerk', 30],
                'auto_pass_clerk',
                $_W['siteroot'] . 'plugin/clerk/task/auto_pass_clerk.php',
                ['uniacid' => $_W['uniacid']],
                null,
                30);
        }

        // 不知为何使用 execute_job 只好直接调用
        $lasttime = (int)(m('cache')->getString('faqun_batch_send', 'global'));
        if ($lasttime + 3 <= time()) {
            m('cache')->set('faqun_batch_send', time(), 'global');
            (new FaqunLogic())->batchTaskSend();
        }
        
        $lasttime = (int)(m('cache')->getString('faqun_batch_check', 'global'));
        if ($lasttime + 3 <= time()) {
            m('cache')->set('faqun_batch_check', time(), 'global');
            (new FaqunLogic())->checkBatchTaskStatus(1);
        }
    }

    private function plog($title, $log) {
        $log_txt = '';
//        $log['执行时间'] = date('Y-m-d H:i:s');
        foreach ($log as $k=>$v) {
            $log_txt .= $k . ':' . $v . "<br/>";
        }
        plog($title, $log_txt);
    }

    /**
     * @param $task_name string 任务名称
     * @param $plugin string 执行任务插件
     * @param $interval int|array 时间间隔, int（分钟)，或传入 array( set 参数名, 默认值)
     * @param $cache_key string 缓存键名
     * @param $task_url string 任务执行地址
     * @param $post array|null 任务执行参数
     * @param $extra array 附加参数
     * @param $timeout int 超时时间
     * @param bool $task_log 是否记录日志
     * @return void
     */
    public function execute_job($task_name, $plugin, $interval, $cache_key, $task_url, $post = null, $extra = [], $timeout = 60, $task_log = false) {
        $log = [
            '任务'    => $task_name,
        ];
        $start = time();

        try {
            $plugin = p($plugin);
            if (!$plugin) return;

            // 如果是数组，第一个元素是参数名，第二个元素是未设置的情况下的默认值
            if (is_array($interval) && count($interval) == 2) {
                $set = m('common')->getSysset('task');
                $interval = intval($set[$interval[0]] ?? $interval[1]); // 找不到的情况下读取默认值
            }

            if ($interval >= 0) {
                $interval *= 60;
            } else {
                $interval = 3600;
            }

            $lasttime = strtotime(m('cache')->getString($cache_key, 'global'));
            $current = time();
            // 当前时间 > (大于上次执行时间 + 间隔时间) ，执行任务
            if ($lasttime + $interval <= $current) {
                m("cache")->set($cache_key, date("Y-m-d H:i:s", $current), "global");
                ihttp_request($task_url, $post, $extra, $timeout);
            }
            if ($task_log) {
                if ($lasttime + $interval <= $current) {
                    $log['执行状态'] = '成功';
                } else {
                    $log['执行状态'] = '跳过 ' . "$lasttime + $interval <= $current";
                }
            } else {
                return;
            }
        } catch (Exception $e) {
            $log['异常'] = $e->getMessage();
            $log['执行状态'] = '异常';
        }

        $log['执行消耗时间'] = (time() - $start);
        $this->plog('system.cronjob.run', $log);
        //$this->plog('system.debug', $log);
    }

    public function template($filename = '', $type = TEMPLATE_INCLUDEPATH, $account = false){
        global $_W, $_GPC;
        // $set = m('common')->getSysset('template');
        
        $bsaeTemp = array('_header', '_header_base', '_footer', '_tabs', 'funbar');
        if($_W['plugin']=='merch' && $_W['merch_user'] && (!in_array($filename, $bsaeTemp))){
            return $this->template_merch($filename);
        }
        //机构合伙人
        if($_W['plugin']=='copartner' && $_W['copartner_user'] && (!in_array($filename, $bsaeTemp))){
            //return $this->template_copartner($filename);//路由没做好，暂时不用返回
        }
        //供应商 Hlei ********
        if($_W['plugin']=='supply' && $_W['supply_user'] && (!in_array($filename, $bsaeTemp))){
            return $this->template_supply($filename);
        }
        
        // 主商城模板处理
        if (empty($filename)) {
            $filename = str_replace(".", "/", $_W['routes']);
            if (in_array($_W['do'], array('post', 'add', 'edit'))) {
                $filename .= '/post';
            } elseif (strexists($filename, '/index')) {
                if (!empty($_W['do']) && 'main' != $_W['do']) {
                    $filename = str_replace("index", $_W['do'], $filename);
                }
            }
        }
        if (@$_GPC['do'] == 'web' || defined('IN_SYS')) {
            $filename = str_replace("/add", "/post", $filename);
            $filename = str_replace("/edit", "/post", $filename);
            $filename_default = str_replace("/add", "/post", $filename);
            $filename_default = str_replace("/edit", "/post", $filename_default);            
            $filename = 'web/' . $filename_default;
        }
        
        $name = 'elapp_shop';
        $moduleroot = IA_ROOT . "/addons/elapp_shop";
        
        // 管理端
        if (defined('IN_SYS')) {                
            $compile = IA_ROOT . "/data/tpl/web/{$_W['template']}/{$name}/{$filename}.tpl.php";                
            $source = $moduleroot . "/template/{$filename}.html";
            if (!is_file($source)) {
                $source = $moduleroot . "/template/{$filename}/index.html";
            }            
            if (!is_file($source)) {
                $explode = array_slice(explode('/', $filename), 1);
                $temp = array_slice($explode, 1);                
                $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . ".html";
                if (!is_file($source)) {
                    $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . "/index.html";
                }                
            }
        }
        // account页面
        elseif ($account){
            $template = $_W['shopset']['wap']['style'];
            if (empty($template)) {
                $template = "default";
            }
            if (!is_dir($moduleroot . "/template/account/" . $template)) {
                $template = "default";
            }
            $compile = IA_ROOT . "/data/tpl/wap/{$name}/{$template}/account/{$filename}.tpl.php";
            $source = IA_ROOT . "/addons/{$name}/template/account/{$template}/{$filename}.html";
            if (!is_file($source)) {
                $source = IA_ROOT . "/addons/{$name}/template/account/default/{$filename}.html";
            }
            if (!is_file($source)) {
                $source = IA_ROOT . "/addons/{$name}/template/account/default/{$filename}/index.html";
            }
        }
        // 手机端商城页面
        else{
            $template = m('cache')->getString('template_shop');
            if (empty($template)) {
                $template = "default";
            }
            if (!is_dir($moduleroot . "/template/mobile/" . $template)) {
                $template = "default";
            }
            $compile = IA_ROOT . "/data/tpl/wap/{$name}/{$template}/mobile/{$filename}.tpl.php";
            $source = IA_ROOT . "/addons/{$name}/template/mobile/{$template}/{$filename}.html";
            if (!is_file($source)) {
                $source = IA_ROOT . "/addons/{$name}/template/mobile/{$template}/{$filename}/index.html";
            }
            if (!is_file($source)) {
                $source = IA_ROOT . "/addons/{$name}/template/mobile/default/{$filename}.html";
            }
            if (!is_file($source)) {
                $source = IA_ROOT . "/addons/{$name}/template/mobile/default/{$filename}/index.html";
            }
            
            // 插件页面
            if (!is_file($source)) {
                //如果还没有就是插件的
                $names = explode('/', $filename);
                $pluginname = $names[0];
                $ptemplate = m('cache')->getString('template_' . $pluginname);
                if (empty($ptemplate) || $pluginname == 'creditshop') {
                    $ptemplate = "default";
                }
                if (!is_dir($moduleroot . "/plugin/" . $pluginname . "/template/mobile/" . $ptemplate)) {
                    $ptemplate = "default";
                }
                unset($names[0]);
                $pfilename = implode('/', $names);
                $compile = IA_ROOT . "/data/tpl/wap/{$name}/plugin/{$pluginname}/{$ptemplate}/mobile/{$filename}.tpl.php";
                $source = $moduleroot . "/plugin/" . $pluginname . "/template/mobile/" . $ptemplate . "/{$pfilename}.html";
                if (!is_file($source)) {
                    $source = $moduleroot . "/plugin/" . $pluginname . "/template/mobile/" . $ptemplate . "/" . $pfilename . "/index.html";
                }
                if (!is_file($source)) {
                    $source = $moduleroot . "/plugin/" . $pluginname . "/template/mobile/default/{$pfilename}.html";
                }
                if (!is_file($source)) {
                    $source = $moduleroot . "/plugin/" . $pluginname . "/template/mobile/default/" . $pfilename . "/index.html";
                }
            }
        }
        
        if (!is_file($source)) {
            exit("Error: template source '{$filename}' is not exist!");
        }
        if (DEVELOPMENT || !is_file($compile) || filemtime($source) > filemtime($compile)) {
            shop_template_compile($source, $compile, true);
        }
        
        return $compile;
    }
    /**
     * 多商户
     */
    public function template_merch($filename) {
        global $_W;
        
        if (empty($filename)) {
            $filename = str_replace(".", "/", $_W['routes']);
        }
        
        $filename = str_replace("/add", "/post", $filename);
        $filename = str_replace("/edit", "/post", $filename);
        
        $name = 'elapp_shop';
        $moduleroot = IA_ROOT . "/addons/elapp_shop";
        
        $compile = IA_ROOT . "/data/tpl/web/{$_W['template']}/merch/{$name}/{$filename}.tpl.php";
        $explode = explode('/', $filename);
        
        $source = $moduleroot . "/plugin/merch/template/web/manage/" . implode('/', $explode) . ".html";
        if (!is_file($source)) {
            $source = $moduleroot . "/plugin/merch/template/web/manage/" . implode('/', $explode) . "/index.html";
        }
        
        //别的插件
        if (!is_file($source)) {
            $explode = explode('/', $filename);
            $temp = array_slice($explode, 1);            
            $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . ".html";
            if (!is_file($source)) {
                $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . "/index.html";
            }            
        }
        
        if (!is_file($source)) {
            exit("Error: template source '{$filename}' is not exist!");
        }
        
        if (DEVELOPMENT || !is_file($compile) || filemtime($source) > filemtime($compile)) {
            shop_template_compile($source, $compile, true);
        }
        return $compile;
    }
    /**
     * 机构合伙人
     */
    public function template_copartner($filename) {
        global $_W;
        
        if (empty($filename)) {
            $filename = str_replace(".", "/", $_W['routes']);
        }
        
        $filename = str_replace("/add", "/post", $filename);
        $filename = str_replace("/edit", "/post", $filename);
        
        $name = 'elapp_shop';
        $moduleroot = IA_ROOT . "/addons/elapp_shop";
        
        $compile = IA_ROOT . "/data/tpl/web/{$_W['template']}/copartner/{$name}/{$filename}.tpl.php";
        $explode = explode('/', $filename);
        
        $source = $moduleroot . "/plugin/copartner/template/web/manage/" . implode('/', $explode) . ".html";
        if (!is_file($source)) {
            $source = $moduleroot . "/plugin/copartner/template/web/manage/" . implode('/', $explode) . "/index.html";
        }        
        
        //别的插件
        if (!is_file($source)) {
            $explode = explode('/', $filename);
            $temp = array_slice($explode, 1);            
            $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . ".html";
            if (!is_file($source)) {
                $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . "/index.html";
            }
        }
        
        if (!is_file($source)) {
            exit("Error: template source '{$filename}' is not exist!");
        }
        
        if (DEVELOPMENT || !is_file($compile) || filemtime($source) > filemtime($compile)) {
            shop_template_compile($source, $compile, true);
        }
        return $compile;
    }
    /**
     * 供应商
     * @
     * <AUTHOR> ********
     */
    public function template_supply($filename) {
        global $_W;
        
        if (empty($filename)) {
            $filename = str_replace(".", "/", $_W['routes']);
        }
        
        $filename = str_replace("/add", "/post", $filename);
        $filename = str_replace("/edit", "/post", $filename);
        
        $name = 'elapp_shop';
        $moduleroot = IA_ROOT . "/addons/elapp_shop";
        
        $compile = IA_ROOT . "/data/tpl/web/{$_W['template']}/supply/{$name}/{$filename}.tpl.php";
        $explode = explode('/', $filename);
       
        $source = $moduleroot . "/plugin/supply/template/web/manage/" . implode('/', $explode) . ".html";
        if (!is_file($source)) {
            $source = $moduleroot . "/plugin/supply/template/web/manage/" . implode('/', $explode) . "/index.html";
        }
        
        //别的插件
        if (!is_file($source)) {
            $explode = explode('/', $filename);
            $temp = array_slice($explode, 1);            
            $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . ".html";
            if (!is_file($source)) {
                $source = $moduleroot . "/plugin/" . $explode[0] . "/template/web/" . implode('/', $temp) . "/index.html";
            }
        }
        
        if (!is_file($source)) {
            exit("Error: template source '{$filename}' is not exist!");
        }
        
        if (DEVELOPMENT || !is_file($compile) || filemtime($source) > filemtime($compile)) {
            shop_template_compile($source, $compile, true);
        }
        return $compile;
    }
    function message($msg, $redirect = '', $type = ''){
        global $_W;
        $title = "";
        $buttontext = "";
        $message = $msg;
        $buttondisplay = true;
        if (is_array($msg)) {
            $message = isset($msg['message']) ? $msg['message'] : '';
            $title = isset($msg['title']) ? $msg['title'] : '';
            $buttontext = isset($msg['buttontext']) ? $msg['buttontext'] : '';
            $buttondisplay = isset($msg['buttondisplay']) ? $msg['buttondisplay'] : true;
        }
        if (empty($redirect)) {
            $redirect = 'javascript:history.back(-1);';
        } elseif ($redirect == 'close') {
            $redirect = 'javascript:WeixinJSBridge.call("closeWindow")';
        } elseif ($redirect == 'exit') {
            $redirect = "";
        }
        define('IS_ELAPP_SHOP_SYSTEM', true);
        include $this->template('_message');
        exit;
    }
    
    function checkSubmit($key, $time = 2, $message = '操作频繁，请稍后再试!') {
        
        global $_W;
        $open_redis = function_exists('redis') && !is_error(redis());
        if ($open_redis) {
            $redis_key = "{$_W['setting']['site']['key']}_{$_W['account']['key']}_{$_W['uniacid']}_{$_W['openid']}_mobilesubmit_{$key}";
            $redis = redis();
            if ($redis->setnx($redis_key, time())) {
                $redis->expireAt($redis_key, time() + $time);
            } else {
                return error(-1, $message);
            }
        }
        return true;
        
    }
    function checkSubmitGlobal($key, $time = 2, $message = '操作频繁，请稍后再试!') {
        
        global $_W;
        $open_redis = function_exists('redis') && !is_error(redis());
        if ($open_redis) {
            $redis_key = "{$_W['setting']['site']['key']}_{$_W['account']['key']}_{$_W['uniacid']}_mobilesubmit_{$key}";
            $redis = redis();
            if ($redis->setnx($redis_key, time())) {
                $redis->expireAt($redis_key, time() + $time);
            } else {
                return error(-1, $message);
            }
        }
        return true;
        
    }

    /**
     * @notes 是否免登录验证
     * @return bool
     * <AUTHOR>
     * @date 2024/1/8 12:01
     */
    function isNotNeedLogin() : bool
    {
        $notNeedLogin = $this->notNeedLogin;
        if (empty($notNeedLogin)) {
            return false;
        }

        $action = $this->request->action();
        if (!in_array(trim($action), $notNeedLogin)) {
            return false;
        }

        return true;
    }

    /**
     * @notes 操作成功
     * @param string $msg
     * @param array $data
     * @param int $code
     * @param int $show
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/1/12 14:21
     */
    protected function succeed(string $msg = 'success', array $data = [], int $code = 0, int $show = 0)
    {
        return JsonService::success($msg, $data, $code, $show);
    }


    /**
     * @notes 数据返回
     * @param $data
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/1/12 14:21\
     */
    protected function data($data)
    {
        return JsonService::data($data);
    }


    /**
     * @notes 操作失败
     * @param string $msg
     * @param array $data
     * @param int $code
     * @param int $show
     * @return \think\response\Json
     * <AUTHOR>
     * @date 2024/1/12 14:21
     */
    protected function fail(string $msg = 'fail', array $data = [], int $code = 1, int $show = 1)
    {
        return JsonService::fail($msg, $data, $code, $show);
    }
}