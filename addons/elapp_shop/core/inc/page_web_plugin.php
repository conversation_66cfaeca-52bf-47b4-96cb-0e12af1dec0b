<?php
namespace web\controller;

class PluginWebPage extends WebPage {

    public $pluginname;
    /**
     * @var $model AppModel
     */
    public $model;
    public $plugintitle;
    public $set;

    public $beforeRequest = array();

    public $afterRequest;

    public function __construct($_init = true) {

        parent::__construct($_init);
        global $_W,$_GPC;

        if (com('perm') && !com('perm')->check_plugin($_W['plugin'])) {
            $this->message("你没有相应的权限查看1012");
        }
        if (empty($_W['plugin'])) {
            $this->message($_W['plugin'] . "插件不存在");
        }
        $this->pluginname = $_W['plugin'];
        $this->modulename = 'elapp_shop';
        $this->plugintitle = m('plugin')->getPluginName($this->pluginname);
        //判断是否是第三方的应用
        if(strpos($this->pluginname,'open_messikefu')!==false){
            $redis = redis();
            if(!function_exists('redis') || is_error($redis)){
                $this->message('请联系管理员开启 redis 支持，才能使用第三方插件','','error');
                exit;
            }
            
        }

        // PC插件必须开启全网通和用户手机号绑定
        if ($this->pluginname == 'pc') {
            $wapSetting = m('common')->getSysset('wap');
            if (!$wapSetting['open']) {
                $this->message('使用PC必须开启全网通WAP访问');
            }
            if (!$wapSetting['mustbind']) {
                $this->message('使用PC必须开启强制绑定手机号');
            }
        }


        $this->model = m('plugin')->loadModel($this->pluginname);
        $this->set = $this->model->getSet();
        
    }

    public function getSet() {
        return $this->set;
    }

    public function updateSet($data = array()) {
        $this->model->updateSet($data);
    }
}
