<?php
namespace app\controller;

use framework\classes\account\WeModuleProcessor;

class Processor extends WeModuleProcessor {
	public function respond(){
		$rule = pdo_fetch('select * from ' . tablename('rule') . ' where id=:id limit 1', array(':id' => $this->rule));

		if (empty($rule)) {
			return false;
		}

		$names = explode(':', $rule['name']);
		$plugin = (isset($names[1]) ? $names[1] : '');
		$processname = $plugin;

		if (!empty($plugin)) {
			if ($plugin == 'com') {
				$com = (isset($names[2]) ? $names[2] : '');

				if (empty($com)) {
					return false;
				}

				$processname = $com;
			}

            $processor_class =  '\\'.ucfirst($processname) . 'Processor';
            if (class_exists($processor_class)) {
                $proc = new $processor_class($plugin);
                if (method_exists($proc, 'respond')) {
                    return $proc->respond($this);
                }
            }
		}
	}
}
