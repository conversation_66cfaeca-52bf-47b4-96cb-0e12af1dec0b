<?php

error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';
ignore_user_abort();
set_time_limit(0);
$sets = pdo_fetchall('select uniacid from ' . tablename('elapp_shop_sysset'));

if (!empty($sets)) {
	foreach ($sets as $set) {
		$sql = 'SELECT id,accounttime FROM ' . tablename('elapp_shop_merch_user') . ' WHERE accounttime <= ' . time() . ' AND uniacid = :uniacid';
		$params = array(':uniacid' => $set['uniacid']);
		$merchUsers = pdo_fetchall($sql, $params);

		if (!empty($merchUsers)) {
			foreach ($merchUsers as $merchUser) {
				pdo_update('elapp_shop_goods', array('status' => 0), array('merchid' => $merchUser['id'], 'uniacid' => $set['uniacid']));
			}
		}
	}
}

?>
