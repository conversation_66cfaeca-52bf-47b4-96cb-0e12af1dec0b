<?php
error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';
global $_W;
global $_GPC;
ignore_user_abort();
set_time_limit(0);
$sets = pdo_fetchall('select uniacid from ' . tablename('elapp_shop_sysset'));

foreach ($sets as $set) {
	$_W['uniacid'] = $set['uniacid'];

	if (empty($_W['uniacid'])) {
		continue;
	}

	$trade = m('common')->getSysset('trade', $_W['uniacid']);
	$goods = pdo_fetchall('select id,statustimestart,statustimeend from ' . tablename('elapp_shop_goods') . ' where uniacid = ' . $_W['uniacid'] . ' and isstatustime > 0 and deleted = 0 ');

	foreach ($goods as $key => $value) {
		// 自动上架时间 自动下架时间
		if (($value['statustimestart'] < time()) && (time() < $value['statustimeend'])) {
			$value['status'] = 1;
		} else {
			$value['status'] = 0;
		}

		pdo_update('elapp_shop_goods', array('status' => $value['status']), array('id' => $value['id']));
	}
}

?>
