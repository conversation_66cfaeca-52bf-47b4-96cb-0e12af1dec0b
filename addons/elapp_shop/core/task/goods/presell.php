<?php
error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';
global $_W;
global $_GPC;
ignore_user_abort();
set_time_limit(0);
$sets = pdo_fetchall('select uniacid from ' . tablename('elapp_shop_sysset'));
foreach ($sets as $set ) 
{
	$_W['uniacid'] = $set['uniacid'];
	if (empty($_W['uniacid'])) 
	{
		continue;
	}
	$trade = m('common')->getSysset('trade', $_W['uniacid']);
	$goods = pdo_fetchall('select id,preselltimeend,presellover,ispresell from ' . tablename('elapp_shop_goods') . ' where uniacid = ' . $_W['uniacid'] . ' and ispresell > 0 and deleted = 0 ');
	foreach ($goods as $key => $value ) 
	{
		if (($value['ispresell'] == 1) && ($value['presellover'] == 0) && ($value['preselltimeend'] < time())) 
		{
			$value['status'] = 0;
			pdo_update('elapp_shop_goods', array('status' => $value['status']), array('id' => $value['id']));
		}
	}
}
?>