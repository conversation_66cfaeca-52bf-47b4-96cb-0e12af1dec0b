<?php
error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';
global $_W;
global $_GPC;
ignore_user_abort();
set_time_limit(0);
$cycel = m('common')->getSysset('cycelbuy', $_GPC['uniacid']);
$p = p('commission');
$plugin_vrshop = p('vrshop');
$plugin_clerk = p('clerk');
$plugin_doctor = p('doctor');
$plugin_copartner = p('copartner');
$pcoupon = com('coupon');
$_W['uniacid'] = $_GPC['uniacid'];
$trade = m('common')->getSysset('trade', $_W['uniacid']);
$days = intval($trade['receive']);
$receive_goods = ((empty($cycel['receive_goods']) ? $days : $cycel['receive_goods']));
if (empty($receive_goods)) 
{
	return false;
}
$order = pdo_fetchall('select id,openid,deductcredit2,price,address,ordersn,isparent,deductcredit,deductprice,status,isparent,isverify,`virtual`,`virtual_info`,createtime,cycelbuy_periodic,onmid from ' . tablename('elapp_shop_order') . ' where uniacid=' . $_W['uniacid'] . '  and paytype<>3   and status=2 and iscycelbuy = 1');
if (!(empty($order))) 
{
	foreach ($order as $k => $v ) 
	{
		$last_periods = pdo_fetch('select * from ' . tablename('elapp_shop_cycelbuy_periods') . ' where uniacid=:uniacid and orderid=:orderid order by id desc  limit 1', array(':uniacid' => $_W['uniacid'], ':orderid' => $v['id']));
		$cycel = pdo_fetchall('select * from ' . tablename('elapp_shop_cycelbuy_periods') . ' where  orderid = ' . $v['id'] . ' and status = 1 and uniacid = ' . $_W['uniacid'] . ' order by receipttime asc limit 1');
		$days = 86400 * $receive_goods;
		$sendtime = $cycel[0]['sendtime'];
		if (time() <= $sendtime + $days) {
			if (!(empty($last_periods))) {
				if ($last_periods['id'] == $cycel[0]['id']) {
					pdo_update('elapp_shop_cycelbuy_periods', array('status' => 2, 'finishtime' => time()), array('orderid' => $v['id'], 'uniacid' => $_W['uniacid']));
					pdo_update('elapp_shop_order', array('status' => 3, 'finishtime' => time()), array('id' => $v['id'], 'status' => 2));
					m('member')->upgradeLevel($v['openid'], $v['id']);
					m('order')->setGiveBalance($v['id'], 1);
					m('notice')->sendOrderMessage($v['id']);
					m('order')->fullback($v['id']);
					m('order')->setStocksAndCredits($v['id'], 3);
					if ($pcoupon) 
					{
						com('coupon')->sendcouponsbytask($v['id']);
						if (!(empty($order['couponid']))) 
						{
							$pcoupon->backConsumeCoupon($v['id']);
						}
					}
					//分销检测
					if ($p) {
						$p->checkOrderFinish($v['id']);						
					}
					//虚店店长检测
					if($plugin_vrshop){
						$plugin_vrshop->checkOrderFinish($v['id']);
					}
					//虚店店员检测
					if($plugin_clerk){
						$plugin_clerk->checkOrderFinish($v['id']);
					}
					//虚店合伙人检测
					if($plugin_copartner){
						$plugin_copartner->checkOrderFinish($v['id']);
					}
					//扶植分红检测
					if(p('mentor')){
						p('mentor')->checkOrderFinish($v['id']);
					}
					//医生检测
					if($plugin_doctor){
						$plugin_doctor->checkOrderFinish($v['id']);
					}							
					//会员分享检测
					if (p('userpromote')) {			
						//会员分享积分检测
						p('userpromote')->setCredits($v['onmid'], $v['id']);
						//会员消费积分检测
						p('userpromote')->shopSetcredits($v['id']);
					}		
					
				}else {
					pdo_update('elapp_shop_cycelbuy_periods', array('status' => 2, 'finishtime' => time()), array('id' => $cycel[0]['id'], 'uniacid' => $_W['uniacid']));
				}
			}
		}
	}
}
?>