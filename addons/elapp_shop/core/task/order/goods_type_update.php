<?php
error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';

global $_W, $_GPC;

ignore_user_abort(); 
set_time_limit(0); 

$sql = "UPDATE
  ims_elapp_shop_order AS a
left join (SELECT o.id,
      CASE
        WHEN g.goodsid = 4894 THEN '2'
        WHEN o.activity_id IN (1, 2, 4) THEN '3'
        ELSE '1'
      END AS order_type
    FROM
      ims_elapp_shop_order o
      LEFT JOIN ims_elapp_shop_order_goods g ON o.id = g.orderid 
    where o.goods_type = 0 or o.goods_type = '' limit 0,100
) as b on a.id = b.id  
SET
  a.goods_type = b.order_type
WHERE
  a.id = b.id and a.goods_type = 0;";

pdo_query($sql); 