<?php
error_reporting(0);
require "../../../../../extend/framework/bootstrap.inc.php";
require dirname(__DIR__) . "/../../defines.php";
require "../../../../../extend/framework/common/function.php";
require "../../../../../addons/elapp_shop/core/inc/plugin_model.php";
ignore_user_abort();
set_time_limit(0);
global $_W;
global $_GPC;
$sets = pdo_fetchall("select uniacid from " . tablename("elapp_shop_sysset"));
foreach ($sets as $set) {
    $_W["uniacid"] = $set["uniacid"];
    if (empty($_W["uniacid"])) {
        continue;
    }
    $sql = "select * from " . tablename("elapp_shop_order") . " where uniacid=" . $_W["uniacid"] . " and isparent=0 and ((refundstate=0 and refundid=0) or refundtime<>0)\r\n            and is_sub_account = 1 and paytype=21 and sub_account_status=0 and sub_account_time<'" . date("Y-m-d H:i:s") . "'";
    $orders = pdo_fetchall($sql);
    if (empty($orders)) {
        continue;
    }
    $merch_data = m("common")->getPluginset("merch");
    $paySet = m("common")->getSysset("pay");
    if (!empty($paySet["weixin_id"])) {
        $payments = pdo_fetch("SELECT * FROM " . tablename("elapp_shop_payment") . " WHERE uniacid=:uniacid AND id=:id", array(":uniacid" => $_W["uniacid"], ":id" => $paySet["weixin_id"]));
    }
    $sec = m("common")->getSec();
    $sec = iunserializer($sec["sec"]);
    $merchInfo = array();
    foreach ($orders as $order) {
        $receivers = array();
        if (empty($merchInfo[$order["merchid"]])) {
            $merchInfo[$order["merchid"]] = pdo_fetch("select * from " . tablename("elapp_shop_merch_user") . " where id=" . $order["merchid"]);
        }
        $orderPriceRes = getMerchPrice($order["merchid"], $order["id"], $order["agentid"], $merch_data, $merchInfo[$order["merchid"]]);
        if ($order["is_wxapp"]) {
            $data = array("appid" => trim($sec["wxapp"]["service_appid"]), "mch_id" => trim($sec["wxapp"]["service_mch_id"]));
        } else {
            $data = array("appid" => trim($payments["appid"]), "mch_id" => trim($payments["mch_id"]));
        }
        $certfile = IA_ROOT . "/addons/elapp_shop/cert/" . random(64);
        file_put_contents($certfile, $payments["cert_file"]);
        $keyfile = IA_ROOT . "/addons/elapp_shop/cert/" . random(64);
        file_put_contents($keyfile, $payments["key_file"]);
        $extras["CURLOPT_SSLCERT"] = $certfile;
        $extras["CURLOPT_SSLKEY"] = $keyfile;
        if ($orderPriceRes["rake"] == 0) {
            if ($order["is_wxapp"]) {
                $cancelData = array("appid" => trim($sec["wxapp"]["service_appid"]), "mch_id" => trim($sec["wxapp"]["service_mch_id"]));
            } else {
                $cancelData = array("appid" => trim($payments["appid"]), "mch_id" => trim($payments["mch_id"]));
            }
            $cancelData = array_merge($cancelData, array("sub_mch_id" => $merchInfo[$order["merchid"]]["sub_mch_id"], "nonce_str" => random(32), "transaction_id" => $order["transid"], "out_order_no" => $order["ordersn"], "description" => "没有抽成自动解冻"));
            $cancelData["sign"] = getSign($cancelData, $payments["apikey"]);
            $xmlData = array2xml($cancelData);
            $cancelRes = ihttp_request("https://api.mch.weixin.qq.com/secapi/pay/profitsharingfinish", $xmlData, $extras);
            if (empty($merchInfo["first_sub_account_time"])) {
                pdo_update("elapp_shop_merch_user", array("first_sub_account_time" => date("Y-m-d H:i:s")), array("id" => $order["merchid"]));
            }
            $logData = array("uniacid" => $_W["uniacid"], "order_id" => $order["id"], "merchid" => $order["merchid"], "shop_price" => $orderPriceRes["rake"], "merch_price" => $orderPriceRes["realpricerate"], "role" => json_encode($orderPriceRes), "status" => 1, "create_time" => date("Y-m-d H:i:s"));
            pdo_insert("elapp_shop_sub_account_order_log", $logData);
            pdo_update("elapp_shop_order", array("sub_account_status" => 1), array("id" => $order["id"]));
            continue;
        }
        $data["nonce_str"] = random(32);
        $data["sub_mch_id"] = $merchInfo[$order["merchid"]]["sub_mch_id"];
        $data["sub_appid"] = $merchInfo[$order["merchid"]]["sub_appid_sub"];
        $data["transaction_id"] = $order["transid"];
        $data["out_order_no"] = $order["ordersn"];
        $receivers[] = array("type" => "MERCHANT_ID", "account" => $payments["mch_id"], "amount" => $orderPriceRes["rake"] * 100, "description" => "平台抽成");
        $data["receivers"] = json_encode($receivers);
        $data["sign"] = getSign($data, $payments["apikey"]);
        $xmlData = array2xml($data);
        $response = ihttp_request("https://api.mch.weixin.qq.com/secapi/pay/profitsharing", $xmlData, $extras);
        libxml_disable_entity_loader(true);
        $responseArr = json_decode(json_encode(simplexml_load_string($response["content"], "SimpleXMLElement", LIBXML_NOCDATA)), true);
        if ($responseArr["return_code"] == "SUCCESS") {
            $logData = array("uniacid" => $_W["uniacid"], "order_id" => $order["id"], "merchid" => $order["merchid"], "shop_price" => $orderPriceRes["rake"], "merch_price" => $orderPriceRes["realpricerate"], "role" => json_encode($orderPriceRes), "request_data" => json_encode($data), "create_time" => date("Y-m-d H:i:s"));
            if ($responseArr["result_code"] == "SUCCESS") {
                $orderData["sub_account_status"] = 1;
                $logData["status"] = 1;
                if (empty($merchInfo["first_sub_account_time"])) {
                    pdo_update("elapp_shop_merch_user", array("first_sub_account_time" => date("Y-m-d H:i:s")), array("id" => $order["merchid"]));
                }
            } else {
                $orderData["sub_account_status"] = -1;
                $logData["status"] = 0;
                $logData["error_message"] = $responseArr["err_code_des"];
                if ($order["is_wxapp"]) {
                    $cancelData = array("appid" => trim($sec["wxapp"]["service_appid"]), "mch_id" => trim($sec["wxapp"]["service_mch_id"]));
                } else {
                    $cancelData = array("appid" => trim($payments["appid"]), "mch_id" => trim($payments["mch_id"]));
                }
                $cancelData = array_merge($cancelData, array("sub_mch_id" => $merchInfo[$order["merchid"]]["sub_mch_id"], "nonce_str" => random(32), "transaction_id" => $order["transid"], "out_order_no" => $order["ordersn"], "description" => "没有抽成自动解冻"));
                $cancelData["sign"] = getSign($cancelData, $payments["apikey"]);
                $xmlData = array2xml($cancelData);
                $cancelRes = ihttp_request("https://api.mch.weixin.qq.com/secapi/pay/profitsharingfinish", $xmlData, $extras);
            }
            pdo_insert("elapp_shop_sub_account_order_log", $logData);
            pdo_update("elapp_shop_order", $orderData, array("id" => $order["id"]));
        }
        @unlink($certfile);
        @unlink($keyfile);
    }
}
function getMerchPrice($merchid, $orderid, $agentid, $merch_data, $merchInfo)
{
    global $_W;
    if (!empty($merch_data["deduct_commission"])) {
        $deduct_commission = 1;
    } else {
        $deduct_commission = 0;
    }
    $condition = "  and o.isparent=0 and o.merchapply<=0 and o.paytype<>0 and o.paytype<>3 and o.id=" . $orderid;
    $conditionrefund = " and o.status=-1 and o.isparent=0 and o.merchapply<=0 and o.paytype<>3 and o.id=" . $orderid;
    $paramsrefund = array();
    $con = "sum(o.price) price,sum(o.goodsprice) goodsprice,sum(o.dispatchprice) dispatchprice,\r\n            sum(o.discountprice) discountprice,sum(o.deductprice) deductprice,sum(o.deductcredit2) deductcredit2,sum(o.isdiscountprice) isdiscountprice,\r\n            sum(o.deductenough) deductenough,sum(o.merchdeductenough) merchdeductenough,sum(o.merchisdiscountprice) merchisdiscountprice,\r\n            sum(o.changeprice) changeprice,sum(o.seckilldiscountprice) seckilldiscountprice";
    $sql = "select " . $con . " from " . tablename("elapp_shop_order") . " o " . " where 1 " . $condition . " limit 1";
    $list = pdo_fetch($sql);
    $merchcouponprice = pdo_fetchcolumn("select sum(o.couponprice) from " . tablename("elapp_shop_order") . " o " . " where o.couponmerchid>0 " . $condition . " limit 1");
    $refundlist = pdo_fetchall("select o.id,r.orderprice,r.applyprice, r.pay_refund_price from " . tablename("elapp_shop_order") . " o " . " left join " . tablename("elapp_shop_order_refund") . " r on o.refundid=r.id" . " where o.refundid>0 and r.status=1  and r.applyprice<r.orderprice  " . $conditionrefund . " ", $paramsrefund, "id");
    $refundprice = 0;
    foreach ($refundlist as $key => $value) {
        $refundprice += $value["pay_refund_price"];
    }
    $sql = "SELECT o.id,refund.ordergoodsrealprice,refund.applyprice FROM\r\n            " . tablename("elapp_shop_order") . " AS o\r\n            LEFT JOIN " . tablename("elapp_shop_order_single_refund") . " AS refund ON o.id = refund.orderid\r\n            INNER JOIN " . tablename("elapp_shop_order_goods") . " AS og ON refund.ordergoodsid = og.id\r\n            AND o.id = og.orderid where o.uniacid = :uniacid and o.merchid = :merchid and refund.status=1 and o.id=" . $orderid . " ";
    $result = pdo_fetchall($sql, array(":uniacid" => $_W["uniacid"], ":merchid" => $merchid));
    foreach ($result as $key => $value) {
        $refundprice += $value["applyprice"];
    }
    $list["commission"] = m("order")->getOrderCommission($orderid, $agentid);
    $list["deduct_commission"] = $deduct_commission;
    $list["orderprice"] = $list["goodsprice"] + $list["dispatchprice"] + $list["changeprice"];
    $list["realprice"] = $list["price"] - $refundprice;
    $list["realpricerate"] = (100 - floatval($merchInfo["payrate"])) * $list["realprice"] / 100;
    $list["rake"] = $list["realprice"] - $list["realpricerate"];
    if ($deduct_commission) {
        $list["realpricerate"] -= $list["commission"];
        $list["realprice"] -= $list["commission"];
        $list["rake"] += $list["commission"];
    }
    $list["rake"] = round($list["rake"], 2);
    $list["merchcouponprice"] = $merchcouponprice;
    return $list;
}
function getSign($params, $apikey)
{
    ksort($params, SORT_STRING);
    $string1 = "";
    foreach ($params as $key => $v) {
        if (empty($v)) {
            continue;
        }
        $string1 .= (string) $key . "=" . $v . "&";
    }
    $string1 .= "key=" . $apikey;
    return strtoupper(hash_hmac("sha256", $string1, $apikey));
}

?>