<?php

error_reporting(0);
require '../../../../../extend/framework/bootstrap.inc.php';
require '../../../../../addons/elapp_shop/defines.php';
require '../../../../../extend/framework/common/function.php';
require '../../../../../addons/elapp_shop/core/inc/plugin_model.php';
global $_W, $_GPC;

use app\controller\activity\Action;
use app\controller\activity\OrderSignOffData;
use app\model\ActivityModel;

ignore_user_abort(); 
set_time_limit(0); 

$sets = pdo_fetchall('select uniacid from ' . tablename('elapp_shop_sysset'));
foreach ($sets as $set) {

    $_W['uniacid'] = $set['uniacid'];
    if (empty($_W['uniacid'])) {
        continue;
    }
    $cityexpress_receive=0;
    $cityexpress = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_city_express') . " WHERE uniacid=:uniacid AND merchid=:merchid",array(":uniacid"=>$_W['uniacid'],":merchid"=>0));
    if(!empty($cityexpress['enabled']) && !empty($cityexpress['receive_goods'])){
        $cityexpress_receive=intval($cityexpress['receive_goods'])>0?intval($cityexpress['receive_goods']):0;
    }

    $trade = m('common')->getSysset('trade', $_W['uniacid']);
    $days = intval($trade['receive']);

    $p = p('commission');
    $plugin_clerk = p('clerk');
    $plugin_vrshop = p('vrshop');    
    $plugin_copartner = p('copartner');
    $plugin_mentor = p('mentor');
    $plugin_doctor = p('doctor');
    $pcoupon = com('coupon');

    $orders = pdo_fetchall("select id,couponid,onmid,status,openid,isparent,sendtime,price,merchid,isverify,addressid,isvirtualsend,`virtual`,dispatchtype,city_express_state,refundstate from " . tablename('elapp_shop_order') . " where uniacid={$_W['uniacid']} and (status=2 or  (status = 1 and `isverify` = 1 and `verifyendtime` <= unix_timestamp() and `verifyendtime` > 0))", array(), 'id');

    if (!empty($orders)) {
        foreach ($orders as $orderid => $order) {

            if(!empty($order['city_express_state']) && !empty($cityexpress_receive)){
                $days=$cityexpress_receive;
            }
            if ($order['status'] == 2) {
                $result = goodsReceive($order, $days);
                if (!$result) {
                    continue;
                }
            }
            
            $time = time();
            pdo_query("update " . tablename('elapp_shop_order') . " set status=3,finishtime=:time where id=:orderid",array(':time'=>$time,':orderid'=>$orderid) );
            if ($order['isparent'] == 1) {
                continue;
            }

            // 如果存在退款申请则不进行确认收货
            if ($order['refundstate'] == 1 || $order['refundstate']==3) {
                continue;
            }

//            //单品退换货，确认收货后取消维权
//            pdo_update('elapp_shop_order_goods', array('single_refundstate' => 0), array('orderid' => $orderid));
//            // 如果存在单品退换货, 取消维权
//            $single_refund_goods = pdo_getall('elapp_shop_order_goods', array('orderid' => $orderid, 'single_refundid >' => 0));
//            if (!empty($single_refund_goods)) {
//                foreach ($single_refund_goods as $single_refund_goods_item) {
//                    pdo_update('elapp_shop_order_goods', array('single_refundid' => 0), array('id' => $single_refund_goods_item['id']));
//                    pdo_update('elapp_shop_order_single_refund', array('status' => -2, 'refundtime' => time()), array('id' => $single_refund_goods_item['single_refundid'], 'status' => 0));
//                }
//            }

            m('member')->upgradeLevel($order['openid'], $orderid);
            m('order')->setGiveBalance($orderid, 1);
            m('notice')->sendOrderMessage($orderid);
            m('order')->fullback($orderid);
            m('order')->setStocksAndCredits($orderid, 3);
            if ($pcoupon) {
                if (!empty($order['couponid'])) {
                    $pcoupon->backConsumeCoupon($order['id']); 
                }
                $pcoupon->sendcouponsbytask($order['id']); 
            }

            // 活动签收
            $activityPlugin = new ActivityModel();
            if ($activityPlugin) {
                $activityPlugin->hook(Action::ORDER_SIGNOFF, new OrderSignOffData($orderid));
            }

            if ($p) {
                $p->checkOrderFinish($orderid);
            }
            //虚店店长检测
            if($plugin_vrshop){
                $plugin_vrshop->checkOrderFinish($orderid);
            }
            //虚店店员检测
            if($plugin_clerk){
                $plugin_clerk->checkOrderFinish($orderid);
            }
            //虚店合伙人检测
            if($plugin_copartner){
                $plugin_copartner->checkOrderFinish($orderid);
            }
            //虚店扶植分红检测
            if($plugin_mentor){
                $plugin_mentor->checkOrderFinish($orderid);
            }
            //医生检测
            if($plugin_doctor){
                $plugin_doctor->checkOrderFinish($orderid);
            }		
            //会员分享检测
            if (p('userpromote')) {			
                //会员分享积分检测
                p('userpromote')->setCredits($order['onmid'], $orderid);
                //会员消费积分检测
                p('userpromote')->shopSetcredits($orderid);
            }
            
            if(p('lottery') && $order['merchid'] == 0){
                $res = p('lottery')->getLottery($order['openid'],1,array('money'=>$order['price'],'paytype'=>2));
                if($res){
                    p('lottery')->getLotteryList($order['openid'],array('lottery_id'=>$res));
                }
            }
        }
    }
}

function goodsReceive($order, $sysday=0){
    $days = array();
    if (checkFetchOrder($order)){
        return false;
    }

    $isonlyverifygoods = m('order')->checkisonlyverifygoods($order['id']);
    if($isonlyverifygoods)
    {
        return false;
    }

    if ($order['merchid'] == 0) {
        $goods = pdo_fetchall("select og.goodsid, g.autoreceive from".tablename("elapp_shop_order_goods") ." og left join ".tablename("elapp_shop_goods")." g on g.id=og.goodsid where og.orderid=".$order['id']);

        foreach ($goods as $i=>$g){
            $days[] = $g['autoreceive'];
        }

        $day = max($days);
    } else {
        $day = 0;
    }

    if($day<0){
        return false;
    }
    elseif($day==0){
        if($sysday<=0){
            return false;
        }
        $day = $sysday;
    }

    $daytimes = 86400 * $day;

    if($order['sendtime']+$daytimes<=time()){
        return true;
    }

    return false;
}

function checkFetchOrder($order)
{
    if ($order['isverify'] != 1 && empty($order['addressid']) && empty($order['isvirtualsend']) && empty($order['virtual']) && $order['dispatchtype']){
        return true;
    }else{
        return false;
    }
}


