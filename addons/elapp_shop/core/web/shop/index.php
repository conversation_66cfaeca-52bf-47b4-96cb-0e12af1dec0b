<?php
namespace web\controller\shop;
use web\controller\WebPage;
use \app\model\FilterModel;

class IndexController extends WebPage {
    private $filter;
    public function __construct($_init = true)
    {
        parent::__construct($_init);
        $this->filter = new FilterModel();
    }

    public function main() {
        global $_W;
        if(!empty($_W['shopversion'])) {
            header('Content-Type: text/plain');
            if (cv('shop.adv')) {
                header('location: ' . webUrl('shop/adv/main'));
            } elseif (cv('shop.nav')) {
                header('location: ' . webUrl('shop/nav/main'));
            } elseif (cv('shop.banner')) {
                header('location: ' . webUrl('shop/banner/main'));
            } elseif (cv('shop.cube')) {
                header('location: ' . webUrl('shop/cube/main'));
            } elseif (cv('shop.recommand')) {
                header('location: ' . webUrl('shop/recommand/main'));
            } elseif (cv('shop.sort')) {
                header('location: ' . webUrl('shop/sort/main'));
            } elseif (cv('shop.verify.store')) {
                header('location: ' . webUrl('shop/verify/store/main'));
            } elseif (cv('shop.verify.saler')) {
                header('location: ' . webUrl('shop/verify/saler/main'));
            } elseif (cv('shop.verify.set')) {
                header('location: ' . webUrl('shop/verify/set/main'));
            }elseif (cv('shop.dispatch.main')) {
                header('location: ' . webUrl('shop/dispatch/main'));
            }else{
                header('location: ' . webUrl());
            }
            exit();
        }else{
            $shop_data = m('common')->getSysset('shop');
            //多商户
            $merch_plugin = p('merch');
            $merch_data = m('common')->getPluginset('merch');
            if ($merch_plugin && $merch_data['is_openmerch']) {
                $is_openmerch = 1;
            } else {
                $is_openmerch = 0;
            }
            //机构合伙人
            $copartner_plugin = p('copartner');
            $copartner_data = m('common')->getPluginset('copartner');
            if ($copartner_plugin && $copartner_data['is_opencopartner']) {
                $is_opencopartner = 1;
            } else {
                $is_opencopartner = 0;
            }
            //供应商 Hlei 20220314
            $supply_plugin = p('supply');
            $supply_data = m('common')->getPluginset('supply');
            if ($supply_plugin && $supply_data['is_opensupply']) {
                $is_opensupply = 1;
            } else {
                $is_opensupply = 0;
            }

            //待发货详细信息
            $order_sql ="select id,ordersn,createtime,address,price,invoicename from " . tablename('elapp_shop_order') . " where uniacid = :uniacid and merchid=0 and isparent=0 and deleted=0 AND ( status = 1 or (status=0 and paytype=3) ) ORDER BY createtime ASC LIMIT 20";

            $order = pdo_fetchall($order_sql,array(':uniacid' => $_W['uniacid']));

            foreach ($order as &$value)
            {
                $value['address'] = iunserializer($value['address']);
            }
            unset($value);
            $order_ok = $order;

            //公告信息
            $notice = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_system_copyright_notice') . " ORDER BY displayorder ASC,createtime DESC LIMIT 10" );

            $hascommission =false;
            if(p('commission')){
                $hascommission =  intval($_W['shopset']['commission']['level'])>0;
            }

            include $this->template();
        }
    }


    public function view() {
        global $_GPC;
        $id = intval($_GPC['id']);
        $item = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_system_copyright_notice') . " WHERE id = $id ORDER BY displayorder ASC,createtime DESC" );
        $item['content'] = htmlspecialchars_decode($item['content']);
        include $this->template('shop/view');
    }

    public function notice() {
        global $_W, $_GPC;

        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $condition = " and status=1 ";
        $params = array();
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $condition.=' and title like :keyword';
            $params[':keyword'] = "%{$_GPC['keyword']}%";
        }

        $list = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_system_copyright_notice') . " WHERE 1 {$condition}  ORDER BY displayorder DESC, createtime DESC limit " . ($pindex - 1) * $psize . ',' . $psize, $params);
        $total = pdo_fetchcolumn("SELECT count(*) FROM " . tablename('elapp_shop_system_copyright_notice') . " WHERE 1 {$condition}", $params);
        $pager = pagination2($total, $pindex, $psize);

        include $this->template('shop/notice');
    }

    public function ajax() {
        global $_W;
        $paras = array(':uniacid' => $_W['uniacid']);

        $cond = $this->filter->getConditionsSql( FilterModel::TABLE_SHOP_GOODS);

        //已售罄商品
        $goods_totals = pdo_fetchcolumn(
            'SELECT COUNT(1) FROM ' . tablename('elapp_shop_goods') ." WHERE uniacid = :uniacid and status=1 and deleted=0 and stock<=0 and stock<>-1 $cond ",
            $paras
        );
        $member_cond = $this->filter->getConditionsSql( FilterModel::TABLE_MEMBER, 'm');
        //待审核提现
        $finance_total = pdo_fetchcolumn("select count(1) from " . tablename('elapp_shop_member_log') . " log "
            . " left join " . tablename('elapp_shop_member') . " m on m.openid=log.openid and m.uniacid= log.uniacid"
            . " left join " . tablename('elapp_shop_member_group') . " g on m.groupid=g.id"
            . " left join " . tablename('elapp_shop_member_level') . " l on m.level =l.id"
            . " where log.uniacid=:uniacid and log.type=:type and log.money<>0 and log.status=:status {$member_cond}", array(':uniacid' => $_W['uniacid'], ':type' => 1,':status'=>0));

        //分销商总数
        $commission_agent_total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " as m "
            . " where m.uniacid =:uniacid and m.isagent =1 and m.status = 1 $member_cond", array(':uniacid' => $_W['uniacid']));

        //待审核分销商
        $commission_agent_status0_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_member') . " as  m "
            . " where m.uniacid =:uniacid and m.isagent =1 and m.status=:status $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 0));

        //待审核佣金提现申请
        $commission_apply_status1_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_commission_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_commission_level') . " l on l.id = m.agentlevel"
            . " where a.uniacid=:uniacid and a.status=:status $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 1));

        //待打款佣金提现申请
        $commission_apply_status2_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_commission_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_commission_level') . " l on l.id = m.agentlevel"
            . " where a.uniacid=:uniacid and a.status=:status $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 2));
        
        // 云店长
        // 店员总数
        $clerk_total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " as m "
            . " where m.uniacid =:uniacid and m.is_clerk =1 and m.clerk_status = 1 $member_cond", array(':uniacid' => $_W['uniacid']));

        // 待审核店员
        $clerk_status0_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_member') . " as  m "
            . " where m.uniacid =:uniacid and m.is_clerk =1 and m.clerk_status=:clerk_status $member_cond", array(':uniacid' => $_W['uniacid'], ':clerk_status' => 0));

        // 待审核店员佣金提现申请
        $clerk_apply_status1_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
            . " where a.uniacid=:uniacid and a.status=:status and a.money_type=:money_type $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 0, ':money_type' => 0));

        // 待打款店员佣金提现申请
        $clerk_apply_status2_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
            . " where a.uniacid=:uniacid and a.status=:status and a.money_type=:money_type $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 2, ':money_type' => 0));

        // 合伙人
        //合伙人总数
        $copartner_total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_copartner_user') . " as m "
            . " where m.uniacid =:uniacid and m.status =1 and m.del_at = 0 $member_cond", array(':uniacid' => $_W['uniacid']));

        //待审核合伙人
        $copartner_status0_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_copartner_reg') . " as  m "
            . " where m.uniacid =:uniacid and m.status =:status and m.del_at=0 $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 0));

        //待审核合伙人佣金提现申请
        $copartner_apply_status1_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
            . " where a.uniacid=:uniacid and a.status=:status and a.money_type=:money_type $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 0, ':money_type' => 1));

        //待打款合伙人佣金提现申请
        $copartner_apply_status2_total = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
            . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
            . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
            . " where a.uniacid=:uniacid and a.status=:status and a.money_type=:money_type $member_cond", array(':uniacid' => $_W['uniacid'], ':status' => 2, ':money_type' => 0));

        show_json(1,array(
            'goods_totals' => $goods_totals,
            'finance_total' => $finance_total,
            'commission_agent_total' => $commission_agent_total,
            'commission_agent_status0_total' => $commission_agent_status0_total,
            'commission_apply_status1_total' => $commission_apply_status1_total,
            'commission_apply_status2_total' => $commission_apply_status2_total,
            'clerk_total' => $clerk_total,
            'clerk_status0_total' => $clerk_status0_total,
            'clerk_apply_status1_total' => $clerk_apply_status1_total,
            'clerk_apply_status2_total' => $clerk_apply_status2_total,
            // 合伙人
            'copartner_total' => $copartner_total,
            'copartner_status0_total' => $copartner_status0_total,
            'copartner_apply_status1_total' => $copartner_apply_status1_total,
            'copartner_apply_status2_total' => $copartner_apply_status2_total
        ));
    }

    public function ajaxgoods() {
        global $_W,$_GPC;
        $day = (int)$_GPC['day'];
        $goods_rank=$this->selectGoodsRank($day);

        show_json(1, array(
            'obj'=>array(
                'goods_rank_'.$day=>$goods_rank
            )
        ));
    }

    protected function selectGoodsRank($day=0) {
        global $_W;

        $day = (int)$day;
        $createtime1 = 0;
        $createtime2 = 0;
        if($day != 0) {
            // $createtime1 = strtotime(date('Y-m-d',time()-$day*3600*24));
            // $createtime2 = strtotime(date('Y-m-d',time()));
            if($day == 30){
                $d = date("t");    
                $year = date("Y");
                $month = date("m");            
                $createtime1 = strtotime("{$year}-{$month}-1 00:00:00");
                $createtime2 = strtotime("{$year}-{$month}-{$d} 23:59:59");
            }else if($day == 7){
                $yest = date('Y-m-d',strtotime("0 day"));
                $createtime1 = strtotime(date('Y-m-d',strtotime("-6 day")));
                $createtime2 = strtotime("{$yest} 23:59:59");//time();//
            }else{
                $yesterday = strtotime("-1 day");
                $yy = date("Y",$yesterday);
                $ym = date("m",$yesterday);
                $yd = date("d",$yesterday);
                $createtime1 = strtotime("{$yy}-{$ym}-{$yd} 00:00:00");
                $createtime2 = strtotime("{$yy}-{$ym}-{$yd} 23:59:59");
            }   
        }else{
            $createtime1 = strtotime(date('Y-m-d',time()));
            $createtime2 = strtotime(date('Y-m-d',time()))+3600*24-1;
        }

        $condition = " and og.uniacid={$_W['uniacid']} ";
        $condition1 = ' and g.uniacid=:uniacid and g.deleted=0';
        $params1 = array(':uniacid' => $_W['uniacid']);

        if (!empty($createtime1)) {
            $condition .= " AND o.paytime >= {$createtime1}";
        }

        if (!empty($createtime2)) {
            $condition .= " AND o.paytime <= {$createtime2} ";
        }

        // filter
        $this->filter->injectConditionsSql($condition, FilterModel::TABLE_ORDER, 'o');
        $this->filter->injectConditionsSql($condition1, FilterModel::TABLE_SHOP_GOODS,'g');

        $sql = "SELECT g.id,g.title,g.thumb,"
            . "(select ifnull(sum(og.price),0) from  " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_order') . " o on og.orderid=o.id  where o.status>=1 and og.goodsid=g.id {$condition})  as money,"
            . "(select ifnull(sum(og.total),0) from  " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_order') . " o on og.orderid=o.id  where o.status>=1 and og.goodsid=g.id {$condition}) as count  "
            . "from " . tablename('elapp_shop_goods') . " g "
            . "where 1 {$condition1}  order by count desc limit 7 ";
        $list = pdo_fetchall($sql, $params1);

        return $list;
    }
}
