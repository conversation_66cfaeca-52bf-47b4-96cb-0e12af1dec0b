<?php
namespace web\controller\shop;
use web\controller\WebPage;
class CubeController extends WebPage{
	public function main(){
		global $_W,$_GPC;
        $shop = m('common')->getSysset('shop');
        $cubes = $shop['cubes'] ?? array();
		if ($_W['ispost']) {
			$imgs = $_GPC['cube_img'];
			$urls = $_GPC['cube_url'];
			$cubes = array();
			if (is_array($imgs)) {
				foreach ($imgs as $key => $img) {
					$cubes[] = array('img' => save_media($img), 'url' => trim($urls[$key]));
				}
			}
			$shop = $_W['shopset']['shop'];
			$shop['cubes'] = $cubes;
			m('common')->updateSysset(array('shop' => $shop));

			plog('shop.cube.edit', '修改基本设置');
			show_json(1);
		}
		include $this->template();
	}
}