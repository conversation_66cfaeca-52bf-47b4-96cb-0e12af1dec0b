<?php
namespace web\controller\sale;
use web\controller\ComWebPage;
class IndexController extends ComWebPage {
	public function __construct() {
		parent::__construct('sale');
	}
	public function main() {
		if (cv('sale.enough')) {
			header('location: ' . webUrl('sale/index/enough'));
		}
		else if (cv('sale.enoughfree')) {
			header('location: ' . webUrl('sale/index/enoughfree'));
		}
		/*满件优惠功能-新增fullenough Hlei20210427*/
		else if (cv('sale.fullenough')) {
			header('location: ' . webUrl('sale/index/fullenough'));
		}
		/*主推级别-新增reclevel Hlei20210701*/
		else if (cv('sale.reclevel')) {
			header('location: ' . webUrl('sale/index/reclevel'));
		}
		else if (cv('sale.deduct')) {
			header('location: ' . webUrl('sale/index/deduct'));
		}
		else if (cv('sale.recharge')) {
			header('location: ' . webUrl('sale/index/recharge'));
		}
		else if (cv('sale.credit1')) {
			header('location: ' . webUrl('sale/index/credit1'));
		}
		else if (cv('sale.package')) {
			header('location: ' . webUrl('sale/package'));
		}
		else if (cv('sale.gift')) {
			header('location: ' . webUrl('sale/gift'));
		}
		else if (cv('sale.fullback')) {
			header('location: ' . webUrl('sale/fullback'));
		}
		else if (cv('sale.peerpay')) {
			header('location: ' . webUrl('sale/peerpay'));
		}
		else if (cv('sale.coupon')) {
			header('location: ' . webUrl('sale/coupon'));
		}
		else if (cv('sale.wxcard')) {
			header('location: ' . webUrl('sale/wxcard'));
		}
		else if (cv('sale.virtual')) {
			header('location: ' . webUrl('sale/virtual'));
		}
		else {
			header('location: ' . webUrl());
		}
        die;
	}
	public function deduct() {
		global $_W,$_GPC;
		if (!(function_exists('redis')) || is_error(redis())) {
			$this->message('请联系系统管理员设置 Redis 才能使用抵扣!', '', 'error');
		}
		if ($_W['ispost']) {
			$post = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
			$data['creditdeduct'] = intval($post['creditdeduct']);
			$data['credit'] = 1;
			$data['moneydeduct'] = intval($post['moneydeduct']);
			$data['money'] = round(floatval($post['money']), 2);
			$data['dispatchnodeduct'] = intval($post['dispatchnodeduct']);
			plog('sale.deduct', '修改抵扣设置');
			m('common')->updatePluginset(array('sale' => $data));
			show_json(1);
		}
		$data = m('common')->getPluginset('sale');
		load()->func('tpl');
		include $this->template('sale/index');
	}
	public function enough() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
			$data['enoughmoney'] = round(floatval($data['enoughmoney']), 2);
			$data['enoughdeduct'] = round(floatval($data['enoughdeduct']), 2);
			$enoughs = array();
			$postenoughs = ((is_array($_GPC['enough']) ? $_GPC['enough'] : array()));
			foreach ($postenoughs as $key => $value ) {
				$enough = floatval($value);
				if (0 < $enough) {
					$enoughs[] = array('enough' => floatval($_GPC['enough'][$key]), 'give' => floatval($_GPC['give'][$key]));
				}
			}
			$data['enoughs'] = $enoughs;
			plog('sale.enough', '修改满额立减优惠');
			m('common')->updatePluginset(array('sale' => $data));
			show_json(1);
		}
		$areas = m('common')->getAreas();
		$data = m('common')->getPluginset('sale');
		load()->func('tpl');
		include $this->template('sale/enough');
	}
	public function enoughfree() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
			$data['enoughfree'] = intval($data['enoughfree']);
			$data['enoughorder'] = round(floatval($data['enoughorder']), 2);
			$data['goodsids'] = $_GPC['goodsid'];
			plog('sale.enough', '修改满额包邮优惠');
			m('common')->updatePluginset(array('sale' => $data));
			show_json(1);
		}
		$data = m('common')->getPluginset('sale');
		if (!(empty($data['goodsids']))) {
			$goods = pdo_fetchall('SELECT id,uniacid,title,thumb FROM ' . tablename('elapp_shop_goods') . ' WHERE uniacid=:uniacid AND id IN (' . implode(',', $data['goodsids']) . ')', array(':uniacid' => $_W['uniacid']));
		}
		$area_set = m('util')->get_area_config_set();
		$new_area = intval($area_set['new_area']);
		$address_street = intval($area_set['address_street']);
		$areas = m('common')->getAreas();
		include $this->template('sale/enoughfree');
	}
	public function recharge() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$recharges = array();
			$datas = ((is_array($_GPC['enough']) ? $_GPC['enough'] : array()));
			foreach ($datas as $key => $value ) {
				$enough = trim($value);
				if (!(empty($enough))) {
					$recharges[] = array('enough' => trim($_GPC['enough'][$key]), 'give' => trim($_GPC['give'][$key]));
				}
			}
			$data['recharges'] = iserializer($recharges);
			m('common')->updatePluginset(array('sale' => $data));
			plog('sale.recharge', '修改充值优惠设置');
			show_json(1);
		}
		$data = m('common')->getPluginset('sale');
		$recharges = iunserializer($data['recharges']);
		include $this->template('sale/recharge');
	}
	public function credit1() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$enough1 = array();
			$postenough1 = ((is_array($_GPC['enough1_1']) ? $_GPC['enough1_1'] : array()));
			foreach ($postenough1 as $key => $value ) {
				$enough = floatval($value);
				if (0 < $enough) {
					$enough1[] = array('enough1_1' => floatval($_GPC['enough1_1'][$key]), 'enough1_2' => floatval($_GPC['enough1_2'][$key]), 'give1' => floatval($_GPC['give1'][$key]));
				}
			}
			$data['isgoodspoint'] = intval($_GPC['isgoodspoint']);
			$data['enough1'] = $enough1;
			$enough2 = array();
			$postenough2 = ((is_array($_GPC['enough2_1']) ? $_GPC['enough2_1'] : array()));
			foreach ($postenough2 as $key => $value ) {
				$enough = floatval($value);
				if (0 < $enough) {
					$enough2[] = array('enough2_1' => floatval($_GPC['enough2_1'][$key]), 'enough2_2' => floatval($_GPC['enough2_2'][$key]), 'give2' => floatval($_GPC['give2'][$key]));
				}
			}
			if (!(empty($enough2))) {
				m('common')->updateSysset(array( 'trade' => array('credit' => 0) ));
			}
			$data['enough1'] = $enough1;
			$data['enough2'] = $enough2;
			$data['paytype'] = ((is_array($_GPC['paytype']) ? $_GPC['paytype'] : array()));
			m('common')->updatePluginset(array( 'sale' => array('credit1' => iserializer($data)) ));
			plog('sale.credit1.edit', '修改基本积分活动配置');
			show_json(1, array('url' => webUrl('sale/index/credit1', array('tab' => str_replace('#tab_', '', $_GPC['tab'])))));
		}
		$data = m('common')->getPluginset('sale');
		$credit1 = iunserializer($data['credit1']);
		$enough1 = ((empty($credit1['enough1']) ? array() : $credit1['enough1']));
		$enough2 = ((empty($credit1['enough2']) ? array() : $credit1['enough2']));
		include $this->template('sale/credit1');
	}
	public function bindmobile() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
			$data['bindmobile'] = intval($data['bindmobile']);
			$data['bindmobilecredit'] = intval($data['bindmobilecredit']);
			m('common')->updatePluginset(array('sale' => $data));
			show_json(1);
		}
		$data = m('common')->getPluginset('sale');
		include $this->template('sale/bindmobile');
	}

	/*满件优惠功能-新增 Hlei20210427*/
	public function fullenough() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$data = ((is_array($_GPC['data']) ? $_GPC['data'] : array()));
			$data['fullenoughopen'] = intval($data['fullenoughopen']);//功能开关
			$data['fullenoughmoney'] = intval(floatval($data['fullenoughmoney']), 0);//订单数量
			$data['fullenoughdeduct'] = round(floatval($data['fullenoughdeduct']), 2);//立减金额
			$data['fullenoughscale'] = round(floatval($data['fullenoughscale']), 2);//立减比例

			//$data['fullenoughorder'] = round(floatval($data['fullenoughorder']), 2);
			$data['fullgoodsids'] = ($_GPC['fullgoodsid']);

			$fullenoughs = array();
			$fullpostenoughs = ((is_array($_GPC['fullenough']) ? $_GPC['fullenough'] : array()));
			foreach ($fullpostenoughs as $key => $value ) {
				$fullenough = floatval($value);
				if (0 < $fullenough) {
					$fullenoughs[] = array('fullenough' => floatval($_GPC['fullenough'][$key]), 'fullgive' => floatval($_GPC['fullgive'][$key]), 'fullscale' => floatval($_GPC['fullscale'][$key]));
				}
			}
			$data['fullenoughs'] = $fullenoughs;
			plog('sale.fullenough', '修改满件立减优惠');
			m('common')->updatePluginset(array('sale' => $data));
			show_json(1);
		}
		$data = m('common')->getPluginset('sale');//获取营销插件数据
		if (!(empty($data['fullgoodsids']))) {
			$fullgoods = pdo_fetchall('SELECT id,uniacid,title,thumb FROM ' . tablename('elapp_shop_goods') . ' WHERE uniacid=:uniacid AND id IN (' . implode(',', $data['fullgoodsids']) . ')', array(':uniacid' => $_W['uniacid']));
		}
		$areas = m('common')->getAreas();
		
		load()->func('tpl');
		include $this->template('sale/fullenough');
	}
	/**
	 * 商品主推级别 RecLevel
	 * Hlei 20210701
	 */
	public function reclevel() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$reclevel_1 = array();
			$postreclevel_1 = ((is_array($_GPC['level']) ? $_GPC['level'] : array()));
			foreach ($postreclevel_1 as $key => $value ) {
				$enough = floatval($value);
				if (0 < $enough) {
					$reclevel_1[] = array('level' => intval($_GPC['level'][$key]), 'levelname' => $_GPC['levelname'][$key], 'give1' => floatval($_GPC['give1'][$key]));
				}
			}
			$data['isgoodsreclevel'] = intval($_GPC['isgoodsreclevel']);
			$data['reclevel_1'] = $reclevel_1;
			$enough2 = array();
			$postenough2 = ((is_array($_GPC['enough2_1']) ? $_GPC['enough2_1'] : array()));
			foreach ($postenough2 as $key => $value ) {
				$enough = floatval($value);
				if (0 < $enough) {
					$enough2[] = array('enough2_1' => floatval($_GPC['enough2_1'][$key]), 'enough2_2' => floatval($_GPC['enough2_2'][$key]), 'give2' => floatval($_GPC['give2'][$key]));
				}
			}
			if (!(empty($enough2))) {
				m('common')->updateSysset(array( 'trade' => array('credit' => 0) ));
			}
			$data['reclevel_1'] = $reclevel_1;
			$data['enough2'] = $enough2;
			$data['paytype'] = ((is_array($_GPC['paytype']) ? $_GPC['paytype'] : array()));
			m('common')->updatePluginset(array( 'sale' => array('reclevel' => iserializer($data)) ));
			plog('sale.reclevel.edit', '修改商品主推级别基本配置');
			show_json(1, array('url' => webUrl('sale/index/reclevel', array('tab' => str_replace('#tab_', '', $_GPC['tab'])))));
		}
		$data = m('common')->getPluginset('sale');
		$reclevel = iunserializer($data['reclevel']);
		$reclevel_1 = ((empty($reclevel['reclevel_1']) ? array() : $reclevel['reclevel_1']));
		$enough2 = ((empty($reclevel['enough2']) ? array() : $reclevel['enough2']));
		include $this->template('sale/reclevel');
	}	
}