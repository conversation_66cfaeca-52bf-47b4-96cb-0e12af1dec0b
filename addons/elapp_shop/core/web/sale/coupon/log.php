<?php
namespace web\controller\sale\coupon;
use web\controller\ComWebPage;

class LogController extends ComWebPage {

    public function __construct($_com = 'coupon') {
        parent::__construct($_com);
    }

    function main() {
        global $_W, $_GPC;

        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $condition = ' d.uniacid = :uniacid and d.merchid=0';
        $params = array(':uniacid' => $_W['uniacid']);
        $couponid = intval($_GPC['couponid']);

        if (!empty($couponid)) {
            $coupon = pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id and uniacid=:uniacid and merchid=0 limit 1', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));
            $condition .= " AND c.id=" . intval($couponid);
        }
        $searchfield = strtolower(trim($_GPC['searchfield']));
        $keyword = trim($_GPC['keyword']);
        if (!empty($searchfield) && !empty($keyword)) {
            if ($searchfield == 'member') {
                $condition.=' and ( m.realname like :keyword or m.nickname like :keyword or m.mobile like :keyword)';
            } else if ($searchfield == 'coupon') {
                $condition.=' and c.couponname like :keyword';
            }
            $params[':keyword'] = "%{$keyword}%";
        }

        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        if (empty($starttime1) || empty($endtime1)) {
            $starttime1 = strtotime('-1 month');
            $endtime1 = time();
        }
        if (!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])) {
            $starttime = strtotime($_GPC['time']['start']);
            $endtime = strtotime($_GPC['time']['end']);


            $condition .= " AND d.gettime >= :starttime AND d.gettime <= :endtime ";
            $params[':starttime'] = $starttime;
            $params[':endtime'] = $endtime;
        }
        if (!empty($_GPC['time1']['start']) && !empty($_GPC['time1']['end'])) {
            $starttime1 = strtotime($_GPC['time1']['start']);
            $endtime1 = strtotime($_GPC['time1']['end']);

            $condition .= " AND d.usetime >= :starttime1 AND d.usetime <= :endtime1 ";
            $params[':starttime1'] = $starttime1;
            $params[':endtime1'] = $endtime1;
        }
        if ($_GPC['type'] != '') {
            $condition .= ' AND c.coupontype = :coupontype';
            $params[':coupontype'] = intval($_GPC['type']);
        }
        if ($_GPC['used'] != '') {
            $condition .= ' AND d.used =' . intval($_GPC['used']);
        }
        if ($_GPC['gettype'] != '') {
            $condition .= ' AND d.gettype = :gettype';
            $params[':gettype'] = intval($_GPC['gettype']);
        }

        (new \app\model\FilterModel())->injectConditionsSql($condition, \app\model\FilterModel::TABLE_MEMBER, 'm');
        $sql = 'SELECT d.*, c.coupontype,c.couponname,m.nickname,m.avatar,m.realname,m.mobile,m.id as mid FROM ' . tablename('elapp_shop_coupon_data') . " d "
            . " left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . " left join " . tablename('elapp_shop_member') . " m on m.openid = d.openid and m.uniacid = d.uniacid"
            . " where  1 and {$condition} ORDER BY gettime DESC";
        if (empty($_GPC['export'])) {
            $sql.=" LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
        }
        $list = pdo_fetchall($sql, $params);
        foreach ($list as &$row) {

            $couponstr = "消费";
            if ($row['coupontype'] == 1) {
                $couponstr = "充值";
            }
            $row['couponstr'] = $couponstr;
            if ($row['gettype'] == 0) {
                $row['gettypestr'] = "后台发放";
            } else if ($row['gettype'] == 1) {
                $row['gettypestr'] = "领券中心";
            } else if ($row['gettype'] == 2) {
                $row['gettypestr'] = "积分商城";
            } else if ($row['gettype'] == 3) {
                $row['gettypestr'] = "任务海报";
            } else if ($row['gettype'] == 4) {
                $row['gettypestr'] = "超级海报";
            } else if ($row['gettype'] == 5) {
                $row['gettypestr'] = "活动海报";
            }else if ($row['gettype'] == 6) {
                $row['gettypestr'] = "任务发送";
            }else if ($row['gettype'] == 7) {
                $row['gettypestr'] = "兑换中心";
            }else if ($row['gettype'] == 8) {
                $row['gettypestr'] = "快速领取";
            }else if ($row['gettype'] == 9) {
                $row['gettypestr'] = "收银台发送";
            }else if ($row['gettype'] == 10) {
                $row['gettypestr'] = "微信会员卡激活发送";
            }else if ($row['gettype'] == 11) {
                $row['gettypestr'] = "直播间领取优惠券";
            }else if ($row['gettype'] == 12) {
                $row['gettypestr'] = "直播间推送优惠券";
            }else if ($row['gettype'] == 13) {
                $row['gettypestr'] = "口令优惠券";
            }else if ($row['gettype'] == 14) {
                $row['gettypestr'] = "新人领券";
            }else if ($row['gettype'] == 15) {
                $row['gettypestr'] = "发券分享";
            }
        }
        unset($row);
        if ($_GPC['export'] == 1) {
            ca('sale.coupon.log.export');

            foreach ($list as &$row) {

                $row['gettime'] = date('Y-m-d H:i', $row['gettime']);
                if (!empty($row['usetime'])) {
                    $row['usetime'] = date('Y-m-d H:i', $row['usetime']);
                } else {
                    $row['usetime'] = "---";
                }
            }
            $columns = array(
                array('title' => 'ID', 'field' => 'id', 'width' => 12),
                array('title' => '优惠券', 'field' => 'couponname', 'width' => 24),
                array('title' => '类型', 'field' => 'couponstr', 'width' => 12),
                array('title' => '会员信息', 'field' => 'nickname', 'width' => 12),
                array('title' => '姓名', 'field' => 'realname', 'width' => 12),
                array('title' => '手机号', 'field' => 'mobile', 'width' => 12),
                array('title' => 'openid', 'field' => 'openid', 'width' => 24),
                array('title' => '获取方式', 'field' => 'gettypestr', 'width' => 12),
                array('title' => '获取时间', 'field' => 'gettime', 'width' => 12),
                array('title' => '使用时间', 'field' => 'usetime', 'width' => 12),
                array('title' => '使用单号', 'field' => 'ordersn', 'width' => 12)
            );
            m('excel')->export($list, array(
                "title" => "优惠券数据-" . date('Y-m-d-H-i', time()),
                "columns" => $columns
            ));
            plog('sale.coupon.log.export', '导出优惠券发放记录');
        }
        $total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename('elapp_shop_coupon_data') . " d "
            . " left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . " left join " . tablename('elapp_shop_member') . " m on m.openid = d.openid and m.uniacid = d.uniacid "
            . "where 1 and {$condition}", $params);
        $pager = pagination2($total, $pindex, $psize);
        include $this->template();
    }

    public function delete(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if(!$id){
            show_json(0,'数据不存在');
        }
        $condition = ' uniacid = :uniacid and merchid=0 and used = 0';
        $params = array(':uniacid' => $_W['uniacid']);
        $condition.=' and id = :id';
        $params[':id'] = "{$id}";
        $sql = 'SELECT id FROM ' . tablename('elapp_shop_coupon_data')
            . " where  1 and {$condition}";
        $data = pdo_fetch($sql,$params);
        if(!$data){
            show_json(0,'数据不存在');
        }
        $result = pdo_delete('elapp_shop_coupon_data',array('id'=>$id));
        if($result){
            show_json(1,'删除成功');
        }else{
            show_json(0,'删除失败');
        }
    }

    public function apply(){
        global $_W, $_GPC;

        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $condition = ' d.uniacid = :uniacid and d.merchid=0';
        $params = array(':uniacid' => $_W['uniacid']);
        $couponid = intval($_GPC['couponid']);
        $searchfield = strtolower(trim($_GPC['searchfield']));

        $keyword = trim($_GPC['keyword']);
        if (!empty($searchfield) && !empty($keyword)) {
            $condition.=' and openid like :keyword';
            $params[':openid'] = "%{$keyword}%";
        }

        if ($_GPC['used'] != '') {
            $condition .= ' AND d.status =' . intval($_GPC['used']);
        }
        $sql = 'SELECT d.*,c.id as couponid, c.applymax,c.coupontype,c.couponname,m.nickname,m.avatar,m.realname,m.mobile,m.id as mid FROM ' . tablename('elapp_shop_coupon_apply_log') . " d "
            . " inner join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . " inner join " . tablename('elapp_shop_member') . " m on m.openid = d.openid and m.uniacid = d.uniacid"
            . " where  1 and {$condition} ORDER BY d.id DESC";

        if (empty($_GPC['export'])) {
            $sql.=" LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
        }
        
        $list = pdo_fetchall($sql, $params);
        $total = pdo_fetchcolumn( 'SELECT count(*) FROM ' . tablename('elapp_shop_coupon_apply_log') . " d "
            . " where  1 and {$condition}" , $params);
        $pager = pagination2($total, $pindex, $psize);
        include $this->template('sale/coupon/apply');
    }

    public function apply_send(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $condition = ' d.uniacid = :uniacid and d.merchid=0';
        $params = array(':uniacid' => $_W['uniacid']);
        $condition.=' and d.id = :id';
        $params[':id'] = "{$id}";

        $sql = 'SELECT d.*,c.id as couponid, c.applymax,c.coupontype,c.couponname,m.nickname,m.avatar,m.realname,m.mobile,m.id as mid FROM ' . tablename('elapp_shop_coupon_apply_log') . " d "
            . " left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . " left join " . tablename('elapp_shop_member') . " m on m.openid = d.openid and m.uniacid = d.uniacid"
            . " where  1 and {$condition}";
        $data = pdo_fetch($sql, $params);
        include $this->template('sale/coupon/apply_send');
    }

    public function apply_do(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if(!$id){
            show_json(0,'数据不存在');
        }
        $condition = ' d.uniacid = :uniacid and d.merchid=0 and d.status = 0';
        $params = array(':uniacid' => $_W['uniacid']);
        $condition.=' and d.id = :id';
        $params[':id'] = "{$id}";

        $sql = 'SELECT d.*,c.id as couponid, c.applymax,c.coupontype,c.couponname as mid FROM ' . tablename('elapp_shop_coupon_apply_log') . " d "
            . " left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . " where  1 and {$condition}";
        $data = pdo_fetch($sql,$params);
        if(!$data){
            show_json(0,'数据不存在');
        }
        if(intval($_GPC['send_total']) > intval($data['count'])){
            show_json(0,'发放数量不能大于申请数量');
        }
        $sendCoupon = new SendcouponController();
        $sendCoupon->fetch(true);
        pdo_update('elapp_shop_coupon_apply_log', array('status' =>  1), array('id' => $id));
        show_json(1, array());
    }
}
