<?php
namespace web\controller\sale\coupon;
use app\core\com\logic\sale\coupon\CouponLogic;
use web\controller\ComWebPage;

class TicketCouponController extends ComWebPage {

    public function __construct($_com = 'coupon') {
        parent::__construct($_com);
    }

    //资格券查询列表
    function main() {
        global $_W, $_GPC;
        $kwd = trim($_GPC['keyword']);
        $diy = intval($_GPC['diy']);
        $where = [['merchid' , '=', $_GPC['merchid']?: 0], ['coupontype', '=', 3]];
        if (!empty($kwd)) {
            $where[] = ['couponname', 'like', "%{$kwd}%"];
        }
        $time = time();
        $list_result = app(CouponLogic::class)->getCouponList($where);
        $ds = $list_result['data'];
        if(!empty($ds)){
            foreach ($ds as &$d) {
                $d = com('coupon')->setCoupon($d, $time, false);
                $d['last'] = com('coupon')->get_last_count($d['id']);
                if ($d['last'] == -1) {
                    $d['last'] = '不限';
                }
            }
            unset($d);
        }

        include $this->template('sale/coupon/query_ticket_coupon');
    }
}
