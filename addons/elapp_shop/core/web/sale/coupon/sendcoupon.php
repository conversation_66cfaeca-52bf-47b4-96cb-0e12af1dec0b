<?php
namespace web\controller\sale\coupon;
use web\controller\ComWebPage;

class SendcouponController extends ComWebPage {

    public function __construct($_com = 'coupon') {
        parent::__construct($_com);
    }

    function main() {
        global $_W, $_GPC;

        $couponid = intval($_GPC['couponid']);
        $coupon = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_coupon') . ' WHERE id=:id and uniacid=:uniacid and merchid=0', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));

        $list = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_member_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY level asc");
        $list2 = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_member_group') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY id asc");
        $coupons = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_coupon') . " WHERE uniacid = '{$_W['uniacid']}' and merchid=0 and isfriendcoupon = 0  ORDER BY id asc");


        //是否开启分销商
        $hascommission = false;
        $plugin_com = p('commission');
        if ($plugin_com) {
            $plugin_com_set = $plugin_com->getSet();
            $hascommission = !empty($plugin_com_set['level']);
        }

        //是否开启股东
        $hasglobonus = false;
        $plugin_globonus = p('globonus');
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !empty($plugin_globonus_set['open']);
        }

        //是否开启区域代理
        $hasabonus = false;
        $plugin_abonus = p('abonus');
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !empty($plugin_abonus_set['open']);
        }

        //分销商列表
        if ($hascommission) {
            $list3 = $plugin_com->getLevels();
        }

        //股东列表
        if ($hasglobonus) {
            $list4 = $plugin_globonus->getLevels();
        }

        //区域代理列表
        if ($hasabonus) {
            $list5 = $plugin_abonus->getLevels();
        }

        $data = m('common')->getPluginset('coupon');

        m('common')->updatePluginset(array('coupon'=>$data));

        load()->func('tpl');

        include $this->template();
    }

    function fetch($return = false) {
        global $_W, $_GPC;


        $couponid = intval($_GPC['couponid']);
        $class1 = $_GPC['send1']; //1:openid 2:level 3:class 4:all
        $coupon = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_coupon') . ' WHERE id=:id and uniacid=:uniacid and merchid=0', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));

        if (empty($coupon)) {
            show_json(0, '未找到优惠券!');
        }

        $send_total=intval($_GPC['send_total']);

        if(empty($send_total))
        {
            show_json(0, '发送数量最小为1!');
        }

        if ($class1 == 1) {

            $send_openid = $_GPC['send_openid'];

            $openids =explode(",",$send_openid);
            $plog = "发放优惠券 ID: {$couponid} 方式: 指定 OPENID 人数: " . count($openids);
        } elseif ($class1 == 2) {
            $where = '';
            if (!empty($_GPC['send_level'])) {
                $where.= " and level =" . intval($_GPC['send_level']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}'" . $where, array(), 'openid');
            if (!empty($_GPC['send_level'])) {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_member_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_level']));
            } else {
                $levelname = "全部";
            }
            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid} 方式: 等级-{$levelname} 人数: " . count($members);
        } elseif ($class1 == 3) {
            $where = '';
            if (!empty($_GPC['send_group'])) {

                $where.=' and find_in_set('.intval($_GPC['send_group']).',groupid) ';
            }

            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}'" . $where, array(), 'openid');
            if (!empty($_GPC['send_group'])) {
                $groupname = pdo_fetchcolumn('select groupname from ' . tablename('elapp_shop_member_group') . ' where id=:id limit 1', array(':id' => $_GPC['send_group']));
            } else {
                $groupname = "全部分组";
            }
            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 分组-{$groupname} 人数: " . count($members);
        } elseif ($class1 == 4) {
            $where = '';
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}'" . $where, array(), 'openid');
            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 全部会员 人数: " . count($members);


        } elseif ($class1 == 5) {
            $where = '';
            if (!empty($_GPC['send_agentlevel'])||$_GPC['send_partnerlevels']==='0') {
                $where.= " and agentlevel =" . intval($_GPC['send_agentlevel']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}' and isagent=1 and status=1 " . $where, array(), 'openid');
            if ($_GPC['send_agentlevel'] != '') {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_commission_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_agentlevel']));
            } else {
                $levelname = "全部";
            }

            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 分销商-{$levelname} 人数: " . count($members);
        } elseif ($class1 == 6) {

            $where = '';
            if (!empty($_GPC['send_partnerlevels'])||$_GPC['send_partnerlevels']==='0') {
                $where.= " and partnerlevel =" . intval($_GPC['send_partnerlevels']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}' and ispartner=1 and partnerstatus=1 " . $where, array(), 'openid');
            if ($_GPC['send_partnerlevels'] != '') {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_globonus_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_partnerlevels']));
            } else {
                $levelname = "全部";
            }

            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 股东-{$levelname} 人数: " . count($members);
        }elseif ($class1 == 7) {
            $where = '';
            if (!empty($_GPC['send_aagentlevels'])||$_GPC['send_aagentlevels']==='0') {
                $where.= " and aagentlevel =" . intval($_GPC['send_aagentlevels']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}' and isaagent=1 and aagentstatus=1 " . $where, array(), 'openid');
            if ($_GPC['send_aagentlevels'] != '') {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_abonus_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_aagentlevels']));
            } else {
                $levelname = "全部";
            }

            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 区域代理-{$levelname} 人数: " . count($members);
        } elseif ($class1 == 8) {
            $where = '';
            if (!empty($_GPC['send_clerklevel'])||$_GPC['send_partnerlevels']==='0') {
                $where.= " and clerk_level =" . intval($_GPC['send_clerklevel']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}' and is_clerk=1 and clerk_status=1 " . $where, array(), 'openid');
            if ($_GPC['send_clerklevel'] != '') {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_clerk_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_clerklevel']));
            } else {
                $levelname = "全部";
            }

            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 店员-{$levelname} 人数: " . count($members);
        } elseif ($class1 == 9) {
            $where = '';
            if (!empty($_GPC['send_doctorlevel'])||$_GPC['send_partnerlevels']==='0') {
                $where.= " and doctor_level =" . intval($_GPC['send_doctorlevel']);
            }
            $members = pdo_fetchall("SELECT openid FROM " . tablename('elapp_shop_member') . " WHERE uniacid = '{$_W['uniacid']}' and is_doctor=1 and doctor_status=1 " . $where, array(), 'openid');
            if ($_GPC['send_doctorlevel'] != '') {
                $levelname = pdo_fetchcolumn('select levelname from ' . tablename('elapp_shop_doctor_level') . ' where id=:id limit 1', array(':id' => $_GPC['send_doctorlevel']));
            } else {
                $levelname = "全部";
            }

            $openids = array_keys($members);
            $plog = "发放优惠券 ID: {$couponid}  方式: 医生-{$levelname} 人数: " . count($members);
        }

        $mopenids = array();
        foreach ($openids as $openid) {
            $mopenids[] = "'" . str_replace("'", "''", $openid) . "'";
        }
        if (empty($mopenids)) {
            show_json(0, '未找到发送的会员!');
        }
        $members = pdo_fetchall('select id,openid,nickname from ' . tablename('elapp_shop_member') . ' where openid in (' . implode(',', $mopenids) . ") and uniacid={$_W['uniacid']}");
        if (empty($members)) {
            show_json(0, '未找到发送的会员!');
        }

        if ($coupon['total'] != -1) {
            //判断剩余数量
            $last = com('coupon')->get_last_count($couponid);

            if ($last <= 0) {
                show_json(0, '优惠券数量不足,无法发放!');
            }
            $need = count($members) - $last;
            if ($need > 0) {
                show_json(0, "优惠券数量不足,请补充 {$need} 张优惠券才能发放!");
            }
        }

        $data=array(

            'sendtemplateid'  => $_GPC['sendtemplateid'],
            'frist' => $_GPC['frist'],
            'fristcolor'  => $_GPC['fristcolor'],
            'keyword1' => $_GPC['keyword1'],
            'keyword1color'  => $_GPC['keyword1color'],
            'keyword2'  => $_GPC['keyword2'],
            'keyword2color' => $_GPC['keyword2color'],
            'remark' => $_GPC['remark'],
            'remarkcolor'  => $_GPC['remarkcolor'],
            'templateurl' => $_GPC['templateurl'],

            'custitle'  => $_GPC['custitle'],
            'custhumb' => $_GPC['custhumb'],
            'cusdesc'  => $_GPC['cusdesc'],
            'cusurl'  => $_GPC['cusurl']
        );


        m('common')->updatePluginset(array('coupon'=>$data));



        $time = time();
        foreach ($members as $m) {
            for ($i = 1; $i <= $send_total; $i++) {
                $member = m('member')->getMember($m['openid']);
                //增加优惠券日志
                $log = array(
                    'uniacid' => $_W['uniacid'],
                    'merchid' => $coupon['merchid'],
                    'openid' => $m['openid'],
                    'member_id' => $member['id'],
                    'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                    'couponid' => $couponid,
                    'status' => 1,
                    'paystatus' => -1,
                    'creditstatus' => -1,
                    'createtime' => $time,
                    'getfrom' => 0
                );
                pdo_insert('elapp_shop_coupon_log', $log);
                $logid = pdo_insertid();

                $data = array(
                    'uniacid' => $_W['uniacid'],
                    'merchid' => $coupon['merchid'],
                    'openid' => $m['openid'],
                    'member_id' => $member['id'],
                    'couponid' => $couponid,
                    'gettype' => 0,
                    'gettime' => $time,
                    'senduid' => $_W['uid']
                );
                pdo_insert('elapp_shop_coupon_data', $data);
            }
        }
        if($return == true){
            return true;
        }
        show_json(1, array('openids' => $openids));
    }

    function sendmessage() {
        global $_GPC, $_W;

        $openid = $_GPC['openid'];
        $messagetype = intval($_GPC['messagetype']);
        $couponid = intval($_GPC['couponid']);

        $data = m('common')->getPluginset('coupon');

        if(empty($messagetype)) {

            die(json_encode(array('result' => 0)));
        } else if($messagetype==1) {
            if (empty($data['sendtemplateid'])) {
                die(json_encode(array('result' => 0, 'mesage' => '未指设定发送模板!', 'openid' => $openid)));
            }
            if (empty($openid)) {
                die(json_encode(array('result' => 0, 'mesage' => '未指定openid!', 'openid' => $openid)));
            }

            //die(json_encode(array('result' => 0, 'message' => 'openid:'.$openid.'\'', 'openid' => $openid)));

            $msg = array(
                'first' => array('value' => $data['frist'], 'color' => $data['fristcolor']),
                'remark' => array('value' => $data['remark'], 'color' => $data['remarkcolor'])
            );

            $msg['keyword1'] = array('value' => '会员通知', 'color' => $data['keyword1color']);
            $msg['keyword2'] = array('value' => $data['keyword1'], 'color' => $data['keyword1color']);
            $msg['keyword3'] = array('value' => $data['keyword2'], 'color' => $data['keyword2color']);

            if(empty($data['templateurl']))
            {
                $data['templateurl']= mobileUrl('sale/coupon/my/main',null,true);

            }

            $result = m('message')->sendTplNotice($openid, $data['sendtemplateid'], $msg, $data['templateurl']);
            if(is_error($result)) {
                die(json_encode(array('result' => 0, 'message' => $result['message'], 'openid' => $openid)));
            }

            die(json_encode(array('result' => 1)));
        } else if($messagetype==2) {
            if (empty($openid)) {
                die(json_encode(array('result' => 0, 'mesage' => '未指定openid!', 'openid' => $openid)));
            }

            if(empty($data['cusurl'])) {
                $data['cusurl']= mobileUrl('sale/coupon/my/main',null,true);
            }

            $resp = $this->sendNews($openid, $data['custitle'],$data['cusdesc'],$data['cusurl'],$data['custhumb']);

            if(is_error($resp)){
                die(json_encode(array('result' => 0, 'message' => $resp['message'], 'openid' => $openid)));
            }

            die(json_encode(array('result' => 1)));
        }else{
            die(json_encode(array('result' => 0)));
        }
    }

    function sendNews($openid,$title,$desc,$url,$picurl, $account = null){
        global  $_W;
        $result =false;
        $articles[] = array(
            "title" => urlencode($title),
            "description" => urlencode($desc),
            "url" => $url,
            "picurl" => tomedia($picurl)
        );
        $result = m('message')->sendNews($openid, $articles, $account);
        return $result;
    }
}
