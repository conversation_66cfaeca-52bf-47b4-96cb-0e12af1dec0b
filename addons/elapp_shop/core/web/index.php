<?php
namespace web\controller;

class IndexController extends WebPage {
	public function main(){
		global $_W,$_GPC;
		if (empty($_W['shopversion'])) {
			header('location:' . webUrl('shop'));
			exit();
		}
		$shop_data = m('common')->getSysset('shop');
		//多商户
		$merch_plugin = p('merch');
		$merch_data = m('common')->getPluginset('merch');
		if ($merch_plugin && $merch_data['is_openmerch']) {
			$is_openmerch = 1;
		}else {
			$is_openmerch = 0;
		}
		//机构合伙人
		$copartner_plugin = p('copartner');
		$copartner_data = m('common')->getPluginset('copartner');
		if ($copartner_plugin && $copartner_data['is_opencopartner']) {
			$is_opencopartner = 1;
		}else {
			$is_opencopartner = 0;
		}
		//供应商
		$supply_plugin = p('supply');
		$supply_data = m('common')->getPluginset('supply');
		if ($supply_plugin && $supply_data['is_opensupply']) {
			$is_opensupply = 1;
		}else {
			$is_opensupply = 0;
		}

		$hascommission = false;

		if (p('commission')) {
			$hascommission = 0 < intval($_W['shopset']['commission']['level']);
		}
        if (p('clerk')) {
            $clerkSet = m('common')->getPluginset('clerk');
            $hasClerk = 0 < intval($clerkSet['level']) && 0 < intval($clerkSet['isopen']);
        }
        if (p('copartner')) {
            $copartnerSet = m('common')->getPluginset('copartner');
            $hasCopartner = 0 < intval($copartnerSet['level']) && 0 < intval($copartnerSet['is_opencopartner']);
        }

		$ordercol = 6;
		if (cv('goods') && cv('order')) {
			$ordercol = 6;
		} else {
			if (cv('goods') && !cv('order')) {
				$ordercol = 12;
			} else {
				if (cv('order') && !cv('goods')) {
					$ordercol = 12;
				} else {
					$ordercol = 0;
				}
			}
		}

		$pluginnum = m('plugin')->getCount();
		$no_left = true;
		include $this->template();
	}

	public function searchlist() {
		global $_W,$_GPC;
		$return_arr = array();
		$menu = m('system')->getSubMenus(true, true);
		$keyword = trim($_GPC['keyword']);
		if (empty($keyword) || empty($menu)) {
			show_json(1, array('menu' => $return_arr));
		}
		foreach ($menu as $index => $item) {
			if (strexists($item['title'], $keyword) || strexists($item['desc'], $keyword) || strexists($item['keywords'], $keyword) || strexists($item['topsubtitle'], $keyword)) {
				if (cv($item['route'])) {
					$return_arr[] = $item;
				}
			}
		}
		show_json(1, array('menu' => $return_arr));
	}

	public function search() {
		global $_W,$_GPC;
		$keyword = trim($_GPC['keyword']);
		$list = array();
		$history = $_GPC['history_search'];

		if (empty($history)) {
			$history = array();
		} else {
			$history = htmlspecialchars_decode($history);
			$history = json_decode($history, true);
		}

		if (!empty($keyword)) {
			$submenu = m('system')->getSubMenus(true, true);
			if (!empty($submenu)) {
				foreach ($submenu as $index => $submenu_item) {
					$top = $submenu_item['top'];
					if (strexists($submenu_item['title'], $keyword) || strexists($submenu_item['desc'], $keyword) || strexists($submenu_item['keywords'], $keyword) || strexists($submenu_item['topsubtitle'], $keyword)) {
						if (cv($submenu_item['route'])) {
							if (!is_array($list[$top])) {
								$title = (!empty($submenu_item['topsubtitle']) ? $submenu_item['topsubtitle'] : $submenu_item['title']);
								if (strexists($title, $keyword)) {
									$title = str_replace($keyword, '<b>' . $keyword . '</b>', $title);
								}
								$list[$top] = array(
									'title' => $title,
									'items' => array()
								);
							}
							if (strexists($submenu_item['title'], $keyword)) {
								$submenu_item['title'] = str_replace($keyword, '<b>' . $keyword . '</b>', $submenu_item['title']);
							}
							if (strexists($submenu_item['desc'], $keyword)) {
								$submenu_item['desc'] = str_replace($keyword, '<b>' . $keyword . '</b>', $submenu_item['desc']);
							}
							$list[$top]['items'][] = $submenu_item;
						}
					}
				}
			}
			if (empty($history)) {
				$history_new = array($keyword);
			} else {
				$history_new = $history;
				foreach ($history_new as $index => $key) {
					if ($key == $keyword) {
						unset($history_new[$index]);
					}
				}
				$history_new = array_merge(array($keyword), $history_new);
				$history_new = array_slice($history_new, 0, 20);
			}
			isetcookie('history_search', json_encode($history_new), 7 * 86400);
			$history = $history_new;
		}

		include $this->template();
	}

	public function clearhistory() {
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$type = intval($_GPC['type']);
			if (empty($type)) {
				isetcookie('history_url', '', -7 * 86400);
			} else {
				isetcookie('history_search', '', -7 * 86400);
			}
		}
		show_json(1);
	}

	public function switchversion() {
		global $_W,$_GPC;
		$route = trim($_GPC['route']);
		$id = intval($_GPC['id']);
		$set = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_version') . ' WHERE uid=:uid AND `type`=0', array(':uid' => $_W['uid']));
		$data = array('version' => !empty($_W['shopversion']) ? 0 : 1);

		if (empty($set)) {
			$data['uid'] = $_W['uid'];
			pdo_insert('elapp_shop_version', $data);
		} else {
			pdo_update('elapp_shop_version', $data, array('id' => $set['id']));
		}
		$params = array();
		if (!empty($id)) {
			$params['id'] = $id;
		}
		load()->model('cache');
		cache_clean();
		cache_build_template();
		header('location: ' . webUrl($route, $params));
		exit();
	}
}