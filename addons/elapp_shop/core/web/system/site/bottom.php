<?php
namespace web\controller\system\site;
use web\controller\SystemPage;
class Bottom<PERSON>ontroller extends SystemPage{
	public $mid;
	public function __construct(){
		global $_W;
		$username = $_W['username'];
		$this->mid = 1;//pdo_getcolumn('elapp_shop_system_site_domain', array("username" => $username), 'id');
		$website = $_SERVER['HTTP_HOST'];
		$web = pdo_get('elapp_shop_system_site_domain', array("domain" => $website, "username" => $_W['username']));
		/* if (!$web) {
			itoast('请先联系创始人绑定用户名和域名', referer(), 'error');
			exit;
		} */		
	}
	public function main(){
		global $_GPC, $_W;
		if (checksubmit()) {
			$post_data['uniacid'] = 0;
			$post_data['mid'] = $this->mid;
			$post_data['about'] = $_GPC['about'];
			$post_data['about_sub1'] = $_GPC['about_sub1'];
			$post_data['about_sub2'] = $_GPC['about_sub2'];
			$post_data['about_sub3'] = $_GPC['about_sub3'];
			$post_data['about_sub4'] = $_GPC['about_sub4'];
			$post_data['about_sub5'] = $_GPC['about_sub5'];
			$post_data['protect'] = $_GPC['protect'];
			$post_data['protect_sub1'] = $_GPC['protect_sub1'];
			$post_data['protect_sub2'] = $_GPC['protect_sub2'];
			$post_data['protect_sub3'] = $_GPC['protect_sub3'];
			$post_data['protect_sub4'] = $_GPC['protect_sub4'];
			$post_data['protect_sub5'] = $_GPC['protect_sub5'];
			$post_data['help'] = $_GPC['help'];
			$post_data['help_sub1'] = $_GPC['help_sub1'];
			$post_data['help_sub2'] = $_GPC['help_sub2'];
			$post_data['help_sub3'] = $_GPC['help_sub3'];
			$post_data['help_sub4'] = $_GPC['help_sub4'];
			$post_data['help_sub5'] = $_GPC['help_sub5'];
			$post_data['contact'] = $_GPC['contact'];
			$post_data['contact_sub1'] = $_GPC['contact_sub1'];
			$post_data['contact_sub2'] = $_GPC['contact_sub2'];
			$post_data['contact_sub3'] = $_GPC['contact_sub3'];
			$post_data['contact_sub4'] = $_GPC['contact_sub4'];
			$post_data['contact_sub5'] = $_GPC['contact_sub5'];
			$post_data['about_link1'] = $_GPC['about_link1'];
			$post_data['about_link2'] = $_GPC['about_link2'];
			$post_data['about_link3'] = $_GPC['about_link3'];
			$post_data['about_link4'] = $_GPC['about_link4'];
			$post_data['about_link5'] = $_GPC['about_link5'];
			$post_data['protect_link1'] = $_GPC['protect_link1'];
			$post_data['protect_link2'] = $_GPC['protect_link2'];
			$post_data['protect_link3'] = $_GPC['protect_link3'];
			$post_data['protect_link4'] = $_GPC['protect_link4'];
			$post_data['protect_link5'] = $_GPC['protect_link5'];
			$post_data['help_link1'] = $_GPC['help_link1'];
			$post_data['help_link2'] = $_GPC['help_link2'];
			$post_data['help_link3'] = $_GPC['help_link3'];
			$post_data['help_link4'] = $_GPC['help_link4'];
			$post_data['help_link5'] = $_GPC['help_link5'];
			$post_data['contact_link1'] = $_GPC['contact_link1'];
			$post_data['contact_link2'] = $_GPC['contact_link2'];
			$post_data['contact_link3'] = $_GPC['contact_link3'];
			$post_data['contact_link4'] = $_GPC['contact_link4'];
			$post_data['contact_link5'] = $_GPC['contact_link5'];
			$post_data['time_work'] = $_GPC['time_work'];
			$post_data['time_day'] = $_GPC['time_day'];
			$post_data['public_name'] = $_GPC['public_name'];
			$post_data['xcx_name'] = $_GPC['xcx_name'];
			$is_con = pdo_get('elapp_shop_system_site_bottom', array("mid" => $this->mid));
			if ($is_con) {
				$res = pdo_update('elapp_shop_system_site_bottom', $post_data, ["mid" => $this->mid]);
				if (!$res) {
					show_json(0, array('message' => '修改失败','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			} else {
				$res = pdo_insert('elapp_shop_system_site_bottom', $post_data);
				if (!$res) {
					show_json(0, array('message' => '添加失败','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			}
		} else {
			$data = pdo_get('elapp_shop_system_site_bottom', array("mid" => $this->mid));
			include $this->template();
		}	
	}
}