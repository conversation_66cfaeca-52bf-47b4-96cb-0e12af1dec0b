<?php
namespace web\controller\system\site;
use web\controller\SystemPage;
class HonorController extends SystemPage{
	public function main(){
		global $_W,$_GPC;
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
		$condition = '';
		$params = array();

		if ($_GPC['status'] != '') {
			$condition .= ' and status=' . intval($_GPC['status']);
		}
		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and title like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}
		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_honor') . ' WHERE 1 ' . $condition . '  ORDER BY displayorder DESC limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);
		$total = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_system_site_honor') . ' WHERE 1 ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add(){
		$this->post();
	}

	public function edit(){
		$this->post();
	}

	protected function post(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$mid = 1;
		if ($_W['ispost']) {
			empty($_GPC['title']) && show_json(0, array('message' => '名称不能为空', 'url' => referer()));
			empty($_GPC['thumb']) && show_json(0, array('message' => '头像不能为空', 'url' => referer()));
			$data = array(
				'uniacid' => trim($_GPC['uniacid']),
				'mid' => $mid,
				'title' => trim($_GPC['title']),
				'background' => trim($_GPC['background']),
				'thumb' => trim($_GPC['thumb']),				
				'content' => trim($_GPC['content']),				
				'displayorder' => intval($_GPC['displayorder']),
				'status' => trim($_GPC['status'])
				);

			if (!empty($id)) {
				pdo_update('elapp_shop_system_site_honor', $data, array('id' => $id));
				plog('system.site.honor.edit', '修改荣誉证书 ID: ' . $id);
			}else {
				$data['createtime'] = TIMESTAMP;
				pdo_insert('elapp_shop_system_site_honor', $data);
				$id = pdo_insertid();
				plog('system.site.honor.add', '添加荣誉证书 ID: ' . $id);
			}
			show_json(1);
		}else {
			$item = pdo_fetch('select * from ' . tablename('elapp_shop_system_site_honor') . ' where id=:id limit 1', array(':id' => $id));
			include $this->template();
		}
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,title FROM ' . tablename('elapp_shop_system_site_honor') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_delete('elapp_shop_system_site_honor', array('id' => $item['id']));
			plog('system.site.honor.delete', '删除荣誉证书 ID: ' . $item['id'] . ' 标题: ' . $item['title'] . ' ');
		}
		show_json(1, array('url' => referer()));
	}

	public function displayorder(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$displayorder = intval($_GPC['value']);
		$item = pdo_fetchall('SELECT id,title FROM ' . tablename('elapp_shop_system_site_honor') . ' WHERE id in( ' . $id . ' )');

		if (!empty($item)) {
			pdo_update('elapp_shop_system_site_honor', array('displayorder' => $displayorder), array('id' => $id));
			plog('system.site.honor.delete', '修改荣誉证书排序 ID: ' . $item['id'] . ' 标题: ' . $item['title'] . ' 排序: ' . $displayorder . ' ');
		}
		show_json(1);
	}

	public function status(){
		global $_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,title FROM ' . tablename('elapp_shop_system_site_honor') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_update('elapp_shop_system_site_honor', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('system.site.honor.edit', ('修改荣誉证书状态<br/>ID: ' . $item['id'] . '<br/>标题: ' . $item['title'] . '<br/>状态: ' . $_GPC['enabled']) == 1 ? '显示' : '隐藏');
		}
		show_json(1, array('url' => referer()));
	}
}