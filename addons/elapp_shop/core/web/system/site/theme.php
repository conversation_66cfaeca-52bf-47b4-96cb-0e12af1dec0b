<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class ThemeController extends SystemPage{
	public function main()	{
		global $_W,$_GPC;
		if (checksubmit()) {
			$post_data['uniacid'] = 0;
			$post_data['mid'] = 1;
			$post_data['theme_color'] = $_GPC['theme_color'];
			$post_data['bottom_color'] = $_GPC['bottom_color'];
			$post_data['ad_color'] = $_GPC['ad_color'];
			$post_data['trans_color'] = $_GPC['trans_color'];
			$post_data['button_color'] = $_GPC['button_color'];
			$post_data['h_button_color'] = $_GPC['h_button_color'];
			$post_data['banner_color'] = $_GPC['banner_color'];
			$post_data['four_img'] = $_GPC['four_img'];
			$post_data['bottom_img'] = $_GPC['bottom_img'];
			$post_data['inner_img'] = $_GPC['inner_img'];
			$post_data['login_img'] = $_GPC['login_img'];
			$post_data['kefu_img'] = $_GPC['kefu_img'];
			$post_data['ads_img'] = $_GPC['ads_img'];
			$post_data['adl_img'] = $_GPC['adl_img'];
			$post_data['kefu_link'] = $_GPC['kefu_link'];
			$post_data['adl_link'] = $_GPC['adl_link'];
			$post_data['adp_img'] = $_GPC['adp_img'];
			$post_data['large_img'] = $_GPC['large_img'];
			$post_data['button_img'] = $_GPC['button_img'];
			$post_data['large_link'] = $_GPC['large_link'];
			$post_data['login_link'] = $_GPC['login_link'];
			$post_data['register_link'] = $_GPC['register_link'];
			$post_data['email_status'] = $_GPC['email_status'] == 'on' ? 1 : 0;
			$post_data['phone_status'] = $_GPC['phone_status'] == 'on' ? 1 : 0;
			$post_data['kefu_status'] = $_GPC['kefu_status'] == 'on' ? 1 : 0;
			$post_data['login_status'] = $_GPC['login_status'] == 'on' ? 1 : 0;
			$post_data['animate_status'] = $_GPC['animate_status'] == 'on' ? 1 : 0;
			$post_data['link_status'] = $_GPC['link_status'] == 'on' ? 1 : 0;
			$post_data['kefuopen_status'] = $_GPC['kefuopen_status'] == 'on' ? 1 : 0;
			$post_data['banner_status'] = $_GPC['banner_status'] == 'on' ? 1 : 0;
			$post_data['ad_status'] = $_GPC['ad_status'] == 'on' ? 1 : 0;
			$post_data['ad_on'] = $_GPC['ad_on'] == 'on' ? 1 : 0;
			$post_data['kefu_on'] = $_GPC['kefu_on'] == 'on' ? 1 : 0;
			$post_data['other_status'] = $_GPC['other_status'] == 'on' ? 1 : 0;
			$post_data['register_status'] = $_GPC['register_status'] == 'on' ? 1 : 0;
			$post_data['large_status'] = $_GPC['large_status'] == 'on' ? 1 : 0;
			$post_data['return_status'] = $_GPC['return_status'] == 'on' ? 1 : 0;
			$post_data['title1_size'] = $_GPC['title1_size'];
			$post_data['title1_color'] = $_GPC['title1_color'];
			$post_data['mouse1_color'] = $_GPC['mouse1_color'];
			$post_data['title2_size'] = $_GPC['title2_size'];
			$post_data['title2_color'] = $_GPC['title2_color'];
			$post_data['mouse2_color'] = $_GPC['mouse2_color'];
			$post_data['title3_size'] = $_GPC['title3_size'];
			$post_data['title3_color'] = $_GPC['title3_color'];
			$post_data['mouse3_color'] = $_GPC['mouse3_color'];
			$post_data['bg_color'] = $_GPC['bg_color'];
			$is_con = pdo_get('elapp_shop_system_site_theme', array("mid" => 1));
			if ($is_con) {
				$res = pdo_update('elapp_shop_system_site_theme', $post_data, ["mid" => 1]);
				if (!$res) {
					show_json(0, array('message' => '没有任何修改','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			} else {
				$res = pdo_insert('elapp_shop_system_site_theme', $post_data);
				if (!$res) {
					show_json(0, array('message' => '没有任何修改','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			}
		} else {
			$data = pdo_get('elapp_shop_system_site_theme', array("mid" => 1));
			include $this->template();
		}
	}	
}