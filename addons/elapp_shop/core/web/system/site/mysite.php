<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class MysiteController extends SystemPage{
	public $mid;
	public function __construct(){
		global $_W;
		$username = $_W['username'];
		$this->mid = 1;//pdo_getcolumn('elapp_shop_system_site_domain', array("username" => $username), 'id');
		$website = $_SERVER['HTTP_HOST'];
		$web = pdo_get('elapp_shop_system_site_domain', array("domain" => $website, "username" => $_W['username']));
		/* if (!$web) {
			itoast('请先联系创始人绑定用户名和域名', referer(), 'error');
			exit;
		} */		
	}

	/**
	 * 添加或者修改
	 * @return void
	 */
	public function add(){
		global $_GPC, $_W;
		if(checksubmit()){
			$domain = pdo_get('elapp_shop_system_site_domain',array('uniacid'=>0,'domain'=>$_GPC['domain']));
			if($domain){
			   itoast("域名已存在，请不要重复添加", referer(),'error');exit;
			}
			$post_data['uniacid']      = 0;
			$post_data['mid']      	   = $this->mid;
			$post_data['domain']       = $_GPC['domain'];       // 域名
			$post_data['remark']       = $_GPC['remark'];			// 站点备注
			if($_GPC['username']){
			   $post_data['disanfang']      = '';               // 用户名和模块2选1
			   $post_data['username']      = $_GPC['username'];   // 用户名和模块2选1
			}else{
			   $post_data['disanfang']      = $_GPC['disanfang'];   // 用户名和模块2选1
			   $post_data['username']      = '';               // 用户名和模块2选1
			   $p = pdo_get("modules", array("name" => $post_data['disanfang']));
			   $post_data['title']       = $p['title'];// 模块名称
			}
			$res = pdo_insert('elapp_shop_system_site_domain',$post_data);
			if(!$res){
			   show_json(0, array('message' => '添加失败','url' => referer()));
			}else{
				$mid = pdo_insertid();
				$mdata = array(
					'mid' => $this->mid,
					'uniacid'	=> 0
				);
				pdo_insert('elapp_shop_system_site_setup',$mdata);
				pdo_insert('elapp_shop_system_site_theme',$mdata);
				show_json(1, array('url' => referer()));
			}
		 }else{
			$data = pdo_get('elapp_shop_system_site_domain',array('uniacid'=>0,'id'=>$_GPC['site_id']));
			$modules = pdo_getall("modules", array("welcome_support" => 2));
			include $this->template('system/site/mysite/site_add');
		 }

	}

	// 显示列表
	public function list(){
		global $_GPC, $_W;
		//echo '<pre>';
		// var_dump($_W);exit;
		// echo 1;
		$i = 1;
		$data = pdo_getall('elapp_shop_system_site_domain',array('uniacid'=>0));
		include $this->template('system/site/mysite/site_list');
	}

	// 删除操作

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,domain FROM ' . tablename('elapp_shop_system_site_domain') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_delete('elapp_shop_system_site_domain', array('id' => $item['id']));
			plog('system.site.mysite.delete', '删除 ID: ' . $item['id'] . ' 标题: ' . $item['domain'] . ' ');
		}
		show_json(1, array('url' => referer()));
	}

	public function updateDomain(){
		global $_GPC, $_W;
		$id = $_GPC['update_id'];
		$val = $_GPC['update_val'];
		$res = pdo_update('elapp_shop_system_site_domain',['domain'=>$val],['id'=>$id,'uniacid'=>0]);
		if($res){
			iajax(0, '成功');
		}else{
			iajax(-1, '失败');
		}
	}
}