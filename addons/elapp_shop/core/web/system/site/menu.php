<?php
namespace web\controller\system\site;
use web\controller\SystemPage;
class <PERSON>u<PERSON>ontroller extends SystemPage{
	public $mid;
	public function __construct(){
		global $_W,$_GPC;
		$username = $_W['username'];
		$this->mid = 1;//pdo_getcolumn('elapp_shop_system_site_domain', array("username" => $username), 'id');
		$website = $_SERVER['HTTP_HOST'];
		$web = pdo_get('elapp_shop_system_site_domain', array("domain" => $website, "username" => $_W['username']));
		/* if (!$web) {
			itoast('请先联系创始人绑定用户名和域名', referer(), 'error');
			exit;
		} */		
	}	
	public function main(){
		global $_W,$_GPC;
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
		$condition = '';
		$params = array();

		if ($_GPC['status'] != '') {
			$condition .= ' and status=' . intval($_GPC['status']);
		}
		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and title like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}
		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_menu') . ' WHERE 1 ' . $condition . '  ORDER BY displayorder DESC limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);
		$total = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_system_site_menu') . ' WHERE 1 ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add(){
		$this->post();
	}

	public function edit(){
		$this->post();
	}

	protected function post(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);

		if ($_W['ispost']) {
			empty($_GPC['menu']) && show_json(0, array('message' => '菜单标题不能为空', 'url' => referer()));
			empty($_GPC['url']) && show_json(0, array('message' => '菜单链接不能为空', 'url' => referer()));
			$data = array(
				'uniacid' => trim($_GPC['uniacid']),
				'mid' => trim($_GPC['mid']),
				'menu' => trim($_GPC['menu']),
				'url' => trim($_GPC['url']),
				'sort' => trim($_GPC['sort']),
				'remark' => trim($_GPC['remark']),
				'displayorder' => intval($_GPC['displayorder']),
				'status' => trim($_GPC['status'])
				);

			if (!empty($id)) {
				pdo_update('elapp_shop_system_site_menu', $data, array('id' => $id));
				plog('system.site.menu.edit', '修改菜单 ID: ' . $id);
			}else {
				$data['createtime'] = TIMESTAMP;
				pdo_insert('elapp_shop_system_site_menu', $data);
				$id = pdo_insertid();
				plog('system.site.menu.add', '添加菜单 ID: ' . $id);
			}
			show_json(1);
		}else {
			$item = pdo_fetch('select * from ' . tablename('elapp_shop_system_site_menu') . ' where id=:id limit 1', array(':id' => $id,"mid" => $this->mid));
			include $this->template();
		}
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,menu FROM ' . tablename('elapp_shop_system_site_menu') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_delete('elapp_shop_system_site_menu', array('id' => $item['id']));
			plog('system.site.menu.delete', '删除菜单 ID: ' . $item['id'] . ' 标题: ' . $item['menu'] . ' ');
		}
		show_json(1, array('url' => referer()));
	}

	public function displayorder(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$displayorder = intval($_GPC['value']);
		$item = pdo_fetchall('SELECT id,menu FROM ' . tablename('elapp_shop_system_site_menu') . ' WHERE id in( ' . $id . ' )');

		if (!empty($item)) {
			pdo_update('elapp_shop_system_site_menu', array('displayorder' => $displayorder), array('id' => $id));
			plog('system.site.menu.delete', '修改菜单排序 ID: ' . $item['id'] . ' 标题: ' . $item['menu'] . ' 排序: ' . $displayorder . ' ');
		}
		show_json(1);
	}

	public function status(){
		global $_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,menu FROM ' . tablename('elapp_shop_system_site_menu') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_update('elapp_shop_system_site_menu', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('system.site.menu.edit', ('修改菜单状态<br/>ID: ' . $item['id'] . '<br/>标题: ' . $item['menu'] . '<br/>状态: ' . $_GPC['enabled']) == 1 ? '显示' : '隐藏');
		}
		show_json(1, array('url' => referer()));
	}
}