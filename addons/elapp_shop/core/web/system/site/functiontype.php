<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class FunctiontypeController extends SystemPage{
	public function main()	{
		global $_W,$_GPC;
		if (!empty($_GPC['catid'])) {
			foreach ($_GPC['catid'] as $k => $v) {
				$data = array(
					'name' => trim($_GPC['catname'][$k]),
					'price' => trim($_GPC['price'][$k]),
					'year' => trim($_GPC['year'][$k]),
					'img' => trim($_GPC['img'][$k]),
					'contact' => trim($_GPC['contact'][$k]),
					'contact_link' => trim($_GPC['contact_link'][$k]),
					'displayorder' => $k,
					'status' => intval($_GPC['status'][$k])
					);

				if (empty($v)) {
					pdo_insert('elapp_shop_system_site_function_type', $data);
					$insert_id = pdo_insertid();
					plog('system.functiontype.add', '添加套餐分类 ID: ' . $insert_id);
				}
				else {
					pdo_update('elapp_shop_system_site_function_type', $data, array('id' => $v));
					plog('system.functiontype.edit', '修改套餐分类 ID: ' . $v);
				}
			}
			plog('system.functiontype.edit', '批量修改套餐分类');
			show_json(1);
		}

		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_function_type') . ' ORDER BY displayorder asc');
		include $this->template('system/site/function/function_type');
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$item = pdo_fetch('SELECT id,name FROM ' . tablename('elapp_shop_system_site_function_type') . ' WHERE id = :id', array(':id' => $id));
		if (!empty($item)) {
			pdo_delete('elapp_shop_system_site_function_type', array('id' => $id));
			plog('system.functiontype.delete', '删除套餐分类 ID: ' . $id . ' 标题: ' . $item['name'] . ' ');
		}
		show_json(1);
	}
}