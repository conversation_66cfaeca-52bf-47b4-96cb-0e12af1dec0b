<?php
namespace web\controller\system\site;
use web\controller\SystemPage;
class GuestbookController extends SystemPage{
	public function main(){
		global $_W,$_GPC;

		if (!empty($_GPC['catid'])) {
			foreach ($_GPC['catid'] as $k => $v) {
				$data = array('name' => trim($_GPC['catname'][$k]), 'displayorder' => $k, 'status' => intval($_GPC['status'][$k]));

				if (empty($v)) {
					pdo_insert('elapp_shop_system_site_guestbook', $data);
					$insert_id = pdo_insertid();
					plog('system.guestbook.add', '添加分类 ID: ' . $insert_id);
				}else {
					pdo_update('elapp_shop_system_site_guestbook', $data, array('id' => $v));
					plog('system.guestbook.edit', '修改分类 ID: ' . $v);
				}
			}
			plog('system.guestbook.edit', '批量修改分类');
			show_json(1);
		}
		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_guestbook') . ' ORDER BY id desc');
		include $this->template();
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$item = pdo_fetch('SELECT id,title FROM ' . tablename('elapp_shop_system_site_guestbook') . ' WHERE id = :id', array(':id' => $id));
		if (!empty($item)) {
			pdo_delete('elapp_shop_system_site_guestbook', array('id' => $id));
			plog('system.guestbook.delete', '删除留言 ID: ' . $id . ' 标题: ' . $item['title'] . ' ');
		}
		show_json(1);
	}

	public function view(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$item = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_system_site_guestbook') . ' WHERE id = :id', array(':id' => $id));
		include $this->template();
	}
}