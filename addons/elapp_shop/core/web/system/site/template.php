<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class Template<PERSON>ontroller extends SystemPage{
	public $mid;
	public function __construct(){
		global $_W;
		$username = $_W['username'];
		$this->mid = 1;//pdo_getcolumn('elapp_shop_system_site_domain', array("username" => $username), 'id');
		$website = $_SERVER['HTTP_HOST'];
		$web = pdo_get('elapp_shop_system_site_domain', array("domain" => $website, "username" => $_W['username']));
		/* if (!$web) {
			itoast('请先联系创始人绑定用户名和域名', referer(), 'error');
			exit;
		} */		
	}
	public function main(){
		global $_GPC, $_W;
		$template_path = $_W['siteroot'] . 'pcsite/static/admin/images/template';
		$op = $_GPC['op'];
		if ($op == 'change') {
			$post_data = ["template_ident" => $_GPC['ident']];
			$res = pdo_update('elapp_shop_system_site_setup', $post_data, ["uniacid" => 0, "mid" => $this->mid]);
			if (!$res) {
				iajax(-1, '切换模板失败');
			} else {
				iajax(0, '切换模板成功');
			}
		} else {
			$template_list = pdo_getall('elapp_shop_system_site_template', array("uniacid" => 0));
			$ident = pdo_getcolumn('elapp_shop_system_site_setup', array("uniacid" => 0, "mid" => $this->mid), 'template_ident');
			include $this->template();
		}
	}
}
