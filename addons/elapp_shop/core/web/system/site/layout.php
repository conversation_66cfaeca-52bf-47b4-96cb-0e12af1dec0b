<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class LayoutController extends SystemPage{
	public function main(){
		global $_W,$_GPC;
		$template_path = $_W["siteroot"] . "pcsite/static/images/model/";
		if (checksubmit()) {
			$post_data["uniacid"] = 0;
			$post_data["mid"] = 1;//$this->mid;
			$post_data["index1"] = $_GPC["index1"];
			$post_data["index2"] = $_GPC["index2"];
			$post_data["index3"] = $_GPC["index3"];
			$post_data["index4"] = $_GPC["index4"];
			$post_data["index5"] = $_GPC["index5"];
			$post_data["index6"] = $_GPC["index6"];
			$post_data["index7"] = $_GPC["index7"];
			$post_data["index8"] = $_GPC["index8"];
			$post_data["index9"] = $_GPC["index9"];
			$post_data["index10"] = $_GPC["index10"];
			$post_data["index11"] = $_GPC["index11"];
			$post_data["index12"] = $_GPC["index12"];
			$post_data["index13"] = $_GPC["index13"];
			$post_data["index14"] = $_GPC["index14"];
			$post_data["app"] = $_GPC["app"];
			$post_data["news"] = $_GPC["news"];
			$post_data["case"] = $_GPC["case"];
			$is_con = pdo_get("elapp_shop_system_site_diy_set", array("uniacid" => 0, "mid" => 1));
			if ($is_con) {
				$res = pdo_update("elapp_shop_system_site_diy_set", $post_data, ["uniacid" => 0, "mid" => 1]);//$this->mid
				if (!$res) {
					show_json(-1, array('message' => '没有任何修改','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			} else {
				$res = pdo_insert("elapp_shop_system_site_diy_set", $post_data);
				if (!$res) {
					show_json(0, array('message' => '没有任何修改','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			}
		} else {
			$cate_list = pdo_getall("elapp_shop_system_site_diy_cate");
			$column_list = pdo_getall("elapp_shop_system_site_diy_column", array("status" => 1));
			$list = [];
			foreach ($cate_list as $k => $v) {
				$list[$k]["name"] = $v["name"];
				$list[$k]["field"] = $v["field"];
				foreach ($column_list as $key => $val) {
					if ($v["id"] == $val["cate_id"]) {
						$list[$k]["column"][] = $val;
					}
				}
			}
			$diy_set = pdo_get("elapp_shop_system_site_diy_set", array("uniacid" => 0, "mid" => 1));
			include $this->template();
		}
	}	
}
