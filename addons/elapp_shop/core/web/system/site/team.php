<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class TeamController extends SystemPage{
	public function main(){
		global $_W,$_GPC;
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
		$condition = '';
		$params = array();
		if ($_GPC['status'] != '') {
			$condition .= ' and status=' . intval($_GPC['status']);
		}
		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and name like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}
		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_team') . ' WHERE 1 ' . $condition . '  ORDER BY displayorder DESC limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);
		$total = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_system_site_team') . ' WHERE 1 ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add(){
		$this->post();
	}

	public function edit(){
		$this->post();
	}

	protected function post(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if ($_W['ispost']) {
			empty($_GPC['name']) && show_json(0, array('message' => '名称不能为空', 'url' => referer()));
			empty($_GPC['thumb']) && show_json(0, array('message' => '头像不能为空', 'url' => referer()));
			$data = array(
				'uniacid' => trim($_GPC['uniacid']),
				'mid' => trim($_GPC['mid']),
				'name' => trim($_GPC['name']),
				'background' => trim($_GPC['background']),
				'thumb' => trim($_GPC['thumb']),
				'small_thumb' => trim($_GPC['small_thumb']),				
				'info' => trim($_GPC['info']),
				'motto' => trim($_GPC['motto']),
				'position' => trim($_GPC['position']),
				'displayorder' => intval($_GPC['displayorder']),
				'status' => trim($_GPC['status'])
				);

			if (!empty($id)) {
				pdo_update('elapp_shop_system_site_team', $data, array('id' => $id));
				plog('system.site.team.edit', '修改团队风采 ID: ' . $id);
			}else {
				$data['createtime'] = TIMESTAMP;
				pdo_insert('elapp_shop_system_site_team', $data);
				$id = pdo_insertid();
				plog('system.site.team.add', '添加团队风采 ID: ' . $id);
			}
			show_json(1);
		}else {
			$item = pdo_fetch('select * from ' . tablename('elapp_shop_system_site_team') . ' where id=:id limit 1', array(':id' => $id));
			include $this->template();
		}
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_system_site_team') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_delete('elapp_shop_system_site_team', array('id' => $item['id']));
			plog('system.site.team.delete', '删除团队风采 ID: ' . $item['id'] . ' 标题: ' . $item['name'] . ' ');
		}
		show_json(1, array('url' => referer()));
	}

	public function displayorder(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$displayorder = intval($_GPC['value']);
		$item = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_system_site_team') . ' WHERE id in( ' . $id . ' )');
		if (!empty($item)) {
			pdo_update('elapp_shop_system_site_team', array('displayorder' => $displayorder), array('id' => $id));
			plog('system.site.team.delete', '修改团队风采排序 ID: ' . $item['id'] . ' 标题: ' . $item['name'] . ' 排序: ' . $displayorder . ' ');
		}
		show_json(1);
	}

	public function status(){
		global $_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_system_site_team') . ' WHERE id in( ' . $id . ' )');
		foreach ($items as $item) {
			pdo_update('elapp_shop_system_site_team', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('system.site.team.edit', ('修改团队风采状态<br/>ID: ' . $item['id'] . '<br/>标题: ' . $item['name'] . '<br/>状态: ' . $_GPC['enabled']) == 1 ? '显示' : '隐藏');
		}
		show_json(1, array('url' => referer()));
	}
}