<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class NewstypeController extends SystemPage{
	public function main(){
		global $_W,$_GPC;

		if (!empty($_GPC['catid'])) {
			foreach ($_GPC['catid'] as $k => $v) {
				$data = array(
				'name' => trim($_GPC['catname'][$k]),
				'thumb' => trim($_GPC['thumb'][$k]),
				'displayorder' => $k,
				'status' => intval($_GPC['status'][$k]));
				if (empty($v)) {
					pdo_insert('elapp_shop_system_site_news_type', $data);
					$insert_id = pdo_insertid();
					plog('system.newstype.add', '添加分类 ID: ' . $insert_id);
				}else {
					pdo_update('elapp_shop_system_site_news_type', $data, array('id' => $v));
					plog('system.newstype.edit', '修改分类 ID: ' . $v);
				}
			}
			plog('system.newstype.edit', '批量修改分类');
			show_json(1);
		}

		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_system_site_news_type') . ' ORDER BY displayorder asc');
		include $this->template('system/site/news/news_type');
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$item = pdo_fetch('SELECT id,name FROM ' . tablename('elapp_shop_system_site_news_type') . ' WHERE id = :id', array(':id' => $id));
		if (!empty($item)) {
			pdo_delete('elapp_shop_system_site_news_type', array('id' => $id));
			plog('system.newstype.delete', '删除分类 ID: ' . $id . ' 标题: ' . $item['name'] . ' ');
		}
		show_json(1);
	}
}