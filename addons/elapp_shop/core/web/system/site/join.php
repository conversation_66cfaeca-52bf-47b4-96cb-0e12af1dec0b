<?php
namespace web\controller\system\site;
use web\controller\SystemPage;
class <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> extends SystemPage{
	protected $type = 'set';
	public function main(){
		global $_W,$_GPC;
		if ($_W['ispost']) {
			$data = array();
			$data['type'] = $this->type;
			
			$_GPC['data']['img'] = save_media($_GPC['data']['img']);
			$data['content'] = iserializer($_GPC['data']);
			$res = pdo_fetch('select id from ' . tablename('elapp_shop_system_site_join') . ' where `type`=:type', array(':type' => $this->type));
			if (empty($res)) {
				$ok = pdo_insert('elapp_shop_system_site_join', $data);
				$ok ? show_json(1) : show_json(0);
			}else {
				$ok = pdo_update('elapp_shop_system_site_join', $data, array('id' => $res['id']));
				show_json(1);
			}
		}

		$styles = array();
		$dir = IA_ROOT . '/pcsite/template';

		if ($handle = @opendir($dir)) {
			while (($file = readdir($handle)) !== false) {
				if (($file != '..') && ($file != '.')) {
					if (is_dir($dir . '/' . $file)) {
						$styles[] = $file;
					}
				}
			}
			closedir($handle);
		}

		$res = pdo_fetch('select * from ' . tablename('elapp_shop_system_site_join') . ' where `type`=:type', array(':type' => $this->type));
		if (!empty($res['content']) && !is_array($res['content'])) {
			if (strexists($res['content'], '{"')) {
				$data = json_decode($res['content'], true);
			}
			else {
				$data = unserialize($res['content']);
			}
		}
		include $this->template();
	}
}