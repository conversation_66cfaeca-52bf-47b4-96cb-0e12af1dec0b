<?php
namespace web\controller\system\site;
use web\controller\SystemPage;

class HomecontrolController extends SystemPage{
	public $mid;
	public function __construct(){
		global $_W;
		$username = $_W['username'];
		$this->mid = 1;//pdo_getcolumn('elapp_shop_system_site_domain', array("username" => $username), 'id');
		$website = $_SERVER['HTTP_HOST'];
		$web = pdo_get('elapp_shop_system_site_domain', array("domain" => $website, "username" => $_W['username']));
		/* if (!$web) {
			itoast('请先联系创始人绑定用户名和域名', referer(), 'error');
			exit;
		} */		
	}
	public function main(){
		global $_GPC, $_W;
		if (checksubmit()) {
			$post_data['uniacid'] = 0;
			$post_data['mid'] = $this->mid;
			$post_data['title1'] = $_GPC['title1'];
			$post_data['title2'] = $_GPC['title2'];
			$post_data['title3'] = $_GPC['title3'];
			$post_data['title4'] = $_GPC['title4'];
			$post_data['title5'] = $_GPC['title5'];
			$post_data['title6'] = $_GPC['title6'];
			$post_data['title7'] = $_GPC['title7'];
			$post_data['title8'] = $_GPC['title8'];
			$post_data['title9'] = $_GPC['title9'];
			$post_data['title10'] = $_GPC['title10'];
			$post_data['title11'] = $_GPC['title11'];
			$post_data['title12'] = $_GPC['title12'];
			$post_data['title13'] = $_GPC['title13'];
			$post_data['title14'] = $_GPC['title14'];
			$post_data['title16'] = $_GPC['title16'];
			$post_data['title17'] = $_GPC['title17'];
			$post_data['title18'] = $_GPC['title18'];
			$post_data['title19'] = $_GPC['title19'];
			$post_data['title20'] = $_GPC['title20'];
			$post_data['sketch1'] = $_GPC['sketch1'];
			$post_data['sketch2'] = $_GPC['sketch2'];
			$post_data['sketch3'] = $_GPC['sketch3'];
			$post_data['sketch4'] = $_GPC['sketch4'];
			$post_data['sketch5'] = $_GPC['sketch5'];
			$post_data['sketch6'] = $_GPC['sketch6'];
			$post_data['sketch8'] = $_GPC['sketch8'];
			$post_data['sketch9'] = $_GPC['sketch9'];
			$post_data['sketch10'] = $_GPC['sketch10'];
			$post_data['sketch11'] = $_GPC['sketch11'];
			$post_data['sketch12'] = $_GPC['sketch12'];
			$post_data['sketch13'] = $_GPC['sketch13'];
			$post_data['sketch14'] = $_GPC['sketch14'];
			$post_data['sketch15'] = $_GPC['sketch15'];
			$post_data['sketch16'] = $_GPC['sketch16'];
			$post_data['sketch17'] = $_GPC['sketch17'];
			$post_data['sketch18'] = $_GPC['sketch18'];
			$post_data['sketch19'] = $_GPC['sketch19'];
			$post_data['sketch20'] = $_GPC['sketch20'];
			$post_data['sketch21'] = $_GPC['sketch21'];
			$post_data['sketch22'] = $_GPC['sketch22'];
			$post_data['sketch23'] = $_GPC['sketch23'];
			$post_data['poster1'] = $_GPC['poster1'];
			$post_data['poster2'] = $_GPC['poster2'];
			$post_data['poster3'] = $_GPC['poster3'];
			$post_data['poster4'] = $_GPC['poster4'];
			$post_data['ad_title1'] = $_GPC['ad_title1'];
			$post_data['ad_title2'] = $_GPC['ad_title2'];
			$post_data['ad_title3'] = $_GPC['ad_title3'];
			$post_data['ad_title4'] = $_GPC['ad_title4'];
			$post_data['ad_img1'] = $_GPC['ad_img1'];
			$post_data['ad_img2'] = $_GPC['ad_img2'];
			$post_data['ad_img3'] = $_GPC['ad_img3'];
			$post_data['ad_img4'] = $_GPC['ad_img4'];
			$post_data['describe'] = $_GPC['describe'];
			$post_data['name1'] = $_GPC['name1'];
			$post_data['name2'] = $_GPC['name2'];
			$post_data['name3'] = $_GPC['name3'];
			$post_data['name4'] = $_GPC['name4'];
			$post_data['name5'] = $_GPC['name5'];
			$post_data['name6'] = $_GPC['name6'];
			$post_data['name7'] = $_GPC['name7'];
			$post_data['name8'] = $_GPC['name8'];
			$post_data['name9'] = $_GPC['name9'];
			$post_data['name10'] = $_GPC['name10'];
			$post_data['name11'] = $_GPC['name11'];
			$post_data['name12'] = $_GPC['name12'];
			$post_data['status1'] = $_GPC['status1'] == 'on' ? 1 : 0;
			$post_data['status2'] = $_GPC['status2'] == 'on' ? 1 : 0;
			$post_data['status3'] = $_GPC['status3'] == 'on' ? 1 : 0;
			$post_data['status4'] = $_GPC['status4'] == 'on' ? 1 : 0;
			$post_data['status5'] = $_GPC['status5'] == 'on' ? 1 : 0;
			$post_data['status6'] = $_GPC['status6'] == 'on' ? 1 : 0;
			$post_data['status7'] = $_GPC['status7'] == 'on' ? 1 : 0;
			$post_data['status8'] = $_GPC['status8'] == 'on' ? 1 : 0;
			$post_data['status9'] = $_GPC['status9'] == 'on' ? 1 : 0;
			$post_data['status10'] = $_GPC['status10'] == 'on' ? 1 : 0;
			$post_data['status11'] = $_GPC['status11'] == 'on' ? 1 : 0;
			$post_data['status12'] = $_GPC['status12'] == 'on' ? 1 : 0;
			$post_data['status13'] = $_GPC['status13'] == 'on' ? 1 : 0;
			$post_data['status14'] = $_GPC['status14'] == 'on' ? 1 : 0;
			$post_data['status15'] = $_GPC['status15'] == 'on' ? 1 : 0;
			$post_data['status16'] = $_GPC['status16'] == 'on' ? 1 : 0;
			$post_data['status17'] = $_GPC['status17'] == 'on' ? 1 : 0;
			$post_data['status18'] = $_GPC['status18'] == 'on' ? 1 : 0;
			$post_data['status19'] = $_GPC['status19'] == 'on' ? 1 : 0;
			$post_data['status20'] = $_GPC['status20'] == 'on' ? 1 : 0;
			$post_data['status21'] = $_GPC['status21'] == 'on' ? 1 : 0;
			$post_data['status22'] = $_GPC['status22'] == 'on' ? 1 : 0;
			$post_data['mark_name1'] = $_GPC['mark_name1'];
			$post_data['mark_name2'] = $_GPC['mark_name2'];
			$post_data['mark_name3'] = $_GPC['mark_name3'];
			$post_data['mark_name4'] = $_GPC['mark_name4'];
			$post_data['mark_name5'] = $_GPC['mark_name5'];
			$post_data['mark_name6'] = $_GPC['mark_name6'];
			$post_data['mark_name7'] = $_GPC['mark_name7'];
			$post_data['mark_name8'] = $_GPC['mark_name8'];
			$post_data['mark_name9'] = $_GPC['mark_name9'];
			$post_data['img'] = $_GPC['img'];
			$post_data['content'] = $_GPC['content'];
			$post_data['p_img1'] = $_GPC['p_img1'];
			$post_data['p_img2'] = $_GPC['p_img2'];
			$post_data['p_img3'] = $_GPC['p_img3'];
			$post_data['p_img4'] = $_GPC['p_img4'];
			$post_data['p_title1'] = $_GPC['p_title1'];
			$post_data['p_title2'] = $_GPC['p_title2'];
			$post_data['p_title3'] = $_GPC['p_title3'];
			$post_data['p_title4'] = $_GPC['p_title4'];
			$post_data['synopsis1'] = $_GPC['synopsis1'];
			$post_data['synopsis2'] = $_GPC['synopsis2'];
			$post_data['synopsis3'] = $_GPC['synopsis3'];
			$post_data['synopsis4'] = $_GPC['synopsis4'];
			$post_data['poster1_link'] = $_GPC['poster1_link'];
			$post_data['poster2_link'] = $_GPC['poster2_link'];
			$post_data['poster3_link'] = $_GPC['poster3_link'];
			$post_data['poster4_link'] = $_GPC['poster4_link'];
			$post_data['app_bg'] = $_GPC['app_bg'];
			$post_data['service_bg'] = $_GPC['service_bg'];
			$post_data['case_bg'] = $_GPC['case_bg'];
			$post_data['news_bg'] = $_GPC['news_bg'];
			$post_data['func_bg'] = $_GPC['func_bg'];
			$post_data['help_bg'] = $_GPC['help_bg'];
			$post_data['join_bg'] = $_GPC['join_bg'];
			$post_data['about_bg'] = $_GPC['about_bg'];
			$post_data['contact_bg'] = $_GPC['contact_bg'];
			$post_data['app1_bg'] = $_GPC['app1_bg'];
			$post_data['service1_bg'] = $_GPC['service1_bg'];
			$post_data['case1_bg'] = $_GPC['case1_bg'];
			$post_data['news1_bg'] = $_GPC['news1_bg'];
			$post_data['func1_bg'] = $_GPC['func1_bg'];
			$post_data['help1_bg'] = $_GPC['help1_bg'];
			$post_data['join1_bg'] = $_GPC['join1_bg'];
			$post_data['about1_bg'] = $_GPC['about1_bg'];
			$post_data['contact1_bg'] = $_GPC['contact1_bg'];
			$post_data['notice1_bg'] = $_GPC['notice1_bg'];
			$post_data['advantage1_bg'] = $_GPC['advantage1_bg'];
			$post_data['team1_bg'] = $_GPC['team1_bg'];
			$post_data['profit1_bg'] = $_GPC['profit1_bg'];
			$post_data['customer1_bg'] = $_GPC['customer1_bg'];
			$post_data['link1_bg'] = $_GPC['link1_bg'];
			$post_data['four1_bg'] = $_GPC['four1_bg'];
			$post_data['product1_bg'] = $_GPC['product1_bg'];
			$post_data['bottom_img1'] = $_GPC['bottom_img1'];
			$post_data['bottom_img2'] = $_GPC['bottom_img2'];
			$post_data['bottom_img3'] = $_GPC['bottom_img3'];
			$post_data['bottom_img4'] = $_GPC['bottom_img4'];
			$post_data['bottom_sketch1'] = $_GPC['bottom_sketch1'];
			$post_data['bottom_sketch2'] = $_GPC['bottom_sketch2'];
			$post_data['bottom_sketch3'] = $_GPC['bottom_sketch3'];
			$post_data['bottom_sketch4'] = $_GPC['bottom_sketch4'];
			$is_con = pdo_get('elapp_shop_system_site_homecontrol', array("mid" => $this->mid));
			if ($is_con) {
				$res = pdo_update('elapp_shop_system_site_homecontrol', $post_data, ["mid" => $this->mid]);
				if (!$res) {
					show_json(0, array('message' => '修改失败','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			} else {
				$res = pdo_insert('elapp_shop_system_site_homecontrol', $post_data);
				if (!$res) {
					show_json(0, array('message' => '添加失败','url' => referer()));
				} else {
					show_json(1, array('url' => referer()));
				}
			}
		} else {
			$data = pdo_get('elapp_shop_system_site_homecontrol', array("mid" => $this->mid));
			include $this->template();
		}
	}
}