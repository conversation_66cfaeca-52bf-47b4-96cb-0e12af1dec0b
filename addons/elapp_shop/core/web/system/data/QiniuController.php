<?php
namespace web\controller\system\data;
use web\controller\SystemPage;
class QiniuController extends SystemPage{
	public function main(){
		global $_W,$_GPC;
		$path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/global';

		if (!is_dir($path)) {
			load()->func('file');
			mkdirs($path);
		}
		if ($_W['ispost']) {
			$data = (is_array($_GPC['data']) ? $_GPC['data'] : array());
			if ($data['upload']) {
				$check = com('qiniu')->save('static/application/shop/images/nopic100.jpg', $data);
				if (is_array($check) && is_error($check)) {
					show_json(0, '保存失败: ' . $check['message']);
				}
			}
			m('cache')->set('qiniu', $data, 'global');
			$data_authcode = authcode(json_encode($data), 'ENCODE', 'global');
			file_put_contents($path . '/qiniu.cache', $data_authcode);
			show_json(1);
		}
		$data = m('cache')->getArray('qiniu', 'global');
		if (empty($data['upload']) && is_file($path . '/qiniu.cache')) {
			$data_authcode = authcode(file_get_contents($path . '/qiniu.cache'), 'DECODE', 'global');
			$data = json_decode($data_authcode, true);
		}
		include $this->template();
	}
}