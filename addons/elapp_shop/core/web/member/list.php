<?php
namespace web\controller\member;
use app\com\logic\MemberCancelLogic;
use app\com\validate\MemberValidate;
use app\model\FilterModel;
use app\model\SettleModel;
use app\model\SettleWithdrawApplyModel;
use app\plugin\clerk\core\enum\ClerkRegWayEnum;
use app\plugin\clerk\core\logic\ClerkLevelLogic;
use web\controller\WebPage;
class ListController extends WebPage {

    function main($index=1) {
        global $_W, $_GPC;
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        if ($_GPC['export'] == 1) {
            $pindex = max(1, intval($index));
            $psize = 200;
        }
        $condition = " and dm.uniacid=:uniacid";
        $params = array(':uniacid' => $_W['uniacid']);
        if (!empty($_GPC['mid'])) {
            $condition.=' and dm.id=:mid';
            $params[':mid'] = intval($_GPC['mid']);
        }
        if (!empty($_GPC['realname'])) {
            $realname = trim($_GPC['realname']);
            // 验证搜索的是用户手机号码还是用户id
            $validateNumber = (new MemberValidate())->validateNumber($realname);
            if ($validateNumber) {
                $condition .= ' and dm.id = :realname';
                $params[':realname'] = $realname;
            } else {
                $condition .= ' and ( dm.realname like :realname or dm.nickname like :realname or dm.mobile like :realname or dm.openid like :realname)';
                $params[':realname'] = '%' . $realname . '%';
            }
        }
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }

        if (!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])) {
            $starttime = strtotime($_GPC['time']['start']);
            $endtime = strtotime($_GPC['time']['end']);
            $condition .= " AND dm.createtime >= :starttime AND dm.createtime <= :endtime ";
            $params[':starttime'] = $starttime;
            $params[':endtime'] = $endtime;
        }
        if ($_GPC['level'] != '') {
            $condition.=' and level=' . intval($_GPC['level']);
        }
        $join = '';
        if ($_GPC['groupid'] != '') {
            $condition.=' and find_in_set('.intval($_GPC['groupid']).',groupid) ';
            $join .= " left join (select * from " . tablename('elapp_shop_member_group_log') . " order by log_id desc limit 1 ) glog on (glog.openid = dm.openid) and glog.group_id = ".(int)$_GPC['groupid'];
        }
        if ($_GPC['followed'] != '') {
            // 0: 未关注;1: 已关注;2:取消关注
            if ($_GPC['followed'] == 2) {
                $condition.=' and f.follow=0 and f.unfollowtime<>0';
            } else if ($_GPC['followed'] == 1) {
                $condition.=' and f.follow=' . intval($_GPC['followed']);
            } else {
                $condition.=' and f.follow=' . intval($_GPC['followed']). ' and f.unfollowtime=0 ';
            }
            $join .= " join " . tablename('mc_mapping_fans') . " f on f.openid=dm.openid";
        }

        if ($_GPC['iscommission'] != ''){
            if ($_GPC['iscommission'] == 0){
                $condition.=' and dm.isagent=0 ';
            }else{
                $condition.=' and dm.isagent=1';
            }
        }
        //虚店店长
        if ($_GPC['isvrshopOwner'] != ''){
            if ($_GPC['isvrshopOwner'] == 0){
                $condition.=' and dm.is_owner=0 ';
            }else{
                $condition.=' and dm.is_owner=1';
            }
        }
        //虚店店员
        if ($_GPC['isvrshopClerk'] != ''){
            if ($_GPC['isvrshopClerk'] == 0){
                $condition.=' and dm.is_clerk=0 ';
            }else{
                $condition.=' and dm.is_clerk=1';
            }
        }
        //医生
        if ($_GPC['isDoctor'] != ''){
            if ($_GPC['isDoctor'] == 0){
                $condition.=' and dm.is_doctor=0 ';
            }else{
                $condition.=' and dm.is_doctor=1';
            }
        }
        //合伙人
        if ($_GPC['isvrshopCopartner'] != ''){
            if ($_GPC['isvrshopCopartner'] == 0){
                $condition.=' and dm.is_copartner=0 ';
            }else{
                $condition.=' and dm.is_copartner=1';
            }
        }

        // 根据当前登录用户角色煮注入数据过滤条件
        $filter =  new FilterModel();
        $filter->injectConditions($condition, $params, FilterModel::TABLE_MEMBER, 'dm');
        if ($_GPC['isblack'] != '') {
            $condition.=' and dm.isblack=' . intval($_GPC['isblack']);
        }
        $sql = "select * ,dm.openid from " . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition}  ORDER BY dm.id DESC,dm.createtime DESC ";

        ini_set('memory_limit','-1');
        $sql.=" limit " . ($pindex - 1) * $psize . ',' . $psize;
        //echo $sql;
        //exit();
        $list = pdo_fetchall($sql, $params);

        $list_group = array();
        $list_level = array();
        $list_agent = array();
        $list_clerk = array();//店员
        $list_doctor = array();//医生
        $list_owner = array();//店长
        $list_copartner = array();//合伙人
        $list_fans = array();
        foreach ($list as $val) {
            $list_group[] = trim($val['groupid'],',');
            $list_level[] = trim($val['level'],',');
            $list_agent[] = trim($val['agentid'],',');
            $list_clerk[] = trim($val['clerk_id'],',');
            $list_doctor[] = trim($val['doctor_id'],',');
            $list_owner[] = trim($val['owner_id'],',');
            $list_copartner[] = trim($val['copartner_id'],',');
            $list_fans[] = trim($val['openid'],',');
        }
        $memberids = array_keys($list);
        isset($list_group) && $list_group = array_values(array_filter($list_group));
        if (!empty($list_group)){
            $res_group = pdo_fetchall("select id,groupname from " . tablename('elapp_shop_member_group') . " where id in (".implode(',',$list_group).")",array(),'id');
        }
        isset($list_level) && $list_level = array_values(array_filter($list_level));
        if (!empty($list_level)){
            $res_level = pdo_fetchall("select id,levelname from " . tablename('elapp_shop_member_level') . " where id in (".implode(',',$list_level).")",array(),'id');
        }
        //分销商
        isset($list_agent) && $list_agent = array_values(array_filter($list_agent));
        if (!empty($list_agent)){
            $res_agent = pdo_fetchall("select id,nickname as agentnickname,avatar as agentavatar from " . tablename('elapp_shop_member') . " where id in (".implode(',',$list_agent).")",array(),'id');
        }
        //店员
        isset($list_clerk) && $list_clerk = array_values(array_filter($list_clerk));
        if (!empty($list_clerk)){
            $res_clerk = pdo_fetchall("select id,nickname as clerknickname,avatar as clerkavatar from " . tablename('elapp_shop_member') . " where id in (".implode(',',$list_clerk).")",array(),'id');
        }
        //医生
        isset($list_doctor) && $list_doctor = array_values(array_filter($list_doctor));
        if (!empty($list_doctor)){
            $res_doctor = pdo_fetchall("select id,nickname as doctornickname,avatar as doctoravatar from " . tablename('elapp_shop_member') . " where id in (".implode(',',$list_doctor).")",array(),'id');
        }
        //店长
        isset($list_owner) && $list_owner = array_values(array_filter($list_owner));
        if (!empty($list_owner)){
            $res_owner = pdo_fetchall("select id,nickname as ownernickname,avatar as owneravatar from " . tablename('elapp_shop_member') . " where id in (".implode(',',$list_owner).")",array(),'id');
        }
        //合伙人
        isset($list_copartner) && $list_copartner = array_values(array_filter($list_copartner));
        if (!empty($list_copartner)){
            $res_copartner = pdo_fetchall("select id,nickname as copartnernickname,avatar as copartneravatar from " . tablename('elapp_shop_member') . " where id in (".implode(',',$list_copartner).")",array(),'id');
        }

        isset($list_fans) && $list_fans = array_values(array_filter($list_fans));
        if (!empty($list_fans)){
            $res_fans = pdo_fetchall("select fanid,openid,follow as followed, unfollowtime from " . tablename('mc_mapping_fans') . " where openid in ('".implode('\',\'',$list_fans)."') and uniacid = :uniacid",array('uniacid'=>$_W['uniacid']),'openid');
        }
        $shop = m('common')->getSysset('shop');
        foreach ($list as &$row) {
            if(isset($row['groupid'])){
                $groupid= explode(',', $row['groupid']);
            }
            $row['groupname'] = '';
            if(is_array($groupid)){
                foreach($groupid as $key=>$value){
                    $row['groupname'] .= $res_group[$value]['groupname'].',';
                }
                $row['groupname'] = substr($row['groupname'],0,-1);
            }
            $row['levelname'] = isset($res_level[$row['level']]) ? $res_level[$row['level']]['levelname'] : '';
            //分销商
            $row['agentnickname'] = isset($res_agent[$row['agentid']]) ? $res_agent[$row['agentid']]['agentnickname'] : '';
            $row['agentnickname'] = str_replace('"', "",$row['agentnickname']);
            $row['agentavatar'] = isset($res_agent[$row['agentid']]) ? $res_agent[$row['agentid']]['agentavatar'] : '';
            //店员
            $row['clerknickname'] = isset($res_clerk[$row['clerk_id']]) ? $res_clerk[$row['clerk_id']]['clerknickname'] : '';
            $row['clerknickname'] = str_replace('"', "",$row['clerknickname']);
            $row['clerkavatar'] = isset($res_clerk[$row['clerk_id']]) ? $res_clerk[$row['clerk_id']]['clerkavatar'] : '';
            //医生
            $row['doctornickname'] = isset($res_doctor[$row['doctor_id']]) ? $res_doctor[$row['doctor_id']]['doctornickname'] : '';
            $row['doctornickname'] = str_replace('"', "",$row['doctornickname']);
            $row['doctoravatar'] = isset($res_doctor[$row['doctor_id']]) ? $res_doctor[$row['doctor_id']]['doctoravatar'] : '';
            //店长
            $row['ownernickname'] = isset($res_owner[$row['owner_id']]) ? $res_owner[$row['owner_id']]['ownernickname'] : '';
            $row['ownernickname'] = str_replace('"', "",$row['ownernickname']);
            $row['owneravatar'] = isset($res_owner[$row['owner_id']]) ? $res_owner[$row['owner_id']]['owneravatar'] : '';
            //合伙人
            $row['copartnernickname'] = isset($res_copartner[$row['copartner_id']]) ? $res_copartner[$row['copartner_id']]['copartnernickname'] : '';
            $row['copartnernickname'] = str_replace('"', "",$row['copartnernickname']);
            $row['copartneravatar'] = isset($res_copartner[$row['copartner_id']]) ? $res_copartner[$row['copartner_id']]['copartneravatar'] : '';

            $row['followed'] = isset($res_fans[$row['openid']]) ? $res_fans[$row['openid']]['followed'] : '';
            $row['unfollowtime'] = isset($res_fans[$row['openid']]) ? $res_fans[$row['openid']]['unfollowtime'] : '';
            $row['fanid'] = isset($res_fans[$row['openid']]) ? $res_fans[$row['openid']]['fanid'] : '';
            $row['levelname'] = empty($row['levelname']) ? (empty($shop['levelname']) ? '普通会员' : $shop['levelname']) : $row['levelname'];
            $ordercountsql = 'select count(*) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3';
            $ordercountparams = array(':uniacid' => $_W['uniacid'], ':openid' => $row['openid']);
            // 注入角色权限过滤条件
            $filter->injectConditions($ordercountsql, $ordercountparams, FilterModel::TABLE_ORDER);
            $row['ordercount'] = pdo_fetchcolumn($ordercountsql, $ordercountparams);
            $row['ordermoney'] = pdo_fetchcolumn('select sum(price) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3', array(':uniacid' => $_W['uniacid'], ':openid' => $row['openid']));
            $row['credit1'] = m('member')->getCredit($row['openid'], 'credit1');
            $row['credit2'] = m('member')->getCredit($row['openid'], 'credit2');

			if (!(empty($row['onmid']))) {
				$row['fx_member'] = m('member')->getMember($row['onmid']);
			}
        }
        unset($row);

        //导出Excel
        if ($_GPC['export'] == '1') {

            $columns = array(
                    array('title' => '昵称', 'field' => 'nickname', 'width' => 12),
                    array('title' => '姓名', 'field' => 'realname', 'width' => 12),
                    array('title' => '手机号', 'field' => 'mobile', 'width' => 12),
                    array('title' => 'openid', 'field' => 'openid', 'width' => 24),
                    array('title' => '会员等级', 'field' => 'levelname', 'width' => 12),
                    array('title' => '会员分组', 'field' => 'groupname', 'width' => 12),
                    array('title' => '注册时间', 'field' => 'createtime', 'width' => 12),
                    array('title' => '积分', 'field' => 'credit1', 'width' => 12),
                    array('title' => '余额', 'field' => 'credit2', 'width' => 12),
                    array('title' => '成交订单数', 'field' => 'ordercount', 'width' => 12),
                    array('title' => '成交总金额', 'field' => 'ordermoney', 'width' => 12),
                    array('title' => '备注', 'field' => 'remark', 'width' => 24)
            );
            plog('member.list', '导出会员数据');
            foreach ($list as &$row) {
                $row['createtime'] = date('Y-m-d H:i', $row['createtime']);
                $row['groupname'] = empty($row['groupname']) ? '无分组' : $row['groupname'];
                $row['levelname'] = empty($row['levelname']) ? '普通会员' : $row['levelname'];
                $row['realname'] = str_replace('=', "", $row['realname']);
                $row['nickname'] = str_replace('=', "", $row['nickname']);
                $row['remark'] = trim($row['content']);
        }
            unset($row);
            if(!empty($list)){
                $exflag = false;
            }else{
                $exflag = true;
            }
            if ($index == 1) {
                $filename = date('Ymd', time());
                $filename = ($columns['title']) . '-' .$filename.'.csv';
                $savepath = ELAPP_SHOP_DATA.'member/'.$filename;
                file_delete($savepath);//todo 未定义错误 Hlei2023/09/06
            }

            m('excel')->exportCSV($list, array(
                "title" => "会员数据",
                "columns" => $columns
            ),ELAPP_SHOP_DATA.'member/',$index,$exflag);
            unset($list);
            if(!$exflag){
                $pindex ++;
                $this->main($pindex);
            }
            exit();

            //m('excel')->export($list, array(
            // "title" => "会员数据-" . date('Y-m-d-H-i', time()),
            //
            //));
        }

        $open_redis = function_exists('redis') && !is_error(redis());
        if($join == "" && $condition == " and dm.uniacid=:uniacid"){
            if($open_redis) {
                $redis_key = "elapp_{$_W['uniacid']}_member_list";
                $total = m('member')->memberRadisCount($redis_key,false);
                if(!$total){
                    $total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
                    m('member') -> memberRadisCount($redis_key,$total);
                }
            }else{
                $total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
            }
        }else{
            $total = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm {$join} where 1 {$condition} ", $params);
        }
        $pager = pagination2($total, $pindex, $psize);

        //是否开启分销
        $opencommission = false;
        $plugin_commission = p('commission');
        if ($plugin_commission) {
            $comset = $plugin_commission->getSet();
            if (!empty($comset)) {
                $opencommission = true;
            }
        }

        //是否开启虚店店长
        $openvrshop = false;
        $plugin_vrshop = p('vrshop');
        if ($plugin_vrshop) {
            $vrshopSet = $plugin_vrshop->getSet();
            if (!empty($vrshopSet)) {
                $openvrshop = true;
            }
        }
        //是否开启虚店店员
        $openclerk = false;
        $plugin_clerk = p('clerk');
        if ($plugin_clerk) {
            $clerkSet = $plugin_clerk->getSet();
            if (!empty($clerkSet)) {
                $openclerk = true;
            }
        }

        //是否开启医生
        $opendoctor = false;
        $plugin_doctor = p('doctor');
        if ($plugin_doctor) {
            $doctorSet = $plugin_doctor->getSet();
            if (!empty($doctorSet)) {
                $opendoctor = true;
            }
        }

        //是否开启合伙人
        $opencopartner = false;
        $plugin_copartner = p('copartner');
        if ($plugin_copartner) {
            $copartnerSet = $plugin_copartner->getSet();
            if (!empty($copartnerSet)) {
                $opencopartner = true;
            }
        }

        $groups = m('member')->getGroups();
        $levels = m('member')->getLevels();

        $set = m('common')->getSysset();
        $default_levelname = empty($set['shop']['levelname']) ? '普通等级' : $set['shop']['levelname'];

        include $this->template();
    }

    function detail() {
        global $_W, $_GPC;
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $shop = $_W['shopset']['shop'];
        $id = intval($_GPC['id']);
        $member = m('member')->getMember($id);

        if (false == $member) {
            include $this->template('member/list/notFound');
            die;
        }

        // orgs
        $orgs = pdo_getall('elapp_shop_org_user', ['uniacid' => $_W['uniacid'],'deleted' => 0],['id','orgname']);

        // 系统日志
        $logs = pdo_getall('elapp_shop_member_logs', ['member_id' => $id], ['message', 'created_at']);
        foreach ($logs as $k=>$log) {
            $logs[$k]['create_time'] = date('Y-m-d H:i:s', $log['created_at']);
        }
        unset($log);
        // 关系变更日志
        $relation_logs = pdo_getall('elapp_shop_member_relation_log', ['member_id' => $id, 'uniacid' => $_W['uniacid']]);
        foreach ($relation_logs as $k=>$log) {
            // 枚举record_type类型
            $record_type = ['0' => '绑定', '1' => '解绑', '2' => '锁定', '3' => '解锁', '4' => '清除', '5' => '升级变更'];
            $operator_role = ['0' => '用户', '1' => '管理员'];
            $is_lock_relation = ['0' => '未锁定', '1' => '已锁定'];
            $relation_logs[$k]['is_lock_relation'] = $log['is_lock_relation'] ? $is_lock_relation[$log['is_lock_relation']] : '未锁定';
            $relation_logs[$k]['record_type'] = $log['type'] ? $record_type[$log['type']] : '绑定';
            $relation_logs[$k]['operator_role'] = $operator_role[$log['operator_role']];
            $relation_logs[$k]['create_time'] = date('Y-m-d H:i:s', $log['create_time']);
            // 通过onmid获取分享会员信息
            $onMember = m('member')->getMember($log['onmid']);
            $relation_logs[$k]['onmember_realname'] = $onMember['realname'];
            $relation_logs[$k]['onmember_nickname'] = $onMember['nickname'];
        }
        unset($log);

        #分销
        $hascommission = false;
        $plugin_com = p('commission');
        if ($plugin_com) {
            $plugin_com_set = $plugin_com->getSet();
            $hascommission = !empty($plugin_com_set['level']);
        }
        if ($hascommission) {
            $agentlevels = $plugin_com->getLevels();
            $commission = $plugin_com->getInfo($id, array('total', 'pay'));
            $member['com_commission_total'] = $commission['commission_total'];
            $member['com_commission_pay'] = $commission['commission_pay'];
        }

        #帮扶分红
        $hasmentor = false;
        $plugin_mentor = p('mentor');
        if ($plugin_mentor) {
            $plugin_mentor_set = $plugin_mentor->getSet();
            $hasmentor = !empty($plugin_mentor_set['open']);
        }

        #虚店店员
        $hasclerk = false;
        $plugin_clerk = p('clerk');
        if ($plugin_clerk) {
            $plugin_clerk_set = $plugin_clerk->getSet();
            $hasclerk = !empty($plugin_clerk_set['level']);
        }        
        if ($hasclerk) {
            $clerklevels_res = app(ClerkLevelLogic::class)->getClerkLevels();
            $clerklevels = $clerklevels_res['data'];
            $clerk = $plugin_clerk->getInfo($id, array('total', 'pay'));
            $clerk_mentor = $plugin_mentor->getInfo($id, array('total', 'pay'));
            //销售
            $member['clerk_commission_total'] = $clerk['commission_total'] + $clerk['mcard_commission_total'];
            $member['clerk_goods_commission_total'] = $clerk['commission_total'];
            $member['clerk_mcard_commission_total'] = $clerk['mcard_commission_total'];
            $member['clerk_commission_pay'] = $clerk['commission_pay'] + $clerk['mcard_commission_pay'];
            $member['clerk_goods_commission_pay'] = $clerk['commission_pay'];
            $member['clerk_mcard_commission_pay'] = $clerk['mcard_commission_pay'];
            //帮扶
            $member['clerk_dividend_total'] = $clerk_mentor['dividend_total'] + $clerk_mentor['mcard_dividend_total'] + $clerk_mentor['servicefee_dividend_total'];
            $member['clerk_goods_dividend_total'] = $clerk_mentor['dividend_total'];
            $member['clerk_mcard_dividend_total'] = $clerk_mentor['mcard_dividend_total'];
            $member['clerk_servicefee_dividend_total'] = $clerk_mentor['servicefee_dividend_total'];
            $member['clerk_dividend_pay'] = $clerk_mentor['dividend_pay'] + $clerk_mentor['mcard_dividend_pay'] + $clerk_mentor['servicefee_dividend_pay'];
            $member['clerk_goods_dividend_pay'] = $clerk_mentor['dividend_pay'];
            $member['clerk_mcard_dividend_pay'] = $clerk_mentor['mcard_dividend_pay'];
            $member['clerk_servicefee_dividend_pay'] = $clerk_mentor['servicefee_dividend_pay'];
            //累计收益
            $member['clerk_commission_total'] += $member['clerk_dividend_total'];
            //累计打款
            $member['clerk_commission_pay'] += $member['clerk_dividend_pay'];

            // 检查提现权限 是否缴纳店铺服务费
            $trade_set = $_W['shopset']['trade'];
            $apply_total_money = (new SettleWithdrawApplyModel())->getWithdrawTotal(['openid' => $_W['openid'], 'money_type' => 0,  ['status', '>=', 0]]);
            list($can, $err, $err_msg, $url) = (new SettleModel())->clerkCanWithdraw($member);
            if (!$can) {
                if ($err == 4 && $apply_total_money >= $trade_set['total_withdraw_money']) {
                    $withdrawMsg['title'] = '店铺服务费';
                    $withdrawMsg['message'] = '累计提现额度已超过免费提现额度' . $trade_set['total_withdraw_money'] . '元，须缴纳店铺服务费，请您缴费！';
                    $withdrawMsg['buttontext'] = '确定缴费';
                    //show_message($withdrawMsg, $url, 'error', $this);
                } else if ($err == 5) {
                    $withdrawMsg['title'] = '店铺服务费';
                    $withdrawMsg['message'] = '店铺服务费已到期，请您续费！';
                    $withdrawMsg['buttontext'] = '确定续费';
                    //show_message($withdrawMsg, $url,'error', $this);
                }
            } else {
                $withdrawMsg['title'] = '店铺服务费';
                $withdrawMsg['message'] = '可正常提现';
                $withdrawMsg['buttontext'] = '已缴费';
            }
            $ClerkRegWay = ClerkRegWayEnum::getRegWayDesc($member['clerk_register_way']);

        }

        #医生
        $hasdoctor = false;
        $plugin_doctor = p('doctor');
        if ($plugin_doctor) {
            $plugin_doctor_set = $plugin_doctor->getSet();
            $hasdoctor = !empty($plugin_doctor_set['level']);
        }        
        if ($hasdoctor) {
            $doctor_levels = $plugin_doctor->getLevels();
            $doctor = $plugin_doctor->getInfo($id, array('total', 'pay'));
            $doctor_mentor = $plugin_mentor->getInfo($id, array('total', 'pay'));
            //销售
            $member['doctor_commission_total'] = $doctor['commission_total'] + $doctor['mcard_commission_total'];
            $member['doctor_goods_commission_total'] = $doctor['commission_total'];
            $member['doctor_mcard_commission_total'] = $doctor['mcard_commission_total'];
            $member['doctor_commission_pay'] = $doctor['commission_pay'] + $doctor['mcard_commission_pay'];
            $member['doctor_goods_commission_pay'] = $doctor['commission_pay'];
            $member['doctor_mcard_commission_pay'] = $doctor['mcard_commission_pay'];
            //帮扶
            $member['doctor_dividend_total'] = $doctor_mentor['dividend_total'] + $doctor_mentor['mcard_dividend_total'] + $doctor_mentor['servicefee_dividend_total'];
            $member['doctor_goods_dividend_total'] = $doctor_mentor['dividend_total'];
            $member['doctor_mcard_dividend_total'] = $doctor_mentor['mcard_dividend_total'];
            $member['doctor_servicefee_dividend_total'] = $doctor_mentor['servicefee_dividend_total'];
            $member['doctor_dividend_pay'] = $doctor_mentor['dividend_pay'] + $doctor_mentor['mcard_dividend_pay'] + $doctor_mentor['servicefee_dividend_pay'];
            $member['doctor_goods_dividend_pay'] = $doctor_mentor['dividend_pay'];
            $member['doctor_mcard_dividend_pay'] = $doctor_mentor['mcard_dividend_pay'];
            $member['doctor_servicefee_dividend_pay'] = $doctor_mentor['servicefee_dividend_pay'];
            //累计收益
            $member['doctor_commission_total'] += $member['doctor_dividend_total'];
            //累计打款
            $member['doctor_commission_pay'] += $member['doctor_dividend_pay'];

        }

        #虚店店长
        $hasvrshop = false;
        $plugin_vrshop = p('vrshop');
        if ($plugin_vrshop) {
            $plugin_vrshop_set = $plugin_vrshop->getSet();
            $hasvrshop = !empty($plugin_vrshop_set['level']);
        }       
        if ($hasvrshop) {
            $owner_levels = $plugin_vrshop->getLevels();
            $owner = $plugin_vrshop->getInfo($id, array('total', 'pay'));
            $member['owner_commission_total'] = $owner['commission_total'];
            $member['owner_commission_pay'] = $owner['commission_pay'];
        }        

        #机构合伙人
        $hascopartner = false;
        $plugin_copartner = p('copartner');
        if ($plugin_copartner) {
            $plugin_copartner_set = $plugin_copartner->getSet();
            $hascopartner = !empty($plugin_copartner_set['level']);
        }
        if ($hascopartner) {
            $copartnerlevels = $plugin_copartner->getLevels();
            $copartner = $plugin_copartner->getInfo($id, array('total', 'pay'));
            $member['copartner_commission_total'] = $copartner['commission_total'];
            $member['copartner_commission_pay'] = $copartner['commission_pay'];
            
            //是否是创始人
            $member_isCopartnerAccount = $member['is_copartner']?$member['is_copartner']:$plugin_copartner->isCopartnerFounder($member['openid']);
            if($member_isCopartnerAccount){
                $member['copartner'] = $plugin_copartner->getCopartnerUserInfo($member['openid']);
                $member['copartnerAccount'] = $plugin_copartner->isCopartnerAccount($member['openid']);
                $member['is_copartner'] = !empty($member['is_copartner'])?$member['is_copartner']:1;
            }
        }
        # 集团
        $has_org = false;
        $plugin_org = p('org');
        if ($plugin_org) {
            $plugin_org_set = m('common')->getPluginSet('org');;
            $has_org = !empty($plugin_org_set['is_openorg']);
        }
        if ($has_org) {
            $org = $plugin_org->getListUserOne($member['org_id']);
            $member['org_name'] = $org['orgname'];
        }

        #会员分享
        $hasuserpromote = false;
        $plugin_userpromote = p('userpromote');
        if ($plugin_userpromote) {
            $plugin_userpromote_set = $plugin_userpromote->getSet();
            $hasuserpromote = !empty($plugin_userpromote_set['open']);
        }

        #股东分红
        $plugin_globonus = p('globonus');
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !empty($plugin_globonus_set['open']);
        }
        if ($hasglobonus) {
            $partnerlevels = $plugin_globonus->getLevels();
            $bonus = $plugin_globonus->getBonus($member['openid'],array('ok'));
            $member['bonusmoney'] = $bonus['ok'];
        }

        //联合创始人
        $plugin_author = p('author');
        if ($plugin_author) {
            $plugin_author_set = $plugin_author->getSet();
            $hasauthor = !empty($plugin_author_set['open']);
        }

        //区域代理
        $plugin_abonus = p('abonus');
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !empty($plugin_abonus_set['open']);
        }
        if ($hasabonus) {
            $aagentlevels = $plugin_abonus->getLevels();
            $bonus = $plugin_abonus->getBonus($member['openid'],array('ok','ok1','ok2','ok3'));
            $member['abonus_ok'] = $bonus['ok'];
            $member['abonus_ok1'] = $bonus['ok1'];
            $member['abonus_ok2'] = $bonus['ok2'];
            $member['abonus_ok3'] = $bonus['ok3'];

            $member['aagentprovinces'] = iunserializer($member['aagentprovinces']);
            $member['aagentcitys'] = iunserializer($member['aagentcitys']);
            $member['aagentareas'] = iunserializer($member['aagentareas']);
        }

        //社区
        $plugin_sns = p('sns');
        if ($plugin_sns) {
            $plugin_sns_set = $plugin_sns->getSet();
            $sns_member = pdo_fetch('select * from '.tablename('elapp_shop_sns_member')." where openid=:openid and uniacid=:uniacid limit 1",array(':openid'=>$member['openid'],':uniacid'=>$_W['uniacid']));
            $sns_member['postcount'] = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_sns_post').' where uniacid=:uniacid and openid=:openid and pid=0 and deleted = 0 and checked=1',array(':uniacid'=>$_W['uniacid'],':openid'=>$member['openid']));
            $sns_member['replycount'] = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_sns_post').' where uniacid=:uniacid and openid=:openid and pid>0 and deleted = 0 and checked=1',array(':uniacid'=>$_W['uniacid'],':openid'=>$member['openid']));

            $hassns = !empty($sns_member);
            if($hassns){
                $snslevels = $plugin_sns->getLevels();
            }
        }       
        
        //上级分销商
        if (!empty($member['agentid'])) {
            $parentagent = m('member')->getMember($member['agentid']);
        }
        //上级店员
        if (!empty($member['clerk_id'])) {
            $parentclerk = m('member')->getMember($member['clerk_id']);
        }
        //上级医生
        if (!empty($member['doctor_id'])) {
            $parentdoctor = m('member')->getMember($member['doctor_id']);
        }
        //上级店长
        if (!empty($member['owner_id'])) {
            $parentowner = m('member')->getMember($member['owner_id']);
        }
        //获取帮扶人
        if (!empty($member['mentor_id'])) {
            $parentmentor = m('member')->getMember($member['mentor_id']);
        }
        //获取所属合伙人
        if (!empty($member['copartner_id'])) {
            $parentcopartner = $plugin_copartner->getCopartnerUserInfo($member['copartner_id']);
            $parentcopartner['thumb'] = $parentcopartner['logo'];
            $parentcopartner['nickname'] = $parentcopartner['mcnname'];
        }
        //获取所属合伙人员工UID
        if (!empty($member['copartner_account_id'])) {
            $copartnerAccount = $plugin_copartner->isCopartnerAccount($member['copartner_account_id']);
            $parentCopartnerAccount = m('member')->getMember($copartnerAccount['openid']);
            $parentCopartnerAccount['thumb'] = $parentCopartnerAccount['avatar'];
        }        
        //获取上级分享会员
        if (!empty($member['onmid'])) {
            $parentonmember = m('member')->getMember($member['onmid']);
        }
        //获取最后邀请人
        if (!empty($member['inviter_id'])) {
            $inviterMember = m('member')->getMember($member['inviter_id']);
        }

        $self_ordercount = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $self_ordermoney = pdo_fetchcolumn('select sum(price) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $order = pdo_fetch('select finishtime from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status>=1 Limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $member['last_ordertime'] = $order['finishtime'];        
        $followed = m('user')->followed($member['openid']);        

        //自定义表单
        $diyform_flag = 0;
        $diyform_flag_commission = 0;
        $diyform_flag_clerk = 0;
        $diyform_flag_doctor = 0;
        $diyform_flag_owner = 0;
        $diyform_flag_copartner = 0;
        $diyform_flag_globonus = 0;
        $diyform_flag_abonus = 0;
        $diyform_flag_dividend = 0;
        $diyform_flag_mentor = 0;
        $diyform_plugin = p('diyform');
        if ($diyform_plugin) {
            //会员
            if (!empty($member['diymemberdata'])) {
                $diyform_flag = 1;
                $fields = iunserializer($member['diymemberfields']);
            }
            //分销商
            if (!empty($member['diycommissiondata'])) {
                $diyform_flag_commission = 1;
                $cfields = iunserializer($member['diycommissionfields']);
            }
            //团队分红
            if (!empty($member['diyheadsfields'])) {
                $diyform_flag_dividend = 1;
                $dfields = iunserializer($member['diyheadsfields']);
            }
            //店员
            if (!empty($member['diy_clerk_data'])) {
                $diyform_flag_clerk = 1;
                $clerkfields = iunserializer($member['diy_clerk_fields']);
            }
            //医生
            if (!empty($member['diy_doctor_data'])) {
                $diyform_flag_doctor = 1;
                $doctorfields = iunserializer($member['diy_doctor_fields']);
            }
            //店长
            if (!empty($member['diy_owner_data'])) {
                $diyform_flag_owner = 1;
                $ownerfields = iunserializer($member['diy_owner_fields']);
            }
            //合伙人
            if (!empty($member['diycopartnerdata'])) {
                $diyform_flag_copartner = 1;
                $copartnerfields = iunserializer($member['diy_copartner_fields']);
            }
            //扶植分红
            if (!empty($member['diy_mentor_fields'])) {
                $diyform_flag_mentor = 1;
                $mfields = iunserializer($member['diy_mentor_fields']);
            }
            //股东分红
            if (!empty($member['diyglobonusdata'])) {
                $diyform_flag_globonus = 1;
                $gfields = iunserializer($member['diyglobonusfields']);
            }
            //区域代理
            if (!empty($member['diyaagentdata'])) {
                $diyform_flag_abonus = 1;
                $aafields = iunserializer($member['diyaagentfields']);
            }
        }

        $groups = m('member')->getGroups();
        $levels = m('member')->getLevels();
        $openbind = false;
        if((empty($_W['shopset']['app']['isclose']) && !empty($_W['shopset']['app']['openbind'])) || !empty($_W['shopset']['wap']['open'])){
            $openbind = true;
        }

        if ($_W['ispost']) {

            $data = is_array($_GPC['data']) ? $_GPC['data'] : array();
            if($data['maxcredit']<0){
                $data['maxcredit'] = 0;
            }

            if($openbind){
                if(!empty($data['mobileverify'])){
                    if(empty($data['mobile'])){
                        show_json(0, "绑定手机号请先填写用户手机号!");
                    }
                    $m = pdo_fetch('select id from ' . tablename('elapp_shop_member') . ' where mobile=:mobile and mobileverify=1 and uniacid=:uniaicd limit 1 ', array(':mobile'=>$data['mobile'], ':uniaicd'=>$_W['uniacid']));
                    if(!empty($m) && $m['id']!=$id){
                        show_json(0, "此手机号已绑定其他用户!(uid:".$m['id'].")");
                    }
                }

                $data['pwd'] = trim($data['pwd']);
                if(!empty($data['pwd'])){
                    $salt = $member['salt'];
                    if (empty($salt)) {
                        //生成识别码
                        $salt = m('account')->getSalt();
                    }
                    $data['pwd'] = md5($data['pwd'] . $salt);
                    $data['salt'] = $salt;
                }else{
                    unset($data['pwd']);
                    unset($data['salt']);
                }
            }

            if (is_array($_GPC['data']['groupid'])){
                $data['groupid'] = implode(',',$_GPC['data']['groupid']);
            }
            if(empty($data['groupid'])){
                $data['groupid']='';
            }
            pdo_update('elapp_shop_member', $data, array('id' => $id, 'uniacid' => $_W['uniacid']));

            $member =array_merge($member,$data);

            plog('member.list.edit', "修改会员资料  ID: {$member['id']} <br/> 会员信息:  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");

            //分销资料
            if ($hascommission) {
                if (cv('commission.agent.edit')) {
                    $adata = is_array($_GPC['adata']) ? $_GPC['adata'] : array();
                    $adata['childtime'] = time();
                    if (!empty($adata)) {
                        //  判断修改上线权限 并写日志
                        if($adata['agentid']!=$member['agentid']){
                            //重新创建关系树，如果关系树有错误则提示
                            if(p('commission')){
                                p('commission') -> delRelation($member['id']);
                                $mem = p('commission') -> saveRelation($member['id'],$adata['agentid']);
                                if(is_array($mem)){
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $mem['id']))."'>会员(".$mem['nickname'].")</a>的上级分销商!");
                                }
                            }

                            if(cv('commission.agent.changeagent')){
                                plog('commission.agent.changeagent', "修改上级分销商 <br/> 会员信息:  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级ID: {$member['agentid']} -> 新上级ID: {$adata['agentid']}; <br/> 固定上级: ".($member['fixagentid']?'是':'否')." -> ".($adata['fixagentid']?'是':'否') );
                            }else{
                                $adata['agentid']=$member['agentid'];
                            }
                        }

                        $agent_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $adata['cmemberagent'] = $adata['cmemberagent'];
                            $adata['agentnotupgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $agent_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $adata['isagent'] = 1;
                                $adata['status'] = 1;

                                if(!empty($adata['agentid'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $adata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $adata['isagent'] = 0;
                                $adata['status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['oldstatus']) && $adata['status'] == 1) {
                                $agent_flag = 1;
                            }
                        }

                        if (!empty($agent_flag)) {
                            $time = time();
                            $adata['agenttime'] = time();
                            //成为分销商消息通知
                            $plugin_com->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'agenttime' => $time), TM_COMMISSION_BECOME);
                            plog('commission.agent.check', "审核分销商 <br/>分销商信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('commission.agent.edit', "修改分销商 <br/>分销商信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $adata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                        if($adata['agentid']!=$member['agentid']){
                            //p('commission') -> modify($member['id'], $adata['agentid']);
                            if(p('dividend')){
                                $dividend = pdo_fetch('select id,isheads,headsid,headsstatus from '.tablename('elapp_shop_member').' where id = :id',array(':id'=>$adata['agentid']));
                                $dividend_init = pdo_fetch('select * from '.tablename('elapp_shop_dividend_init').' where headsid = :headsid',array(':headsid'=>$adata['agentid']));
                                if(!empty($dividend['isheads']) && !empty($dividend['headsstatus']) && !empty($dividend_init['status'])){
                                    pdo_update('elapp_shop_member',array('headsid'=>$adata['agentid']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>$adata['agentid']), array('id' =>$ids));
                                    }
                                }else if(empty($dividend['isheads']) && !empty($dividend['headsid'])){
                                    pdo_update('elapp_shop_member',array('headsid'=>$dividend['headsid']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>$dividend['headsid']), array('id' =>$ids));
                                    }
                                }else{
                                    pdo_update('elapp_shop_member',array('headsid'=>0),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>0), array('id' =>$ids));
                                    }
                                }
                            }
                        }
                        if (!empty($agent_flag)) {
                            //检测升级
                            if (!empty($member['agentid'])) {
                                $plugin_com->upgradeLevelByAgent($member['agentid']);

                                if(p('globonus')){
                                    p('globonus')->upgradeLevelByAgent($member['agentid']);
                                }
                                //创始人升级
                                if(p('author')){
                                    p('author')->upgradeLevelByAgent($member['agentid']);
                                }
                            }
                        }
                    }
                }
            }
            //会员分享裂变
            if ($hasuserpromote) {
                if (cv('userpromote.member.edit')) {
                    $udata = is_array($_GPC['udata']) ? $_GPC['udata'] : array();                    
                    if (!empty($udata)) {
                        // 判断修改上线权限 并写日志
                        if($udata['onmid'] != $member['onmid']){
                            $udata['onmid_create_time'] = time();
                            //重新创建关系树，如果关系树有错误则提示
                            if (p('userpromote')) {
                                p('userpromote') -> delRelation($member['id']);
                                $umem = p('userpromote') -> saveRelation($member['id'],$udata['onmid']);
                                if (is_array($umem)) {
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $stmem['id']))."'>会员(".$stmem['nickname'].")</a>的上级会员!");
                                }
                            }
                            if (cv('userpromote.member.changeParentMember')) {
                                plog('userpromote.member.changeParentMember', "修改上级会员 <br/> 会员信息:  {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级会员ID: {$member['onmid']} -> 新上级会员ID: {$udata['onmid']}; <br/> 固定上级: ".($member['fix_onmid']?'是':'否')." -> ".($udata['fix_onmid']?'是':'否')."<br/> 登记时间: ".($member['onmid_create_time']?date('Y-m-d H:i:s',$member['onmid_create_time']):'无')." -> ".($udata['onmid_create_time']?date('Y-m-d H:i:s',$udata['onmid_create_time']):'无') );
                            } else {
                                $udata['onmid'] = $member['onmid'];
                                $udata['onmid_create_time'] = $member['onmid_create_time'];
                            }
                        }
                        // 修改最后邀请人 并写日志
                        if ($udata['inviter_id'] != $member['inviter_id']) {
                            //固定上级会员
                            if(!empty($udata['fix_onmid'])){
                                show_json(-1, "保存错误！" . "<br/>已固定上级会员，无法修改邀请人");
                            }
                            $udata['inviter_id_create_time'] = time();
                            if (cv('userpromote.member.changeInviterMember')) {
                                plog('userpromote.member.changeInviterMember', "修改邀请人 <br/> 会员信息:  {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>邀请人ID: {$member['inviter_id']} -> 新邀请人ID: {$udata['inviter_id']}; <br/> 登记时间: ".($member['inviter_id_create_time']?date('Y-m-d H:i:s',$member['inviter_id_create_time']):'无')." -> ".($udata['inviter_id_create_time']?date('Y-m-d H:i:s',$udata['inviter_id_create_time']):'无') );
                            } else {
                                $udata['inviter_id'] = $member['inviter_id'];
                                $udata['inviter_id_create_time'] = $member['inviter_id_create_time'];
                            }
                        }
                        
                        $userpromote_log = "修改会员关系 <br/>会员信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}";
                        //plog('userpromote.member.edit', $userpromote_log);
                        pdo_update('elapp_shop_member', $udata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                    }
                }
            }

            //虚店店员资料
            if ($hasclerk) {
                if (cv('clerk.clerk.edit')) {
                    $cdata = is_array($_GPC['cdata']) ? $_GPC['cdata'] : array();
                    $cdata['childtime'] = time();
                    if (!empty($cdata)) {
                        //  判断修改上线权限 并写日志
                        if($cdata['clerk_id']!=$member['clerk_id']){
                            $cdata['clerk_id_create_time'] = time();
                            //重新创建关系树，如果关系树有错误则提示
                            if(p('clerk')){
                                p('clerk') -> delRelation($member['id']);
                                $mem = p('clerk') -> saveRelation($member['id'],$cdata['clerk_id']);
                                if(is_array($mem)){
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $mem['id']))."'>会员(".$mem['nickname'].")</a>的上级店员!");
                                }
                            }
                            //写日志
                            if(cv('clerk.clerk.changeclerk')){
                                plog('clerk.clerk.changeclerk', "修改上级店员 <br/> 会员信息:  {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级店员ID: {$member['clerk_id']} -> 新上级店员ID: {$cdata['clerk_id']}; <br/> 固定上级店员: ".($member['fix_clerk_id']?'是':'否')." -> ".($cdata['fix_clerk_id']?'是':'否') );
                            }else{
                                $cdata['clerk_id']=$member['clerk_id'];
                                $cdata['clerk_id_create_time'] = $member['clerk_id_create_time'];
                            }
                        }

                        $clerk_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $cdata['cmemberclerk'] = $cdata['cmemberclerk'];
                            $cdata['clerk_not_upgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $clerk_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $cdata['is_clerk'] = 1;
                                $cdata['clerk_status'] = 1;

                                if(!empty($cdata['clerk_id'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $cdata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $cdata['is_clerk'] = 0;
                                $cdata['clerk_status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['oldclerk_status']) && $cdata['clerk_status'] == 1) {
                                $clerk_flag = 1;
                            }
                        }

                        if (!empty($clerk_flag)) {
                            $time = time();
                            $cdata['clerk_create_time'] = time();
                            //成为店员消息通知
                            $plugin_clerk->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'clerk_create_time' => $time), TM_CLERK_BECOME);
                            plog('clerk.clerk.check', "审核店员 <br/>店员信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('clerk.clerk.edit', "修改店员 <br/>店员信息:  ID: {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $cdata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                        
                        if($cdata['clerk_id'] != $member['clerk_id']){
                            //p('clerk') -> modify($member['id'], $cdata['owner_id']);
                            if(p('mentor')){
                                $mentor = pdo_fetch('select id,is_mentor,mentor_id,mentor_status from '.tablename('elapp_shop_member').' where id = :id',array(':id'=>$cdata['clerk_id']));
                                $mentor_init = pdo_fetch('select * from '.tablename('elapp_shop_mentor_init').' where mentor_id = :mentor_id',array(':mentor_id'=>$cdata['clerk_id']));
                                if(!empty($mentor['is_mentor']) && !empty($mentor['mentor_status']) && !empty($mentor_init['status'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$cdata['clerk_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_clerk_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$cdata['clerk_id']), array('id' =>$ids));
                                    }
                                }else if(empty($mentor['is_mentor']) && !empty($mentor['mentor_id'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$mentor['mentor_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_clerk_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$mentor['mentor_id']), array('id' =>$ids));
                                    }
                                }else{
                                    pdo_update('elapp_shop_member',array('mentor_id'=>0),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_clerk_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array('mentor_id' => 0), array('id' =>$ids));
                                    }
                                }
                            }
                        }
                        if (!empty($clerk_flag)) {
                            //检测升级
                            if (!empty($member['clerk_id'])) {
                                $plugin_clerk->upgradeLevelByClerk($member['clerk_id']);
                            }
                        }
                    }
                }
            }

            //医生资料
            if ($hasdoctor) {
                if (cv('doctor.doctor.edit')) {
                    $ddata = is_array($_GPC['ddata']) ? $_GPC['ddata'] : array();
                    $ddata['childtime'] = time();
                    if (!empty($ddata)) {
                        //  判断修改上线权限 并写日志
                        if($ddata['doctor_id']!=$member['doctor_id']){
                            $ddata['doctor_id_create_time'] = time();
                            //重新创建关系树，如果关系树有错误则提示
                            if(p('doctor')){
                                p('doctor') -> delRelation($member['id']);
                                $mem = p('doctor') -> saveRelation($member['id'],$ddata['doctor_id']);
                                if(is_array($mem)){
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $mem['id']))."'>会员(".$mem['nickname'].")</a>的上级店员!");
                                }
                            }
                            //写日志
                            if(cv('doctor.doctor.changedoctor')){
                                plog('doctor.doctor.changedoctor', "修改上级店员 <br/> 会员信息:  {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级店员ID: {$member['doctor_id']} -> 新上级店员ID: {$ddata['doctor_id']}; <br/> 固定上级店员: ".($member['fix_doctor_id']?'是':'否')." -> ".($ddata['fix_doctor_id']?'是':'否') );
                            }else{
                                $ddata['doctor_id']=$member['doctor_id'];
                                $ddata['doctor_id_create_time'] = $member['doctor_id_create_time'];
                            }
                        }

                        $doctor_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $ddata['cmemberdoctor'] = $ddata['cmemberdoctor'];
                            $ddata['doctor_not_upgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $doctor_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $ddata['is_doctor'] = 1;
                                $ddata['doctor_status'] = 1;

                                if(!empty($ddata['doctor_id'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $ddata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $ddata['is_doctor'] = 0;
                                $ddata['doctor_status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['olddoctorstatus']) && $ddata['doctor_status'] == 1) {
                                $doctor_flag = 1;
                            }
                        }

                        if (!empty($doctor_flag)) {
                            $time = time();
                            $ddata['doctor_create_time'] = time();
                            //成为店员消息通知
                            $plugin_doctor->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'doctor_create_time' => $time), TM_DOCTOR_BECOME);
                            plog('doctor.doctor.check', "审核店员 <br/>店员信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('doctor.doctor.edit', "修改店员 <br/>店员信息:  ID: {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $ddata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                        
                        if($ddata['doctor_id'] != $member['doctor_id']){
                            //p('doctor') -> modify($member['id'], $ddata['owner_id']);
                            if(p('mentor')){
                                $mentor = pdo_fetch('select id,is_mentor,mentor_id,mentor_status from '.tablename('elapp_shop_member').' where id = :id',array(':id'=>$ddata['doctor_id']));
                                $mentor_init = pdo_fetch('select * from '.tablename('elapp_shop_mentor_init').' where mentor_id = :mentor_id',array(':mentor_id'=>$ddata['doctor_id']));
                                if(!empty($mentor['is_mentor']) && !empty($mentor['mentor_status']) && !empty($mentor_init['status'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$ddata['doctor_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_doctor_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$ddata['doctor_id']), array('id' =>$ids));
                                    }
                                }else if(empty($mentor['is_mentor']) && !empty($mentor['mentor_id'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$mentor['mentor_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_doctor_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$mentor['mentor_id']), array('id' =>$ids));
                                    }
                                }else{
                                    pdo_update('elapp_shop_member',array('mentor_id'=>0),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_doctor_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array('mentor_id' => 0), array('id' =>$ids));
                                    }
                                }
                            }
                        }
                        if (!empty($doctor_flag)) {
                            //检测升级
                            if (!empty($member['doctor_id'])) {
                                $plugin_doctor->upgradeLevelByDoctor($member['doctor_id']);                                
                            }
                        }
                    }
                }
            }

            //虚店店长资料
            if ($hasvrshop) {
                if (cv('vrshop.owner.edit')) {
                    $vdata = is_array($_GPC['vdata']) ? $_GPC['vdata'] : array();
                    $vdata['childtime'] = time();
                    if (!empty($vdata)) {

                        //  判断修改上线权限 并写日志
                        if($vdata['owner_id']!=$member['owner_id']){

                            //重新创建关系树，如果关系树有错误则提示
                            if(p('vrshop')){
                                p('vrshop') -> delRelation($member['id']);
                                $mem = p('vrshop') -> saveRelation($member['id'],$vdata['owner_id']);
                                if(is_array($mem)){
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $mem['id']))."'>会员(".$mem['nickname'].")</a>的上级店长!");
                                }
                            }

                            if(cv('vrshop.owner.changeowner')){

                                plog('vrshop.owner.changeowner', "修改上级店长 <br/> 会员信息:  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级ID: {$member['owner_id']} -> 新上级ID: {$vdata['owner_id']}; <br/> 固定上级: ".($member['fix_owner_id']?'是':'否')." -> ".($vdata['fix_owner_id']?'是':'否') );

                            }else{
                                $vdata['owner_id']=$member['owner_id'];
                            }
                        }

                        $owner_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $vdata['cmemberowner'] = $vdata['cmemberowner'];
                            $vdata['owner_not_upgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $owner_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $vdata['is_owner'] = 1;
                                $vdata['owner_status'] = 1;

                                if(!empty($vdata['owner_id'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $vdata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $vdata['is_owner'] = 0;
                                $vdata['owner_status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['oldownerstatus']) && $vdata['owner_status'] == 1) {
                                $owner_flag = 1;
                            }
                        }

                        if (!empty($owner_flag)) {
                            $time = time();
                            $vdata['owner_create_time'] = time();
                            //成为店长消息通知
                            $plugin_vrshop->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'owner_create_time' => $time), TM_VRSHOP_BECOME);
                            plog('vrshop.owner.check', "审核店长 <br/>店长信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('vrshop.owner.edit', "修改店长 <br/>店长信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $vdata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                        if($vdata['owner_id'] != $member['owner_id']){
                            //p('vrshop') -> modify($member['id'], $vdata['owner_id']);
                            //扶植分红
                            if(p('mentor')){
                                $mentor = pdo_fetch('select id,is_mentor,mentor_id,mentor_status from '.tablename('elapp_shop_member').' where id = :id',array(':id'=>$vdata['owner_id']));
                                $mentor_init = pdo_fetch('select * from '.tablename('elapp_shop_mentor_init').' where mentor_id = :mentor_id',array(':mentor_id'=>$vdata['owner_id']));
                                if(!empty($mentor['is_mentor']) && !empty($mentor['mentor_status']) && !empty($mentor_init['status'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$vdata['owner_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_vrshop_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$vdata['owner_id']), array('id' =>$ids));
                                    }
                                }else if(empty($mentor['is_mentor']) && !empty($mentor['mentor_id'])){
                                    pdo_update('elapp_shop_member',array('mentor_id'=>$mentor['mentor_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_vrshop_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>$mentor['mentor_id']), array('id' =>$ids));
                                    }
                                }else{
                                    pdo_update('elapp_shop_member',array('mentor_id'=>0),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_vrshop_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("mentor_id"=>0), array('id' =>$ids));
                                    }
                                }
                            }
                        }
                        if (!empty($owner_flag)) {
                            //检测升级
                            if (!empty($member['owner_id'])) {
                                //$plugin_com->upgradeLevelByOwner($member['owner_id']);

                                if(p('globonus')){
                                    //p('globonus')->upgradeLevelByOwner($member['owner_id']);
                                }
                                //创始人升级
                                if(p('author')){
                                    //p('author')->upgradeLevelByOwner($member['owner_id']);
                                }
                            }
                        }
                    }
                }
            }
            
            //帮扶分红
            if ($hasmentor) {
                if (cv('mentor.agent.edit')) {
                    $mentordata = is_array($_GPC['mentordata']) ? $_GPC['mentordata'] : array();
                    $mentordata['childtime'] = time();
                    if (!empty($mentordata)) {
                        //  判断修改上线权限 并写日志
                        if($mentordata['mentor_id']!=$member['mentor_id']){
                            $mentordata['mentor_id_create_time'] = time();
                            //重新创建关系树，如果关系树有错误则提示
                            //todo在这里写处理关系;
                            //写日志
                            if(cv('mentor.agent.changementor')){
                                plog('mentor.agent.changementor', "修改帮扶人 <br/> 会员信息:  {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>原帮扶人ID: {$member['mentor_id']} -> 新帮扶人ID: {$mentordata['clerk_id']}; <br/> 固定帮扶人: ".($member['fix_mentor_id']?'是':'否')." -> ".($mentordata['fix_mentor_id']?'是':'否') );
                            }else{
                                $mentordata['mentor_id'] = $member['mentor_id'];
                                $mentordata['mentor_id_create_time'] = $member['mentor_id_create_time'];
                            }
                        }

                        $mentor_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $mentordata['cmembermentor'] = $mentordata['cmembermentor'];
                            $mentordata['mentornotupgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $mentor_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $mentordata['is_mentor'] = 1;
                                $mentordata['mentor_status'] = 1;

                                if(!empty($mentordata['mentor_id'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $mentordata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $mentordata['is_mentor'] = 0;
                                $mentordata['mentor_status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['oldmentor_status']) && $mentordata['mentor_status'] == 1) {
                                $mentor_flag = 1;
                            }
                        }

                        if (!empty($mentor_flag)) {
                            $time = time();
                            $mentordata['mentor_create_time'] = time();
                            //成为帮扶人消息通知
                            $plugin_mentor->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'mentor_create_time' => $time), TM_MENTOR_BECOME);
                            plog('mentor.agent.check', "审核帮扶 <br/>店员信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('mentor.agent.edit', "修改帮扶关系 <br/>店员信息:  ID: {$member['id']}/{$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $mentordata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                                                
                        if (!empty($mentor_flag)) {
                            //检测升级
                            if (!empty($member['mentor_id'])) {
                                //$plugin_mentor->upgradeLevelByMentor($member['mentor_id']);                                
                            }
                        }
                    }
                }
            }

            //机构合伙人资料
            if ($hascopartner) {
                if (cv('copartner.user.edit')) {
                    $codata = is_array($_GPC['codata']) ? $_GPC['codata'] : array();
                    $codata['copartner_id'] = trim($_GPC['copartner_id']);
                    $codata['copartner_account_id'] = trim($_GPC['copartner_account_id']);
                    $copartner = is_array($_GPC['copartner']) ? $_GPC['copartner'] : array();
                    $codata['childtime'] = time();
                    if (!empty($codata)) {
                        //  判断修改上线权限 并写日志
                        if ($codata['copartner_id'] != $member['copartner_id']) {
                            $codata['copartner_id_create_time'] = time();
                            //重新创建关系树，如果关系树有错误则提示
                            if(p('copartner')){
                                p('copartner') -> delRelation($member['id']);
                                $mem = p('copartner') -> saveRelation($member['id'],$codata['copartner_id']);
                                if(is_array($mem)){
                                    show_json(-1, "保存错误！". "<br/>请修改<a style='color: #259fdc;' target='_blank' href='".webUrl('member/list/detail', array('id' => $mem['id']))."'>会员(".$mem['nickname'].")</a>的上级分销商!");
                                }
                            }

                            if(cv('copartner.user.changecopartner')){
                                plog('copartner.user.changecopartner', "修改上级合伙人 <br/> 会员信息:  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']} <br/>上级ID: {$member['copartner_id']} -> 新上级ID: {$codata['copartner_id']}; <br/> 固定上级: ".($member['fix_copartner_id']?'是':'否')." -> ".($codata['fix_copartner_id']?'是':'否') );
                            }else{
                                $codata['copartner_id'] = $member['copartner_id'];
                                $codata['copartner_user_path'] = $member['copartner_user_path'];
                                $codata['copartner_id_create_time'] = $member['copartner_id_create_time'];
                            }
                        }

                        $copartner_flag = 0;
                        $cmember_plugin = p('cmember');
                        if ($cmember_plugin) {
                            $codata['cmembercopartner'] = $codata['cmembercopartner'];
                            $codata['copartner_not_upgrade'] = 1;
                            if($member['level'] == 0 && intval($data['level']) > 0) {
                                $copartner_flag = 1;
                            }

                            if (intval($data['level']) > 0) {
                                $codata['is_copartner'] = 1;
                                $codata['copartner_status'] = 1;

                                if(!empty($codata['copartner_id'])) {
                                    $cmemberuid = $cmember_plugin->getCmemberuid($member['id']);
                                    if ($cmemberuid > 0) {
                                        $codata['cmemberuid'] = $cmemberuid;
                                    }
                                }
                            } else {
                                $codata['is_copartner'] = 0;
                                $codata['copartner_status'] = 0;
                            }
                        } else {
                            if (empty($_GPC['oldcopartnerstatus']) && $codata['copartner_status'] == 1) {
                                $copartner_flag = 1;
                            }
                        }

                        if (!empty($copartner_flag)) {
                            $time = time();
                            $codata['copartner_create_time'] = time();
                            //成为合伙人消息通知
                            $plugin_copartner->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'copartner_create_time' => $time), TM_COPARTNER_BECOME);
                            plog('copartner.user.check', "审核合伙人 <br/>分销商信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }

                        // 变更机构
                        if (isset($copartner['org_id'])) {
                            $codata['org_id'] = $copartner['org_id'];
                        }

                        plog('copartner.user.edit', "修改合伙人 <br/>合伙人信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $codata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                        $copartner = is_array($_GPC['copartner']) ? $_GPC['copartner'] : array();
                        if($codata['owner_id'] != $member['owner_id']){
                            //p('copartner') -> modify($member['id'], $codata['owner_id']);
                            if(p('dividend')){
                                $dividend = pdo_fetch('select id,isheads,headsid,headsstatus from '.tablename('elapp_shop_member').' where id = :id',array(':id'=>$vdata['owner_id']));
                                $dividend_init = pdo_fetch('select * from '.tablename('elapp_shop_dividend_init').' where headsid = :headsid',array(':headsid'=>$vdata['owner_id']));
                                if(!empty($dividend['isheads']) && !empty($dividend['headsstatus']) && !empty($dividend_init['status'])){
                                    pdo_update('elapp_shop_member',array('headsid'=>$codata['owner_id']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>$codata['owner_id']), array('id' =>$ids));
                                    }
                                }else if(empty($dividend['isheads']) && !empty($dividend['headsid'])){
                                    pdo_update('elapp_shop_member',array('headsid'=>$dividend['headsid']),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>$dividend['headsid']), array('id' =>$ids));
                                    }
                                }else{
                                    pdo_update('elapp_shop_member',array('headsid'=>0),array('id'=>$member['id'],'uniacid'=>$_W['uniacid']));
                                    $data = pdo_fetchall('select id from '.tablename('elapp_shop_commission_relation').' where pid = :pid',array(':pid'=>$member['id']));
                                    if(!empty($data)){
                                        $ids = array();
                                        foreach($data as $k => $v){
                                            $ids[] = $v['id'];
                                        }
                                        pdo_update('elapp_shop_member', array("headsid"=>0), array('id' =>$ids));
                                    }
                                }
                            }
                        }
                        if (!empty($copartner_flag)) {
                            //检测升级
                            if (!empty($member['copartner_id'])) {
                                //$plugin_com->upgradeLevelByOwner($member['clerk_id']);

                                if(p('globonus')){
                                    //p('globonus')->upgradeLevelByOwner($member['clerk_id']);
                                }
                                //创始人升级
                                if(p('author')){
                                    //p('author')->upgradeLevelByOwner($member['clerk_id']);
                                }
                            }
                        }
                    }
                    //机构
                    $copartnerData = array();
                    if(!empty($copartner)){
                        if($copartner['levelid'] != $member['copartner']['levelid']){
                            $copartnerData['levelid'] = $copartner['levelid'];
                        }
                        if(!empty($copartnerData)) {
                            pdo_update('elapp_shop_copartner_user', $copartnerData, array('id' => $member['copartner']['id'], 'uniacid' => $_W['uniacid']));
                        }
                    }
                }
            }

            //股东资料
            if($hasglobonus){
                if (cv('globonus.partner.check')) {
                    $gdata = is_array($_GPC['gdata']) ? $_GPC['gdata'] : array();
                    if (!empty($gdata)) {
                        if (empty($_GPC['oldpartnerstatus']) && $gdata['partnerstatus'] == 1) {
                            $time = time();
                            $gdata['partnertime'] = time();
                            //成为股东消息通知
                            $plugin_globonus->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'partnertime' => $time), TM_GLOBONUS_BECOME);
                            plog('globonus.partner.check', "审核股东 <br/>股东信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }
                        plog('globonus.partner.edit', "修改股东 <br/>股东信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $gdata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                    }
                }
            }

            //联合创始人资料
            if($hasauthor){
                if (cv('author.partner.check')) {
                    $author_data = is_array($_GPC['authordata']) ? $_GPC['authordata'] : array();
                    if (!empty($author_data)) {
                        if (empty($_GPC['oldauthorstatus']) && $author_data['authorstatus'] == 1) {
                            $author_data['authortime'] = time();
                            if (method_exists($plugin_author,'changeAuthorId')){
                                $plugin_author->changeAuthorId($member['id']);
                            }
                            //成为创始人消息通知
                            $plugin_author->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'authortime' => time()), TM_AUTHOR_BECOME);
                            plog('author.partner.check', "审核创始人 <br/>创始人信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }

                        if ($_GPC['oldauthorstatus'] == 1 && $author_data['authorstatus'] == 0) {
                            if (method_exists($plugin_author,'changeAuthorId')){
                                $plugin_author->changeAuthorId($member['id'],intval($member['authorid']));
                            }
                        }

                        plog('author.partner.edit', "修改创始人 <br/>创始人信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        pdo_update('elapp_shop_member', $author_data, array('id' => $id, 'uniacid' => $_W['uniacid']));
                    }
                }
            }

            //区域代理商资料
            if($hasabonus){
                if (cv('abonus.agent.check')) {
                    $aadata = is_array($_GPC['aadata']) ? $_GPC['aadata'] : array();
                    if (!empty($aadata)) {

                        $aagentprovinces =  is_array($_GPC['aagentprovinces'])?$_GPC['aagentprovinces']:array();
                        $aagentcitys =  is_array($_GPC['aagentcitys'])?$_GPC['aagentcitys']:array();
                        $aagentareas =  is_array($_GPC['aagentareas'])?$_GPC['aagentareas']:array();

                        $aadata['aagentprovinces'] =iserializer($aagentprovinces);
                        $aadata['aagentcitys'] = iserializer($aagentcitys);
                        $aadata['aagentareas'] =iserializer($aagentareas);
                        if($aadata['aagenttype']==2){
                            //市级删除省级代理地区
                            $aadata['aagentprovinces'] = iserializer(array());
                        } else if($aadata['aagenttype']==3){
                            //区级代理删除省级及市级代理地区
                            $aadata['aagentprovinces'] = iserializer(array());
                            $aadata['aagentcitys'] = iserializer(array());
                        }
                        $areas = array_merge($aagentprovinces, $aagentcitys,$aagentareas );

                        if (empty($_GPC['oldaagentstatus']) && $aadata['aagentstatus'] == 1) {
                            $time = time();
                            $aadata['aagenttime'] = time();
                            //成为代理商消息通知
                            $plugin_abonus->sendMessage($member['openid'],
                                array('nickname' => $member['nickname'],
                                    'aagenttype' => $aadata['aagenttype'],
                                    'aagenttime' => $time,
                                    'aagentareas'=>implode( "; ", $areas)
                                ), TM_ABONUS_BECOME);
                            plog('abonus.agent.check', "审核代理商 <br/>代理商信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
                        }

                        $log = "修改代理商 <br/>代理商信息:  ID: {$member['id']}/{$member['openid']}/{$member['nickname']}";
                        if(is_array($_GPC['aagentprovinces'])) {
                            $log .= "<br/>代理省份:" . implode(',', $_GPC['aagentprovinces']);
                        }
                        if(is_array($_GPC['aagentcitys'])) {
                            $log .= "<br/>代理城市:" . implode(',', $_GPC['aagentcitys']);
                        }
                        if(is_array($_GPC['aagentareas'])) {
                            $log .= "<br/>代理地区:" . implode(',', $_GPC['aagentareas']);
                        }

                        plog('abonus.agent.edit', $log);
                        pdo_update('elapp_shop_member', $aadata, array('id' => $id, 'uniacid' => $_W['uniacid']));
                    }
                }
            }

            //社区资料
            if($hassns){
                if (cv('sns.member.edit')) {
                    $snsdata = is_array($_GPC['snsdata']) ? $_GPC['snsdata'] : array();
                    if (!empty($snsdata)) {
                        $sns_log = "修改会员社区资料 SNSUID: {$sns_member['id']}<br/>会员信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}";
                        $sns_member_level = $plugin_sns->getLevel($sns_member['openid']); 
                        //修改社区会员等级                       
                        if($snsdata['level'] != $sns_member['level']){
                            $new_snslevel = pdo_fetch('select * from ' . tablename('elapp_shop_sns_level') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $snsdata['level'], ':uniacid' => $_W['uniacid']));
                            $sns_log .= "<br/>社区等级：" . $sns_member_level['levelname']." -> ".$new_snslevel['levelname'];
                        }
                        //修改社区会员是否强制不自动升级
                        if($snsdata['notupgrade'] != $sns_member['notupgrade']){                            
                            $sns_log .= "<br/>强制不自动升级：" . (!empty($sns_member['notupgrade'])?'允许自动升级':'强制不自动升级')." -> ".(!empty($snsdata['notupgrade'])?'允许自动升级':'强制不自动升级');
                        }
                        //修改黑名单
                        if($snsdata['isblack'] != $sns_member['isblack']){                            
                            $sns_log .= "<br/>社区黑名单：" . (!empty($sns_member['isblack'])?'是':'否')." -> ".(!empty($snsdata['isblack'])?'是':'否');
                        }
                        plog('sns.member.edit',$sns_log);
                        pdo_update('elapp_shop_sns_member', $snsdata, array('id' => $sns_member['id'], 'uniacid' => $_W['uniacid']));
                    }
                }
            }

            //更新用户会员卡信息
            //com('wxcard')->updateMemberCardByOpenid($member['openid']);
            com_run('wxcard::updateMemberCardByOpenid',$member['openid']);

            show_json(1);
        }
        include $this->template('member/list/detail');
    }

    function view() {
        global $_W, $_GPC;
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $shop = $_W['shopset']['shop'];
        $id = intval($_GPC['id']);
        $member = m('member')->getMember($id);

        #分销
        $hascommission = false;
        $plugin_com = p('commission');
        if ($plugin_com) {
            $plugin_com_set = $plugin_com->getSet();
            $hascommission = !empty($plugin_com_set['level']);
        }
        if ($hascommission) {
            $agentlevels = $plugin_com->getLevels();
            $commission = $plugin_com->getInfo($id, array('total', 'pay'));
            $member['com_commission_total'] = $commission['commission_total'];
        }

        #虚店店员
        $hasclerk = false;
        $plugin_clerk = p('clerk');
        if ($plugin_clerk) {
            $plugin_clerk_set = $plugin_clerk->getSet();
            $hasclerk = !empty($plugin_clerk_set['level']);
        }        
        if ($hasclerk) {
            $clerklevels_res = app(ClerkLevelLogic::class)->getClerkLevels();
            $clerklevels = $clerklevels_res['data'];
            $clerk = $plugin_clerk->getInfo($id, array('total', 'pay'));
            $member['clerk_commission_total'] = $clerk['commission_total'];
        }

        #医生
        $hasdoctor = false;
        $plugin_doctor = p('doctor');
        if ($plugin_doctor) {
            $plugin_doctor_set = $plugin_doctor->getSet();
            $hasdoctor = !empty($plugin_doctor_set['level']);
        }        
        if ($hasdoctor) {
            $doctor_levels = $plugin_doctor->getLevels();
            $doctor = $plugin_doctor->getInfo($id, array('total', 'pay'));
            $member['doctor_commission_total'] = $doctor['commission_total'];
        }

        #虚店店长
        $hasvrshop = false;
        $plugin_vrshop = p('vrshop');
        if ($plugin_vrshop) {
            $plugin_vrshop_set = $plugin_vrshop->getSet();
            $hasvrshop = !empty($plugin_vrshop_set['level']);
        }       
        if ($hasvrshop) {
            $owner_levels = $plugin_vrshop->getLevels();
            $owner = $plugin_vrshop->getInfo($id, array('total', 'pay'));
            $member['owner_commission_total'] = $owner['commission_total'];
        }

        #帮扶分红
        $hasmentor = false;
        $plugin_mentor = p('mentor');
        if ($plugin_mentor) {
            $plugin_mentor_set = $plugin_mentor->getSet();
            $hasmentor = !empty($plugin_mentor_set['level']);
        }

        #合伙人
        $hascopartner = false;
        $plugin_copartner = p('copartner');
        if ($plugin_copartner) {
            $plugin_copartner_set = $plugin_copartner->getSet();
            $hascopartner = !empty($plugin_copartner_set['level']);
        }
        if ($hascopartner) {
            $copartnerlevels = $plugin_copartner->getLevels();
            $copartner = $plugin_copartner->getInfo($id, array('total', 'pay'));
            $member['copartner_commission_total'] = $copartner['commission_total'];
        }
        

        #会员分享 Hlei 20210427
        $hasuserpromote = false;
        $plugin_userpromote = p('userpromote');
        if ($plugin_userpromote) {
            $plugin_userpromote_set = $plugin_userpromote->getSet();
            $hasuserpromote = !empty($plugin_userpromote_set['open']);
        }

        #股东分红
        $plugin_globonus = p('globonus');
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !empty($plugin_globonus_set['open']);
        }
        if ($hasglobonus) {
            $partnerlevels = $plugin_globonus->getLevels();
            $bonus = $plugin_globonus->getBonus($member['openid'],array('ok'));
            $member['bonusmoney'] = $bonus['ok'];
        }

        //联合创始人
        $plugin_author = p('author');
        if ($plugin_author) {
            $plugin_author_set = $plugin_author->getSet();
            $hasauthor = !empty($plugin_author_set['open']);
        }

        //区域代理
        $plugin_abonus = p('abonus');
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !empty($plugin_abonus_set['open']);
        }
        if ($hasabonus) {
            $aagentlevels = $plugin_abonus->getLevels();
            $bonus = $plugin_abonus->getBonus($member['openid'],array('ok','ok1','ok2','ok3'));
            $member['abonus_ok'] = $bonus['ok'];
            $member['abonus_ok1'] = $bonus['ok1'];
            $member['abonus_ok2'] = $bonus['ok2'];
            $member['abonus_ok3'] = $bonus['ok3'];

            $member['aagentprovinces'] = iunserializer($member['aagentprovinces']);
            $member['aagentcitys'] = iunserializer($member['aagentcitys']);
            $member['aagentareas'] = iunserializer($member['aagentareas']);
        }

        //社区
        $plugin_sns = p('sns');
        if ($plugin_sns) {
            $plugin_sns_set = $plugin_sns->getSet();
            $sns_member = pdo_fetch('select * from '.tablename('elapp_shop_sns_member')." where openid=:openid and uniacid=:uniacid limit 1",array(':openid'=>$member['openid'],':uniacid'=>$_W['uniacid']));
            $sns_member['postcount'] = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_sns_post').' where uniacid=:uniacid and openid=:openid and pid=0 and deleted = 0 and checked=1',array(':uniacid'=>$_W['uniacid'],':openid'=>$member['openid']));
            $sns_member['replycount'] = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_sns_post').' where uniacid=:uniacid and openid=:openid and pid>0 and deleted = 0 and checked=1',array(':uniacid'=>$_W['uniacid'],':openid'=>$member['openid']));

            $hassns = !empty($sns_member);
            if($hassns){
                $snslevels = $plugin_sns->getLevels();
            }
        }       
        
        //上级分销商
        if (!empty($member['agentid'])) {
            $parentagent = m('member')->getMember($member['agentid']);
        }
        //上级店员
        if (!empty($member['clerk_id'])) {
            $parentclerk = m('member')->getMember($member['clerk_id']);
        }
        //上级医生
        if (!empty($member['doctor_id'])) {
            $parentdoctor = m('member')->getMember($member['doctor_id']);
        }
        //上级店长
        if (!empty($member['owner_id'])) {
            $parentowner = m('member')->getMember($member['owner_id']);
        }        
        //获取所属合伙人
        if (!empty($member['copartner_id'])) {
            $parentcopartner = $plugin_copartner->getCopartnerUserInfo($member['copartner_id']);
            $parentcopartner['thumb'] = $parentcopartner['logo'];
            $parentcopartner['nickname'] = $parentcopartner['mcnname'];
        }
        //获取所属合伙人员工UID
        if (!empty($member['copartner_account_id'])) {
            $copartnerAccount = $plugin_copartner->isCopartnerAccount($member['copartner_account_id']);
            $parentCopartnerAccount = m('member')->getMember($copartnerAccount['openid']);
            $parentCopartnerAccount['thumb'] = $parentCopartnerAccount['avatar'];
            $parentCopartnerAccount['nickname'] = $parentCopartnerAccount['realname'];
        }        
        //获取上级分享会员
        if (!empty($member['onmid'])) {
            $parentonmember = m('member')->getMember($member['onmid']);
        }
        //获取最后邀请人
        if (!empty($member['inviter_id'])) {
            $inviterMember = m('member')->getMember($member['inviter_id']);
        }

        $order = pdo_fetch('select finishtime from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status>=1 Limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $member['last_ordertime'] = $order['finishtime'];
        $member['self_ordercount'] = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $member['self_ordermoney'] = pdo_fetchcolumn('select sum(price) from ' . tablename('elapp_shop_order') . ' where uniacid=:uniacid and openid=:openid and status=3', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
        $followed = m('user')->followed($member['openid']);

        //自定义表单
        $diyform_flag = 0;
        $diyform_flag_commission = 0;
        $diyform_flag_globonus = 0;
        $diyform_flag_abonus = 0;
        $diyform_flag_clerk = 0;//店员
        $diyform_flag_doctor = 0;//店员
        $diyform_flag_owner = 0;//店长
        $diyform_flag_copartner = 0;//合伙人
        $diyform_plugin = p('diyform');
        if ($diyform_plugin) {
            //会员
            if (!empty($member['diymemberdata'])) {
                $diyform_flag = 1;
                $fields = iunserializer($member['diymemberfields']);
            }

            //分销
            if (!empty($member['diycommissiondata'])) {
                $diyform_flag_commission = 1;
                $cfields = iunserializer($member['diycommissionfields']);
            }
            //股东分红
            if (!empty($member['diyglobonusdata'])) {
                $diyform_flag_globonus = 1;
                $gfields = iunserializer($member['diyglobonusfields']);
            }
            //区域代理
            if (!empty($member['diyaagentdata'])) {
                $diyform_flag_abonus = 1;
                $aafields = iunserializer($member['diyaagentfields']);
            }
            //店员
            if (!empty($member['diy_clerk_data'])) {
                $diyform_flag_clerk = 1;
                $clerkfields = iunserializer($member['diy_clerk_fields']);
            }
            //医生
            if (!empty($member['diy_doctor_data'])) {
                $diyform_flag_doctor = 1;
                $doctorfields = iunserializer($member['diy_doctor_fields']);
            }
            //店长
            if (!empty($member['diy_owner_data'])) {
                $diyform_flag_owner = 1;
                $ownerfields = iunserializer($member['diy_owner_fields']);
            }
            //合伙人
            if (!empty($member['diycopartnerdata'])) {
                $diyform_flag_copartner = 1;
                $copartnerfields = iunserializer($member['diy_copartner_fields']);
            }
        }
        $groups = m('member')->getGroups();
        $levels = m('member')->getLevels();
        $openbind = false;
        if((empty($_W['shopset']['app']['isclose']) && !empty($_W['shopset']['app']['openbind'])) || !empty($_W['shopset']['wap']['open'])){
            $openbind = true;
        }

        include $this->template();
    }

    /**
     * 获取用户的所有订单ids
     * @param $openid
     * @return mixed|string
     */
    private function getMemberOrderids($openid) {
        $sql = "SELECT GROUP_CONCAT(id) as ids FROM ims_elapp_shop_order where openid=:openid and uniacid=:uniacid";
        $m = m('member')->getMember(238);
        $ids = pdo_fetch($sql, array('openid' => $m['openid'], 'uniacid' => $m['uniacid']));
        return $ids['ids'] ?? '';
    }
    function clear_user_data() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $member = pdo_fetch('select id,openid from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($member)) {
            show_json(0, '会员不存在!' . $id);
        }

        $zeros = [
            'mobileverify','level','agentid','agenttime','status','isagent','clickcount','agentlevel',
            'isaagent','aagentlevel','aagenttime','aagentstatus','aagentblack',
            'isheads','headsid','headsstatus','headstime',
            'onmid','fix_onmid','onmid_create_time','inviter_id','inviter_id_create_time',
            'is_owner','owner_id','owner_status','owner_create_time','owner_level','fix_owner_id','owner_id_create_time',
            'is_clerk','clerk_id','clerk_status','clerk_create_time','clerk_level','fix_clerk_id','clerk_black','clerk_id_create_time','is_self_share','isNotBecomeClerk','clerk_apply_time',
            'is_copartner','copartner_id','copartner_status','copartner_create_time','copartner_account_id','fix_copartner_id','copartner_black','copartner_id_create_time','copartner_account_id_create_time',
            'is_mentor','mentor_id','mentor_status','mentor_create_time','mentor_id_create_time',
            'is_doctor','doctor_id','doctor_status','doctor_create_time','doctor_level','fix_doctor_id','doctor_black','pharmacist_apply_time','pharmacist_create_time','pharmacist_status','is_pharmacist','is_instructor',
            'org_id','org_sub_id','copartner_user_path'
        ];
        $emptys = [
            'groupid'
        ];
        foreach ($zeros as $v) {
            $update[$v] = 0;
        }
        foreach ($emptys as $v) {
            $update[$v] = '';
        }
        $where = ['id'=>$id, 'uniacid'=>$_W['uniacid']];
        pdo_update('elapp_shop_member', $update, $where);

        // 删除会员卡
        $update = ['del_time'=>time(),'isdelete'=>1];
        $where = ['openid'=>$member['openid'], 'uniacid'=>$_W['uniacid'],'isdelete'=>0,'del_time'=>0];
        pdo_update('elapp_shop_member_card_history', $update, $where);

        // 清空订单
        $where = ['openid'=>$member['openid'], 'uniacid'=>$_W['uniacid']];
        pdo_delete('elapp_shop_order', $where);
        pdo_delete('elapp_shop_order_goods', $where);
        pdo_delete('elapp_shop_member_card_order', $where);
        pdo_delete('elapp_shop_member_card_order_card', $where);
        pdo_delete('elapp_shop_member_card_history', $where);
        pdo_delete('elapp_shop_member_servicefee_order', $where);
        pdo_delete('elapp_shop_member_servicefee_order_fee', $where);

        $where = ['mid'=>$member['id'], 'uniacid'=>$_W['uniacid']];
        pdo_delete('elapp_shop_clerk_apply', $where);
        pdo_delete('elapp_shop_mentor_apply', $where);
        pdo_delete('elapp_shop_copartner_apply', $where);

        // 清理合伙人数据
        $cop = pdo_get('elapp_shop_copartner_user', ['openid'=>$member['openid']]);
        if ($cop) {
            $where = ['openid'=>$member['openid']];
            pdo_delete('elapp_shop_copartner_user', $where);
            pdo_delete('elapp_shop_copartner_reg', $where);
            $where = ['copartner_id'=>$cop['id']];
            pdo_delete('elapp_shop_copartner_account', $where);
            $where = ['copartner_id'=>$cop['id']];
            pdo_delete('elapp_shop_virtual_point_copartner_set', $where);
        }

        $order_ids = $this->getMemberOrderids($member['openid']);
        if ($order_ids) {
            $order_ids = explode(',', $order_ids);
            // 清空订单结算记录
            // todo 添加clerk所有收益 key 方法
            $where = ['belong_to'=>$member['id'], 'role_id'=>['clerk','clerk_2','mentor', 'doctor','activity_1_1','activity_1_2']];
            pdo_delete('elapp_shop_settle_order', $where);
            $where = ['orderid'=> $order_ids,'order_type'=>'goods'];
            pdo_delete('elapp_shop_settle_order_handle_record', $where);
            $where = ['order_id'=> $order_ids,'order_type'=>SettleModel::ORDER_TYPE_GOODS_ID];
            pdo_delete('elapp_shop_settle_order_oids', $where);

            if ($cop) {
                // todo yh 添加clerk所有收益 key 方法
//                $where = ['belong_to'=>$cop['id'], 'role_id' => [
//                    'copartner',
//                    'copartner_refund',
//                    'copartner_withdraw_service_fee',
//                    'copartner_withdraw_service_fee_refund',
//                    'copartner_9800_first_gift',
//                    'copartner_9800_first_gift_refund',
//                    'copartner_mentor',
//                    'copartner_mentor_refund',
//                ]];
//                pdo_delete('elapp_shop_settle_order', $where);
//                $where = ['orderid'=> $order_ids,'order_type'=>'goods'];
//                pdo_delete('elapp_shop_settle_order_handle_record', $where);
//                $where = ['order_id'=> $order_ids,'order_type'=>SettleModel::ORDER_TYPE_GOODS_ID];
//                pdo_delete('elapp_shop_settle_order_oids', $where);
            }
        }

        // 提现记录
        $where = ['openid'=>$member['openid']];
        pdo_delete('elapp_shop_settle_withdraw_apply', $where);

        // 删除积分
        $where = ['user_id'=>$member['id'], 'user_type'=>0];
        pdo_delete('elapp_shop_virtual_point_user', $where);
        pdo_delete('elapp_shop_virtual_point_user_record', $where);
        if ($cop) {
            $where = ['user_id'=>$cop['id'], 'user_type'=>1];
            pdo_delete('elapp_shop_virtual_point_user', $where);
            pdo_delete('elapp_shop_virtual_point_user_record', $where);
        }

        // 删除SDM数据
        $where = ['member_id'=>$member['id']];
        pdo_update('elapp_shop_sandimeng_member', ['member_id' => 0, 'update_at' => 0], $where);
        //todo 是否有医生结算表??
        //pdo_delete('elapp_shop_tcmd_doctor_apply', $where);

        // clerk_id = mid
        //pdo_update('elapp_shop_member', ['clerk_id'=>0], ['clerk_id'=>$member['id'], 'uniacid'=>$_W['uniacid']]);
        // copartner_id = 0
        // copartner_account_id = 0
        // onmid = 0
        //pdo_update('elapp_shop_member', ['onmid'=>0], ['onmid'=>$member['id'], 'uniacid'=>$_W['uniacid']]);
        // fix_onmid = 0
        // fix_clerk_id = 0
        // owner_id = 0
        // mentor_id = 0
        // inviter = 0

        show_json(1, '清理成功!');
    }

    /**
     * 重置订单关系
     * @return void
     */
    function re_calcute_order_relation() {
        global $_W, $_GPC;

        $id = intval($_GPC['id']);
        $member = pdo_fetch('select id,clerk_id,copartner_id,org_id,is_clerk,clerk_status from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($member)) {
            show_json(0, '会员不存在!' . $id);
        }
        $orders = pdo_getall('elapp_shop_order', ['member_id'=>$member['id'], 'uniacid'=>$_W['uniacid']],['id', 'clerk_id','copartner_id','org_id','createtime']);

        $update_count = 0;
        $set = p('clerk')->getSet();
        // 内购没有开启，或用户是普通用户，那么将所有的订单设置为用户当前的上级
        if (empty($set['selfbuy']) || $member['is_clerk'] == 0 || $member['clerk_status'] == 0) {
            foreach ($orders as $order) {
                $update = ['clerk_id'=>$member['clerk_id'], 'copartner_id'=>$member['copartner_id'], 'copartner_account_id'=>$member['copartner_account_id']??0, 'org_id'=>$member['org_id']];
                pdo_update('elapp_shop_order', $update, ['id'=>$order['id']]);
            }
        } else {
            // 如果用户是店员，那么将用户成为店员前的订单设置为用户的上级，后面的订单设置为自己的订单
            $clerk = m('member')->getMember($member['clerk_id']);
            foreach ($orders as $order) {
                if ($order['createtime'] < $member['clerk_create_time']) {
                    $update = ['clerk_id'=>$clerk['id'], 'copartner_id'=>$clerk['copartner_id'],'copartner_account_id'=>$clerk['copartner_account_id']??0, 'org_id'=>$clerk['org_id']];
                    pdo_update('elapp_shop_order', $update, ['id'=>$order['id']]);
                } else {
                    $update = ['clerk_id'=>$member['id'], 'copartner_id'=>$member['copartner_id'],'copartner_account_id'=>$member['copartner_account_id']??0, 'org_id'=>$member['org_id']];
                    pdo_update('elapp_shop_order', $update, ['id'=>$order['id']]);
                }
            }
        }

        show_json(1, "更新成功!");
    }

    function cancle_user_account() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $member = pdo_fetch('select id,openid,is_cancel_account from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($member)) {
            show_json(0, '会员不存在!' . $id);
        }
        if ($member['is_cancel_account'] != 0) {
            show_json(0, '该会员已经申请注销!');
        }

        MemberCancelLogic::cancleUserAccountApply($id, 2);

        show_json(1, '注销成功!');
    }

    function delete() {

        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }

        $members = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_member') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);
        foreach ($members as $member) {
            //pdo_update('elapp_shop_member',array('agentid'=>0),array('agentid'=>$member['id']));
            pdo_delete('elapp_shop_member', array('id' => $member['id']));
            plog('member.list.delete', "删除会员  ID: {$member['id']} <br/>会员信息: {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
            if(method_exists(m('member'),'memberRadisCountDelete')) {
                m('member')->memberRadisCountDelete(); //清除会员统计radis缓存
            }
        }
        show_json(1, array('url' => referer()));
    }

    function setblack() {

        global $_W, $_GPC;

        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        $members = pdo_fetchall("select id,openid,nickname,realname,mobile from " . tablename('elapp_shop_member')  . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);

        $black = intval($_GPC['isblack']);
        foreach($members as $member) {
            if (!empty($black)) {
                pdo_update('elapp_shop_member', array('isblack' => 1), array('id' => $member['id']));
                plog('member.list.edit', "设置黑名单 <br/>用户信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
            } else {
                pdo_update('elapp_shop_member', array('isblack' => 0), array('id' => $member['id']));
                plog('member.list.edit', "取消黑名单 <br/>用户信息:  ID: {$member['id']} /  {$member['openid']}/{$member['nickname']}/{$member['realname']}/{$member['mobile']}");
            }
        }
        show_json(1);
    }

    function changelevel() {
        global $_W, $_GPC;

        if($_W['ispost']){
            $toggle = trim($_GPC['toggle']);
            $ids = $_GPC['ids'];
            $levelid = $_GPC['level'];
            !strpos($levelid,',') && $levelid = intval($_GPC['level']);
            if(empty($ids) || !is_array($ids)){
                show_json(0, "请选择要操作的会员");
            }
            if(empty($toggle)){
                show_json(0, "请选择要操作的类型");
            }
            $ids = array_filter($ids);
            $idsstr = implode(',', $ids);
            $loginfo = "批量修改";
            if($toggle=='group'){
                if(!empty($levelid)){
                    $levelid_arr = explode(',',$levelid);
                    if(!empty($levelid_arr)){
                        foreach ($levelid_arr as $id){
                            $group = pdo_fetch('select * from ' . tablename('elapp_shop_member_group') . ' where id = :id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
                            if(empty($group)){
                                show_json(0, "此分组不存在");
                            }
                        }
                    }else{
                        show_json(0, "此分组不存在");
                    }
                }else{
                    $group = array('groupname'=>'无分组');
                }

                $loginfo .= "用户分组 分组名称：".$group['groupname'];
            }elseif($toggle=='level'){
                if(!empty($levelid)) {
                    $level = pdo_fetch('select * from ' . tablename('elapp_shop_member_level') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $levelid, ':uniacid' => $_W['uniacid']));
                    if (empty($level)) {
                        show_json(0, "此等级不存在");
                    }
                }else{
                    $set = m('common')->getSysset();
                    $level = array('levelname'=>empty($set['shop']['levelname']) ? '普通等级' : $set['shop']['levelname']);
                }
                $arr = array('level'=>$levelid);
                $loginfo .= "用户等级 等级名称：".$level['levelname'];
            }

            $changeids = array();

            $members = pdo_fetchall("select id,openid,nickname,realname,mobile from " . tablename('elapp_shop_member')  . " WHERE id in( $idsstr ) AND uniacid=" . $_W['uniacid']);
            if(!empty($members)){
                foreach ($members as $member) {
                    if ($toggle=='group'){
                        m('member')->setGroups($member['id'],$levelid,'管理员设置批量分组');
                    }else{
                        pdo_update('elapp_shop_member', $arr, array('id' => $member['id']));
                        $changeids[] = $member['id'];
                    }
                }
            }

            if(!empty($changeids)){
                $loginfo .= " 用户id：". implode(",", $changeids);
                plog('member.list.edit', $loginfo);
            }
            show_json(1);
        }
        include $this->template();
    }

    function query() {

        global $_W, $_GPC;
        $kwd = trim($_GPC['keyword']);
        $wechatid = intval($_GPC['wechatid']);
        if (empty($wechatid)) {
            $wechatid = $_W['uniacid'];
        }
        $params = array();
        $params[':uniacid'] = $wechatid;
        $condition = " and uniacid=:uniacid";
        if (!empty($kwd)) {
            $condition.=" AND ( `nickname` LIKE :keyword or `realname` LIKE :keyword or `mobile` LIKE :keyword )";
            $params[':keyword'] = "%{$kwd}%";
        }
        $ds = pdo_fetchall('SELECT id,avatar,openid,nickname,openid,realname,mobile FROM ' . tablename('elapp_shop_member') . " WHERE 1 {$condition} order by createtime desc", $params);
        if ($_GPC['suggest']) {
            die(json_encode(array('value' => $ds)));
        }
        include $this->template();
    }
}
