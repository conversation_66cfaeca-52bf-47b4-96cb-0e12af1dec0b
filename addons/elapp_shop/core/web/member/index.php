<?php
namespace web\controller\member;

use web\controller\WebPage;

class IndexController extends WebPage
{
    public function main()
    {
        global $_W;
        if (!empty($_W['shopversion'])) {
            header('location: ' . webUrl('member/list/main'));
            exit();
        }
        include $this->template();
    }

    protected function selectMemberCreate($day = 0)
    {
        global $_W;
        $day = (int) $day;
        if ($day != 0) {
            $createtime1 = strtotime(date('Y-m-d', time() - ($day * 3600 * 24)));
            $createtime2 = strtotime(date('Y-m-d', time()));
        } else {
            $createtime1 = strtotime(date('Y-m-d', time()));
            $createtime2 = strtotime(date('Y-m-d', time() + (3600 * 24)));
        }
        $sql   = 'select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid and createtime between :createtime1 and :createtime2';
        $param = [':uniacid' => $_W['uniacid'], ':createtime1' => $createtime1, ':createtime2' => $createtime2];
        return pdo_fetchcolumn($sql, $param);
    }

    public function query()
    {
        global $_W, $_GPC;
        $uniacid = intval($_W['uniacid']);
        $kwd = trim($_GPC['keyword']);
        $pindex = max(1, intval($_GPC['page']));
        $psize = 8;
        $params = array();
        $params[':uniacid'] = $uniacid;
        $condition          = ' and uniacid=:uniacid ';
        if (!empty($kwd)) {
            $condition .= ' AND (realname LIKE :keywords or nickname LIKE :keywords or mobile LIKE :keywords or openid LIKE :keywords)';
            $params[':keywords'] = '%' . $kwd . '%';
        }
        $list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_member') . (' WHERE 1 ' . $condition . ' ORDER BY createtime DESC, id DESC LIMIT ') . ($pindex - 1) * $psize . ',' . $psize, $params);
        $total = pdo_fetchcolumn('SELECT COUNT(1) FROM ' . tablename('elapp_shop_member') . ' WHERE 1 ' . $condition . ' ', $params);
        $pager = pagination2($total, $pindex, $psize, '', array('before' => 5, 'after' => 4, 'ajaxcallback' => 'select_page', 'callbackfuncname' => 'select_page'));
        $list = set_medias($list, array('avatar'));
        foreach ($list as &$value) {
            $value['nickname'] = htmlspecialchars($value['nickname'], ENT_QUOTES);
        }
        unset($value);
        if ($_GPC['suggest']) {
            exit(json_encode(['value' => $list]));
        }
        include $this->template('member/query');
    }

    protected function ajaxnewmember($day = 0)
    {
        global $_GPC;
        global $_W;
        $day = (int) $day;
        if (isset($_GPC['day'])) {
            $day = (int) $_GPC['day'];
        }
        $param        = [':uniacid' => $_W['uniacid']];
        $member_count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=:uniacid', $param);
        $newmember    = $this->selectMemberCreate($day);
        return ['count' => (int) $newmember, 'rate' => empty($member_count) ? 0 : (int) number_format(round($newmember / $member_count, 3) * 100)];
    }

    protected function ajaxmembergender()
    {
        global $_W;
        $gender_array = [0, 0, 0];
        $sql_member   = 'select gender,count(gender) as gender_num from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid group by gender';
        $param_member = [':uniacid' => $_W['uniacid']];
        $member       = pdo_fetchall($sql_member, $param_member);
        foreach ($member as $key => $val) {
            if ($val['gender'] == -1) {
                $gender_array[0] += (int) $val['gender_num'];
            } else {
                $gender_array[$val['gender']] += (int) $val['gender_num'];
            }
        }
        return $gender_array;
    }
    protected function ajaxmemberlevel()
    {
        global $_W;
        $levels    = pdo_fetchall('select * from ' . tablename('elapp_shop_member_level') . ' where uniacid=:uniacid order by level asc', [':uniacid' => $_W['uniacid']], 'id');
        $levelname = [];
        foreach ($levels as $l) {
            $levelname[$l['id']] = $l['levelname'];
        }
        $levelname[0] = '普通等级';
        ksort($levelname);
        $sql_level    = 'select level,count(level) as level_num from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid group by level';
        $param_level  = [':uniacid' => $_W['uniacid']];
        $member_level = pdo_fetchall($sql_level, $param_level);
        $levels_array = [];

        foreach ($levelname as $lkey => $lvalue) {
            $levels_array[$lkey] = 0;
        }
        foreach ($member_level as $key => $val) {
            if (array_key_exists($val['level'], $levelname)) {
                $levels_array[$val['level']] = $val['level_num'];
            } else {
                $levels_array[0] += $val['level_num'];
            }
        }

        if (!array_key_exists(0, $levels_array)) {
            $levels_array[0] = 0;
        }

        $count = array_values($levels_array);
        $name  = array_values($levelname);
        $res   = [];

        foreach ($count as $key => $value) {
            $res[$key]['value'] = $value;
            $res[$key]['name']  = $name[$key];
        }
        return ['count' => $count, 'name' => $name, 'data' => $res];
    }

    protected function ajaxprovince()
    {
        global $_W;
        $province = pdo_fetchall('select province,count(province) as province_num from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid group by province', [':uniacid' => $_W['uniacid']]);
        $result   = [];
        foreach ($province as $array) {
            $array['province'] = preg_replace('/(市|省)(.*)/', '', $array['province']);
            $res               = ['name' => $array['province'], 'value' => (int) $array['province_num']];
            $result[]          = $res;
        }
        return $result;
    }

    public function ajaxall()
    {
        echo json_encode(['ajaxmembergender' => $this->ajaxmembergender(), 'ajaxmemberlevel' => $this->ajaxmemberlevel(), 'ajaxprovince' => $this->ajaxprovince(), 'ajaxnewmember0' => $this->ajaxnewmember(0), 'ajaxnewmember1' => $this->ajaxnewmember(1), 'ajaxnewmember7' => $this->ajaxnewmember(7)]);
    }

    public function ajaxmemberanalysis(){
        global $_GPC;
        global $_W;
        $day = (int) $day;
        if (isset($_GPC['day'])) {
            $day = (int) $_GPC['day'];
        }
        if ($day != 0) {
            $createtime1 = strtotime(date('Y-m-d', time() - ($day * 3600 * 24)));
            $createtime2 = strtotime(date('Y-m-d', time() + (3600 * 24)));
        } else {
            $createtime1 = strtotime(date('Y-m-d', time()));
            $createtime2 = strtotime(date('Y-m-d', time() + (3600 * 24)));
        }
        $sql   = 'select id,createtime,level from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid and createtime between :createtime1 and :createtime2';
        $param = [':uniacid' => $_W['uniacid'], ':createtime1' => $createtime1, ':createtime2' => $createtime2];
        $list =  pdo_fetchall($sql, $param);
        //print_r($list);
        $result = array();
        $i = $day - 1;
        while (0 <= $i) {
            $result['member'][date('m-d', time() - $i * 3600 * 24)] = 0;
            $result['vip'][date('m-d', time() - $i * 3600 * 24)] = 0;
            --$i;
        }
        if(!empty($list)){
            foreach($list as $key => $value){
                if (array_key_exists(date('m-d', $value['createtime']), $result['member'])) {
                    if($value['level'] == 0){
                        $result['member'][date('m-d', $value['createtime'])] += 1;    
                    }else{
                        $result['vip'][date('m-d', $value['createtime'])] += 1;    
                    }
                }
            }
        }
        echo json_encode(array(
            'key' => array_keys($result['member']),
            'member_value' => array_values($result['member']), 
            'vip_value' => array_values($result['vip'])
        ));
    }

    public function ajaxclerkanalysis(){
        
        global $_GPC;
        global $_W;
        $day = (int) $day;
        if (isset($_GPC['day'])) {
            $day = (int) $_GPC['day'];
        }
        if ($day != 0) {
            $createtime1 = strtotime(date('Y-m-d', time() - ($day * 3600 * 24)));
            $createtime2 = strtotime(date('Y-m-d', time() + (3600 * 24)));
        } else {
            $createtime1 = strtotime(date('Y-m-d', time()));
            $createtime2 = strtotime(date('Y-m-d', time() + (3600 * 24)));
        }
        $sql = "SELECT levelname,(
SELECT COUNT(*)
FROM ims_elapp_shop_member
WHERE clerk_level = 0 AND is_clerk = 1 and ims_elapp_shop_member.uniacid = :uniacid and createtime between :createtime1 and :createtime2) level_z_num,
from_unixtime(ims_elapp_shop_member.createtime ,'%Y%m%d') AS ddd,
COUNT(*) AS cc,
ims_elapp_shop_member.createtime,clerk_level
FROM ims_elapp_shop_clerk_level LEFT JOIN ims_elapp_shop_member on clerk_level = ims_elapp_shop_clerk_level.id WHERE  is_clerk = 1  and ims_elapp_shop_member.uniacid = :uniacid and createtime between :createtime1 and :createtime2
GROUP BY ddd,ims_elapp_shop_clerk_level.level
ORDER BY ims_elapp_shop_clerk_level.`level` ASC 
";
        
        $param = [':uniacid' => $_W['uniacid'], ':createtime1' => $createtime1, ':createtime2' => $createtime2];
        $list =  pdo_fetchall($sql, $param);
        $level_data = pdo_fetchall('select levelname,id FROM ims_elapp_shop_clerk_level where uniacid = :uniacid',[':uniacid' => $_W['uniacid']]);
        $result_date = array();
        $i = $day - 1;
        while (0 <= $i) {
            $result_date[date('m-d', time() - $i * 3600 * 24)] = 0;
            --$i;
        }
        $result = [];
        $items = [];
        foreach($level_data as  $value){
            $items[$value['id']]['name'] = $value['levelname'];
            $items[$value['id']]['data'] = $result_date;
        }
        
        if(!empty($list)){
            foreach($list as $key => $value) {
                $items[$value['clerk_level']]['name'] = $value['levelname'];
                $items[$value['clerk_level']]['clerk_level'] = $value['clerk_level'];
                if (!isset($items[$value['clerk_level']]['data'])) {
                    $items[$value['clerk_level']]['data']= $result_date;
                }
                $items[$value['clerk_level']]['data'][date('m-d', $value['createtime'])] += $value['cc'];
            }
        }
     
        $label = [];
        foreach($items as $value){
            $value['data'] = array_values($value['data']); //[rand(1,10),rand(1,7),rand(1,4),rand(0,10),rand(1,6),rand(1,3),rand(1,2)];
            $result[] = $value;
            $label[] = $value['name'];
        }
        echo json_encode(array(
            'key' => array_keys($result_date),
            'label' => $label,
            'list'=>$result
        ));
    }

    public function ajaxanalysis()
    {
        global $_GPC;
        global $_W;
        $param                   = [':uniacid' => $_W['uniacid']];
        $member_count            = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=:uniacid and level = 0', $param);
        $member_vip_count        = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=:uniacid and level > 0 ', $param);
        $clerk_count             = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=:uniacid and clerk_status > 0 and is_clerk = 1 and clerk_level = 0 ', $param);
        $clerk_vip_count         = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=:uniacid and clerk_status > 0 and is_clerk = 1 and clerk_level > 0 ', $param);
        $member                  = ['member_count' => (int) $member_count, 'member_vip_count' => (int) $member_vip_count, 'rate' => empty($member_vip_count) ? 0 : (int) number_format(round($member_vip_count / $member_count, 3) * 100)];
        $clerk                   = ['clerk_count' => (int) $clerk_count, 'clerk_vip_count' => (int) $clerk_vip_count, 'rate' => empty($clerk_vip_count) ? 0 : (int) number_format(round($clerk_vip_count / $clerk_count, 3) * 100)];
        $copartner_count         = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_copartner_user') . ' where uniacid=:uniacid', $param);
        $copartner_count_level_1 = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_copartner_user') . ' where uniacid=:uniacid and levelid = 1', $param);
        $copartner_count_level_2 = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_copartner_user') . ' where uniacid=:uniacid and levelid = 2', $param);
        $copartner               = ['count' => $copartner_count, 'copartner_count_level_1' => $copartner_count_level_1, 'copartner_count_level_2' => $copartner_count_level_2];
        $result                  = ['member' => $member, 'clerk' => $clerk, 'copartner'=>$copartner,'ajaxprovince'=>$this->ajaxprovince()];
        echo json_encode($result);
    }

    public function ajaxprovince_list(){
        global $_W;
        //$province = pdo_fetchall('select city,count(city) as city_num from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid group by city', [':uniacid' => $_W['uniacid']]);
        $province = pdo_fetchall('select province,count(province) as province_num from ' . tablename('elapp_shop_member') . ' where uniacid = :uniacid group by province', [':uniacid' => $_W['uniacid']]);
        $result   = [];
        foreach ($province as $array) {
            $array['province'] = preg_replace('/(市|省)(.*)/', '', $array['province']);
            if($array['province']){
                $res               = ['name' => $array['province'], 'value' => (int) $array['province_num']];
                $result[]          = $res;
            }
        }
        echo json_encode($result);
    }

    //
    public function offlineLogin(){
        global $_GPC,$_W;
        $id = intval($_GPC['id']);
        $member = pdo_fetch('select id,openid,mobile,pwd,salt from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        m('account')->clearLoginCookie();
        m('account')->setLogin($member);
        $url =  mobileUrl('member/index','',true);
        header("Location: $url", true, 302);
        exit();
    }

}
