<?php
namespace web\controller\statistics;
use app\core\com\logic\statistics\GoodsSaleStatisticsLogic;
use web\controller\WebPage;
class GoodsTransController extends WebPage{
	public function main(){
		global $_W,$_GPC;
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $condition = [];// 查询条件数组
        $condition['page'] = intval($_GPC['page']);// 页码
        $condition['psize'] = 20;// 每页数量
        $condition['datetime'] = $_GPC['datetime'];// 时间选择器含['start','end']
        $condition['keywords'] = trim($_GPC['keywords']);// 关键字搜索
        $condition['orderby'] = trim($_GPC['orderby']);// 排序 0销售额 1销售量
        $condition['orderway'] = trim($_GPC['orderway']);// 排序方式 0降序 1升序
        $condition['export'] = intval($_GPC['export']);// 导出数据
        $result = app(GoodsSaleStatisticsLogic::class)->getGoodsSaleTrans($condition);
        if (isset($result['data']) && is_array($result['data'])) {
            extract($result['data']);
        }

		load()->func('tpl');
		include $this->template('statistics/goods_trans');
	}
}