<?php
namespace web\controller\statistics;
use app\core\com\logic\statistics\GoodsSaleStatisticsLogic;
use web\controller\WebPage;
class GoodsController extends WebPage{
	public function main(){
		global $_W,$_GPC;
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
        $condition = [];// 查询条件数组
        $condition['page'] = $pindex;// 页码
        $condition['psize'] = $psize;// 每页数量
        $condition['datetime'] = $_GPC['datetime'];// 时间选择器含['start','end']
        $condition['keywords'] = trim($_GPC['keywords']);// 关键字搜索
        $condition['orderby'] = trim($_GPC['orderby']);// 排序 0销售额 1销售量
        $condition['orderway'] = trim($_GPC['orderway']);// 排序方式 0降序 1升序
        $condition['export'] = intval($_GPC['export']);// 导出数据
        $comefrom = trim($_GPC['comefrom']);// 终端来源 未必用到，controller继承不一样，先保留
        $result = app(GoodsSaleStatisticsLogic::class)->getGoodsSaleDetail($condition);
        if (isset($result['data']) && is_array($result['data'])) {
            extract($result['data']);
        }
		load()->func('tpl');
		include $this->template('statistics/goods');
	}
}