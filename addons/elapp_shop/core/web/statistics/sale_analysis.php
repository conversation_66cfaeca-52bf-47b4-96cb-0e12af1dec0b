<?php
namespace web\controller\statistics;
use web\controller\WebPage;
class SaleAnalysisController extends WebPage{
	public function main(){
		global $_W,$_GPC;
		function sale_analysis_count($sql){
			$c = pdo_fetchcolumn($sql);
			return intval($c);
		}
		$filter = new \app\model\FilterModel();
		$member_cond = $filter->getConditionsSql(\app\model\FilterModel::TABLE_MEMBER);
		$order_cond = $filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER);
		$goods_cond = $filter->getConditionsSql(\app\model\FilterModel::TABLE_SHOP_GOODS);

		$member_count = sale_analysis_count('SELECT count(*) FROM ' . tablename('elapp_shop_member') . '   WHERE uniacid = \'' . $_W['uniacid'] . '\' ' . $member_cond);
		$orderprice = sale_analysis_count('SELECT sum(price) FROM ' . tablename('elapp_shop_order') . ' WHERE status>=1 and uniacid = \'' . $_W['uniacid'] . '\' '. $order_cond);
		$ordercount = sale_analysis_count('SELECT count(*) FROM ' . tablename('elapp_shop_order') . ' WHERE status>=1 and uniacid = \'' . $_W['uniacid'] . '\' ' . $order_cond);
		$viewcount = sale_analysis_count('SELECT sum(viewcount) FROM ' . tablename('elapp_shop_goods') . ' WHERE uniacid = \'' . $_W['uniacid'] . '\' ' . $goods_cond);
		$member_buycount = sale_analysis_count('select count(*) from ' . tablename('elapp_shop_member') . ' where uniacid=' . $_W['uniacid'] . $member_cond . ' and openid in ( SELECT distinct openid from ' . tablename('elapp_shop_order') . '   WHERE uniacid = \'' . $_W['uniacid'] . '\' and status>=1' . $order_cond . ' )');
		include $this->template('statistics/sale_analysis');
	}
}
