<?php
namespace web\controller\goods;
use web\controller\WebPage;


class PrescriptionController extends WebPage
{
	public function main()
	{
		global $_W, $_GPC;
		$uniacid = $_W['uniacid'];
		$params[':uniacid'] = $uniacid;
		$condition = ' and deleted_at=0';
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;

		if ($_GPC['status'] != '') {
			$condition .= ' and status=' . intval($_GPC['status']);
		}

        if ($_GPC['group_id'] != '') {
            $condition .= ' and group_id=' . intval($_GPC['group_id']);
        }

		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and name like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}

		$list = pdo_fetchall('SELECT id,uniacid,group_id,name,status,goods,displayorder FROM ' . tablename('elapp_shop_tcmd_prescription') . " WHERE uniacid=:uniacid " . $condition . ' order by displayorder desc,id desc limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);
        $groups = pdo_getall('elapp_shop_tcmd_prescription_group', ['uniacid' => $uniacid]);
        $groups = array_column($groups, null, 'id');
        foreach ($list as $k=>$item) {
            $goods = json_decode($item['goods'], true);
            $list[$k]['goods_count'] = count($goods);
            $list[$k]['group_name'] = $groups[$item['group_id']]['name'] ?? '';
        }

		$total = pdo_fetchcolumn('SELECT count(1) FROM ' . tablename('elapp_shop_tcmd_prescription') . ' WHERE uniacid=:uniacid ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add()
	{
		$this->post();
	}

	public function edit()
	{
		$this->post();
	}

	protected function post()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);
		$uniacid = intval($_W['uniacid']);

        $piclist = [];
		if (!empty($id)) {
            $item = pdo_get('elapp_shop_tcmd_prescription', ['id'=>$id,'uniacid' => $uniacid]);
            if ($item && isset($item['extend_info'])) {
                $item['extend_info'] = json_decode($item['extend_info'], true);
                $piclist = array_merge(array($item['thumb']), iunserializer($item['thumb_url']));
                $goodsids = json_decode($item['goods'], true);
                if (!empty($goodsids)) {
                    $goodsids = array_column($goodsids, null, 'id');
                    $ids = implode(',', array_column($goodsids, 'id'));
                    $goods = pdo_fetchall('select id,title,thumb from ' . tablename('elapp_shop_goods') . ' where id in (' . $ids. ') and status=1 and deleted=0 and uniacid=' . $_W['uniacid'] . ' order by instr(\'' . $ids . '\',id)');
                    foreach ($goods as $k=>$g) {
                        $goods[$k]['count'] = $goodsids[$g['id']]['count'];
                    }
                }
            }
        } else {
            $item['dosage_type'] = 1; // 默认为汤剂
        }

        $groups = pdo_getall('elapp_shop_tcmd_prescription_group', ['uniacid' => $uniacid]);

        // 加工剂型
        $dosage_types = [
            ['id'=> 1, 'name'=>'汤剂', 'options'=> [['id'=>1, 'text'=> '代煎'],['id'=> 0, 'text'=> '自煎']]],
            ['id'=> 5, 'name'=>'膏剂', 'options'=> [['id'=>7, 'text'=> '膏剂']]]
        ];
        $dosage_type_methods = [
            ['id'=> 0, 'name'=> '自煎', 'type'=> 1],['id'=>1, 'name'=> '代煎','type'=> 1],
            ['id'=>7, 'name'=> '膏剂', 'type'=> 5]
        ];

		if ($_W['ispost']) {
            $fields = ['id', 'uniacid', 'name', 'group_id', 'subname', 'thumbs','goodsids',
                       'dosage', 'dosage_type', 'dosage_type_method','primary_diagnosis', 'extend_info', 'created_userid', 'created_at',
                       'updated_at', 'status', 'displayorder','group_id'];
            // 过滤掉非必要的字段
            $data = array_intersect_key($_GPC, array_flip($fields));
            $data['name'] = trim($data['name']);
            $data['status'] = (intval($data['status']))?1:0;
            $data['displayorder'] = intval($data['displayorder']);

            $not_empty = [
                'name' => '药方名称',
                'subname' => '药方副标题',
                'group_id' => '药方分组',
                'thumbs' => '药方图片',
                'goodsids' => '药方商品',
                'dosage' => '药方用量',
                'primary_diagnosis' => '临床诊断',
                'extend_info' => '使用方法 / 每日剂量 / 使用频次',
            ];
            foreach ($not_empty as $key=>$title) {
                if (empty($data[$key])) {
                    if ($key =='group_id') {
                        show_json(0, array('message' => '请选择药方分组'));
                    }
                    show_json(0, array('message' => $title.'不能为空'));
                }
            }

            if (!in_array($data['dosage_type'], [1,5])) {
                show_json(0, array('message' => empty($data['dosage_type']) ? '加工剂型不能为空':'加工剂型错误'));
            }
            if (!in_array($data['dosage_type_method'], [0,1,7])) {
                show_json(0, array('message' => empty($data['dosage_type_method'])? '加工方式不能为空':'加工方式错误' . $data['dosage_type_method']));
            }

            $data['thumb'] = $data['thumbs'][0];
            unset($data['thumbs'][0]);
            $data['thumb_url'] = iserializer($data['thumbs']);
            unset($data['thumbs']);
            $data['goods'] = json_encode($data['goodsids']);
            $data['extend_info'] = json_encode($data['extend_info']);
            unset($data['goodsids']);
            $data['updated_at'] = time();

			if (!empty($item) && $item['id']) {
				pdo_update('elapp_shop_tcmd_prescription', $data, array('id' => $item['id']));
				plog('goods.prescription.edit', '修改平台药方 ID: ' . $id);
			}
			else {
				$data['uniacid'] = $uniacid;
                unset($data['id']);
                $data['created_at'] = time();
                $data['created_userid'] = $_W['uid'];

                pdo_insert('elapp_shop_tcmd_prescription', $data);
				$id = pdo_insertid();
				plog('goods.prescription.add', '添加平台药方 ID: ' . $id);
			}

			show_json(1, array('url' => webUrl('goods/prescription')));
		}

		include $this->template();
	}


	public function delete()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}

		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_tcmd_prescription') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);

        pdo_begin();
		foreach ($items as $item) {
            pdo_update('elapp_shop_tcmd_prescription', ['deleted_at'=>time()], array('id' => $item['id']));
			plog('goods.prescription.delete', '删除平台药方<br/>ID: ' . $item['id'] . '<br/>药方名称: ' . $item['name']);
		}

        pdo_commit();

		show_json(1, array('url' => webUrl('goods.prescription')));
	}

	public function status()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}

		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_tcmd_prescription') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);

		if (empty($item)) {
			$item = array();
		}

		foreach ($items as $item) {
			pdo_update('elapp_shop_tcmd_prescription', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('goods.prescription.edit', ('修改平台药方状态<br/>ID: ' . $item['id'] . '<br/>平台药方名称: ' . $item['name'] . '<br/>状态: ' . $_GPC['status']) == 1 ? '上架' : '下架');
		}

		show_json(1, array('url' => webUrl('goods.prescription')));
	}

    function query(){
        global $_W, $_GPC;
        $kwd = trim($_GPC['keyword']);
        $type = intval($_GPC['type']);

        $live = intval($_GPC['live']);

        $params = array();
        $params[':uniacid'] = $_W['uniacid'];
        $condition=" and status=1 and deleted=0 and uniacid=:uniacid";
        if (!empty($kwd)) {
            $condition.=" AND (`title` LIKE :keywords OR `keywords` LIKE :keywords OR `title_pinyin` LIKE :keywords)";
            $params[':keywords'] = "%{$kwd}%";
        }
        if (empty($type)) {
            $condition.=" AND `type` != 10 ";
        }else{
            $condition.=" AND `type` = :type ";
            $params[':type'] = $type;
        }

        // 云商品
        $condition .= ' and goodsClassID=2 and medicineClassID=2 and medicineAttributeID = 1 and is_cloud = 1 ';
        $ds = pdo_fetchall('SELECT id,title,thumb,marketprice,productprice,share_title,share_icon,description,minprice,costprice,stock,sales,islive,liveprice FROM ' . tablename('elapp_shop_goods') . " WHERE 1 {$condition} order by createtime desc", $params);
        foreach($ds as &$value){
            $value['share_title'] = htmlspecialchars_decode($value['share_title']);
            unset($value);
        }
        $ds = set_medias($ds, array('thumb','share_icon'));
        if($_GPC['suggest']){
            die(json_encode(array('value'=>$ds)));
        }
        include $this->template();
    }
}
