<?php
use app\com\enum\member\MemberTerminalEnum;
use app\core\com\logic\sale\coupon\CouponLogic;

global $_W, $_GPC;
$shopset_level = intval($_W['shopset']['commission']['level']);
$vrshopSet_level = intval($_W['shopset']['vrshop']['level']);
$clerkSet_level = intval($_W['shopset']['clerk']['level']);
$doctorSet_level = intval($_W['shopset']['doctor']['level']);
$copartnerSet_level = intval($_W['shopset']['copartner']['level']);
$id = intval($_GPC['id']);
if(!empty($id)){
    pdo_update('elapp_shop_goods',array('newgoods'=>0),array('id'=>$id));
}
if (p('userpromote')) {
    $userpromote_set = p('userpromote')->getSet();
}

$item = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_goods') . " WHERE id = :id and uniacid = :uniacid", array(':id' => $id,':uniacid'=>$_W['uniacid']));

// 获取终端的键值对映射（中文名 => 常量名）
$terminal = MemberTerminalEnum::getTerminalMap();
// 获取限制配置
if (!empty($item['limitation'])) {
    $item['limitation'] = json_decode($item['limitation'], true);
    // 获取终端上架状态
    $item['terminal'] = $item['limitation']['terminal'] ?? array();
    // 获取购买资格券
    $ticket_coupon_ids = !empty($item['limitation']['ticket_coupons']['ids']) && is_array($item['limitation']['ticket_coupons']['ids'])? $item['limitation']['ticket_coupons']['ids'] : [];
    $ticket_coupons = !empty($ticket_coupon_ids) ? app(CouponLogic::class)->queryCoupon($ticket_coupon_ids): [];
} else {
    $item['terminal'] = array();
}

$item['functions'] = unserialize($item['functions']);//功能主治参数设置转回数组hlei20210622
$item['isdiscount_time'] = date('Y-m-d H:i',$item['isdiscount_time']);
$item['isdiscount_time_start'] = date('Y-m-d H:i',$item['isdiscount_time_start']);//
$item['medicines'] = unserialize($item['medicines']);//药品信息

if(!empty($item)&&$item['type']==5&&!empty($item['opencard'])&&!empty($item['cardid'])){
    $card = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_goods_cards') . " WHERE id = :id and uniacid = :uniacid", array(':id' => $item['cardid'],':uniacid'=>$_W['uniacid']));
}
if(p('offic')){
    $item['officthumb'] = set_medias($item['officthumb']);
}
$status = $item['status'];
if(json_decode($item['labelname'],true)){
    $labelname = json_decode($item['labelname'],true);
}else{
    $labelname = unserialize($item['labelname']);
}
$endtime = empty($item['endtime']) ? date('Y-m-d H:i', time()) : date('Y-m-d H:i', $item['endtime']);
$item['statustimestart'] = $item['statustimestart']>0 ? $item['statustimestart'] : time();
$item['statustimeend'] = $item['statustimeend']>0 ? $item['statustimeend'] : strtotime('+1 month');

$intervalprices =iunserializer($item['intervalprice']);

if(empty($labelname)){
    $labelname = array();
}
//标签
foreach($labelname as $key => $value){
    $label[$key]['id'] = $value;
    $label[$key]['labelname'] = $value;
}
//药品剂型
$formtype = $item['medicines']['formType'];

//多商户
$merchid = 0;
$merch_plugin = p('merch');
if (!empty($item)) {
    if ($item['merchid'] > 0) {
        $merchid = intval($item['merchid']);
        if ($merch_plugin) {
            $merch_user = $merch_plugin->getListUserOne($merchid);
        }
    }
}
//获取供应商信息
$supplyid = 0;
$supply_plugin = p('supply');
if (!empty($item)) {
    if ($item['supplyid'] > 0) {
        $supplyid = intval($item['supplyid']);
        if ($supply_plugin) {
            $supply_user = $supply_plugin->getListUserOne($supplyid);
        }
    }
}
$memberSysset = m('common')->getSysset('member');//获取系统会员设置信息
$goodCodeSysset = m('common')->getSysset('share');//获取系统会员设置信息
$area_set = m('util')->get_area_config_set();
$new_area = intval($area_set['new_area']);
$address_street = intval($area_set['address_street']);

if (p('diyform')) {
    $diyform = p('diyform');
    $globalData = $diyform->globalData();
    extract($globalData);
    if (!empty($item['diysaveid'])) {
        $diyforminfo = $diyform->getDiyformInfo($item['diysaveid'], 0);
    }
}

$ccard_plugin = p('ccard');
$ccard = 0;
if ($ccard_plugin) {
    $ccard = 1;
}

$category = m('shop')->getFullCategory(true,true);
//会员等级
$levels = m('member')->getLevels();
foreach($levels as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$levels =array_merge(array(
    array(
        'id'=>0,
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['shop']['levelname'])?'默认会员':$_W['shopset']['shop']['levelname']
    )
),$levels);

//分销等级
$commission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_commission_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission1 asc");
foreach($commission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$commission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['commission']['levelname'])?'默认等级':$_W['shopset']['commission']['levelname']
    )
),$commission_level);

//虚店店员等级
$clerkCommission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_clerk_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission1 asc");
foreach($clerkCommission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$clerkCommission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['clerk']['levelname'])?'默认等级':$_W['shopset']['clerk']['levelname']
    )
),$clerkCommission_level);
//虚店店长等级
$ownerCommission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_vrshop_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission1 asc");
foreach($ownerCommission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$ownerCommission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['vrshop']['levelname'])?'默认等级':$_W['shopset']['vrshop']['levelname']
    )
),$ownerCommission_level);

//合伙人等级
$copartnerCommission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_copartner_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission1 asc");
foreach($copartnerCommission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$copartnerCommission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['copartner']['levelname'])?'默认等级':$_W['shopset']['copartner']['levelname']
    )
),$copartnerCommission_level);

//招商经理等级
$businessCommission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_copartner_business_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission asc");
foreach($businessCommission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$businessCommission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['copartner']['business_levelname'])?'默认等级':$_W['shopset']['copartner']['business_levelname']
    )
),$businessCommission_level);

//医生等级
$doctorCommission_level = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_doctor_level') . " WHERE uniacid = '{$_W['uniacid']}' ORDER BY commission asc");
foreach($doctorCommission_level as &$l){
    $l['key'] ='level'.$l['id'];
}
unset($l);
$doctorCommission_level =array_merge(array(
    array(
        'key'=>'default',
        'levelname'=>empty($_W['shopset']['doctor']['levelname'])?'默认等级':$_W['shopset']['doctor']['levelname']
    )
),$doctorCommission_level);

$groups = m('member')->getGroups();//会员分组
$com_virtual = com('virtual');
//获取营销主推级别
$saledata = m('common')->getPluginset('sale');
$reclevel = iunserializer($saledata['reclevel']);
$reclevel_1 = ((empty($reclevel['reclevel_1']) ? array() : $reclevel['reclevel_1']));

//处理商品表单
if ($_W['ispost']) {
    if (empty($id)){
        $goodstype = intval($_GPC['type']);
        $goodsClassID = intval($_GPC['goodsClassID']);//商品类型
        $medicineClassID = intval($_GPC['medicineClassID']);//药品类型
        $medicineAttributeID = intval($_GPC['medicineAttributeID']);//药品属性
    } else {
        $goodstype = intval($_GPC['goodstype']);
        $goodsClassID = intval($_GPC['goodsClassID']);//商品类型
        $medicineClassID = intval($_GPC['medicineClassID']);//药品类型
        $medicineAttributeID = intval($_GPC['medicineAttributeID']);//药品属性
    }
    if($goodstype!=1 && intval($_GPC['status'])==2){
        show_json(0,"赠品只能是实体商品");
    }
    if($_GPC['isverify'] ==2 && intval($_GPC['status'])==2){
        show_json(0,"赠品不支持核销");
    }
    if(intval($_GPC['hasoption']) == 1 && intval($_GPC['status'])==2){
        show_json(0,"赠品不支持多规格");
    }
    if($_GPC['isverify'] ==2 && $id){
        $nowtime = TIMESTAMP;
        $gift = pdo_fetch("select id,title from ".tablename('elapp_shop_gift')." WHERE goodsid = :goodsid and uniacid=:uniacid and endtime>=:endtime", array(':goodsid' => $id, ':uniacid' => $_W['uniacid'],':endtime' => $nowtime));
        if($gift) show_json(0,"已为此商品指定了赠品不支持线下核销");
    }

    $act_time = $_GPC['act_time'];

    // 如果会员折扣价低于商品价格，不允许提交
    if ($_GPC['hasoption'] == 0) {
        if ($_GPC['isdiscount_discounts'] > 0 && $_GPC['isdiscount_discounts'] < $_GPC['marketprice']) {
            show_json(0, '会员折扣价不能低于商品价格');
        }
    } else {
        if (is_array($_GPC['option']) || ($_GPC['option'] instanceof Traversable)) {
            foreach ($_GPC['option'] as $k => $v) {
                if ($v['isdiscount_discounts'] > 0 && $v['isdiscount_discounts'] < $v['marketprice']) {
                    show_json(0, '会员折扣价不能低于商品价格');
                }
            }
        }
    }

    $data = array(
        'uniacid' => intval($_W['uniacid']),//公众号ID
        'displayorder' => intval($_GPC['displayorder']),//排序
        'title' => trim($_GPC['goodsname']),//商品名称
        'title_pinyin' => trim($_GPC['title_pinyin']),//商品名称拼音
        'subtitle' => trim($_GPC['subtitle']),//副标题
        'shorttitle' => trim($_GPC['shorttitle']),//商品短标题
        'keywords' => trim($_GPC['keywords']),//关键字
        'thumb_first' => intval($_GPC['thumb_first']),//详情显示首图
        'showsales' => intval($_GPC['showsales']),//显示销量
        'type' => $goodstype,//商品类型
        'goodsClassID' => $goodsClassID,//商品类目hlei20210520
        'goodsspecs' => trim($_GPC['goodsspecs']),//商品品规hlei20210521
        'ispresell' => intval($_GPC['ispresell']),
        'presellover' => intval($_GPC['presellover']),
        'presellovertime' => intval($_GPC['presellovertime']) > 0 ? intval($_GPC['presellovertime']) : 0,
        'presellprice' => floatval($_GPC['presellprice']),
        'presellstart' => intval($_GPC['presellstart']),
        'presellend' => intval($_GPC['presellend']),
        'preselltimestart' => intval($_GPC['presellstart'])>0 ? strtotime($_GPC['preselltimestart']) : 0,
        'preselltimeend' => intval($_GPC['presellend']) > 0 ? strtotime($_GPC['preselltimeend']) : 0,
        'presellsendtype' => intval($_GPC['presellsendtype']),
        'presellsendstatrttime' => strtotime($_GPC['presellsendstatrttime']),
        'presellsendtime' => intval($_GPC['presellsendtime']),
        'labelname' => serialize($_GPC['labelname']),
        'isrecommand' => intval($_GPC['isrecommand']),
        'ishot' => intval($_GPC['ishot']),
        'isnew' => intval($_GPC['isnew']),
        'isdiscount' => intval($_GPC['isdiscount']),
        'isdiscount_title' => trim(mb_substr($_GPC['isdiscount_title'],0,5,'UTF-8')),
        'isdiscount_time_start' => strtotime($act_time['start']),
        'isdiscount_time' => strtotime($act_time['end']),
        'issendfree' => intval($_GPC['issendfree']),
        'isnodiscount' => intval($_GPC['isnodiscount']),
        'istime' => intval($_GPC['istime']),
        'timestart' => strtotime($_GPC['saletime']['start']),
        'timeend' => strtotime($_GPC['saletime']['end']),
        'description' => trim($_GPC['description']),
        'goodssn' => trim($_GPC['goodssn']),
        'unit' => trim($_GPC['unit']),
        'createtime' => TIMESTAMP,
        'stock' => intval($_GPC['stock']),
        'showtotal' => intval($_GPC['showtotal']),
        'totalcnf' => intval($_GPC['totalcnf']),
        'unite_total' => intval($_GPC['unite_total']),
        'marketprice' => $_GPC['marketprice'],
        'weight' => $_GPC['weight'],
        'costprice' => $_GPC['costprice'],
        'productprice' => trim($_GPC['productprice']),
        'productsn' => trim($_GPC['productsn']),
        'credit' => trim($_GPC['credit']),
        'maxbuy' => intval($_GPC['maxbuy']),
        'minbuy' => intval($_GPC['minbuy']),
        'is_minbuy_times_add' => intval($_GPC['is_minbuy_times_add']),
        'usermaxbuy' => intval($_GPC['usermaxbuy']),
        'hasoption' => intval($_GPC['hasoption']),
        'sales' => intval($_GPC['sales']),//销量
        'share_icon' => trim($_GPC['share_icon']),
        'share_title' => trim($_GPC['share_title']),
        'status' => $status!=2 ? intval($_GPC['status']) : $status ,
        'groupstype' => intval($_GPC['groupstype']),
        'virtualsend' => intval($_GPC['virtualsend']),
        'virtualsendcontent' => trim($_GPC['virtualsendcontent']),
        'buyshow' => intval($_GPC['buyshow']),//购买后可见
        'showlevels' => is_array($_GPC['showlevels']) ? implode(",", $_GPC['showlevels']) : '',//会员等级浏览权限
        'buylevels' => is_array($_GPC['buylevels']) ? implode(",", $_GPC['buylevels']) : '',//会员等级购买权限
        'showgroups' => is_array($_GPC['showgroups']) ? implode(",", $_GPC['showgroups']) : '',//会员组浏览权限
        'buygroups' => is_array($_GPC['buygroups']) ? implode(",", $_GPC['buygroups']) : '',//会员组购买权限
        'showClerkLevels' => is_array($_GPC['showClerkLevels']) ? implode(",", $_GPC['showClerkLevels']) : '',//店员等级浏览权限
        'buyClerkLevels' => is_array($_GPC['buyClerkLevels']) ? implode(",", $_GPC['buyClerkLevels']) : '',//店员等级购买权限
        'showOrgs' => is_array($_GPC['showOrgs']) ? implode(",", $_GPC['showOrgs']) : '',//集团浏览权限
        'buyOrgs' => is_array($_GPC['buyOrgs']) ? implode(",", $_GPC['buyOrgs']) : '',//集团购买权限
        'noticeopenid' => is_array($_GPC['noticeopenid']) ? implode(",", $_GPC['noticeopenid']) : '',
        'noticetype' => is_array($_GPC['noticetype']) ? implode(",", $_GPC['noticetype']) : '',
        'needfollow' => intval($_GPC['needfollow']),
        'followurl' => trim($_GPC['followurl']),
        'followtip' => trim($_GPC['followtip']),
        'deduct' => $_GPC['deduct'],
        'manydeduct' => $_GPC['manydeduct'],
        'manydeduct2' => $_GPC['manydeduct2'],
        'deduct2' => $_GPC['deduct2'],
        'pay_deduct' => $_GPC['pay_deduct'],
        'virtual'=>$goodstype==3?intval($_GPC['virtual']):0,
        'ednum' => intval($_GPC['ednum']),
        'edareas' => trim($_GPC['edareas']),
        'edareas_code' => trim($_GPC['edareas_code']),
        'nodispatchareas' => trim($_GPC['nodispatchareas']),
        'nodispatchareas_code' => trim($_GPC['nodispatchareas_code']),
        'edmoney' => trim($_GPC['edmoney']),
        'invoice' => intval($_GPC['invoice']),
        'repair' => intval($_GPC['repair']),
        'seven' => intval($_GPC['seven']),
        'money'=>trim($_GPC['money']),
        'province'=>trim($_GPC['province']),
        'city'=>trim($_GPC['city']),
        'quality'=>intval($_GPC['quality']),
        'sharebtn'=>intval($_GPC['sharebtn']),
        'autoreceive'=>intval($_GPC['autoreceive']),
        'not_receive_days'=>intval($_GPC['not_receive_days']),
        'cannotrefund'=>intval($_GPC['cannotrefund']),
        'refund'=>intval($_GPC['refund']),
        'returngoods'=>intval($_GPC['returngoods']),
        'exchange'=>intval($_GPC['exchange']),
        'buyagain'=>floatval($_GPC['buyagain']),
        'buyagain_islong'=>intval($_GPC['buyagain_islong']),
        'buyagain_condition'=>intval($_GPC['buyagain_condition']),
        'buyagain_sale'=>intval($_GPC['buyagain_sale']),
        'diypage'=>intval($_GPC['diypage']),
        'cashier'=>intval($_GPC['cashier']),
        'video'=>trim($_GPC['video']),
        'isreclevel'=>intval($_GPC['isreclevel']),//营销主推级别开关
        'reclevel'=>intval($_GPC['reclevel']),//营销主推级别开关
        'gdocid'=>trim($_GPC['gdocid']),//商品资料ID
        'erpGoodsID'=>trim($_GPC['erpGoodsID']),//ERP商品资料ID
        'supplyid'=>intval($_GPC['supplyid']),//供应商ID
        'isSupplySend' => intval($_GPC['isSupplySend']),//是否供应商发货
        'stockErpType'=>intval($_GPC['stockErpType']),//ERP来源类型 0自营 1ERP 2多商户
        'isK' => intval($_GPC['isK']),//运营属性：K控销商品
        'isS' => intval($_GPC['isS']),//运营属性：S商调商品
        'isT' => intval($_GPC['isT']),//运营属性：T特价商品
        'isTB' => intval($_GPC['isTB']),//运营属性：TB补贴商品
        'isnoReward' => intval($_GPC['isnoReward']),//运营属性：不参与分佣
        'isBanWechat' => intval($_GPC['isBanWechat']),//运营属性：不参与分佣
        'isMembeCanSeeVipText' => intval($_GPC['isMembeCanSeeVipText']),//是否显示高一级会员权益价格开关
        'hasMembeCanSeeVip' => $_GPC['hasMembeCanSeeVip'],//是否开启独立会员文本：会员卡
        'memberPriceText' => $_GPC['memberPriceText'],//“会员价”显示文本
        'memberCardText' => $_GPC['memberCardText'],//“会员卡”显示文本
        'isGoodCodeAloneRules' => intval($_GPC['isGoodCodeAloneRules']),//商品海报独立设置开关 1开启 0关闭
        'hasGoodCodeBottomSlogan' => intval($_GPC['hasGoodCodeBottomSlogan']),//商品海报底部宣传语 独立规则 1开启 0关闭
        'hasShowGoodCodeBottomSlogan' => intval($_GPC['hasShowGoodCodeBottomSlogan']),//商品海报底部宣传语 独立规则 是否显示 1显示 0关闭
        'is_cloud' => intval($_GPC['is_cloud']),//云商品0否1是
        'can_add_cart' => intval($_GPC['can_add_cart']),//是否允许商品加入购物车0否1是
        'activity_id' => intval($_GPC['activity_id']),//活动id
        'cycelbuy_goods_id'=>intval($_GPC['cycelbuy_goods_id'])//周期购商品
    );

    // 处理终端上架设置  若不存在则初始化为空数组
    $terminal_arr = is_array($_GPC['terminal']) ? $_GPC['terminal'] : [];
    // 初始化 $data['limitation']['terminal']
    if (!isset($data['limitation'])) {
        $data['limitation'] = [];
    }
    $data['limitation']['terminal'] = [];
    // 遍历所有终端，设置选中或未选中的状态 如果 $terminal_arr 为空，将所有终端设置为 true, 不为空，根据 $terminal_arr 设置终端状态
    foreach ($terminal as $terminalName => $terminalConstant) {
        $data['limitation']['terminal'][$terminalConstant] = empty($terminal_arr) || in_array($terminalConstant, $terminal_arr);
    }

    // 处理购买资格券设置  若不存在则初始化为空数组
    $_GPC['ticket_coupons']['ids'] = !empty($_GPC['couponids']) ? $_GPC['couponids'] : [];
    $data['limitation']['ticket_coupons'] = is_array($_GPC['ticket_coupons']) ? $_GPC['ticket_coupons'] : [];
    $ticket_coupon_ids = (array) $data['limitation']['ticket_coupons']['ids'];
    if (empty($ticket_coupon_ids) && !empty($data['limitation']['ticket_coupons']['is_open'])) {
        show_json(0, '请选择资格券!');
    }
    $count_ticket_coupon = count($ticket_coupon_ids);
    if ($count_ticket_coupon > 1) {
        foreach ($ticket_coupon_ids as $key => $id) {
            if (in_array($id, array_slice($ticket_coupon_ids, $key + 1))) {
                show_json(0, '资格券不能重复选择！');
            }
        }
    }

    // 将 $data['limitation'] 编码为 JSON 格式
    $data['limitation'] = json_encode($data['limitation']);

    if(p('offic')){
        $data['officthumb'] = save_media($_GPC['officthumb']);
    }
    $data['nosearch'] = intval($_GPC['nosearch']);
    $data['isstatustime'] = intval($_GPC['isstatustime']);
    $statustimestart = strtotime($_GPC['statustime']['start']);
    $statustimeend = strtotime($_GPC['statustime']['end']);
    $data['statustimestart'] = $statustimestart;
    $data['statustimeend'] = $statustimeend;
    /* 增加代理商、团队、合伙人、推广者岗位佣金字段hlei 20180907start----> */

    $data['tgcredits1'] = intval($_GPC['tgcredits1']);//会员推广分享一级积分奖励
    $data['tgcredits2'] = intval($_GPC['tgcredits2']);//会员推广分享二级积分奖励
    $data['tgcredits3'] = intval($_GPC['tgcredits3']);//会员消费积分奖励
    $data['sfjoin4'] = intval($_GPC['sfjoin4']);//会员是否参与积分推广奖励
    $data['sfjoin6'] = intval($_GPC['sfjoin6']);//会员是否参与积分消费奖励

    $data['medicineAttributeID'] = $_GPC['medicineAttributeID'];//药品属性ID：1处方药 2OTC
    $data['medicineClassID'] = $_GPC['medicineClassID'];//药品类别ID
    $data['medicineOtcTypeID'] = $_GPC['medicineOtcTypeID'];//非处方药类别ID，1甲类非处方2乙类非处方
    $data['brandName'] = $_GPC['brandName'];//产品品牌名称
    $data['up_hassharecreditsRateType'] = intval($_GPC['up_hassharecreditsRateType']);//会员分享积分独立计算模式开关：0关闭，1开启。
    $data['up_hasshelbuycreditsRateType'] = intval($_GPC['up_hasshelbuycreditsRateType']);//会员消费积分独立计算模式开关：0关闭，1开启。
    $data['up_sharecreditsRateType'] = intval($_GPC['up_sharecreditsRateType']);//会员分享积分独立计算模式：0毛利百分比，1成交价百分比。
    $data['up_shelbuycreditsRateType'] = intval($_GPC['up_shelbuycreditsRateType']);//会员消费积分独立计算模式：0毛利百分比，1成交价百分比。

    //药品剂型
    $formTypes = [];
    $formTypeCid = $_GPC['cid'];
    $formTypeIds = $_GPC['formTypeIds'];
    $formTypeNames = $_GPC['fname'];
    $formTypes = array('id' => $formTypeIds,'cid' => $formTypeCid,'name' => $formTypeNames);
    //药品信息表
    $data['medicines'] = serialize(
        array(
            "medicineName"=>$_GPC['medicineName'],//药品名称
            "specification"=>$_GPC['specification'],//药品规格
            "medicationDays"=>$_GPC['medicationDays'],//用药疗程
            "approvalNo"=>$_GPC['approvalNo'],//批准文号
            "formType"=> $formTypes,//药品剂型
            "takeDose"=>$_GPC['takeDose'],//服用剂量
            "takeFrequence"=>$_GPC['takeFrequence'],//服用频次
            "takeDirection"=>$_GPC['takeDirection'],//使用方法
            "medicineFunctions"=>$_GPC['medicineFunctions'],//功能主治
            "manufacturer"=>$_GPC['manufacturer'],//生产企业
            "usefulLife"=>$_GPC['usefulLife'],//有效期
            "usefulLifeunit"=>$_GPC['usefulLifeunit'],//有效期单位：天、月、年
        )
    );
    //药品医疗器械功能主治作用,数组转编码Hlei0210621
    $data['functions'] = serialize(
        array(
            "functions_rx"=>$_GPC['functions_rx'],
            "functions_otc"=>$_GPC['functions_otc'],
            "functions_bjyp"=>$_GPC['functions_bjyp'],
            "functions_bjsp"=>$_GPC['functions_bjsp'],
            "functions_ylqx"=>$_GPC['functions_ylqx'],
            "functions_qqyp"=>$_GPC['functions_qqyp'],
            "functions_jsyp"=>$_GPC['functions_jsyp'],
        )
    );
    /* hlei <----------------------end */

    if($data['status']==1 && $data['isstatustime'] > 0){
        if($statustimeend <= time()){
            show_json(0,'上架时间不符合要求！');
        }
    }
    if($data['status'] < 2 && $data['isstatustime'] == 1){
        if($data['statustimestart'] < time() && $data['statustimeend'] > time()){
            $data['status'] = 1;
        }else{
            $data['status'] = 0;
        }
    }
    $intervalfloor=0;
    $intervalprice="";
    if($goodstype==4) {
        $intervalfloor = intval($_GPC['intervalfloor']);
        if($intervalfloor>3||$intervalfloor<1){
            show_json(0,'请至少添加一个区间价格！');
        }
        $intervalprices = array();
        if($intervalfloor>0){
            if(intval($_GPC['intervalnum1'])<=0){
                show_json(0,'请设置起批发量！');
            }

            if(floatval($_GPC['intervalprice1'])<=0){
                show_json(0,'批发价必须大于0！');
            }
            $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum1']),"intervalprice"=>floatval($_GPC['intervalprice1']));
        }
        if($intervalfloor>1){
            if(intval($_GPC['intervalnum2'])<=0){
                show_json(0,'请设置起批发量！');
            }
            if(intval($_GPC['intervalnum2'])<=intval($_GPC['intervalnum1'])){
                show_json(0,'批发量需大于上级批发量！');
            }

            if(floatval($_GPC['intervalprice2'])>=floatval($_GPC['intervalprice1'])){
                show_json(0,'批发价需小于上级批发价！');
            }

            $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum2']),"intervalprice"=>floatval($_GPC['intervalprice2']));
        }
        if($intervalfloor>2){
            if(intval($_GPC['intervalnum3'])<=0){
                show_json(0,'请设置起批发量！');
            }
            if(intval($_GPC['intervalnum3'])<=intval($_GPC['intervalnum2'])){
                show_json(0,'批发量需大于上级批发量！');
            }
            if(floatval($_GPC['intervalprice3'])>=floatval($_GPC['intervalprice2'])) {
                show_json(0,'批发价需小于上级批发价！');
            }
            $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum3']),"intervalprice"=>floatval($_GPC['intervalprice3']));
        }
        $intervalprice = iserializer($intervalprices);
        $data['intervalfloor']  = $intervalfloor;
        $data['intervalprice']  = $intervalprice;
        $data['minbuy']  = $_GPC['intervalnum1'];
        $data['marketprice']  = $_GPC['intervalprice1'];
        $data['productprice']  = 0;
        $data['costprice']  = 0;
    }

    if(intval($_GPC['ispresell'])==1){
        if(floatval($_GPC['presellprice']<=0)){
            show_json(0,'请填写预售价格！');
        }
        $data['isdiscount'] = 0;
        $data['istime'] = 0;
        $data['isstatustime'] = 0;
        $data['statustimestart'] = 0;
        $data['statustimeend'] = 0;
    }
    $buyagain_commission = is_array($_GPC['buyagain_commission']) ? $_GPC['buyagain_commission'] : array();
    if (!empty($buyagain_commission)){
        $buyagain_commission['type'] = 0;
        $data['buyagain_commission'] = json_encode($buyagain_commission);
    }

    $buyagain_clerkCommission = is_array($_GPC['buyagain_clerkCommission']) ? $_GPC['buyagain_clerkCommission'] : array();
    if (!empty($buyagain_clerkCommission)){
        $buyagain_clerkCommission['type'] = 0;
        $data['buyagain_clerkCommission'] = json_encode($buyagain_clerkCommission);
    }

    $buyagain_doctorCommission = is_array($_GPC['doctor_buyagain_commission']) ? $_GPC['doctor_buyagain_commission'] : array();
    if (!empty($buyagain_doctorCommission)){
        $buyagain_doctorCommission['type'] = 0;
        $data['doctor_buyagain_commission'] = json_encode($buyagain_doctorCommission);
    }
    //招商经理
    $buyagain_businessCommission = is_array($_GPC['business_buyagain_commission']) ? $_GPC['business_buyagain_commission'] : array();
    if (!empty($buyagain_businessCommission)){
        $buyagain_businessCommission['type'] = 0;
        $data['business_buyagain_commission'] = json_encode($buyagain_businessCommission);
    }

    $buyagain_ownerCommission = is_array($_GPC['buyagain_ownerCommission']) ? $_GPC['buyagain_ownerCommission'] : array();
    if (!empty($buyagain_ownerCommission)){
        $buyagain_ownerCommission['type'] = 0;
        $data['buyagain_ownerCommission'] = json_encode($buyagain_ownerCommission);
    }

    $buyagain_copartnerCommission = is_array($_GPC['buyagain_copartnerCommission']) ? $_GPC['buyagain_copartnerCommission'] : array();
    if (!empty($buyagain_copartnerCommission)){
        $buyagain_copartnerCommission['type'] = 0;
        $data['buyagain_copartnerCommission'] = json_encode($buyagain_copartnerCommission);
    }

    // 货到付款 现付 配置
    if ($merchid == 0) {
        $data['isverify'] = $_GPC['isverify'];
        $data['verifytype'] = intval($_GPC['verifytype']);
        $data['storeids'] = is_array($_GPC['storeids']) ? implode(',', $_GPC['storeids']) : '';
        if (intval($_GPC['isverify']) == 2 || $goodstype == 2 || $goodstype == 3) {
            $data['cash'] = 0;
            $data['is_only_cash'] = 0;
        } else {
            $data['cash'] = intval($_GPC['cash']);
            $data['is_only_cash'] = intval($_GPC['cash']) ? intval($_GPC['is_only_cash']) : 0;
        }
        $data['detail_logo'] = save_media($_GPC['detail_logo']);
        $data['detail_shopname'] = trim($_GPC['detail_shopname']);
        $data['detail_totaltitle'] = trim($_GPC['detail_totaltitle']);
        $data['detail_btntext1'] = trim($_GPC['detail_btntext1']);
        $data['detail_btnurl1'] = trim($_GPC['detail_btnurl1']);
        $data['detail_btntext2'] = trim($_GPC['detail_btntext2']);
        $data['detail_btnurl2'] = trim($_GPC['detail_btnurl2']);
    } else {
        if (intval($item['isverify']) == 2 || $goodstype == 2 || $goodstype == 3) {
            $data['cash'] = 0;
            $data['is_only_cash'] = 0;
        } else {
            $data['cash'] = intval($_GPC['cash']);
            $data['is_only_cash'] = intval($_GPC['cash']) ? intval($_GPC['is_only_cash']) : 0;
        }
        $data['merchsale'] = intval($_GPC['merchsale']);
    }

    $data['isforceverifystore'] = intval($_GPC['isforceverifystore']);
    if($goodstype==5){
        $data['isverify'] = 2;
        $data['isforceverifystore'] = intval($_GPC['isforceverifystore_verifygoods']);
        $data['storeids'] = is_array($_GPC['storeids_verifygoods']) ? implode(',', $_GPC['storeids_verifygoods']) : '';
    }

    $data['isendtime'] = intval($_GPC['isendtime']);
    $data['usetime'] = intval($_GPC['usetime']);
    $data['endtime'] = strtotime($_GPC['endtime']);

    $cateset = m('common')->getSysset('shop');
    $pcates = array();
    $ccates = array();
    $tcates = array();
    $fcates = array();
    $cates = array();
    $pcateid=0;
    $ccateid = 0;
    $tcateid = 0;
    if (is_array($_GPC['cates'])) {
        $cates = $_GPC['cates'];
        foreach ($cates as $key=>$cid) {
            $c = pdo_fetch('select level from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
            if($c['level']==1){
                $pcates[] = $cid;
            } else if($c['level']==2){
                $ccates[] = $cid;
            } else if($c['level']==3){
                $tcates[] =$cid;
            }
            if($key==0){
                if($c['level']==1){
                    $pcateid = $cid;
                }else if($c['level']==2){
                    $crow = pdo_fetch('select parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                    $pcateid = $crow['parentid'];
                    $ccateid = $cid;
                }else if($c['level']==3){
                    $tcateid = $cid;
                    $tcate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                    $ccateid = $tcate['parentid'];
                    $ccate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $ccateid, ':uniacid' => $_W['uniacid']));
                    $pcateid = $ccate['parentid'];
                }
            }
        }
    }

    $data['pcate'] = $pcateid;
    $data['ccate'] = $ccateid;
    $data['tcate'] = $tcateid;
    $data['cates'] = implode(',', $cates);
    $data['pcates'] = implode(',', $pcates);
    $data['ccates'] = implode(',', $ccates);
    $data['tcates'] = implode(',', $tcates);
    $data['content'] = m('common')->html_images($_GPC['content']);
    $data['buycontent'] = m('common')->html_images($_GPC['buycontent']);
    //分销
    if (p('commission')) {
        $cset = p('commission')->getSet();
        if (!empty($cset['level'])) {
            $data['nocommission'] = intval($_GPC['nocommission']);
            $data['hascommission'] = intval($_GPC['hascommission']);
            $data['hidecommission'] = intval($_GPC['hidecommission']);
            $data['commission1_rate'] = $_GPC['commission1_rate'];
            $data['commission2_rate'] = $_GPC['commission2_rate'];
            $data['commission3_rate'] = $_GPC['commission3_rate'];
            $data['commission1_pay'] = $_GPC['commission1_pay'];
            $data['commission2_pay'] = $_GPC['commission2_pay'];
            $data['commission3_pay'] = $_GPC['commission3_pay'];
            $data['commission_thumb'] = save_media($_GPC['commission_thumb']);
        }
    }
    //虚店店员
    if (p('clerk')) {
        $clerkSet = p('clerk')->getSet();
        if (!empty($clerkSet['level'])) {
            $data['noClerkCommission'] = intval($_GPC['noClerkCommission']);
            $data['hasClerkCommission'] = intval($_GPC['hasClerkCommission']);
            $data['hideClerkCommission'] = intval($_GPC['hideClerkCommission']);
            $data['clerkCommission1_rate'] = $_GPC['clerkCommission1_rate'];
            $data['clerkCommission2_rate'] = $_GPC['clerkCommission2_rate'];
            $data['clerkCommission3_rate'] = $_GPC['clerkCommission3_rate'];
            $data['clerkCommission1_pay'] = $_GPC['clerkCommission1_pay'];
            $data['clerkCommission2_pay'] = $_GPC['clerkCommission2_pay'];
            $data['clerkCommission3_pay'] = $_GPC['clerkCommission3_pay'];
            $data['clerkCommission_thumb'] = save_media($_GPC['clerkCommission_thumb']);
        }
    }
    //医生
    if (p('doctor')) {
        $doctorSet = p('doctor')->getSet();
        if (!empty($doctorSet['level'])) {
            $data['doctor_no_commission'] = intval($_GPC['doctor_no_commission']);
            $data['doctor_has_commission'] = intval($_GPC['doctor_has_commission']);
            $data['doctor_hide_commission'] = intval($_GPC['doctor_hide_commission']);
            $data['doctor_commission_rate'] = $_GPC['doctor_commission_rate'];
            $data['doctor_commission_pay'] = $_GPC['doctor_commission_pay'];
            $data['doctor_commission_thumb'] = save_media($_GPC['doctor_commission_thumb']);
        }
    }

    //虚店店长
    if (p('vrshop')) {
        $vrshopSet = p('vrshop')->getSet();
        if (!empty($vrshopSet['level'])) {
            $data['noOwnerCommission'] = intval($_GPC['noOwnerCommission']);
            $data['hasOwnerCommission'] = intval($_GPC['hasOwnerCommission']);
            $data['hideownerCommission'] = intval($_GPC['hideownerCommission']);
            $data['ownerCommission1_rate'] = $_GPC['ownerCommission1_rate'];
            $data['ownerCommission2_rate'] = $_GPC['ownerCommission2_rate'];
            $data['ownerCommission3_rate'] = $_GPC['ownerCommission3_rate'];
            $data['ownerCommission1_pay'] = $_GPC['ownerCommission1_pay'];
            $data['ownerCommission2_pay'] = $_GPC['ownerCommission2_pay'];
            $data['ownerCommission3_pay'] = $_GPC['ownerCommission3_pay'];
            $data['ownerCommission_thumb'] = save_media($_GPC['ownerCommission_thumb']);
        }
    }
    //合伙人
    if (p('copartner')) {
        $copartnerSet = p('copartner')->getSet();
        if (!empty($copartnerSet['level'])) {
            //机构
            $data['noCopartnerCommission'] = intval($_GPC['noCopartnerCommission']);
            $data['hasCopartnerCommission'] = intval($_GPC['hasCopartnerCommission']);
            $data['hideCopartnerCommission'] = intval($_GPC['hideCopartnerCommission']);
            $data['copartnerCommission1_rate'] = $_GPC['copartnerCommission1_rate'];
            $data['copartnerCommission2_rate'] = $_GPC['copartnerCommission2_rate'];
            $data['copartnerCommission3_rate'] = $_GPC['copartnerCommission3_rate'];
            $data['copartnerCommission1_pay'] = $_GPC['copartnerCommission1_pay'];
            $data['copartnerCommission2_pay'] = $_GPC['copartnerCommission2_pay'];
            $data['copartnerCommission3_pay'] = $_GPC['copartnerCommission3_pay'];
            $data['copartnerCommission_thumb'] = save_media($_GPC['copartnerCommission_thumb']);
            //招商经理
            $data['business_no_commission'] = intval($_GPC['business_no_commission']);
            $data['business_has_commission'] = intval($_GPC['business_has_commission']);
            $data['business_hide_commission'] = intval($_GPC['business_hide_commission']);
            $data['business_commission_rate'] = $_GPC['business_commission_rate'];
            $data['business_commission_pay'] = $_GPC['business_commission_pay'];
            $data['business_commission_thumb'] = save_media($_GPC['business_commission_thumb']);
        }
    }
    if ($ccard_plugin) {
        if ($goodstype == 20) {
            $data['rdays'] = intval($_GPC['rdays']);
            $data['ccardexplain'] = trim($_GPC['ccardexplain']);
            $data['ccardtimeexplain'] = trim($_GPC['ccardtimeexplain']);
            $data['nocommission'] = 1;
        }
    }

    if ($diyform) {
        if ($_GPC['diyformtype'] == 2) {
            $diydata = $diyform->getInsertDataByAdmin();
            $diydata = iserializer($diydata);
            $_GPC['diysave'] = intval($_GPC['diysave']);
            if ($_GPC['diysave'] == 1) {
                $diysaveid = $item['diysaveid'];
                $insert = array();
                $insert['title'] = '商品ID' . $item['id'] . '的自定义表单';
                $insert['fields'] = $diydata;
                $is_save = $diyform->isHasDiyform($diysaveid);
                if (empty($is_save)) {
                    $insert['uniacid'] = $_W['uniacid'];
                    pdo_insert('elapp_shop_diyform_type', $insert);
                    $data['diysaveid'] = pdo_insertid();
                } else {
                    pdo_update('elapp_shop_diyform_type', $insert, array('id' => $diysaveid));
                }
            }
            $data['diyfields'] = $diydata;
            $data['diysave'] = $_GPC['diysave'];
        }

        $data['diyformtype'] = $_GPC['diyformtype'];
        if ($_GPC['diyformtype'] == 1) {
            $data['diyformid'] = $_GPC['diyformid'];
        }
        $data['diymode'] = intval($_GPC['diymode']);
    }
    $data['dispatchtype'] = intval($_GPC['dispatchtype']);
    $data['dispatchprice'] = trim($_GPC['dispatchprice']);
    $data['dispatchid'] = intval($_GPC['dispatchid']);
    $data['isshowfreight'] = intval($_GPC['isshowfreight']);
    if ($data['stock'] === -1) {
        $data['stock'] = 0;
        $data['totalcnf'] = 2;
    }
    if (is_array($_GPC['thumbs'])) {
        $thumbs = $_GPC['thumbs'];
        $thumb_url = array();
        foreach ($thumbs as $th) {
            $thumb_url[] = trim($th);
        }
        $data['thumb'] = save_media($thumb_url[0]);
        unset($thumb_url[0]);
        $data['thumb_url'] = serialize(m('common')->array_images($thumb_url));
    }
    if (p('threen')){
        $threen = $_GPC['threen'];
        if (!empty($threen['discount']) && !empty($threen['price'])){
            show_json(0,'3N营销优惠只能设置一个');
        }
        $threen['price'] = floatval(round($threen['price'],2));
        $threen['discount'] = floatval(round($threen['discount'],2));
        if (!empty($threen['price']) && !$threen['price'] > 0){
            show_json(0,'3N营销会员价必须大于0');
        }
        if (!empty($threen['discount'])&&($threen['discount'] >10 ||$threen <0.1))show_json(0,'3N营销优惠需在0.1-10之间');
        $data['threen'] = json_encode($threen);
    }
    if($goodstype==5){
        $verifygoodsnum = intval($_GPC['verifygoodsnum']);
        $verifygoodslimittype = intval($_GPC['verifygoodslimittype']);
        if(!empty($_GPC['verifygoodslimitdate'])){
            $verifygoodslimitdate = strtotime($_GPC['verifygoodslimitdate']);
        }else{
            $verifygoodslimitdate=0;
        }
        $verifygoodsdays = intval($_GPC['verifygoodsdays']);
        if(empty($verifygoodslimittype)){
            if(empty($verifygoodsdays))
            {
                $verifygoodsdays=365;
            }
        }
        if(com('wxcard')){
            $opencard = intval($_GPC['opencard']);
            if(!empty($opencard)){
                if (strlen($_GPC['custom_cell1_url']) > 128) {
                    show_json(0, '入口跳转链接不能超过128个字符');
                }
                $prerogative = htmlspecialchars($_GPC['prerogative'],ENT_QUOTES);
                $prerogative =istripslashes($prerogative);
                $card_description = htmlspecialchars($_GPC['card_description'],ENT_QUOTES);
                $card_description =istripslashes($card_description);
                if (empty($prerogative)) {
                    show_json(0, '会员卡特权说明不能为空');
                }
                if (mb_strlen($prerogative,'UTF-8') > 300) {
                    show_json(0, '会员卡特权不能超过300个字符');
                }
                if (empty($card_description)) {
                    show_json(0, '使用须知说明不能为空');
                }
                if (mb_strlen($card_description,'UTF-8') > 300) {
                    show_json(0, '使用须知不能超过300个字符');
                }
                $carddata = array(
                    "uniacid"=>$_W['uniacid'],
                    "card_backgroundtype"=>$_GPC['card_backgroundtype'],
                    "color" => $_GPC['color'],
                    "color2" => $_GPC['color2'],
                    "prerogative"=>$_GPC['prerogative'],
                    "card_description"=>$_GPC['card_description'],
                    "custom_cell1"=>$_GPC['custom_cell1'],
                    "custom_cell1_name"=>$_GPC['custom_cell1_name'],
                    "custom_cell1_tips"=>$_GPC['custom_cell1_tips'],
                    "custom_cell1_url"=>$_GPC['custom_cell1_url']
                );
                if(empty($card)||$card['card_logoimg']!=$_GPC['card_logoimg']){
                    if (empty($card)) {
                        if (empty($_GPC['card_logoimg'])) {
                            show_json(0, 'logo图片不能为空');
                        }
                    }
                    $imgurl =ATTACHMENT_ROOT.$_GPC['card_logolocalpath'];
                    if (!is_file($imgurl)) {
                        $img = tomedia($_GPC['card_logolocalpath']);
                        $img = ihttp_get($img);
                        if(is_error($img)){
                            show_json(0, "上传的logo图片限制文件大小限制1MB，像素为300*300，仅支持JPG、PNG格式。");
                        }
                        $img = $img['content'];
                        if(strlen($img) != 0){
                            file_put_contents($imgurl, $img);
                        }else{
                            show_json(0, "上传的logo图片限制文件大小限制1MB，像素为300*300，仅支持JPG、PNG格式。");
                        }
                    }
                    $result = com('wxcard')->wxCardUpdateImg($imgurl);
                    if (is_wxerror($result)) {
                        show_json(0,"上传的logo图片限制文件大小限制1MB，像素为300*300，仅支持JPG、PNG格式。" );
                    }
                    $carddata['card_logoimg']=$_GPC['card_logoimg'];
                    $carddata['card_logowxurl']=$result["url"];
                }

                if(!empty($_GPC['card_backgroundtype'])){
                    if(empty($card)||$card['card_backgroundimg']!=$_GPC['card_backgroundimg']){
                        if(empty($card)){
                            if(empty($_GPC['card_logoimg'])){
                                show_json(0,'设置使用背景图片时图片不能为空');
                            }
                        }
                        $imgurl =ATTACHMENT_ROOT.$_GPC['card_backgroundimg_localpath'];
                        if (!is_file($imgurl)) {
                            $img = tomedia($_GPC['card_backgroundimg_localpath']);
                            $img = ihttp_get($img);
                            if(is_error($img)){
                                show_json(0, "上传的背景图片限制文件大小限制1MB，像素为1000*600，仅支持JPG、PNG格式");
                            }
                            $img = $img['content'];
                            if(strlen($img) != 0){
                                file_put_contents($imgurl, $img);
                            }else {
                                show_json(0, "上传的背景图片限制文件大小限制1MB，像素为1000*600，仅支持JPG、PNG格式");
                            }
                        }
                        $result = com('wxcard')->wxCardUpdateImg($imgurl);
                        if (is_wxerror($result)) {
                            show_json(0,"上传的背景图片限制文件大小限制1MB，像素为1000*600，仅支持JPG、PNG格式" );
                        }
                        $carddata['card_backgroundimg']=$_GPC['card_backgroundimg'];
                        $carddata['card_backgroundwxurl']=$result["url"];
                    }
                    else if(!empty($card) &&$card['card_backgroundimg']== $_GPC['card_backgroundimg']){
                        $carddata['card_backgroundimg'] = $card['card_backgroundimg'];
                        $carddata['card_backgroundwxurl'] = $card['card_backgroundwxurl'];
                    }
                }

                if (!empty($card)) {
                    $change = com('wxcard')->checkchange($card,$carddata);
                    if($change){
                        $result = com('wxcard')->verifygoodcard($carddata,$card['card_id']);
                        if($result['errcode']==48001) {
                            show_json(0, "您尚未开通微信会员卡。");
                        }
                        if (is_wxerror($result)) {
                            show_json(0,"卡券信息填写有误。" );
                        }
                        pdo_update('elapp_shop_goods_cards', $carddata, array('id' => $card['id']));
                    }
                    $cardid = $card['id'];

                }else{
                    if(strlen($_GPC['title'])>25){
                        show_json(0,'会员卡标题不能超过25个字符');
                    }
                    if(strlen($_GPC['card_brand_name'])>30){
                        show_json(0,'商户名字不能超过30个字符');
                    }
                    if(intval($_GPC['card_totalquantity'])>9999999||intval($_GPC['card_totalquantity'])<1){
                        show_json(0,"会员卡库存需设置再1与9999999之间");
                    }
                    $carddata["card_title"]=$_GPC['card_title'];
                    $carddata["card_brand_name"]=$_GPC['card_brand_name'];
                    $carddata["card_totalquantity"]=$_GPC['card_totalquantity'];
                    $carddata["card_quantity"]=$_GPC['card_totalquantity'];
                    $carddata["freewifi"]=$_GPC['freewifi']=="on"?1:0;
                    $carddata["withpet"]=$_GPC['withpet']=="on"?1:0;
                    $carddata["freepark"]=$_GPC['freepark']=="on"?1:0;
                    $carddata["deliver"]=$_GPC['deliver']=="on"?1:0;
                    $result = com('wxcard')->verifygoodcard($carddata);
                    if($result['errcode']==48001){
                        show_json(0, "您尚未开通微信会员卡。");
                    }
                    if (is_wxerror($result)) {
                        show_json(0,"卡券信息填写有误。" );
                    }else{
                        $carddata['card_id']=$result['card_id'];
                    }
                    pdo_insert('elapp_shop_goods_cards', $carddata);
                    $cardid = pdo_insertid();
                }
                $data['cardid']  = $cardid;
            }
            $data['opencard']  = $opencard;
        }//wxcard end
        $data['verifygoodsnum']  = intval($verifygoodsnum);
        $data['verifygoodslimittype']  = intval($verifygoodslimittype);
        $data['verifygoodsdays']  =intval( $verifygoodsdays);
        $data['verifygoodslimitdate']  = intval($verifygoodslimitdate);
        $data['verifygoodstype']  = intval($_GPC['verifygoodstype']);
        $data['verifytype']  = intval($_GPC['verify_type1']);
        if ($data['verifytype'] == 0){
            $data['verifygoodsnum'] = 1;
        }
    }//$goodstype==5 end

    if($data['hasoption']!=0){
        $data['productsn']='';
    }
    //更新数据
    if (empty($id)) {
        $data['merchid'] = 0;
        pdo_insert('elapp_shop_goods', $data);
        $id = pdo_insertid();
        // 云商品编码
        if(!empty($data['is_cloud'])){
            //查询云商品cloud_code编码最大的值
            $result_cloud_code = pdo_fetch('SELECT MAX(cloud_code) + 1 as code FROM '.tablename('elapp_shop_goods').' WHERE is_cloud=1 and uniacid=:uniacid',array(':uniacid'=>$_W['uniacid']));
            $new_cloud_code = $result_cloud_code['code'];
            if ($new_cloud_code <= 0) {
                $new_cloud_code = 10000000;//如果 $new_cloud_code 的值小于等于 0，将其补全为 8 位数，并从 10000000 开始重新赋值
            } else {
                $new_cloud_code = max(10000000, $new_cloud_code); // 确保云商品编码大于等于 10000000
            }
            // 补全8位数
            $new_cloud_code_str = str_pad($new_cloud_code, 8, '0', STR_PAD_LEFT);
            // 更新云商品编码
            pdo_update('elapp_shop_goods', array('cloud_code'=>$new_cloud_code_str), array('id' => $id));
        }
        //更新日志
        plog('goods.add', "添加商品 ID: {$id}<br>".(!empty($data['nocommission']) ? "是否参与分销 -- 否" : "是否参与分销 -- 是").(!empty($data['noOwnerCommission']) ? "是否参与店长绩效 -- 否" : "是否参与店长绩效 -- 是").(!empty($data['noClerkCommission']) ? "是否参与店员提成 -- 否" : "是否参与店员提成 -- 是").(!empty($data['noCopartnerCommission']) ? "是否参与合伙人分润 -- 否" : "是否参与合伙人分润 -- 是") . (!empty($data['doctor_no_commission']) ? "是否参与医生提成 -- 否" : "是否参与医生提成 -- 是"));
    } else {
        unset($data['createtime']);
        $old_data = pdo_fetch('SELECT nocommission,marketprice,stock,noOwnerCommission,noClerkCommission,doctor_no_commission,noCopartnerCommission,business_no_commission FROM '.tablename('elapp_shop_goods').' WHERE id=:id AND uniacid=:uniacid',array(':id'=>$id,':uniacid'=>$_W['uniacid']));
        pdo_update('elapp_shop_goods', $data, array('id' => $id));
        if ($old_data['marketprice'] != $data['marketprice']){
            plog('goods.price.edit', "更改商品 ID: {$id} <br>价格为".$data['marketprice']);
        }
        if($old_data['nocommission'] != $data['nocommission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['nocommission']) ? "是否参与分销 -- 否" : "是否参与分销 -- 是"));
        }
        #虚店店长
        if($old_data['noOwnerCommission'] != $data['noOwnerCommission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['noOwnerCommission']) ? "是否参与店长绩效 -- 否" : "是否参与店长绩效 -- 是"));
        }
        #虚店店员
        if($old_data['noClerkCommission'] != $data['noClerkCommission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['noClerkCommission']) ? "是否参与店员提成 -- 否" : "是否参与店员提成 -- 是"));
        }
        #合伙人
        if($old_data['noCopartnerCommission'] != $data['noCopartnerCommission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['noCopartnerCommission']) ? "是否参与合伙人分润 -- 否" : "是否参与合伙人分润 -- 是"));
        }
        #招商经理
        if($old_data['business_no_commission'] != $data['business_no_commission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['business_no_commission']) ? "是否参与招商经理提成 -- 否" : "是否参与招商经理提成 -- 是"));
        }
        #医生
        if($old_data['doctor_no_commission'] != $data['doctor_no_commission']){
            plog('goods.edit', "编辑商品 ID: {$id}<br>".(!empty($data['doctor_no_commission']) ? "是否参与医生提成 -- 否" : "是否参与医生提成 -- 是"));
        }
        if($old_data['stock'] != $data['stock']){
            plog('goods.edit', "修改商品库存   ID: {$id} <br> 库存量为".$data['stock']);
        }
    }//更新数据 end
    //商品参数
    $param_ids = $_POST['param_id'];
    $param_titles = $_POST['param_title'];
    $param_values = $_POST['param_value'];
    $param_displayorders = $_POST['param_displayorder'];
    $len = is_array($param_ids)?count($param_ids):0;
    $paramids = array();
    for ($k = 0; $k < $len; $k++) {
        $param_id = "";
        $get_param_id = $param_ids[$k];
        $a = array(
            "uniacid"=>$_W['uniacid'],
            "title" => $param_titles[$k],
            "value" => $param_values[$k],
            "displayorder" => $k,
            "goodsid" => $id,
        );
        if (!is_numeric($get_param_id)) {
            pdo_insert("elapp_shop_goods_param", $a);
            $param_id = pdo_insertid();
        } else {
            pdo_update("elapp_shop_goods_param", $a, array('id' => $get_param_id));
            $param_id = $get_param_id;
        }
        $paramids[] = $param_id;
    }

    if (count($paramids) > 0) {
        pdo_query("delete from " . tablename('elapp_shop_goods_param') . " where goodsid=$id and id not in ( " . implode(',', $paramids) . ")");
    } else {
        pdo_query("delete from " . tablename('elapp_shop_goods_param') . " where goodsid=$id");
    }

    $totalstocks = 0;
    $files = $_FILES;
    $spec_ids = $_POST['spec_id'];
    $spec_titles = $_POST['spec_title'];
    $specids = array();
    $len = is_array($spec_ids)?count($spec_ids):0;
    $specids = array();
    $spec_items = array();
    for ($k = 0; $k < $len; $k++) {
        $spec_id = "";
        $get_spec_id = $spec_ids[$k];
        $a = array(
            "uniacid" => $_W['uniacid'],
            "goodsid" => $id,
            "displayorder" => $k,
            "title" => $spec_titles[$get_spec_id]
        );
        if (is_numeric($get_spec_id)) {
            pdo_update("elapp_shop_goods_spec", $a, array("id" => $get_spec_id));
            $spec_id = $get_spec_id;
        } else {
            pdo_insert("elapp_shop_goods_spec", $a);
            $spec_id = pdo_insertid();
        }

        $spec_item_ids = $_POST["spec_item_id_" . $get_spec_id];
        $spec_item_titles = $_POST["spec_item_title_" . $get_spec_id];
        $spec_item_shows = $_POST["spec_item_show_" . $get_spec_id];
        $spec_item_thumbs = $_POST["spec_item_thumb_" . $get_spec_id];
        $spec_item_oldthumbs = $_POST["spec_item_oldthumb_" . $get_spec_id];
        $spec_item_virtuals = $_POST["spec_item_virtual_" . $get_spec_id];

        $itemlen = is_array($spec_item_ids) ? count($spec_item_ids) : 0;
        $itemids = array();
        for ($n = 0; $n < $itemlen; $n++) {
            $item_id = "";
            $get_item_id = $spec_item_ids[$n];
            $d = array(
                "uniacid" => $_W['uniacid'],
                "specid" => $spec_id,
                "displayorder" => $n,
                "title" => $spec_item_titles[$n],
                "show" => $spec_item_shows[$n],
                "thumb" => save_media($spec_item_thumbs[$n]),
                "virtual" => $data['type'] == 3 ? $spec_item_virtuals[$n] : 0
            );
            $f = "spec_item_thumb_" . $get_item_id;
            if (is_numeric($get_item_id)) {
                pdo_update("elapp_shop_goods_spec_item", $d, array("id" => $get_item_id));
                $item_id = $get_item_id;
            } else {
                pdo_insert("elapp_shop_goods_spec_item", $d);
                $item_id = pdo_insertid();
            }
            $itemids[] = $item_id;
            $d['get_id'] = $get_item_id;
            $d['id'] = $item_id;
            $spec_items[] = $d;
        }
        if (count($itemids) > 0) {
            pdo_query("delete from " . tablename('elapp_shop_goods_spec_item') . " where uniacid={$_W['uniacid']} and specid=$spec_id and id not in (" . implode(",", $itemids) . ")");
        } else {
            pdo_query("delete from " . tablename('elapp_shop_goods_spec_item') . " where uniacid={$_W['uniacid']} and specid=$spec_id");
        }
        pdo_update("elapp_shop_goods_spec", array("content" => serialize($itemids)), array("id" => $spec_id));
        $specids[] = $spec_id;
    }
    if (count($specids) > 0) {
        pdo_query("delete from " . tablename('elapp_shop_goods_spec') . " where uniacid={$_W['uniacid']} and goodsid=$id and id not in (" . implode(",", $specids) . ")");
    } else {
        pdo_query("delete from " . tablename('elapp_shop_goods_spec') . " where uniacid={$_W['uniacid']} and goodsid=$id");
    }
    $optionArray = json_decode($_POST['optionArray'],true);
    $discountArray = json_decode($_POST['discountArray'],true);
    $specialpriceArray = json_decode($_POST['specialpriceArray'],true);// 特价通

    $isdiscountDiscountsArray = json_decode($_POST['isdiscountDiscountsArray'],true);
    $commissionArrayPost = json_decode($_POST['commissionArray'],true);//分销
    $clerkCommissionArrayPost = json_decode($_POST['clerkCommissionArray'],true);//虚店店员
    $doctorCommissionArrayPost = json_decode($_POST['doctorCommissionArray'],true);//医生
    $businessCommissionArrayPost = json_decode($_POST['businessCommissionArray'],true);//招商经理
    $ownerCommissionArrayPost = json_decode($_POST['ownerCommissionArray'],true);//虚店店长
    $copartnerCommissionArrayPost = json_decode($_POST['copartnerCommissionArray'],true);//合伙人


    $option_idss = $optionArray['option_ids'];
    $len = is_array($option_idss)?count($option_idss):0;
    $optionids = array();
    $levelArray = array();// 会员折扣
    $specialArray = array();// 特价通
    $isDiscountsArray = array();// 促销
    $commissionArray = array();//分销
    $ownerCommissionArray = array();//虚店店长
    $clerkCommissionArray = array();//虚店店员
    $doctorCommissionArray = array();//医生
    $businessCommissionArray = array();//招商经理
    $copartnerCommissionArray = array();//合伙人
    $commissionDefaultArray = array();//分销
    $ownerCommissionDefaultArray = array();//虚店店长
    $clerkCommissionDefaultArray = array();//虚店店员
    $doctorCommissionDefaultArray = array();//虚店店员
    $businessCommissionDefaultArray = array();//虚店店员
    $copartnerCommissionDefaultArray = array();//合伙人

    for ($k = 0; $k < $len; $k++) {
        $option_id = "";
        $ids = $option_idss[$k];
        $get_option_id = $optionArray['option_id'][$k];
        $idsarr = explode("_", $ids);
        $newids = array();
        foreach ($idsarr as $key => $ida) {
            foreach ($spec_items as $it) {
                if ($it['get_id'] == $ida) {
                    $newids[] = $it['id'];
                    break;
                }
            }
        }

        $newids = implode("_", $newids);
        $a = array(
            "uniacid" => $_W['uniacid'],
            "title" => $optionArray['option_title'][$k],
            "productprice" => $optionArray['option_productprice'][$k],
            "costprice" => $optionArray['option_costprice'][$k],
            "marketprice" => $optionArray['option_marketprice'][$k],
            "presellprice" => $optionArray['option_presellprice'][$k],
            "stock" => $optionArray['option_stock'][$k],
            "weight" => $optionArray['option_weight'][$k],
            "goodssn" => $optionArray['option_goodssn'][$k],
            "productsn" => $optionArray['option_productsn'][$k],
            "goodsid" => $id,
            "specs" => $newids,
            'virtual' => $data['type'] == 3 ? $optionArray['option_virtual'][$k] : 0,
        );

        if($goodstype==4){
            $a['presellprice']=0;
            $a['productprice']=0;
            $a['costprice'] = 0;
            $a['marketprice']=floatval($_GPC['intervalprice1']);
        }

        /*$totalstocks+=$a['stock'];*/
        if (is_numeric($a['stock'])) {
            $totalstocks += $a['stock'];
        }
        if (empty($get_option_id)){
            pdo_insert("elapp_shop_goods_option", $a);
            $option_id = pdo_insertid();
        } else {
            pdo_update("elapp_shop_goods_option", $a, array('id' => $get_option_id));
            plog('goods.price.edit', "更改多规格商品  规格ID: {$get_option_id} 属性: {$a['title']} <br>价格为".$a['marketprice']);
            if( p('groups') ){
                pdo_update( 'elapp_shop_groups_goods_option' , array( 'title' => $a['title'],'specs' => $newids ) , array( 'goods_option_id' => $get_option_id ) );
            }
            $option_id = $get_option_id;
        }
        $optionids[] = $option_id;
        //会员
        foreach ($levels as $level) {
            $levelArray[$level['key']]['option'.$option_id] = $discountArray['discount_' . $level['key']][$k];
            $isDiscountsArray[$level['key']]['option'.$option_id] = $isdiscountDiscountsArray['isdiscount_discounts_'.$level['key']][$k];
        }
        // 特价通
        $specialArray['clerk']['option'.$option_id] = $specialpriceArray['specialprice_clerk'][$k];

        //分销
        foreach ($commission_level as $level) {
            if($level['key']=='default'){
                $commissionArray[$level['key']]['option'.$option_id] = $commissionArrayPost['commission']['commission_level_' . $level['key'] . "_" . $ids];
            }else{
                $commissionArray[$level['key']]['option'.$option_id] = $commissionArrayPost['commission']['commission_level_' . $level['id'] . "_" . $ids];
            }
        }
        //虚店店长
        foreach ($ownerCommission_level as $level) {
            if($level['key']=='default'){
                $ownerCommissionArray[$level['key']]['option'.$option_id] = $ownerCommissionArrayPost['ownerCommission']['ownerCommission_level_' . $level['key'] . "_" . $ids];
            }else{
                $ownerCommissionArray[$level['key']]['option'.$option_id] = $ownerCommissionArrayPost['ownerCommission']['ownerCommission_level_' . $level['id'] . "_" . $ids];
            }
        }
        //虚店店员
        foreach ($clerkCommission_level as $level) {
            if($level['key']=='default'){
                $clerkCommissionArray[$level['key']]['option'.$option_id] = $clerkCommissionArrayPost['clerkCommission']['clerkCommission_level_' . $level['key'] . "_" . $ids];
            }else{
                $clerkCommissionArray[$level['key']]['option'.$option_id] = $clerkCommissionArrayPost['clerkCommission']['clerkCommission_level_' . $level['id'] . "_" . $ids];
            }
        }
        //医生
        foreach ($doctorCommission_level as $level) {
            if($level['key']=='default'){
                $doctorCommissionArray[$level['key']]['option'.$option_id] = $doctorCommissionArrayPost['doctorCommission']['doctorCommission_level_' . $level['key'] . "_" . $ids];
            }else{
                $doctorCommissionArray[$level['key']]['option'.$option_id] = $doctorCommissionArrayPost['doctorCommission']['doctorCommission_level_' . $level['id'] . "_" . $ids];
            }

        }
        //招商经理
        foreach ($businessCommission_level as $level) {
            if($level['key']=='default'){
                $businessCommissionArray[$level['key']]['option'.$option_id] = $businessCommissionArrayPost['businessCommission']['businessCommission_level_' . $level['key'] . "_" . $ids];
            }else{
                $businessCommissionArray[$level['key']]['option'.$option_id] = $businessCommissionArrayPost['businessCommission']['businessCommission_level_' . $level['id'] . "_" . $ids];
            }

        }
        //合伙人
        foreach ($copartnerCommission_level as $level) {
            if($level['key']=='default'){
                $copartnerCommissionArray[$level['key']]['option'.$option_id] = $copartnerCommissionArrayPost['copartnerCommission']['copartnerCommission_level_' . $level['key'] . "_" . $ids];
            }else{
                $copartnerCommissionArray[$level['key']]['option'.$option_id] = $copartnerCommissionArrayPost['copartnerCommission']['copartnerCommission_level_' . $level['id'] . "_" . $ids];
            }
        }
    }
    //会员折扣数据
    if ((int)$_GPC['discounts']['type'] == 1 && $data['hasoption']){
        $discounts_arr = array('type' => (int)$_GPC['discounts']['type'],);
        $discounts_arr = array_merge($discounts_arr,$levelArray);
        $discounts_json = json_encode($discounts_arr);
    }else{
        $discounts_json = is_array($_GPC['discounts']) ? json_encode($_GPC['discounts']) : json_encode(array());
    }
    $discounts_json_arr = json_decode($discounts_json,true);
    foreach ($levels as $l) {
        $levelPrice = false;
        // 确保 discounts_json_arr[$l['key']] 是一个数组
        if (is_array($discounts_json_arr[$l['key']])) {
            // 遍历数组，查找折扣信息
            foreach ($discounts_json_arr[$l['key']] as $key => $value) {
                if (is_string($value) && strpos($value, '%') !== false) {
                    $discountPercentage = str_replace('%', '', $value);
                    if ($discountPercentage > 100) {
                        show_json(0, '[' . $l['levelname'] . '] 的折扣设置不能大于100%');
                    }
                    $levelPrice = round($data['marketprice'] * $discountPercentage / 100, 2);
                    break; // 找到折扣信息后跳出循环
                } else if (is_numeric($value) && $value > 0) {
                    $levelPrice = $value;
                    break; // 找到最终价后跳出循环
                }
            }
        } else {
            // 处理非数组的情况
            // 这里进行处理跳过或记录错误
            continue;
        }

        if (false !== $levelPrice) {
            // 如果折扣字段设置了，判断零售价是否大于会员折扣，如果大于，则提示保存失败
            if ($data['marketprice'] < $levelPrice) {
                show_json(0, '[' . $l['levelname'] . ']会员折扣 '. $levelPrice . ' 不能大于零售价 ' . $data['marketprice']);
            }
        }
    }

    //更新数据
    pdo_update('elapp_shop_goods', array('discounts'=>$discounts_json), array('id' => $id));

    //特价通数据
    if ((int)$_GPC['specialprice']['type'] == 1 && $data['hasoption']){
        $specialprice_arr = array('type' => (int)$_GPC['specialprice']['type'],);
        $specialprice_arr = array_merge($specialprice_arr,$specialArray);
        $specialprice_json = json_encode($specialprice_arr);
    }else{
        $specialprice_json = is_array($_GPC['specialprice']) ? json_encode($_GPC['specialprice']) : json_encode(array());
    }
    $specialprice_json_arr = json_decode($specialprice_json,true);

    $specialpricePrice = false;
    // 确保 discounts_json_arr[$l['key']] 是一个数组
    if (is_string($specialprice_json_arr['clerk']) && strpos($specialprice_json_arr['clerk'], '%') !== false) {
        $specialpricePercentage = str_replace('%', '', $specialprice_json_arr['clerk']);
        if ($specialpricePercentage > 100) {
            show_json(0, '[云店长] 的折扣设置不能大于100%');
        }
        $specialpricePrice = round($data['marketprice'] * $specialpricePercentage / 100, 2);
    } else if (is_numeric($specialprice_json_arr['clerk']) && $specialprice_json_arr['clerk'] > 0) {
        $specialpricePrice = $specialprice_json_arr['clerk'];
    } else {
        // 处理非数组的情况
        // 这里进行处理跳过或记录错误
    }

    if (false !== $specialpricePrice) {
        // 如果折扣字段设置了，判断零售价是否大于会员折扣，如果大于，则提示保存失败
        if ($data['marketprice'] < $specialpricePrice) {
            show_json(0, '[云店长]折扣 '. $specialpricePrice . ' 不能大于零售价 ' . $data['marketprice']);
        }
    }
    //更新数据
    pdo_update('elapp_shop_goods', array('specialprice'=>$specialprice_json), array('id' => $id));

    //促销
    $has_merch = 0;
    $old_isdiscount_discounts = json_decode($item['isdiscount_discounts'], true);
    if (!empty($old_isdiscount_discounts['merch'])) {
        $has_merch = 1;
    }
    if (!empty($isDiscountsArray) && $data['hasoption']){
        $is_discounts_arr = array_merge(array('type' => 1),$isDiscountsArray);
        if ($has_merch == 1) {
            $is_discounts_arr['merch'] = $old_isdiscount_discounts['merch'];
        }
        $is_discounts_json = json_encode($is_discounts_arr);
    }else{
        foreach ($levels as $level){
            if ($level['key']=='default'){
                $isDiscountsDefaultArray[$level['key']]['option0'] = $_GPC['isdiscount_discounts_level_' . $level['key'] . "_default"];
            }else{
                $isDiscountsDefaultArray[$level['key']]['option0'] = $_GPC['isdiscount_discounts_level_' . $level['id'] . "_default"];
            }
        }
        $is_discounts_arr = array_merge(array('type' => 0),$isDiscountsDefaultArray);
        if ($has_merch == 1) {
            $is_discounts_arr['merch'] = $old_isdiscount_discounts['merch'];
        }
        $is_discounts_json = is_array($is_discounts_arr) ? json_encode($is_discounts_arr) : json_encode(array());;
    }
    pdo_update('elapp_shop_goods', array('isdiscount_discounts'=>$is_discounts_json), array('id' => $id));

    //分销
    if(!empty($commissionArray) && $data['hasoption']){
        $commissionArray = array_merge(array('type'=>(int)$_GPC['commission_type']),$commissionArray);
        $commission_arr = array(
            'commission' => is_array($commissionArray) ? json_encode($commissionArray) : json_encode(array())
        );
    }else{
        foreach ($commission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['commission_level_' . $level['key'] . "_default"]) )
                {
                    foreach ($_GPC['commission_level_' . $level['key'] . "_default"] as $key=>$value)
                    {
                        $commissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['commission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['commission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $commissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $commissionDefaultArray = array_merge(array('type'=>(int)$_GPC['commission_type']),$commissionDefaultArray);
        $commission_arr = array(
            'commission' => is_array($commissionDefaultArray) ? json_encode($commissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $commission_arr, array('id' => $id));

    //虚店店长
    if(!empty($ownerCommissionArray) && $data['hasoption']){
        $ownerCommissionArray = array_merge(array('type'=>(int)$_GPC['ownerCommission_type']),$ownerCommissionArray);
        $ownerCommission_arr = array(
            'ownerCommission' => is_array($ownerCommissionArray) ? json_encode($ownerCommissionArray) : json_encode(array())
        );
    }else{
        foreach ($ownerCommission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['ownerCommission_level_' . $level['key'] . "_default"]) ) {
                    foreach ($_GPC['ownerCommission_level_' . $level['key'] . "_default"] as $key=>$value) {
                        $ownerCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['ownerCommission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['ownerCommission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $ownerCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $ownerCommissionDefaultArray = array_merge(array('type'=>(int)$_GPC['ownerCommission_type']),$ownerCommissionDefaultArray);
        $ownerCommission_arr = array(
            'ownerCommission' => is_array($ownerCommissionDefaultArray) ? json_encode($ownerCommissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $ownerCommission_arr, array('id' => $id));

    //虚店店员
    if(!empty($clerkCommissionArray) && $data['hasoption']){
        $clerkCommissionArray = array_merge(array('type'=>(int)$_GPC['clerkCommission_type']),$clerkCommissionArray);
        $clerkCommission_arr = array(
            'clerkCommission' => is_array($clerkCommissionArray) ? json_encode($clerkCommissionArray) : json_encode(array())
        );
    }else{
        foreach ($clerkCommission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['clerkCommission_level_' . $level['key'] . "_default"]) ) {
                    foreach ($_GPC['clerkCommission_level_' . $level['key'] . "_default"] as $key=>$value) {
                        $clerkCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['clerkCommission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['clerkCommission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $clerkCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $clerkCommissionDefaultArray = array_merge(array('type'=>(int)$_GPC['clerkCommission_type']),$clerkCommissionDefaultArray);
        $clerkCommission_arr = array(
            'clerkCommission' => is_array($clerkCommissionDefaultArray) ? json_encode($clerkCommissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $clerkCommission_arr, array('id' => $id));

    //医生
    if(!empty($doctorCommissionArray) && $data['hasoption']){
        $doctorCommissionArray = array_merge(array('type'=>(int)$_GPC['doctor_commission_type']),$doctorCommissionArray);
        $doctorCommission_arr = array(
            'doctor_commission' => is_array($doctorCommissionArray) ? json_encode($doctorCommissionArray) : json_encode(array())
        );
    }else{
        foreach ($doctorCommission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['doctorCommission_level_' . $level['key'] . "_default"]) ) {
                    foreach ($_GPC['doctorCommission_level_' . $level['key'] . "_default"] as $key=>$value) {
                        $doctorCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['doctorCommission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['doctorCommission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $doctorCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $doctorCommissionDefaultArray = array_merge(array('type'=>(int)$_GPC['doctor_commission_type']),$doctorCommissionDefaultArray);
        $doctorCommission_arr = array(
            'doctor_commission' => is_array($doctorCommissionDefaultArray) ? json_encode($doctorCommissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $doctorCommission_arr, array('id' => $id));

    //招商经理
    if(!empty($businessCommissionArray) && $data['hasoption']){
        $businessCommissionArray = array_merge(array('type'=>(int)$_GPC['business_commission_type']),$businessCommissionArray);
        $businessCommission_arr = array(
            'business_commission' => is_array($businessCommissionArray) ? json_encode($businessCommissionArray) : json_encode(array())
        );
    }else{
        foreach ($businessCommission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['businessCommission_level_' . $level['key'] . "_default"]) ) {
                    foreach ($_GPC['businessCommission_level_' . $level['key'] . "_default"] as $key=>$value) {
                        $businessCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['businessCommission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['businessCommission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $businessCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $businessCommissionDefaultArray = array_merge(array('type'=>(int)$_GPC['business_commission_type']),$businessCommissionDefaultArray);
        $businessCommission_arr = array(
            'business_commission' => is_array($businessCommissionDefaultArray) ? json_encode($businessCommissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $businessCommission_arr, array('id' => $id));

    //合伙人
    if(!empty($copartnerCommissionArray) && $data['hasoption']){
        $copartnerCommissionArray = array_merge(array('type'=>(int)$_GPC['copartnerCommission_type']),$copartnerCommissionArray);
        $copartnerCommission_arr = array(
            'copartnerCommission' => is_array($copartnerCommissionArray) ? json_encode($copartnerCommissionArray) : json_encode(array())
        );
    }else{
        foreach ($copartnerCommission_level as $level) {
            if ($level['key']=='default'){
                if ( !empty($_GPC['copartnerCommission_level_' . $level['key'] . "_default"]) ) {
                    foreach ($_GPC['copartnerCommission_level_' . $level['key'] . "_default"] as $key=>$value) {
                        $copartnerCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }else{
                if ( !empty($_GPC['copartnerCommission_level_' . $level['id'] . "_default"]) ){
                    foreach ($_GPC['copartnerCommission_level_' . $level['id'] . "_default"] as $key=>$value){
                        $copartnerCommissionDefaultArray[$level['key']]['option0'][] = $value;
                    }
                }
            }
        }
        $copartnerCommissionDefaultArray = array_merge(array('type'=>(int)$_GPC['copartnerCommission_type']),$copartnerCommissionDefaultArray);
        $copartnerCommission_arr = array(
            'copartnerCommission' => is_array($copartnerCommissionDefaultArray) ? json_encode($copartnerCommissionDefaultArray) : json_encode(array())
        );
    }
    pdo_update('elapp_shop_goods', $copartnerCommission_arr, array('id' => $id));

    if (count($optionids) > 0 && $data['hasoption'] !== 0) {
        pdo_query("delete from " . tablename('elapp_shop_goods_option') . " where goodsid=$id and id not in ( " . implode(',', $optionids) . ")");
        if( p('groups') ){
            pdo_query("delete from " . tablename('elapp_shop_groups_goods_option') . " where goodsid=$id and goods_option_id not in ( " . implode(',', $optionids) . ")");
        }

        $sql = "update ".tablename('elapp_shop_goods')." g set
            g.minprice = (select min(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id),
            g.maxprice = (select max(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id)
            where g.id = $id and g.hasoption=1";

        pdo_query($sql);
    } else {
        pdo_query("delete from " . tablename('elapp_shop_goods_option') . " where goodsid=$id");
        $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = $id and hasoption=0;";
        pdo_query($sql);

        if( p('groups') ){
            pdo_query("delete from " . tablename('elapp_shop_groups_goods_option') . " where goodsid=$id");
            pdo_update( 'elapp_shop_groups_goods' , array( 'more_spec' => 0 ) , array( 'gid' => $id ) );
        }
    }

    $sqlgoods = "SELECT id,title,thumb,marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,stock,description,merchsale FROM " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1";
    $goodsinfo = pdo_fetch($sqlgoods,array(':id'=>$id,':uniacid'=>$_W['uniacid']));
    $goodsinfo = m('goods')->getOneMinPrice($goodsinfo);

    pdo_update('elapp_shop_goods',array('minprice'=>$goodsinfo['minprice'],'maxprice'=>$goodsinfo['maxprice']),array('id'=>$id,'uniacid'=>$_W['uniacid']));

    if ($data['type'] == 3 && $com_virtual) {
        $com_virtual->updateGoodsStock($id);
    } else {
        if ($data['hasoption'] !== 0 && ($data['totalcnf'] != 3) && empty($data['unite_total'])) {
            pdo_update("elapp_shop_goods", array("stock" => $totalstocks), array("id" => $id));
        }
    }

    // 保存事件配置
    if (isset($_GPC['event_order_buy_send_member_card']) && $_GPC['event_order_buy_send_member_card']) {
        $event = [
            \app\controller\activity\Action::ORDER_PAY => [
                'send_member_card' => intval($_GPC['event_order_buy_send_member_card']),
            ]
        ];
        (new \app\model\DiyattrsModel())->setValue(
            \app\model\DiyattrsEnums::TYPE_GOODS,
            $id,
            \app\model\DiyattrsEnums::EVENT_CONFIG,
            $event);
    } else {
        (new \app\model\DiyattrsModel())->deleteValue(
            \app\model\DiyattrsEnums::TYPE_GOODS,
            $id,
            \app\model\DiyattrsEnums::EVENT_CONFIG);
    }

    show_json(1,array('url'=>webUrl('goods/index/edit', array('id' => $id,'tab'=>str_replace("#tab_","",$_GPC['tab'])))));
}

if (!empty($id)) {
    if (empty($item)) {
        $this->message('抱歉，商品不存在或是已经删除！', '', 'error');
    }

    $noticetype = explode(',', $item['noticetype']);
    $cates = explode(',', $item['cates']);
    //分销
    $commission = json_decode($item['commission'], true);
    if (isset($commission['type'])){
        $commission_type = $commission['type'];
        unset($commission['type']);
    }
    $buyagain_commission = array();
    if (!empty($item['buyagain_commission'])){
        $buyagain_commission = json_decode($item['buyagain_commission'], true);
    }
    //虚店店员
    $clerkCommission = json_decode($item['clerkCommission'], true);
    if (isset($clerkCommission['type'])){
        $clerkCommission_type = $clerkCommission['type'];
        unset($clerkCommission['type']);
    }
    $buyagain_clerkCommission = array();
    if (!empty($item['buyagain_clerkCommission'])){
        $buyagain_clerkCommission = json_decode($item['buyagain_clerkCommission'], true);
    }
    //医生
    $doctorCommission = json_decode($item['doctor_commission'], true);
    if (isset($doctorCommission['type'])){
        $doctor_commission_type = $doctorCommission['type'];
        unset($doctorCommission['type']);
    }
    $buyagain_doctorCommission = array();
    if (!empty($item['doctor_buyagain_commission'])){
        $buyagain_doctorCommission = json_decode($item['doctor_buyagain_commission'], true);
    }
    //虚店店长
    $ownerCommission = json_decode($item['ownerCommission'], true);
    if (isset($ownerCommission['type'])){
        $ownerCommission_type = $ownerCommission['type'];
        unset($ownerCommission['type']);
    }
    $buyagain_ownerCommission = array();
    if (!empty($item['buyagain_ownerCommission'])){
        $buyagain_ownerCommission = json_decode($item['buyagain_ownerCommission'], true);
    }
    //招商经理
    $businessCommission = json_decode($item['business_commission'], true);
    if (isset($businessCommission['type'])){
        $business_commission_type = $businessCommission['type'];
        unset($businessCommission['type']);
    }
    $buyagain_businessCommission = array();
    if (!empty($item['business_buyagain_commission'])){
        $buyagain_businessCommission = json_decode($item['business_buyagain_commission'], true);
    }
    //合伙人
    $copartnerCommission = json_decode($item['copartnerCommission'], true);
    if (isset($copartnerCommission['type'])){
        $copartnerCommission_type = $copartnerCommission['type'];
        unset($copartnerCommission['type']);
    }
    $buyagain_copartnerCommission = array();
    if (!empty($item['buyagain_copartnerCommission'])){
        $buyagain_copartnerCommission = json_decode($item['buyagain_copartnerCommission'], true);
    }

    $discounts = json_decode($item['discounts'], true);
    $isdiscount_discounts = json_decode($item['isdiscount_discounts'], true);
    // 特价通
    $specialprice = json_decode($item['specialprice'], true);

    $allspecs = pdo_fetchall("select * from " . tablename('elapp_shop_goods_spec') . " where goodsid=:id order by displayorder asc", array(":id" => $id));
    foreach ($allspecs as &$s) {
        $s['items'] = pdo_fetchall("select a.id,a.specid,a.title,a.thumb,a.show,a.displayorder,a.valueId,a.virtual,b.title as title2 from " . tablename('elapp_shop_goods_spec_item') . " a left join " . tablename('elapp_shop_virtual_type') . " b on b.id=a.virtual  where a.specid=:specid order by a.displayorder asc", array(":specid" => $s['id']));
    }
    unset($s);

    $params = pdo_fetchall("select * from " . tablename('elapp_shop_goods_param') . " where goodsid=:id order by displayorder asc", array(':id' => $id));

    if (!empty($item['thumb'])) {
        $piclist =array_merge( array($item['thumb']), iunserializer($item['thumb_url']) );
    }
    $item['content'] = m('common')->html_to_images($item['content']);
    $item['buycontent'] = m('common')->html_to_images($item['buycontent']);

    $html = "";
    $discounts_html='';
    $specialprice_html='';// 特价通
    $commission_html='';//分销
    $ownerCommission_html='';//虚店店长
    $clerkCommission_html='';//虚店店员
    $doctorCommission_html='';//虚店店员
    $businessCommission_html='';//虚店店员
    $copartnerCommission_html='';//合伙人

    $isdiscount_discounts_html='';// 促销
    $options = pdo_fetchall("select * from " . tablename('elapp_shop_goods_option') . " where goodsid=:id order by id asc", array(':id' => $id));
    $specs = array();
    if (count($options) > 0) {
        $specitemids = explode("_", $options[0]['specs']);
        foreach ($specitemids as $itemid) {
            foreach ($allspecs as $ss) {
                $items = $ss['items'];
                foreach ($items as $it) {
                    if ($it['id'] == $itemid) {
                        $specs[] = $ss;
                        break;
                    }
                }
            }
        }
        $html = '';
        $html .= '<table class="table table-bordered table-condensed">';
        $html .= '<thead class="navbar-inner">';
        $html .= '<tr class="active">';
        //会员
        $discounts_html .= '<table class="table table-bordered table-condensed">';
        $discounts_html .= '<thead>';
        $discounts_html .= '<tr class="active">';

        // 特价通
        $specialprice_html .= '<table class="table table-bordered table-condensed">';
        $specialprice_html .= '<thead>';
        $specialprice_html .= '<tr class="active">';

        //分销
        $commission_html .= '<table class="table table-bordered table-condensed">';
        $commission_html .= '<thead>';
        $commission_html .= '<tr class="active">';
        //虚店店长
        $ownerCommission_html .= '<table class="table table-bordered table-condensed">';
        $ownerCommission_html .= '<thead>';
        $ownerCommission_html .= '<tr class="active">';
        //虚店店员
        $clerkCommission_html .= '<table class="table table-bordered table-condensed">';
        $clerkCommission_html .= '<thead>';
        $clerkCommission_html .= '<tr class="active">';
        //医生
        $doctorCommission_html .= '<table class="table table-bordered table-condensed">';
        $doctorCommission_html .= '<thead>';
        $doctorCommission_html .= '<tr class="active">';
        //招商经理
        $businessCommission_html .= '<table class="table table-bordered table-condensed">';
        $businessCommission_html .= '<thead>';
        $businessCommission_html .= '<tr class="active">';
        //合伙人
        $copartnerCommission_html .= '<table class="table table-bordered table-condensed">';
        $copartnerCommission_html .= '<thead>';
        $copartnerCommission_html .= '<tr class="active">';

        //促销
        $isdiscount_discounts_html .= '<table class="table table-bordered table-condensed">';
        $isdiscount_discounts_html .= '<thead>';
        $isdiscount_discounts_html .= '<tr class="active">';

        $len = count($specs);
        $newlen = 1;
        $h = array();
        $rowspans = array();
        for ($i = 0; $i < $len; $i++) {
            $html .= "<th>" . $specs[$i]['title'] . "</th>";
            $discounts_html .= "<th>" . $specs[$i]['title'] . "</th>";//会员
            $specialprice_html .= "<th>" . $specs[$i]['title'] . "</th>";//特价通
            $commission_html .= "<th>" . $specs[$i]['title'] . "</th>";//分销
            $ownerCommission_html .= "<th>" . $specs[$i]['title'] . "</th>";//虚店店长
            $clerkCommission_html .= "<th>" . $specs[$i]['title'] . "</th>";//虚店店员
            $doctorCommission_html .= "<th>" . $specs[$i]['title'] . "</th>";//医生
            $businessCommission_html .= "<th>" . $specs[$i]['title'] . "</th>";//招商经理
            $copartnerCommission_html .= "<th>" . $specs[$i]['title'] . "</th>";//合伙人
            $isdiscount_discounts_html .= "<th>" . $specs[$i]['title'] . "</th>";//促销

            $itemlen = count($specs[$i]['items']);
            if ($itemlen <= 0) {
                $itemlen = 1;
            }
            $newlen *= $itemlen;
            $h = array();
            for ($j = 0; $j < $newlen; $j++) {
                $h[$i][$j] = array();
            }
            $l = count($specs[$i]['items']);
            $rowspans[$i] = 1;
            for ($j = $i + 1; $j < $len; $j++) {
                $rowspans[$i]*= count($specs[$j]['items']);
            }
        }
        $canedit = ce('goods',$item);
        if($canedit){
            //会员
            foreach ($levels as $level) {
                $discounts_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div><div class="input-group"><input type="text" class="form-control  input-sm discount_'.$level['key'].'_all" VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'discount_'.$level['key'].'\');"></a></span></div></div></th>';
                $isdiscount_discounts_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div><div class="input-group"><input type="text" class="form-control  input-sm isdiscount_discounts_'.$level['key'].'_all" VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'isdiscount_discounts_'.$level['key'].'\');"></a></span></div></div></th>';
            }

            //特价通
            $specialprice_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.'云店长'.'</div><div class="input-group"><input type="text" class="form-control  input-sm specialprice_clerk_all" VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'specialprice_clerk'.'\');"></a></span></div></div></th>';
            //分销
            foreach ($commission_level as $level) {
                $commission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //虚店店长
            foreach ($ownerCommission_level as $level) {
                $ownerCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //虚店店员
            foreach ($clerkCommission_level as $level) {
                $clerkCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //医生
            foreach ($doctorCommission_level as $level) {
                $doctorCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //招商经理
            foreach ($businessCommission_level as $level) {
                $businessCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //合伙人
            foreach ($copartnerCommission_level as $level) {
                $copartnerCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }

            if($item['type']==3) {
                $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">库存</div><div class="input-group"><input type="text" class="form-control input-sm option_stock_all" readonly="readonly" VALUE=""  /><span class="input-group-addon disabled"  ><a href="javascript:;"  class="fa fa-angle-double-down" title="批量设置" ></a></span></div></div></th>';
            }else{
                $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">库存</div><div class="input-group"><input type="text" class="form-control input-sm option_stock_all"  VALUE=""  /><span class="input-group-addon"  ><a href="javascript:;"  class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_stock\');"></a></span></div></div></th>';
            }
            $html .= '<th class="type-4"><div class=""><div style="padding-bottom:10px;text-align:center;">预售价</div><div class="input-group"><input type="text" class="form-control  input-sm option_presell_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_presell\');"></a></span></div></div></th>';
            $html .= '<th class="type-4"><div class=""><div style="padding-bottom:10px;text-align:center;">现价</div><div class="input-group"><input type="text" class="form-control  input-sm option_marketprice_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_marketprice\');"></a></span></div></div></th>';
            $html .= '<th class="type-4"><div class=""><div style="padding-bottom:10px;text-align:center;">原价</div><div class="input-group"><input type="text" class="form-control input-sm option_productprice_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_productprice\');"></a></span></div></div></th>';
            $html .= '<th class="type-4"><div class=""><div style="padding-bottom:10px;text-align:center;">成本价</div><div class="input-group"><input type="text" class="form-control input-sm option_costprice_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_costprice\');"></a></span></div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">编码</div><div class="input-group"><input type="text" class="form-control input-sm option_goodssn_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_goodssn\');"></a></span></div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">条码</div><div class="input-group"><input type="text" class="form-control input-sm option_productsn_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_productsn\');"></a></span></div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">重量（克）</div><div class="input-group"><input type="text" class="form-control input-sm option_weight_all"  VALUE=""/><span class="input-group-addon"><a href="javascript:;" class="fa fa-angle-double-down" title="批量设置" onclick="setCol(\'option_weight\');"></a></span></div></div></th>';
        }else {
            //会员
            foreach ($levels as $level) {
                $discounts_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
                $isdiscount_discounts_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }

            //特价通
            $specialprice_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.'云店长'.'</div></div></th>';

            //分销
            foreach ($commission_level as $level) {
                $commission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //虚店店长
            foreach ($ownerCommission_level as $level) {
                $ownerCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //虚店店员
            foreach ($clerkCommission_level as $level) {
                $clerkCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //医生
            foreach ($doctorCommission_level as $level) {
                $doctorCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //招商经理
            foreach ($businessCommission_level as $level) {
                $businessCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }
            //合伙人
            foreach ($copartnerCommission_level as $level) {
                $copartnerCommission_html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">'.$level['levelname'].'</div></div></th>';
            }

            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">库存</div></div></th>';
            $html .= '<th"><div class=""><div style="padding-bottom:10px;text-align:center;">预售价格</div></div></th>';
            $html .= '<th"><div class=""><div style="padding-bottom:10px;text-align:center;">销售价格</div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">市场价格</div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">成本价格</div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">商品编码</div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">商品条码</div></div></th>';
            $html .= '<th><div class=""><div style="padding-bottom:10px;text-align:center;">重量（克）</div></th>';
        }
        $html .= '</tr></thead>';
        $discounts_html .= '</tr></thead>';
        $isdiscount_discounts_html .= '</tr></thead>';
        // 特价通
        $specialprice_html .= '</tr></thead>';
        $commission_html .= '</tr></thead>';//分销
        $ownerCommission_html .= '</tr></thead>';//虚店店长
        $clerkCommission_html .= '</tr></thead>';//虚店店员
        $doctorCommission_html .= '</tr></thead>';//虚店店员
        $businessCommission_html .= '</tr></thead>';//招商经理
        $copartnerCommission_html .= '</tr></thead>';//合伙人
        for ($m = 0; $m < $len; $m++) {
            $k = 0;
            $kid = 0;
            $n = 0;
            for ($j = 0; $j < $newlen; $j++) {
                $rowspan = $rowspans[$m];
                if ($j % $rowspan == 0) {
                    $h[$m][$j] = array("html" => "<td class='full' rowspan='" . $rowspan . "'>" . $specs[$m]['items'][$kid]['title'] . "</td>", "id" => $specs[$m]['items'][$kid]['id']);
                } else {
                    $h[$m][$j] = array("html" => "", "id" => $specs[$m]['items'][$kid]['id']);
                }
                $n++;
                if ($n == $rowspan) {
                    $kid++;
                    if ($kid > count($specs[$m]['items']) - 1) {
                        $kid = 0;
                    }
                    $n = 0;
                }
            }
        }
        $hh = "";
        $dd = "";
        $sp = "";
        $cc = "";
        $vcc = "";
        $ccc = "";
        $dcc = "";
        $buscc = "";
        $cocc = "";
        for ($i = 0; $i < $newlen; $i++) {
            if ($i != 0) {
                $hh.="<tr style='border-top: 1px solid #eee'>";
            } else {
                $hh.="<tr>";
            }
            $dd.="<tr>";
            $sp.="<tr>";
            $cc.="<tr>";
            $vcc.="<tr>";
            $ccc.="<tr>";
            $dcc.="<tr>";
            $buscc.="<tr>";
            $cocc.="<tr>";
            $stcc.="<tr>";
            $ids = array();
            for ($j = 0; $j < $len; $j++) {
                $hh.=$h[$j][$i]['html'];
                $dd.=$h[$j][$i]['html'];
                $sp.=$h[$j][$i]['html'];
                $isdd.=$h[$j][$i]['html'];
                $cc.=$h[$j][$i]['html'];
                $vcc.=$h[$j][$i]['html'];
                $ccc.=$h[$j][$i]['html'];
                $dcc.=$h[$j][$i]['html'];
                $buscc.=$h[$j][$i]['html'];
                $cocc.=$h[$j][$i]['html'];
                $stcc.=$h[$j][$i]['html'];
                $ids[] = $h[$j][$i]['id'];
            }
            $ids = implode("_", $ids);
            $val = array("id" => "", "title" => "", "stock" => "","presell"=>"", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            $discounts_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            // 特价通
            $specialprice_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            // 促销
            $isdiscounts_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //分销
            $commission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //虚店店长
            $ownerCommission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //虚店店员
            $clerkCommission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //医生
            $doctorCommission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //招商经理
            $businessCommission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');
            //合伙人
            $copartnerCommission_val = array("id" => "", "title" => "", "level" => "", "costprice" => "", "productprice" => "", "marketprice" => "", "weight" => "",'virtual'=>'');

            //会员
            foreach ($levels as $level) {
                $discounts_val[$level['key']] = '';
                $isdiscounts_val[$level['key']] = '';
            }
            // 特价通
            $specialprice_val['clerk'] = '';
            //分销
            foreach ($commission_level as $level) {
                $commission_val[$level['key']] = '';
            }
            //虚店店长
            foreach ($ownerCommission_level as $level) {
                $ownerCommission_val[$level['key']] = '';
            }
            //虚店店员
            foreach ($clerkCommission_level as $level) {
                $clerkCommission_val[$level['key']] = '';
            }
            //医生
            foreach ($doctorCommission_level as $level) {
                $doctorCommission_val[$level['key']] = '';
            }
            //招商经理
            foreach ($businessCommission_level as $level) {
                $businessCommission_val[$level['key']] = '';
            }
            //合伙人
            foreach ($copartnerCommission_level as $level) {
                $copartnerCommission_val[$level['key']] = '';
            }
            //规格
            foreach ($options as $o) {
                if ($ids === $o['specs']) {
                    $val = array(
                        "id" => $o['id'],
                        "title" => $o['title'],
                        "stock" => $o['stock'],
                        "costprice" => $o['costprice'],
                        "productprice" => $o['productprice'],
                        "presell" => $o['presellprice'],
                        "marketprice" => $o['marketprice'],
                        "goodssn"=>$o['goodssn'],
                        "productsn"=>$o['productsn'],
                        "weight" => $o['weight'],
                        'virtual'=>$o['virtual']
                    );
                    //会员
                    $discount_val = array(
                        "id" => $o['id'],
                    );
                    foreach ($levels as $level) {
                        $discounts_val[$level['key']] = is_string($discounts[$level['key']]) ? '' : $discounts[$level['key']]['option'.$o['id']];
                        $isdiscounts_val[$level['key']] = is_string($isdiscount_discounts[$level['key']]) ? '' : $isdiscount_discounts[$level['key']]['option'.$o['id']];
                    }

                    // 特价通
                    $specialprice_val = array(
                        "id" => $o['id'],
                    );
                    $specialprice_val['clerk'] = is_string($specialprice['clerk']) ? '' : $specialprice['clerk']['option'.$o['id']];

                    //分销
                    $commission_val = array();
                    foreach ($commission_level as $level) {
                        $temp = is_string($commission[$level['key']]) ? '' : $commission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $commission_val[$level['key']][] = $t_val;
                            }
                        }
                    }
                    //虚店店长
                    $ownerCommission_val = array();
                    foreach ($ownerCommission_level as $level) {
                        $temp = is_string($ownerCommission[$level['key']]) ? '' : $ownerCommission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $ownerCommission_val[$level['key']][] = $t_val;
                            }
                        }
                    }
                    //虚店店员
                    $clerkCommission_val = array();
                    foreach ($clerkCommission_level as $level) {
                        $temp = is_string($clerkCommission[$level['key']]) ? '' : $clerkCommission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $clerkCommission_val[$level['key']][] = $t_val;
                            }
                        }
                    }
                    //医生
                    $doctorCommission_val = array();
                    foreach ($doctorCommission_level as $level) {
                        $temp = is_string($doctorCommission[$level['key']]) ? '' : $doctorCommission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $doctorCommission_val[$level['key']][] = $t_val;
                            }
                        }
                    }
                    //招商经理
                    $businessCommission_val = array();
                    foreach ($businessCommission_level as $level) {
                        $temp = is_string($businessCommission[$level['key']]) ? '' : $businessCommission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $businessCommission_val[$level['key']][] = $t_val;
                            }
                        }
                    }
                    //合伙人
                    $copartnerCommission_val = array();
                    foreach ($copartnerCommission_level as $level) {
                        $temp = is_string($copartnerCommission[$level['key']]) ? '' : $copartnerCommission[$level['key']]['option'.$o['id']];
                        if (is_array($temp)){
                            foreach ($temp as $t_val){
                                $copartnerCommission_val[$level['key']][] = $t_val;
                            }
                        }
                    }

                    unset($temp);
                    break;
                }
            }
            if($canedit){
                //会员
                foreach ($levels as $level) {
                    $dd .= '<td>';
                    $isdd .= '<td>';
                    if($level['key']=='default'){
                        $dd .= '<input data-name="discount_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control discount_'.$level['key'].' discount_'.$level['key'].'_' . $ids . '" value="' . $discounts_val[$level['key']] . '"/> ';
                        $isdd .= '<input data-name="isdiscount_discounts_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control isdiscount_discounts_'.$level['key'].' isdiscount_discounts_'.$level['key'].'_' . $ids . '" value="' . $isdiscounts_val[$level['key']] . '"/> ';
                    }else{
                        $dd .= '<input data-name="discount_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control discount_level'.$level['id'].' discount_level'.$level['id'].'_' . $ids . '" value="' . $discounts_val['level'.$level['id']] . '"/> ';
                        $isdd .= '<input data-name="isdiscount_discounts_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control isdiscount_discounts_level'.$level['id'].' isdiscount_discounts_level'.$level['id'].'_' . $ids . '" value="' . $isdiscounts_val['level'.$level['id']] . '"/> ';
                    }
                    $dd .= '</td>';
                    $isdd .= '</td>';
                }
                $dd .= '<input data-name="discount_id_' . $ids . '"  type="hidden" class="form-control discount_id discount_id_' . $ids . '" value="' . $discounts_val['id'] . '"/>';
                $dd .= '<input data-name="discount_ids"  type="hidden" class="form-control discount_ids discount_ids_' . $ids . '" value="' . $ids . '"/>';
                $dd .= '<input data-name="discount_title_' . $ids . '"  type="hidden" class="form-control discount_title discount_title_' . $ids . '" value="' . $discounts_val['title'] . '"/>';
                $dd .= '<input data-name="discount_virtual_' . $ids . '"  type="hidden" class="form-control discount_title discount_virtual_' . $ids . '" value="' . $discounts_val['virtual'] . '"/>';
                $dd .= "</tr>";

                $isdd .= '<input data-name="isdiscount_discounts_id_' . $ids . '"  type="hidden" class="form-control isdiscount_discounts_id isdiscount_discounts_id_' . $ids . '" value="' . $isdiscounts_val['id'] . '"/>';
                $isdd .= '<input data-name="isdiscount_discounts_ids"  type="hidden" class="form-control isdiscount_discounts_ids isdiscount_discounts_ids_' . $ids . '" value="' . $ids . '"/>';
                $isdd .= '<input data-name="isdiscount_discounts_title_' . $ids . '"  type="hidden" class="form-control isdiscount_discounts_title isdiscount_discounts_title_' . $ids . '" value="' . $isdiscounts_val['title'] . '"/>';
                $isdd .= '<input data-name="isdiscount_discounts_virtual_' . $ids . '"  type="hidden" class="form-control isdiscount_discounts_title isdiscount_discounts_virtual_' . $ids . '" value="' . $isdiscounts_val['virtual'] . '"/>';
                $isdd .= "</tr>";

                // 特价通
                $sp .= '<td>';
                $sp .= '<input data-name="specialprice_'.'clerk'.'_' . $ids . '"  type="text" class="form-control specialprice_'.'clerk'.' specialprice_clerk'.'_' . $ids . '" value="' . $specialprice_val['clerk'] . '"/> ';
                $sp .= '</td>';
                $sp .= '<input data-name="specialprice_id_' . $ids . '"  type="hidden" class="form-control specialprice_id specialprice_id_' . $ids . '" value="' . $specialprice_val['id'] . '"/>';
                $sp .= '<input data-name="specialprice_ids"  type="hidden" class="form-control specialprice_ids specialprice_ids_' . $ids . '" value="' . $ids . '"/>';
                $sp .= '<input data-name="specialprice_title_' . $ids . '"  type="hidden" class="form-control specialprice_title specialprice_title_' . $ids . '" value="' . $specialprice_val['title'] . '"/>';
                $sp .= '<input data-name="specialprice_virtual_' . $ids . '"  type="hidden" class="form-control specialprice_title specialprice_virtual_' . $ids . '" value="' . $specialprice_val['virtual'] . '"/>';
                $sp .= "</tr>";

                //分销
                foreach ($commission_level as $level) {
                    $cc .= '<td>';
                    if(!empty($commission_val) && isset($commission_val[$level['key']])){
                        foreach ($commission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $shopset_level;$c_i ++) {
                                        $cc .= '<input data-name="commission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control commission_'.$level['key'].' commission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$shopset_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $shopset_level;$c_i ++) {
                                        $cc .= '<input data-name="commission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control commission_level'.$level['id'].' commission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$shopset_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $shopset_level; $c_i++) {
                                $cc .= '<input data-name="commission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control commission_' . $level['key'] . ' commission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $shopset_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $shopset_level; $c_i++) {
                                $cc .= '<input data-name="commission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control commission_level' . $level['id'] . ' commission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $shopset_level) . '%;"/> ';
                            }
                        }
                    }
                    $cc .= '</td>';
                }
                //虚店店长
                foreach ($ownerCommission_level as $level) {
                    $vcc .= '<td>';
                    if(!empty($ownerCommission_val) && isset($ownerCommission_val[$level['key']])){
                        foreach ($ownerCommission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $vrshopSet_level;$c_i ++) {
                                        $vcc .= '<input data-name="ownerCommission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control ownerCommission_'.$level['key'].' ownerCommission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$vrshopSet_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $vrshopSet_level;$c_i ++) {
                                        $vcc .= '<input data-name="ownerCommission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control ownerCommission_level'.$level['id'].' ownerCommission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$vrshopSet_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $vrshopSet_level; $c_i++) {
                                $vcc .= '<input data-name="ownerCommission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control ownerCommission_' . $level['key'] . ' ownerCommission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $vrshopSet_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $vrshopSet_level; $c_i++) {
                                $vcc .= '<input data-name="ownerCommission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control ownerCommission_level' . $level['id'] . ' ownerCommission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $vrshopSet_level) . '%;"/> ';
                            }
                        }
                    }
                    $vcc .= '</td>';
                }
                //虚店店员
                foreach ($clerkCommission_level as $level) {
                    $ccc .= '<td>';
                    if(!empty($clerkCommission_val) && isset($clerkCommission_val[$level['key']])){
                        foreach ($clerkCommission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $clerkSet_level;$c_i ++) {
                                        $ccc .= '<input data-name="clerkCommission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control clerkCommission_'.$level['key'].' clerkCommission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$clerkSet_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $clerkSet_level;$c_i ++) {
                                        $ccc .= '<input data-name="clerkCommission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control clerkCommission_level'.$level['id'].' clerkCommission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$clerkSet_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $clerkSet_level; $c_i++) {
                                $ccc .= '<input data-name="clerkCommission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control clerkCommission_' . $level['key'] . ' clerkCommission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $clerkSet_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $clerkSet_level; $c_i++) {
                                $ccc .= '<input data-name="clerkCommission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control clerkCommission_level' . $level['id'] . ' clerkCommission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $clerkSet_level) . '%;"/> ';
                            }
                        }
                    }
                    $ccc .= '</td>';
                }
                //医生
                foreach ($doctorCommission_level as $level) {
                    $dcc .= '<td>';
                    if(!empty($doctorCommission_val) && isset($doctorCommission_val[$level['key']])){
                        foreach ($doctorCommission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $doctorSet_level;$c_i ++) {
                                        $dcc .= '<input data-name="doctorCommission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control doctorCommission_'.$level['key'].' doctorCommission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$doctorSet_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $doctorSet_level;$c_i ++) {
                                        $dcc .= '<input data-name="doctorCommission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control doctorCommission_level'.$level['id'].' doctorCommission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$doctorSet_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $doctorSet_level; $c_i++) {
                                $dcc .= '<input data-name="doctorCommission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control doctorCommission_' . $level['key'] . ' doctorCommission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $doctorSet_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $doctorSet_level; $c_i++) {
                                $dcc .= '<input data-name="doctorCommission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control doctorCommission_level' . $level['id'] . ' doctorCommission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $doctorSet_level) . '%;"/> ';
                            }
                        }
                    }
                    $dcc .= '</td>';
                }
                //招商经理
                foreach ($businessCommission_level as $level) {
                    $buscc .= '<td>';
                    if(!empty($businessCommission_val) && isset($businessCommission_val[$level['key']])){
                        foreach ($businessCommission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $copartnerSet_level;$c_i ++) {
                                        $buscc .= '<input data-name="businessCommission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control businessCommission_'.$level['key'].' businessCommission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$copartnerSet_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $copartnerSet_level;$c_i ++) {
                                        $buscc .= '<input data-name="businessCommission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control businessCommission_level'.$level['id'].' businessCommission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$copartnerSet_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $copartnerSet_level; $c_i++) {
                                $buscc .= '<input data-name="businessCommission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control businessCommission_' . $level['key'] . ' businessCommission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $copartnerSet_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $copartnerSet_level; $c_i++) {
                                $buscc .= '<input data-name="businessCommission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control businessCommission_level' . $level['id'] . ' businessCommission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $copartnerSet_level) . '%;"/> ';
                            }
                        }
                    }
                    $buscc .= '</td>';
                }
                //合伙人
                foreach ($copartnerCommission_level as $level) {
                    $cocc .= '<td>';
                    if(!empty($copartnerCommission_val) && isset($copartnerCommission_val[$level['key']])){
                        foreach ($copartnerCommission_val as $c_key=>$c_val){
                            if($c_key == $level['key']){
                                if($level['key']=='default'){
                                    for ($c_i = 0;$c_i < $copartnerSet_level;$c_i ++) {
                                        $cocc .= '<input data-name="copartnerCommission_level_'.$level['key'].'_' . $ids . '"  type="text" class="form-control copartnerCommission_'.$level['key'].' copartnerCommission_'.$level['key'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$copartnerSet_level).'%;"/> ';
                                    }
                                }else{
                                    for ($c_i = 0;$c_i < $copartnerSet_level;$c_i ++) {
                                        $cocc .= '<input data-name="copartnerCommission_level_'.$level['id'].'_' . $ids . '"  type="text" class="form-control copartnerCommission_level'.$level['id'].' copartnerCommission_level'.$level['id'].'_' . $ids . '" value="' . $c_val[$c_i] . '" style="display:inline;width: '.(96/$copartnerSet_level).'%;"/> ';
                                    }
                                }
                            }
                        }
                    }else {
                        if($level['key']=='default'){
                            for ($c_i = 0; $c_i < $copartnerSet_level; $c_i++) {
                                $cocc .= '<input data-name="copartnerCommission_level_' . $level['key'] . '_' . $ids . '"  type="text" class="form-control copartnerCommission_' . $level['key'] . ' copartnerCommission_' . $level['key'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $copartnerSet_level) . '%;"/> ';
                            }
                        } else {
                            for ($c_i = 0; $c_i < $copartnerSet_level; $c_i++) {
                                $cocc .= '<input data-name="copartnerCommission_level_' . $level['id'] . '_' . $ids . '"  type="text" class="form-control copartnerCommission_level' . $level['id'] . ' copartnerCommission_level' . $level['id'] . '_' . $ids . '" value="" style="display:inline;width: ' . (96 / $copartnerSet_level) . '%;"/> ';
                            }
                        }
                    }
                    $cocc .= '</td>';
                }

                $cc .= '<input data-name="commission_id_' . $ids . '"  type="hidden" class="form-control commission_id commission_id_' . $ids . '" value="' . $commissions_val['id'] . '"/>';
                $cc .= '<input data-name="commission_ids"  type="hidden" class="form-control commission_ids commission_ids_' . $ids . '" value="' . $ids . '"/>';
                $cc .= '<input data-name="commission_title_' . $ids . '"  type="hidden" class="form-control commission_title commission_title_' . $ids . '" value="' . $commissions_val['title'] . '"/>';
                $cc .= '<input data-name="commission_virtual_' . $ids . '"  type="hidden" class="form-control commission_title commission_virtual_' . $ids . '" value="' . $commissions_val['virtual'] . '"/>';
                $cc .= "</tr>";

                $vcc .= '<input data-name="ownerCommission_id_' . $ids . '"  type="hidden" class="form-control ownerCommission_id ownerCommission_id_' . $ids . '" value="' . $ownerCommissions_val['id'] . '"/>';
                $vcc .= '<input data-name="ownerCommission_ids"  type="hidden" class="form-control ownerCommission_ids ownerCommission_ids_' . $ids . '" value="' . $ids . '"/>';
                $vcc .= '<input data-name="ownerCommission_title_' . $ids . '"  type="hidden" class="form-control ownerCommission_title ownerCommission_title_' . $ids . '" value="' . $ownerCommissions_val['title'] . '"/>';
                $vcc .= '<input data-name="ownerCommission_virtual_' . $ids . '"  type="hidden" class="form-control ownerCommission_title ownerCommission_virtual_' . $ids . '" value="' . $ownerCommissions_val['virtual'] . '"/>';
                $vcc .= "</tr>";

                $ccc .= '<input data-name="clerkCommission_id_' . $ids . '"  type="hidden" class="form-control clerkCommission_id clerkCommission_id_' . $ids . '" value="' . $clerkCommissions_val['id'] . '"/>';
                $ccc .= '<input data-name="clerkCommission_ids"  type="hidden" class="form-control clerkCommission_ids clerkCommission_ids_' . $ids . '" value="' . $ids . '"/>';
                $ccc .= '<input data-name="clerkCommission_title_' . $ids . '"  type="hidden" class="form-control clerkCommission_title clerkCommission_title_' . $ids . '" value="' . $clerkCommissions_val['title'] . '"/>';
                $ccc .= '<input data-name="clerkCommission_virtual_' . $ids . '"  type="hidden" class="form-control clerkCommission_title clerkCommission_virtual_' . $ids . '" value="' . $clerkCommissions_val['virtual'] . '"/>';
                $ccc .= "</tr>";

                $dcc .= '<input data-name="doctorCommission_id_' . $ids . '"  type="hidden" class="form-control doctorCommission_id doctorCommission_id_' . $ids . '" value="' . $doctorCommissions_val['id'] . '"/>';
                $dcc .= '<input data-name="doctorCommission_ids"  type="hidden" class="form-control doctorCommission_ids doctorCommission_ids_' . $ids . '" value="' . $ids . '"/>';
                $dcc .= '<input data-name="doctorCommission_title_' . $ids . '"  type="hidden" class="form-control doctorCommission_title doctorCommission_title_' . $ids . '" value="' . $doctorCommissions_val['title'] . '"/>';
                $dcc .= '<input data-name="doctorCommission_virtual_' . $ids . '"  type="hidden" class="form-control doctorCommission_title doctorCommission_virtual_' . $ids . '" value="' . $doctorCommissions_val['virtual'] . '"/>';
                $dcc .= "</tr>";

                $buscc .= '<input data-name="businessCommission_id_' . $ids . '"  type="hidden" class="form-control businessCommission_id businessCommission_id_' . $ids . '" value="' . $businessCommissions_val['id'] . '"/>';
                $buscc .= '<input data-name="businessCommission_ids"  type="hidden" class="form-control businessCommission_ids businessCommission_ids_' . $ids . '" value="' . $ids . '"/>';
                $buscc .= '<input data-name="businessCommission_title_' . $ids . '"  type="hidden" class="form-control businessCommission_title businessCommission_title_' . $ids . '" value="' . $businessCommissions_val['title'] . '"/>';
                $buscc .= '<input data-name="businessCommission_virtual_' . $ids . '"  type="hidden" class="form-control businessCommission_title businessCommission_virtual_' . $ids . '" value="' . $businessCommissions_val['virtual'] . '"/>';
                $buscc .= "</tr>";

                $cocc .= '<input data-name="copartnerCommission_id_' . $ids . '"  type="hidden" class="form-control copartnerCommission_id copartnerCommission_id_' . $ids . '" value="' . $copartnerCommissions_val['id'] . '"/>';
                $cocc .= '<input data-name="copartnerCommission_ids"  type="hidden" class="form-control copartnerCommission_ids copartnerCommission_ids_' . $ids . '" value="' . $ids . '"/>';
                $cocc .= '<input data-name="copartnerCommission_title_' . $ids . '"  type="hidden" class="form-control copartnerCommission_title copartnerCommission_title_' . $ids . '" value="' . $copartnerCommissions_val['title'] . '"/>';
                $cocc .= '<input data-name="copartnerCommission_virtual_' . $ids . '"  type="hidden" class="form-control copartnerCommission_title copartnerCommission_virtual_' . $ids . '" value="' . $copartnerCommissions_val['virtual'] . '"/>';
                $cocc .= "</tr>";

                $hh .= '<td>';
                if($item['type']==3) {
                    $hh .= '<input data-name="option_stock_' . $ids . '"  type="text" class="form-control option_stock option_stock_' . $ids . '" readonly="readonly" value=""/>';
                }else{
                    $hh .= '<input data-name="option_stock_' . $ids . '"  type="text" class="form-control option_stock option_stock_' . $ids . '" value="' . $val['stock'] . '"/>';
                }
                $hh .= '</td>';
                $hh .= '<input data-name="option_id_' . $ids . '"  type="hidden" class="form-control option_id option_id_' . $ids . '" value="' . $val['id'] . '"/>';
                $hh .= '<input data-name="option_ids"  type="hidden" class="form-control option_ids option_ids_' . $ids . '" value="' . $ids . '"/>';
                $hh .= '<input data-name="option_title_' . $ids . '"  type="hidden" class="form-control option_title option_title_' . $ids . '" value="' . $val['title'] . '"/>';
                $hh .= '<input data-name="option_virtual_' . $ids . '"  type="hidden" class="form-control option_virtual option_virtual_' . $ids . '" value="' . $val['virtual'] . '"/>';
                $hh .= '<td class="type-4"><input data-name="option_presell_' . $ids . '" type="text" class="form-control option_presell option_presell_' . $ids . '" value="' . $val['presell'] . '"/></td>';
                $hh .= '<td class="type-4"><input data-name="option_marketprice_' . $ids . '" type="text" class="form-control option_marketprice option_marketprice_' . $ids . '" value="' . $val['marketprice'] . '"/></td>';
                $hh .= '<td class="type-4"><input data-name="option_productprice_' . $ids . '" type="text" class="form-control option_productprice option_productprice_' . $ids . '" " value="' . $val['productprice'] . '"/></td>';
                $hh .= '<td class="type-4"><input data-name="option_costprice_' . $ids . '" type="text" class="form-control option_costprice option_costprice_' . $ids . '" " value="' . $val['costprice'] . '"/></td>';
                $hh .= '<td><input data-name="option_goodssn_' . $ids . '" type="text" class="form-control option_goodssn option_goodssn_' . $ids . '" " value="' . $val['goodssn'] . '"/></td>';
                $hh .= '<td><input data-name="option_productsn_' . $ids . '" type="text" class="form-control option_productsn option_productsn_' . $ids . '" " value="' . $val['productsn'] . '"/></td>';
                $hh .= '<td><input data-name="option_weight_' . $ids . '" type="text" class="form-control option_weight option_weight_' . $ids . '" " value="' . $val['weight'] . '"/></td>';
                $hh .= '</tr>';
            } else{
                $hh .= '<td>' . $val['stock'] . '</td>';
                $hh .= '<td>' . $val['presell'] . '</td>';
                $hh .= '<td>' . $val['marketprice'] . '</td>';
                $hh .= '<td>' . $val['productprice'] . '</td>';
                $hh .= '<td>' . $val['costprice'] . '</td>';
                $hh .= '<td>' . $val['goodssn'] . '</td>';
                $hh .= '<td>' . $val['productsn'] . '</td>';
                $hh .= '<td>' . $val['weight'] . '</td>';
                $hh .= '</tr>';
            }
        }
        //会员
        $discounts_html .= $dd;
        $discounts_html .= "</table>";
        $isdiscount_discounts_html .= $isdd;
        $isdiscount_discounts_html .= "</table>";

        // 特价通
        $specialprice_html .= $sp;
        $specialprice_html .= "</table>";

        $html .= $hh;
        $html .= "</table>";
        //分销
        $commission_html .= $cc;
        $commission_html .= "</tr>";
        $commission_html .= "</thead>";
        $commission_html .= "</table>";
        //虚店店员
        $clerkCommission_html .= $ccc;
        $clerkCommission_html .= "</tr>";
        $clerkCommission_html .= "</thead>";
        $clerkCommission_html .= "</table>";
        //医生
        $doctorCommission_html .= $dcc;
        $doctorCommission_html .= "</tr>";
        $doctorCommission_html .= "</thead>";
        $doctorCommission_html .= "</table>";
        //招商经理
        $businessCommission_html .= $buscc;
        $businessCommission_html .= "</tr>";
        $businessCommission_html .= "</thead>";
        $businessCommission_html .= "</table>";
        //虚店店长
        $ownerCommission_html .= $vcc;
        $ownerCommission_html .= "</tr>";
        $ownerCommission_html .= "</thead>";
        $ownerCommission_html .= "</table>";
        //合伙人
        $copartnerCommission_html .= $cocc;
        $copartnerCommission_html .= "</tr>";
        $copartnerCommission_html .= "</thead>";
        $copartnerCommission_html .= "</table>";
    }

    if ($item['showlevels'] != '') {
        $item['showlevels'] = explode(',', $item['showlevels']);
    }
    if ($item['buylevels'] != '') {
        $item['buylevels'] = explode(',', $item['buylevels']);
    }
    if ($item['showgroups'] != '') {
        $item['showgroups'] = explode(',', $item['showgroups']);
    }
    if ($item['buygroups'] != '') {
        $item['buygroups'] = explode(',', $item['buygroups']);
    }
    if ($item['showClerkLevels'] != '') {
        $item['showClerkLevels'] = explode(',', $item['showClerkLevels']);
    }
    if ($item['buyClerkLevels'] != '') {
        $item['buyClerkLevels'] = explode(',', $item['buyClerkLevels']);
    }
    //集团权限
    if ($item['showOrgs'] != '') {
        $item['showOrgs'] = explode(',', $item['showOrgs']);
    }
    if ($item['buyOrgs'] != '') {
        $item['buyOrgs'] = explode(',', $item['buyOrgs']);
    }

    if ($merchid == 0) {
        $stores = array();
        if (!empty($item['storeids'])) {
            $stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_store') . ' where id in (' . $item['storeids'] . ' ) and uniacid=' . $_W['uniacid']);
        }
    }

    if (!empty($item['noticeopenid'])) {
        $salers = array();
        if (isset($item['noticeopenid'])) {
            if (!empty($item['noticeopenid'])) {
                $openids = array();
                $strsopenids = explode(",", $item['noticeopenid']);
                foreach ($strsopenids as $openid) {
                    $openids[] = "'" . $openid . "'";
                }
                $salers = pdo_fetchall("select id,nickname,avatar,openid from " . tablename('elapp_shop_member') . ' where openid in (' . implode(",", $openids) . ") and uniacid={$_W['uniacid']}");
            }
        }
    }
}
$orgs = pdo_fetchall('select * from ' . tablename('elapp_shop_org_user') . ' where uniacid=:uniacid ', array(':uniacid' => $_W['uniacid']));
$dispatch_data = pdo_fetchall('select * from ' . tablename('elapp_shop_dispatch') . ' where uniacid=:uniacid and merchid=:merchid and enabled=1 order by displayorder desc', array(':uniacid' => $_W['uniacid'], ':merchid' =>$merchid));

if (p('commission')) {
    $com_set = p('commission')->getSet();
}
if (p('clerk')) {
    $clerk_set = p('clerk')->getSet();
}
if (p('doctor')) {
    $doctor_set = p('doctor')->getSet();
}
if (p('vrshop')) {
    $vrshop_set = p('vrshop')->getSet();
}
if (p('copartner')) {
    $copartner_set = p('copartner')->getSet();
}
if ($com_virtual) {
    $virtual_types = pdo_fetchall("select * from " . tablename('elapp_shop_virtual_type') . " where uniacid=:uniacid and merchid=:merchid and recycled = 0 order by id asc", array(":uniacid" => $_W['uniacid'], ':merchid' =>$merchid));
}

if ($merchid == 0) {
    $details = pdo_fetchall('select detail_logo,detail_shopname,detail_btntext1, detail_btnurl1 ,detail_btntext2,detail_btnurl2,detail_totaltitle from '
        . tablename('elapp_shop_goods') . " where uniacid=:uniacid and detail_shopname<>'' group by detail_shopname", array(':uniacid' => $_W['uniacid']));
    foreach ($details as &$d) {
        $d['detail_logo_url'] = tomedia($d['detail_logo']);
    }
    unset($d);
}

$areas = m('common')->getAreas();
//药品使用方法
$takeDirection_data = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_takedirection') . ' where uniacid=:uniacid and status=1 order by displayorder desc', array(':uniacid' => $_W['uniacid']));
//产品品牌
$brand_data = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_brand') . ' where uniacid=:uniacid and status=1 order by displayorder desc', array(':uniacid' => $_W['uniacid']));

if ($diyform) {
    $form_list = $diyform->getDiyformList();
    $dfields = iunserializer($item['diyfields']);
}

if(p('diypage')){
    $detailPages = p('diypage')->getPageList('allpage', " and type=5 ");
    $detailPages = $detailPages['list'];
}

if (p('activity')) {
    $activities = (new \app\model\ActivityModel())->getActivityList();
}

// 读取赠送会员卡列表
if (p('membercard')) {
    // 读取商品赠送会员卡设置
    $event_config = (new \app\model\DiyattrsModel())->getValue(
        \app\model\DiyattrsEnums::TYPE_GOODS,
        $item['id'],
        \app\model\DiyattrsEnums::EVENT_CONFIG, []);
    if ($event_config && isset($event_config[\app\controller\activity\Action::ORDER_PAY]['send_member_card'])) {
        $event_send_member_card_id = $event_config[\app\controller\activity\Action::ORDER_PAY]['send_member_card'];
    }
    $membercards = pdo_getall(
        'elapp_shop_member_card',
        array('uniacid' => $_W['uniacid'], 'status' => 1, 'isdelete' => 0),
        ['id','name','name2']);
} else {
    $events_config = [];
}


load()->func('tpl');
include $this->template('goods/post');
exit;