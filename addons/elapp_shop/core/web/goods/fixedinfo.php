<?php

namespace web\controller\goods;
use web\controller\WebPage;

class FixedinfoController extends WebPage{
	public function main(){
		global $_W,$_GPC;
		$shopSet = m('common')->getSysset('shop');
		$bottomFixedImageSetting = $shopSet['bottomFixedImage'];
		if ($_W['ispost']) {
			$images = empty($_GPC['fixedImages']) ? NULL : $_GPC['fixedImages'];
			$pricedesc = empty($_GPC['pricedesc']) ? NULL : m('common')->html_images($_GPC['pricedesc']);
			if (!empty($images) && isset($images)) {
				array_walk($images, function(&$value) {
					$value = tomedia($value);
				});
			}
			$bottomFixedImageSetting = array('shopStatus' => (bool) $_GPC['shopStatus'], 'merchStatus' => (bool) $_GPC['merchStatus'], 'urls' => $images, 'pricedesc' => $pricedesc);
			$shopSet['bottomFixedImage'] = $bottomFixedImageSetting;
			m('common')->updateSysset(array('shop' => $shopSet));
			show_json(1, '操作成功');
		}
		list($shopStatus, $merchStatus, $picList,$pricedesc) = array_values($bottomFixedImageSetting);
		include $this->template();
	}
}