<?php
namespace web\controller\goods;
use web\controller\WebPage;

class PrescriptionGroupController extends WebPage
{
	public function main()
	{
		global $_W, $_GPC;
		$uniacid = $_W['uniacid'];
		$params[':uniacid'] = $uniacid;
		$condition = '';
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;

		if ($_GPC['enabled'] != '') {
			$condition .= ' and status=' . intval($_GPC['enabled']);
		}

		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and name like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}

        $count = pdo_fetchall('select count(*) count, group_id from ' . tablename('elapp_shop_tcmd_prescription') . ' group by group_id');
        $count = array_column($count, 'count', 'group_id');
		$list = pdo_fetchall('SELECT id,uniacid,name,status,displayorder FROM ' . tablename('elapp_shop_tcmd_prescription_group') . " WHERE uniacid=:uniacid " . $condition . ' order by id limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);

        foreach ($list as $k=>$item) {
            $list[$k]['count'] = $count[$item['id']] ?? 0;
        }

		$total = pdo_fetchcolumn('SELECT count(1) FROM ' . tablename('elapp_shop_tcmd_prescription_group') . ' WHERE uniacid=:uniacid ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add()
	{
		$this->post();
	}

	public function edit()
	{
		$this->post();
	}

	protected function post()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);
		$uniacid = intval($_W['uniacid']);

		if (!empty($id)) {
			$item = pdo_fetch('SELECT id,uniacid,name,status,displayorder FROM ' . tablename('elapp_shop_tcmd_prescription_group') . "\r\n                    WHERE id=:id and uniacid=:uniacid limit 1 ", array(':id' => $id, ':uniacid' => $uniacid));
		}

		if ($_W['ispost']) {
			$data = array(
                'name' => trim($_GPC['name']),
                'status' => intval($_GPC['status']),
                'displayorder' => intval($_GPC['displayorder']),
            );

			if (!empty($item)) {
				pdo_update('elapp_shop_tcmd_prescription_group', $data, array('id' => $item['id']));
				plog('goods.prescription_group.edit', '修改平台药方分组 ID: ' . $id);
			}
			else {
				$data['uniacid'] = $uniacid;
				pdo_insert('elapp_shop_tcmd_prescription_group', $data);
				$id = pdo_insertid();
				plog('goods.prescription_group.add', '添加平台药方分组 ID: ' . $id);
			}

			show_json(1, array('url' => webUrl('goods/prescription_group')));
		}

		include $this->template();
	}

	public function delete()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}



		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_tcmd_prescription_group') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);

        pdo_begin();
		foreach ($items as $item) {

            // 如果该分组下有处方，不允许删除
            $count = pdo_count('elapp_shop_tcmd_prescription',['group_id'=>$item['id']]);
            if ($count>0) {
                pdo_rollback();
                show_json(0, array('message' => '分组 ['.$item['name'].']下有处方，不允许删除'));
            }

			pdo_delete('elapp_shop_tcmd_prescription_group', array('id' => $item['id']));
			plog('goods.prescription_group.delete', '删除平台药方分组<br/>ID: ' . $item['id'] . '<br/>分组名称: ' . $item['name']);
		}

        pdo_commit();

		show_json(1, array('url' => webUrl('goods.prescription_group')));
	}

	public function status()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}

		$items = pdo_fetchall('SELECT id,name FROM ' . tablename('elapp_shop_tcmd_prescription_group') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);

		if (empty($item)) {
			$item = array();
		}

		foreach ($items as $item) {
			pdo_update('elapp_shop_tcmd_prescription_group', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('goods.prescription_group.edit', ('修改平台药方分组状态<br/>ID: ' . $item['id'] . '<br/>平台药方分组名称: ' . $item['name'] . '<br/>状态: ' . $_GPC['status']) == 1 ? '上架' : '下架');
		}

		show_json(1, array('url' => webUrl('goods.prescription_group')));
	}
}
