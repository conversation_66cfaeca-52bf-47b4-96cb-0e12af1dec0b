<?php
namespace web\controller\goods;
use web\controller\WebPage;
class FormtypeController extends WebPage {
	public function main() {
		global $_W,$_GPC;
		$uniacid = $_W['uniacid'];
		$params[':uniacid'] = $uniacid;
		$condition = '';
		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
		if ($_GPC['enabled'] != '') {
			$condition .= ' and status=' . intval($_GPC['enabled']);
		}
		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' and formtype like :keyword';
			$params[':keyword'] = '%' . $_GPC['keyword'] . '%';
		}
		$formtype = pdo_fetchall('SELECT id,uniacid,formtype,formtypename,status,displayorder FROM ' . tablename('elapp_shop_goods_formtype') . "\r\n                WHERE uniacid=:uniacid " . $condition . ' order by id limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);
		$total = pdo_fetchcolumn('SELECT count(1) FROM ' . tablename('elapp_shop_goods_formtype') . ' WHERE uniacid=:uniacid ' . $condition, $params);
		$pager = pagination2($total, $pindex, $psize);
		include $this->template();
	}

	public function add() {
		$this->post();
	}

	public function edit() {
		$this->post();
	}

	protected function post() {
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$uniacid = intval($_W['uniacid']);

		if (!empty($id)) {
			$item = pdo_fetch('SELECT id,uniacid,formtype,formtypename,status,displayorder FROM ' . tablename('elapp_shop_goods_formtype') . "\r\n                    WHERE id=:id and uniacid=:uniacid limit 1 ", array(':id' => $id, ':uniacid' => $uniacid));

			if (json_decode($item['formtypename'], true)) {
				$formtypename = json_decode($item['formtypename'], true);
			} else {
				$formtypename = unserialize($item['formtypename']);
			}
		}
		
		if ($_W['ispost']) {
			if (empty($_GPC['formtypeid'])) {
				$_GPC['formtypeid'] = array();
			}
			if (empty($_GPC['formtypename'])) {
				$_GPC['formtypename'] = array();
			}
			$formtypename = [];			
			$formtypeids = $_GPC['formtypeid'];
			$formtypenames = $_GPC['formtypename'];
			//检查参数值是否重复
			$countedValues = array_count_values($formtypeids);
			$duplicates = array_filter($countedValues, function($value) {
				return $value > 1;
			});
			if (!empty($duplicates)) {
				show_json(0, '参数值有重复，请检查修改后再次提交！');
			}
			//合并参数值和参数名数组
			for ($i = 0; $i < count($formtypeids); $i++) {
				$record = [
					'id' => $formtypeids[$i],
					'name' => $formtypenames[$i]
				];
				$formtypename[] = $record;
			}
			
			$data = array(
				'formtype' => trim($_GPC['formtype']), 
				'formtypename' => serialize(array_filter($formtypename)),
				'status' => intval($_GPC['status'])
			);
			//更新数据
			if (!empty($item)) {
				pdo_update('elapp_shop_goods_formtype', $data, array('id' => $item['id']));
				plog('goods.formtype.edit', '修改剂型组 ID: ' . $id);
			} else {
				$data['uniacid'] = $uniacid;
				pdo_insert('elapp_shop_goods_formtype', $data);
				$id = pdo_insertid();
				plog('goods.formtype.add', '添加剂型组 ID: ' . $id);
			}

			show_json(1, array('url' => webUrl('goods/formtype/edit', array('id' => $id))));
		}

		include $this->template('goods/formtype/post');
	}

	public function delete()	{
		global $_W,$_GPC;
		$id = intval($_GPC['id']);

		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,formtype FROM ' . tablename('elapp_shop_goods_formtype') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);
		if (empty($item)) {
			$item = array();
		}
		foreach ($items as $item) {
			pdo_delete('elapp_shop_goods_formtype', array('id' => $item['id']));
			plog('goods.edit', '从回收站彻底删除剂型组<br/>ID: ' . $item['id'] . '<br/>剂型组名称: ' . $item['formtype']);
		}
		show_json(1, array('url' => referer()));
	}

	public function status(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id,formtype FROM ' . tablename('elapp_shop_goods_formtype') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);
		if (empty($item)) {
			$item = array();
		}
		foreach ($items as $item) {
			pdo_update('elapp_shop_goods_formtype', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
			plog('goods.formtype.edit', ('修改剂型组状态<br/>ID: ' . $item['id'] . '<br/>剂型组名称: ' . $item['formtype'] . '<br/>状态: ' . $_GPC['status']) == 1 ? '上架' : '下架');
		}
		show_json(1, array('url' => referer()));
	}

	public function query(){
		global $_W,$_GPC;
		$params = array();
		$params[':uniacid'] = $_W['uniacid'];
        $condition = '';
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
		$condition .= ' and uniacid=:uniacid and status = 1 ';
		if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
			$condition .= ' AND formtype LIKE :keywords ';
			$params[':keywords'] = '%' . $_GPC['keyword'] . '%';
		}
        $formtypes = pdo_fetchall('SELECT id,uniacid,formtype,formtypename,status,displayorder FROM ' . tablename('elapp_shop_goods_formtype') . " WHERE uniacid=:uniacid " . $condition . ' order by id limit ' . (($pindex - 1) * $psize) . ',' . $psize, $params);

        $total = pdo_fetchcolumn('SELECT count(1) FROM ' . tablename('elapp_shop_goods_formtype') . ' WHERE uniacid=:uniacid ' . $condition, $params);
        $pager = pagination2($total, $pindex, $psize);
        if (empty($formtypes)) {
			$formtypes = array();
		}
		foreach ($formtypes as $key => $value) {
			if (json_decode($value['formtypename'], true)) {
				$formtypes[$key]['formtypename'] = json_decode($value['formtypename'], true);
			} else {
				$formtypes[$key]['formtypename'] = unserialize($value['formtypename']);
			}
		}

		include $this->template('goods/formtype/query');
	}

	public function formtypefile(){
		global $_W,$_GPC;
		$id = intval($_GPC['cid']);//分组id
		$idToFind = intval($_GPC['id']);// 要查找的 标签id		
		if (empty($id)) {
			show_json(0, '您查找的剂型组不存在或已删除！');
		}
		$params = array(':uniacid' => $_W['uniacid'], ':id' => $id, ':status' => 1);
		$condition = ' and id = :id and uniacid=:uniacid and status = :status ';
		$formtypes = pdo_fetch('SELECT id,formtype,formtypename FROM ' . tablename('elapp_shop_goods_formtype') . ' WHERE 1 ' . $condition . ' order by id desc', $params);

		if (empty($formtypes)) {
			$formtypes = array();
			show_json(0, '您查找的剂型组不存在或已删除！');
		}
		if (json_decode($formtypes['formtypename'], true)) {
			$formtypes['formtypename'] = json_decode($formtypes['formtypename'], true);
			$formtypename = $formtypes['formtypename'];
		} else {
			$formtypes['formtypename'] = unserialize($formtypes['formtypename']);
			$formtypename = $formtypes['formtypename'];
		}

		if(empty($formtype)){
			$formtype = array();
		}
		$formtype_id = 0;
		$formtype_name = 0;
		foreach($formtypename as $value){ 
			if($value['id'] == $idToFind){
				$formtype_id = $value['id'];
				$formtype_name = $value['name'];
				break;
			}
		}
		show_json(1, array('cid' => $formtypes['id'],'id' => $formtype_id,'name' => $formtype_name));
	}

	public function style(){
		global $_W,$_GPC;
		$uniacid = intval($_W['uniacid']);
		$style = pdo_fetch('SELECT id,uniacid,style FROM ' . tablename('elapp_shop_goods_formtype_style') . ' WHERE uniacid=' . $uniacid);

		if ($_W['ispost']) {
			$data['style'] = intval($_GPC['style']);
			if (!empty($style)) {
				pdo_update('elapp_shop_goods_formtype_style', $data, array('uniacid' => $uniacid));
				plog('goods.formtypestyle.edit', '修改剂型组样式');
			}else {
				$data['uniacid'] = $uniacid;
				pdo_insert('elapp_shop_goods_formtype_style', $data);
				$id = pdo_insertid();
				plog('goods.formtypestyle.add', '添加剂型组样式');
			}
			show_json(1, array('url' => webUrl('goods/formtype/style')));
		}

		include $this->template('goods/formtype/style');
	}
}