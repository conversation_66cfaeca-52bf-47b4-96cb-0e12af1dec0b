<?php
namespace web\controller\goods\erpgoods;
use web\controller\WebPage;

class RenzhengController extends WebPage{

    function main($goodsfrom='sale') {

        global $_W, $_GPC;
		$supply_plugin = p('supply');
		if($supply_plugin){
			$ERPSQL = $supply_plugin->getOmsSql();
		}
		$erpGoodsDocTable = "V_GOODSDOC_B2C";//OMS 商品资料视图表
		$erpGoodsAttrTable = "V_GOODSATTR_B2C";//OMS 商品属性视图表
		$erpGoodsPgpriceTable = "V_PGPRICE_B2C";//OMS 商品包装关系管理视图表
        if(empty($_W['shopversion'])){
            $goodsfrom = strtolower(trim($_GPC['goodsfrom']));
            if(empty($goodsfrom)){
                header('location: ' . webUrl('goods/erpgoods/renzheng', array('goodsfrom'=>'main')));
            }
        }else{
            if(!empty($_GPC['goodsfrom'])){
                header('location: ' . webUrl('goods/erpgoods/renzheng'. $_GPC['goodsfrom']));
            }
        }

        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }

        $pindex = max(1, intval($_GPC['page']));
        $psize = 1000;
        $sqlcondition = $groupcondition = '';
		$condition = '';
		$params = array();
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);

            $sqlcondition = ' left join ' . tablename('elapp_shop_goods_option') . ' op on g.id = op.goodsid';
            if ($merch_plugin) {
                $sqlcondition .= " left join " . tablename('elapp_shop_merch_user') . " merch on merch.id = g.merchid and merch.uniacid=g.uniacid";
            }

            $groupcondition = ' group by g.`id`';

            $condition .= ' AND (O.`GOODSID` = :GOODSID or O.`GOODSNAME` LIKE :keyword or O.`keywords` LIKE :keyword or O.`OLDCODE` LIKE :keyword or O.`BARCODE` LIKE :keyword or O.`GOODSNAME` LIKE :keyword or O.`GOODSCODE` LIKE :keyword or O.`MANUFACTURER` LIKE :keyword';
            /* if ($merch_plugin) {
                $condition .= ' or merch.`merchname` LIKE :keyword';
            } */
            $condition .= ' )';

            $params[':keyword'] = '%' . $_GPC['keyword'] . '%';
            $params[':GOODSID'] = $_GPC['keyword'];

            $oraclesql = 'AND concat(O.GOODSNAME,O.GOODSID) LIKE ' .'%'. $_GPC['keyword'].'%';//('O.GOODSNAME' ||'O.GOODSID' ||...)
        }

        if (!empty($_GPC['orderby'])){
            if ($_GPC['orderby'] == 'price1'){
                $orderby .= " g.marketprice  DESC,";
            }elseif ($_GPC['orderby'] == 'total1'){
                $orderby .= " g.stock DESC,";
            }elseif ($_GPC['orderby'] == 'price2'){
                $orderby .= " g.marketprice  ASC,";
            }elseif($_GPC['orderby'] == 'total2'){
                $orderby .= " g.stock ASC,";
            }
        }
        empty($goodsfrom) && $_GPC['goodsfrom'] = $goodsfrom = 'sale';
        $_GPC['goodsfrom'] = $goodsfrom;


        /***
         * 多商户商品处理
         * 多商户过期后多商户商品不在主商城商品列表显示
         *
         */
        $sql = 'SELECT GOODSID from ' . $erpGoodsDocTable;
        $total_all = $ERPSQL->findAll($sql);
        $total = count($total_all);
        unset($total_all);
        if (!empty($total)) {
            $sql = 'SELECT * from (select ROWNUM AS R, O.ENTID,O.GOODSID,O.BRANDID,O.ISNEW,O.ISFREEZE,O.ISABANDON,O.BEACTIVE,O.BARCODE,O.GOODSCODE,O.OLDCODE,O.GOODSNAME,O.FOREIGNNAME,O.SHORTNAME,O.LOGOGRAM,O.GOODSSPEC,O.PLACE,O.MANUFACTURER,O.GOODSDESC,O.CREATETIME,O.DOCLEVID,O.CREATERID,O.CREATERINFO,O.LASTMODIFYTIME,O.DELUSERID,O.DELTIME,O.EXPAUTORG,O.RESAUTORG,O.BILLNO,O.ISPASS,O.FLAGID,O.ISCOMMPASS,O.SYNCID,O.BUSINESSID,O.CHECKCOST,O.MFCID,O.LASTSTFID,O.LASTREVTIME,O.COUNTRYID,O.ISIMPORTED,O.PARGOODSID,O.ISCOMBIN,O.VARIID,O.INDICATION,O.PKGAMOUNT,O.CORGOODSID,O.ELECGOODSCODE_CITY,O.OLDCODE2,A.ISMATERIEL,A.ISAUXIL,A.ISTOOLS,A.ISFREE,A.ISGIFT,A.ISSTOCKS,A.HESID,A.ACCTLEVEL,A.ANGLETYPE,A.SCHEMAID,A.YEARSNUM,A.SEASON,A.FABRIC,A.ACCES,A.EXITDATE,A.LIFETYPE,A.CONVRATIO,A.INTAXRATE,A.OUTTAXRATE,A.PURP,A.PURTAXP,A.SALEP,A.SALETAXP,A.RETAILP,A.KEMU,A.ISGOODS,A.ISALWUNDINPACK,A.GOODSGROUP,A.FIRSTSUPP,A.ISPUROK,A.ARCHNO,A.MAINLEVEL,A.SALETYPE,A.ISANGLE,A.APPEARDATE,A.GENERALNAME,A.CHEMNAME,A.COMMNAME,A.GCATEGORY,A.ISGSP,A.ISGMP,A.ISFIRST,A.ISHERBAL,A.ISTRAD,A.APPROVALNO,A.FORMULA,A.ISPSY,A.ISMEDICARE,A.ISREFRIG,A.BIDDPRICE,A.EXPRICE,A.QUALSTAND,A.ISSALE,A.ISPROCUR,A.ISCONTROL,A.ISMAIN,A.ISPROM,A.LIMITPURP,A.MAXSALEP,A.MINSALEP,A.MAXRETAIP,A.MINRETAIP,A.ADRETAILP,A.MEMPRICE,A.MANAGEPACK,A.ISCOSTACCT,A.INTERSCA,A.GENDER,A.PLANCATE,A.ISLIMIT,A.LIMITNUM,A.ISPRES,A.APPROVALDATE,A.APPROVALTO,A.ISDRUG,A.ISKEF,A.ISQA,A.INEFFECTDAY,A.DAYUNIT,A.DFTWHID,A.DFTLOCATID,A.CURCYCLE,A.ISTJ,A.TJ,A.STORAGETERM,A.ISELEC,A.ISDOUCHK,A.ISWITHPIC,A.REGMARK,A.DOWNPURP,A.LISTPRICE,A.QCATEGORY,A.INNERPRICE,A.REGMARKVALTO,A.ISBASEMED,A.SDATE,A.EDATE,A.WMSMEAS,A.LISTSALEP,A.ISWHOLLIMT,A.WHOLLIMTNUM,A.ISEPHE,A.ISCHKPACK,A.CURRENCYID,A.PAIJIA,A.ISPOINT,A.ISDEDRATE,A.ELECFLAG,A.DEDUCTTYPE,A.DEDUCTRATE,A.ISCHRONIC,A.ELECGOODSCODE,A.TREATCATE,A.GMPNO,A.GMPVALDATE,A.ISMEMDEDRATE,A.ISADULTTIN,A.K_EXP1,A.K_EXP2,A.K_EXP3,A.K_EXP4,A.K_EXP5,A.K_SEPORDFLAG,A.REMARK,A.ISRFQ,A.SAFEDAYS,A.ISDOPE,A.MEDCARETYPE,A.ISTWOVOTE,A.RECIPETYPE,A.INSTREGNO,A.INSTREGVALDATE,A.MFCREGNO,A.MFCREGVALDATE,A.REGNO,A.REGVALDATE,A.RPTYEAR,A.REGYEARDATE,A.STANDCODE,A.K_B2B,A.K_PACKAGE,A.K_ZHONGBZ,A.K_BUYERID,A.K_ASSISTANTID,A.K_HEDS,A.K_ELECGOODSCODE_CITY,A.K_ELECGOODSCODE_SW1,A.K_ELECGOODSCODE_SW2,A.SPXSJ1,A.SPXSJ2,A.SPXSJ3,A.SPXSJ4,A.SPXSJ5,A.SPXSJ6,A.SPXSJ7,A.SPXSJ8,A.SPXSJ9,A.VALDATE,A.K_TAXCODE,A.CATONE,A.CATTWO,A.CATTHREE,A.RTNFLAG,A.OPCAT,A.MARKETCAT,A.SOURCECAT,A.ISNECE,A.MULPOINT,A.ISBRAND,A.ISWEIGHT,A.K_IS_JIT,A.K_ISCCLQHL,A.OLDNAME,A.K_FLM,A.K_HWM,A.K_BJM,A.K_SPISFP,A.K_CATONE,A.K_CATTWO,A.K_CATTHREE,A.K_SHANGSXKCYR,A.K_GNFL,A.K_JBFL,A.PROCERTNO,A.PROCERTDATE,A.PROCERTTO,A.K_ENTRUSTDATE,A.K_PLANSALEP,A.K_RATE_TX,A.K_REMARKTO,A.K_IS_PDD,A.K_IS_NEW,A.K_CDSCBL,P.PACKID,P.UNIT,P.MEAS,P.ISBASE,P.ISPURPACK,P.ISRETPACK,P.LENGTH,P.WIDE,P.HIGH,P.BULKS,P.WEIGHT,P.ISSALEPACK,P.ISALWPUR,P.ISALWRET,P.ISINPACK,P.ISWMSPACK,P.ZYPACKID from V_GOODSDOC_B2C O inner join V_GOODSATTR_B2C A on A.GOODSID=O.GOODSID inner join V_PGPRICE_B2C P on P.GOODSID=A.GOODSID ) where R>'. ($pindex -1) * $psize.' AND R<=' . $pindex * $psize ;//四表联查分页 ok 2022/03/31 存在查询卡慢

            $list = $ERPSQL->findAll($sql);
            foreach($list as $key => &$value){
                $list[$key]['GOODSID'] = $value['GOODSID'];                
                $value['allcates'] = explode(",",$value['cates']);
                $value['allcates']=array_unique($value['allcates']);
                $url = mobileUrl('goods/detail', array('id' => $value['GOODSID']),true);
                $value['qrcode'] = m('qrcode')->createQrcode($url);
                $sale_cpcount=pdo_fetch("SELECT sum(og.total)  as sale_count   FROM ims_elapp_shop_order_goods  og LEFT JOIN ims_elapp_shop_order o on og.orderid=o.id  WHERE og.goodsid=:gsid and o.`status`>=:status and o.refundstate = 0 and og.uniacid=:uniacid",array(':gsid'=>$value['id'],':status'=>1,':uniacid'=>$_W['uniacid']));
                $value['sale_cpcount']=!empty($sale_cpcount['sale_count']) ? $sale_cpcount['sale_count'] : 0;
                //产品同步状态
                $goodsDoc = pdo_fetchall("SELECT erpGoodsID FROM " . tablename('elapp_shop_erp_goods_doc') . " WHERE erpGoodsID=:erpGoodsID and uniacid=:uniacid ", array('erpGoodsID' => $value['GOODSID'],'uniacid'=>$_W['uniacid']));
                if($goodsDoc){
                    $list[$key]['synstatus'] = 1;
                }else{
                    $list[$key]['synstatus'] = 0;
                }
            }
            if ($_GPC['export'] == 1) {
                foreach ($list as $k => $v) {
                    if ($v['isnew'] == 1) {
                        $list[$k]['shuxing'] = '新品 ';
                    }
                    if ($v['ishot'] == 1) {
                        $list[$k]['shuxing'] .= '热卖 ';
                    }
                    if ($v['isdiscount'] == 1) {
                        $list[$k]['shuxing'] .= '促销 ';
                    }
                    if ($v['isrecommand'] == 1) {
                        $list[$k]['shuxing'] .= '推荐 ';
                    }
                    if ($v['issendfree'] == 1) {
                        $list[$k]['shuxing'] .= '包邮 ';
                    }
                    if ($v['istime'] == 1) {
                        $list[$k]['shuxing'] .= '限时卖 ';
                    }
                    if ($v['isnodiscount'] == 1) {
                        $list[$k]['shuxing'] .= '不参与折扣 ';
                    }
                }
                m('excel')->export($list, array(
                    "title" => "商品列表明细-" . date('Y-m-d-H-i', time()),
                    "columns" => array(
                        array('title' => '商品名称', 'field' => 'title', 'width' => 24),
                        array('title' => '商品价格', 'field' => 'marketprice', 'width' => 12),
                        array('title' => '成本价格', 'field' => 'costprice', 'width' => 12),
                        array('title' => '商品库存', 'field' => 'stock', 'width' => 24),
                        array('title' => '销量', 'field' => 'salesreal', 'width' => 12),
                        array('title' => '实际销量', 'field' => 'sale_cpcount', 'width' => 12),
                        array('title' => '属性', 'field' => 'shuxing', 'width' => 64)
                    )
                ));

                plog('goods.list', '导出商品列表明细');
            }

            $pager = pagination2($total, $pindex, $psize);

            if ($merch_plugin) {
                $merch_user = $merch_plugin->getListUser($list,'merch_user');
                if (!empty($list) && !empty($merch_user)) {
                    foreach ($list as &$row) {
                        $row['merchname'] = $merch_user[$row['merchid']]['merchname'] ? $merch_user[$row['merchid']]['merchname'] : $_W['shopset']['shop']['name'];
                    }
                }
            }
        }

        $categorys = m('shop')->getFullCategory(true,true);
        $category = array();
        foreach($categorys as $cate){
            $category[$cate['id']] = $cate;
        }

        $goodstotal = intval($_W['shopset']['shop']['goodstotal']);
        $shopset = $_W['shopset']['shop'];

        include $this->template();
    }

    function sale() {
        $this->main('sale');
    }
    function out() {
        $this->main('out');
    }
    function stock() {
        $this->main('stock');
    }
    function cycle() {
        $this->main('cycle');
    }
    function verify() {
        $this->main('verify');
    }

    function create() {
        global $_W, $_GPC;
        $merchid = intval($_W['merchid']);
        $com_virtual = com('virtual');

        /*会员等级*/
        $levels = m('member')->getLevels();
        foreach($levels as &$l){
            $l['key'] ='level'.$l['id'];
        }
        unset($l);

        if ($_W['ispost']) {
            $data = array(
                'uniacid' => intval($_W['uniacid']),
                'title' => trim($_GPC['goodsname']),//商品名称
                'unit' => trim($_GPC['unit']),//商品单位
                'keywords' => trim($_GPC['keywords']),//关键字
                'type' => intval($_GPC['type']),//商品类型
                'thumb_first' => intval($_GPC['thumb_first']),//详情显示缩略图
                'isrecommand' => intval($_GPC['isrecommand']),//推荐
                'isnew' => intval($_GPC['isnew']),//新品
                'ishot' => intval($_GPC['ishot']),//热卖
                'issendfree' => intval($_GPC['issendfree']),//包邮
                'isnodiscount' => intval($_GPC['isnodiscount']),//不参与会员折扣
                'marketprice' => floatval($_GPC['marketprice']),//售价
                'minprice' => floatval($_GPC['marketprice']),//售价
                'maxprice' => floatval($_GPC['marketprice']),//售价
                'productprice' => trim($_GPC['productprice']),//原价
                'costprice' => $_GPC['costprice'],//成本
                'virtualsend' => intval($_GPC['virtualsend']),//虚拟物品自动发货
                'virtualsendcontent' => trim($_GPC['virtualsendcontent']),//自动发货内容
                'virtual'=>intval($_GPC['type'])==3?intval($_GPC['virtual']):0,//多规格虚拟物品
                'cash' => intval($_GPC['cash']),//是否支持货到付款
                'cashier' => intval($_GPC['cashier']),//是否支持收银台
                'invoice' => intval($_GPC['invoice']),//是否支持发票
                'dispatchtype' => intval($_GPC['dispatchtype']),//运费类型
                'dispatchprice' => trim($_GPC['dispatchprice']),//统一运费
                'dispatchid' => intval($_GPC['dispatchid']),//运费模板ID
                'status' => intval($_GPC['status']),//上架

                'goodssn' => trim($_GPC['goodssn']),//编码
                'productsn' => trim($_GPC['productsn']),//条码
                'weight' => $_GPC['weight'],//重量
                'stock' => intval($_GPC['stock']),//库存
                'showtotal' => intval($_GPC['showtotal']),//是否显示库存
                'totalcnf' => intval($_GPC['totalcnf']),//减库存类型
                'hasoption' => intval($_GPC['hasoption']),//是否有规格

                'subtitle' => trim($_GPC['subtitle']),//商品副标题
                'shorttitle' => trim($_GPC['shorttitle']),//商品短标题
                'content' => m('common')->html_images($_GPC['content']),//商品详情


                'createtime' => TIMESTAMP,//创建时间
                'video'=>trim($_GPC['video'])
            );

            $discounts = array('type'=>0, 'default'=>'', 'default_pay'=>'');
            if(!empty($levels)){
                foreach($levels as $level){
                    $discounts[$level['key']] = '';
                    $discounts[$level['key']. '_pay'] = '';
                }
                unset($level);
            }
            $data['discounts'] = json_encode($discounts);

            /*
             * 商品分类
             * */
            $cateset = m('common')->getSysset('shop');
            $pcates = array();
            $ccates = array();
            $tcates = array();
            $fcates = array();
            $cates = array();
            $pcateid=0;
            $ccateid = 0;
            $tcateid = 0;
            if (is_array($_GPC['cates'])) {

                $cates = $_GPC['cates'];

                foreach ($cates as $key=>$cid) {

                    $c = pdo_fetch('select level from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));

                    if($c['level']==1){ //一级
                        $pcates[] = $cid;
                    } else if($c['level']==2){  //二级
                        $ccates[] = $cid;
                    } else if($c['level']==3){  //三级
                        $tcates[] =$cid;
                    }

                    if($key==0){
                        //兼容 1.x
                        if($c['level']==1){ //一级
                            $pcateid = $cid;
                        }
                        else if($c['level']==2){
                            $crow = pdo_fetch('select parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                            $pcateid = $crow['parentid'];
                            $ccateid = $cid;

                        }
                        else if($c['level']==3){
                            $tcateid = $cid;
                            $tcate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                            $ccateid = $tcate['parentid'];
                            $ccate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $ccateid, ':uniacid' => $_W['uniacid']));
                            $pcateid = $ccate['parentid'];
                        }
                    }
                }
            }

            $data['pcate'] = $pcateid;
            $data['ccate'] = $ccateid;
            $data['tcate'] = $tcateid;
            $data['cates'] = implode(',', $cates);

            $data['pcates'] = implode(',', $pcates);
            $data['ccates'] = implode(',', $ccates);
            $data['tcates'] = implode(',', $tcates);
            /*保存图片*/
            if (is_array($_GPC['thumbs'])) {
                $thumbs = $_GPC['thumbs'];
                $thumb_url = array();
                foreach ($thumbs as $th) {
                    $thumb_url[] = trim($th);
                }
                //兼容1.x
                $data['thumb'] = save_media($thumb_url[0]);
                unset($thumb_url[0]);
                $data['thumb_url'] = serialize(m('common')->array_images($thumb_url));
            }
            if($data['type']==4){
                //批发商品 阶梯价格设置
                $intervalfloor = intval($_GPC['intervalfloor']);
                if($intervalfloor>3||$intervalfloor<1){
                    show_json(0,'请至少添加一个区间价格！');
                }
                $intervalprices = array();
                if($intervalfloor>0){
                    if(intval($_GPC['intervalnum1'])<=0){
                        show_json(0,'请设置起批发量！');
                    }
                    if(floatval($_GPC['intervalprice1'])<=0){
                        show_json(0,'批发价必须大于0！');
                    }
                    $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum1']),"intervalprice"=>floatval($_GPC['intervalprice1']));
                }
                if($intervalfloor>1){
                    if(intval($_GPC['intervalnum2'])<=0)
                    {
                        show_json(0,'请设置起批发量！');
                    }
                    if(intval($_GPC['intervalnum2'])<=intval($_GPC['intervalnum1']))
                    {
                        show_json(0,'批发量需大于上级批发量！');
                    }
                    if(floatval($_GPC['intervalprice2'])>=floatval($_GPC['intervalprice1']))
                    {
                        show_json(0,'批发价需小于上级批发价！');
                    }
                    $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum2']),"intervalprice"=>floatval($_GPC['intervalprice2']));
                }
                if($intervalfloor>2){
                    if(intval($_GPC['intervalnum3'])<=0) {
                        show_json(0,'请设置起批发量！');
                    }
                    if(intval($_GPC['intervalnum3'])<=intval($_GPC['intervalnum2'])) {
                        show_json(0,'批发量需大于上级批发量！');
                    }
                    if(floatval($_GPC['intervalprice3'])>=floatval($_GPC['intervalprice2'])) {
                        show_json(0,'批发价需小于上级批发价！');
                    }

                    $intervalprices[]=array("intervalnum"=>intval($_GPC['intervalnum3']),"intervalprice"=>floatval($_GPC['intervalprice3']));
                }
                //加密
                $intervalprice = iserializer($intervalprices);

                $data['intervalfloor']  = $intervalfloor;
                $data['intervalprice']  = $intervalprice;

                $data['minbuy']  = $_GPC['intervalnum1'];
                $data['marketprice']  = $_GPC['intervalprice1'];
                $data['productprice']  = 0;
                $data['costprice']  = 0;
            }
            /*
             * 商品自动上架
             * */
            $data['isstatustime'] = intval($_GPC['isstatustime']);
            $data['statustimestart'] = strtotime($_GPC['statustime']['start']);
            $data['statustimeend'] = strtotime($_GPC['statustime']['end']);
            if($data['status']==1 && $data['isstatustime'] > 0){
                if(!($data['statustimestart'] < time() && $data['statustimeend'] > time())){
                    show_json(0,'上架时间不符合要求！');
                }
            }

            //show_json(0,$data);
            pdo_insert('elapp_shop_goods', $data);
            $id = pdo_insertid();
            plog('goods.add', "添加商品 ID: {$id}<br>".(!empty($data['nocommission']) ? "是否参与分销 -- 否" : "是否参与分销 -- 是"));
            //处理商品规格
            $files = $_FILES;
            $spec_ids = $_POST['spec_id'];
            $spec_titles = $_POST['spec_title'];
            $specids = array();
            $len = count($spec_ids);
            $specids = array();
            $spec_items = array();
            for ($k = 0; $k < $len; $k++) {
                $spec_id = "";
                $get_spec_id = $spec_ids[$k];
                $a = array(
                    "uniacid" => $_W['uniacid'],
                    "goodsid" => $id,
                    "displayorder" => $k,
                    "title" => $spec_titles[$get_spec_id]
                );
                if (is_numeric($get_spec_id)) {
                    pdo_update("elapp_shop_goods_spec", $a, array("id" => $get_spec_id));
                    $spec_id = $get_spec_id;
                } else {
                    pdo_insert("elapp_shop_goods_spec", $a);
                    $spec_id = pdo_insertid();
                }
                //子项
                $spec_item_ids = $_POST["spec_item_id_" . $get_spec_id];
                $spec_item_titles = $_POST["spec_item_title_" . $get_spec_id];
                $spec_item_shows = $_POST["spec_item_show_" . $get_spec_id];
                $spec_item_thumbs = $_POST["spec_item_thumb_" . $get_spec_id];
                $spec_item_oldthumbs = $_POST["spec_item_oldthumb_" . $get_spec_id];
                $spec_item_virtuals = $_POST["spec_item_virtual_" . $get_spec_id];

                $itemlen = count($spec_item_ids);
                $itemids = array();
                for ($n = 0; $n < $itemlen; $n++) {
                    $item_id = "";
                    $get_item_id = $spec_item_ids[$n];
                    $d = array(
                        "uniacid" => $_W['uniacid'],
                        "specid" => $spec_id,
                        "displayorder" => $n,
                        "title" => $spec_item_titles[$n],
                        "show" => $spec_item_shows[$n],
                        "thumb" => save_media($spec_item_thumbs[$n]),
                        "virtual" => $data['type'] == 3 ? $spec_item_virtuals[$n] : 0
                    );
                    $f = "spec_item_thumb_" . $get_item_id;
                    if (is_numeric($get_item_id)) {
                        pdo_update("elapp_shop_goods_spec_item", $d, array("id" => $get_item_id));
                        $item_id = $get_item_id;
                    } else {
                        pdo_insert("elapp_shop_goods_spec_item", $d);
                        $item_id = pdo_insertid();
                    }
                    $itemids[] = $item_id;
                    //临时记录，用于保存规格项
                    $d['get_id'] = $get_item_id;
                    $d['id'] = $item_id;
                    $spec_items[] = $d;
                }
                //删除其他的
                if (count($itemids) > 0) {
                    pdo_query("delete from " . tablename('elapp_shop_goods_spec_item') . " where uniacid={$_W['uniacid']} and specid=$spec_id and id not in (" . implode(",", $itemids) . ")");
                } else {
                    pdo_query("delete from " . tablename('elapp_shop_goods_spec_item') . " where uniacid={$_W['uniacid']} and specid=$spec_id");
                }
                //更新规格项id
                pdo_update("elapp_shop_goods_spec", array("content" => serialize($itemids)), array("id" => $spec_id));
                $specids[] = $spec_id;
            }
            //删除其他的
            if (count($specids) > 0) {
                pdo_query("delete from " . tablename('elapp_shop_goods_spec') . " where uniacid={$_W['uniacid']} and goodsid=$id and id not in (" . implode(",", $specids) . ")");
            } else {
                pdo_query("delete from " . tablename('elapp_shop_goods_spec') . " where uniacid={$_W['uniacid']} and goodsid=$id");
            }
            //保存规格
            $totalstocks = 0;
            $optionArray = json_decode($_POST['optionArray'],true);
            $option_idss = $optionArray['option_ids'];
            $len = count($option_idss);
            $optionids = array();
            for ($k = 0; $k < $len; $k++) {
                $option_id = "";
                $ids = $option_idss[$k];
                $get_option_id = $optionArray['option_id'][$k];

                $idsarr = explode("_", $ids);
                $newids = array();
                foreach ($idsarr as $key => $ida) {
                    foreach ($spec_items as $it) {
                        if ($it['get_id'] == $ida) {
                            $newids[] = $it['id'];
                            break;
                        }
                    }
                }

                $newids = implode("_", $newids);
                $a = array(
                    "uniacid" => $_W['uniacid'],
                    "title" => $optionArray['option_title'][$k],
                    "productprice" => $optionArray['option_productprice'][$k],
                    "costprice" => $optionArray['option_costprice'][$k],
                    "marketprice" => $optionArray['option_marketprice'][$k],
                    "stock" => $optionArray['option_stock'][$k],
                    "weight" => $optionArray['option_weight'][$k],
                    "goodssn" => $optionArray['option_goodssn'][$k],
                    "productsn" => $optionArray['option_productsn'][$k],
                    "goodsid" => $id,
                    "specs" => $newids,
                    'virtual' => $data['type'] == 3 ? $optionArray['option_virtual'][$k] : 0,
                );

                if($data['type']==4){
                    $a['presellprice']=0;
                    $a['productprice']=0;
                    $a['costprice'] = 0;
                    $a['marketprice']=intval($_GPC['intervalprice1']);
                }

                $totalstocks+=$a['stock'];
                pdo_insert("elapp_shop_goods_option", $a);
                $option_id = pdo_insertid();

                $optionids[] = $option_id;
                if (count($optionids) > 0 && $data['hasoption'] !== 0) {
                    pdo_query("delete from " . tablename('elapp_shop_goods_option') . " where goodsid=$id and id not in ( " . implode(',', $optionids) . ")");

                    //更新最低价和最高价
                    $sql = "update ".tablename('elapp_shop_goods')." g set
                    g.minprice = (select min(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id),
                    g.maxprice = (select max(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id)
                    where g.id = $id and g.hasoption=1";

                    pdo_query($sql);
                } else {
                    pdo_query("delete from " . tablename('elapp_shop_goods_option') . " where goodsid=$id");
                    $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = $id and hasoption=0;";
                    pdo_query($sql);
                }
            }
            //如果是有促销,那么更新最大最小价格
            $sqlgoods = "SELECT id,title,thumb,marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,stock,description,merchsale FROM " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1";
            $goodsinfo = pdo_fetch($sqlgoods,array(':id'=>$id,':uniacid'=>$_W['uniacid']));
            $goodsinfo = m('goods')->getOneMinPrice($goodsinfo);

            pdo_update('elapp_shop_goods',array('minprice'=>$goodsinfo['minprice'],'maxprice'=>$goodsinfo['maxprice']),array('id'=>$id,'uniacid'=>$_W['uniacid']));
            //总库存
            if ($data['type'] == 3 && $com_virtual) {
                $com_virtual->updateGoodsStock($id);
            } else {
                if ($data['hasoption'] !== 0 && ($data['totalcnf'] != 2) && empty($data['unite_total'])) {
                    pdo_update("elapp_shop_goods", array("stock" => $totalstocks), array("id" => $id));
                }
            }
            show_json(1,array('url'=>webUrl('goods/edit', array('id' => $id))));
        }
        $statustimestart = time();
        $statustimeend = strtotime('+1 month');
        $category = m('shop')->getFullCategory(true,true);
        $com_virtual = com('virtual');

        //查询快递模板
        $dispatch_data = pdo_fetchall('select * from ' . tablename('elapp_shop_dispatch') . ' where uniacid=:uniacid and merchid=:merchid and enabled=1 order by displayorder desc', array(':uniacid' => $_W['uniacid'], ':merchid' =>$merchid));
        $levels =array_merge(array(
            array(
                'id'=>0,
                'key'=>'default',
                'levelname'=>empty($_W['shopset']['shop']['levelname'])?'默认会员':$_W['shopset']['shop']['levelname']
            )
        ),$levels);

        if ($com_virtual) {
            $virtual_types = pdo_fetchall("select * from " . tablename('elapp_shop_virtual_type') . " where uniacid=:uniacid and merchid=:merchid and recycled = 0 order by id asc", array(":uniacid" => $_W['uniacid'], ':merchid' =>0));
        }

        include $this->template('goods/create');
    }

    function add() {
        $this->post();
    }
    function edit() {
        $this->post();
    }

    protected function post() {
        require dirname(__FILE__)."/post.php";
    }

    function delete() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        $items = pdo_fetchall("SELECT id,title FROM " . tablename('elapp_shop_goods') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);
        foreach ($items as $item) {
            pdo_update('elapp_shop_goods', array('deleted' => 1), array('id' => $item['id']));
            plog('goods.delete', "删除商品 ID: {$item['id']} 商品名称: {$item['title']} ");
        }
        show_json(1, array('url' => referer()));
    }

    function status() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        //'新'字角标去除
        pdo_query("update ".tablename('elapp_shop_goods')." set newgoods = 0 where id in ( {$id} ) AND uniacid=" . $_W['uniacid']);

        $items = pdo_fetchall("SELECT id,title,status,isstatustime,statustimestart,statustimeend FROM " . tablename('elapp_shop_goods') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);

        foreach ($items as $item) {
            if($item['isstatustime']>0){
                if(intval($_GPC['status']) > 0 && $item['statustimestart'] < time() && $item['statustimeend'] > time()){

                }else{
                    show_json(0,"商品 [{$item['title']}] 上架时间不符合要求！");
                }
            }
            $goodsstatus = $_GPC['status'] == 1 ? '上架' : '下架';
            pdo_update('elapp_shop_goods', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
            plog('goods.edit', "修改商品状态<br/>ID: {$item['id']}<br/>商品名称: {$item['title']}<br/>状态: " .$goodsstatus);
        }
        show_json(1, array('url' => referer()));
    }

    function checked() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        $items = pdo_fetchall("SELECT id,title FROM " . tablename('elapp_shop_goods') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);

        foreach ($items as $item) {
            pdo_update('elapp_shop_goods', array('checked' => intval($_GPC['checked'])), array('id' => $item['id']));
            plog('goods.edit', "修改商品状态<br/>ID: {$item['id']}<br/>商品名称: {$item['title']}<br/>状态: " . $_GPC['checked'] == 0 ? '审核通过' : '审核中');
        }

        show_json(1, array('url' => referer()));
    }

    function delete1() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        $items = pdo_fetchall("SELECT id,title FROM " . tablename('elapp_shop_goods') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);

        foreach ($items as $item) {
            pdo_delete('elapp_shop_goods', array('id' => $item['id']));
            plog('goods.edit', "从回收站彻底删除商品<br/>ID: {$item['id']}<br/>商品名称: {$item['title']}");
        }
        show_json(1, array('url' => referer()));
    }

    function restore() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0;
        }
        $items = pdo_fetchall("SELECT id,title FROM " . tablename('elapp_shop_goods') . " WHERE id in( $id ) AND uniacid=" . $_W['uniacid']);

        foreach ($items as $item) {
            pdo_update('elapp_shop_goods', array('deleted' => 0), array('id' => $item['id']));
            plog('goods.edit', "从回收站恢复商品<br/>ID: {$item['id']}<br/>商品名称: {$item['title']}");
        }
        show_json(1, array('url' => referer()));
    }

    function property() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $type = $_GPC['type'];
        $data = intval($_GPC['data']);
        if (in_array($type, array('new', 'hot', 'recommand', 'discount', 'time', 'sendfree', 'nodiscount'))) {

            pdo_update("elapp_shop_goods", array("is" . $type => $data), array("id" => $id, "uniacid" => $_W['uniacid']));
            if ($type == 'new') {
                $typestr = "新品";
            } else if ($type == 'hot') {
                $typestr = "热卖";
            } else if ($type == 'recommand') {
                $typestr = "推荐";
            } else if ($type == 'discount') {
                $typestr = "促销";
            } else if ($type == 'time') {
                $typestr = "限时卖";
            } else if ($type == 'sendfree') {
                $typestr = "包邮";
            } else if ($type == 'nodiscount') {
                $typestr = "不参与折扣状态";
            }
            plog('goods.edit', "修改商品{$typestr}状态   ID: {$id}");
        }
        if (in_array($type, array('status'))) {
            pdo_update("elapp_shop_goods", array($type => $data), array("id" => $id, "uniacid" => $_W['uniacid']));
            plog('goods.edit', "修改商品上下架状态   ID: {$id}");
        }
        if (in_array($type, array('type'))) {
            pdo_update("elapp_shop_goods", array($type => $data), array("id" => $id, "uniacid" => $_W['uniacid']));
            plog('goods.edit', "修改商品类型   ID: {$id}");
        }
        show_json(1);
    }

    function change() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if (empty($id)) {
            show_json(0, array('message' => '参数错误'));
        }else{
            pdo_update('elapp_shop_goods',array('newgoods'=>0),array('id'=>$id));
        }
        $type = trim($_GPC['type']);
        $value = trim($_GPC['value']);
        if (!in_array($type, array('title', 'marketprice', 'stock', 'goodssn', 'productsn', 'displayorder', 'dowpayment'))) {
            show_json(0, array('message' => '参数错误'));
        }
        $goods = pdo_fetch('select id,hasoption,marketprice,dowpayment,`type`,isdiscount,isdiscount_time from ' . tablename('elapp_shop_goods') . ' where id=:id and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $id));
        if (empty($goods)) {
            show_json(0, array('message' => '参数错误'));
        }

        if($type=='dowpayment') {
            if($goods['marketprice']<$value){
                show_json(0, array('message' => '定金不能大于总价'));
            }
        }elseif ($type=='marketprice'){
            if($goods['dowpayment']>$value){
                show_json(0, array('message' => '总价不能小于定金'));
            }
            plog('goods.price.edit', "更改商品 ID: {$id}<br>价格为".$value);
        }

        if($type=='stock' && $goods['type'] == 3){
            show_json(0, array('message' => '虚拟卡密产品不可直接修改库存'));
        }
        $result = pdo_update('elapp_shop_goods', array($type => $value), array('id' => $id));
        if($type=='stock'&& $result){
            plog('goods.list', "编辑商品 ID: {$id}<br>库存量为".$value);
        }
        /*if ($goods['hasoption'] == 0 && $type != 'displayorder' && ($goods['isdiscount'] ==0 || $goods['isdiscount_time']<time()) && $type != 'stock' ) {
            $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = {$goods['id']} and hasoption=0;";
            pdo_query($sql);
        }*/
        if($goods['hasoption'] == 0 && !in_array($type,array('displayorder','stock'))){
            if($goods['isdiscount'] ==0 || $goods['isdiscount_time']<time()){
                $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = {$goods['id']} and hasoption=0;";
                pdo_query($sql);
            }else{
                //如果是有促销,那么更新最大最小价格
                $sqlgoods = "SELECT id,title,thumb,marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sales,stock,description,merchsale FROM " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1";
                $goodsinfo = pdo_fetch($sqlgoods,array(':id'=>$id,':uniacid'=>$_W['uniacid']));
                $goodsinfo = m('goods')->getOneMinPrice($goodsinfo);
                pdo_update('elapp_shop_goods',array('minprice'=>$goodsinfo['minprice'],'maxprice'=>$goodsinfo['maxprice']),array('id'=>$id,'uniacid'=>$_W['uniacid']));
            }
        }
        show_json(1);
    }

    function tpl() {
        global $_GPC, $_W;
        $tpl = trim($_GPC['tpl']);
        if ($tpl == 'option') {

            $tag = random(32);
            include $this->template('goods/tpl/option');
        } else if ($tpl == 'spec') {

            $spec = array("id" => random(32), "title" => $_GPC['title']);
            include $this->template('goods/tpl/spec');
        } else if ($tpl == 'specitem') {

            $spec = array("id" => $_GPC['specid']);
            $specitem = array("id" => random(32), "title" => $_GPC['title'], "show" => 1);
            include $this->template('goods/tpl/spec_item');
        } else if ($tpl == 'param') {

            $tag = random(32);
            include $this->template('goods/tpl/param');
        }
    }

    function query(){
        global $_W, $_GPC;
        $kwd = trim($_GPC['keyword']);
        $type = intval($_GPC['type']);

        $live = intval($_GPC['live']);

        $params = array();
        $params[':uniacid'] = $_W['uniacid'];
        $condition=" and status=1 and deleted=0 and uniacid=:uniacid";
        if (!empty($kwd)) {
            $condition.=" AND (`title` LIKE :keywords OR `keywords` LIKE :keywords)";
            $params[':keywords'] = "%{$kwd}%";
        }
        if (empty($type)) {
            $condition.=" AND `type` != 10 ";
        }else{
            $condition.=" AND `type` = :type ";
            $params[':type'] = $type;
        }

        $ds = pdo_fetchall('SELECT id,title,thumb,marketprice,productprice,share_title,share_icon,description,minprice,costprice,stock,sales,islive,liveprice FROM ' . tablename('elapp_shop_goods') . " WHERE 1 {$condition} order by createtime desc", $params);
        foreach($ds as &$value){
            $value['share_title'] = htmlspecialchars_decode($value['share_title']);
            unset($value);
        }
        $ds = set_medias($ds, array('thumb','share_icon'));
        if($_GPC['suggest']){
            die(json_encode(array('value'=>$ds)));
        }
        include $this->template();

    }

    function goodsprice(){
        global $_W;
        $sql = "update ".tablename('elapp_shop_goods')." g set g.minprice = (select min(marketprice) from ".tablename('elapp_shop_goods_option')." where g.id = goodsid),g.maxprice = (select max(marketprice) from ".tablename('elapp_shop_goods_option')." where g.id = goodsid) where g.hasoption=1 and g.uniacid=".$_W['uniacid']."; update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where hasoption=0 and uniacid=".$_W['uniacid'].";";
        pdo_run($sql);
        show_json(1);
    }

    // 批量分类模版显示
    function batchcates(){
        $categorys = m('shop')->getFullCategory(true);
        $category = array();
        foreach($categorys as $cate){
            $category[$cate['id']] = $cate;
        }
        include $this->template();
    }

    //批量修改分类
    function ajax_batchcates(){
        global $_W,$_GPC;

        //是否覆盖分类
        $iscover=$_GPC['iscover'];
        $goodsids=$_GPC['goodsids'];
        $cates=$_GPC['cates'];
        $data=array();

        //处理分类
        $reust_cates=$this->reust_cates($cates);

        foreach ($goodsids as $goodsid){
            //覆盖原有分类
            if(!empty($iscover)){
                $data=$reust_cates;
                $data['cates'] = implode(',', $data['cates']);
                $data['pcates'] = implode(',', $data['pcates']);
                $data['ccates'] = implode(',', $data['ccates']);
                $data['tcates'] = implode(',', $data['tcates']);

                pdo_update('elapp_shop_goods', $data, array('id' => $goodsid));
            }else{
                //不覆盖原有分类
                $goods = pdo_fetch('select pcate,ccate,tcate,cates,pcates,ccates,tcates  from ' . tablename('elapp_shop_goods') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $goodsid, ':uniacid' => $_W['uniacid']));
                if (!empty($goods['cates'])){
                    $goods_cates=explode(',',$goods['cates']);
                    if(!empty($reust_cates['cates'])){
                        //合并去重复后将数组转换成字符串
                        $data['cates'] =implode(',', array_unique(array_merge($goods_cates,$reust_cates['cates']),SORT_NUMERIC ));
                    }
                }
                if (!empty($goods['pcates'])){
                    $goods_pcates=explode(',',$goods['pcates']);
                    if(!empty($reust_cates['pcates'])){
                        //合并去重复后将数组转换成字符串
                        $data['pcates'] =implode(',', array_unique(array_merge($goods_pcates,$reust_cates['pcates']),SORT_NUMERIC ));
                    }
                }
                if (!empty($goods['ccates'])){
                    $goods_ccates=explode(',',$goods['ccates']);
                    if(!empty($reust_cates['ccates'])){
                        //合并去重复后将数组转换成字符串
                        $data['ccates'] =implode(',', array_unique(array_merge($goods_ccates,$reust_cates['ccates']),SORT_NUMERIC ));
                    }
                }
                if (!empty($goods['tcates'])){
                    $goods_tcates=explode(',',$goods['tcates']);
                    if(!empty($reust_cates['tcates'])){
                        //合并去重复后将数组转换成字符串
                        $data['tcates'] =implode(',', array_unique(array_merge($goods_tcates,$reust_cates['tcates']),SORT_NUMERIC ));
                    }
                }

                if(!empty($reust_cates['pcate'])){
                    $data['pcate'] = $reust_cates['pcate'] ;
                }

                if(!empty($reust_cates['ccate'])){
                    $data['ccate'] = $reust_cates['ccate'];
                }

                if(!empty( $reust_cates['tcate'])){
                    $data['tcate'] = $reust_cates['tcate'];
                }

                pdo_update('elapp_shop_goods', $data, array('id' => $goodsid));
            }
        }
        show_json(1);
    }


    //返回处理过的分类信息--批量分类调用
    function  reust_cates($param_cates){
        global $_W;
        $pcates = array();
        $ccates = array();
        $tcates = array();
        $cates = array();
        $pcateid=0;
        $ccateid = 0;
        $tcateid = 0;
        if (is_array($param_cates)) {
            foreach ($param_cates as $key=>$cid) {
                $c = pdo_fetch('select level from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                if($c['level']==1){ //一级
                    $pcates[] = $cid;
                } else if($c['level']==2){  //二级
                    $ccates[] = $cid;
                } else if($c['level']==3){  //三级
                    $tcates[] =$cid;
                }
                if($key==0){
                    //兼容 1.x
                    if($c['level']==1){ //一级
                        $pcateid = $cid;
                    }
                    else if($c['level']==2){
                        $crow = pdo_fetch('select parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                        $pcateid = $crow['parentid'];
                        $ccateid = $cid;
                    }
                    else if($c['level']==3){
                        $tcateid = $cid;
                        $tcate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $cid, ':uniacid' => $_W['uniacid']));
                        $ccateid = $tcate['parentid'];
                        $ccate = pdo_fetch('select id,parentid from ' . tablename('elapp_shop_category') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $ccateid, ':uniacid' => $_W['uniacid']));
                        $pcateid = $ccate['parentid'];
                    }
                }
            }
        }
        $data['pcate'] = $pcateid;
        $data['ccate'] = $ccateid;
        $data['tcate'] = $tcateid;
        $data['cates'] = $param_cates;
        $data['pcates'] =$pcates;
        $data['ccates'] = $ccates;
        $data['tcates'] = $tcates;
        return $data;
    }
    /**
     * 同步商品资料到本地ERP
     * <AUTHOR> 2022/04/02 完成 后期整理各表重复字段
     **/
    function synGoodsDoc() {
        global $_W, $_GPC;
        $supply_plugin = p('supply');
		if($supply_plugin){
			$ERPSQL = $supply_plugin->getOmsSql();
		}
        $id = $_GPC['id'];
        
        if (empty($id)) {
            $id = is_array($_GPC['ids']) ? "'".implode("','", $_GPC['ids'])."'" : 0;//分割后字符串加单引号 'aa','bb' ，SQL查询标准 OK
        }
        //$items = $ERPSQL->findAll("SELECT * FROM V_GOODSDOC_B2C WHERE GOODSID in($id)");//单表查询ok
        //$items = $ERPSQL->findAll("SELECT O.ENTID,O.GOODSID,O.BRANDID,O.ISNEW,O.ISFREEZE,O.ISABANDON,O.BEACTIVE,O.BARCODE,O.GOODSCODE,O.OLDCODE,O.GOODSNAME,O.FOREIGNNAME,O.SHORTNAME,O.LOGOGRAM,O.GOODSSPEC,O.PLACE,O.MANUFACTURER,O.GOODSDESC,O.CREATETIME,O.DOCLEVID,O.CREATERID,O.CREATERINFO,O.LASTMODIFYTIME,O.DELUSERID,O.DELTIME,O.EXPAUTORG,O.RESAUTORG,O.BILLNO,O.ISPASS,O.FLAGID,O.ISCOMMPASS,O.SYNCID,O.BUSINESSID,O.CHECKCOST,O.MFCID,O.LASTSTFID,O.LASTREVTIME,O.COUNTRYID,O.ISIMPORTED,O.PARGOODSID,O.ISCOMBIN,O.VARIID,O.INDICATION,O.PKGAMOUNT,O.CORGOODSID,O.ELECGOODSCODE_CITY,O.OLDCODE2,A.ISMATERIEL,A.ISAUXIL,A.ISTOOLS,A.ISFREE,A.ISGIFT,A.ISSTOCKS,A.HESID,A.ACCTLEVEL,A.ANGLETYPE,A.SCHEMAID,A.YEARSNUM,A.SEASON,A.FABRIC,A.ACCES,A.EXITDATE,A.LIFETYPE,A.CONVRATIO,A.INTAXRATE,A.OUTTAXRATE,A.PURP,A.PURTAXP,A.SALEP,A.SALETAXP,A.RETAILP,A.KEMU,A.ISGOODS,A.ISALWUNDINPACK,A.GOODSGROUP,A.FIRSTSUPP,A.ISPUROK,A.ARCHNO,A.MAINLEVEL,A.SALETYPE,A.ISANGLE,A.APPEARDATE,A.GENERALNAME,A.CHEMNAME,A.COMMNAME,A.GCATEGORY,A.ISGSP,A.ISGMP,A.ISFIRST,A.ISHERBAL,A.ISTRAD,A.APPROVALNO,A.FORMULA,A.ISPSY,A.ISMEDICARE,A.ISREFRIG,A.BIDDPRICE,A.EXPRICE,A.QUALSTAND,A.ISSALE,A.ISPROCUR,A.ISCONTROL,A.ISMAIN,A.ISPROM,A.LIMITPURP,A.MAXSALEP,A.MINSALEP,A.MAXRETAIP,A.MINRETAIP,A.ADRETAILP,A.MEMPRICE,A.MANAGEPACK,A.ISCOSTACCT,A.INTERSCA,A.GENDER,A.PLANCATE,A.ISLIMIT,A.LIMITNUM,A.ISPRES,A.APPROVALDATE,A.APPROVALTO,A.ISDRUG,A.ISKEF,A.ISQA,A.INEFFECTDAY,A.DAYUNIT,A.DFTWHID,A.DFTLOCATID,A.CURCYCLE,A.ISTJ,A.TJ,A.STORAGETERM,A.ISELEC,A.ISDOUCHK,A.ISWITHPIC,A.REGMARK,A.DOWNPURP,A.LISTPRICE,A.QCATEGORY,A.INNERPRICE,A.REGMARKVALTO,A.ISBASEMED,A.SDATE,A.EDATE,A.WMSMEAS,A.LISTSALEP,A.ISWHOLLIMT,A.WHOLLIMTNUM,A.ISEPHE,A.ISCHKPACK,A.CURRENCYID,A.PAIJIA,A.ISPOINT,A.ISDEDRATE,A.ELECFLAG,A.DEDUCTTYPE,A.DEDUCTRATE,A.ISCHRONIC,A.ELECGOODSCODE,A.TREATCATE,A.GMPNO,A.GMPVALDATE,A.ISMEMDEDRATE,A.ISADULTTIN,A.K_EXP1,A.K_EXP2,A.K_EXP3,A.K_EXP4,A.K_EXP5,A.K_SEPORDFLAG,A.REMARK,A.ISRFQ,A.SAFEDAYS,A.ISDOPE,A.MEDCARETYPE,A.ISTWOVOTE,A.RECIPETYPE,A.INSTREGNO,A.INSTREGVALDATE,A.MFCREGNO,A.MFCREGVALDATE,A.REGNO,A.REGVALDATE,A.RPTYEAR,A.REGYEARDATE,A.STANDCODE,A.K_B2B,A.K_PACKAGE,A.K_ZHONGBZ,A.K_BUYERID,A.K_ASSISTANTID,A.K_HEDS,A.K_ELECGOODSCODE_CITY,A.K_ELECGOODSCODE_SW1,A.K_ELECGOODSCODE_SW2,A.SPXSJ1,A.SPXSJ2,A.SPXSJ3,A.SPXSJ4,A.SPXSJ5,A.SPXSJ6,A.SPXSJ7,A.SPXSJ8,A.SPXSJ9,A.VALDATE,A.K_TAXCODE,A.CATONE,A.CATTWO,A.CATTHREE,A.RTNFLAG,A.OPCAT,A.MARKETCAT,A.SOURCECAT,A.ISNECE,A.MULPOINT,A.ISBRAND,A.ISWEIGHT,A.K_IS_JIT,A.K_ISCCLQHL,A.OLDNAME,A.K_FLM,A.K_HWM,A.K_BJM,A.K_SPISFP,A.K_CATONE,A.K_CATTWO,A.K_CATTHREE,A.K_SHANGSXKCYR,A.K_GNFL,A.K_JBFL,A.PROCERTNO,A.PROCERTDATE,A.PROCERTTO,A.K_ENTRUSTDATE,A.K_PLANSALEP,A.K_RATE_TX,A.K_REMARKTO,A.K_IS_PDD,A.K_IS_NEW,A.K_CDSCBL,P.PACKID,P.UNIT,P.MEAS,P.ISBASE,P.ISPURPACK,P.ISRETPACK,P.LENGTH,P.WIDE,P.HIGH,P.BULKS,P.WEIGHT,P.ISSALEPACK,P.ISALWPUR,P.ISALWRET,P.ISINPACK,P.ISWMSPACK,P.ZYPACKID,B.BESALENUM FROM V_GOODSDOC_B2C O inner join V_GOODSATTR_B2C A on A.GOODSID=O.GOODSID inner join V_PGPRICE_B2C P on P.GOODSID=A.GOODSID inner join V_DS_BESALENUM_B2C B on B.GOODSID=P.GOODSID WHERE O.GOODSID in($id)");//联表查询OK Hlei 2022/04/02
        $items = $ERPSQL->findAll("SELECT O.ENTID,O.GOODSID,O.BRANDID,O.ISNEW,O.ISFREEZE,O.ISABANDON,O.BEACTIVE,O.BARCODE,O.GOODSCODE,O.OLDCODE,O.GOODSNAME,O.FOREIGNNAME,O.SHORTNAME,O.LOGOGRAM,O.GOODSSPEC,O.PLACE,O.MANUFACTURER,O.GOODSDESC,O.CREATETIME,O.DOCLEVID,O.CREATERID,O.CREATERINFO,O.LASTMODIFYTIME,O.DELUSERID,O.DELTIME,O.EXPAUTORG,O.RESAUTORG,O.BILLNO,O.ISPASS,O.FLAGID,O.ISCOMMPASS,O.SYNCID,O.BUSINESSID,O.CHECKCOST,O.MFCID,O.LASTSTFID,O.LASTREVTIME,O.COUNTRYID,O.ISIMPORTED,O.PARGOODSID,O.ISCOMBIN,O.VARIID,O.INDICATION,O.PKGAMOUNT,O.CORGOODSID,O.ELECGOODSCODE_CITY,O.OLDCODE2,A.ISMATERIEL,A.ISAUXIL,A.ISTOOLS,A.ISFREE,A.ISGIFT,A.ISSTOCKS,A.HESID,A.ACCTLEVEL,A.ANGLETYPE,A.SCHEMAID,A.YEARSNUM,A.SEASON,A.FABRIC,A.ACCES,A.EXITDATE,A.LIFETYPE,A.CONVRATIO,A.INTAXRATE,A.OUTTAXRATE,A.PURP,A.PURTAXP,A.SALEP,A.SALETAXP,A.RETAILP,A.KEMU,A.ISGOODS,A.ISALWUNDINPACK,A.GOODSGROUP,A.FIRSTSUPP,A.ISPUROK,A.ARCHNO,A.MAINLEVEL,A.SALETYPE,A.ISANGLE,A.APPEARDATE,A.GENERALNAME,A.CHEMNAME,A.COMMNAME,A.GCATEGORY,A.ISGSP,A.ISGMP,A.ISFIRST,A.ISHERBAL,A.ISTRAD,A.APPROVALNO,A.FORMULA,A.ISPSY,A.ISMEDICARE,A.ISREFRIG,A.BIDDPRICE,A.EXPRICE,A.QUALSTAND,A.ISSALE,A.ISPROCUR,A.ISCONTROL,A.ISMAIN,A.ISPROM,A.LIMITPURP,A.MAXSALEP,A.MINSALEP,A.MAXRETAIP,A.MINRETAIP,A.ADRETAILP,A.MEMPRICE,A.MANAGEPACK,A.ISCOSTACCT,A.INTERSCA,A.GENDER,A.PLANCATE,A.ISLIMIT,A.LIMITNUM,A.ISPRES,A.APPROVALDATE,A.APPROVALTO,A.ISDRUG,A.ISKEF,A.ISQA,A.INEFFECTDAY,A.DAYUNIT,A.DFTWHID,A.DFTLOCATID,A.CURCYCLE,A.ISTJ,A.TJ,A.STORAGETERM,A.ISELEC,A.ISDOUCHK,A.ISWITHPIC,A.REGMARK,A.DOWNPURP,A.LISTPRICE,A.QCATEGORY,A.INNERPRICE,A.REGMARKVALTO,A.ISBASEMED,A.SDATE,A.EDATE,A.WMSMEAS,A.LISTSALEP,A.ISWHOLLIMT,A.WHOLLIMTNUM,A.ISEPHE,A.ISCHKPACK,A.CURRENCYID,A.PAIJIA,A.ISPOINT,A.ISDEDRATE,A.ELECFLAG,A.DEDUCTTYPE,A.DEDUCTRATE,A.ISCHRONIC,A.ELECGOODSCODE,A.TREATCATE,A.GMPNO,A.GMPVALDATE,A.ISMEMDEDRATE,A.ISADULTTIN,A.K_EXP1,A.K_EXP2,A.K_EXP3,A.K_EXP4,A.K_EXP5,A.K_SEPORDFLAG,A.REMARK,A.ISRFQ,A.SAFEDAYS,A.ISDOPE,A.MEDCARETYPE,A.ISTWOVOTE,A.RECIPETYPE,A.INSTREGNO,A.INSTREGVALDATE,A.MFCREGNO,A.MFCREGVALDATE,A.REGNO,A.REGVALDATE,A.RPTYEAR,A.REGYEARDATE,A.STANDCODE,A.K_B2B,A.K_PACKAGE,A.K_ZHONGBZ,A.K_BUYERID,A.K_ASSISTANTID,A.K_HEDS,A.K_ELECGOODSCODE_CITY,A.K_ELECGOODSCODE_SW1,A.K_ELECGOODSCODE_SW2,A.SPXSJ1,A.SPXSJ2,A.SPXSJ3,A.SPXSJ4,A.SPXSJ5,A.SPXSJ6,A.SPXSJ7,A.SPXSJ8,A.SPXSJ9,A.VALDATE,A.K_TAXCODE,A.CATONE,A.CATTWO,A.CATTHREE,A.RTNFLAG,A.OPCAT,A.MARKETCAT,A.SOURCECAT,A.ISNECE,A.MULPOINT,A.ISBRAND,A.ISWEIGHT,A.K_IS_JIT,A.K_ISCCLQHL,A.OLDNAME,A.K_FLM,A.K_HWM,A.K_BJM,A.K_SPISFP,A.K_CATONE,A.K_CATTWO,A.K_CATTHREE,A.K_SHANGSXKCYR,A.K_GNFL,A.K_JBFL,A.PROCERTNO,A.PROCERTDATE,A.PROCERTTO,A.K_ENTRUSTDATE,A.K_PLANSALEP,A.K_RATE_TX,A.K_REMARKTO,A.K_IS_PDD,A.K_IS_NEW,A.K_CDSCBL,P.PACKID,P.UNIT,P.MEAS,P.ISBASE,P.ISPURPACK,P.ISRETPACK,P.LENGTH,P.WIDE,P.HIGH,P.BULKS,P.WEIGHT,P.ISSALEPACK,P.ISALWPUR,P.ISALWRET,P.ISINPACK,P.ISWMSPACK,P.ZYPACKID FROM V_GOODSDOC_B2C O inner join V_GOODSATTR_B2C A on A.GOODSID=O.GOODSID inner join V_PGPRICE_B2C P on P.GOODSID=A.GOODSID WHERE O.GOODSID in($id)");//联表查询OK Hlei 2022/04/02
        
        //return var_dump($items);
        foreach ($items as $item) {
            //商品资料 ok
            $docData =array(
                'erpID' => 1,
                'entID' => $item['ENTID'],//企业ID
                'brandID' => $item['BRANDID'],//品牌ID
                'isNew' => $item['ISNEW'],//是否有新注册反馈号
                'isFreeze' => $item['ISFREEZE'],//冻结
                'isAbandon' => $item['ISABANDON'],//是否放弃
                'isBeactive' => $item['BEACTIVE'],//是否活动
                'barCode' => $item['BARCODE'],//条码
                'goodsCode' => $item['GOODSCODE'],//商品编号
                'oldGoodsCode' => $item['OLDCODE'],//旧编码
                'goodsName' => $item['GOODSNAME'],//商品名称
                'goodsForeignName' => $item['FOREIGNNAME'],//西文名称
                'entShortName' => $item['SHORTNAME'],//单位简称
                'goodsLogogram' => $item['LOGOGRAM'],//助记码
                'goodsSpec' => $item['GOODSSPEC'],//商品规格
                'goodsPlace' => $item['PLACE'],//商品产地
                'manufacturerName' => $item['MANUFACTURER'],//生产厂家
                'goodsDesc' => $item['GOODSDESC'],//商品详述
                'createTime' => $item['CREATETIME'],//创建时间
                'goodsDocLevID' => $item['DOCLEVID'],//资料权限级别ID
                'goodsInfoCreaterUserID' => $item['CREATERID'],//信息登录人
                'lastModifyTime' => $item['LASTMODIFYTIME'],//最后更新时间
                'goodsDelUserID' => $item['DELUSERID'],//信息删除人
                'goodsDelTime' => $item['DELTIME'],//删除时间
                'EXPAUTORG' => $item['EXPAUTORG'],//扩展授权机构
                'RESAUTORG' => $item['RESAUTORG'],//授权限制机构
                'billNo' => $item['BILLNO'],//单据ID
                'isPass' => $item['ISPASS'],//是否审批
                'FlagID' => $item['FLAGID'],//标示ID
                'isCommPass' => $item['ISCOMMPASS'],//云社区审批
                'syncID' => $item['SYNCID'],//同步ID
                'businessID' => $item['BUSINESSID'],//单位ID
                'checkCost' => $item['CHECKCOST'],//考核成本价
                'manufacturerID' => $item['MFCID'],//生产厂家Id
                'lastModifyUserID' => $item['LASTSTFID'],//最后修改人
                'countryID' => $item['COUNTRYID'],//国家ID
                'isImported' => $item['ISIMPORTED'],//是否进口药品
                'parentGoodsID' => $item['PARGOODSID'],//父商品id
                'isCombin' => $item['ISCOMBIN'],//是否组合商品
                'varietiesID' => $item['VARIID'],//品种id
                'indication' => $item['INDICATION'],//适应症
                'packSpec' => $item['PKGAMOUNT'],//包装规格
                'offlineGoodsID' => $item['CORGOODSID'],//线下商品ID
                'elecGoodsCode_city' => $item['ELECGOODSCODE_CITY'],//市监管网药品编码
                'oldGoodsCode2' => $item['OLDCODE2'],//惠州仁正编号
            );
            //商品属性
            $attributeData =array(
                'erpID' => 1,//ERPID
                'entID' => $item['ENTID'],//企业ID
                'erpSortCode' => $item['K_FLM'],//分类码
                'erpLocationCode' => $item['K_HWM'],//货位码
                'erpTagCode' => $item['K_BJM'],//标记码
                'erpSmallTicket' => $item['K_SPISFP'],//默认小票
                'erpBigClassValue' => $item['K_CATONE'],//大类值
                'erpMiddleClassValue' => $item['K_CATTWO'],//中类值
                'erpSmallClassValue' => $item['K_CATTHREE'],//小类值
                'erpListingPermitHolder' => $item['K_SHANGSXKCYR'],//上市许可持有人
                'erpFunctionalClass' => $item['K_GNFL'],//功能分类
                'erpLevelClass' => $item['K_JBFL'],//级别分类
                'ProductionlicenseNo' => $item['PROCERTNO'],//生产许可证号
                'LicenseIssuingDate' => $item['PROCERTDATE'],//许可发证日期
                'LicenseValidityDate' => $item['PROCERTTO'],//生产许可有效期至
                'erpContractProductionValidityDate' => $item['K_ENTRUSTDATE'],//委托生产批件有效期
                'erpStandardSellingPrice' => $item['K_PLANSALEP'],//标准售价
                'erpTaxRateReminderMark' => $item['K_RATE_TX'],//税率提醒标识
                'erpRemarksValidityDate' => $item['K_REMARKTO'],//备注有效期至
                'erpIsUploadPingduoduo' => $item['K_IS_PDD'],//是否上传拼多多
                'erpIsNewGoods' => $item['K_IS_NEW'],//是否新品
                'erpCendUploadRatio' => $item['K_CDSCBL'],//C端上传比例
                'qualityStandard' => $item['QUALSTAND'],//质量标准
                'isLockedSale' => $item['ISSALE'],//是否锁定
                'isStopPurchasing' => $item['ISPROCUR'],//停止采购
                'isBlockNegativeInventory' => $item['ISCONTROL'],//是否拦截负库存
                'isMainContact' => $item['ISMAIN'],//是否主联系人
                'isPromotionalPackage' => $item['ISPROM'],//是否促销包
                'upperLimitPurchasePrice' => $item['LIMITPURP'],//上限进价
                'downLimitPurchasePrice' => $item['DOWNPURP'],//下限进价
                'isCostAccounting' => $item['ISCOSTACCT'],//是否成本核算
                'minSellingPrice' => $item['MAXSALEP'],//最低售价
                'maxSellingPrice' => $item['MINSALEP'],//最高售价
                'minRetailPrice' => $item['MAXRETAIP'],//最低零售价
                'maxRetailPrice' => $item['MINRETAIP'],//最高零售价
                'proposalRetailPrice' => $item['ADRETAILP'],//建议零售价
                'memberPrice' => $item['MEMPRICE'],//会员价
                'isManagePackingBalance' => $item['MANAGEPACK'],//是否管理包装结存
                'innerSettleRatio' => $item['INTERSCA'],//内部结算比例
                'gender' => $item['GENDER'],//性别
                'planningClass' => $item['PLANCATE'],//规划分类
                'isLimitQuantity' => $item['ISLIMIT'],//是否限制数量
                'limitQuantity' => $item['LIMITNUM'],//限制数量
                'isPrescription' => $item['ISPRES'],//是否处方药
                'approvalDate' => $item['APPROVALDATE'],//批准日期
                'approvalNumberValidityDate' => $item['APPROVALTO'],//批准文号有效期至
                'isDrugs' => $item['ISDRUG'],//是否毒品
                'isnarcotic' => $item['ISKEF'],//是否麻醉品
                'isQualityTest' => $item['ISQA'],//是否质量检验
                'validityDate' => $item['INEFFECTDAY'],//有效期
                'validityDateUnit' => $item['DAYUNIT'],//效期单位
                'defaultWarehouseID' => $item['DFTWHID'],//默认库房ID
                'defaultLocationID' => $item['DFTLOCATID'],//默认货位ID
                'curingCycle' => $item['CURCYCLE'],//养护周期
                'isBargainGoods' => $item['ISTJ'],//是否特价商品
                'bargainPrice' => $item['TJ'],//特价
                'storageConditions' => $item['STORAGETERM'],//存储条件
                'isNetSupervise' => $item['ISELEC'],//是否电子监管
                'isAcceptAgain' => $item['ISDOUCHK'],//是否二次验收
                'isFDAReport' => $item['ISWITHPIC'],//是否附药监报告
                'trademark' => $item['REGMARK'],//注册商标
                'countryRetailPrice' => $item['LISTPRICE'],//国零价
                'qualityCategory' => $item['QCATEGORY'],//质量类别
                'internalPurchasePrice' => $item['INNERPRICE'],//内购价
                'trademarkValidityDate' => $item['REGMARKVALTO'],//注册商标有效期
                'isEssentialDrugs' => $item['ISBASEMED'],//是否基本药物
                'bargainStartDate' => $item['SDATE'],//特价起始日期
                'bargainEndDate' => $item['EDATE'],//特价终止日期
                'storagePackingSize' => $item['WMSMEAS'],//仓储包装规格
                'countryWholesalePrice' => $item['LISTSALEP'],//国批价
                'isWholesaleLimitNum' => $item['ISWHOLLIMT'],//批发是否限制数量
                'wholesaleLimitNum' => $item['WHOLLIMTNUM'],//批发限制数量
                'isEphedrine' => $item['ISEPHE'],//是否含麻黄碱
                'isCheckPackagingUnit' => $item['ISCHKPACK'],//是否检查包装单位
                'currencyID' => $item['CURRENCYID'],//币种ID
                'tagPrice' => $item['PAIJIA'],//牌价
                'ElectronicSupervisionIcode' => $item['ELECFLAG'],//电子监管识别码
                'isChronicMedication' => $item['ISCHRONIC'],//是否慢性用药
                'regulatoryNetDrugCode' => $item['ELECGOODSCODE'],//监管网药品编码
                'treatmentClass' => $item['TREATCATE'],//诊疗类别
                'GMPCertificateNo' => $item['GMPNO'],//GMP证书号
                'GMPCertificateNoVDate' => $item['GMPVALDATE'],//GMP证书效期
                'isContraceptives' => $item['ISADULTTIN'],//是否计生用品
                'erpSplitID' => $item['K_SEPORDFLAG'],//分单标识
                'goodsRemarks' => $item['REMARK'],//备注
                'isNeedInquiry' => $item['ISRFQ'],//是否需要询价
                'safetyStockDays' => $item['SAFEDAYS'],//安全库存天数
                'isStimulants' => $item['ISDOPE'],//是否兴奋剂
                'lastUpdateTime' => $item['LASTMODIFYTIME'],//最后更新时间
                'isActive' => $item['BEACTIVE'],//是否活动
                'medicalInsuranceClass' => $item['MEDCARETYPE'],//医保类别
                'isTwoVoteSysControl' => $item['ISTWOVOTE'],//是否两票制管控
                'prescriptionClassification' => $item['RECIPETYPE'],//处方分类
                'apparatusRegNo' => $item['INSTREGNO'],//器械注册证号
                'apparatusRegVDate' => $item['INSTREGVALDATE'],//器械注册证有效期
                'productionLicenseVDate' => $item['MFCREGVALDATE'],//生产许可证有效期
                'businessLicenseNo' => $item['REGNO'],//营业执照号
                'registrationVDate' => $item['REGVALDATE'],//登记证有效期至
                'annualReport' => $item['RPTYEAR'],//年度报告年度
                'annualReportVDate' => $item['REGYEARDATE'],//年度报告有效期
                'drugStandardCode' => $item['STANDCODE'],//药品本位码
                'isBTB' => $item['K_B2B'],//B2B标志
                'piecePacked' => $item['K_PACKAGE'],//件包装
                'middlePacked' => $item['K_ZHONGBZ'],//中包装
                'buyerUserID' => $item['K_BUYERID'],//采购员
                'assistantUserID' => $item['K_ASSISTANTID'],//助理
                'baseDrugClass' => $item['K_HEDS'],//基药类别
                'goodsValidityDateTo' => $item['VALDATE'],//有效期至
                'taxClassificationNo' => $item['K_TAXCODE'],//税务分类编号
                'bigClass' => $item['CATONE'],//大类
                'middleClass' => $item['CATTWO'],//中类
                'smallClass' => $item['CATTHREE'],//小类
                'goodsReturnMark' => $item['RTNFLAG'],//退货标志
                'businessCategory' => $item['OPCAT'],//经营类别
                'marketStructure' => $item['MARKETCAT'],//市场结构
                'supplyChannels' => $item['SOURCECAT'],//供应渠道
                'isMustHave' => $item['ISNECE'],//是否必备
                'isBrandGoods' => $item['ISBRAND'],//是否品牌商品
                'isWeigh' => $item['ISWEIGHT'],//是否称重
                'isPurchaseBasedOnSales' => $item['K_IS_JIT'],//是否以销定采
                'isCalculateOutOfStockRate' => $item['K_ISCCLQHL'],//是否计算缺货率
                'nameUsedBefore' => $item['OLDNAME'],//曾用名                
                'isMaterial' => $item['ISMATERIEL'],//是否物料
                'isAccessories' => $item['ISAUXIL'],//是否辅料
                'isOfficeSupplies' => $item['ISTOOLS'],//是否办公用品
                'isFixedAssets' => $item['ISASSETS'],//是否固定资
                'isServiceItems' => $item['ISSERV'],//是否服务项目
                'isGiftGoods' => $item['ISFREE'],//是否赠品
                'isPresentGoods' => $item['ISGIFT'],//是否礼品
                'isManageInventory' => $item['ISSTOCKS'],//是否管理库存
                'costAccountingMethod' => $item['HESID'],//成本核算方式ID
                'costCccountingLevel' => $item['ACCTLEVEL'],//成本核算级别
                'dimensionSupervisorMode' => $item['ANGLETYPE'],//维度管理方式
                'dimensionSupervisorPlanID' => $item['SCHEMAID'],//维度方案ID
                'lifeSpan' => $item['YEARSNUM'],//生命年限
                'season' => $item['SEASON'],//季节
                'fabric' => $item['FABRIC'],//面料
                'parts' => $item['ACCES'],//配件
                'delistingDate' => $item['EXITDATE'],//撤市日期
                'shelfLifeMethod' => $item['LIFETYPE'],//保质期方式
                'numberOfPackagesRatio' => $item['CONVRATIO'],//件数与数量折算比例
                'inputTaxRate' => $item['INTAXRATE'],//进项税率
                'outputTaxRate' => $item['OUTTAXRATE'],//销项税率
                'standardPurchasePrice' => $item['PURP'],//标准进价
                'taxIncludedPurchasePrice' => $item['PURTAXP'],//含税进价
                'salePrice' => $item['SALEP'],//售价
                'taxSalePrice' => $item['SALETAXP'],//含税售价
                'retailPrice' => $item['RETAILP'],//零售价
                'financialAccount' => $item['KEMU'],//对应财务科目
                'isGoods' => $item['ISGOODS'],//是否商品
                'isUnpackingSales' => $item['ISALWUNDINPACK'],//允许拆中包装销售
                'goodsGroups' => $item['GOODSGROUP'],//互斥分组
                'firstSupplier' => $item['FIRSTSUPP'],//首次供应商
                'firstSupplyFinish' => $item['ISPUROK'],//首次供应完成
                'fileNo' => $item['ARCHNO'],//档案编号
                'shareLevel' => $item['MAINLEVEL'],//推荐等级
                'natureNales' => $item['SALETYPE'],//商品销售性质
                'isManageMulti' => $item['ISANGLE'],//是否管理多维度
                'goodsListingDate' => $item['APPEARDATE'],//上市日期
                'billNo' => $item['BILLNO'],//单据ID
                'isPass' => $item['ISPASS'],//是否审批
                'goodsCommonName' => $item['GENERALNAME'],//通用名
                'goodsChemicalName' => $item['CHEMNAME'],//化学名
                'goodsCategory' => $item['GCATEGORY'],//商品类别
                'isGSP' => $item['ISGSP'],//GSP达标
                'isGMP' => $item['ISGMP'],//是否有GMP证书
                'firstManageStatus' => $item['ISFIRST'],//首营状态
                'isDecoctionPieces' => $item['ISHERBAL'],//是否饮片
                'isCnMedicine' => $item['ISTRAD'],//是否中药材
                'approvalNo' => $item['APPROVALNO'],//批准文号
                'formType' => $item['FORMULA'],//剂型
                'isPsychotropicDrugs' => $item['ISPSY'],//是否精神药品
                'isMedicareDrugs' => $item['ISMEDICARE'],//是否医保药品
                'isColdStorage' => $item['ISREFRIG'],//是否冷藏
                'biddingPrice' => $item['BIDDPRICE'],//招标价
                'exFactoryPrice' => $item['EXPRICE'],//正常出厂价                
            );
            //商品包装
            $packData =array(
                'erpID' => 1,//ERPID
                'entID' => $item['ENTID'],//企业ID
                'packID' => $item['PACKID'],//商品包装ID
                'unit' => $item['UNIT'],//单位
                'meteringSpecs' => $item['MEAS'],//计量规格
                'barCode' => $item['BARCODE'],//条码
                'isBasicAccount' => $item['ISBASE'],//是否基本账户
                'isPurPack' => $item['ISPURPACK'],//采购默认单位
                'isRetPack' => $item['ISRETPACK'],//零售默认单位
                'isSalePack' => $item['ISSALEPACK'],//销售默认单位
                'packLength' => $item['LENGTH'],//长度
                'packWidth' => $item['WIDE'],//宽度
                'packHeight' => $item['HIGH'],//高度
                'packVolume' => $item['BULKS'],//体积
                'packWeight' => $item['WEIGHT'],//重量
                'isAllowPurchase' => $item['ISALWPUR'],//是否允许采购
                'isAllowRetail' => $item['ISALWRET'],//是否允许零售
                'isMiddlePack' => $item['ISINPACK'],//是否中包装
                'isStoragePack' => $item['ISWMSPACK'],//是否仓储包装
                //'saleStock' => $item['BESALENUM'],//可销数量
            );
            $goodsDoc = pdo_fetchall("SELECT erpGoodsID FROM " . tablename('elapp_shop_erp_goods_doc') . " WHERE erpGoodsID=:erpGoodsID and uniacid=:uniacid ", array('erpGoodsID' => $item['GOODSID'],'uniacid'=>$_W['uniacid']));
            //如果本地ERP已经存在同步过的商品，更新商品资料
            if(!empty($goodsDoc)){
                //更新商品资料表
                pdo_update('elapp_shop_erp_goods_doc',$docData, array('erpGoodsID' => $item['GOODSID']));//ok
                //更新商品属性表
                pdo_update('elapp_shop_erp_goods_attribute',$attributeData, array('erpGoodsID' => $item['GOODSID']));//ok
                //更新商品包装表
                pdo_update('elapp_shop_erp_goods_pack',$packData, array('erpGoodsID' => $item['GOODSID']));//ok
            }else{
                //否则添加商品资料
                //商品资料
                $docData['erpGoodsID'] = $item['GOODSID'];
                $docData['uniacid'] = $_W['uniacid'];
                //商品属性
                $attributeData['erpGoodsID'] = $item['GOODSID'];
                $attributeData['uniacid'] = $_W['uniacid'];
                //商品包装
                $packData['erpGoodsID'] = $item['GOODSID'];
                $packData['uniacid'] = $_W['uniacid'];

                //添加商品资料表
                pdo_insert('elapp_shop_erp_goods_doc',$docData);//ok
                //添加商品属性表
                pdo_insert('elapp_shop_erp_goods_attribute',$attributeData);//ok
                //添加商品包装表
                pdo_insert('elapp_shop_erp_goods_pack',$packData);//ok
            }
            plog('goods.erpgoods.renzheng.synGoodsDoc', "同步商品 ID: {$item['GOODSID']} 商品名称: {$item['GOODSNAME']} ");
        }
        show_json(1, array('url' => referer()));
    }
}