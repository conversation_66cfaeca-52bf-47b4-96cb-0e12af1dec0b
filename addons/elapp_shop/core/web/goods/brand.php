<?php
namespace web\controller\goods;
use web\controller\WebPage;

class BrandController extends WebPage{
	public function main(){
		global $_W,$_GPC;
		$condition = ' and uniacid=:uniacid';
		$params = array(':uniacid' => $_W['uniacid']);
		$list = pdo_fetchall('SELECT * FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE 1 ' . $condition . '  ORDER BY displayorder DESC', $params);
		$total = pdo_fetchcolumn('SELECT count(1) FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE 1 ' . $condition, $params);
		include $this->template();
    }

	public function add(){
		$this->post();
	}

	public function edit(){
		$this->post();
	}

	protected function post(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if ($_W['ispost']) {
			$_GPC['brandname'] = trim($_GPC['brandname']);
			$_GPC['status'] = intval($_GPC['status']);
			if (empty($_GPC['brandname'])) {
				show_json(0, '请输入品牌名称');
			}
			$data = array();
			$data['uniacid'] = $_W['uniacid'];
			$data['brandname'] = $_GPC['brandname'];
			$data['status'] = $_GPC['status'];
			if (!empty($id)) {
				pdo_update('elapp_shop_goods_brand', $data, array('id' => $id));
			}else {
				pdo_insert('elapp_shop_goods_brand', $data);
				$id = pdo_insertid();
			}
			show_json(1);
		}
		$item = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE id = \'' . $id . '\' and uniacid = \'' . $_W['uniacid'] . '\'');
        include $this->template('goods/brand/post');
	}

	public function delete(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);
		foreach ($items as $item) {
			pdo_delete('elapp_shop_goods_brand', array('id' => $item['id']));
		}
		show_json(1, array('url' => referer()));
	}

	public function status(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		if (empty($id)) {
			$id = (is_array($_GPC['ids']) ? implode(',', $_GPC['ids']) : 0);
		}
		$items = pdo_fetchall('SELECT id FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);
		foreach ($items as $item) {
			pdo_update('elapp_shop_goods_brand', array('status' => intval($_GPC['status'])), array('id' => $item['id']));
		}
		show_json(1, array('url' => referer()));
	}

	public function displayorder(){
		global $_W,$_GPC;
		$id = intval($_GPC['id']);
		$displayorder = intval($_GPC['value']);
		$item = pdo_fetchall('SELECT id FROM ' . tablename('elapp_shop_goods_brand') . ' WHERE id in( ' . $id . ' ) AND uniacid=' . $_W['uniacid']);

		if (!empty($item)) {
			pdo_update('elapp_shop_goods_brand', array('displayorder' => $displayorder), array('id' => $id));
		}
		show_json(1);
	}
}