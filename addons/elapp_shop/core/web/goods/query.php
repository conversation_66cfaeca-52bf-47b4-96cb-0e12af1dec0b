<?php
namespace web\controller\goods;
use web\controller\WebPage;

class QueryController extends WebPage {
    function main(){
        global $_W, $_GPC;
        $kwd = trim($_GPC['keyword']);
        $type = intval($_GPC['type']);
        $live = intval($_GPC['live']);
        $params = array();
        $params[':uniacid'] = $_W['uniacid'];
        $condition=" and status=1 and deleted=0 and uniacid=:uniacid";
        if (!empty($kwd)) {
            $condition.=" AND (`title` LIKE :keywords OR `keywords` LIKE :keywords)";
            $params[':keywords'] = "%{$kwd}%";
        }
        if (empty($type)) {
            $condition.=" AND `type` != 10 ";
        }else{
            $condition.=" AND `type` = :type ";
            $params[':type'] = $type;
        }

        $ds = pdo_fetchall('SELECT id,title,thumb,marketprice,productprice,share_title,share_icon,description,minprice,costprice,stock,sales,islive,liveprice FROM ' . tablename('elapp_shop_goods') . " WHERE 1 {$condition} order by createtime desc", $params);
        foreach($ds as &$value){
            $value['share_title'] = htmlspecialchars_decode($value['share_title']);
            unset($value);
        }
        $ds = set_medias($ds, array('thumb','share_icon'));
        if($_GPC['suggest']){
            die(json_encode(array('value'=>$ds)));
        }
        include $this->template();
    }
}