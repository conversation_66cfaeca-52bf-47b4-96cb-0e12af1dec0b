<?php
namespace web\controller\perm;
use web\controller\WebPage;

class LogController extends WebPage {

	function main() {
		global $_W,$_GPC;
        $permset = intval(m('cache')->getString('permset', 'global'));
        $is_perm_plugin = true;
        if($permset && !com_run('perm::is_perm_plugin', 'perm',true)) $is_perm_plugin = false;
        if(!cv('perm') || !$is_perm_plugin){
            show_message('暂无此操作权限', '', 'error');
        }

		$pindex = max(1, intval($_GPC['page']));
		$psize = 20;
		$condition = " and log.uniacid=:uniacid";
		$params = array(':uniacid' => $_W['uniacid']);

		if (!empty($_GPC['keyword'])) {
			$_GPC['keyword'] = trim($_GPC['keyword']);
			$condition.=' and ( log.op like :keyword or u.username like :keyword)';
			$params[':keyword'] = "%{$_GPC['keyword']}%";
		}
		if (!empty($_GPC['logtype'])) {
            $condition.=' and log.type=:logtype';
			$params[':logtype'] = trim($_GPC['logtype']);
		}

		if (empty($starttime) || empty($endtime)) {
			$starttime = strtotime('-1 month');
			$endtime = time();
		}
		if(!empty($_GPC['time'])){
            if(!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])){
                $starttime = strtotime($_GPC['time']['start']);
                $endtime = strtotime($_GPC['time']['end']);
                $condition .= " AND log.createtime >= :starttime AND log.createtime <= :endtime ";
                $params[':starttime'] = $starttime;
                $params[':endtime'] = $endtime;
            }
        }
        $extendCondition = ' and u.username <> ""';
        //$extendCondition = null;
		$list = pdo_fetchall("SELECT  log.* ,u.username FROM " . tablename('elapp_shop_perm_log') . " log  "
			. " left join " . tablename('users') . " u on log.uid = u.uid  "
			. " WHERE 1 {$condition}{$extendCondition} ORDER BY id desc LIMIT " . ($pindex - 1) * $psize . ',' . $psize, $params);

		$total = pdo_fetchcolumn("SELECT count(*) FROM " . tablename('elapp_shop_perm_log') . " log  "
			. " left join " . tablename('users') . " u on log.uid = u.uid  "
			. " WHERE 1 {$extendCondition}{$condition} ", $params);

		$pager = pagination2($total, $pindex, $psize);
		$types = com('perm')->getLogTypes();
		include $this->template('perm/log/index');
	}

	public function merch() {
        global $_W,$_GPC;
        // 判断应用中心
        if(!m('common')->pluginPermissions('merch')) {
            show_message('暂无此操作权限', '', 'error');
        }
        $permset = intval(m('cache')->getString('permset', 'global'));
        $is_perm_plugin = true;
        if($permset && !com_run('perm::is_perm_plugin', 'perm',true)) $is_perm_plugin = false;
        if(!cv('perm') || !$is_perm_plugin){
            show_message('暂无此操作权限', '', 'error');
        }
        //ini_set('display_errors','On');
        //ini_set('error_reporting', E_ALL);
        // 多商户操作日志
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $params = array(':uniacid' => $_W['uniacid']);
        $condition = " and log.uniacid=:uniacid";
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $condition.=' and ( log.op like :keyword or user.merchname like :keyword)';
            $params[':keyword'] = "%{$_GPC['keyword']}%";
        }
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        if(!empty($_GPC['time'])){
            if(!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])){
                $starttime = strtotime($_GPC['time']['start']);
                $endtime = strtotime($_GPC['time']['end']);
                $condition .= " AND log.createtime >= :starttime AND log.createtime <= :endtime ";
                $params[':starttime'] = $starttime;
                $params[':endtime'] = $endtime;
            }
        }

        $sql = "select log.id,user.merchname as username,log.name,log.op,log.ip,log.createtime from" . tablename('elapp_shop_merch_perm_log') . " log "
                ." left join " . tablename('elapp_shop_merch_user') . " user"
                ." on log.merchid = user.id"
                ." where 1 {$condition}"
                ." order by log.id desc"
                ." limit ". ($pindex - 1) * $psize.",".$psize;

        $list = pdo_fetchall($sql,$params);
        // 已经删除的会员做下处理
        array_walk($list, function (&$item, $key) {
            if (empty($item['username'])) {
                $item['username'] = '该用户已被删除';
            }
        });

        $total = pdo_fetchcolumn("select count(log.id) from" . tablename('elapp_shop_merch_perm_log') . " log "
            ." left join " . tablename('elapp_shop_merch_user') . " user"
            ." on log.merchid = user.id"
            ." where 1 {$condition}",$params);

        $pager = pagination2($total, $pindex, $psize);
        include $this->template();
    }

    //组织
    public function org() {
        global $_W,$_GPC;
        // 判断应用中心
        if(!m('common')->pluginPermissions('org')) {
            show_message('暂无此操作权限', '', 'error');
        }
        $permset = intval(m('cache')->getString('permset', 'global'));
        $is_perm_plugin = true;
        if($permset && !com_run('perm::is_org_plugin', 'perm',true)) $is_perm_plugin = false;
        if(!cv('perm') || !$is_perm_plugin){
            show_message('暂无此操作权限', '', 'error');
        }
        //ini_set('display_errors','On');
        //ini_set('error_reporting', E_ALL);
        // 组织操作日志
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $params = array(':uniacid' => $_W['uniacid']);
        $condition = " and log.uniacid=:uniacid";
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $condition.=' and ( log.op like :keyword or user.orgname like :keyword)';
            $params[':keyword'] = "%{$_GPC['keyword']}%";
        }
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        if(!empty($_GPC['time'])){
            if(!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])){
                $starttime = strtotime($_GPC['time']['start']);
                $endtime = strtotime($_GPC['time']['end']);
                $condition .= " AND log.createtime >= :starttime AND log.createtime <= :endtime ";
                $params[':starttime'] = $starttime;
                $params[':endtime'] = $endtime;
            }
        }

        $sql = "select log.id,user.orgname as username,log.name,log.op,log.ip,log.createtime from" . tablename('elapp_shop_org_perm_log') . " log "
            ." left join " . tablename('elapp_shop_org_user') . " user"
            ." on log.org_id = user.id"
            ." where 1 {$condition}"
            ." order by log.id desc"
            ." limit ". ($pindex - 1) * $psize.",".$psize;

        $list = pdo_fetchall($sql,$params);
        // 已经删除的会员做下处理
        array_walk($list, function (&$item, $key) {
            if (empty($item['username'])) {
                $item['username'] = '该用户已被删除';
            }
        });

        $total = pdo_fetchcolumn("select count(log.id) from" . tablename('elapp_shop_org_perm_log') . " log "
            ." left join " . tablename('elapp_shop_org_user') . " user"
            ." on log.org_id = user.id"
            ." where 1 {$condition}",$params);

        $pager = pagination2($total, $pindex, $psize);
        include $this->template();
    }
    //合伙人
    public function copartner() {
        global $_W,$_GPC;
        // 判断应用中心
        if(!m('common')->pluginPermissions('copartner')) {
            show_message('暂无此操作权限', '', 'error');
        }
        $permset = intval(m('cache')->getString('permset', 'global'));
        $is_perm_plugin = true;
        if($permset && !com_run('perm::is_perm_plugin', 'perm',true)) $is_perm_plugin = false;
        if(!cv('perm') || !$is_perm_plugin){
            show_message('暂无此操作权限', '', 'error');
        }
        //ini_set('display_errors','On');
        //ini_set('error_reporting', E_ALL);
        // 多商户操作日志
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $params = array(':uniacid' => $_W['uniacid']);
        $condition = " and log.uniacid=:uniacid";
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $condition.=' and ( log.op like :keyword or user.mcnname like :keyword)';
            $params[':keyword'] = "%{$_GPC['keyword']}%";
        }
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        if(!empty($_GPC['time'])){
            if(!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])){
                $starttime = strtotime($_GPC['time']['start']);
                $endtime = strtotime($_GPC['time']['end']);
                $condition .= " AND log.createtime >= :starttime AND log.createtime <= :endtime ";
                $params[':starttime'] = $starttime;
                $params[':endtime'] = $endtime;
            }
        }

        $sql = "select log.id,user.mcnname as username,log.name,log.op,log.ip,log.createtime from" . tablename('elapp_shop_copartner_perm_log') . " log "
                ." left join " . tablename('elapp_shop_copartner_user') . " user"
                ." on log.copartner_id = user.id"
                ." where 1 {$condition}"
                ." order by log.id desc"
                ." limit ". ($pindex - 1) * $psize.",".$psize;

        $list = pdo_fetchall($sql,$params);
        // 已经删除的会员做下处理
        array_walk($list, function (&$item, $key) {
            if (empty($item['username'])) {
                $item['username'] = '该用户已被删除';
            }
        });

        $total = pdo_fetchcolumn("select count(log.id) from" . tablename('elapp_shop_copartner_perm_log') . " log "
            ." left join " . tablename('elapp_shop_copartner_user') . " user"
            ." on log.copartner_id = user.id"
            ." where 1 {$condition}",$params);

        $pager = pagination2($total, $pindex, $psize);
        include $this->template();
    }
	public function supply()
    {
        global $_W,$_GPC;
        // 判断应用中心
        if(!m('common')->pluginPermissions('supply')) {
            show_message('暂无此操作权限', '', 'error');
        }
        $permset = intval(m('cache')->getString('permset', 'global'));
        $is_perm_plugin = true;
        if($permset && !com_run('perm::is_perm_plugin', 'perm',true)) $is_perm_plugin = false;
        if(!cv('perm') || !$is_perm_plugin){
            show_message('暂无此操作权限', '', 'error');
        }
        //ini_set('display_errors','On');
        //ini_set('error_reporting', E_ALL);
        // 多商户操作日志
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $params = array(':uniacid' => $_W['uniacid']);
        $condition = " and log.uniacid=:uniacid";
        if (!empty($_GPC['keyword'])) {
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $condition.=' and ( log.op like :keyword or user.supplyname like :keyword)';
            $params[':keyword'] = "%{$_GPC['keyword']}%";
        }
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        if(!empty($_GPC['time'])){
            if(!empty($_GPC['time']['start']) && !empty($_GPC['time']['end'])){
                $starttime = strtotime($_GPC['time']['start']);
                $endtime = strtotime($_GPC['time']['end']);
                $condition .= " AND log.createtime >= :starttime AND log.createtime <= :endtime ";
                $params[':starttime'] = $starttime;
                $params[':endtime'] = $endtime;
            }
        }

        $sql = "select log.id,user.supplyname as username,log.name,log.op,log.ip,log.createtime from" . tablename('elapp_shop_supply_perm_log') . " log "
                ." left join " . tablename('elapp_shop_supply_user') . " user"
                ." on log.supplyid = user.id"
                ." where 1 {$condition}"
                ." order by log.id desc"
                ." limit ". ($pindex - 1) * $psize.",".$psize;

        $list = pdo_fetchall($sql,$params);
        // 已经删除的会员做下处理
        array_walk($list, function (&$item, $key) {
            if (empty($item['username'])) {
                $item['username'] = '该用户已被删除';
            }
        });

        $total = pdo_fetchcolumn("select count(log.id) from" . tablename('elapp_shop_supply_perm_log') . " log "
            ." left join " . tablename('elapp_shop_supply_user') . " user"
            ." on log.supplyid = user.id"
            ." where 1 {$condition}",$params);

        $pager = pagination2($total, $pindex, $psize);
        include $this->template();
    }    
}
