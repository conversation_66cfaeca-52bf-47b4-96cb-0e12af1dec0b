<?php
namespace web\controller\sysset;
use web\controller\WebPage;

class PostertoolController extends WebPage{
	public function main(){
		include $this->template();
	}
	public function clear(){
		global $_W,$_GPC;
        //todo 这里的目录要根据往后迁移的目录进行修改 Hlei 2023-09-08
		load()->func('file');
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/poster/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid']);
		$acid = pdo_fetchcolumn('SELECT acid FROM ' . tablename('account_wechats') . ' WHERE `uniacid`=:uniacid LIMIT 1', array(':uniacid' => $_W['uniacid']));
		pdo_update('elapp_shop_poster_qr', array('mediaid' => ''), array('acid' => $acid));
		plog('poster.clear', '清除海报缓存');
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/goodscode/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/poster_wxapp/commission/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/poster_wxapp/goods/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/postera/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . ' /task/poster/' . $_W['uniacid']);
		@rmdirs(DATA_ROOT . ELAPP_SHOP_MODULE_NAME . ' /upload/exchange/' . $_W['uniacid']);
		show_json(1);
	}
}