<?php
namespace web\controller\sysset;
use web\controller\WebPage;

class DatatransferController extends WebPage {
	public function main() {
		global $_W,$_GPC;
		$item = pdo_fetch('select dt.*,w.name from ' . tablename('elapp_shop_datatransfer') . ' dt left join ' . tablename('account_wechats') . ' w on w.uniacid = dt.touniacid where dt.fromuniacid =:uniacid limit 1', array(':uniacid' => $_W['uniacid']));
		$senduniacid = $_GPC['acid'];
		$isopen = $_GPC['isopen'];
		if ($_W['ispost']) {
			if (!empty($isopen)) {
				pdo_delete('elapp_shop_datatransfer', array('fromuniacid' => $_W['uniacid']));
				show_json(1, array('url' => referer()));
			}
			$data = array('fromuniacid' => $_W['uniacid'], 'touniacid' => $senduniacid, 'status' => 1);
			pdo_insert('elapp_shop_datatransfer', $data);
			$tables = array('elapp_shop_category', 'elapp_shop_carrier', 'elapp_shop_adv', 'elapp_shop_feedback', 'elapp_shop_form', 'elapp_shop_form_category', 'elapp_shop_gift', 'elapp_shop_goods', 'elapp_shop_goods_comment', 'elapp_shop_goods_group', 'elapp_shop_goods_label', 'elapp_shop_goods_labelstyle', 'elapp_shop_goods_option', 'elapp_shop_goods_param', 'elapp_shop_goods_spec', 'elapp_shop_goods_spec_item', 'elapp_shop_member_address', 'elapp_shop_member_printer', 'elapp_shop_member_printer_template', 'elapp_shop_member_group', 'elapp_shop_member_level', 'elapp_shop_member_log', 'mc_credits_record', 'elapp_shop_commission_apply', 'elapp_shop_commission_bank', 'elapp_shop_commission_level', 'elapp_shop_commission_log', 'elapp_shop_commission_rank', 'elapp_shop_commission_repurchase', 'elapp_shop_commission_shop', 'elapp_shop_order', 'elapp_shop_order_comment', 'elapp_shop_order_goods', 'elapp_shop_order_peerpay', 'elapp_shop_order_peerpay_payinfo', 'elapp_shop_order_refund');
			foreach ($tables as $table) {
				pdo_update($table, array('uniacid' => $senduniacid), array('uniacid' => $_W['uniacid']));
			}
			show_json(1, array('url' => referer()));
		}
		include $this->template();
	}
}