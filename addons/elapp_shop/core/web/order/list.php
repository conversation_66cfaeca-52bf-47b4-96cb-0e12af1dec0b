<?php
namespace web\controller\order;
use app\model\DoctorModel;
use app\model\SettleModel;
use app\plugin\clerk\core\logic\ClerkLevelLogic;
use app\plugin\prescription\core\enum\OrderEnum;
use app\plugin\prescription\core\logic\PrescriptionCheckGoodsLogic;
use app\plugin\prescription\core\logic\PrescriptionOrderGoodsLogic;
use web\controller\WebPage;
class ListController extends WebPage {
    private $filter;
    public function __construct($_init = true)
    {
        parent::__construct($_init);

        $this->filter = new \app\model\FilterModel();
    }

    protected  function orderData($status, $st,$index){
        global $_W,$_GPC;
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        
        //是否订单中有赠品,赠品标识
        $giftSign = false;
        //分销
        $p = p('commission');
        //店员
        $plugin_clerk = p('clerk');
        //医生
        $plugin_doctor = new DoctorModel();
        //店长
        $plugin_owner = p('vrshop');
        //帮扶分红
        $plugin_mentor = p('mentor');
        //机构
        $plugin_copartner = p('copartner');
        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
            $this->updateChildOrderPay();
        } else {
            $is_openmerch = 0;
        }
        //供应商
        $supply_plugin = p('supply');
        $supply_data = m('common')->getPluginset('supply');
        if ($supply_plugin && $supply_data['is_opensupply']) {
            $is_opensupply = 1;
            $this->updateChildOrderPay();
        } else {
            $is_opensupply = 0;
        }
        if ($st == "main") {
            $st = '';
        } else {
            $st = ".".$st;
        }
        
        $sendtype = !isset($_GPC['sendtype']) ? 0 : $_GPC['sendtype'];
        $condition = " o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0 and o.istrade=0 and o.iscycelbuy=0";
        
        $uniacid = $_W['uniacid'];
        $paras = $paras1 = array(':uniacid' => $uniacid);
        
        $merch_data = m('common')->getPluginset('merch');//多商户
        $supply_data = m('common')->getPluginset('supply');//供应商 Hlei 20220317
        
        if (empty($starttime) || empty($endtime)) {
            $starttime = strtotime('-1 month');
            $endtime = time();
        }
        
        $priceCondition = '';
        $timeCondition = '';
        //默认用订单创建时间排序
        $orderbuy = 'o.createtime';
        $searchtime = trim($_GPC['searchtime']);
        if (!empty($searchtime) && is_array($_GPC['time']) && in_array($searchtime, array('create', 'pay', 'send', 'finish'))) {
            $starttime = strtotime($_GPC['time']['start']);
            $endtime = strtotime($_GPC['time']['end']);
            $condition .= " AND o.{$searchtime}time >= :starttime AND o.{$searchtime}time <= :endtime ";
            $paras[':starttime'] = $starttime;
            $paras[':endtime'] = $endtime;
            $priceCondition .= " AND o.{$searchtime}time >= {$starttime} AND o.{$searchtime}time <= {$endtime} ";
            $timeCondition .= " AND o.{$searchtime}time >= {$starttime} AND o.{$searchtime}time <= {$endtime} ";
            $orderbuy='o.'.$searchtime.'time';
        }
        
        if ($_GPC['paytype'] != '') {
            if ($_GPC['paytype'] == '2') {
                $condition .= " AND ( o.paytype =21 or o.paytype=22 or o.paytype=23 )";
                $priceCondition.=" AND ( o.paytype =21 or o.paytype=22 or o.paytype=23 )";
            }else if($_GPC['paytype'] == '4'){
                $condition .= " AND o.paytype = 3 AND is_cashier = 1 "; //收银台现金收款
                $priceCondition.=" AND o.paytype = 3 AND is_cashier = 1 ";
            } else {
                $condition .= " AND o.paytype =" . intval($_GPC['paytype']);
                $priceCondition.=" AND o.paytype =".intval($_GPC['paytype']);
            }
        }
        
        //全返订单查询
        $order_type = $_GPC['order_type'];
        $fullback_where = '';
        $fullback_sqlcondition = '';
        if($order_type !== ''){
            $searchfield = trim(strtolower($_GPC['searchfield']));
            //查找普通订单
            if($order_type == 'general'){
                if(in_array($searchfield,array('goodstitle','goodssn','goodsoptiontitle'))){
                    //下文已经联查elapp_shop_order_goods 表
                    $fullback_where = ' and og.fullbackid=0';
                }else{
                    //此时需要联查elapp_shop_order_goods 表
                    $fullback_sqlcondition =  " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og  where og.uniacid = '$uniacid' and og.fullbackid=0) gs on gs.orderid=o.id";
                }
            }elseif ($order_type == 'fullback'){
                //查找全返订单
                $fullback_goods_id = intval($_GPC['fullback_goodsid']);
                $fullback_goods_where = '';
                if($fullback_goods_id){
                    $fullback_goods_where .= ' and og.goodsid='.$fullback_goods_id;
                }
                if(in_array($searchfield,array('goodstitle','goodssn','goodsoptiontitle'))){
                    //下文已经联查elapp_shop_order_goods 表
                    $fullback_where = $fullback_goods_where.' and og.fullbackid<>0';
                }else{
                    //此时需要联查elapp_shop_order_goods 表
                    $fullback_sqlcondition =  " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og  where og.uniacid = '$uniacid' and og.fullbackid<>0 {$fullback_goods_where}) gs on gs.orderid=o.id";
                }
            }
        }
        
        //查询拼接
        $sqlcondition = '';
        if (!empty($_GPC['searchfield']) && !empty($_GPC['keyword'])) {
            $searchfield = trim(strtolower($_GPC['searchfield']));
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'],ENT_QUOTES);
            
            if ($searchfield == 'ordersn') {
                $condition .= " AND locate(:keyword,o.ordersn)>0";
            } else if ($searchfield == 'member') {
                //if(empty($_GPC['isprecise'])){
                //  $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0)";
                //}else{
                //   $condition .= " AND (m.realname=:keyword or m.mobile=:keyword or m.nickname=:keyword)";
                //}
                //$priceCondition .= " AND (nickname LIKE '%".$_GPC['keyword']."%' OR realname LIKE '%".$_GPC['keyword']."%' OR mobile LIKE '%".$_GPC['keyword']."%') ";
                //$memberCondition = " AND (nickname LIKE '%".$_GPC['keyword']."%' OR realname LIKE '%".$_GPC['keyword']."%' OR mobile LIKE '%".$_GPC['keyword']."%') ";
            } elseif ($searchfield == "member_group") {
                $group = pdo_fetchall("select id from " . tablename("elapp_shop_member_group") . " where uniacid = :uniacid AND groupname like :group_name", array(":uniacid" => $_W["uniacid"], ":group_name" => "%" . $_GPC["keyword"] . "%"));
                if (!empty($group)) {
                    $groupId = implode(",", array_column($group, "id"));
                    $paras[":keyword"] = $groupId;
                } else {
                    $paras[":keyword"] = "-1";
                }
                $condition .= " AND m.groupid in (:keyword)";
            } elseif ($searchfield == "member_level") {
                $level = pdo_fetchall("select id from " . tablename("elapp_shop_member_level") . " where uniacid = :uniacid AND levelname like :level_name", array(":uniacid" => $_W["uniacid"], ":level_name" => "%" . $_GPC["keyword"] . "%"));
                $set = m("common")->getSysset();
                $defaultLevelName = $set["shop"]["levelname"] ?: "普通等级";
                if (strstr($defaultLevelName, $_GPC["keyword"])) {
                    $level[] = array("id" => 0);
                }
                if (!empty($level)) {
                    $levelId = implode(",", array_column($level, "id"));
                    $paras[":keyword"] = $levelId;
                } else {
                    $paras[":keyword"] = "-1";
                }
                $condition .= " AND m.level in (:keyword)";
            } else if ($searchfield == 'mid') {
                $condition .= " AND m.id = :keyword";
            }  else if ($searchfield == 'address') {
                $condition .= " AND ( locate(:keyword,a.realname)>0 or locate(:keyword,a.mobile)>0 or locate(:keyword,o.carrier)>0 or locate(:keyword,o.address)>0)";
                //$priceCondition .= " AND (a.realname LIKE '".$_GPC['keyword']."%' OR a.mobile LIKE '".$_GPC['keyword']."%')";
                $priceCondition .= " AND ( locate('".$_GPC['keyword']."',a.realname)>0 or locate('".$_GPC['keyword']."',a.mobile)>0 or locate('".$_GPC['keyword']."',o.carrier)>0 or locate('".$_GPC['keyword']."',o.address)>0)";
            } else if ($searchfield == 'location') {
                $condition .= " AND ( locate(:keyword,o.address)>0 or locate(:keyword,o.address_send)>0 )";
                $priceCondition .= " AND (o.address LIKE '%".$_GPC['keyword']."%' OR o.address_send LIKE '%".$_GPC['keyword']."%' ) ";
            } else if ($searchfield == 'expresssn') {
                $condition .= " AND locate(:keyword,o.expresssn)>0";
            } else if ($searchfield == 'saler') {
            //  $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0 or locate(:keyword,s.salername)>0 )";
                if(p('merch')){
                    $condition .= " AND (locate(:keyword,sm.realname)>0 or locate(:keyword,sm.mobile)>0 or locate(:keyword,sm.nickname)>0 or locate(:keyword,s.salername)>0 or locate(:keyword,ms.salername)>0 )";
                }else{
                    $condition .= " AND (locate(:keyword,sm.realname)>0 or locate(:keyword,sm.mobile)>0 or locate(:keyword,sm.nickname)>0 or locate(:keyword,s.salername)>0)";
                }
            } else if ($searchfield == 'verifycode') {
                $condition .= " AND (verifycode=:keyword or locate(:keyword,o.verifycodes)>0)";
            } else if ($searchfield == 'store') {
                if (p('merch')){
                    $sqlcondition = " left join " . tablename('elapp_shop_store') . " store on ( store.id = vgl.storeid or store.id = vol.storeid or store.id = o.verifystoreid) and store.uniacid=o.uniacid and o.ismerch=0
                    left join " . tablename('elapp_shop_merch_store') . " merstore on (  merstore.id = o.verifystoreid or merstore.id = vgl.storeid or merstore.id = vol.storeid )and merstore.uniacid=o.uniacid and o.ismerch=1";
                    $condition .= " AND (locate(:keyword,store.storename)>0 or locate(:keyword,merstore.storename)>0)";
                }else{
                    $sqlcondition = " left join " . tablename('elapp_shop_store') . " store on ( store.id = o.verifystoreid or store.id = vgl.storeid or store.id = vol.storeid ) and store.uniacid=o.uniacid ";
                    $condition .= " AND (locate(:keyword,store.storename)>0)";
                }
            } else if ($searchfield == 'goodstitle') {
                $sqlcondition =  " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (locate(:keyword,g.title)>0) {$fullback_where}) gs on gs.orderid=o.id";
                
            } else if ($searchfield == 'goodssn') {
                $sqlcondition =  " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (((locate(:keyword,g.goodssn)>0)) or (locate(:keyword,og.goodssn)>0)) {$fullback_where}) gs on gs.orderid=o.id";
            } else if ($searchfield == 'goodsoptiontitle') {
                $sqlcondition =  " inner join ( select  DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (locate(:keyword,og.optionname)>0) {$fullback_where}) gs on gs.orderid=o.id";
            } else if ($searchfield == 'merch') {
                if ($merch_plugin) {
                    $condition .= " AND (locate(:keyword,merch.merchname)>0)";
                    $sqlcondition = " left join " . tablename('elapp_shop_merch_user') . " merch on merch.id = o.merchid and merch.uniacid=o.uniacid";
                }
            } else if ($searchfield == 'supply') {//供应商 Hlei 20220317
                if ($supply_plugin) {
                    $condition .= " AND (locate(:keyword,supply.supplyname)>0)";
                    $sqlcondition = " left join " . tablename('elapp_shop_supply_user') . " supply on supply.id = o.supplyid and supply.uniacid=o.uniacid";
                }
            } else if($searchfield == 'selfget'){
                $condition .= " AND (locate(:keyword,store.storename)>0)";
                $sqlcondition = " left join " . tablename('elapp_shop_store') . " store on store.id = o.storeid and store.uniacid=o.uniacid";
            } else if ($searchfield == 'copartner') {
                //机构
                if ($plugin_copartner) {
                    $condition .= " AND (locate(:keyword,copartner.mcnname)>0)";
                    $sqlcondition = " left join " . tablename('elapp_shop_copartner_user') . " copartner on copartner.id = o.copartner_id and copartner.uniacid=o.uniacid";
                }
            } else if ($searchfield == 'copartner_account') {
                //机构经理
                if ($plugin_copartner) {
                    $condition .= " AND (locate(:keyword,copartner_account.realname)>0)";
                    $sqlcondition = " left join " . tablename('elapp_shop_copartner_account') . " copartner_account on copartner_account.id = o.copartner_account_id and copartner_account.uniacid=o.uniacid";
                }
            }
        }
        $sqlcondition .= $fullback_sqlcondition;    //拼接上全返查询语句
        $agentid = intval($_GPC['agentid']);
        $clerk_id = intval($_GPC['clerk_id']);//订单店员ID
        $doctor_id = intval($_GPC['doctor_id']);//订单医生ID
        $owner_id = intval($_GPC['owner_id']);//订单店长ID
        $mentor_id = intval($_GPC['mentor_id']);//帮扶人id
        $copartner_id = intval($_GPC['copartner_id']);//订单机构ID
        $copartner_account_id = intval($_GPC['copartner_account_id']);//订单机构UID
        $statuscondition = ''; 
        //分销商ID
        if (!empty($agentid)) {
            $statuscondition = ' AND o.status <> -1';
        }
        //店员ID
        if (!empty($clerk_id)) {
            $statuscondition = ' AND o.status <> -1';
        }
        //医生ID
        if (!empty($doctor_id)) {
            $statuscondition = ' AND o.status <> -1';
        }
        //店长ID
        if (!empty($owner_id)) {
            $statuscondition = ' AND o.status <> -1';
        }
        //帮扶ID
        if (!empty($mentor_id)) {
            $statuscondition = ' AND o.status <> -1';
        }
        //合伙人ID
        if (!empty($copartner_id)) {
            $statuscondition = ' AND o.status <> -1';
        }
        if ($status !== '') {            
            if ($status == '-1') {
                $statuscondition = " AND o.status=-1 and (o.refundtime=0 or o.refundstate=3)";
                $priceStatus = " AND status=-1 and (refundtime=0 or refundstate=3)";
            } else if ($status == '4') {
                $statuscondition = " AND ((o.refundstate>0 and o.refundid<>0 and o.refundtime=0) or (o.refundstate>0 and o.refundtime=0 and o.refundstate=3))";
                $priceStatus = " AND (refundstate>0 and refundid<>0 or (o.refundtime=0 and o.refundstate=3))";
            } else if ($status == '5') {
                $statuscondition = " AND o.refundtime<>0";
                $priceStatus = " AND refundtime<>0";
            } else if ($status=='1'){
                $statuscondition = " AND ( o.status = 1 or (o.status=0 and o.paytype=3) )";
                $priceStatus = " AND ( status = 1 or (status=0 and paytype=3) )";
            } else if($status=='0'){
                $statuscondition = " AND o.status = 0 and o.paytype<>3";
                $priceStatus = " AND status = 0 and paytype<>3";
            }else if($status=='2'){
                $statuscondition = " AND ( o.status = 2 or (o.status=1 and o.sendtype>0) )";
                $priceStatus = " AND (  status = 2 or (status=1 and sendtype>0) )";
            }else {
                $statuscondition = " AND o.status = ".intval($status);
                $priceStatus = " AND o.status = ".intval($status);
            }
        }
        //分销商        
        $level = 0;
        if ($p) {
            $cset = $p->getSet();
            $level = intval($cset['level']);
        }
        $olevel = intval($_GPC['olevel']);
        if (!empty($agentid) && $level > 0) {
            //显示三级订单
            $agent = $p->getInfo($agentid, array());
            if (!empty($agent)) {
                $agentLevel = $p->getLevel($agentid);
            }
            if (empty($olevel)) {
                if ($level >= 1) {
                    $condition.=' and  ( o.agentid=' . intval($_GPC['agentid']);
                }
                if($cset['selfbuy'] == 1){
                    //开启内购
                    if ($level >= 2 && $agent['level1'] > 0) {
                        $condition.= " or o.agentid in( " . implode(',', array_keys($agent['level1_agentids'])) . ")";
                    }
                    if ($level >= 3 && $agent['level2'] > 0) {
                        $condition.= " or o.agentid in( " . implode(',', array_keys($agent['level2_agentids'])) . ")";
                    }
                }else{
                    //不开启内购
                    if ($level >= 2 && $agent['level2'] > 0) {
                        $condition.= " or o.agentid in( " . implode(',', array_keys($agent['level1_agentids'])) . ")";
                    }
                    if ($level >= 3 && $agent['level3'] > 0) {
                        $condition.= " or o.agentid in( " . implode(',', array_keys($agent['level2_agentids'])) . ")";
                    }
                    
                }
                if ($level >= 1) {
                    $condition.=")";
                }
            } else {
                if ($olevel == 1) {
                    $condition.=' and  o.agentid=' . intval($_GPC['agentid']);
                } else if ($olevel == 2) {
                    if ($agent['level2'] > 0) {
                        $condition.= " and o.agentid in( " . implode(',', array_keys($agent['level1_agentids'])) . ")";
                    } else {
                        $condition.= " and o.agentid in( 0 )";
                    }
                } else if ($olevel == 3) {
                    if ($agent['level3'] > 0) {
                        $condition.= " and o.agentid in( " . implode(',', array_keys($agent['level2_agentids'])) . ")";
                    } else {
                        $condition.= " and o.agentid in( 0 )";
                    }
                }
            }
        }
        //团长分红
        $heads = intval($_GPC['headsid']);  //团长id
        $dividend = p('dividend');  //插件权限
        if($dividend && !empty($heads)){
            $condition .= " and o.headsid = ".$heads;
        }
        //扶植分红
        if($plugin_mentor){
            if(!empty($mentor_id)) $condition .= " and o.mentor_id = ".$mentor_id;
            $mentorSet = $plugin_mentor->getSet();
        }
        
        $authorid = intval($_GPC['authorid']);
        $author = p('author');
        if ($author && !empty($authorid)){
            $condition.= " and o.authorid = :authorid";
            $paras[':authorid'] = $authorid;
        }

        //虚店店员        
        $clerkSet_level = 0;
        if ($plugin_clerk) {
            //$clerk_id = intval($_GPC['clerk_id']);//订单推广者ID
            $clerkSet = $plugin_clerk->getSet();
            $clerkSet_level = intval($clerkSet['level']);
        }
        $oclerklevel = intval($_GPC['oclerklevel']);
        if (!empty($clerk_id) && $clerkSet_level > 0) {
            //显示三级订单
            $clerk = $plugin_clerk->getInfo($clerk_id, array());
            if (!empty($clerk)) {
                $clerkLevel = app(ClerkLevelLogic::class)->getClerkLevel($clerk['clerk_level']);
            }
            if (empty($oclerklevel)) {
                if ($clerkSet_level >= 1) {
                    $condition.=' and  ( o.clerk_id=' . intval($_GPC['clerk_id']);
                }
                if($clerkSet['selfbuy'] == 1){
                    //开启内购
                    if ($clerkSet_level >= 2 && $clerk['level1'] > 0) {
                        $condition.= " or o.clerk_id in( " . implode(',', array_keys($clerk['level1_clerk_ids'])) . ")";
                    }
                    if ($clerkSet_level >= 3 && $clerk['level2'] > 0) {
                        $condition.= " or o.clerk_id in( " . implode(',', array_keys($clerk['level2_clerk_ids'])) . ")";
                    }
                }else{
                    //不开启内购
                    if ($clerkSet_level >= 2 && $clerk['level2'] > 0) {
                        $condition.= " or o.clerk_id in( " . implode(',', array_keys($clerk['level1_clerk_ids'])) . ")";
                    }
                    if ($clerkSet_level >= 3 && $clerk['level3'] > 0) {
                        $condition.= " or o.clerk_id in( " . implode(',', array_keys($clerk['level2_clerk_ids'])) . ")";
                    }
                    
                }
                if ($clerkSet_level >= 1) {
                    $condition.=")";
                }
            } else {
                if ($oclerklevel == 1) {
                    $condition.=' and  o.clerk_id=' . intval($_GPC['clerk_id']);
                } else if ($oclerklevel == 2) {
                    if ($clerk['level2'] > 0) {
                        $condition.= " and o.clerk_id in( " . implode(',', array_keys($clerk['level1_clerk_ids'])) . ")";
                    } else {
                        $condition.= " and o.clerk_id in( 0 )";
                    }
                } else if ($oclerklevel == 3) {
                    if ($clerk['level3'] > 0) {
                        $condition.= " and o.clerk_id in( " . implode(',', array_keys($clerk['level2_clerk_ids'])) . ")";
                    } else {
                        $condition.= " and o.clerk_id in( 0 )";
                    }
                }
            }
        }

        //医生        
        $doctorSet_level = 0;
        if ($plugin_doctor) {
            //$doctor_id = intval($_GPC['doctor_id']);//订单医生ID
            $doctorSet = $plugin_doctor->getSet();
            $doctorSet_level = intval($doctorSet['level']);
        }
        $odoctorlevel = intval($_GPC['odoctorlevel']);
        if (!empty($doctor_id) && $doctorSet_level > 0) {
            //显示三级订单
            $doctor = $plugin_doctor->getInfo($doctor_id, array());
            if (!empty($doctor)) {
                $doctorLevel = $plugin_doctor->getLevel($doctor_id);
            }
            if (empty($odoctorlevel)) {
                if ($doctorSet_level >= 1) {
                    $condition.=' and  ( o.doctor_id=' . intval($_GPC['doctor_id']);
                }
                if($doctorSet['selfbuy'] == 1){
                    //开启内购
                    if ($doctorSet_level >= 2 && $doctor['level1'] > 0) {
                        $condition.= " or o.doctor_id in( " . implode(',', array_keys($doctor['level1_doctor_ids'])) . ")";
                    }
                    if ($doctorSet_level >= 3 && $doctor['level2'] > 0) {
                        $condition.= " or o.doctor_id in( " . implode(',', array_keys($doctor['level2_doctor_ids'])) . ")";
                    }
                }else{
                    //不开启内购
                    if ($doctorSet_level >= 2 && $doctor['level2'] > 0) {
                        $condition.= " or o.doctor_id in( " . implode(',', array_keys($doctor['level1_doctor_ids'])) . ")";
                    }
                    if ($doctorSet_level >= 3 && $doctor['level3'] > 0) {
                        $condition.= " or o.doctor_id in( " . implode(',', array_keys($doctor['level2_doctor_ids'])) . ")";
                    }
                    
                }
                if ($doctorSet_level >= 1) {
                    $condition.=")";
                }
            } else {
                if ($odoctorlevel == 1) {
                    $condition.=' and  o.doctor_id=' . intval($_GPC['doctor_id']);
                } else if ($odoctorlevel == 2) {
                    if ($doctor['level2'] > 0) {
                        $condition.= " and o.doctor_id in( " . implode(',', array_keys($doctor['level1_doctor_ids'])) . ")";
                    } else {
                        $condition.= " and o.doctor_id in( 0 )";
                    }
                } else if ($odoctorlevel == 3) {
                    if ($doctor['level3'] > 0) {
                        $condition.= " and o.doctor_id in( " . implode(',', array_keys($doctor['level2_doctor_ids'])) . ")";
                    } else {
                        $condition.= " and o.doctor_id in( 0 )";
                    }
                }
            }
        }

        //虚店店长        
        $ownerSet_level = 0;
        if ($plugin_owner) {
            //$clerk_id = intval($_GPC['clerk_id']);//订单推广者ID
            $ownerSet = $plugin_owner->getSet();
            $ownerSet_level = intval($ownerSet['level']);
        }
        $oownerlevel = intval($_GPC['oownerlevel']);
        if (!empty($owner_id) && $ownerSet_level > 0) {
            //显示三级订单
            $owner = $plugin_owner->getInfo($owner_id, array());
            if (!empty($owner)) {
                $ownerLevel = $plugin_owner->getLevel($owner_id);
            }
            if (empty($oownerlevel)) {
                if ($ownerSet_level >= 1) {
                    $condition.=' and  ( o.owner_id=' . intval($_GPC['owner_id']);
                }
                if($ownerSet['selfbuy'] == 1){
                    //开启内购
                    if ($ownerSet_level >= 2 && $owner['level1'] > 0) {
                        $condition.= " or o.owner_id in( " . implode(',', array_keys($owner['level1_owner_ids'])) . ")";
                    }
                    if ($ownerSet_level >= 3 && $owner['level2'] > 0) {
                        $condition.= " or o.owner_id in( " . implode(',', array_keys($owner['level2_owner_ids'])) . ")";
                    }
                }else{
                    //不开启内购
                    if ($ownerSet_level >= 2 && $owner['level2'] > 0) {
                        $condition.= " or o.owner_id in( " . implode(',', array_keys($owner['level1_owner_ids'])) . ")";
                    }
                    if ($ownerSet_level >= 3 && $owner['level3'] > 0) {
                        $condition.= " or o.owner_id in( " . implode(',', array_keys($owner['level2_owner_ids'])) . ")";
                    }
                    
                }
                if ($ownerSet_level >= 1) {
                    $condition.=")";
                }
            } else {
                if ($oownerlevel == 1) {
                    $condition.=' and  o.owner_id=' . intval($_GPC['owner_id']);
                } else if ($oownerlevel == 2) {
                    if ($owner['level2'] > 0) {
                        $condition.= " and o.owner_id in( " . implode(',', array_keys($owner['level1_owner_ids'])) . ")";
                    } else {
                        $condition.= " and o.owner_id in( 0 )";
                    }
                } else if ($oownerlevel == 3) {
                    if ($owner['level3'] > 0) {
                        $condition.= " and o.owner_id in( " . implode(',', array_keys($owner['level2_owner_ids'])) . ")";
                    } else {
                        $condition.= " and o.owner_id in( 0 )";
                    }
                }
            }
        }

        //虚店合伙人        
        $copartnerSet_level = 0;
        if ($plugin_copartner) {
            //$clerk_id = intval($_GPC['copartner_id']);//订单推广者ID
            $copartnerSet = $plugin_copartner->getSet();
            $copartnerSet_level = intval($copartnerSet['level']);
        }
        $ocopartnerlevel = intval($_GPC['ocopartnerlevel']);
        if (!empty($copartner_id) && $copartnerSet_level > 0) {
            //显示三级订单
            $copartner = $plugin_copartner->getInfo($copartner_id, array());
            if (!empty($copartner)) {
                $copartnerLevel = $plugin_copartner->getLevel($copartner_id);
            }
            if (empty($ocopartnerlevel)) {
                if ($copartnerSet_level >= 1) {
                    $condition.=' and  ( o.copartner_id=' . intval($_GPC['copartner_id']);
                }
                if($copartnerSet['selfbuy'] == 1){
                    //开启内购
                    if ($copartnerSet_level >= 2 && $copartner['level1'] > 0) {
                        $condition.= " or o.copartner_id in( " . implode(',', array_keys($copartner['level1_copartner_ids'])) . ")";
                    }
                    if ($copartnerSet_level >= 3 && $copartner['level2'] > 0) {
                        $condition.= " or o.copartner_id in( " . implode(',', array_keys($copartner['level2_copartner_ids'])) . ")";
                    }
                }else{
                    //不开启内购
                    if ($copartnerSet_level >= 2 && $copartner['level2'] > 0) {
                        $condition.= " or o.copartner_id in( " . implode(',', array_keys($copartner['level1_copartner_ids'])) . ")";
                    }
                    if ($copartnerSet_level >= 3 && $copartner['level3'] > 0) {
                        $condition.= " or o.copartner_id in( " . implode(',', array_keys($copartner['level2_copartner_ids'])) . ")";
                    }
                    
                }
                if ($copartnerSet_level >= 1) {
                    $condition.=")";
                }
            } else {
                if ($ocopartnerlevel == 1) {
                    $condition.=' and  o.copartner_id=' . intval($_GPC['copartner_id']);
                } else if ($ocopartnerlevel == 2) {
                    if ($copartner['level2'] > 0) {
                        $condition.= " and o.copartner_id in( " . implode(',', array_keys($copartner['level1_copartner_ids'])) . ")";
                    } else {
                        $condition.= " and o.copartner_id in( 0 )";
                    }
                } else if ($ocopartnerlevel == 3) {
                    if ($copartner['level3'] > 0) {
                        $condition.= " and o.copartner_id in( " . implode(',', array_keys($copartner['level2_copartner_ids'])) . ")";
                    } else {
                        $condition.= " and o.copartner_id in( 0 )";
                    }
                }
            }
        }
        
        if ($condition != ' o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0 and o.istrade=0 ' || !empty($sqlcondition)){
            $cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER, 'o');

            //查询语句优化 lgt+
            if($searchfield == 'member') {
                $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'],ENT_QUOTES);
                if(empty($_GPC['isprecise'])) {
                    $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0)";
                }else{
                    $condition .= " AND (m.realname=:keyword or m.mobile=:keyword or m.nickname=:keyword)";
                }
                
                
                $sql = "select distinct o.* , a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                      d.dispatchname,m.nickname,m.id as mid,m.realname as mrealname,m.mobile as mmobile,m.uid,m.groupid,
                      r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
            } else if($searchfield == 'saler'){
                $sql = "select distinct o.* ,m.groupid, a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                      d.dispatchname,
                      r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid = o.openid and m.uniacid=o.uniacid"
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " left join " . tablename('elapp_shop_verifygoods') . " vg on vg.orderid = o.id "
                    . " left join " . tablename('elapp_shop_verifygoods_log') . " vgl on vgl.verifygoodsid = vg.id"
                    . " left join " . tablename('elapp_shop_verifyorder_log') . " vol on vol.orderid=o.id "
                    . " left join " . tablename('elapp_shop_saler') . " s on (s.id = vgl.salerid or s.id=vol.salerid or s.openid=o.verifyopenid) and s.uniacid=o.uniacid and o.ismerch=0"
                    . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                    . " left join " . tablename('elapp_shop_merch_saler') . " ms on (ms.id = vgl.salerid or ms.id=vol.salerid or ms.openid=o.verifyopenid) and ms.uniacid=o.uniacid and o.ismerch=1"
                    . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
            } else if($searchfield == 'store'){
                $sql = "select distinct o.* ,m.groupid, a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                      d.dispatchname,
                      r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid = o.openid and m.uniacid=o.uniacid"
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " left join " . tablename('elapp_shop_verifygoods') . " vg on vg.orderid = o.id "
                    . " left join " . tablename('elapp_shop_verifygoods_log') . " vgl on vgl.verifygoodsid = vg.id"
                    . " left join " . tablename('elapp_shop_verifyorder_log') . " vol on vol.orderid=o.id "
                    . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
            } else if($searchfield == 'mid'){
                $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'],ENT_QUOTES);
                $condition .= " AND (m.id=:keyword)";
                
                $sql = "select distinct o.* , a.realname as arealname,a.mobile as amobile,o.merchdeductenough,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                      d.dispatchname,m.nickname,m.id as mid,m.groupid,m.realname as mrealname,m.mobile as mmobile,m.uid,
                      r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
            } else if($searchfield == 'clerk') {
                if ($plugin_clerk) {
                    $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'],ENT_QUOTES);
                    if(empty($_GPC['isprecise'])) {
                        $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0)";
                    }else{
                        $condition .= " AND (m.realname=:keyword or m.mobile=:keyword or m.nickname=:keyword)";
                    }                    
                    $sql = "select distinct o.* , a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                        d.dispatchname,m.nickname,m.id as mid,m.realname as mrealname,m.mobile as mmobile,m.uid,m.groupid,
                        r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                        . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                        . " left join " . tablename('elapp_shop_member') . " m on m.id=o.clerk_id and m.uniacid =  o.uniacid "
                        . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                        . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                        . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
                }
                
            } else if($searchfield == 'doctor') {
                if ($plugin_doctor) {
                    $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'],ENT_QUOTES);
                    if(empty($_GPC['isprecise'])) {
                        $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0)";
                    }else{
                        $condition .= " AND (m.realname=:keyword or m.mobile=:keyword or m.nickname=:keyword)";
                    }                    
                    $sql = "select distinct o.* , a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                        d.dispatchname,m.nickname,m.id as mid,m.realname as mrealname,m.mobile as mmobile,m.uid,m.groupid,
                        r.rtype,r.status as rstatus,o.sendtype,o.city_express_state from " . tablename('elapp_shop_order') . " o"
                        . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                        . " left join " . tablename('elapp_shop_member') . " m on m.id=o.doctor_id and m.uniacid =  o.uniacid "
                        . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                        . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                        . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC ";
                }
                
            } else {
                //Hlei 黄小勇 优化SQL
                $sql = "select  o.* ,m.groupid,m.level,a.realname as arealname,o.merchdeductenough,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress,
                  r.rtype,r.status as rstatus,o.sendtype,o.city_express_state,m.avatar,(select ifnull(state, 0) from " .tablename('elapp_shop_express_cache') ." e where e.expresssn = o.expresssn )  as express_status from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join "  . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid " . " $sqlcondition where $condition $statuscondition $cond ORDER BY $orderbuy DESC  ";
            }
            $sql.="LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            $list = pdo_fetchall($sql, $paras);
        }else{
            $cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER);
            $member_cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_MEMBER);
            $status_condition = str_replace('o.','',$statuscondition);
            $sql = "select * from " . tablename('elapp_shop_order') . " where uniacid = :uniacid and ismr=0 and deleted=0 and isparent=0 {$status_condition} $cond GROUP BY id ORDER BY createtime DESC  ";
            $sql.="LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            $list = pdo_fetchall($sql, $paras);
            if (!empty($list)){
                $refundid = '';
                $openid = '';
                $addressid = '';
                $dispatchid = '';
                $verifyopenid = '';
                foreach ($list as $key=>$value){
                    $refundid .= ",'{$value['refundid']}'";
                    $openid .= ",'{$value['openid']}'";
                    $addressid .= ",'{$value['addressid']}'";
                    $dispatchid .= ",'{$value['dispatchid']}'";
                    $verifyopenid .= ",'{$value['verifyopenid']}'";
                }
                $refundid = ltrim($refundid,',');
                $openid = ltrim($openid,',');
                $addressid = ltrim($addressid,',');
                $dispatchid = ltrim($dispatchid,',');
                $verifyopenid = ltrim($verifyopenid,',');
                $refundid_array = pdo_fetchall("SELECT id,rtype,status as rstatus FROM " .tablename('elapp_shop_order_refund')." WHERE id IN ({$refundid})",array(),'id');
                $openid_array = pdo_fetchall("SELECT openid,nickname,id,groupid as mid,level,realname as mrealname,uid,mobile as mmobile FROM " .tablename('elapp_shop_member')." WHERE openid IN ({$openid}) AND uniacid={$_W['uniacid']} $member_cond",array(),'openid');
                $addressid_array = pdo_fetchall("SELECT id,realname as arealname,mobile as amobile,province as aprovince ,city as acity , area as aarea,address as aaddress FROM " .tablename('elapp_shop_member_address')." WHERE id IN ({$addressid})",array(),'id');
                $dispatchid_array = pdo_fetchall("SELECT id,dispatchname FROM " .tablename('elapp_shop_dispatch')." WHERE id IN ({$dispatchid})",array(),'id');
                $verifyopenid_array = pdo_fetchall("SELECT m.id as salerid,m.nickname as salernickname,m.openid,s.salername FROM " .tablename('elapp_shop_saler')." s LEFT JOIN ".tablename('elapp_shop_member')." m ON m.openid = s.openid and m.uniacid=s.uniacid WHERE s.openid IN ({$verifyopenid})",array(),'openid');
                foreach ($list as $key=>&$value){
                    $list[$key]['rtype'] = $refundid_array[$value['refundid']]['rtype'];
                    $list[$key]['rstatus'] = $refundid_array[$value['refundid']]['rstatus'];
                    $list[$key]['nickname'] = $openid_array[$value['openid']]['nickname'];
                    $list[$key]['mid'] = $openid_array[$value['openid']]['id'];
                    $list[$key]['uid'] = $openid_array[$value['openid']]['uid'];
                    $list[$key]['mrealname'] = $openid_array[$value['openid']]['mrealname'];
                    $list[$key]['mmobile'] = $openid_array[$value['openid']]['mmobile'];
                    $list[$key]['groupid'] = $openid_array[$value['openid']]['groupid'];
		            $list[$key]['level'] = $openid_array[$value['openid']]['level'];
                    $list[$key]['mmobile'] = $openid_array[$value['openid']]['mmobile'];
                    $list[$key]['arealname'] = $addressid_array[$value['addressid']]['arealname'];
                    $list[$key]['amobile'] = $addressid_array[$value['addressid']]['amobile'];
                    $list[$key]['aprovince'] = $addressid_array[$value['addressid']]['aprovince'];
                    $list[$key]['acity'] = $addressid_array[$value['addressid']]['acity'];
                    $list[$key]['aarea'] = $addressid_array[$value['addressid']]['aarea'];
                    $list[$key]['astreet'] = $addressid_array[$value['addressid']]['astreet'];
                    $list[$key]['aaddress'] = $addressid_array[$value['addressid']]['aaddress'];
                    $list[$key]['dispatchname'] = $dispatchid_array[$value['dispatchid']]['dispatchname'];
                    $list[$key]['salerid'] = $verifyopenid_array[$value['verifyopenid']]['salerid'];
                    $list[$key]['salernickname'] = $verifyopenid_array[$value['verifyopenid']]['salernickname'];
                    $list[$key]['salername'] = $verifyopenid_array[$value['verifyopenid']]['salername'];
                }
                unset($value);
            }
        }
        
        $gdata = pdo_fetchall("select * from " . tablename('elapp_shop_member_group') . " where uniacid = $uniacid");
	    $ldata = pdo_fetchall("select * from " . tablename("elapp_shop_member_level") . " where uniacid = " . $uniacid);

        $paytype = array(
            '0' => array('css' => 'default', 'name' => '未支付'),
            '1' => array('css' => 'danger', 'name' => '余额支付'),
            '11' => array('css' => 'default', 'name' => '后台付款'),
            '2' => array('css' => 'danger', 'name' => '在线支付'),
            '21' => array('css' => 'success', 'name' => '微信支付'),
            '22' => array('css' => 'warning', 'name' => '支付宝支付'),
            '23' => array('css' => 'warning', 'name' => '银联支付'),
            '3' => array('css' => 'primary', 'name' => '货到付款'),
            '4' => array('css' => 'primary', 'name' => '收银台现金收款'), //当支付方式为3并且是收银台订单时是现金收款
        );
        $orderstatus = array(
            '-1' => array('css' => 'default', 'name' => '已关闭'),
            '0' => array('css' => 'danger', 'name' => '待付款'),
            '1' => array('css' => 'info', 'name' => '待发货'),
            '2' => array('css' => 'warning', 'name' => '待收货'),
            '3' => array('css' => 'success', 'name' => '已完成')
        );

        $expressstatus = array(
            '0' => array('css' => 'primary', 'name' => '暂无轨迹信息'),
            '1' => array('css' => 'info', 'name' => '已揽收'),
            '2' => array('css' => 'warning', 'name' => '在途中'),
            '3' => array('css' => 'success', 'name' => '已签收'),
            '4' => array('css' => 'warning', 'name' => '问题件'),
            '5' => array('css' => 'warning', 'name' => '转寄'),
            '6' => array('css' => 'success', 'name' => '清关')
        );

        //多商户
        $is_merch = array();
        $is_merchname = 0;
        if ($merch_plugin) {
            $merch_user = $merch_plugin->getListUser($list,'merch_user');
            if (!empty($merch_user)) {
                $is_merchname = 1;
            }
            
        }
        //供应商 Hlei 20220317
        $is_supply = array();
        $is_supplyname = 0;
        if ($supply_plugin) {
            $supply_user = $supply_plugin->getListUser($list,'supply_user');
            if (!empty($supply_user)) {
                $is_supplyname = 1;
            }
            
        }
        
        if (!empty($list)) {
            $diy_title_data = array();
            $diy_data = array();
            $openids = array(); // 会员openids
            $verifyopenids = array();
            
            $temp_refund = array(); // 临时维权订单ids
            foreach ($list as $key => &$value) {
                $openids[] = $value['openid'];
                $verifyopenids[] = $value['verifyopenid'];
                if ($is_merchname == 1) {
                    $value['merchname'] = $merch_user[$value['merchid']]['merchname'] ? $merch_user[$value['merchid']]['merchname'] : '';
                }
                //供应商 Hlei 20220317
                if ($is_supplyname == 1) {
                    $value['supplyname'] = $supply_user[$value['supplyid']]['supplyname'] ? $supply_user[$value['supplyid']]['supplyname'] : '';
                }
                $value['status_id'] = $value['status'];
                $s = $value['status'];
                $pt = $value['paytype'];
                
                $value['statusvalue'] = $s;
                $value['statuscss'] = $orderstatus[$value['status']]['css'];
                $value['oldstatus'] = $value['status'];
                $value['status'] = $orderstatus[$value['status']]['name'];
                if(empty($value['express_status'])){
                    $value['express_status'] = 0;
                }
                $value['express_status_name'] = $expressstatus[$value['express_status']]['name'];
                $value['express_status_css'] = $expressstatus[$value['express_status']]['css'];
                if ($pt == 3 && empty($value['statusvalue'])) {
                    $value['statuscss'] = $orderstatus[1]['css'];
                    $value['status'] = $orderstatus[1]['name'];
                }
                if ($s == 1) {
                    if ($value['isverify'] == 1) {
                        $value['status'] = "待使用";
                        if($value['sendtype']>0){
                            $value['status'] = "部分使用";
                        }
                    } else if (empty($value['addressid'])) {
                        $value['status'] = "待取货";
                    }else{
                        if($value['sendtype']>0){
                            $value['status'] = "部分发货";
                        }
                    }
                }
                
                if ($s == -1) {
                    if (!empty($value['refundtime'])) {
                        $value['status'] = '已维权';
                    }
                }
                
                if(!empty($value['refundid']) && !empty($_GPC['export'])){
                    $temp_refund[] =  $value['refundid'];
                }
                $value['applyprice'] = 0;
                $value['has_refunded'] = false;
                $value['order_refund_status'] = false;
                
                $value['paytypevalue'] = $pt;
                $value['css'] = $paytype[$pt]['css'];
                $value['paytype'] = $paytype[$pt]['name'];
                $value['dispatchname'] = empty($value['addressid']) ? (empty($value['storeid']) ? '缺少配送方式': '自提') : $value['dispatchname'];
                if (empty($value['dispatchname'])) {
                    $value['dispatchname'] = '快递';
                }
                
                if($value['city_express_state']==1){
                    $value['dispatchname'] = '同城配送';
                }
                if ($pt == 3) {
                    $value['dispatchname'] = "货到付款";
                } else if ($value['isverify'] == 1) {
                    $value['dispatchname'] = "线下核销";
                } else if ($value['isvirtual'] == 1) {
                    $value['dispatchname'] = "虚拟物品";
                } else if (!empty($value['virtual'])) {
                    $value['dispatchname'] = "虚拟物品(卡密)<br/>自动发货";
                }
                $isonlyverifygoods = m('order')->checkisonlyverifygoods($value['id']);
                if($isonlyverifygoods){
                    $value['dispatchname'] = "记次/时商品";
                }
                
                if ($value['dispatchtype'] == 1 || !empty($value['isverify']) || !empty($value['virtual']) || !empty($value['isvirtual'])) {
                    $value['address'] = '';
                    $carrier = iunserializer($value['carrier']);
                    if (is_array($carrier)) {
                        $value['addressdata']['realname'] = $value['realname'] = $carrier['carrier_realname'];
                        $value['addressdata']['mobile'] = $value['mobile'] = $carrier['carrier_mobile'];
                    }
                } else {
                    
                    
                    $address = iunserializer($value['address']);
                    $isarray = is_array($address);
                    
                    
                    $value['realname'] = $isarray ? $address['realname'] : $value['arealname'];
                    $value['mobile'] = $isarray ? $address['mobile'] : $value['amobile'];
                    $value['province'] = $isarray ? $address['province'] : $value['aprovince'];
                    $value['city'] = $isarray ? $address['city'] : $value['acity'];
                    $value['area'] = $isarray ? $address['area'] : $value['aarea'];
                    $value['street'] = $isarray ? $address['street'] : $value['astreet'];
                    $value['address'] = $isarray ? $address['address'] : $value['aaddress'];
                    
                    $value['address_province'] = $value['province'];
                    $value['address_city'] = $value['city'];
                    $value['address_area'] = $value['area'];
                    $value['address_street'] = $value['street'];
                    $value['address_address'] = $value['address'];
                    
                    $value['address_str'] = $value['province']." ".$value['city']." ".$value['area']." ".$value['street']." ".$value['address'];
                    $value['address'] = $value['province'] . " " . $value['city'] . " " . $value['area'] . " " . $value['address'];
                    $value['addressdata'] = array(
                        'realname' => $value['realname'],
                        'mobile' => $value['mobile'],
                        'address' => $value['address'],
                    );
                }

                //分销等级
                $commission1 = -1;
                $commission2 = -1;
                $commission3 = -1;
                $m1 = false;
                $m2 = false;
                $m3 = false;
                if (!empty($level) && empty($agentid)) {                    
                    if (!empty($value['agentid'])) {
                        $m1 = m('member')->getMember($value['agentid']);
                        $commission1 = 0;
                        if (!empty($m1['agentid']) && $level>1) {
                            $m2 = m('member')->getMember($m1['agentid']);
                            $commission2 = 0;
                            if (!empty($m2['agentid']) && $level>2) {
                                $m3 = m('member')->getMember($m2['agentid']);
                                $commission3 = 0;
                            }
                        }
                    }
                }
                
                if (!empty($agentid)) {
                    $magent = m('member')->getMember($agentid);
                }

                //虚店店员等级
                $clerkCommission1 = -1;
                $clerkCommission2 = -1;
                $clerkCommission3 = -1;
                $clerkm1 = false;
                $clerkm2 = false;
                $clerkm3 = false;
                if (!empty($clerkSet_level)) {                    
                    if (!empty($value['clerk_id'])) {
                        $clerkm1 = m('member')->getMember($value['clerk_id']);
                        $clerkCommission1 = 0;                        
                        if(!empty($clerkm1)){
                            $value['clerk'] = $clerkm1;
                            $is_clerk = $plugin_clerk->isClerk($clerkm1['openid'],true);
                            if($is_clerk){
                                $value['is_clerk'] = $is_clerk;
                                $value['clerkRealname'] = $clerkm1['realname'];
                                $value['clerkMID'] = $clerkm1['id'];
                                //店员等级
                                $clerkLevel = app(ClerkLevelLogic::class)->getClerkLevel($clerkm1['clerk_level']);
                                $value['clerkLevelname'] = $clerkLevel['levelname'] ? $clerkLevel['levelname'] : (empty($_W['shopset']['clerk']['levelname'])?'默认等级':$_W['shopset']['clerk']['levelname']);

                            }
                        }
                        
                        if (!empty($clerkm1['clerk_id']) && $clerkSet_level>1) {
                            $clerkm2 = m('member')->getMember($clerkm1['clerk_id']);
                            $clerkCommission2 = 0;
                            if (!empty($clerkm2['clerk_id']) && $clerkSet_level>2) {
                                $clerkm3 = m('member')->getMember($clerkm2['clerk_id']);
                                $clerkCommission3 = 0;
                            }
                        }
                    }
                }
                
                if (!empty($clerk_id)) {
                    $mclerk = m('member')->getMember($clerk_id);
                }

                //医生等级
                $doctorCommission1 = -1;
                $doctorCommission2 = -1;
                $doctorCommission3 = -1;
                $doctorm1 = false;
                $doctorm2 = false;
                $doctorm3 = false;
                if (!empty($doctorSet_level)) {                    
                    if (!empty($value['doctor_id'])) {
                        $doctorm1 = m('member')->getMember($value['doctor_id']);
                        $doctorCommission1 = 0;                        
                        if(!empty($doctorm1)){
                            $value['doctor'] = $doctorm1;
                            $isDoctor = $plugin_doctor->isDoctor($doctorm1['openid'],true);                            
                            if($isDoctor){
                                $value['isDoctor'] = $isDoctor;
                                $value['doctorRealname'] = $doctorm1['realname'];
                                $value['doctorMID'] = $doctorm1['id'];
                                //店员等级
                                $doctorLevel = $plugin_doctor->getLevel($doctorm1['openid']);
                                $value['doctorLevelname'] = $doctorLevel['levelname'] ? $doctorLevel['levelname'] : (empty($_W['shopset']['doctor']['levelname'])?'默认等级':$_W['shopset']['doctor']['levelname']);

                            }
                        }
                        
                        if (!empty($doctorm1['doctor_id']) && $doctorSet_level>1) {
                            $doctorm2 = m('member')->getMember($doctorm1['doctor_id']);
                            $doctorCommission2 = 0;
                            if (!empty($doctorm2['doctor_id']) && $doctorSet_level>2) {
                                $doctorm3 = m('member')->getMember($doctorm2['doctor_id']);
                                $doctorCommission3 = 0;
                            }
                        }
                    }
                }
                
                if (!empty($doctor_id)) {
                    $mdoctor = m('member')->getMember($doctor_id);
                }

                //虚店店长等级
                $ownerCommission1 = -1;
                $ownerCommission2 = -1;
                $ownerCommission3 = -1;
                $ownerm1 = false;
                $ownerm2 = false;
                $ownerm3 = false;
                if (!empty($ownerSet_level)) {                    
                    if (!empty($value['owner_id'])) {
                        $ownerm1 = m('member')->getMember($value['owner_id']);
                        $ownerCommission1 = 0;                        
                        if(!empty($ownerm1)){
                            $value['owner'] = $ownerm1;
                            $isOwner = $plugin_owner->isOwner($ownerm1['openid'],true);                            
                            if($isOwner){
                                $value['isOwner'] = $isOwner;
                                $value['ownerRealname'] = $ownerm1['realname'];
                                $value['ownerMID'] = $ownerm1['id'];
                            }
                        }
                        
                        if (!empty($ownerm1['owner_id']) && $ownerSet_level>1) {
                            $ownerm2 = m('member')->getMember($ownerm1['owner_id']);
                            $ownerCommission2 = 0;
                            if (!empty($ownerm2['owner_id']) && $ownerSet_level>2) {
                                $ownerm3 = m('member')->getMember($ownerm2['owner_id']);
                                $ownerCommission3 = 0;
                            }
                        }
                    }
                }
                
                if (!empty($owner_id)) {
                    $mowner = m('member')->getMember($owner_id);
                }

                //帮扶分红
                $mentorCommission = -1;
                $mentorMember = false;
                if (!empty($mentorSet['open'])) {
                    if (!empty($value['mentor_id']) || !empty($value['copartner_mentor_id'])) {
                        $mentorMember = m('member')->getMember($value['mentor_id']);
                        $mentorCommission = 0;
                        if(!empty($mentorMember)){
                            $value['mentorMember'] = $mentorMember;
                            $is_mentor = $plugin_mentor->ismentor($mentorMember['openid'],true);
                            if($is_mentor){
                                $value['is_mentor'] = $is_mentor;
                                $value['mentorRealname'] = $mentorMember['realname'];
                                $value['mentorMID'] = $mentorMember['id'];
                                //帮持人的店员等级
                                $mentor_clerkLevel = app(ClerkLevelLogic::class)->getClerkLevel($mentorMember['clerk_level']);
                                $value['mentor_clerkLevelname'] = $mentor_clerkLevel['levelname'] ? $mentor_clerkLevel['levelname'] : (empty($_W['shopset']['clerk']['levelname'])?'默认等级':$_W['shopset']['clerk']['levelname']);
                            }                            
                        }
                        //分红数据
                        if (!empty($value["mentor"])) {
                            $mentorCommissions = iunserializer($value["mentor"]);
                            if (!empty($mentorCommissions)) {
                                $value["mentorCommission"] = isset($mentorCommissions["dividend_price"]) ? floatval($mentorCommissions["dividend_price"]) : 0;
                                $value["mentorCommission_ratio"] = isset($mentorCommissions["dividend_ratio"]) ? floatval($mentorCommissions["dividend_ratio"]) : 0;
                                $value["copartnerMentorCommission"] = isset($mentorCommissions["copartner_dividend_price"]) ? floatval($mentorCommissions["copartner_dividend_price"]) : 0;
                                $value["copartnerMentorCommission_ratio"] = isset($mentorCommissions["copartner_dividend_ratio"]) ? floatval($mentorCommissions["copartner_dividend_ratio"]) : 0;
                            }
                        }
                    }
                }
                if (!empty($mentor_id)) {
                    $mmentor = m('member')->getMember($mentor_id);
                }

                //虚店合伙人等级
                $copartnerCommission1 = -1;
                $copartnerCommission2 = -1;
                $copartnerCommission3 = -1;
                $copartnerm1 = false;
                $copartnerm2 = false;
                $copartnerm3 = false;
                if (!empty($copartnerSet_level)) {                    
                    if (!empty($value['copartner_id'])) {
                        $copartnerm1 = $plugin_copartner->getCopartnerInfo($value['copartner_id']);
                        $copartnerCommission1 = 0;                        
                        if(!empty($copartnerm1)){
                            $value['copartner'] = $copartnerm1;
                            $isCopartner = $plugin_copartner->isCopartner($copartnerm1['id'],true);                            
                            if($isCopartner){
                                $value['isCopartner'] = $isCopartner;
                                $value['copartnerMcnname'] = $copartnerm1['mcnname'];
                                $value['copartnerID'] = $copartnerm1['id'];
                                //机构等级
                                $copartnerLevel = $plugin_copartner->getLevel($copartnerm1['id']);
                                $value['copartnerLevelname'] = $copartnerLevel['levelname'] ? $copartnerLevel['levelname'] : (empty($_W['shopset']['copartner']['levelname'])?'默认等级':$_W['shopset']['copartner']['levelname']);
                            }
                        }
                        
                        if (!empty($copartnerm1['copartner_id']) && $copartnerSet_level>1) {
                            $copartnerm2 = m('member')->getMember($copartnerm1['copartner_id']);
                            $copartnerCommission2 = 0;
                            if (!empty($copartnerm2['copartner_id']) && $copartnerSet_level>2) {
                                $copartnerm3 = m('member')->getMember($copartnerm2['copartner_id']);
                                $copartnerCommission3 = 0;
                            }
                        }

                        //机构员工 isCopartnerAccount
                        if (!empty($value['copartner_account_id'])) {
                            //员工帐号信息
                            $copartnerAccount = $plugin_copartner->isCopartnerAccount($value['copartner_account_id']);
                            if ($copartnerAccount) $value['copartnerAccount'] = $copartnerAccount;
                            //机构员工会员member信息
                            $value['copartnerAccount']['member'] = m('member')->getMember($copartnerAccount['openid']);
                            $value['copartnerAccountName'] = $value['copartnerAccount']['member']['realname'];
                        }
                    }
                }
                
                if (!empty($copartner_id)) {
                    $mcopartner = m('member')->getMember($copartner_id);
                }

                //订单商品
                $order_goods = pdo_fetchall('select op.id as option_id,og.fullbackid,op.fullbackprice,g.isfullback,g.id,g.title,og.title as gtitle,g.thumb,g.invoice,g.goodssn,og.goodssn as option_goodssn, g.productsn,og.productsn as option_productsn, og.total,
                    og.price,og.optionname as optiontitle, og.realprice,og.changeprice,og.oldprice,og.commission1,og.commission2,og.commission3,og.commissions,og.diyformdata,og.clerkCommission1,og.clerkCommission2,og.clerkCommission3,og.clerkCommissions,og.ownerCommission1,og.ownerCommission2,og.ownerCommission3,og.ownerCommissions,og.copartnerCommission1,og.copartnerCommission2,og.copartnerCommission3,og.copartnerCommissions,og.mentor_commission,og.mentor_commissions,og.doctor_commission,og.doctor_commissions,
                    og.diyformfields,op.specs,g.merchid,g.supplyid,g.isSupplySend,og.seckill,og.seckill_taskid,og.seckill_roomid,g.ispresell,g.costprice,op.costprice as option_costprice,og.expresssn,og.expresscom,og.express,og.sendtype,g.status as giftstatus,og.single_refundid,og.single_refundstate,og.id as ogid,og.nocommission,og.noClerkCommission,og.noOwnerCommission,og.noCopartnerCommission,g.goodsClassID,g.medicineClassID,g.medicineAttributeID,og.doctor_no_commission,og.pr_order_id from ' . tablename('elapp_shop_order_goods') . ' og '
                    . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                    . ' left join ' . tablename('elapp_shop_goods_option') . ' op on og.optionid = op.id '
                    . ' where og.uniacid=:uniacid and og.orderid=:orderid order by og.single_refundstate desc ', array(':uniacid' => $uniacid, ':orderid' => $value['id']));
                $goods = '';
                $is_singlerefund=false;//是否正在维权
                $is_singlerefund_success = false;
                foreach ($order_goods as &$og) {
                    if(!$is_singlerefund && ($og['single_refundstate']==1 ||$og['single_refundstate']==2)){
                        $is_singlerefund=true;//存在维权申请，需要处理后再发货
                    }
                    
                    $og['seckill_task'] = false;
                    $og['seckill_room'] = false;
                    if($og['seckill']){
                        $og['seckill_task']  = plugin_run('seckill::getTaskInfo',$og['seckill_taskid']);
                        $og['seckill_room']  = plugin_run('seckill::getRoomInfo',$og['seckill_taskid'],$og['seckill_roomid']);
                    }
                    //读取规格的图片
                    if (!empty($og['specs'])) {
                        $thumb = m('goods')->getSpecThumb($og['specs']);
                        if (!empty($thumb)) {
                            $og['thumb'] = $thumb;
                        }
                    }
                    
                    //读取规格的成本价
                    if (!empty($og['option_costprice'])) {
                        $og['costprice'] = $og['option_costprice'];
                    }
                    
                    //分销商多规格
                    if (!empty($level) && empty($agentid) && empty($og['nocommission']) ) {
                        $commissions = iunserializer($og['commissions']);
                        if (!empty($m1)) {
                            if (is_array($commissions)) {
                                $commission1+= isset($commissions['level1']) ? floatval($commissions['level1']) : 0;
                            } else {
                                $c1 = iunserializer($og['commission1']);
                                $l1 = $p->getLevel($m1['openid']);
                                
                                if (!empty($c1)) {
                                    $commission1+= isset($c1['level' . $l1['id']]) ? $c1['level' . $l1['id']] : $c1['default'];
                                }
                            }
                        }
                        if (!empty($m2)) {
                            if (is_array($commissions)) {
                                $commission2+= isset($commissions['level2']) ? floatval($commissions['level2']) : 0;
                            } else {
                                $c2 = iunserializer($og['commission2']);
                                $l2 = $p->getLevel($m2['openid']);
                                if (!empty($c2)) {
                                    $commission2+= isset($c2['level' . $l2['id']]) ? $c2['level' . $l2['id']] : $c2['default'];
                                }
                            }
                        }
                        if (!empty($m3)) {
                            if (is_array($commissions)) {
                                $commission3+= isset($commissions['level3']) ? floatval($commissions['level3']) : 0;
                            } else {
                                $c3 = iunserializer($og['commission3']);
                                $l3 = $p->getLevel($m3['openid']);
                                if (!empty($c3)) {
                                    $commission3+= isset($c3['level' . $l3['id']]) ? $c3['level' . $l3['id']] : $c3['default'];
                                }
                            }
                        }
                    }

                    //虚店店员多规格
                    if (!empty($clerkSet_level) && empty($og['noClerkCommission']) ) {
                        $clerkCommissions = iunserializer($og['clerkCommissions']);
                        if (!empty($clerkm1)) {
                            if (is_array($clerkCommissions)) {
                                $clerkCommission1+= isset($clerkCommissions['level1']) ? floatval($clerkCommissions['level1']) : 0;
                            } else {
                                $clerkc1 = iunserializer($og['clerkCommission1']);
                                $clerkl1 = app(ClerkLevelLogic::class)->getClerkLevel($clerkm1['clerk_level']);
                                
                                if (!empty($clerkc1)) {
                                    $clerkCommission1+= isset($clerkc1['level' . $clerkl1['id']]) ? $clerkc1['level' . $clerkl1['id']] : $clerkc1['default'];
                                }
                            }
                        }
                        if (!empty($clerkm2)) {
                            if (is_array($clerkCommissions)) {
                                $clerkCommission2+= isset($clerkCommissions['level2']) ? floatval($clerkCommissions['level2']) : 0;
                            } else {
                                $clerkc2 = iunserializer($og['clerkCommission2']);
                                $clerkl2 = app(ClerkLevelLogic::class)->getClerkLevel($clerkm2['clerk_level']);
                                if (!empty($clerkc2)) {
                                    $clerkCommission2+= isset($clerkc2['level' . $clerkl2['id']]) ? $clerkc2['level' . $clerkl2['id']] : $clerkc2['default'];
                                }
                            }
                        }
                        if (!empty($clerkm3)) {
                            if (is_array($clerkCommissions)) {
                                $clerkCommission3+= isset($clerkCommissions['level3']) ? floatval($clerkCommissions['level3']) : 0;
                            } else {
                                $clerkc3 = iunserializer($og['clerkCommission3']);
                                $clerkl3 = app(ClerkLevelLogic::class)->getClerkLevel($clerkm3['clerk_level']);
                                if (!empty($clerkc3)) {
                                    $clerkCommission3+= isset($clerkc3['level' . $clerkl3['id']]) ? $clerkc3['level' . $clerkl3['id']] : $clerkc3['default'];
                                }
                            }
                        }
                    }

                    //医生多规格
                    if (!empty($doctorSet_level) && empty($og['doctor_no_commission']) ) {
                        $doctorCommissions = iunserializer($og['doctor_commissions']);
                        if (!empty($doctorm1)) {
                            if (is_array($doctorCommissions)) {
                                $doctorCommission1+= isset($doctorCommissions['level1']) ? floatval($doctorCommissions['level1']) : 0;
                            } else {
                                $doctorc1 = iunserializer($og['doctor_commission']);
                                $doctorl1 = $plugin_doctor->getLevel($doctorm1['openid']);
                                
                                if (!empty($doctorc1)) {
                                    $doctorCommission1+= isset($doctorc1['level' . $doctorl1['id']]) ? $doctorc1['level' . $doctorl1['id']] : $doctorc1['default'];
                                }
                            }
                        }
                        if (!empty($doctorm2)) {
                            if (is_array($doctorCommissions)) {
                                $doctorCommission2+= isset($doctorCommissions['level2']) ? floatval($doctorCommissions['level2']) : 0;
                            } else {
                                $doctorc2 = iunserializer($og['doctorCommission2']);
                                $doctorl2 = $plugin_doctor->getLevel($doctorm2['openid']);
                                if (!empty($doctorc2)) {
                                    $doctorCommission2+= isset($doctorc2['level' . $doctorl2['id']]) ? $doctorc2['level' . $doctorl2['id']] : $doctorc2['default'];
                                }
                            }
                        }
                        if (!empty($doctorm3)) {
                            if (is_array($doctorCommissions)) {
                                $doctorCommission3+= isset($doctorCommissions['level3']) ? floatval($doctorCommissions['level3']) : 0;
                            } else {
                                $doctorc3 = iunserializer($og['doctorCommission3']);
                                $doctorl3 = $plugin_doctor->getLevel($doctorm3['openid']);
                                if (!empty($doctorc3)) {
                                    $doctorCommission3+= isset($doctorc3['level' . $doctorl3['id']]) ? $doctorc3['level' . $doctorl3['id']] : $doctorc3['default'];
                                }
                            }
                        }
                    }

                    //虚店店长多规格
                    if (!empty($ownerSet_level) && empty($og['noOwnerCommission']) ) {
                        $ownerCommissions = iunserializer($og['ownerCommissions']);
                        if (!empty($ownerm1)) {
                            if (is_array($ownerCommissions)) {
                                $ownerCommission1+= isset($ownerCommissions['level1']) ? floatval($ownerCommissions['level1']) : 0;
                            } else {
                                $ownerc1 = iunserializer($og['ownerCommission1']);
                                $ownerl1 = $plugin_owner->getLevel($ownerm1['openid']);
                                
                                if (!empty($ownerc1)) {
                                    $ownerCommission1+= isset($ownerc1['level' . $ownerl1['id']]) ? $ownerc1['level' . $ownerl1['id']] : $ownerc1['default'];
                                }
                            }
                        }
                        if (!empty($ownerm2)) {
                            if (is_array($ownerCommissions)) {
                                $ownerCommission2+= isset($ownerCommissions['level2']) ? floatval($ownerCommissions['level2']) : 0;
                            } else {
                                $ownerc2 = iunserializer($og['ownerCommission2']);
                                $ownerl2 = $plugin_owner->getLevel($ownerm2['openid']);
                                if (!empty($ownerc2)) {
                                    $ownerCommission2+= isset($ownerc2['level' . $ownerl2['id']]) ? $ownerc2['level' . $ownerl2['id']] : $ownerc2['default'];
                                }
                            }
                        }
                        if (!empty($ownerm3)) {
                            if (is_array($ownerCommissions)) {
                                $ownerCommission3+= isset($ownerCommissions['level3']) ? floatval($ownerCommissions['level3']) : 0;
                            } else {
                                $ownerc3 = iunserializer($og['ownerCommission3']);
                                $ownerl3 = $plugin_owner->getLevel($ownerm3['openid']);
                                if (!empty($ownerc3)) {
                                    $ownerCommission3+= isset($ownerc3['level' . $ownerl3['id']]) ? $ownerc3['level' . $ownerl3['id']] : $ownerc3['default'];
                                }
                            }
                        }
                    }

                    //虚店合伙人多规格
                    if (!empty($copartnerSet_level) && empty($og['noCopartnerCommission']) ) {
                        $copartnerCommissions = iunserializer($og['copartnerCommissions']);
                        if (!empty($copartnerm1)) {
                            if (is_array($copartnerCommissions)) {
                                $copartnerCommission1+= isset($copartnerCommissions['level1']) ? floatval($copartnerCommissions['level1']) : 0;
                            } else {
                                $copartnerc1 = iunserializer($og['copartnerCommission1']);
                                $copartnerl1 = $plugin_copartner->getLevel($copartnerm1['id']);
                                
                                if (!empty($copartnerc1)) {
                                    $copartnerCommission1+= isset($copartnerc1['level' . $copartnerl1['id']]) ? $copartnerc1['level' . $copartnerl1['id']] : $copartnerc1['default'];
                                }
                            }
                        }
                        if (!empty($copartnerm2)) {
                            if (is_array($copartnerCommissions)) {
                                $copartnerCommission2+= isset($copartnerCommissions['level2']) ? floatval($copartnerCommissions['level2']) : 0;
                            } else {
                                $copartnerc2 = iunserializer($og['copartnerCommission2']);
                                $copartnerl2 = $plugin_copartner->getLevel($copartnerm2['copartner_id']);
                                if (!empty($copartnerc2)) {
                                    $copartnerCommission2+= isset($copartnerc2['level' . $copartnerl2['id']]) ? $copartnerc2['level' . $copartnerl2['id']] : $copartnerc2['default'];
                                }
                            }
                        }
                        if (!empty($copartnerm3)) {
                            if (is_array($copartnerCommissions)) {
                                $copartnerCommission3+= isset($copartnerCommissions['level3']) ? floatval($copartnerCommissions['level3']) : 0;
                            } else {
                                $copartnerc3 = iunserializer($og['copartnerCommission3']);
                                $copartnerl3 = $plugin_copartner->getLevel($copartnerm3['copartner_id']);
                                if (!empty($copartnerc3)) {
                                    $copartnerCommission3+= isset($copartnerc3['level' . $copartnerl3['id']]) ? $copartnerc3['level' . $copartnerl3['id']] : $copartnerc3['default'];
                                }
                            }
                        }
                    }

                    $goods.="" . $og['title'] . "\r\n";
                    
                    if (!empty($og['optiontitle'])) {
                        $goods.=" 规格: " . $og['optiontitle'];
                    }
                    if (!empty($og['option_goodssn'])) {
                        $og['goodssn'] = $og['option_goodssn'];
                    }
                    if (!empty($og['option_productsn'])) {
                        $og['productsn'] = $og['option_productsn'];
                    }
                    
                    if (!empty($og['goodssn'])) {
                        $goods.=' 商品编号: ' . $og['goodssn'];
                    }
                    if (!empty($og['productsn'])) {
                        $goods.=' 商品条码: ' . $og['productsn'];
                    }
                    // 检查 total 是否为 0
                    if ($og['total'] == 0) {
                        // 如果 total 为 0，设置单价和折扣后单价为 0
                        $unitPrice = 0;
                        $discountedUnitPrice = 0;
                    } else {
                        // 计算单价
                        $unitPrice = $og['price'] / $og['total'];

                        // 计算折扣后单价
                        // 如果 realprice 为 0，说明订单金额被优惠券完全抵扣
                        $discountedUnitPrice = ($og['realprice'] == 0) ? 0 : ($og['realprice'] / $og['total']);
                    }
                    $goods.=' 单价: ' . $unitPrice . ' 折扣后: ' . $discountedUnitPrice . ' 数量: ' . $og['total'] . ' 总价: ' . $og['price'] . " 折扣后: " . $og['realprice'] . "\r\n ";
                    
                    if (p('diyform') && !empty($og['diyformfields']) && !empty($og['diyformdata'])) {
                        $diyformdata_array = p('diyform') ->getDatas(iunserializer($og['diyformfields']), iunserializer($og['diyformdata']), 1);
                        $diyformdata = "";
                        
                        $dflag = 1;
                        foreach ($diyformdata_array as $da) {
                            
                            if (!empty($diy_title_data)) {
                                if(array_key_exists($da['key'], $diy_title_data)) {
                                    $dflag = 0;
                                }
                            }
                            
                            if ($dflag == 1) {
                                $diy_title_data[$da['key']] = $da['name'];
                            }
                            /*零宽度字符&#8203;问题导致写入文本异常*/
                            $og['goods_' . $da['key']] = str_replace("\xE2\x80\x8B", "", $da['value']);
                            $diyformdata.=$da['name'] . ": " . $da['value'] . " \r\n";
                        }
                        /*零宽度字符&#8203;问题导致写入文本异常*/
                        $og['goods_diyformdata'] = str_replace("\xE2\x80\x8B", "", $diyformdata);
                    }
                    if(empty($og['gtitle'])!=true){
                        $og['title']=$og['gtitle'];
                    }
                    if($og['giftstatus'] ==2){
                        $value['giftSign'] = true;
                    }
                    // 检查处方药是否已开方
                    // 1.是否处方药 0否1是
                    $og['is_prescription_drug'] = 0;
                    $is_prescription_drug = $og['id'] ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescriptionDrug($og['id']) : 0;
                    // 2.获取开方信息
                    if ($is_prescription_drug) {
                        $og['is_prescription_drug'] = 1;
                        if ($og['pr_order_id']) {
                            $prescribe_info = app(PrescriptionOrderGoodsLogic::class)->getPrescribeByPrOrderIdAndGoodsId($og['pr_order_id'], $og['id']);
                            $og['expire_time'] = $prescribe_info['data']['expire_time'];
                            $og['expire_days'] = $prescribe_info['data']['expire_days'];
                            $og['prescribe_status'] = (new OrderEnum())::getStatusName()[$prescribe_info['data']['code']];
                        }
                    }
                }
                unset($og);
                //分销商佣金级别
                if (!empty($level) && empty($agentid)) {                    
                    $value['commission1'] = $commission1;
                    $value['commission2'] = $commission2;
                    $value['commission3'] = $commission3;
                }

                //虚店店员佣金级别
                if (!empty($clerkSet_level)) {                    
                    $value['clerkCommission1'] = number_format($clerkCommission1,2);
                    $value['clerkCommission2'] = number_format($clerkCommission2,2);
                    $value['clerkCommission3'] = number_format($clerkCommission3,2);
                }

                //医生佣金级别
                if (!empty($doctorSet_level)) {                    
                    $value['doctorCommission1'] = number_format($doctorCommission1,2);
                    $value['doctorCommission2'] = number_format($doctorCommission2,2);
                    $value['doctorCommission3'] = number_format($doctorCommission3,2);
                }

                //虚店店长佣金级别
                if (!empty($ownerSet_level)) {                    
                    $value['ownerCommission1'] = number_format($ownerCommission1,2);
                    $value['ownerCommission2'] = number_format($ownerCommission2,2);
                    $value['ownerCommission3'] = number_format($ownerCommission3,2);
                }

                //虚店合伙人佣金级别
                if (!empty($copartnerSet_level)) {                    
                    $value['copartnerCommission1'] = number_format($copartnerCommission1,2);
                    $value['copartnerCommission2'] = number_format($copartnerCommission2,2);
                    $value['copartnerCommission3'] = number_format($copartnerCommission3,2);
                }

                $value['goods'] = set_medias($order_goods, 'thumb');
                $value['goods_str'] = $goods;
                //计算分销商订单
                if (!empty($agentid) && $level > 0) {
                    //计算几级订单
                    $commission_level = 0;
                    if ($value['agentid'] == $agentid) {
                        $value['level'] = 1;
                        $level1_commissions = pdo_fetchall('select commission1,commissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                            . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                            . ' where og.orderid=:orderid and o.agentid= ' . $agentid . "  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                        foreach ($level1_commissions as $c) {
                            $commission = iunserializer($c['commission1']);
                            $commissions = iunserializer($c['commissions']);
                            if (empty($commissions)) {
                                $commission_level+= isset($commission['level' . $agentLevel['id']]) ? $commission['level' . $agentLevel['id']] : $commission['default'];
                            } else {
                                $commission_level+= isset($commissions['level1']) ? floatval($commissions['level1']) : 0;
                            }
                        }
                    } else if (in_array($value['agentid'], array_keys($agent['level1_agentids']))) {
                        $value['level'] = 2;
                        if ($agent['level2'] > 0) {
                            $level2_commissions = pdo_fetchall('select commission2,commissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.agentid in ( ' . implode(',', array_keys($agent['level1_agentids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level2_commissions as $c) {
                                $commission = iunserializer($c['commission2']);
                                $commissions = iunserializer($c['commissions']);
                                if (empty($commissions)) {
                                    $commission_level+= isset($commission['level' . $agentLevel['id']]) ? $commission['level' . $agentLevel['id']] : $commission['default'];
                                } else {
                                    $commission_level+= isset($commissions['level2']) ? floatval($commissions['level2']) : 0;
                                }
                            }
                        }
                    } else if (in_array($value['agentid'], array_keys($agent['level2_agentids']))) {
                        $value['level'] = 3;
                        if ($agent['level3'] > 0) {
                            $level3_commissions = pdo_fetchall('select commission3,commissions from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.agentid in ( ' . implode(',', array_keys($agent['level2_agentids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level3_commissions as $c) {
                                $commission = iunserializer($c['commission3']);
                                $commissions = iunserializer($c['commissions']);
                                if (empty($commissions)) {
                                    $commission_level+= isset($commission['level' . $agentLevel['id']]) ? $commission['level' . $agentLevel['id']] : $commission['default'];
                                } else {
                                    $commission_level+= isset($commissions['level3']) ? floatval($commissions['level3']) : 0;
                                }
                            }
                        }
                    }
                    $value['commission'] = $commission_level;
                }

                //计算虚店店员订单
                if ($clerkSet_level > 0) {
                    //计算几级订单
                    $clerkCommission_level = 0;
                    if ($value['clerk_id']) {
                        $value['clerk_level'] = 1;
                        $level1_clerkCommissions = pdo_fetchall('select clerkCommission1,clerkCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                            . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                            . ' where og.orderid=:orderid and o.clerk_id= ' . $value['clerk_id'] . "  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                        foreach ($level1_clerkCommissions as $clc) {
                            $clerkCommission = iunserializer($clc['clerkCommission1']);
                            $clerkCommissions = iunserializer($clc['clerkCommissions']);
                            if (empty($clerkCommissions)) {
                                $clerkCommission_level+= isset($clerkCommission['level' . $clerkLevel['id']]) ? $clerkCommission['level' . $clerkLevel['id']] : $clerkCommission['default'];
                            } else {
                                $clerkCommission_level+= isset($clerkCommissions['level1']) ? floatval($clerkCommissions['level1']) : 0;
                            }
                        }
                    } else if (is_array($clerk['level1_clerk_ids'])) {
                            if (in_array($value['clerk_id'], array_keys($clerk['level1_clerk_ids']))){
                            $value['clerk_level'] = 2;
                            if ($clerk['level2'] > 0) {
                                $level2_clerkCommissions = pdo_fetchall('select clerkCommission2,clerkCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                                    . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                    . ' where og.orderid=:orderid and  o.clerk_id in ( ' . implode(',', array_keys($clerk['level1_clerk_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                                foreach ($level2_clerkCommissions as $clc) {
                                    $clerkCommission = iunserializer($clc['clerkCommission2']);
                                    $clerkCommissions = iunserializer($clc['clerkCommissions']);
                                    if (empty($clerkCommissions)) {
                                        $clerkCommission_level += isset($clerkCommission['level' . $clerkLevel['id']]) ? $clerkCommission['level' . $clerkLevel['id']] : $clerkCommission['default'];
                                    } else {
                                        $clerkCommission_level += isset($clerkCommissions['level2']) ? floatval($clerkCommissions['level2']) : 0;
                                    }
                                }
                            }
                        }
                    } else if (is_array($clerk['level2_clerk_ids'])) {
                            if (in_array($value['clerk_id'], array_keys($clerk['level2_clerk_ids']))){
                            $value['clerk_level'] = 3;
                            if ($clerk['level3'] > 0) {
                                $level3_clerkCommissions = pdo_fetchall('select clerkCommission3,clerkCommissions from ' . tablename('elapp_shop_order_goods') . ' og '
                                    . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                    . ' where og.orderid=:orderid and  o.clerk_id in ( ' . implode(',', array_keys($clerk['level2_clerk_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                                foreach ($level3_clerkCommissions as $clc) {
                                    $clerkCommission = iunserializer($clc['commission3']);
                                    $clerkCommissions = iunserializer($clc['commissions']);
                                    if (empty($clerkCommissions)) {
                                        $clerkCommission_level += isset($clerkCommission['level' . $clerkLevel['id']]) ? $clerkCommission['level' . $clerkLevel['id']] : $clerkCommission['default'];
                                    } else {
                                        $clerkCommission_level += isset($clerkCommissions['level3']) ? floatval($clerkCommissions['level3']) : 0;
                                    }
                                }
                            }
                        }
                        $value['clerkCommission'] = number_format($clerkCommission_level, 2);
                    }
                }

                //计算医生订单
                if ($doctorSet_level > 0) {
                    //计算几级订单
                    $doctorCommission_level = 0;
                    if ($value['doctor_id']) {
                        $value['doctor_level'] = 1;
                        $level1_doctorCommissions = pdo_fetchall('select doctor_commission,doctor_commissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                            . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                            . ' where og.orderid=:orderid and o.doctor_id= ' . $value['doctor_id'] . "  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                        foreach ($level1_doctorCommissions as $clc) {
                            $doctorCommission = iunserializer($clc['doctor_commission']);
                            $doctorCommissions = iunserializer($clc['doctor_commissions']);
                            if (empty($doctorCommissions)) {
                                $doctorCommission_level+= isset($doctorCommission['level' . $doctorLevel['id']]) ? $doctorCommission['level' . $doctorLevel['id']] : $doctorCommission['default'];
                            } else {
                                $doctorCommission_level+= isset($doctorCommissions['level1']) ? floatval($doctorCommissions['level1']) : 0;
                            }
                        }
                    } else if (is_array($doctor['level1_doctor_ids'])) {
                        if (isset($doctor['level1_doctor_ids']) && in_array($value['doctor_id'], array_keys($doctor['level1_doctor_ids']))){
                            $value['doctor_level'] = 2;
                            if ($doctor['level2'] > 0) {
                                $level2_doctorCommissions = pdo_fetchall('select doctorCommission2,doctorCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                                    . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                    . ' where og.orderid=:orderid and  o.doctor_id in ( ' . implode(',', array_keys($doctor['level1_doctor_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                                foreach ($level2_doctorCommissions as $clc) {
                                    $doctorCommission = iunserializer($clc['doctorCommission2']);
                                    $doctorCommissions = iunserializer($clc['doctorCommissions']);
                                    if (empty($doctorCommissions)) {
                                        $doctorCommission_level += isset($doctorCommission['level' . $doctorLevel['id']]) ? $doctorCommission['level' . $doctorLevel['id']] : $doctorCommission['default'];
                                    } else {
                                        $doctorCommission_level += isset($doctorCommissions['level2']) ? floatval($doctorCommissions['level2']) : 0;
                                    }
                                }
                            }
                        }
                    } else if (is_array($doctor['level2_doctor_ids'])) {
                        if (isset($doctor['level2_doctor_ids']) && in_array($value['doctor_id'], array_keys($doctor['level2_doctor_ids']))) {
                            $value['doctor_level'] = 3;
                            if ($doctor['level3'] > 0) {
                                $level3_doctorCommissions = pdo_fetchall('select doctorCommission3,doctorCommissions from ' . tablename('elapp_shop_order_goods') . ' og '
                                    . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                    . ' where og.orderid=:orderid and  o.doctor_id in ( ' . implode(',', array_keys($doctor['level2_doctor_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                                foreach ($level3_doctorCommissions as $clc) {
                                    $doctorCommission = iunserializer($clc['commission3']);
                                    $doctorCommissions = iunserializer($clc['commissions']);
                                    if (empty($doctorCommissions)) {
                                        $doctorCommission_level += isset($doctorCommission['level' . $doctorLevel['id']]) ? $doctorCommission['level' . $doctorLevel['id']] : $doctorCommission['default'];
                                    } else {
                                        $doctorCommission_level += isset($doctorCommissions['level3']) ? floatval($doctorCommissions['level3']) : 0;
                                    }
                                }
                            }
                        }
                    }
                    $value['doctorCommission'] = number_format($doctorCommission_level,2);
                }

                //计算虚店店长订单
                if ($ownerSet_level > 0) {
                    //计算几级订单
                    $ownerCommission_level = 0;
                    if ($value['owner_id']) {
                        $value['owner_level'] = 1;
                        $level1_ownerCommissions = pdo_fetchall('select ownerCommission1,ownerCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                            . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                            . ' where og.orderid=:orderid and o.owner_id= ' . $value['owner_id'] . "  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                        foreach ($level1_ownerCommissions as $clc) {
                            $ownerCommission = iunserializer($clc['ownerCommission1']);
                            $ownerCommissions = iunserializer($clc['ownerCommissions']);
                            if (empty($ownerCommissions)) {
                                $ownerCommission_level+= isset($ownerCommission['level' . $ownerLevel['id']]) ? $ownerCommission['level' . $ownerLevel['id']] : $ownerCommission['default'];
                            } else {
                                $ownerCommission_level+= isset($ownerCommissions['level1']) ? floatval($ownerCommissions['level1']) : 0;
                            }
                        }
                    } else if (in_array($value['owner_id'], array_keys($owner['level1_owner_ids']))) {
                        $value['owner_level'] = 2;
                        if ($owner['level2'] > 0) {
                            $level2_ownerCommissions = pdo_fetchall('select ownerCommission2,ownerCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.owner_id in ( ' . implode(',', array_keys($owner['level1_owner_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level2_ownerCommissions as $clc) {
                                $ownerCommission = iunserializer($clc['ownerCommission2']);
                                $ownerCommissions = iunserializer($clc['ownerCommissions']);
                                if (empty($ownerCommissions)) {
                                    $ownerCommission_level+= isset($ownerCommission['level' . $ownerLevel['id']]) ? $ownerCommission['level' . $ownerLevel['id']] : $ownerCommission['default'];
                                } else {
                                    $ownerCommission_level+= isset($ownerCommissions['level2']) ? floatval($ownerCommissions['level2']) : 0;
                                }
                            }
                        }
                    } else if (in_array($value['owner_id'], array_keys($owner['level2_owner_ids']))) {
                        $value['owner_level'] = 3;
                        if ($owner['level3'] > 0) {
                            $level3_ownerCommissions = pdo_fetchall('select ownerCommission3,ownerCommissions from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.owner_id in ( ' . implode(',', array_keys($owner['level2_owner_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level3_ownerCommissions as $clc) {
                                $ownerCommission = iunserializer($clc['ownerCommission3']);
                                $ownerCommissions = iunserializer($clc['ownerCommissions']);
                                if (empty($ownerCommissions)) {
                                    $ownerCommission_level+= isset($ownerCommission['level' . $ownerLevel['id']]) ? $ownerCommission['level' . $ownerLevel['id']] : $ownerCommission['default'];
                                } else {
                                    $ownerCommission_level+= isset($ownerCommissions['level3']) ? floatval($ownerCommissions['level3']) : 0;
                                }
                            }
                        }
                    }
                    $value['ownerCommission'] = number_format($ownerCommission_level,2);
                }

                //计算虚店合伙人订单
                if ($copartnerSet_level > 0) {
                    //计算几级订单
                    $copartnerCommission_level = 0;
                    if ($value['copartner_id']) {
                        $value['copartner_level'] = 1;
                        $level1_copartnerCommissions = pdo_fetchall('select copartnerCommission1,copartnerCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                            . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                            . ' where og.orderid=:orderid and o.copartner_id= ' . $value['copartner_id'] . "  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                        foreach ($level1_copartnerCommissions as $clc) {
                            $copartnerCommission = iunserializer($clc['copartnerCommission1']);
                            $copartnerCommissions = iunserializer($clc['copartnerCommissions']);
                            if (empty($copartnerCommissions)) {
                                $copartnerCommission_level+= isset($copartnerCommission['level' . $copartnerLevel['id']]) ? $copartnerCommission['level' . $copartnerLevel['id']] : $copartnerCommission['default'];
                            } else {
                                $copartnerCommission_level+= isset($copartnerCommissions['level1']) ? floatval($copartnerCommissions['level1']) : 0;
                            }
                        }
                    } else if (!empty($copartner['level1_copartner_ids']) && in_array($value['copartner_id'], array_keys($copartner['level1_copartner_ids']))) {
                        $value['copartner_level'] = 2;
                        if ($copartner['level2'] > 0) {
                            $level2_copartnerCommissions = pdo_fetchall('select copartnerCommission2,copartnerCommissions  from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.copartner_id in ( ' . implode(',', array_keys($copartner['level1_copartner_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level2_copartnerCommissions as $clc) {
                                $copartnerCommission = iunserializer($clc['copartnerCommission2']);
                                $copartnerCommissions = iunserializer($clc['copartnerCommissions']);
                                if (empty($copartnerCommissions)) {
                                    $copartnerCommission_level+= isset($copartnerCommission['level' . $copartnerLevel['id']]) ? $copartnerCommission['level' . $copartnerLevel['id']] : $copartnerCommission['default'];
                                } else {
                                    $copartnerCommission_level+= isset($copartnerCommissions['level2']) ? floatval($copartnerCommissions['level2']) : 0;
                                }
                            }
                        }
                    } else if (!empty($copartner['level2_copartner_ids']) && in_array($value['copartner_id'], array_keys($copartner['level2_copartner_ids']))) {
                        $value['copartner_level'] = 3;
                        if ($copartner['level3'] > 0) {
                            $level3_copartnerCommissions = pdo_fetchall('select copartnerCommission3,copartnerCommissions from ' . tablename('elapp_shop_order_goods') . ' og '
                                . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid '
                                . ' where og.orderid=:orderid and  o.copartner_id in ( ' . implode(',', array_keys($copartner['level2_copartner_ids'])) . ")  and o.uniacid=:uniacid", array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                            foreach ($level3_copartnerCommissions as $clc) {
                                $copartnerCommission = iunserializer($clc['copartnerCommission3']);
                                $copartnerCommissions = iunserializer($clc['copartnerCommissions']);
                                if (empty($copartnerCommissions)) {
                                    $copartnerCommission_level+= isset($copartnerCommission['level' . $copartnerLevel['id']]) ? $copartnerCommission['level' . $copartnerLevel['id']] : $copartnerCommission['default'];
                                } else {
                                    $copartnerCommission_level+= isset($copartnerCommissions['level3']) ? floatval($copartnerCommissions['level3']) : 0;
                                }
                            }
                        }
                    }
                    $value['copartnerCommission'] = number_format($copartnerCommission_level,2);
                }
                
                //是否是代付订单
                $is_peerpay = 0;
                if(m('order')->checkpeerpay($value['id'])){
                    $is_peerpay = 1;
                }
                $value['is_peerpay'] = $is_peerpay;
                $value['is_singlerefund'] = $is_singlerefund;
            }
            
            //订单查询语句优化 lgt
            $oopenid = "'".implode("','",$openids)."'";
            $verifyopenid = "'".implode("','",$verifyopenids)."'";
            $omember = pdo_fetchall('select openid,nickname,id as mid,realname as mrealname,mobile as mmobile,uid from '.tablename('elapp_shop_member').' where openid in('.$oopenid.') and uniacid = :uniacid',array(':uniacid'=>$_W['uniacid']),'openid');
            $verifyopenid_array = pdo_fetchall("SELECT m.id as salerid,m.nickname as salernickname,m.openid,s.salername FROM " .tablename('elapp_shop_saler')." s LEFT JOIN ".tablename('elapp_shop_member')." m ON m.openid = s.openid and m.uniacid=s.uniacid WHERE s.openid IN ({$verifyopenid}) and s.uniacid = :uniacid",array(':uniacid'=>$_W['uniacid']),'openid');
            foreach($list as $lk => $lv){
                $list[$lk]['nickname'] = $omember[$lv['openid']]['nickname'];
                $list[$lk]['mid'] = $omember[$lv['openid']]['mid'];
                $list[$lk]['mrealname'] = $omember[$lv['openid']]['mrealname'];
                $list[$lk]['mmobile'] = $omember[$lv['openid']]['mmobile'];
                $list[$lk]['uid'] = $omember[$lv['openid']]['mid'];
                $list[$lk]['salerid'] = $verifyopenid_array[$lv['verifyopenid']]['salerid'];
                $list[$lk]['salernickname'] = $verifyopenid_array[$lv['verifyopenid']]['salernickname'];
                $list[$lk]['salername'] = $verifyopenid_array[$lv['verifyopenid']]['salername'];
            }            
        }
        unset($value);

        
        if (!empty($temp_refund)) {
            $refund_applys =  pdo_fetchall('select id,status,applyprice,orderid from ' . tablename('elapp_shop_order_refund') . ' where id in (' . implode(',', $temp_refund) . ') and uniacid=:uniacid ', array(':uniacid' => $_W['uniacid']));
        }
        foreach ($list as $key => &$value) {

            if (!empty($refund_applys)) {
                foreach ($refund_applys as $k => $v) {
                    if ($value['id'] == $v['orderid']) {
                        $value['applyprice'] =  $v['applyprice'];
                        $value['has_refunded'] = true;
                        $value['order_refund_status'] = $v['status'];
                    }
                }
                
            }

            if ($value['is_settle']) {
                $commissions = (new SettleModel())->getOrderSettleCommissions($value['id']);
                foreach ($commissions as $k=>$v) {
                    $value['commission_' . $k] = $v;
                }
            } else {
                $value['commissions'] = [];
            }
        }
        unset($value);

        if ($condition != ' o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0 ' || !empty($sqlcondition)){
            $member_cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_MEMBER);
            $order_cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER, 'o');
//            $order_o_cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER, 'o');

            // 查询条件是会员信息
            if ($searchfield == 'member') {

                $priceCondition .= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";
                $priceCondition1= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";

                $openidArr = pdo_fetchall('SELECT openid FROM '.tablename('elapp_shop_member').' WHERE uniacid = '.$_W['uniacid'].$priceCondition1 . $member_cond);

                if (!empty($openidArr)) {
                    foreach ($openidArr as $openid) {
                        $openids[] = $openid['openid'];
                    }
                    $inOpenid = "'".implode('\',\'',$openids)."'";
                    $orderPrice = pdo_fetch('SELECT COUNT(DISTINCT(o.id)) as count,sum(price) as sumprice FROM '.tablename('elapp_shop_order').' o WHERE o.uniacid = '.$_W['uniacid'].' AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 AND o.openid IN ('.$inOpenid.')'.$timeCondition . $statuscondition . $order_cond) ;
                }else{
                    $orderPrice['sumprice'] = 0;
                }
                $totalmoney = $orderPrice['sumprice'];
                $total = $orderPrice['count'];
            } elseif ($searchfield == "member_group") {
                if (empty($groupId)) {
                    $groupId = "-1";
                }
                $openidArr = pdo_fetchall("SELECT openid FROM " . tablename("elapp_shop_member") . " WHERE uniacid = " . $_W["uniacid"] . " and `groupid` in (" . $groupId . ") $member_cond");
                if (!empty($openidArr)) {
                    foreach ($openidArr as $openid) {
                        $openids[] = $openid["openid"];
                    }
                    $inOpenid = "'" . implode("','", $openids) . "'";
                    $orderPrice = pdo_fetch("SELECT COUNT(DISTINCT(o.id)) as count,sum(price) as sumprice FROM " . tablename("elapp_shop_order") . " o WHERE o.uniacid = " . $_W["uniacid"] . " AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 AND o.openid IN (" . $inOpenid . ")" . $timeCondition . $statuscondition . $order_cond);
                } else {
                    $orderPrice["sumprice"] = 0;
                }
                $totalmoney = $orderPrice["sumprice"];
                $total = $orderPrice["count"];
            } elseif ($searchfield == "member_level") {
                if (empty($levelId)) {
                    $levelId = "-1";
                }
                $openidArr = pdo_fetchall("SELECT openid FROM " . tablename("elapp_shop_member") . " WHERE uniacid = " . $_W["uniacid"] . " and `level` in (" . $levelId . ") $member_cond");
                if (!empty($openidArr)) {
                    foreach ($openidArr as $openid) {
                        $openids[] = $openid["openid"];
                    }
                    $inOpenid = "'" . implode("','", $openids) . "'";
                    $orderPrice = pdo_fetch("SELECT COUNT(DISTINCT(o.id)) as count,sum(price) as sumprice FROM " . tablename("elapp_shop_order") . " o WHERE o.uniacid = " . $_W["uniacid"] . " AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 AND o.openid IN (" . $inOpenid . ")" . $timeCondition . $statuscondition . $order_cond);
                } else {
                    $orderPrice["sumprice"] = 0;
                }
                $totalmoney = $orderPrice["sumprice"];
                $total = $orderPrice["count"];
            }else if ($searchfield == 'address') {
                // 按收件人查找
                $orderPrice = pdo_fetch('SELECT COUNT(DISTINCT(o.id)) as count,sum(o.price) as sumprice FROM '.tablename('elapp_shop_order').
                    ' o left join '.tablename('elapp_shop_member_address').' a on o.addressid = a.id WHERE o.uniacid = '.$_W['uniacid'].'  AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 '.$priceCondition.$statuscondition . $order_cond);
                $totalmoney = $orderPrice['sumprice'];
                $total = $orderPrice['count'];
                if ($orderPrice['count'] == 0) {
                    $totalmoney = 0;
                }
            }else if ($searchfield == 'location') {
                // 按地址查找
                $orderPrice = pdo_fetch('SELECT COUNT(DISTINCT(o.id)) as count,sum(o.price) as sumprice FROM '.tablename('elapp_shop_order').
                    ' o left join '.tablename('elapp_shop_member_address').' a on o.addressid = a.id WHERE o.uniacid = '.$_W['uniacid'].' AND o.deleted=0 AND o.isparent=0 '.$priceCondition.$statuscondition . $order_cond);
                $totalmoney = $orderPrice['sumprice'];
                $total = (int)$orderPrice['count'];
                if ($orderPrice['count'] == 0) {
                    $totalmoney = 0;
                }
            } else if ($searchfield == 'saler') {
                // 按核销员查找
                $t = pdo_fetch(
                    'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_verifygoods') . " vg on vg.orderid = o.id"
                    . " left join " . tablename('elapp_shop_verifygoods_log') . " vgl on vgl.verifygoodsid = vg.id"
                    . " left join " . tablename('elapp_shop_verifyorder_log') . " vol on vol.orderid=o.id "
                    . " left join " . tablename('elapp_shop_saler') . " s on (s.id = vgl.salerid or s.id=vol.salerid) and s.uniacid=o.uniacid and o.ismerch=0"
                    . " left join " . tablename('elapp_shop_merch_saler') . " ms on (ms.id = vgl.salerid or ms.id=vol.salerid) and ms.uniacid=o.uniacid and o.ismerch=1"
                    . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                    . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                $total = (int)$t['count'];
                $totalmoney = $t['sumprice'];
            } else if ($searchfield == 'store') {
                // 按核销门店查找
                $t = pdo_fetch(
                    'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_verifygoods') . " vg on vg.orderid = o.id"
                    . " left join " . tablename('elapp_shop_verifygoods_log') . " vgl on vgl.verifygoodsid = vg.id"
                    . " left join " . tablename('elapp_shop_verifyorder_log') . " vol on vol.orderid=o.id "
                    . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                $total = (int)$t['count'];
                $totalmoney = $t['sumprice'];
            } else if ($searchfield == 'mid') {
                // 按核销门店查找
                $t = pdo_fetch(
                    'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid = o.openid and m.uniacid=o.uniacid"
                    . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                $total = (int)$t['count'];
                $totalmoney = $t['sumprice'];
            } else if ($searchfield == 'clerk') {
                //按推广店员
                $priceCondition .= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";
                $priceCondition1= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";

                $openidArr = pdo_fetchall('SELECT id,openid FROM '.tablename('elapp_shop_member').' WHERE uniacid = ' . $_W['uniacid'] . $priceCondition1);

                if (!empty($openidArr)) {
                    foreach ($openidArr as $openid) {
                        $openids[] = $openid['openid'];
                        $mids[] = $openid['id'];
                    }
                    $inOpenid = "'".implode('\',\'',$openids)."'";
                    $inMid = "'".implode('\',\'',$mids)."'";
                    $orderPrice = pdo_fetch('SELECT COUNT(DISTINCT(o.id)) as count,sum(price) as sumprice FROM '.tablename('elapp_shop_order').' o WHERE o.uniacid = '.$_W['uniacid'].' AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 AND o.clerk_id IN ('.$inMid.')'.$timeCondition . $statuscondition . $order_cond) ;
                }else{
                    $orderPrice['sumprice'] = 0;
                }
                $totalmoney = $orderPrice['sumprice'];
                $total = $orderPrice['count'];
            } else if ($searchfield == 'doctor') {
                //按推广医生
                $priceCondition .= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";
                $priceCondition1= " AND (nickname LIKE '".$_GPC['keyword']."%' OR realname LIKE '".$_GPC['keyword']."%' OR mobile LIKE '".$_GPC['keyword']."%') ";

                $openidArr = pdo_fetchall('SELECT id,openid FROM '.tablename('elapp_shop_member').' WHERE uniacid = ' . $_W['uniacid'] . $priceCondition1 . $order_cond);

                if (!empty($openidArr)) {
                    foreach ($openidArr as $openid) {
                        $openids[] = $openid['openid'];
                        $mids[] = $openid['id'];
                    }
                    $inOpenid = "'".implode('\',\'',$openids)."'";
                    $inMid = "'".implode('\',\'',$mids)."'";
                    $orderPrice = pdo_fetch('SELECT COUNT(DISTINCT(o.id)) as count,sum(price) as sumprice FROM '.tablename('elapp_shop_order').' o WHERE o.uniacid = '.$_W['uniacid'].' AND o.deleted=0 AND o.isparent=0 and o.iscycelbuy=0 AND o.doctor_id IN ('.$inMid.')'.$timeCondition . $statuscondition) ;
                }else{
                    $orderPrice['sumprice'] = 0;
                }
                $totalmoney = $orderPrice['sumprice'];
                $total = $orderPrice['count'];
            } else {
                // 其他
                // 全部订单 没有查询条件 时间为空
                if($status === '' && empty($_GPC['keyword']) && empty($_GPC['time'])){
                    $redis = redis();
                    // $heads 团长 是怎么用到的
                    if(!empty($heads)){
                        $redis_key_total = $_W['uniacid'].'_elapp_shop_order_list_total'.$heads;
                        $redis_key_totalmoney = $_W['uniacid'].'_elapp_shop_order_list_totalmoney'.$heads;
                    }
                    if(!is_error($redis)){
                        if (false && $redis->get($redis_key_total) != false && $redis->get($redis_key_totalmoney) != false) {  //判断key值所对应的值是否存在 去掉缓存
                            $total =  $redis->get($redis_key_total);
                            $totalmoney =  $redis->get($redis_key_totalmoney);
                        }else {
                            $count = 0;
                            $sumprice = 0.0;
                            if (empty($agentid)){
                                $t = pdo_fetch(
                                    'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                                    . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                                $total =  (int)$t['count'];
                                $totalmoney =  $t['sumprice'];
                            }else{
                                //分销订单总数=付款订单 分销订单金额 = 付款金额
                                if (p('commission')) {
                                    //付款后
                                    $member     = p('commission')->getInfo($magent['openid'], array('total', 'ordercount0', 'ok', 'ordercount', 'wait', 'pay'));
                                    $count      = $member['ordercount'];
                                    $sumprice   = $member['ordermoney'];
                                }
                                $total =  (int)$count;
                                $totalmoney =  $sumprice;
                            }
                        }
                    }else{
                        $t = pdo_fetch(
                            'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                            . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                            . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                        $total = (int)$t['count'];
                        $totalmoney = $t['sumprice'];
                    }

                }else{

                    // 有查询条件，但不在上面几个if里
                    // 用户id  多商户
                    $t = pdo_fetch(
                        'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                        . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                        . " $sqlcondition WHERE $condition $statuscondition $order_cond", $paras);
                    $total = (int)$t['count'];
                    $totalmoney = $t['sumprice'];
                }
            }

            //处理重复会员
            //找出会员的所有openid
            //订单的opeind in

        }else{
            // 什么条件也没有的情况
            // 新加两个条件 不知道对不对
            // 不加了  and istrade=0 and iscycelbuy=0
            // 上面写了好多情况，这里应该永远执行不到
            $order_cond = $this->filter->getConditionsSql(\app\model\FilterModel::TABLE_ORDER);
            $t = pdo_fetch(
                'SELECT COUNT(*) as count, ifnull(sum(price),0) as sumprice   FROM ' . tablename('elapp_shop_order') .
                " WHERE uniacid = :uniacid and ismr=0 and deleted=0 and isparent=0 {$status_condition} $order_cond", $paras);
            $total = $t['count'];
            $totalmoney = $t['sumprice'];
        }

        //导出Excel
        if($_GPC['export_ajax']){
            return ['size'=>$psize,'total'=>$total,'data'=>$list];
        }
        $pager = pagination2($total, $pindex, $psize);
        $stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_store') . ' where uniacid=:uniacid ', array(':uniacid' => $uniacid));
        $r_type = array( '0' => '退款', '1' => '退货退款', '2' => '换货');
        load()->func('tpl');
        include $this->template('order/list');
    }

    function main() {
        global $_W,$_GPC;
        $orderData = $this->orderData('',__FUNCTION__,1);
    }

    function status0(){
        global $_W, $_GPC;
        $orderData = $this->orderData(0,__FUNCTION__,1);
    }

    function status1(){
        global $_W, $_GPC;
        $orderData = $this->orderData(1,__FUNCTION__,1);
    }

    function status2(){
        global $_W, $_GPC;
        $orderData = $this->orderData(2,__FUNCTION__,1);
    }

    function status3(){
        global $_W, $_GPC;
        $orderData = $this->orderData(3,__FUNCTION__,1);
    }


    function status4(){
        global $_W, $_GPC;
        $orderData = $this->orderData(4,__FUNCTION__,1);
    }

    function status5(){
        global $_W, $_GPC;
        $orderData = $this->orderData(5,__FUNCTION__,1);
    }

    function status_1(){
        global $_W, $_GPC;
        $orderData = $this->orderData(-1,__FUNCTION__,1);
    }

    public function ajaxgettotals()
    {
        global $_GPC;
        $merch = intval($_GPC['merch']);
        $totals = m('order')->getTotals($merch);
        $result = empty($totals) ? array() : $totals;
        show_json(1,$result);
    }

    public function updateChildOrderPay(){
        global $_W;

        $params = array();
        $params[':uniacid'] = $_W['uniacid'];

        $sql = "select id,parentid from " . tablename('elapp_shop_order') . " where parentid>0 and status>0 and paytype=0 and uniacid=:uniacid";
        $list = pdo_fetchall($sql, $params);

        if (!empty($list)) {
            foreach($list as $k => $v) {
                $params[':orderid'] = $v['parentid'];
                $sql1 = "select paytype from " . tablename('elapp_shop_order') . " where id=:orderid and status>0 and paytype>0 and uniacid=:uniacid";
                $item = pdo_fetch($sql1, $params);
                if ($item['paytype'] > 0) {
                    pdo_update('elapp_shop_order', array('paytype' => $item['paytype']), array('id' => $v['id']));
                }

            }

        }
    }

    /**
     * 获取订单的维权状态
     * @param string $refund_type_text
     * @param $refund_status
     * <AUTHOR>
     * @return string
     */
    private function order_refund_status($refund_type_text = '',$refund_status){
        if($refund_status === false || empty($refund_type_text)){
            return $refund_type_text;
        }
        $status_text = '';
        switch ($refund_status) {
            case -2:
                $status_text = '客户取消'.$refund_type_text;
                break;
            case -1:
                $status_text = '已拒绝'.$refund_type_text;
                break;
            case 0:
                $status_text = '等待商家处理申请';
                break;
            case 1:
                $status_text = $refund_type_text.'完成';
                break;
            case 3:
                $status_text = '等待客户退回物品';
                break;
            case 4:
                $status_text = '客户退回物品，等待商家重新发货';
                break;
            case 5:
                $status_text = '等待客户收货';
                break;
            default:
                $status_text = $refund_type_text;
                break;
        }
        return $status_text;
    }

    public function export_ajax(){
        global $_W,$_GPC;
        $status = $_GPC['status'];
        $orderData = $this->orderData($status,__FUNCTION__,1);
        return $orderData;
    }


    public function export($list = array(),$page = 1,$exflag = true){
        //导出Excel
        global $_W,$_GPC;
        set_time_limit(0);
        $agentid = intval($_GPC['agentid']);
        $clerk_id = intval($_GPC['clerk_id']);//订单店员ID
        $doctor_id = intval($_GPC['doctor_id']);//订单医生ID
        $owner_id = intval($_GPC['owner_id']);//订单店长ID
        $mentor_id = intval($_GPC['mentor_id']);//帮扶人id
        $copartner_id = intval($_GPC['copartner_id']);//订单机构ID
        $copartner_account_id = intval($_GPC['copartner_account_id']);//订单机构UID
        $statuscondition = '';
        $uniacid = $_W['uniacid'];
        $plugin_clerk = p('clerk');

        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
            $this->updateChildOrderPay();
        } else {
            $is_openmerch = 0;
        }
        //虚店店员
        $clerkSet_level = 0;
        if ($plugin_clerk) {
            $clerkSet = $plugin_clerk->getSet();
            $clerkSet_level = intval($clerkSet['level']);
        }
         //店长
        $plugin_owner = p('vrshop');
        //帮扶分红
        $plugin_mentor = p('mentor');
        //机构
        $plugin_copartner = p('copartner');

        //虚店店长
        $ownerSet_level = 0;
        if ($plugin_owner) {
            //$clerk_id = intval($_GPC['clerk_id']);//订单推广者ID
            $ownerSet = $plugin_owner->getSet();
            $ownerSet_level = intval($ownerSet['level']);
        }
        //扶植分红
        if($plugin_mentor){
            $mentorSet = $plugin_mentor->getSet();
        }
        //虚店合伙人
        $copartnerSet_level = 0;
        if ($plugin_copartner) {
            //$clerk_id = intval($_GPC['copartner_id']);//订单推广者ID
            $copartnerSet = $plugin_copartner->getSet();
            $copartnerSet_level = intval($copartnerSet['level']);
        }

        //医生
        $plugin_doctor = new DoctorModel();

        $doctorSet_level = 0;
        if ($plugin_doctor) {
            $doctorSet = $plugin_doctor->getSet();
            $doctorSet_level = intval($doctorSet['level']);
        }
        // 查找门店
        $temp_stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_store') . ' where uniacid='.$_W['uniacid']);
        $temp_merch_stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_merch_store') . ' where uniacid='.$_W['uniacid']);

        $temp_saler = pdo_fetchall("SELECT s.id salerid, s.salername, m.nickname salernickname,s.openid FROM ".tablename('elapp_shop_saler')." s left join ".tablename('elapp_shop_member')." m on m.openid=s.openid
                     WHERE s.uniacid={$_W['uniacid']}");
        if (p("merch")) {
        $temp_merch_stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_merch_store') . ' where uniacid='.$_W['uniacid']);
        $temp_merch_saler = pdo_fetchall("SELECT s.id salerid, s.salername, m.nickname salernickname,s.openid FROM ".tablename('elapp_shop_merch_saler')." s left join ".tablename('elapp_shop_member')." m on m.openid=s.openid
                     WHERE s.uniacid={$_W['uniacid']}");
        }
        $gdata = pdo_fetchall("select * from " . tablename('elapp_shop_member_group') . " where uniacid = $uniacid");
        $ldata = pdo_fetchall("select * from " . tablename("elapp_shop_member_level") . " where uniacid = " . $uniacid);
        $gdata = array_column($gdata, NULL, "id");
        plog('order.op.export', "导出订单");
        $columns = array(
            array('title' => '订单编号', 'field' => 'ordersn', 'width' => 24),
            array('title' => '粉丝昵称', 'field' => 'nickname', 'width' => 12),
            array('title' => '会员id', 'field' => 'uid', 'width' => 12),
            array('title' => '会员姓名', 'field' => 'mrealname', 'width' => 12),
            array("title" => "会员等级", "field" => "levelname", "width" => 12),
            array('title' => 'openid', 'field' => 'openid', 'width' => 24),
            array('title' => '会员手机手机号', 'field' => 'mmobile', 'width' => 12),
            array('title' => '收货姓名(或自提人)', 'field' => 'realname', 'width' => 12),
            array('title' => '联系电话', 'field' => 'mobile', 'width' => 12),
            array('title' => '收货地址', 'field' => 'address_str', 'width' => 50),
            array('title' => '商品名称', 'field' => 'goods_title', 'width' => 24),
            array('title' => '商品编码', 'field' => 'goods_goodssn', 'width' => 12),
            array('title' => '商品规格', 'field' => 'goods_optiontitle', 'width' => 12),
            array('title' => '商品数量', 'field' => 'goods_total', 'width' => 12),
            array('title' => '商品原价', 'field' => 'oldprice', 'width' => 12),
            array('title' => '商品单价(折扣前)', 'field' => 'goods_price1', 'width' => 12),
            array('title' => '商品单价(折扣后)', 'field' => 'goods_price2', 'width' => 12),
            array('title' => '商品价格(折扣前)', 'field' => 'goods_rprice1', 'width' => 12),
            array('title' => '商品价格(折扣后)', 'field' => 'goods_rprice2', 'width' => 12),
            array('title' => '商品成本价', 'field' => 'goods_costprice', 'width' => 12),
            array('title' => '支付方式', 'field' => 'paytype', 'width' => 12),
            array('title' => '标签组', 'field' => 'groupname', 'width' => 12),
            array('title' => '付款渠道', 'field' => 'channel', 'width' => 12),
            array('title' => '微信支付单号', 'field' => 'transid', 'width' => 12),
            array('title' => '配送方式', 'field' => 'dispatchname', 'width' => 12),
            array('title' => '自提门店', 'field' => 'pickname', 'width' => 24),
            array('title' => '自提码', 'field' => 'verifycode', 'width' => 24),
            array('title' => '商品小计', 'field' => 'goodsprice', 'width' => 12),
            array('title' => '运费', 'field' => 'dispatchprice', 'width' => 12),
            array('title' => '积分抵扣', 'field' => 'deductprice', 'width' => 12),
            array('title' => '余额抵扣', 'field' => 'deductcredit2', 'width' => 12),
            array('title' => '满额立减', 'field' => 'deductenough', 'width' => 12),
            array('title' => '满件立减', 'field' => 'fulldeductenough', 'width' => 12),//满件优惠 Hlei20210430
            array('title' => '多商户满额立减', 'field' => 'merchdeductenough', 'width' => 12),
            array('title' => '优惠券优惠', 'field' => 'couponprice', 'width' => 12),
            array('title' => '订单改价', 'field' => 'changeprice', 'width' => 12),
            array('title' => '运费改价', 'field' => 'changedispatchprice', 'width' => 12),
            array('title' => '应收款', 'field' => 'price', 'width' => 12),
            array('title' => '状态', 'field' => 'status', 'width' => 12),
            array('title' => '维权金额', 'field' => 'applyprice', 'width' => 20),
            array('title' => '维权状态', 'field' => 'refundstatus', 'width' => 20),
            array('title' => '下单时间', 'field' => 'createtime', 'width' => 24),
            array('title' => '付款时间', 'field' => 'paytime', 'width' => 24),
            array('title' => '发货时间', 'field' => 'sendtime', 'width' => 24),
            array('title' => '完成时间', 'field' => 'finishtime', 'width' => 24),
            array('title' => '快递公司', 'field' => 'expresscom', 'width' => 24),
            array('title' => '快递单号', 'field' => 'expresssn', 'width' => 24),
            array('title' => '物流状态', 'field' => 'express_status_name', 'width' => 24),
            array('title' => '订单备注', 'field' => 'remark', 'width' => 36),
            array('title' => '卖家订单备注', 'field' => 'remarksaler', 'width' => 36),
            array('title' => '核销员', 'field' => 'salerinfo', 'width' => 24),
            array('title' => '核销门店', 'field' => 'storeinfo', 'width' => 36),
            array('title' => '订单自定义信息', 'field' => 'order_diyformdata', 'width' => 36),
            array('title' => '商品自定义信息', 'field' => 'goods_diyformdata', 'width' => 100),
        );
        //分销商字段导出
        if (!empty($agentid) && 0 < $level) {
            $columns[] = array('title' => '分销级别', 'field' => 'level', 'width' => 24);
            $columns[] = array('title' => '分销佣金', 'field' => 'commission', 'width' => 24);
        }
        //虚店店员字段导出
        if (0 < $clerkSet_level) {
            $clerkNameText = $clerkSet['texts']['clerk'] ? $clerkSet['texts']['clerk'] : '店员';
            $columns[] = array('title' => $clerkNameText.'MID', 'field' => 'clerkMID', 'width' => 24);
            $columns[] = array('title' => $clerkNameText.'姓名', 'field' => 'clerkRealname', 'width' => 24);
            $columns[] = array('title' => $clerkNameText.'等级', 'field' => 'clerkLevelname', 'width' => 24);
            $columns[] = array('title' => $clerkNameText.'收益', 'field' => 'clerkCommission', 'width' => 24);
        }
        //虚店店长字段导出
        if (0 < $ownerSet_level) {
            $columns[] = array('title' => '店长MID', 'field' => 'ownerMID', 'width' => 24);
            $columns[] = array('title' => '店长姓名', 'field' => 'ownerRealname', 'width' => 24);
            $columns[] = array('title' => '管理级别', 'field' => 'clerkStatus3', 'width' => 24);
            $columns[] = array('title' => '管理绩效', 'field' => 'ownerCommission', 'width' => 24);
        }
        //帮扶分红字段导出
        if (0 < $mentorSet['open']) {
            $mentorNameText = $mentorSet['texts']['agent'] ? $mentorSet['texts']['agent'] : '帮扶人';
            $columns[] = array('title' => $mentorNameText.'MID', 'field' => 'mentorMID', 'width' => 24);
            $columns[] = array('title' => $mentorNameText.'姓名', 'field' => 'mentorRealname', 'width' => 24);
            $columns[] = array('title' => $mentorNameText.'等级', 'field' => 'mentor_clerkLevelname', 'width' => 24);
            $columns[] = array('title' => '帮扶比例', 'field' => 'mentorCommission_ratio', 'width' => 24);
            $columns[] = array('title' => $mentorNameText.'收益', 'field' => 'mentorCommission', 'width' => 24);
        }
        //虚店合伙人字段导出
        if (0 < $copartnerSet_level) {
            $columns[] = array('title' => '机构ID', 'field' => 'copartner_id', 'width' => 24);
            $columns[] = array('title' => '机构名称', 'field' => 'copartnerMcnname', 'width' => 24);
            $columns[] = array('title' => '机构等级', 'field' => 'copartnerLevelname', 'width' => 24);
            $columns[] = array('title' => '岗位绩效', 'field' => 'copartnerCommission', 'width' => 24);
            $columns[] = array('title' => '机构经理ID', 'field' => 'copartner_account_id', 'width' => 24);
            $columns[] = array('title' => '机构经理名称', 'field' => 'copartnerAccountName', 'width' => 24);
        }
        //医生字段导出
        if (0 < $doctorSet_level) {
            $doctorNameText = $doctorSet['texts']['doctor'] ? $doctorSet['texts']['doctor'] : '医生';
            $columns[] = array('title' => $doctorNameText.'MID', 'field' => 'doctorMID', 'width' => 24);
            $columns[] = array('title' => $doctorNameText.'姓名', 'field' => 'doctorRealname', 'width' => 24);
            $columns[] = array('title' => $doctorNameText.'等级', 'field' => 'doctorLevelname', 'width' => 24);
            $columns[] = array('title' => $doctorNameText.'收益', 'field' => 'doctorCommission', 'width' => 24);
        }

        if ($merch_plugin) {
            $columns[] = array('title' => '商户名称', 'field' => 'merchname', 'width' => 24);
        }
        if ($supply_plugin) {
            //$columns[] = array('title' => '供应商名称', 'field' => 'supplyname', 'width' => 24);
        }
        if (p("subaccount")) {
            $columns[] = array("title" => "结算方式", "field" => "account_type", "width" => 24);
            $columns[] = array("title" => "分账状态", "field" => "account_status", "width" => 24);
        }
        $r_type = array( '0' => '退款', '1' => '退货退款', '2' => '换货');

        $exportlist = array();
        foreach ($list as &$row) {
            $row['realname'] = str_replace('=', "", $row['realname']);
            $row['nickname'] = str_replace('=', "", $row['nickname']);
            $row['nickname'] = str_replace("\"","\"\"",$row['nickname']);
            $row['ordersn'] = $row['ordersn'] . " ";
            //Hlei 商品维权状态判定
            $refund_type_text = $row['has_refunded']?$r_type[$row['rtype']].'申请':'';
            $row['refundstatus'] = $this->order_refund_status($refund_type_text,$row['order_refund_status']);
            if ($row['deductprice'] > 0) {
                $row['deductprice'] = "-" . $row['deductprice'];
            }
            if ($row['deductcredit2'] > 0) {
                $row['deductcredit2'] = "-" . $row['deductcredit2'];
            }
            if ($row['deductenough'] > 0) {
                $row['deductenough'] = "-" . $row['deductenough'];
            }
            //商城满件优惠 Hlei20210430
            if ($row['fulldeductenough'] > 0) {
                $row['fulldeductenough'] = "-" . $row['fulldeductenough'];
            }

            if (!empty($row['transid'])){
                $row['transid'] = $row['transid'] . '`';
            }
            if ($row['merchdeductenough'] > 0) {
                $row['merchdeductenough'] = "-" . $row['merchdeductenough'];
            }
            if ($row['changeprice'] < 0) {
                $row['changeprice'] = "-" . $row['changeprice'];
            } else if ($row['changeprice'] > 0) {
                $row['changeprice'] = "+" . $row['changeprice'];
            }
            if ($row['changedispatchprice'] < 0) {
                $row['changedispatchprice'] = "-" . $row['changedispatchprice'];
            } else if ($row['changedispatchprice'] > 0) {
                $row['changedispatchprice'] = "+" . $row['changedispatchprice'];
            }
            if ($row['couponprice'] > 0) {
                $row['couponprice'] = "-" . $row['couponprice'];
            }
            $row['nickname'] = strexists($row['nickname'],'^') ? "'".$row['nickname'] : $row['nickname'];
            $row['expresssn'] = strexists($row['expresssn'],'-') ? "`".$row['expresssn']." " : "'".$row['expresssn']." ";
            $row['expresssn'] = strexists($row['expresssn'],'=') ? "`".$row['expresssn']." " : $row['expresssn']." ";
            $row['createtime'] = date('Y-m-d H:i:s', $row['createtime']);
            $row['paytime'] = !empty($row['paytime']) ? date('Y-m-d H:i:s', $row['paytime']) : '';
            $row['sendtime'] = !empty($row['sendtime']) ? date('Y-m-d H:i:s', $row['sendtime']) : '';
            $row['finishtime'] = !empty($row['finishtime']) ? date('Y-m-d H:i:s', $row['finishtime']) : '';
            $row['salerinfo'] = "";
            $row['storeinfo'] = "";
            $row['pickname'] = "";
            if (p("subaccount")) {
                $row["account_type"] = $row["is_sub_account"] == 1 ? "自动分账" : "手动结算";
                $statusMap = array(-1 => "分账失败", 0 => "未分账", 1 => "已分账", -2 => "分账关闭");
                if ($row["is_sub_account"] == 1) {
                    $row["account_status"] = $statusMap[$row["sub_account_status"]];
                } else {
                    $row["account_status"] = "";
                }
            }
            // 核销订单或自提订单
            if ($row['isverify'] || !empty($row['storeid'])) {
                // 如果按订单核销（只记录一次，只有实体商品类型，而且主商城实体商品只能选择按订单核销）
                if ((!empty($row['verifyopenid']) && $row['verifytype'] == 0) ) {
                    if ($row['merchid'] > 0) {
                        foreach ($temp_merch_stores as $value) {
                            if ($value['id'] == $row['verifystoreid']) {
                                $row['storeinfo'] = "[".$value['storename']."]";
                                break;
                            }
                        }
                        foreach ($temp_merch_saler as $value) {
                            if ($row['verifyopenid'] == $value['openid']) {
                                $row['salerinfo'] = "[" . $value['salerid'] . "]" . $value['salername'] . "(" . $value['salernickname'] . ")";
                                break;
                            }
                        }
                    } else {
                        foreach ($temp_stores as $value) {
                            if ($value['id'] == $row['verifystoreid']) {
                                $row['storeinfo'] = "[".$value['storename']."]";
                                break;
                            }
                        }
                        foreach ($temp_saler as $value) {
                            if ($row['verifyopenid'] == $value['openid']) {
                                $row['salerinfo'] = "[" . $value['salerid'] . "]" . $value['salername'] . "(" . $value['salernickname'] . ")";
                                break;
                            }
                        }
                    }
                }else {
                    $orderid = $row['id'];
                    $ordersn = $row['ordersn'];
                    // 如果多商户（多商户一直记录正常）
                    if (strstr($ordersn, 'ME')) {
                        if (!empty($row['verifyinfo'])) {
                            $verifyinfo = iunserializer($row['verifyinfo']);
                            if (!empty($verifyinfo)) {
                                foreach ($verifyinfo as $k => $v) {
                                    $verifyopenid = $v['verifyopenid'];
                                    $verifystoreid = $v['verifystoreid'];

                                    if (!empty($verifyopenid)) {
                                        $verify_member = array();
                                        $verify_store = array();
                                        if ($row['merchid'] > 0) {
                                            foreach ($temp_merch_saler as $value) {
                                                if ($value['openid'] == $verifyopenid) {
                                                    $verify_member = $value;
                                                    break;
                                                }
                                            }
                                            foreach ($temp_merch_stores as $value) {
                                                if ($value['id'] == $verifystoreid) {
                                                    $verify_store = $value;
                                                    break;
                                                }
                                            }
                                        } else {
                                            foreach ($temp_saler as $value) {
                                                if ($value['openid'] == $verifyopenid) {
                                                    $verify_member = $value;
                                                    break;
                                                }
                                            }
                                            foreach ($temp_stores as $value) {
                                                if ($value['id'] == $verifystoreid) {
                                                    $verify_store = $value;
                                                    break;
                                                }
                                            }
                                        }
                                        $row['salerinfo'] .= "[" . $verify_member['salerid'] . "]" . $verify_member['salername'] . "(" . $verify_member['salernickname'] . ")";
                                        $row['storeinfo'] .= '[' .$verify_store['storename'] .']';
                                    }
                                }
                            }
                        }
                    } else {
                        $temp_verify_orderids[] = $orderid;
                        // 否则去表里查
                        //$sql = 'select s.storename,sa.salername,sa.id,m.nickname  from ' . tablename('elapp_shop_verifygoods_log') . '   vgl
                        //     left join ' . tablename('elapp_shop_verifygoods') . ' vg on vg.id = vgl.verifygoodsid
                        //     left join ' . tablename('elapp_shop_store') . ' s  on s.id = vgl.storeid
                        //     left join ' . tablename('elapp_shop_saler') . ' sa  on sa.id = vgl.salerid
                        //     left join ' . tablename('elapp_shop_order_goods') . ' og on vg.ordergoodsid = og.id
                        //     left join ' . tablename('elapp_shop_member') . ' m on m.openid = sa.openid
                        //     where  og.orderid='.$orderid.' ORDER BY vgl.verifydate DESC ';
                        //
                        //     $res = pdo_fetchall($sql);
                        //          foreach ($res as $k => $v) {
                        //               $row['salerinfo'] .= "[" . $v['id'] . "]" . $v['salername'] . "(".$v['nickname'].")";
                        //               $row['storeinfo'] .= '[' .$v['storename'] .']';
                        //          }
                    }
                }

                if (!empty($row['verifystoreid']) && $row['verifytype'] == 0) {
                    if ($row['merchid'] > 0) {
                        foreach ($temp_merch_stores as $value) {
                            if ($value['id'] == $row['verifystoreid']) {
                                $row['storeinfo'] = "[".$value['storename']."]";
                                break;
                            }
                        }
                    } else {
                        foreach ($temp_stores as $value) {
                            if ($value['id'] == $row['verifystoreid']) {
                                $row['storeinfo'] = "[".$value['storename']."]";
                                break;
                            }
                        }
                    }
                }
            }

            if (!empty($row['storeid'])) {

                if ($row['merchid'] > 0) {
                    foreach ($temp_merch_stores as $value) {
                        if ($value['id'] == $row['storeid']) {
                            $row['pickname'] = $value['storename'];
                            break;
                        }
                    }
                } else {
                    foreach ($temp_stores as $value) {
                        if ($value['id'] == $row['storeid']) {
                            $row['pickname'] = $value['storename'];
                            break;
                        }
                    }
                }
            }
            if (p('diyform')  && !empty($row['diyformfields']) && !empty($row['diyformdata'])) {
                $diyformdata_array = p('diyform')->getDatas(iunserializer($row['diyformfields']), iunserializer($row['diyformdata']));
                $diyformdata = "";
                foreach ($diyformdata_array as $da) {
                    $diyformdata.=$da['name'] . ": " . $da['value'] . "\r\n";
                }
                $row['order_diyformdata'] = $diyformdata;
            }


            foreach ($row['goods'] as $k => $g) {
                if ($k > 0 ) {
                    //$row['ordersn'] = '';
                    $row['realname'] = '';
                    $row['uid'] = '';
                    $row['mobile'] = '';
                    $row['openid'] = '';
                    $row['nickname'] = '';
                    $row['mrealname'] = '';
                    $row['mmobile'] = '';
                    $row['address'] = '';
                    $row['address_province'] = '';
                    $row['address_city'] = '';
                    $row['address_area'] = '';
                    $row['address_street'] = '';
                    $row['address_address'] = '';
                    //$row['paytype'] = '';
                    $row['dispatchname'] = '';
                    $row['dispatchprice'] = '';
                    $row['goodsprice'] = '';
                    //$row['status'] = '';
                    $row['createtime'] = '';
                    $row['sendtime'] = '';
                    $row['finishtime'] = '';
                    //如果是分包裹发货的，则不替换为空
                    if($g['sendtype'] >0 ){
                        $row['expresssn'] = $g['expresssn'].' ';
                        $row['express'] = $g['express'];
                    }else{
                        $row['expresssn'] = '';
                        $row['express'] = '';
                    }
                    $row['deductprice'] = '';
                    $row['deductcredit2'] = '';
                    $row['deductenough'] = '';
                    $row['fulldeductenough'] = '';//商城满件优惠 Hlei20210430
                    $row['changeprice'] = '';
                    $row['changedispatchprice'] = '';
                    $row['price'] = '';
                    $row['order_diyformdata'] = '';
                    $row['applyprice'] = '';
                }
                $row['goods_title'] = $g['title'];
                $row['goods_goodssn'] = $g['goodssn'];
                $row['goods_optiontitle'] = $g['optiontitle'];
                $row['goods_total'] = $g['total'];
                $row['goods_price1'] = $g['price'] / $g['total'];
                $row['goods_price2'] = $g['realprice'] / $g['total'];
                $row['goods_rprice1'] = $g['price'];
                $row['goods_rprice2'] = $g['realprice'];
                $row['goods_costprice'] = $g['costprice']==0?'':$g['costprice'];//成本价为零时不输出
                $row['goods_diyformdata'] = $g['goods_diyformdata'];
                //店员单独输出多规格收益
                $clerkCommissions = iunserializer($g['clerkCommissions']);
                if (!empty($clerkm1)) {
                    if (is_array($clerkCommissions)) {
                        $row['clerkCommission'] = isset($clerkCommissions['level1']) ? floatval($clerkCommissions['level1']) : 0;
                    } else {
                        $clerkc1 = iunserializer($g['clerkCommission1']);
                        $clerkl1 = $plugin_clerk->getLevel($clerkm1['openid']);
                        if (!empty($clerkc1)) {
                            $row['clerkCommission'] = isset($clerkc1['level' . $clerkl1['id']]) ? $clerkc1['level' . $clerkl1['id']] : $clerkc1['default'];
                        }
                    }
                }
                //医生单独输出多规格收益
                $doctorCommissions = iunserializer($g['doctor_commissions']);
                if (!empty($doctorm1)) {
                    if (is_array($doctorCommissions)) {
                        $row['doctorCommission'] = isset($doctorCommissions['level1']) ? floatval($doctorCommissions['level1']) : 0;
                    } else {
                        $doctorc1 = iunserializer($g['doctor_commission']);
                        $doctorl1 = $plugin_doctor->getLevel($doctorm1['openid']);
                        if (!empty($doctorc1)) {
                            $row['doctorCommission'] = isset($doctorc1['level' . $doctorl1['id']]) ? $doctorc1['level' . $doctorl1['id']] : $doctorc1['default'];
                        }
                    }
                }
                //帮扶单独输出多规格收益
                $mentorCommissions = iunserializer($g['mentor_commissions']);
                if (!empty($mentorMember)) {
                    if (is_array($mentorCommissions)) {
                        $row['mentorCommission'] = isset($mentorCommissions['dividend_price']) ? floatval($mentorCommissions['dividend_price']) : 0;
                    } else {
                        $mentorCommission = iunserializer($g['mentor_commission']);
                        $row['mentorCommission'] = isset($mentorCommission['dividend_price']) ? $mentorCommission['dividend_price'] : 0;
                    }
                }
                //机构单独输出多规格收益
                $copartnerCommissions = iunserializer($g['copartnerCommissions']);
                if (!empty($copartnerm1)) {
                    if (is_array($copartnerCommissions)) {
                        $row['copartnerCommission'] = isset($copartnerCommissions['level1']) ? floatval($copartnerCommissions['level1']) : 0;
                    } else {
                        $copartnerc1 = iunserializer($g['copartnerCommission1']);
                        $copartnerl1 = $plugin_copartner->getLevel($copartnerm1['id']);
                        if (!empty($copartnerc1)) {
                            $row['copartnerCommission'] = isset($copartnerc1['level' . $copartnerl1['id']]) ? $copartnerc1['level' . $copartnerl1['id']] : $copartnerc1['default'];
                        }
                    }
                }

                if ($g["hasoption"]) {
                    $row["oldprice"] = $g["op_productprice"] ?: 0;
                } else {
                    $row["oldprice"] = $g["productprice"] ?: 0;
                }

                $row['groupname'] = '';
                $row['channel'] = "";
                $groupId = is_array($row["groupid"]) ? $row["groupid"] : explode(",", $row["groupid"]);
                $row["groupid"] = array_filter($groupId);
                foreach ($gdata as $gval) {
                    if (!empty($row["groupid"]) && in_array($gval["id"], $row["groupid"])) {
                        $row["groupname"] .= $gval["groupname"] . "|";
                        }
                    }
                foreach ($ldata as $lval) {
                    if (!empty($row["level"]) && $lval["id"] == $row["level"]) {
                        $row["levelname"] = $lval["levelname"];
                    } else {
                        $row["levelname"] = "默认等级";
                    }
                }
                if(!empty($row['is_cashier'])){
                    $row['channel'] = "收银台";
                }else if(!empty($row['iswxappcreate'])){
                    $row['channel'] = "小程序";
                }else if (!empty($row['apppay'])){
                    $row['channel'] = "APP";
                }else if(false){ //Hlei 判断条件
                    $row['channel'] = "H5";
                }else {
                    $row['channel'] = "公众号";
                }

                //Hlei 单商品维权金额重新判定并覆盖
                $res_multi_arr = pdo_fetchall('select applyprice,rtype,status from ' . tablename('elapp_shop_order_single_refund') . ' where id=:id and uniacid=:uniacid limit 1', array(':id'=>$g['single_refundid'],':uniacid' => $_W['uniacid']));
                /*if(!empty($row['refundstate'])&&$row['refundstate'] == 3) {*/
                $is_single = false;
                foreach ($row["goods"] as $v) {
                    if (!empty($v["single_refundid"])) {
                        $is_single = true;
                    }
                }
                if (!empty($g['single_refundid'])){
                    $applyprice = 0;
                    $refundstatus = '';
                    if(is_array($res_multi_arr) && !empty($res_multi_arr[0])) {
                        $applyprice = $res_multi_arr[0]['applyprice'];
                        $singlerefund_status_text = !empty($g['single_refundid'])?$r_type[$res_multi_arr[0]['rtype']].'申请':'';
                        $refundstatus = $this->order_refund_status($singlerefund_status_text,$res_multi_arr[0]['status']);
                    }

                    $row['applyprice'] = $applyprice;
                    $row['refundstatus'] = $refundstatus;
                } else {
                    if ($is_single) {
                        $row["applyprice"] = 0;
                        $row["refundstatus"] = "";
                    }
                }
                $exportlist[] = $row;
            }
        }
        unset($row);

        // 计次核销商品的核销信息
        if(!empty($temp_verify_orderids)) {
            $sql = 'select og.orderid,s.storename,sa.salername,sa.id,m.nickname  from ' . tablename('elapp_shop_verifygoods_log') . '   vgl
                             left join ' . tablename('elapp_shop_verifygoods') . ' vg on vg.id = vgl.verifygoodsid
                             left join ' . tablename('elapp_shop_store') . ' s  on s.id = vgl.storeid
                             left join ' . tablename('elapp_shop_saler') . ' sa  on sa.id = vgl.salerid
                             left join ' . tablename('elapp_shop_order_goods') . ' og on vg.ordergoodsid = og.id
                             left join ' . tablename('elapp_shop_member') . ' m on m.openid = sa.openid
                             where  og.orderid in ('. implode(',', $temp_verify_orderids) .') ORDER BY vgl.verifydate DESC ';
            $res = pdo_fetchall($sql);

            foreach ($exportlist as &$row) {
                foreach ($res as $k => $v) {
                    if ($v['orderid'] == $row['id']) {
                        $row['salerinfo'] .= "[" . $v['id'] . "]" . $v['salername'] . "(".$v['nickname'].")";
                        $row['storeinfo'] .= '[' .$v['storename'] .']';
                    }
                }
            }
            unset($row);

        }        
        m('excel')->exportCSV($exportlist,array(
            "title" => "订单数据",
            "columns" => $columns
        ),ELAPP_SHOP_DATA .'orgdata/',$page,$exflag);
        
    }
    
}