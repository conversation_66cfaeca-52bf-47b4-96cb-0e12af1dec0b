<?php
namespace web\controller\order;
use web\controller\WebPage;
class OpController extends WebPage {
	/**
     * 删除订单
     * @param 
     * @return 
     */
    function delete() {
        global $_W, $_GPC;
        $status = intval($_GPC['status']);
        $orderid = intval($_GPC['id']);

        pdo_update('elapp_shop_order', array('deleted' => 1), array('id' => $orderid, 'uniacid' => $_W['uniacid']));
        plog('order.op.delete', "订单删除 ID: {$orderid}");
        show_json(1, webUrl('order', array('status' => $status)));
    }

    protected function opData() {
       
        global $_W, $_GPC;
        if($_POST['oms'] == 1){
           $id = $_POST['id']; 
        }else{
            $id = intval($_GPC['id']);
        }
        
        $item = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_order') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($item)) {
            if ($_W['isajax']) {
                show_json(0, "未找到订单!");
            }
            $this->message('未找到订单!', '', 'error');
        }

        $order_goods = pdo_fetchall('select single_refundstate from ' . tablename('elapp_shop_order_goods'). ' where orderid=:orderid and uniacid=:uniacid', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));

        $is_singlerefund=false;//是否正在维权
        foreach ($order_goods as $og){
            if(!$is_singlerefund && ($og['single_refundstate']==1 ||$og['single_refundstate']==2)){
                $is_singlerefund=true;//存在维权申请，需要处理后再进行后续操作
                break;
            }
        }
        
        return array('id' => $id, 'item' => $item,'is_singlerefund'=>$is_singlerefund);
    }
	/**
     * 订单改价
     * @param int 
     * @return bool
     */
    function changeprice() {

        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        //检查是否是代付,如果是拒绝改价
        $is_peerpay = false;
        $is_peerpay = m('order')->checkpeerpay($item['id']);
        if (!empty($is_peerpay)) show_json(0,'代付订单不能改价');

        if($item['ordersn2']>=100) $item['ordersn2'] = 0;
        if ($_W['ispost']) {
            $changegoodsprice = $_GPC['changegoodsprice'];
            if (!is_array($changegoodsprice)) {
                show_json(0, '未找到改价内容!');
            }

            if ($item['parentid'] > 0) {
                $parent_order = array();
                $parent_order['id'] = $item['parentid'];
            }


            $changeprice = 0;
            foreach ($changegoodsprice as $ogid => $change) {
                $changeprice+=floatval($change);
            }

            $dispatchprice = floatval($_GPC['changedispatchprice']);
            if ($dispatchprice < 0) {
                $dispatchprice = 0;
            }
            $orderprice = $item['price'] + $changeprice;
            $changedispatchprice = 0;
            if ($dispatchprice != $item['dispatchprice']) {
                //修改了运费
                $changedispatchprice = $dispatchprice - $item['dispatchprice'];
                $orderprice+=$changedispatchprice;
            }

            if ($orderprice < 0) {
                show_json(0, "订单实际支付价格不能小于0元!");
            }
            foreach ($changegoodsprice as $ogid => $change) {
                $og = pdo_fetch('select price,realprice from ' . tablename('elapp_shop_order_goods') . ' where id=:ogid and uniacid=:uniacid limit 1', array(':ogid' => $ogid, ':uniacid' => $_W['uniacid']));
                if (!empty($og)) {
                    $realprice = $og['realprice'] + $change;
                    if ($realprice < 0) {
                        show_json(0, '单个商品不能优惠到负数');
                    }
                }
            }
            $ordersn2 = $item['ordersn2'] + 1;
            if ($ordersn2 > 99) {
                show_json(0, '超过改价次数限额');
            }


            $orderupdate = array();
            if ($orderprice != $item['price']) {
                //订单价格变化
                $orderupdate['price'] = $orderprice;
                $orderupdate['ordersn2'] = $item['ordersn2'] + 1;

                if ($item['parentid'] > 0) {
                    $parent_order['price_change'] = $orderprice - $item['price'];
                }
            }
            //订单的价格变化值
            $orderupdate['changeprice'] = $item['changeprice'] + $changeprice;

            if ($dispatchprice != $item['dispatchprice']) {
                //运费变化
                $orderupdate['dispatchprice'] = $dispatchprice; //这次的运费变化
                $orderupdate['changedispatchprice']+=$changedispatchprice; //累计的运费变化

                if ($item['parentid'] > 0) {
                    $parent_order['dispatch_change'] = $changedispatchprice;
                }
            }
            if (!empty($orderupdate)) {
                pdo_update('elapp_shop_order', $orderupdate, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
            }
            $dividend = p('dividend');
            if ($dividend) {
                $dividend->checkOrderConfirm($item['id']);
            }
            //扶植分红
            $mentor = p('mentor');
            if ($mentor) {
                $mentor->checkOrderConfirm($item['id']);
            }
            if ($item['parentid'] > 0) {
                if (!empty($parent_order)) {
                    m('order')->changeParentOrderPrice($parent_order);
                    if ($dividend) {
                        $dividend->checkOrderConfirm($parent_order['id']);
                    }
                }
            }

            //修改商品价
            foreach ($changegoodsprice as $ogid => $change) {
                $og = pdo_fetch('select price,realprice,changeprice from ' . tablename('elapp_shop_order_goods') . ' where id=:ogid and uniacid=:uniacid limit 1', array(':ogid' => $ogid, ':uniacid' => $_W['uniacid']));
                if (!empty($og)) {
                    $realprice = $og['realprice'] + $change; //这次的变化
                    $changeprice = $og['changeprice'] + $change; //累计的变化
                    pdo_update('elapp_shop_order_goods', array('realprice' => $realprice, 'changeprice' => $changeprice), array('id' => $ogid));
                }
            }

            //修改商品佣金
            $pluginc = p('commission');
            if ($pluginc) {
                $pluginc->calculate($item['id'], true);
            }
            plog('order.op.changeprice', "订单号： {$item['ordersn']} <br/> 价格： {$item['price']} -> {$orderprice}");

            m('notice')->sendOrderChangeMessage($item['openid'],array(
                'title'=>'订单金额',
                'orderid'=>$item['id'],
                'ordersn'=>$item['ordersn'],
                'olddata'=>$item['price'],
                'data'=> round($orderprice ,2),
                'type'=>1
            ),'orderstatus');
            show_json(1);
        }
        //订单商品
        $order_goods = pdo_fetchall('select og.id,g.title,g.thumb,g.goodssn,og.goodssn as option_goodssn, g.productsn,og.productsn as option_productsn, og.total,og.price,og.optionname as optiontitle, og.realprice,og.oldprice from ' . tablename('elapp_shop_order_goods') . ' og ' . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid ' . ' where og.uniacid=:uniacid and og.orderid=:orderid ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));



        if (empty($item['addressid'])) {
            $user = unserialize($item['carrier']);
            $item['addressdata'] = array(
                'realname' => $user['carrier_realname'],
                'mobile' => $user['carrier_mobile']
            );
        }
        else {

            $user = iunserializer($item['address']);
            if (!is_array($user)) {
                $user = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_member_address') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $item['addressid'], ':uniacid' => $_W['uniacid']));
            }
            $user['address'] = $user['province'] . ' ' . $user['city'] . ' ' . $user['area'] . ' ' . $user['address'];

            $item['addressdata'] = array(
                'realname' => $user['realname'],
                'mobile' => $user['mobile'],
                'address' => $user['address'],
            );
        }
        include $this->template('order/op/changeprice');
    }
	/**
     * 订单确认付款
     * @param int 
     * @return bool
     */
    function pay($a = array(), $b = array()) {

        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if ($item['status'] > 1) {
            show_json(0, '订单已付款，不需重复付款！');
        }
        if (!empty($item['virtual']) && com('virtual')) {
            //虚拟物品自动发货
            com('virtual')->pay($item);
        } else {

            $time = time();
            $updateorder = array(
                'status' => 1,
                'paytype' => 11,
                'paytime' => $time
            );
            $isonlyverifygoods = m('order')->checkisonlyverifygoods($item['id']);

            if($isonlyverifygoods){
                $updateorder['status']=2;
                $updateorder['sendtime']= $time;
            }
            //确认付款先改状态，再设置库存
            pdo_update('elapp_shop_order', $updateorder, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));

            //设置库存,增加积分
            m('order')->setStocksAndCredits($item['id'], 1);

            //模板消息
            m('notice')->sendOrderMessage($item['id']);

            //打印机打印
            com_run('printer::sendOrderMessage',$item['id']);

            //发送赠送优惠券
            if (com('coupon')) {
                com('coupon')->sendcouponsbytask($item['id']); //订单支付
            }

            //优惠券返利
            if (com('coupon') && !empty($item['couponid'])) {
                com('coupon')->backConsumeCoupon($item['id']); //后台确认付款
            }

            //分销检测
            if (p('commission')) {
                p('commission')->checkOrderPay($item['id']);
            }

            //虚店店长
            if (p('vrshop')) {
                p('vrshop')->checkOrderPay($item['id']);
            }
            //虚店店员
            if (p('clerk')) {
                p('clerk')->checkOrderPay($item['id']);
            }
            //医生
            if (p('doctor')) {
                p('doctor')->checkOrderPay($item['id']);
            }
            //虚店合伙人
            if (p('copartner')) {
                p('copartner')->checkOrderPay($item['id']);
            }

            //抵消优惠券
            $plugincoupon = com('coupon');
            if ($plugincoupon) {
                $oid = $item['id'];
                $plugincoupon->useConsumeCoupon($oid);
            }
        }

        //创建记次时商品记录
        m('verifygoods')->createverifygoods($item['id']);

        plog('order.op.pay', "订单确认付款 ID: {$item['id']} 订单号: {$item['ordersn']}");
        show_json(1);
    }

    function close() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法关闭订单！');
        }

        if ($item['status'] == -1) {
            show_json(0, '订单已关闭，无需重复关闭！');
        } else if ($item['status'] >= 1) {
            show_json(0, '订单已付款，不能关闭！');
        }
        if ($_W['ispost']) {
            if (!empty($item['transid'])) {
                //changeWechatSend($item['ordersn'], 0, $_GPC['reson']);
            }
            $time = time();
            if ($item['refundstate'] > 0 && !empty($item['refundid'])) {

                $change_refund = array();
                $change_refund['status'] = -1;
                $change_refund['refundtime'] = $time;
                pdo_update('elapp_shop_order_refund', $change_refund, array('id' => $item['refundid'], 'uniacid' => $_W['uniacid']));
            }

            //返还抵扣积分
            if ($item['deductcredit'] > 0) {
                m('member')->setCredit($item['openid'], 'credit1', $item['deductcredit'], array('0', $_W['shopset']['shop']['name'] . "购物返还抵扣积分 积分: {$item['deductcredit']} 抵扣金额: {$item['deductprice']} 订单号: {$item['ordersn']}"));
            }

            //返还抵扣余额
            m('order')->setDeductCredit2($item);

            //退还优惠券
            if (com('coupon') && !empty($item['couponid'])) {
                com('coupon')->returnConsumeCoupon($item['id']); //后台关闭订单
            }

            m('order')->setStocksAndCredits($item['id'], 2);

            pdo_update('elapp_shop_order', array('status' => -1, 'refundstate' => 0, 'canceltime' => $time, 'remarkclose' => $_GPC['remark']), array('id' => $item['id'], 'uniacid' => $_W['uniacid']));

            if (!empty($item['virtual']) && $item['virtual'] != 0) {

                $goodsid = pdo_fetch('SELECT goodsid FROM '.tablename('elapp_shop_order_goods').' WHERE uniacid = '.$_W['uniacid'].' AND orderid = '.$item['id']);

                $typeid = $item['virtual'];
                $vkdata = ltrim($item['virtual_info'],'[');
                $vkdata = rtrim($vkdata,']');
                $arr = explode('}',$vkdata);
                foreach($arr as $k => $v){
                    if(!$v){
                        unset($arr[$k]);
                    }
                }
                $vkeynum = count($arr);

                //未付款卡密变为未使用
                pdo_query("update " . tablename('elapp_shop_virtual_data') . ' set openid="",usetime=0,orderid=0,ordersn="",price=0,merchid='.$item['merchid'].' where typeid=' . intval($typeid).' and orderid = '.$item["id"]);

                //模板减少使用数据
                pdo_query("update " . tablename('elapp_shop_virtual_type') . " set usedata=usedata-".$vkeynum." where id=" .intval($typeid));

            }

            //加入好物圈收藏
            $goodscircle = p('goodscircle');
            if($goodscircle){
                $goodscircle->updateOrder($item['openid'],$item['id']);
            }

            plog('order.op.close', "订单关闭 ID: {$item['id']} 订单号: {$item['ordersn']}");
            show_json(1);
        }
        include $this->template('order/op/close');
    }
    //订单取消付款
    function paycancel() {

        global $_W, $_GPC;

        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法取消付款！');
        }

        if ($item['status'] != 1) {
            show_json(0, '订单未付款，不需取消！');
        }
        if ($_W['ispost']) {

            //先设置库存，再更改状态,
            m('order')->setStocksAndCredits($item['id'], 2);

            pdo_update('elapp_shop_order', array(
                'status' => 0,
                'cancelpaytime' => time()
            ), array('id' => $item['id'], 'uniacid' => $_W['uniacid']));

            plog('order.op.paycancel', "订单取消付款 ID: {$item['id']} 订单号: {$item['ordersn']}");
            show_json(1);
        }
    }

    //普通商品确认收货
    function finish() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法确认收货！');
        }

        pdo_update('elapp_shop_order', array(
            'status' => 3,
            'finishtime' => time()
        ), array('id' => $item['id'], 'uniacid' => $_W['uniacid']));


        //商品全返
        m('order')->fullback($item['id']);
        if (p('ccard') && !empty($item['ccardid'])) {
            p('ccard')->setBegin($item['id'], $item['ccardid']);
        }

        //会员升级
        m('member')->upgradeLevel($item['openid'],$item['id']);

        //处理积分
        /*
         * 咖啡--2020-02-17修改，bug【12919】禅道
         * 原来 m('order')->setStocksAndCredits($item['id'], 3);
         * */
        m('order')->setStocksAndCredits($item['id'], 3,true);

        //余额赠送
        m('order')->setGiveBalance($item['id'], 1);

        //模板消息
        m('notice')->sendOrderMessage($item['id']);

        //打印机打印
        com_run('printer::sendOrderMessage',$item['id']);

        //发送赠送优惠券
        if (com('coupon')) {
            com('coupon')->sendcouponsbytask($item['id']); //订单支付
        }

        //优惠券返利
        if (!empty($item['couponid'])) {
            com('coupon')->backConsumeCoupon($item['id']); //后台收货
        }

        //排队全返
        if (p('lineup')) {
            p('lineup')->checkOrder($item);
        }

        //分销检测
        if (p('commission')) {
            p('commission')->checkOrderFinish($item['id']);
        }
        //店员检测
        if (p('clerk')) {
            p('clerk')->checkOrderFinish($item['id']);
        }
        //店长检测
        if (p('vrshop')) {
            p('vrshop')->checkOrderFinish($item['id']);
        }
        //合伙人检测
        if (p('copartner')) {
            p('copartner')->checkOrderFinish($item['id']);
        }
        //扶植分红检测
        if (p('mentor')) {
            p('mentor')->checkOrderFinish($item['id']);
        }
        //医生检测
        if (p('doctor')) {
            p('doctor')->checkOrderFinish($item['id']);
        }
        //抽奖
        if(p('lottery')){
            //type 1:消费 2:签到 3:任务 4:其他
            $res = p('lottery')->getLottery($item['openid'],1,array('money'=>$item['price'],'paytype'=>2));
            if($res){
                p('lottery')->getLotteryList($item['openid'],array('lottery_id'=>$res));
            }
        }

        //任务中心单笔满额
        if(p('task')){
            p('task')->checkTaskProgress($item['price'],'order_full','',$item['openid']);
        }

        //加入好物圈收藏
        $goodscircle = p('goodscircle');
        if($goodscircle){
            $goodscircle->updateOrder($item['openid'],$item['id']);
        }
		
        //会员分享检测
		if (p('userpromote')) {
            //会员消费积分检测
			p('userpromote')->setCredits($item['onmid'], $item['id']);
            //会员分享积分检测
			p('userpromote')->shopSetcredits($item['id']);
		}
			
        
        // 小程序订阅消息
        if (!empty($item['wxapp_allow_subscribe'])) {
            $template = explode(',', $item['wxapp_allow_subscribe']);
            if (in_array('receive', $template)) {
                $msgdata = array();
                if (strexists('sns_wa_', $item['openid'])) {
                    $openid = $item['openid'];
                } else {
                    $openid = pdo_fetchcolumn("select openid_wa from ".tablename('elapp_shop_member')." where openid='{$item['openid']}'");
                }
                $msgdata['ordersn'] = $item['ordersn'];
                $goods = pdo_fetchall("select og.goodsid,og.price,g.title,g.thumb,og.total,g.credit,og.optionid,og.optionname as optiontitle,g.isverify,g.storeids, og.realprice from " . tablename('elapp_shop_order_goods') . " og "
                    . " left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid "
                    . " where og.orderid=:orderid ", array(':orderid' => $item['id']));
                $title = '';
                foreach ($goods as $og) {
                    if (!empty($title)) {
                        $title .= "\n";
                    }
                    $title .=  $og['title'];
                    if (!empty($og['optiontitle'])) {
                        $title .= "(" . $og['optiontitle'].')';
                    }
                    $title .= ' 数量:' . $og['total'] . ' 总价: ' . $og['realprice'];
                }
                $msgdata['title'] = $title;
                $msgdata['time'] = date('Y年m月d日 H:i:s');
                $msgdata['page'] = '/pages/order/detail/index?id='.$item['id'];
                p('app')->sendSubscribeMessage($openid, $msgdata, 'receive');
            }
        }

        plog('order.op.finish', "订单完成 ID: {$item['id']} 订单号: {$item['ordersn']}");
        show_json(1);
    }
    //订单取消取货
    function fetchcancel() {

        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法取消取货！');
        }

        if ($item['status'] != 3) {
            show_json(0, '订单未取货，不需取消！');
        }
        pdo_update(
            'elapp_shop_order', array(
            'status' => 1,
            'finishtime' => 0
        ), array('id' => $item['id'], 'uniacid' => $_W['uniacid'])
        );
        plog('order.op.fetchcancel', "订单取消取货 ID: {$item['id']} 订单号: {$item['ordersn']}");
        show_json(1);
    }
    //订单取消发货
    function sendcancel() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法取消发货！');
        }

        $sendtype = intval($_GPC['sendtype']);
        if ($item['status'] != 2 && $item['sendtype'] == 0) {
            show_json(0, '订单未发货，不需取消发货！');
        }

        if ($_W['ispost']) {

            if (!empty($item['transid'])) {
                //changeWechatSend($item['ordersn'], 0, $_GPC['cancelreson']);
            }
            $remark = trim($_GPC['remark']);
            if(!empty($item['remarksend'])){
                $remark = $item['remarksend']."\r\n".$remark;
            }
            $data = array(
                'sendtime' => 0,
                'remarksend'=>$remark
            );
            //是否为包裹
            if($item['sendtype']>0){
                if(empty($sendtype)){
                    if(empty($_GPC['bundle'])){
                        show_json(0, "请选择您要修改的包裹！");
                    }
                    $sendtype = intval($_GPC['bundle']);
                }
                $data['sendtype'] = 0;
                pdo_update('elapp_shop_order_goods', $data, array('orderid' => $item['id'],'sendtype'=>$sendtype, 'uniacid' => $_W['uniacid']));
                $order = pdo_fetch("select sendtype from ".tablename('elapp_shop_order')." where id = ".$item['id']." and uniacid = ".$_W['uniacid']." ");
                pdo_update('elapp_shop_order', array('sendtype'=>$order['sendtype']-1,'status'=>1), array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
            }else{
                $data['status'] = 1;
                pdo_update('elapp_shop_order', $data, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
            }

            if ($item['paytype'] == 3) {
                //处理订单库存
                m('order')->setStocksAndCredits($item['id'], 2);
            }

            plog('order.op.sendcancel', "订单取消发货 ID: {$item['id']} 订单号: {$item['ordersn']} 原因: {$remark}");
            show_json(1);
        }
        //是否存在包裹
        $sendgoods = array();
        $bundles = array();
        if($sendtype>0){
            $sendgoods = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                . ' where og.uniacid=:uniacid and og.orderid=:orderid and og.sendtype='.$sendtype.' ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
        }else{
            if($item['sendtype']>0){
                for($i=1;$i<=intval($item['sendtype']);$i++){
                    $bundles[$i]['goods'] = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                        . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                        . ' where og.uniacid=:uniacid and og.orderid=:orderid and og.sendtype='.$i.' ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
                    $bundles[$i]['sendtype'] = $i;
                    if(empty($bundles[$i]['goods'])){
                        unset($bundles[$i]);
                    }
                }
            }
        }
        include $this->template('order/op/sendcancel');
    }

    //虚拟商品收货
    function fetch() {

        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法确认取货！');
        }

        if ($item['status'] != 1) {
            show_json(0, '订单未付款，无法确认取货！');
        }
        $time = time();
        $d = array(
            'status' => 3,
            'sendtime' => $time,
            'finishtime' => $time
        );

        if ($item['isverify'] == 1) {
            $d['verified'] = 1;
            $d['verifytime'] = $time;
            $d['verifyopenid'] = "";
        }
        pdo_update(
            'elapp_shop_order', $d, array('id' => $item['id'], 'uniacid' => $_W['uniacid'])
        );

        //商品全返
        m('order')->fullback($item['id']);
        //发送赠送优惠券
        if (com('coupon')) {
            com('coupon')->sendcouponsbytask($item['id']); //订单支付
        }

        //优惠券返利
        if (!empty($item['couponid'])) {
            com('coupon')->backConsumeCoupon($item['id']); //后台收货
        }

        //取消退款状态
        if (!empty($item['refundid'])) {
            $refund = pdo_fetch('select * from ' . tablename('elapp_shop_order_refund') . ' where id=:id limit 1', array(':id' => $item['refundid']));
            if (!empty($refund)) {
                pdo_update('elapp_shop_order_refund', array('status' => -1), array('id' => $item['refundid']));
                pdo_update('elapp_shop_order', array('refundstate' => 0), array('id' => $item['id']));
            }
        }



        $log = "订单确认取货";
        if (p('ccard') && !empty($item['ccardid'])) {
            p('ccard')->setBegin($item['id'], $item['ccardid']);
            $log = "订单确认充值";
        }

        $log .=  " ID: {$item['id']} 订单号: {$item['ordersn']}";

        //余额赠送
        m('order')->setGiveBalance($item['id'], 1);

        //处理积分
        m('order')->setStocksAndCredits($item['id'], 3);

        //会员升级
        m('member')->upgradeLevel($item['openid'],$item['id']);

        //模板消息
        m('notice')->sendOrderMessage($item['id']);

        //分销佣金
        if (p('commission')) {
            p('commission')->checkOrderFinish($item['id']);
        }
        //店员
        if (p('clerk')) {
            p('clerk')->checkOrderFinish($item['id']);
        }
        //医生
        if (p('doctor')) {
            p('doctor')->checkOrderFinish($item['id']);
        }
        //店长
        if (p('vrshop')) {
            p('vrshop')->checkOrderFinish($item['id']);
        }
        //合伙人
        if (p('copartner')) {
            p('copartner')->checkOrderFinish($item['id']);
        }
        //扶植分红
        if (p('mentor')) {
            p('mentor')->checkOrderFinish($item['id']);
        }        
        //会员分享检测
		if (p('userpromote')) {
            //会员分享积分检测
			p('userpromote')->setCredits($item['onmid'], $item['id']);
            //会员消费积分检测
			p('userpromote')->shopSetcredits($item['id']);
		}
        //加入好物圈收藏
        $goodscircle = p('goodscircle');
        if($goodscircle){
            $goodscircle->updateOrder($item['openid'],$item['id']);
        }

        plog('order.op.fetch', $log);
        show_json(1);
    }
    //普通商品订单确认发货
    function send() {
        
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if($is_singlerefund){
            show_json(0, '订单商品存在维权，无法发货！');
        }

        if (empty($item['addressid'])) {
            show_json(0, '无收货地址，无法发货！');
        }
        if ($item['paytype'] != 3) {
            if ($item['status'] != 1) {
                show_json(0, '订单未付款，无法发货！');
            }
        }
        if ($_W['ispost']) {
            //如果不是同城配送订单
            if($item['city_express_state']==0){
                    if (!empty($_GPC['isexpress']) && empty($_GPC['expresssn'])) {
                        show_json(0, '请输入快递单号！');
                    }
                    if (!empty($item['transid'])) {
                        //changeWechatSend($item['ordersn'], 1);
                    }

                    $time = time();
                    $data = array(
                        'sendtype' => $item['sendtype'] > 0 ? $item['sendtype'] : intval($_GPC['sendtype']),
                        'express' => trim($_GPC['express']),
                        'expresscom' => trim($_GPC['expresscom']),
                        'expresssn' => trim($_GPC['expresssn']),
                        'sendtime' => $time
                    );
                    //判断是否为包裹发货
                    if(intval($_GPC['sendtype'])==1 || $item['sendtype'] > 0){
                        if(empty($_GPC['ordergoodsid'])){
                            show_json(0,"请选择发货商品！");
                        }
                        $ogoods = array();
                        $ogoods = pdo_fetchall("select sendtype from ".tablename('elapp_shop_order_goods')."
                        where orderid = ".$item['id']." and uniacid = ".$_W['uniacid']." order by sendtype desc ");

                        $senddata = array(
                            'sendtype'=>$ogoods[0]['sendtype']+1,
                            'sendtime'=>$data['sendtime']
                        );
                        $data['sendtype'] = $ogoods[0]['sendtype']+1;
                        $goodsid = $_GPC['ordergoodsid'];
                        foreach($goodsid as $key => $value){
                            pdo_update('elapp_shop_order_goods', $data, array('id'=>$value, 'uniacid' => $_W['uniacid']));
                        }
                        //查询是否有未发货包裹
                        $send_goods = pdo_fetch("select * from ".tablename('elapp_shop_order_goods')."
                        where orderid = ".$item['id']." and sendtype = 0 and single_refundstate<>9 and uniacid = ".$_W['uniacid']." limit 1 ");
                        //如果包裹都发货，修改订单状态
                        if(empty($send_goods)){
                            $senddata['status'] = 2 ;
                        }
                        $senddata['refundid'] =0 ;
                        pdo_update('elapp_shop_order', $senddata, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
                    }else{
                        $data['status'] = 2;
                        $data['refundid'] = 0;
                        pdo_update('elapp_shop_order', $data, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
                    }
                }else{
                $cityexpress = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_city_express') . " WHERE uniacid=:uniacid AND merchid=:merchid",array(":uniacid"=>$_W['uniacid'],":merchid"=>0));
                if($cityexpress['express_type']==1){
                    $ret = pdo_fetchall('select goodsid , total , optionid from ' . tablename('elapp_shop_order_goods') . ' where orderid = :orderid and uniacid = :uniacid',array(':uniacid'=>$_W['uniacid'],':orderid'=>$item['id']));
                    $weight = 0;
                foreach ($ret as $key=>$val)
                {
                    if ($val['optionid'] > 0)
                    {
                        $weight += pdo_fetchcolumn(' select weight*' . $val['total'] . ' from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:id and id = :optionid',array(':id'=>$val['goodsid'],':optionid'=>$val['optionid']));
                    }else
                    {
                        $weight += (pdo_fetchcolumn("select weight* ".$val['total']." from " . tablename('elapp_shop_goods'). ' where id=:id',array(':id'=>$val['goodsid'])));
                    }
                }
                    if ($weight == 0)
                    {
                        show_json(0,'重量必须大于0');
                    }
                    $item['weight'] = $weight;
                    $dada=m('order')->dada_send($item);
                    if($dada['state']==0){
                        show_json(0, $dada['result']);
                    }else{
                        $data['status'] = 2;
                        $data['refundid'] = 0;
                        $data['sendtime'] = time();
                        pdo_update('elapp_shop_order', $data, array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
                    }
                }else{
                    if ($cityexpress["express_type"] == 3) {
                        $data = m("common")->getPluginset("makeexpress");
                        if (empty($data)) {
                            show_json(0, "未开启码科配送");
                        }
                        $model = ELAPP_SHOP_PLUGIN . strtolower("makeexpress") . "/core/model.php";
                        if (is_file($model)) {
                            require_once $model;
                        }
                        $make = new MakeexpressModel($data);
                        $buyerInfo = unserialize($item["address"]);
                        $thirdData = array("fromcoord" => $cityexpress["lat"] . "," . $cityexpress["lng"], "tocoord" => $buyerInfo["lat"] . "," . $buyerInfo["lng"], "shop_id" => $_W["uniacid"]);
                        $res = $make->getOrderPrice($thirdData);
                        $shopAddress = m("common")->getSysset("contact");
                        $thirdData = array("shop_id" => $_W["uniacid"], "goods_name" => "商城商品", "pick_time" => date("Y-m-d H:i:s"), "remark" => "码科跑腿", "address" => json_encode(array("begin_detail" => $shopAddress["address"], "begin_address" => $shopAddress["province"] . $shopAddress["city"] . $shopAddress["address"], "begin_lat" => $cityexpress["lat"], "begin_lng" => $cityexpress["lng"], "begin_username" => $_W["shopset"]["shop"]["name"] ? $_W["shopset"]["shop"]["name"] : "", "begin_phone" => $cityexpress["tel1"] ? $cityexpress["tel1"] : $cityexpress["tel2"], "end_detail" => $buyerInfo["address"], "end_address" => $buyerInfo["province"] . $buyerInfo["city"] . $buyerInfo["area"], "end_lat" => $buyerInfo["lat"], "end_lng" => $buyerInfo["lng"], "end_username" => $buyerInfo["realname"], "end_phone" => $buyerInfo["mobile"])), "pay_price" => $res["total_price"], "total_price" => $res["total_price"], "notify_url" => $_W["siteroot"] . 'app.php/payment.wechat.notify/main?i=' . $_W['uniacid']);
                        $res = $make->createOrder($thirdData);
                        if (empty($res)) {
                            show_json(0, "码科配送错误");
                        } else {
                            unset($data);
                            $data["status"] = 2;
                            $data["refundid"] = 0;
                            $data["sendtime"] = time();
                            pdo_update("elapp_shop_order", $data, array("id" => $item["id"], "uniacid" => $_W["uniacid"]));
                        }
                    } else {
                        $data["status"] = 2;
                        $data["refundid"] = 0;
                        $data["sendtime"] = time();
                        pdo_update("elapp_shop_order", $data, array("id" => $item["id"], "uniacid" => $_W["uniacid"]));
                    }
                }
            }
            //取消退款状态
            if (!empty($item['refundid'])) {
                $refund = pdo_fetch('select * from ' . tablename('elapp_shop_order_refund') . ' where id=:id limit 1', array(':id' => $item['refundid']));
                if (!empty($refund)) {
                    pdo_update('elapp_shop_order_refund', array('status' => -1, 'endtime' => $time), array('id' => $item['refundid']));
                    pdo_update('elapp_shop_order', array('refundstate' => 0), array('id' => $item['id']));
                }
            }

            if ($item['paytype'] == 3) {
                //处理订单库存
                m('order')->setStocksAndCredits($item['id'], 1);
            }

            //加入好物圈收藏
            $goodscircle = p('goodscircle');
            if($goodscircle){
                $goodscircle->updateOrder($item['openid'],$item['id']);
            }

            //模板消息
            m('notice')->sendOrderMessage($item['id']);
            
            // 判断是否发送小程序订阅消息
            if(!empty($item['wxapp_allow_subscribe'])) {
                $template = explode(',', $item['wxapp_allow_subscribe']);
                if (in_array('send', $template)) {
                    $msgdata = array();
                    $msgdata['ordersn'] = $item['ordersn'];
                    $goods = pdo_fetchall("select og.goodsid,og.price,g.title,g.thumb,og.total,g.credit,og.optionid,og.optionname as optiontitle,g.isverify,g.storeids, og.realprice from " . tablename('elapp_shop_order_goods') . " og "
                        . " left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid "
                        . " where og.orderid=:orderid ", array(':orderid' => $item['id']));
                    $title = '';
                    foreach ($goods as $og) {
                        if (!empty($title)) {
                            $title .= "\n";
                        }
                        $title .=  $og['title'];
                        if (!empty($og['optiontitle'])) {
                            $title .= "(" . $og['optiontitle'].')';
                        }
                        $title .= ' 数量:' . $og['total'] . ' 总价: ' . $og['realprice'];
                    }
                    $msgdata['title'] = $title;
                    $msgdata['express'] = $_GPC['expresscom'] ? : '其他快递';
                    $msgdata['expresssn'] = trim($_GPC['expresssn']);
                    $msgdata['time'] = date('Y年m月d日 H:i:s');
                    $msgdata['page'] = '/pages/order/detail/index?id='.$item['id'];
                    if (p('app')) {
                        if (strexists('sns_wa', $item['openid'])) {
                            $openid = $item['openid'];
                        } else {
                            $openid = pdo_fetchcolumn("select openid_wa from ".tablename('elapp_shop_member')." where openid='{$item['openid']}'");
                        }
                        p('app')->sendSubscribeMessage($openid, $msgdata, 'send');
                    }
                }
            }
            
            plog('order.op.send', "订单发货 ID: {$item['id']} 订单号: {$item['ordersn']} <br/>快递公司: {$_GPC['expresscom']} 快递单号: {$_GPC['expresssn']}");
            show_json(1, ['url' => webUrl('order/list/main')]);
        }
        //订单商品
        $noshipped = array();
        $shipped = array();
        if($item['sendtype']>0){
            //未发货商品
            $noshipped = pdo_fetchall('select og.id,g.title,g.thumb,og.sendtype,g.ispresell,g.supplyid,g.isSupplySend,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                . ' where og.uniacid=:uniacid and og.sendtype = 0 and og.orderid=:orderid and og.single_refundstate<>9 ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));

            //已发货商品
            for($i=1;$i<=$item['sendtype'];$i++){
                $shipped[$i]['sendtype'] = $i;
                $shipped[$i]['goods'] = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell,g.supplyid,g.isSupplySend,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                    . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                    . ' where og.uniacid=:uniacid and og.sendtype = '.$i.' and og.orderid=:orderid ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
            }

        }

        $order_goods = pdo_fetchall('select og.id,g.title,g.thumb,og.sendtype,g.ispresell,g.supplyid,g.isSupplySend,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
            . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
            . ' where og.orderid=:orderid and og.uniacid=:uniacid and og.single_refundtime=0', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
        $address = iunserializer($item['address']);
        if (!is_array($address)) {
            $address = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_member_address') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $item['addressid'], ':uniacid' => $_W['uniacid']));
        }
        $express_list = m('express')->getExpressList();

        include $this->template('order/op/send');
    }

    function remarksaler() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        if ($_W['ispost']) {
            pdo_update('elapp_shop_order', array('remarksaler' => $_GPC['remark']), array('id' => $item['id'], 'uniacid' => $_W['uniacid']));
            plog('order.op.remarksaler', "订单备注 ID: {$item['id']} 订单号: {$item['ordersn']} 备注内容: " . $_GPC['remark']);
            show_json(1);
        }
        include $this->template('order/op/remarksaler');
    }

    function changeexpress() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);
        $changeexpress=1;
        $sendtype = intval($_GPC['sendtype']);


        $edit_flag = 1;
        if ($_W['ispost']) {

            $express = $_GPC['express'];
            $expresscom = $_GPC['expresscom'];
            $expresssn = trim($_GPC['expresssn']);

            if (empty($id)) {
                $ret = "参数错误！";
                show_json(0, $ret);
            }

            if (!empty($expresssn)) {
                $change_data = array();
                $change_data['express'] = $express;
                $change_data['expresscom'] = $expresscom;
                $change_data['expresssn'] = $expresssn;
                //是否为包裹发货
                if($item['sendtype']>0){
                    if(empty($sendtype)){
                        if(empty($_GPC['bundle'])){
                            show_json(0, "请选择您要修改的包裹！");
                        }
                        $sendtype = intval($_GPC['bundle']);
                    }
                    pdo_update('elapp_shop_order_goods', $change_data, array('orderid' => $id,'sendtype'=>$sendtype, 'uniacid' => $_W['uniacid']));
                }else{
                    pdo_update('elapp_shop_order', $change_data, array('id' => $id, 'uniacid' => $_W['uniacid']));
                }
				plog('order.op.changeexpress', "修改快递状态 ID: {$item['id']} 订单号: {$item['ordersn']} 快递公司: {$expresscom} 快递单号: {$expresssn}");
				
                show_json(1);
            } else {
                show_json(0, "请填写快递单号！");
            }
        }
        //是否存在包裹
        $sendgoods = array();
        $bundles = array();
        if($sendtype>0){
            $sendgoods = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                . ' where og.uniacid=:uniacid and og.orderid=:orderid and og.sendtype='.$sendtype.' ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
        }else{
            if($item['sendtype']>0){
                for($i=1;$i<=intval($item['sendtype']);$i++){
                    $bundles[$i]['goods'] = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell,g.medicineAttributeID from ' . tablename('elapp_shop_order_goods') . ' og '
                        . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                        . ' where og.uniacid=:uniacid and og.orderid=:orderid and og.sendtype='.$i.' ', array(':uniacid' => $_W['uniacid'], ':orderid' => $item['id']));
                    $bundles[$i]['sendtype'] = $i;
                    if(empty($bundles[$i]['goods'])){
                        unset($bundles[$i]);
                    }
                }
            }
        }

        $address = iunserializer($item['address']);
        if (!is_array($address)) {
            $address = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_member_address') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $item['addressid'], ':uniacid' => $_W['uniacid']));
        }

        $express_list = m('express')->getExpressList();

        include $this->template('order/op/send');
    }

    function changeaddress() {
        global $_W, $_GPC;
        $opdata = $this->opData();
        extract($opdata);

        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $address_street = intval($area_set['address_street']);

        if (empty($item['addressid'])) {
            $user = unserialize($item['carrier']);
        } else {
            $user = iunserializer($item['address']);

            if (!is_array($user)) {
                $user = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_member_address') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $item['addressid'], ':uniacid' => $_W['uniacid']));
            }
            $address_info = $user['address'];
            $user_address = $user['address'];
            $user['address'] = $user['province'] . ' ' . $user['city'] . ' ' . $user['area'] . ' ' . $user['street'] . ' ' . $user['address'];
            $item['addressdata'] =$oldaddress = array(
                'realname' => $user['realname'],
                'mobile' => $user['mobile'],
                'address' => $user['address'],
            );
        }

        if ($_W['ispost']) {
            $realname = $_GPC['realname'];
            $mobile = $_GPC['mobile'];
            $province = $_GPC['province'];
            $city = $_GPC['city'];
            $area = $_GPC['area'];
            $street = $_GPC['street'];
            $changead = intval($_GPC['changead']);
            $address = trim($_GPC['address']);

            if (!empty($id)) {
                if (empty($realname)) {
                    $ret = "请填写收件人姓名！";
                    show_json(0, $ret);
                }

                if (empty($mobile)) {
                    $ret = "请填写收件人手机！";
                    show_json(0, $ret);
                }

                if ($changead) {
                    if ($province == '请选择省份') {
                        $ret = "请选择省份！";
                        show_json(0, $ret);
                    }

                    if (empty($address)) {
                        $ret = "请填写详细地址！";
                        show_json(0, $ret);
                    }
                }

                $item = pdo_fetch("SELECT id, ordersn, address,openid FROM " . tablename('elapp_shop_order') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $id, ':uniacid' => $_W['uniacid']));
                $address_array = iunserializer($item['address']);

                $address_array['realname'] = $realname;
                $address_array['mobile'] = $mobile;

                if ($changead) {
                    $address_array['province'] = $province;
                    $address_array['city'] = $city;
                    $address_array['area'] = $area;
                    $address_array['street'] = $street;
                    $address_array['address'] = $address;
                } else {
                    $address_array['province'] = $user['province'];
                    $address_array['city'] = $user['city'];
                    $address_array['area'] = $user['area'];
                    $address_array['street'] = $user['street'];
                    $address_array['address'] = $user_address;
                }

                $address_array = iserializer($address_array);

                pdo_update('elapp_shop_order', array('address' => $address_array), array('id' => $id, 'uniacid' => $_W['uniacid']));

				plog('order.op.changeaddress', "修改收货地址 ID: {$item['id']} 订单号: {$item['ordersn']} <br>原地址: 收件人: {$oldaddress['realname']} 手机号: {$oldaddress['mobile']} 收件地址: {$oldaddress['address']}<br>新地址: 收件人: {$realname} 手机号: {$mobile} 收件地址: {$province} {$city} {$area} {$address}");

                m('notice')->sendOrderChangeMessage($item['openid'],array(
                    'title'=>'订单收货地址',
                    'orderid'=>$item['id'],
                    'ordersn'=>$item['ordersn'],
                    'olddata'=>$oldaddress['address'],
                    'data'=>"{$province}{$city}{$area}{$address}",
                    'type'=>0
                ),'orderstatus');
				
                show_json(1);
            }
        }
        include $this->template('order/op/changeaddress');
    }

    function uploadInvoice(){
        global $_W,$_GPC;
        $order_id = $_GPC['order_id'];
        if (!$_W['ispost']){
            $invoice = pdo_fetch('select invoicename,invoice_img from '.tablename('elapp_shop_order').' where id = :order_id and uniacid = :uniacid limit 1',array(':order_id'=>$order_id, ':uniacid'=>$_W['uniacid']));
            $invoice_info = m('sale')->parseInvoiceInfo($invoice['invoicename']);
            if ($invoice_info['title']){
                if ($invoice_info['entity']){
                    show_json(0,'本单不支持电子发票');
                }
                include $this->template('order/op/upload_invoice');
                return;
            }
            show_json(0,'本单不支持电子发票');
        }
        $invoice_img = $_GPC['invoice_img'];
        if (empty($invoice_img)){
            show_json(0,'请选择图片');
        }
        $invoice_img = save_media($invoice_img);
        $update_ret = pdo_update('elapp_shop_order', array('invoice_img'=>$invoice_img), array('id'=>$order_id));
        $update_ret ? show_json(1):show_json(0,'电子发票上传失败，请重试');
    }

    //订单代付
    function peerpay() {
        global $_W,$_GPC;
        $order_id = $_GPC['id'];
        if(empty($order_id)){
            show_json(0,'参数错误');
        }
        $peerpay = m('order')->checkpeerpay($order_id);
        if(!$peerpay){
            show_json(0,'非代付订单');
        }

        $sql = "SELECT * FROM ".tablename('elapp_shop_order_peerpay_payinfo')." where pid=:pid";
        $list  = pdo_fetchall($sql, array(':pid'=>$peerpay['id']));

        include $this->template('order/op/peerpay');
    }

    /**
     * 订单修改绑定店员
     */
    function changeClerk() {
        global $_W, $_GPC;
        $id  = intval($_GPC['id']);
        $clerk_id = intval($_GPC['clerk_id']);

        if (empty($clerk_id)) {
            show_json(0, '请选择要绑定的店员');
        }
        $clerk = pdo_fetch('select id,copartner_id,copartner_account_id from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $clerk_id, ':uniacid' => $_W['uniacid']));
        if (empty($clerk)) {
            show_json(0, '店员未找到');
        }
        // 判断订单是否存在
        $order = pdo_fetch('select id,clerk_id,copartner_id,copartner_account_id from ' . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($order)) {
            show_json(0, '订单未找到');
        }
        // copartner_id 和 copartner_account_id 等于 绑定店员的copartner_id 和 copartner_account_id
        $update = array('clerk_id' => $clerk_id, 'copartner_id' => $clerk['copartner_id'], 'copartner_account_id' => $clerk['copartner_account_id'], 'copartner_user_path' => $clerk['copartner_user_path'], 'org_id' => $clerk['org_id'], 'org_sub_id' => $clerk['org_sub_id']);

        pdo_update('elapp_shop_order', $update, array('id' => $id, 'uniacid' => $_W['uniacid']));
        show_json(1, '更新成功');
    }

    /**
     * 订单修改绑定医生
     */
    function changeDoctor() {
        global $_W, $_GPC;
        $id  = intval($_GPC['id']);
        $doctor_id = intval($_GPC['doctor_id']);

        if (empty($doctor_id)) {
            show_json(0, '请选择要绑定的医生');
        }
        $doctor = pdo_fetch('select id,copartner_id,copartner_account_id from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $doctor_id, ':uniacid' => $_W['uniacid']));
        if (empty($doctor)) {
            show_json(0, '医生未找到');
        }
        // 判断订单是否存在
        $order = pdo_fetch('select id,doctor_id,copartner_id,copartner_account_id from ' . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($order)) {
            show_json(0, '订单未找到');
        }
        // copartner_id 和 copartner_account_id 等于 绑定店员的copartner_id 和 copartner_account_id
        $update = array('doctor_id' => $doctor_id, 'copartner_id' => $doctor['copartner_id'], 'copartner_account_id' => $doctor['copartner_account_id'], 'copartner_user_path' => $doctor['copartner_user_path'], 'org_id' => $doctor['org_id'], 'org_sub_id' => $doctor['org_sub_id']);

        pdo_update('elapp_shop_order', $update, array('id' => $id, 'uniacid' => $_W['uniacid']));
        show_json(1, '更新成功');
    }
}
