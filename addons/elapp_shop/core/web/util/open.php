<?php
namespace web\controller\util;
use web\controller\WebPage;
class OpenController extends WebPage{
	public function main(){
		global $_W,$_GPC;
		$plugin = $_GPC['plugin'];
		$title = $_GPC['title'];
		$domain = trim(preg_replace('/http(s)?:\\/\\//', '', trim($_GPC['domain'], '/')));
		$key = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_open_plugin') . ' WHERE plugin = :plugin', array(':plugin' => $plugin));
		if (empty($plugin)) {
			header('Location: ' . webUrl());
		}
		$redis = redis();
		if (!function_exists('redis') || is_error($redis)) {
			$this->message('请联系管理员开启 redis 支持，才能使用第三方插件', '', 'error');
			exit();
		}
		if ($_W['ispost']) {
			$key = $_GPC['key'];
			if ($key) {
				$redis_key = $plugin;
				if (!is_error($redis)) {
					if ($redis->setnx($redis_key, time())) {
						$redis->expireAt($redis_key, time() + 172800);
					}
				}
				$num = pdo_get('elapp_shop_open_plugin', array('plugin' => $plugin), 'status');
				if (!empty($num)) {
					pdo_update('elapp_shop_open_plugin', array('status' => 1, 'expirtime' => time() + 172800), array('plugin' => $plugin));
					show_json(1, array('url' => webUrl($plugin)));
				}
				$data = array('plugin' => $plugin, 'key' => $key, 'expirtime' => time(), 'status' => 1, 'url' => json_encode($_GPC['url']), 'domain' => $domain);
				pdo_insert('elapp_shop_open_plugin', $data);
				$id = pdo_insertid();
				if ($id) {
					show_json(1, array('url' => webUrl($plugin)));
				}
			}
		}
		include $this->template('util/open');
	}	
}