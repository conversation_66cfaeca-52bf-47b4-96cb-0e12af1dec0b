<?php
namespace web\controller\store;
use web\controller\ComWebPage;

class StatisticController extends ComWebPage {
	public function __construct($_com = 'verify') {
		parent::__construct($_com);
	}

	public function main() {
		global $_W,$_GPC;
		$days = array(1, 7, 30);
		$info = array();
		$list = array();
		foreach ($days as $day) {
			$total = array();
			$top10 = array();
			$result = m('statistic')->o2oorderstatistic($day);
			$total['ordernum'] = $result['total'];
			$top10['ordernum'] = $result['top10'];
			$result = m('statistic')->o2osalestatistic($day);
			$total['salesnum'] = $result['total'];
			$top10['salesnum'] = $result['top10'];
			$result = m('statistic')->o2overifystatistic($day);
			$total['verifynum'] = $result['total'];
			$top10['verifynum'] = $result['top10'];
			$result = m('statistic')->o2orefundmoney($day);
			$total['refundmoney'] = $result;
			$result = m('statistic')->o2orefundstatistic($day);
			$total['refundnum'] = $result;
			$info[$day] = $total;
			$list[$day] = $top10;
		}
		include $this->template();
	}
}