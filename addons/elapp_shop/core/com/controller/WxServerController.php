<?php

namespace app\controller;

use app\BaseController;
use extend\framework\WeEngine;
use think\facade\Db;

class WxServerController extends BaseController
{
    function index()
    {
        global $_W;
        $engine = new WeEngine();
        if (!empty($_W['setting']['copyright']['status'])) {
            $engine->died('抱歉，站点已关闭，关闭原因：' . $_W['setting']['copyright']['reason']);
        }
        if (!empty($_W['uniaccount']['endtime']) && TIMESTAMP > $_W['uniaccount']['endtime'] && !in_array($_W['uniaccount']['endtime'], array(USER_ENDTIME_GROUP_EMPTY_TYPE, USER_ENDTIME_GROUP_UNLIMIT_TYPE))) {
            $engine->died('抱歉，您的公众号已过期，请及时联系管理员');
        }

        if($_W['isajax'] && $_W['ispost'] && $_GPC['flag'] == 1) {
            $engine->encrypt();
        }
        if($_W['isajax'] && $_W['ispost'] && $_GPC['flag'] == 2) {
            $engine->decrypt();
        }
        $_W['isajax'] = false;
        $engine->start();
    }
}