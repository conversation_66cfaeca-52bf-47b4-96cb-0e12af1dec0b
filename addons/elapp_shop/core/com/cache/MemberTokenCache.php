<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------


namespace app\com\cache;

use app\common\cache\BaseCache;
use app\core\model\member\MemberSessionModel;
use app\model\MemberModel;

class MemberTokenCache extends BaseCache
{

    private $prefix = 'token_member_';


    /**
     * @notes 通过token获取缓存用户信息
     * @param $token
     * @return array|false|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/8 10:11
     */
    public function getMemberInfo($token)
    {
        //直接从缓存获取
        $memberInfo = $this->get($this->prefix . $token);
        if ($memberInfo) {
            return $memberInfo;
        }

        //从数据获取信息被设置缓存(可能后台清除缓存）
        $memberInfo = $this->setMemberInfo($token);
        if ($memberInfo) {
            return $memberInfo;
        }

        return false;
    }


    /**
     * @notes 通过有效token设置用户信息缓存
     * @param $token
     * @return array|false|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/8 10:11
     */
    public function setMemberInfo($token)
    {
        $memberSession = MemberSessionModel::where([['token', '=', $token], ['expire_time', '>', time()]])->find();
        if (empty($memberSession)) {
            return [];
        }

        $member = MemberModel::where(['id' => $memberSession->member_id])->field('id,uid,openid,openid_wa,uniacid,realname,nickname,mobile,avatar')->find();
        $memberInfo = [
            'member_id' => $member->id,
            'token' => $token,
            'avatar' => tomedia($member->avatar),
            'terminal' => $memberSession->terminal,
            'expire_time' => $memberSession->expire_time,
        ];
        $memberInfo = array_merge($member->toArray(), $memberInfo);

        $ttl = new \DateTime(Date('Y-m-d H:i:s', $memberSession->expire_time));
        $this->set($this->prefix . $token, $memberInfo, $ttl);
        return $this->getMemberInfo($token);
    }


    /**
     * @notes 删除缓存
     * @param $token
     * @return bool
     * <AUTHOR>
     * @date 2024/1/8 10:13
     */
    public function deleteMemberInfo($token)
    {
        return $this->delete($this->prefix . $token);
    }


}