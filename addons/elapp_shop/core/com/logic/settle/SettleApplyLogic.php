<?php

namespace app\com\logic\settle;

use app\com\enum\member\MemberTerminalEnum;
use app\com\logic\MemberBankCardLogic;
use app\com\logic\MemberLogic;
use app\common\logic\BaseLogic;
use app\core\dao\copartner\CopartnerAccountDao;
use app\core\dao\copartner\CopartnerUserDao;
use app\core\dao\settle\SettleApplyDao;
use app\core\dao\sysset\SyssetDao;
use app\model\CopartnerModel;
use app\model\MemberBankCardModel;
use app\model\MemberModel;
use app\model\SettleModel;
use app\traits\LogicTrait;
use think\db\exception\DbException;
use think\exception\ValidateException;

class SettleApplyLogic extends BaseLogic
{
    use LogicTrait;

    /**
     * 审核状态
     * @var string[]
     */
    public $status = [
        -1 => '审核拒绝',
        0 => '待审核',
        1 => '审核通过',
    ];

    public $apply_type = [
        0 => '微信钱包',
        2 => '支付宝',
        3 => '银行卡',
    ];

    function __construct(SettleApplyDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取结算申请列表
     * @param array $where
     * @param string $field
     * @return array
     */
    function getSettleApplyList(array $where, string $field = '*'): array
    {
        [$page, $limit] = $this->getPageValue();
        $list = $this->dao->getList($where, $field, $page, $limit);
        foreach ($list as &$item) {
            $item['status'] = $this->$status[$item['status']] ?? '';
            $item['apply_type'] = $this->$apply_type[$item['apply_type']] ?? '';
        }
        $count = $this->count($where);
        return compact('list', 'count');
    }

    /**
     * 获取单条结算申请
     * @param int $id
     * @param string $field
     * @return array|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function getSettleApply(int $id, string $field = '*')
    {
        if (is_string($field)) $field = explode(',', $field);
        return $this->dao->get(['id' => $id], $field);
    }

    /**
     * 更新/插入申请记录
     * @param array $data
     * @return array|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function setSettleApply(array $data)
    {
        $id = (int)$data['id'];
        $apply = $this->getSettleApply($id);
        if ($id) {
            if ($this->dao->update($id, $data, 'id')) {
                return ['type' => 'edit', 'msg' => '更新成功', 'data' => array_merge($apply,$data)];
            } else {
                abort(400, '更新失败');
            }
        } else {
            if ($apply = $this->dao->create($data)) {
                return ['type' => 'add', 'msg' => '添加成功', 'data' => $apply];
            } else {
                abort(400,'添加失败');
            }
        }
    }

    /**
     * 删除
     * @param $id
     * @throws \Exception
     */
    public function delSettleApply(int $mid, int $id)
    {
        if ($apply = $this->getSettleApply($id)) {
            if ($apply['mid'] != $mid) {
                throw new ValidateException('数据错误');
            }
            if (!$this->dao->delete($id)) {
                throw new ValidateException('删除失败,请稍候再试!');
            }
        }
        return true;
    }

    /**
     * 提现申请首页基础配置信息
     * @param array $member
     * @param array $data
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    function baseInfo(array $member, array $data)
    {
        $moeny_type = $data['money_type'] ? 'copartner' : 'clerk';
        if (0 == $data['money_type']) {
            $credit = app(MemberModel::class)->getCredit($member['openid'], 'credit3');
            $total_money = $this->dao->sum(['mid' => $member['id'], 'money_type' => $data['money_type'], ['status', 'in', [0, 1]]], 'apply_money');
        } elseif (1 == $data['money_type']) {
            $credit = app(SettleModel::class)->getCopartnerMoney($member['openid'], $member['uniacid']);
        }
        $apply_type = $this->applyType($member);

        $copartner = app(CopartnerModel::class);
        $copartnerUserInfo = $copartner->getCopartnerUserInfo($member['openid']);
        $cop_set = $copartner->getSet($copartnerUserInfo['uniacid'], '', $copartnerUserInfo['id']);
        $withdraw_service_fee_text = $cop_set['texts']['withdraw_service_fee'] ?? '平台服务费';
        $withdraw_freeze_fee_text = $cop_set['texts']['withdraw_freeze_fee'] ?? '消费冻结金';
        $withdraw_personal_fee_text = $cop_set['texts']['withdraw_personal_fee'] ?? '个税服务费';

        // todo yonh 优化: 代码重复
        // 获取合伙人平台服务费设置
        $withdrawServiceFeeConfig = $copartner->getCopartnerWithdrawServiceFeeConfig($member['openid'], $moeny_type);
        // 是否打开平台服务费功能
        $isOpenWithdrawServiceFee = $withdrawServiceFeeConfig['open_withdraw_service_fee'];
        // 获取合伙人消费冻结金设置
        $withdrawFreezeFeeConfig = $copartner->getCopartnerWithdrawFreezeFeeConfig($member['openid'], $moeny_type);
        // 是否打开消费冻结金功能
        $isOpenWithdrawFreezeFee = $withdrawFreezeFeeConfig['open_withdraw_freeze_fee'];
        // 获取个税配置
        $withdrawPersonalFeeConfig = $copartner->getCopartnerWithdrawPersonalFeeConfig($member['openid'], $moeny_type);
        // 是否打开个税功能
        $isOpenWithdrawPersonalFee = $withdrawPersonalFeeConfig['open_withdraw_personal_fee_rate'];

        return result(0, 'success', [
            'total_money' => $total_money ?: 0,
            'credit' => $credit,
            'apply_type' => $apply_type['data']['types'],
            'withdraw_service_fee_text' => $withdraw_service_fee_text,
            'withdraw_freeze_fee_text' => $withdraw_freeze_fee_text,
            'withdraw_personal_fee_text' => $withdraw_personal_fee_text,
            'isOpenWithdrawServiceFee' => $isOpenWithdrawServiceFee,
            'isOpenWithdrawFreezeFee' => $isOpenWithdrawFreezeFee,
            'isOpenWithdrawPersonalFee' => $isOpenWithdrawPersonalFee,
            'withdrawServiceFeeConfig' => $withdrawServiceFeeConfig,
            'withdrawFreezeFeeConfig' => $withdrawFreezeFeeConfig,
            'withdrawPersonalFeeConfig' => $withdrawPersonalFeeConfig
        ]);
    }

    /**
     * 是否可提现(条件)
     * @param $member
     * @param $params
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function isWithdraw($member, $params)
    {
        $trade = app(SyssetDao::class)->sysSetsConfig('trade', []);
        if (empty($trade['withdraw'])) {
            return result(4001, '结算提现未开启');
        }

        if (0 == $params['money_type']) {
            $credit = app(MemberModel::class)->getCredit($member['openid'], 'credit3');

            $total_money = $this->dao->sum(['mid' => $member['id'], 'money_type' => $params['money_type'], ['status', 'in', [0, 1]]], 'apply_money');
            [$can, $err, $err_msg, $url] = app(SettleModel::class)->clerkCanWithdraw($member);
            if (!$can) {
                if ($err == 4 && ($total_money + $params['money']) >= $trade['total_withdraw_money']) { //累计达到提现额度后提现要交店铺服务费
                    return result(4002, '累计提现额度已超过免费提现额度' . $trade['total_withdraw_money'] . '元，须缴纳店铺服务费，请您缴费！');
                } else if ($err == 5) {
                    return result(4003, '店铺服务费已到期，请您缴费！');
                }
            }
        } elseif (1 == $params['money_type']) { //合伙人
            $credit = app(SettleModel::class)->getCopartnerMoney($member['openid'], $member['uniacid']);

            $copartner_user = app(CopartnerUserDao::class)->getOne(['mid' => $member['id'], 'del_at' => 0]);
            $is_cf = app(CopartnerAccountDao::class)->isCopartnerFounder($member['id']);
            if (!$is_cf && $copartner_user['status'] != 1) {
                return result(4005, '您还未成为合伙人');
            }
        }
        if (bccomp($params['money'], $credit,2) == 1) {
            return result(4006, '提现金额超过可提现金额!');
        }

        return result(0, true);
    }

    /**
     * 计算税费后的可提现金额
     * @param $openid
     * @param int $moeny_type
     * @param $money
     * @return string
     */
    function getAfterTaxMoney($openid, int $moeny_type, float $money)
    {
        $moeny_type = $moeny_type ? 'copartner' : 'clerk';
        // 计算平台服务费
        $service_money_arr = $this->calcuteWithdrawServiceFee($openid, $moeny_type, $money);
        // 计算消费冻结金
        $freeze_money_arr = $this->calcuteWithdrawFreezeFee($openid, $moeny_type, $money);
        $money = bcsub($money, $service_money_arr['withdraw_service_fee'], 2);
        $money = bcsub($money, $freeze_money_arr['withdraw_freeze_fee'], 2);

        $deduction_money = 0.00;
        $trade = app(SyssetDao::class)->sysSetsConfig('trade', []);
        $set_array = [
            'charge' => $trade['withdrawcharge'],
            'begin' => $trade['withdrawbegin'],
            'end' => $trade['withdrawend'],
        ];
        if (!empty($set_array['charge'])) {
            $money_array = app(MemberModel::class)->getCalculateMoney($money, $set_array);

            if($money_array['flag']) {
                $money = $money_array['realmoney'];
                $deduction_money = $money_array['deductionmoney'];
            }
        }

        //计算个税金额
        $personal_money_arr = $this->calcuteWithdrawPersonalFee($openid, $moeny_type, $money);
        $money = bcsub($money - $personal_money_arr['withdraw_personal_fee'],2);

        return [
            'charge' => $set_array['charge'],
            'money' => $money,
            'deduction_money' => $deduction_money,
            'service_money' => $service_money_arr,
            'freeze_money' => $freeze_money_arr,
            'personal_money' => $personal_money_arr
        ];
    }

    /**
     * 计算提现服务费
     * @param $openid
     * @param $type
     * @param $realmoney
     * @return int[]
     */
    function calcuteWithdrawServiceFee($openid, $type, $realmoney)
    {
        $return        = [
            'withdraw_service_fee_rate' => 0,
            'withdraw_service_fee'      => 0,
        ];

        /** @var CopartnerModel $plugin */
        $plugin = p('copartner');
        $withdrawServiceFeeConfig = $plugin->getCopartnerWithdrawServiceFeeConfig($openid, $type);

        // 是否打开提现服务费功能
        if ($withdrawServiceFeeConfig['open_withdraw_service_fee']) {
            // 按照 比例计算还是固定金额计算 提现服务费
            if ($withdrawServiceFeeConfig['type'] == 'rate') {
                $return['withdraw_service_fee_rate'] = $withdrawServiceFeeConfig['rate'];
                $return['withdraw_service_fee'] = floor($realmoney * $withdrawServiceFeeConfig['rate']) / 100;
            } else {
                $return['withdraw_service_fee'] = $withdrawServiceFeeConfig['pay'];
            }
        }

        return $return;
    }

    /**
     * 计算提现服务费
     * @param $openid
     * @param $type
     * @param $realmoney
     * @return int[]
     */
    function calcuteWithdrawFreezeFee($openid, $type, $realmoney)
    {
        $return        = [
            'withdraw_freeze_fee_rate' => 0,
            'withdraw_freeze_fee'      => 0,
        ];

        /** @var CopartnerModel $plugin */
        $plugin = p('copartner');
        $withdrawFreezeFeeConfig = $plugin->getCopartnerWithdrawFreezeFeeConfig($openid, $type);

        // 是否打开提现服务费功能
        if ($withdrawFreezeFeeConfig['open_withdraw_freeze_fee']) {
            // 按照 比例计算还是固定金额计算 提现服务费
            if ($withdrawFreezeFeeConfig['type'] == 'rate') {
                $return['withdraw_freeze_fee_rate'] = $withdrawFreezeFeeConfig['rate'];
                $return['withdraw_freeze_fee'] = floor($realmoney * $withdrawFreezeFeeConfig['rate']) / 100;
            } else {
                $return['withdraw_freeze_fee'] = $withdrawFreezeFeeConfig['pay'];
            }
        }

        return $return;
    }

    /**
     * 计算个税服务费
     * @param $openid
     * @param $type
     * @param $realmoney
     * @return int[]
     */
    function calcuteWithdrawPersonalFee($openid, $type, $realmoney)
    {
        $return        = [
            'withdraw_personal_fee_rate' => 0,
            'withdraw_personal_fee'      => 0,
        ];

        /** @var CopartnerModel $plugin */
        $plugin = p('copartner');
        $withdrawPersonalFeeConfig = $plugin->getCopartnerWithdrawPersonalFeeConfig($openid, $type);

        // 是否打开提现服务费功能
        if ($withdrawPersonalFeeConfig['open_withdraw_personal_fee_rate']) {
            // 按照 比例计算还是固定金额计算 个税服务费
            if ($withdrawPersonalFeeConfig['type'] == 'rate') {
                $return['withdraw_personal_fee_rate'] = $withdrawPersonalFeeConfig['rate'];
                $return['withdraw_personal_fee'] = floor($realmoney * $withdrawPersonalFeeConfig['rate']) / 100;
            } else {
                $return['withdraw_personal_fee'] = $withdrawPersonalFeeConfig['pay'];
            }
        }

        return $return;
    }

    /**
     * 操作结算申请
     * @param array $data
     * @return void
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function withdraw(array $data)
    {
        $this->transaction(function () use ($data) {
            if ($data['money_type']) {
                $dec = app(CopartnerUserDao::class)->bcDec($data['mid'], 'settle_money', $data['apply_money'], 'mid');
                if (!$dec) {
                    throw new DbException('提现异常，请联系客服');
                }
            } else {
                app(MemberModel::class)->setCredit($data['openid'], 'credit3', - $data['apply_money'], array(0, '钱包余额提现预扣除: ' . $data['apply_money'] . ',实际到账金额:' . $data['real_money'] . ',手续费金额:' . $data['deduction_money']));
            }
            $this->setSettleApply($data);
        });
    }

    /**
     * 申请提现类型信息
     * @param int $type 0微信 2支付宝 3银行
     * @param array $data
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    function applyType(array $member, int $type = -1, array $data = [])
    {
        $types = [];
        $trade = app(SyssetDao::class)->sysSetsConfig('trade', []);
        if (1 == $trade['withdrawcashweixin'] && in_array($member['comefrom'], [MemberTerminalEnum::WECHAT_OA, MemberTerminalEnum::WECHAT_MMP])) {
            $types += [0 => '提现到微信钱包'];
        }
        if (1 == $trade['withdrawcashalipay']) {
            $types += [2 => '提现到支付宝'];
        }
        if (1 == $trade['withdrawcashcard']) {
            $member_bank_card = app(MemberBankCardLogic::class)->getBankCardList($member['id'])->toArray();
            $types += [
                3 => '提现到银行卡',
                'member_bank_card' => $member_bank_card,
            ];
        }

        if (!empty($data['apply_info'])) { // 提交表单的时候
            if (2 == $type ) {
                $apply = $this->applyAlipay($data['apply_info']);
                if ($apply['code'] != 0) {
                    return $apply;
                }
            }

            if (3 == $type) {
                $data['apply_info']['mid'] = $member['id'];
                $apply = $this->applyBank($data['apply_info']);
                if ($apply['code'] != 0) {
                    return $apply;
                }
            }
        }
        return result(0, 'success', [
            'types' => $types,
            'apply' => $apply['data'] ?? [],
        ]);
    }

    /**
     * 支付宝提现
     * @param array $data
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    function applyAlipay(array $data)
    {
        $realname = trim($data['realname']);
        $alipay = trim($data['alipay']);
        $alipay1 = trim($data['comfirm_alipay']);
        if (empty($realname)) {
            return result(8101, '请填写姓名!');
        }
        if (empty($alipay)) {
            return result(8101, '请填写支付宝帐号!');
        }
        if (empty($alipay1)) {
            return result(8101, '请填写确认帐号!');
        }
        if ($alipay != $alipay1) {
            return result(8101, '支付宝帐号与确认帐号不一致!');
        }
        return result(0, 'success', [
            'realname' => $realname,
            'alipay' => $alipay,
        ]);
    }

    /**
     * 银行卡提现(银行卡验证)
     * @param array $data
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DbException
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function applyBank(array $data)
    {
        $apply = array();
        //银行卡号
        $memberBankCardModel = new MemberBankCardModel();
        $bank_account_id = intval($data['bank_account_id']);
        if (!empty($bank_account_id)) { //选择账户
            $bank_account = $memberBankCardModel->where(['id' => $bank_account_id])->find()->toArray();
            if (empty($bank_account['card_no']) || empty($bank_account['id_card']))  {
                return result(8100, '所选择账户异常，身份证号或者银行卡号为空!');
            }
            $apply += [
                'realname' => $bank_account['name'],
                'bank_name' => $bank_account['bank_name'],
                'bank_card' => $bank_account['card_no'],
                'bank_open' => $bank_account['open_bank'],
                'idcard' => $id_card = $bank_account['id_card'],
                'mobile' => $mobile = $bank_account['mobile'],
            ];
        } else { //手动填写账户
            $bankname = $bankopen = null;
            $realname = trim($data['realname']);
            $id_card = trim($data['id_card']);
            $bankcard = trim($data['bankcard']);
            $mobile = trim($data['mobile']);
            if (empty($realname)) {
                return result(8101, '请填写姓名!');
            }
            if (empty($id_card)) {
                return result(8101, '请填写身份证号!');
            }
            if (empty($bankcard)) {
                return result(8101, '请填写银行卡号!');
            }
            if (empty($mobile)) {
                return result(8101, '请填写手机号!');
            }

            $memberBankCardRecord = $memberBankCardModel->where(['card_no' => $bankcard, 'id_card' => $id_card])->findOrEmpty()->toArray();
            //银行卡验证通过则保存银行卡信息
            if (empty($memberBankCardRecord)) {
                //验证银行卡号
                $auth_result = app(MemberBankCardLogic::class)->authBankCard($realname, $id_card, $bankcard);
                if ($auth_result['code'] == -1) {
                    return result(8102, $auth_result['msg']);
                }
                if ($auth_result['data']['result'] != '01') {
                    return result(8102, $auth_result['data']['remark'] . '：请检查您的银行卡相关信息');
                }

                $bankcardResult = $memberBankCardModel->create([
                    'member_id' => $data['mid'],
                    'mobile' => $mobile,
                    'bank_name' => $bankname = $auth_result['data']['bankName'],
                    'card_no' => $bankcard,
                    'open_bank' => $bankopen = $auth_result['data']['bankName'],
                    'name' => $realname,
                    'id_card' => $id_card
                ]);
            }
            $apply += [
                'realname' => $realname,
                'bank_name' => $bankname ?? $memberBankCardRecord['bank_name'],
                'bank_card' => $bankcard,
                'bank_open' => $bankopen ?? $memberBankCardRecord['open_bank'],
                'idcard' => $id_card,
                'mobile' => $mobile
            ];
        }
        //是否实名认证通过
        $memberVerifyData = [
            'realname' => $apply['realname'],
            'idcard' => $id_card,
            'mobile' => $mobile
        ];
        $memberVerifyResult = MemberLogic::isUserVerify($data['mid'], $memberVerifyData);
        if (empty($memberVerifyResult['is_verify'])) {
            $memberVerifyData += [
                'id' => $memberVerifyResult['id'],
                'idcard_front_pic' => $memberVerifyResult['idcard_front_pic'] ?? '',
                'idcard_back_pic' => $memberVerifyResult['idcard_back_pic'] ?? '',
            ];
            return result(8103, ['message' => '该用户：' . $apply['realname'] . '请先实名认证!', 'url' => mobileUrl('member/idverifyication/detail', $memberVerifyData)]);
        }
        $userContractSignResult = MemberLogic::isUserContractSign($memberVerifyData['realname'], $memberVerifyData['idcard']);
        if ('complete' != $userContractSignResult['status']) {
            $is_user_sign = 0;
            if (!empty($userContractSignResult['sn'])) {
                $user_sign_result = MemberLogic::userContractDetail(['sn' => $userContractSignResult['sn']]);
                $user_sign_result = json_decode($user_sign_result, true);
                if (!empty($user_sign_result['data']['status']) && 'complete' == $user_sign_result['data']['status']) {
                    $is_user_sign = 1;
                }
            } else { //用户端获取协议
                $memberVerifyData['member_id'] = $data['mid'];
                $contract_result = MemberLogic::userContractSign($memberVerifyData);
                $contract_result = json_decode($contract_result, true);
                if (200 == $contract_result['code']) {
                    $userContractSignResult['sign_url'] = $contract_result['data']['sign_url'];
                }
            }
            if (0 == $is_user_sign) {
                return result(8104, ['message' => '该用户：' . $apply['realname'] . '未签署协议！', 'url' => $userContractSignResult['sign_url'] ?? '', 'iframe' => 1]);
            }
        }

        return result(0, 'success', $apply);
    }

}