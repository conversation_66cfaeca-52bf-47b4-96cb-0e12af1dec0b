<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\com\logic;

use app\com\enum\member\MemberTerminalEnum;
use app\com\service\wechat\MiniProgramService;
use app\com\service\wechat\OfficialAccountService;
use app\com\service\WechatMemberService;
use app\common\logic\BaseLogic;
use app\model\MemberModel;
use think\facade\Db;
use web\model\fans\MappingFansModel;
use web\model\user\UsersLoginLogsModel;

/**
 * 商城微信登录处理逻辑
 * Class LoginLogic
 * @package app\com\logic
 */
class LoginLogic extends BaseLogic
{

    /**
     * @notes 账号密码注册
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2024/1/31 15:37
     */
    public static function register(array $params)
    {

    }


    /**
     * @notes 账号/手机号登录，手机号验证码
     * @param $params
     * @return array|false
     * <AUTHOR>
     * @date 2024/1/31 19:26
     */
    public static function login($params)
    {

    }


    /**
     * @notes 退出登录
     * @param $memberInfo
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/31 17:56
     */
    public static function logout($memberInfo)
    {
        //token不存在，不注销
        if (!isset($memberInfo['token'])) {
            return false;
        }

        //设置token过期
        return MemberTokenService::expireToken($memberInfo['token']);
    }


    /**
     * @notes 获取微信请求code的链接
     * @param string $url
     * @return string
     * <AUTHOR>
     * @date 2024/1/31 19:47
     */
    public static function codeUrl(string $url)
    {
        return (new OfficialAccountService())->getCodeUrl($url);
    }


    /**
     * @notes 公众号登录
     * @param array $params
     * @return array|false
     * @throws \GuzzleHttp\Exception\GuzzleException
     * <AUTHOR>
     * @date 2024/1/31 19:47  @date \d{4}/\d{1,2}/\d{1,2}
     */
    public static function oaLogin(array $params)
    {
        Db::startTrans();
        try {
            //通过code获取微信 openid
            $response = (new OfficialAccountService($params['i']))->getOaResByCode($params['code']);
            $memberServer = app(WechatMemberService::class, [$response, MemberTerminalEnum::WECHAT_OA, $params['i']]);
            $memberInfo = $memberServer->getResopnseByMemberInfo()->authMemberLogin()->getMemberInfo();

            // 更新登录信息
            self::updateLoginInfo($memberInfo['id']);

            Db::commit();
            return $memberInfo;

        } catch (\Exception $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 小程序-静默登录
     * @param array $params
     * @return array|false
     * <AUTHOR>
     * @date 2024/1/31 19:47
     */
    public static function silentLogin(array $params)
    {
        file_put_contents('/tmp/silentLogin.log', json_encode($params) . "\n", FILE_APPEND);

        try {
            //通过code获取微信 openid
            $response = (new MiniProgramService($params['i']))->getMnpResByCode($params['code']);
            $memberServer = app(WechatMemberService::class, [$response, MemberTerminalEnum::WECHAT_MMP, $params['i']]);
            $memberInfo = $memberServer->getResopnseByMemberInfo()->getMemberInfo();

            if (!empty($memberInfo)) {
                // 更新登录信息
                self::updateLoginInfo($memberInfo['id']);
            } else {
                app('cache')->set($response['openid'], $response['unionid'], 86400);
                $memberInfo = ['openid' => $response['openid'], 'unionid' => $response['unionid']];
            }

            return $memberInfo;
        } catch (\Exception  $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 小程序-授权登录
     * @param array $params
     * @return array|false
     * <AUTHOR>
     * @date 2024/1/31 19:47
     */
    public static function mnpLogin(array $params)
    {
        file_put_contents('/tmp/mnpLogin.log', json_encode($params) . "\n", FILE_APPEND);
        Db::startTrans();
        try {
            $miniProgramService = app(MiniProgramService::class, [$params['i']]);
            if (in_array($params['login_type'], ['silent', 'openid'])) { //通过code获取微信 openid
                $response = $miniProgramService->getMnpResByCode($params['code']);
            } elseif ($params['login_type']  == 'phone') { //通过code获取微信 phone
                $response = $miniProgramService->getPhoneNumber($params['code']);
                $response['openid_wa'] = $params['openid_wa'] ?? '';
            }
            self::logger('小程序登录响应', $params, $response);
            if (empty($response) || $response['errcode'] != 0) throw new \Exception('微信登录失败：' . $response['errmsg']);
            $memberServer = app(WechatMemberService::class, [$response, MemberTerminalEnum::WECHAT_MMP, $params['i']]);

            if ($params['login_type']  == 'silent') {
                $memberInfo = $memberServer->getResopnseByMemberInfo()->getMemberInfo();
            } else {
                $memberInfo = $memberServer->getResopnseByMemberInfo()->authMemberLogin()->getMemberInfo();
            }

            file_put_contents('/tmp/login.log', json_encode($memberInfo) . "\n", FILE_APPEND);

            if (!empty($memberInfo)) {
                // 更新登录信息
                self::updateLoginInfo($memberInfo['id']);
            } else {
                global $_W, $_SESSION;

                // 使用 openid 作为键，存储完整的用户信息，便于后续注册时查找
                $openid_cache_key = 'mnp_user_' . $params['i'] . '_' . $response['openid'];
                $user_data = [
                    'openid' => $response['openid'],
                    'unionid' => $response['unionid'] ?? '',
                    'session_key' => $response['session_key'] ?? '',
                    'uniacid' => $params['i'],
                    'login_time' => time()
                ];
                app('cache')->set($openid_cache_key, $user_data, 1800); // 30分钟有效期

                // 同时保持原有的通用缓存键，用于向后兼容
                app('cache')->set('mnp_openid', $response['openid'], 300);

                // 设置全局变量和 Session，便于后续流程获取
                $_W['openid'] = $response['openid'];
                $_W['mnp_openid'] = $response['openid'];
                $_W['mnp_session_key'] = $response['session_key'] ?? '';
                $_W['mnp_unionid'] = $response['unionid'] ?? '';

                // 设置 Session
                if (!isset($_SESSION)) {
                    session_start();
                }
                $_SESSION['mnp_openid'] = $response['openid'];
                $_SESSION['mnp_session_key'] = $response['session_key'] ?? '';
                $_SESSION['mnp_unionid'] = $response['unionid'] ?? '';
                $_SESSION['mnp_login_time'] = time();

                // 创建一个临时的用户会话，用于关联后续的注册操作
                $temp_session_key = 'mnp_temp_session_' . $params['i'] . '_' . md5($response['openid'] . time());
                app('cache')->set($temp_session_key, $user_data, 1800);

                // 记录日志
                file_put_contents('/tmp/mnp_login_debug.log', json_encode([
                    'openid' => $response['openid'],
                    'session_key' => $response['session_key'] ?? '',
                    'unionid' => $response['unionid'] ?? '',
                    'cache_key' => $openid_cache_key,
                    'temp_session_key' => $temp_session_key,
                    'time' => date('Y-m-d H:i:s')
                ]) . "\n", FILE_APPEND);

                $memberInfo = [
                    'openid' => $response['openid'],
                    'unionid' => $response['unionid'],
                    'temp_session' => $temp_session_key
                ];
            }


            Db::commit();
            return $memberInfo;
        } catch (\Exception  $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }

    /**
     * 手机号授权登陆
     * @param array $params
     * @return array|false
     */
    static function mnpMobileLogin(array $params)
    {
        file_put_contents('/tmp/mnpMobileLogin.log', json_encode($params) . "\n", FILE_APPEND);
        Db::startTrans();
        try {
            //通过code获取微信 phonenumber
            $response = (new MiniProgramService($params['i']))->getPhoneNumber($params['code']);
            $memberServer = app(WechatMemberService::class, [$response, MemberTerminalEnum::WECHAT_MMP, $params['i']]);
            $memberInfo = $memberServer->getResopnseByMemberInfo()->authMemberLogin()->getMemberInfo();

            // 更新登录信息
            self::updateLoginInfo($memberInfo['id']);

            Db::commit();
            return $memberInfo;
        } catch (\Exception  $e) {
            Db::rollback();
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 更新登录信息
     * @param $memberId
     * @throws \Exception
     * <AUTHOR>
     * @date 2024/1/31 19:46
     */
    public static function updateLoginInfo($memberId)
    {
        $time = time();
        $usersLoginLogs = new UsersLoginLogsModel();
        $usersLoginLogs->uid = $memberId;
        $usersLoginLogs->ip = request()->ip();
        $usersLoginLogs->city = "";
        $usersLoginLogs->createtime = $usersLoginLogs->login_at = $time;
        $usersLoginLogs->save();
    }


    /**
     * @notes 小程序端绑定微信
     * @param array $params
     * @return bool
     * <AUTHOR>
     * @date 2024/1/31 19:46
     */
    public static function mnpAuthLogin(array $params)
    {
        try {
            //通过code获取微信openid
            $response = (new MiniProgramService())->getMnpResByCode($params['code']);
            $response['member_id'] = $params['member_id'];
            $response['uid'] = $params['uid'];
            $response['terminal'] = MemberTerminalEnum::WECHAT_MMP;

            return self::bindMember($response);

        } catch (\Exception  $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 公众号端绑定微信
     * @param array $params
     * @return bool
     * @throws \GuzzleHttp\Exception\GuzzleException
     * <AUTHOR>
     * @date 2024/1/31 10:43
     */
    public static function oaAuthLogin(array $params)
    {
        try {
            //通过code获取微信openid
            $response = (new OfficialAccountService())->getOaResByCode($params['code']);
            $response['member_id'] = $params['member_id'];
            $response['uid'] = $params['uid'];
            $response['terminal'] = MemberTerminalEnum::WECHAT_OA;

            return self::bindMember($response);

        } catch (\Exception  $e) {
            self::$error = $e->getMessage();
            return false;
        }
    }


    /**
     * @notes 绑定会员账户
     * @param $response
     * @return bool
     * @throws \Exception
     * <AUTHOR>
     * @date 2024/1/31 10:43
     */
    public static function bindMember($response)
    {
        //先检查openid是否有记录
        $isAuth = MappingFansModel::where('openid', '=', $response['openid'])->findOrEmpty();
        if (!$isAuth->isEmpty()) {
            throw new \Exception('该微信已被绑定');
        }

        if (isset($response['unionid']) && !empty($response['unionid'])) {
            //在用unionid找记录，防止生成两个账号，同个unionid的问题
            $userAuth = MappingFansModel::where(['unionid' => $response['unionid']])
                ->findOrEmpty();
            if (!$userAuth->isEmpty() && $userAuth->uid != $response['uid']) {
                throw new \Exception('该微信已被绑定');
            }
        }

        //如果没有授权，直接生成一条微信授权记录
        MappingFansModel::create([
            'uid' => $response['uid'],
            'openid' => $response['openid'],
            'unionid' => $response['unionid'] ?? '',
            'user_from' => $response['terminal'],
        ]);
        return true;
    }
    
}