<?php

namespace app\core\com\logic\sale\coupon;

use app\common\logic\BaseLogic;
use app\core\dao\sale\coupon\CouponDao;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 优惠券逻辑
 * Class CouponLogic
 * @package app\core\com\logic\sale\coupon
 * <AUTHOR>
 * @date 2024/7/23 21:06
 */
class CouponLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(CouponDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单条)优惠券信息
     * @param array|string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getCouponDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getCouponDetail($where, $field, $with);
    }

    /**
     * 获取(多条)优惠券列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getCouponList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getCouponList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

    /**
     * 查询优惠券列表
     * @param array $coupon_ids 优惠券id数组
     * @param string $field 要获取的字段数组
     * @param array $with 要加载的关联模型数组
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     * <AUTHOR>
     * @date 2024/07/23 22:53
     */
    public function queryCoupon(array $coupon_ids = [], string $field = '*', array $with = [])
    {
        global $_W;
        $coupons = [];
        if (!is_array($coupon_ids) || empty($coupon_ids)) {
            return $coupons;
        }
        foreach ($coupon_ids as $k => $v) {
            $coupons[$k] = self::getCouponDetail($v, $field, $with)->toArray();
        }
        return $coupons;
    }

}