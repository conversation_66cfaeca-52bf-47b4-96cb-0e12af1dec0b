<?php

namespace app\core\com\logic\sale\coupon;

use app\common\logic\BaseLogic;
use app\core\dao\sale\coupon\CouponDataDao;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 优惠券领取记录 逻辑
 * Class CouponDataLogic
 * @package app\core\com\logic\sale\coupon
 * <AUTHOR>
 * @date 2024/7/23 21:06
 */
class CouponDataLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(CouponDataDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单条)优惠券领取记录
     * @param array|string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getCouponDataDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getCouponDataDetail($where, $field, $with);
    }

    /**
     * 获取(多条)优惠券领取记录列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getCouponDataList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getCouponDataList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

}