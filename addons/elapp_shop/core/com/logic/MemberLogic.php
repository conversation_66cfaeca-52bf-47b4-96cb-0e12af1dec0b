<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\logic;
use app\common\logic\BaseLogic;
use app\core\dao\member\MemberDao;
use app\core\dao\order\OrderDao;
use app\core\model\member\MemberVerifyModel;
use app\model\MemberContractSignModel;
use app\traits\LogicTrait;
use salary\Salary;

/**
 * 会员逻辑层
 * Class MemberLogic
 * @package app\com\logic
 */
class MemberLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(MemberDao $dao)
    {
        $this->dao = $dao;
    }
    /**
     * 是否已经实名验证
     * @param int $memberId
     * @param array $user_info
     * @return MemberVerifyModel|array|mixed|string|\think\Model
     */
    static function isUserVerify(int $memberId, array $user_info)
    {
        try {
            $memberVerifyModel = new MemberVerifyModel();
            $memberVerifyResult = $memberVerifyModel->where(['realname' => $user_info['realname'], 'idcard' => $user_info['idcard']])->find();
            if (empty($memberVerifyResult['is_verify'])) { //$memberVerifyResult null and 0
                $memberVerifyData = [
                    'realname' => $user_info['realname'],
                    'idcard' => $user_info['idcard'],
                    'member_id' => $memberId,
                    'mobile' => $user_info['mobile'],
                    'is_verify' => 0,
                    'idcard_front_pic' => $user_info['idcard_front_pic'] ?? $memberVerifyResult['idcard_front_pic'] ?? '',
                    'idcard_back_pic' => $user_info['idcard_back_pic'] ?? $memberVerifyResult['idcard_back_pic'] ?? '',
                ];
                if (empty($memberVerifyResult)) {
                    //先插入表后跳转验证
                    $insertId = $memberVerifyModel->create($memberVerifyData)->id;
                    $memberVerifyData['id'] = $insertId;
                } else if ($memberVerifyResult['is_verify'] == 0) {
                    $memberVerifyData['id'] = $memberVerifyResult['id'];
                    $memberVerifyResult->save($memberVerifyData);
                }
                return $memberVerifyData;
            } else {
                return $memberVerifyResult;
            }
        } catch (\Exception $e) {
            return $e->getMessage();
        }
    }

    /**
     * 是否已经签署协议
     * @param int $memberId
     * @param string $sn
     * @return MemberContractSignModel|array|mixed|\think\Model
     */
    static function isUserContractSign(string $realname, string $idcard, string $sn = '')
    {
        $where = ['realname' => $realname, 'idcard' => $idcard];
        if (!empty($sn)) $where['sn'] = $sn;
        $memberContractSignResult = MemberContractSignModel::where($where)->find();
        return $memberContractSignResult;
    }

    static function getUserVerifyList(int $memberId)
    {
        $memberVerifyModel = new MemberVerifyModel();
        $memberVerifyResult = $memberVerifyModel->where(['member_id' => $memberId])->select();
        return $memberVerifyResult;
    }

    static function userContractDetail(array $params)
    {
        $result = (new Salary())->userContractDetail($params['sn']);
        $result = json_decode($result, true);
        if (200 == $result['code']) {
            MemberContractSignModel::where(['sn' => $params['sn']])
                ->update(['status' => $result['data']['status'], 'file_url' => $result['data']['file_url'], 'signed_at' => $result['data']['signed_at']]);
        }
        return json_encode($result);
    }

    static function userContractSign(array $params)
    {
        try {
            $notify_url = mobileUrl('member/index/userContractNotify', [], true); //回调地址接收签署结果
            $result = (new Salary())->userContractSign($params['realname'], $params['idcard'], $params['mobile'], $notify_url);
            $result = json_decode($result, true);
            if (200 == $result['code']) {
                $result['data']['member_id'] = $params['member_id'];
                $result['data']['realname'] = $params['realname'];
                $result['data']['idcard'] = $params['idcard'];
                MemberContractSignModel::create($result['data']);
            }
            return json_encode($result);
        } catch (\Exception $e) {
            return json_encode(['code' => -100, 'msg' => $e->getMessage()]);
        }
    }
}