<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\com\logic;

use app\common\logic\BaseLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\core\model\settle\SettleOrderModel;
use app\model\ActivityModel;
use app\model\SettleModel;

/**
 * 结算逻辑
 */
class SettleLogic extends BaseLogic
{
    public function getSettleOrderInfo($id, $belong_to)
    {
        $settle_order =  SettleOrderModel::where( ['id' => $id, 'belong_to' => $belong_to])
            ->field(SettleOrderModel::scene_fields('detail'))
            ->findOrEmpty();

        if ($settle_order->isEmpty()) return [];
        $settle_order = $settle_order->toArray();

        if (in_array($settle_order['role_type'],[SettleModel::ROLE_TYPE_ACTIVITY, SettleModel::ROLE_TYPE_ACTIVITY_REFUND])) {
            // todo 980 优化活动收益名称
            $settle_order['commission_name'] = (new ActivityModel())->getActivityCommissionName($settle_order['role_id'], $settle_order['role_type']);
        } else {
            $settle_order['commission_name'] = (new SettleModel())->getCommissionNames($settle_order['role_type']);
        }

        $ordermap = [
            'membercard'     => 'elapp_shop_member_card_order',
            'servicefee'     => 'elapp_shop_member_servicefee_order',
            'goods'          => 'elapp_shop_order',
            'withdraw_apply' => 'elapp_shop_settle_withdraw_apply',
            'settle'         => 'elapp_shop_settle_order',
        ];
//        $goodsmap = [
//            'membercard'     => 'elapp_shop_member_card_order_card',
//            'servicefee'     => 'elapp_shop_member_servicefee_order_fee',
//            'goods'          => 'elapp_shop_order_goods',
//            'withdraw_apply' => 'elapp_shop_settle_withdraw_apply',
//            'settle'         => 'elapp_shop_settle_order',
//        ];
        $orders = $settle_order['orders'];
        $list = [];
        foreach ($orders as $type=>$ids) {

            if ($type == 'settle') {
                $os = pdo_fetchall('select belong_to, sum(commission) commission from ' . tablename($ordermap[$type]) . ' where id in (' . implode(',',$ids) . ')' . ' group by belong_to');
            } else {
                $os = pdo_getall($ordermap[$type], ['id'=>$ids]);
            }

            // todo yh 这块代码应该可以优化，参考settlehandle类提取公共方法，不需要重复定义
            // orderno,price => og thumb, title , commission
            foreach ($os as &$item) {
                $goods = [];
                if ($type == 'goods') {
                    $item['orderno'] = $item['ordersn'];
                    // goods
                    $goods = pdo_fetchall('SELECT og.id,og.goodsid,g.thumb,og.price,og.total,g.title from ' . tablename('elapp_shop_order_goods') . ' og' . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid  where og.orderid=:orderid and og.single_refundstate in (0,8) order by og.createtime  desc ', array(':orderid' => $item['id']));
                    $goods = set_medias($goods, 'thumb');
                } else if ($type == 'membercard') {
                    $item['price'] = $item['total'];

                    // membercard
                    $goods = pdo_fetchall('SELECT coc.id,coc.cardsid as goodsid,c.thumb,coc.price,coc.total,c.name as title from ' . tablename('elapp_shop_member_card_order_card') . ' coc' . ' left join ' . tablename('elapp_shop_member_card') . ' c on c.id=coc.cardsid  where coc.orderid=:orderid order by coc.createtime  desc ', array(':orderid' => $item['id']));
                    $goods = set_medias($goods, 'thumb');

                } else if ($type == 'servicefee') {
                    $item['price'] = $item['total'];

                    // servicefee
                    $goods = pdo_fetchall('SELECT coc.id,coc.feesid as goodsid,c.thumb,coc.price,coc.total,c.name as title from ' . tablename('elapp_shop_member_servicefee_order_fee') . ' coc' . ' left join ' . tablename('elapp_shop_member_servicefee') . ' c on c.id=coc.feesid  ' . ' where coc.orderid=:orderid order by coc.createtime  desc ', array(':orderid' => $item['id']));
                    $goods = set_medias($goods, 'thumb');
                } else if ($type == 'withdraw_apply') {
                    $item['orderno'] = $item['apply_no'];
                    $item['price'] = $item['withdraw_service_fee'];
                } else if ($type == 'settle') {
                    // 平台补贴需要特殊处理
                    $item['price'] = $item['commission'];
//                    $goods = pdo_fetchall('SELECT * from ' . tablename('elapp_shop_settle_order') . ' where id=:orderid ', array(':orderid' => $item['id']));
                    //$goods = set_medias($goods, 'thumb');
                    $cop = pdo_get('elapp_shop_copartner_user', ['id'=>$item['belong_to']],['id','openid','mcnname']);
                    $member = m('member')->getMember($cop['openid']);
                    if ($member) {
                        $thumb = tomedia($member['avatar']);
                    } else {
                        $thumb = '';
                    }
                    $goods = [
                        ['title' => $cop['mcnname'],'thumb'=>$thumb,'commission'=>$item['commission'],'id'=>$item['id']]
                    ];
                }

//                $paramNormalizer = new ParamNormalizer($settle_order['role_type']);
                if ($type != 'settle' && $goods) {
                    foreach ($goods as &$gitem) {

                        // 通过读取分润记录获取佣金
                        $handle_record =  pdo_get('elapp_shop_settle_order_handle_record',
                            ['ogid'=>$gitem['id'],'order_type'=>$type]);
//                        dump($handle_record);die;
                        $gitem['commission'] = '0.00';
                        if ($handle_record) {
                            $handle_record['data'] = @json_decode($handle_record['data'], true);
                            $gitem['commission'] = $handle_record['data'][$settle_order['role_id']]['commission'] ?? '0.00';
                        }
                    }
                }

                $item = [
                    'id'=>$item['id'],
                    'orderno' => $item['orderno'],
                    'price'=>$item['price'],
                    'order_goods'=>$goods
                ];
            }
            $types = [
                'goods' => '商品订单',
                'membercard' => '会员卡订单',
                'servicefee' => '服务费订单',
//                'withdraw_apply' => '提现申请',
                'settle' => '团队收益',
            ];
            $list[] = ['list' => $os, 'title'=>$types[$type], 'type'=>$type];
        }

        $settle_order['orders'] = $list;

        return $settle_order;
    }
}