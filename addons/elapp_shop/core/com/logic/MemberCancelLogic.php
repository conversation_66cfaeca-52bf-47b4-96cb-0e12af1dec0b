<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\logic;
use app\common\logic\BaseLogic;
use app\core\dao\member\MemberDao;
use app\core\dao\sysset\SyssetDao;
use app\core\model\member\card\MemberCardOrderModel;
use app\core\model\member\MemberVerifyModel;
use app\core\model\member\servicefee\ServiceFeeOrderModel;
use app\core\model\prescription\PrescriptionOrderPatientinfoModel;
use app\core\model\prescription\PrescriptionPatientinfoModel;
use app\core\model\settle\SettleOrderModel;
use app\core\model\uni\AccountModel;
use app\model\MemberAddressModel;
use app\model\MemberContractSignModel;
use app\model\MemberModel;
use app\model\OrderModel;
use app\model\SettleModel;
use app\model\UserModel;
use app\traits\LogicTrait;
use salary\Salary;

/**
 * 会员逻辑层
 * Class MemberLogic
 * @package app\com\logic
 */
class MemberCancelLogic extends BaseLogic
{
    use LogicTrait;
    /**
     * 注销用户申请
     * @param $userId int 注销用户id
     * @param $cancelStyle 1 用户申请注销 2 管理员注销
     * @return bool|string 注销申请成功返回true，注销失败返回错误信息
     */
    static function cancleUserAccountApply($userId, $cancelStyle)
    {
        if (in_array($cancelStyle , [1, 2]) == false) {
            return '注销类型错误';
        }
        $model = new MemberModel();
        $member =  $model->where(['id' => $userId,'delete_time'=>0])->find();
        if ($member->isEmpty()) {
            return '用户不存在';
        }

        $member->is_cancel_account = $cancelStyle;
        $member->cancel_account_apply_time = time();
        $member->save();

        return true;
    }
    /**
     * 撤销注销用户申请
     */
    static function rollbackCancleUserAccountApply($userId) {
        $model = new MemberModel();
        $member =  $model->where(['id' => $userId,'delete_time'=>0])->find();
        if ($member->isEmpty()) {
            return '用户不存在';
        }

        $member->is_cancel_account = 0;
        $member->cancel_account_apply_time = 0;
        $member->save();

        return true;
    }

    /**
     * 获取等待删除数据的注销用户列表
     */
    static function findNeedDeleteDataCancelAccountMembers($uniacid)
    {
        global $_W;
        $_W['uniacid'] = $uniacid;
        $config = app(SyssetDao::class)->sysSetsConfig('member');
        $delete_days = $config['cancel_account_delete_days'] ?? 30;
        if ($delete_days <= 0) {
            $delete_days = 30;
        }
        // 获取需要删除数据的用户
        $members = MemberModel::where('is_cancel_account', '>', 0)
            ->where('delete_time', 0)
            ->where('cancel_account_apply_time', '>', 0)
            ->where('cancel_account_apply_time', '<', time() - $delete_days * 86400)
            ->where('uniacid', '=', $uniacid)
            ->field(MemberModel::scene_fields('delete'))
            ->select();
        if ($members->isEmpty()) return [];

        $members->toArray();
        return $members->toArray();
    }

    /**
     * 注销用户删除数据操作
     * @param $userId
     * @return string|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    static function deleteUserAccountData($userId)
    {
        // 如果用户未申请注销账号，不允许删除用户数据
        $model = new MemberModel();
        $member =  $model->where(['id' => $userId,'delete_time'=>0])->find();
        if ($member->isEmpty()) {
            return '用户不存在';
        }
        if ($member['is_cancel_account'] == 0) {
            return '当前帐号未申请注销账号';
        }

        $newname = '已注销用户';
        $new_openid = 'delete_' . $member['openid'];

        $update = [
            'openid'=> $new_openid,
        ];
        OrderModel::where('member_id', $member['id'])->update($update);
        MemberCardOrderModel::where('member_id', $member['id'])->update($update);
        ServiceFeeOrderModel::where('member_id', $member['id'])->update($update);

        $update = ['openid'=> $new_openid, 'deleted'=>1];
        // 删除病历
        PrescriptionPatientinfoModel::where('openid', $member['openid'])->update($update);
        PrescriptionOrderPatientinfoModel::where('openid', $member['openid'])->update($update);

        // 删除地址
        MemberAddressModel::where('member_id', $member['id'])->delete();

        $member->mobile = '';
        $member->carrier_mobile = '';
        $member->realname = $newname;
        $member->pwd = '';
        $member->weixin = '';
        $member->nickname = $newname;
        $member->nickname_wechat = $newname;
        $member->idnumber = '';
        $member->mobileverify = 0;
        $member->openid = $new_openid;
        $member->delete_time = time();
        $member->delete_reason = '帐号注销';
        $member->save();
    }

    static function deleteUserAccountDataJob()
    {
        $uniacids = AccountModel::field(AccountModel::scene_fields())->select()->toArray();

        if ($uniacids && is_array($uniacids)) {
            foreach ($uniacids as $uni) {
                $members = MemberCancelLogic::findNeedDeleteDataCancelAccountMembers($uni['uniacid']);
                foreach ($members as $member) {
                    MemberCancelLogic::deleteUserAccountData($member['id']);
                }
            }
        }
    }
}