<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\com\logic\clerk;

use app\com\logic\LoginLogic;
use app\com\service\WechatMemberService;
use app\common\logic\BaseLogic;
use app\core\dao\member\MemberDao;
use app\core\model\copartner\CopartnerUserModel;
use app\core\model\member\card\MemberCardOrderCardModel;
use app\core\model\member\card\MemberCardOrderModel;
use app\core\model\member\servicefee\MemberServicefeeModel;
use app\core\model\member\servicefee\MemberServicefeeOrderModel;
use app\model\ClerkModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\MemberModel;
use app\model\OrderGoodsModel;
use app\model\OrderModel;
use app\model\SettleModel;
use app\model\SettleWithdrawApplyModel;
use think\db\Query;

/**
 * 店长逻辑
 * Class LoginLogic
 * @package app\com\logic
 */
class ClerkLogic extends BaseLogic
{
    public function getClerkInfo($memberId)
    {
        $member_fields = [
            'id',
            'openid',
            'uniacid',
            'no_servicefee_pay',
            'copartner_id',
            'is_clerk',
            'clerk_status',
            'clerk_level',
            'mentor_id',
            'clerk_create_time',
        ];
        $member = MemberModel::where(['id' => $memberId])->field( implode(',', $member_fields) )->findOrEmpty()->toArray();

        $member['clerk_level_name'] = (new ClerkModel())->getLevelName($member['clerk_level']);

        if (!empty($member['mentor_id'])) {
            $mentor = MemberModel::where(['id' => $memberId])->field( 'nickname' )->findOrEmpty();
            $member['mentor_name'] = $mentor['nickname'] ?? '';
        }

        $cop = CopartnerUserModel::where(['id' => $member['copartner_id']])->field('mcnname,money')->findOrEmpty()->toArray();

        if (!empty($cop)) {
            $member['copartner_name'] = $cop['mcnname'];
//            $money = json_decode($cop['money'], true);

            list($mustPayService, $isOpenServicefee, $servicefeeid, $isCycleTime, $expireTime) = p('clerk')->checkMustPayServicefee($member);

            $service_fee = MemberServicefeeModel::where(['id' => $servicefeeid])->field('name,price,validate')->findOrEmpty()->toArray();

            $check = p('servicefee')->check_Hasget($servicefeeid, $member['openid']);
            // $result['errno'] = 1 未支付
            // $result['errno'] = 0 && $result['using'] == -1; 已过期
            $notPayOrExpire = $check['errno'] == 1 || ($check['errno'] == 0 && $check['using'] == -1);

            $apply_total_money = (new SettleWithdrawApplyModel())->getWithdrawTotal(['openid' => $member['openid'], 'money_type' => 0,  ['status', '>=', 0]]);
            $tradeSet = m('common')->getSysset('trade', 1);//获取交易设置
            $tradeSet['total_withdraw_money'] = $tradeSet['total_withdraw_money'] ?? 1000;
            $isOver = $apply_total_money >= $tradeSet['total_withdraw_money'];

            // validate
            if (!$isOver) {
                $text = '当前提现收益未达到 1000 元，免收店铺服务费';
            } else if ($check['errno'] == 1) {
                $text = '服务费未支付';
            } else if ($check['errno'] == 0 && $check['using'] == -1) {
                $text = '服务费已过期';
            } else {
                // 从 $check 计算出剩余时间
                $expireTime = $check['expire_time'];
                $leftTime = $expireTime - time();
                $text = '剩余 ' . floor($leftTime / 86400) . '天' . floor($leftTime % 86400 / 3600) . '时' . floor($leftTime % 3600 / 60) . '分 到期';
            }

            $validate_map = [
                '-1'=> '永久有效 ',
                '1'=> '年',
                '2'=> '季',
                '3'=> '月',
                '4'=> '周',
                '5'=> '日',
                '6'=> '次',
            ];

            // 不需要支付服务费
            $member['service_fee'] = [
                'id'    => $servicefeeid,
                'validate'=> $validate_map[$service_fee['validate']] ?? '',
                'name' => $service_fee['name'],
                'price' => $service_fee['price'],
                'need_pay' => $mustPayService && $isOpenServicefee && $notPayOrExpire && $isOver,
                'expire_time' => $expireTime,
                'apply_total_money' => $apply_total_money,
                'text' => $text,
            ];

        } else {
            $member['copartner_name'] = '';
        }

        return $member;
    }

    /**
     * @param $memberId
     * @return array
     */
    public function getMemberInfo($memberId)
    {
        $member = MemberModel::where(['id' => $memberId])
            ->field(MemberModel::scene_fields('detail'))
            ->with(['clerk', 'mentor', 'onmember','copartner'])
            ->withCount(['finishOrders'=>'finish_orders_count'])
            ->withSum(['finishOrders'=>'finish_orders_price'],'price')
            ->withMax(['orders'=>'last_order_create_time'],'createtime')
            ->findOrEmpty()
            ->toArray();

        if (empty($member)) {
            return [];
        }

        return $member;
    }

    /**
     * @param $member_id
     * @param $type 0 onmid 1 clerk_id
     * @param $is_clerk
     * @param $page
     * @param $size
     * @param $keyword
     * @param bool $get_total 是否统计返回总数
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMembers($onmid, $clerk_id, $is_clerk, $keyword = '', $page = 1, $size = 10, $get_total = false)
    {
        $fiels = 'id,openid,avatar,level,clerk_level,realname,nickname,weixin,mobile,clerk_id,onmid,is_clerk,clerk_status,';
        $fiels .= 'clerk_create_time,createtime,onmid_create_time,clerk_id_create_time';

        $where = [];
        if (!empty($onmid) || $onmid !== '') {
            $where['onmid'] = $onmid;
        }
        if (!empty($clerk_id) || $clerk_id !== '') {
            $where['clerk_id'] = $clerk_id;
        }

        if ($is_clerk !== null && $is_clerk !== '') {
            if ($is_clerk === 1 || $is_clerk === '1') {
                $where['is_clerk'] = 1;
                $where['clerk_status'] = 1;
            } else {
                $where['is_clerk'] = 0;
            }
        }
        if (!empty($keyword)) {
            // 生成独立查询sql，针对多个字段模糊查询
            $where[] = ['realname|nickname|mobile', 'like', "%{$keyword}%"];
        }
        if ($get_total) {
            $total = MemberModel::where($where)->count();
        } else {
            $total = 0;
        }

        $members = MemberModel::where($where)->field($fiels)->page($page, $size)->select()->toArray();
        
        if (!empty($members)) {
            $members_ids = array_column($members, 'id');

            // onuid_members count group by onuid
            $onuid_members = MemberModel::where(['onmid' => $members_ids])->field('onmid,count(*) as count')->group('onmid')->select()->toArray();
            $onuid_members = array_column($onuid_members, 'count', 'onmid');

            // clerk_id members count group by clerk_id
            $clerk_members = MemberModel::where(['clerk_id' => $members_ids,'is_clerk'=>1, 'clerk_status'=>1])->field('clerk_id,count(*) as count')->group('clerk_id')->select()->toArray();
            $clerk_members = array_column($clerk_members, 'count', 'clerk_id');

            // orders create time group by member_id, order by id desc
            $order_fields = 'member_id,max(createtime) as createtime, count(*) as order_count, sum(price) as total_price';
            $orders = OrderModel::where(['member_id' => $members_ids,'status'=>3])->field($order_fields)->group('member_id')->select()->toArray();
//            $orders = array_column($orders, 'createtime', 'member_id');
            $orders = array_column($orders, null, 'member_id');

            // 获取用户是否参与980活动
            $multi_diy_attrs = (new DiyattrsModel())->getMultiValues(DiyattrsEnums::TYPE_USER, $members_ids, DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
            // 获取店长等级
            $clerk_levels = (new ClerkModel())->getLevelNames(true, true);
            // 获取会员等级
            $member_levels =  (new MemberModel())->getLevelNames(true);

            $times = [
                'onmid_create_time',
                'clerk_id_create_time',
                'clerk_create_time',
            ];

            foreach ($members as &$m) {
                $m['avatar'] = tomedia($m['avatar']);
                $m['level_name'] = $member_levels[$m['level']] ?? '普通会员';
                $m['clerk_level_name'] = ($m['is_clerk']==1 && $m['clerk_status']==1) ? $clerk_levels[$m['clerk_level']] : '';

                $m['last_order_time'] = (isset($orders[$m['id']]) && !empty($orders[$m['id']])) ? date('Y-m-d H:i:s', $orders[$m['id']]['createtime']) : '';
                $m['order_count'] = $orders[$m['id']]['order_count'] ?? 0; // 添加订单数量信息
                $m['total_price'] = $orders[$m['id']]['total_price'] ?? '0.00'; // 订单总金额

//                $m['is_activity_980_user'] = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $m['id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
                $m['is_activity_980_user'] = (int)($multi_diy_attrs[$m['id']] ?? 0);
                $m['onuid_member_count'] = $onuid_members[$m['id']] ?? 0;
                $m['clerk_member_count'] = $clerk_members[$m['id']] ?? 0;

                $m['create_time'] = date('Y-m-d H:i:s', $m['createtime']);
                foreach ($times as $time) {
                    $m[$time] = $m[$time] ? date('Y-m-d H:i:s', $m[$time]) : '';
                }
            }
        }
        return [$members, $total];
    }

    /**
     * @param $member_id
     * @param $money_type int 提现金额类型 0: 店长提现，1 合伙人提现
     * @param $status string 状态 空：全部 1待审核 2 成功 -1 失败
     * @param $page
     * @param $pageSize
     * @param $sum_field string 统计字段
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSettleWithdrawApplyList($member_id, $money_type, $status, $page = 1, $page_size = 10, $sum_field = '')
    {
        $where = ['mid' => $member_id, 'money_type' => $money_type];
        if (!in_array($status, ['',null],true)) {
            $where['status'] = $status;
        }

        $applies = SettleWithdrawApplyModel::where($where)->page($page, $page_size)->select();
        if ($sum_field) {
            $total = SettleWithdrawApplyModel::where($where)->sum($sum_field);
        } else {
            $total = '0.00';
        }
        $list = [];
        foreach ($applies as $apply) {
            $item = $apply->toArray();
            $item['create_time'] = $apply->create_time;
            $list[] = $item;
        }

        return [$list, $total];
    }

    /**
     * 服务费列表
     * @param $ember_id
     * @param $status
     * @param $page
     * @param $pageSize
     * @param string $sum_field 统计字段
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getServiceFeeOrders($ember_id, $status, $page, $pageSize, $sum_field = '')
    {
        $where = ['member_id' => $ember_id];
        if (!in_array($status, ['',null],true)) {
            $where['status'] = $status;
        }
        $orders = (new MemberServicefeeOrderModel())->where($where)->page($page, $pageSize)->select();
        if ($sum_field) {
            $total = (new MemberServicefeeOrderModel())->where($where)->sum($sum_field);
        } else {
            $total = '0.00';
        }

        $list = [];
        foreach ($orders as $order) {
            $item = $order->toArray();
            $item['create_time'] = date("Y-m-d H:i:s", $order->createtime);
            $item['pay_time'] = date('Y-m-d H:i:s', $order->paytime);
            $list[] = $item;
        }

        return [$list, $total];
    }

    /**
     * opts Array（以下为参数说明）
     * - status 订单状态 '' 所有 0 待付款 1 待发货 2 待收货 3 已完成 -1 关闭/取消
     * - _total bool 是否统计返回总数
     * - clerk_id int 店长ID
     * - member_id int 会员ID
     * - no_member_id int 不查询会员ID
     * - keyword string 关键字搜索
     *
     * @param $opts
     * @param $page
     * @param $page_size
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrders($opts, $page, $page_size)
    {
        // 参数处理
        $where = ['deleted'=>0,'userdeleted'=>0];
        if (isset($opts['status']) && $opts['status'] !== '') {
            $where['status'] = $opts['status'];
        }

        if (!empty($opts['keyword'])) {
            $where[] = ['ordersn|id|expresssn', 'like', "%{$opts['keyword']}%"];
        }
        $fields = [
            'clerk_id',
            'member_id',
            'goods_type',
        ];
        foreach ($fields as $field) {
            if (isset($opts[$field]) && $opts[$field]!=='') {
                $where[$field] = $opts[$field];
            }
        }

        if (isset($opts['no_member_id']) && $opts['no_member_id']!=='') {
            $where[] = ['member_id', '<>', $opts['no_member_id']];
        }

        // 查询
        $orders = OrderModel::where($where)
//            ->hasOne(MemberModel::class, ['id'=>16547])
            ->with(['orderGoods' => function(Query $query) {
                $query->with(['goods' => function($query) {
                    $query->field('id,title,thumb');
                }])->field(OrderGoodsModel::scene_fields('list'));
            }, 'member' => ['clerkLevel','level'],'clerk'=>['clerkLevel','level']])
            ->field(OrderModel::scene_fields('list'))
            ->page($page, $page_size)->select();

        // 额外字段
        foreach ($orders as $order) {
            $filter = [SettleModel::ROLE_KEY_CLERK]; // 只显示店长的收益
            // 预计收益
            $order->set('order_expect_commissions',
                (new SettleModel())->getOrderExpectCommissions($order['id'], SettleModel::ORDER_TYPE_GOODS, $filter));
            // 实际结算收益
            $order->set('order_settle_commissions',
                (new SettleModel())->getOrderSettleCommissions($order['id'], SettleModel::ORDER_TYPE_GOODS, $filter));
        }

        // 条数统计
        if (isset($opts['_total']) && $opts['_total']) {
            $total = OrderModel::where($where)->count();
            return ['list'=>$orders, 'total'=> $total];
        } else {
            return ['list'=>$orders];
        }
    }

    public function getIndexData($memberId)
    {
        $member = MemberModel::where(['id'=>$memberId])
            ->field(MemberModel::scene_fields('default'))
            ->withCount(['subMembers','subClerks'])
            ->findOrEmpty()->toArray();

        if (empty($member)) {
            return [];
        }

        $settleModel = new SettleModel();
        $ClerkModel = new ClerkModel();
        $saler_commission = $ClerkModel->getOrderIds($memberId,0);

        // 销售提成 几项数据
        $self_order_commission = $settleModel->getOrderSettleCommission($saler_commission['self_order'],SettleModel::ROLE_KEY_CLERK);
        $self_vip_order_commission = $settleModel->getOrderSettleCommission($saler_commission['self_vip_order'],SettleModel::ROLE_KEY_CLERK);
        $self_pop_order_commission = $settleModel->getOrderSettleCommission($saler_commission['self_pop_order'],SettleModel::ROLE_KEY_CLERK);
        $self_activity_order_commission = $settleModel->getOrderSettleCommission($saler_commission['self_activity_order'],SettleModel::ROLE_KEY_CLERK);

        // 会员卡收益
        $mcorder_ids = pdo_getall('elapp_shop_member_card_order', ['clerk_id' => $memberId, 'status'=>1, 'is_settle'=>1], 'id');
        $mcorder_ids = $mcorder_ids ? array_column($mcorder_ids, 'id'):[];
        $self_member_card_order_commission = $settleModel->getOrderSettleCommission($mcorder_ids,SettleModel::ROLE_KEY_CLERK, $settleModel::ORDER_TYPE_MEMBERCARD);

        $deduction_money = $settleModel->getSettleDeduction(SettleModel::ROLE_KEY_CLERK, $member['openid']);

        $data = [
            // 代扣费用 消费预留金 店铺服务费 灵工平台服务费
            'withdraw_freeze_fee'     => $deduction_money['withdraw_freeze_fee'] ?? 0,
            'withdraw_personal_fee'   => $deduction_money['withdraw_personal_fee'] ?? 0,
            'servicefee_order_deduct' => $deduction_money['service_fee'] ?? 0,
            // 团队管理 我的店长 我的会员
            'sub_clerks_count' => $member['sub_clerks_count'] ?? 0,
            'sub_members_count' => $member['sub_members_count'] ?? 0,
            // 销售提成
            'self_order_commission'        => $self_order_commission,
            'goods_order_commission'       => $self_pop_order_commission,
            'vip_goods_order_commission'   => $self_vip_order_commission,
            'activity_order_commission'    => $self_activity_order_commission,
            'member_card_order_commission' => $self_member_card_order_commission,
            $saler_commission
        ];
        return $data;
    }

    public function getMcOrders(array $opts, $page, $page_size)
    {
        // 参数处理
        $where = [];
        if (isset($opts['status']) && $opts['status'] !== '') {
            $where['status'] = $opts['status'];
        }

        if (!empty($opts['keyword'])) {
            $where[] = ['orderno|id', 'like', "%{$opts['keyword']}%"];
        }
        $fields = [
            'clerk_id',
            'member_id',
        ];
        foreach ($fields as $field) {
            if (isset($opts[$field]) && $opts[$field]!=='') {
                $where[$field] = $opts[$field];
            }
        }

        // 查询
        $orders = MemberCardOrderModel::where($where)
            ->with(['orderGoods' => function(Query $query) {
                $query->with(['card' => function($query) {
                    $query->field('id,name,thumb');
                }])->field(MemberCardOrderCardModel::scene_fields('list'));
            },'member'=>['clerkLevel','level'],'clerk'=>['clerkLevel','level']])
            ->field(MemberCardOrderModel::scene_fields('list'))
            ->page($page, $page_size)->select();

        // 额外字段
        foreach ($orders as $order) {
            $filter = [SettleModel::ROLE_KEY_CLERK]; // 只显示店长的收益
            // 预计收益
            $order->set('order_expect_commissions',
                (new SettleModel())->getOrderExpectCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
            // 实际结算收益
            $order->set('order_settle_commissions',
                (new SettleModel())->getOrderSettleCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
        }

        // 条数统计
        if (isset($opts['_total']) && $opts['_total']) {
            $total = MemberCardOrderModel::where($where)->count();
            return ['list'=>$orders, 'total'=> $total];
        } else {
            return ['list'=>$orders];
        }
    }

    /**
     * 返回是否是店员或者店员信息
     * @param array $where
     * @param $return_bool
     * @param string|null $field
     * @return array|bool|\think\Model|null
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function isClerk(array $where, $return_bool = true, ?string $field = 'id,uid,realname,mobile,avatar')
    {
        $field .= ',is_clerk,clerk_status,clerk_create_time,clerk_level,clerk_black,fix_clerk_id,clerk_id';
        $res = app(MemberDao::class)->getOne($where, $field);
        if ($return_bool) {
            return $res && $res['is_clerk'] == 1 && $res['clerk_status'] == 1 && empty($res['clerk_black']);
        } else {
            return $res;
        }
    }

    /**
     * 店长工作台登录
     * @param $data
     * @return array|false|void
     */
    function login($data)
    {
        try {
            $res = $this->isClerk(['mobile' => $data['mobile']]);
            if($res){
                $response['phone_info']['purePhoneNumber'] = $data['mobile'];
                $member = app(WechatMemberService::class, [$response, $data['terminal'], $data['i']]);
                $memberInfo = $member->getResopnseByMemberInfo()->getMemberInfo();
                if (!empty($memberInfo)) {
                    // 更新登录信息
                    LoginLogic::updateLoginInfo($memberInfo['id']);
                }
                return result(0, '登录成功', $memberInfo);
            }
            return result(6001, '账户不存在');
        } catch (\Exception  $e) {
            self::$error = $e->getMessage();
            return result(-1, $e->getMessage());
        }
    }
}