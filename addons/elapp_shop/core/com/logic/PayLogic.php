<?php

namespace app\com\logic;

use app\com\enum\member\MemberTerminalEnum;
use app\com\enum\order\OrderEnum;
use app\com\service\payment\AliPayService;
use app\com\service\payment\BalancePayService;
use app\com\service\payment\WxPayService;
use app\common\logic\BaseLogic;
use app\common\service\JsonService;
use app\controller\AppError;
use app\core\com\enum\payment\PayTypeEnum;
use app\core\dao\sysset\SyssetDao;
use app\model\OrderGoodsModel;
use app\model\OrderModel;
use jeepay\Pay;
use think\facade\Cache;
use think\facade\Route;
use web\model\core\CorePaylogModel;

class PayLogic extends BaseLogic
{
    /**
     * @param array $params ['openid', 'order_id', 'pay_type', 'member_uid']
     * @return \think\response\Json|void
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    static function unifyPay(array $params)
    {
        //1.查询订单状态和订单商品库存
        $order = OrderModel::where(['id' => $params['order_id']])->findOrEmpty()->toArray();
        if (empty($order)) {
            return JsonService::fail(AppError::getError(AppError::$OrderNotFound), [], AppError::$OrderNotFound);
        }
        if (OrderEnum::ORDER_STATUS_0_1 == $order['status']) {
            return JsonService::fail(AppError::getError(AppError::$OrderCannotPay), [], AppError::$OrderCannotPay);
        }
        if (OrderEnum::ORDER_STATUS_1 <= $order['status'] ) {
            return JsonService::fail(AppError::getError(AppError::$OrderAlreadyPay), [], AppError::$OrderAlreadyPay);
        }
        //2.检查订单的产品库存
        $over_stock_goods = OrderGoodsModel::overStockGoods($params['order_id']);
        if (!empty($over_stock_goods)) {
            return JsonService::fail(AppError::getError(AppError::$GoodsOverStock), $over_stock_goods, AppError::$GoodsOverStock);
        }
        //3.查询/插入支付记录状态
        $paylog = CorePaylogModel::where(['tid' => $order['ordersn']])->findOrEmpty()->toArray();
        if (empty($paylog)) {
            $paylog = [
                'type' => 'wechat',
                'acid'=> $params['i'],
                'uniontid'=>'',
                'tag'=>'',
                'is_usecard'=>0,
                'card_type'=>0,
                'card_id'=>'',
                'card_fee'=>'',
                'encrypt_code'=>'',
                'is_wish'=>'',
                'coupon'=>'',
                'openid' => $params['openid'] ?? $order['openid'],
                'tid' => $order['ordersn'],
                'fee' => $order['price'],
                'status' => 0,
                'module' => 'elapp_shop',
            ];
            CorePaylogModel::create($paylog);
        }
        if ($paylog['status'] == 1) { //已支付 此处不做订单状态的更改，理应接口查询订单状态？
            return JsonService::fail(AppError::getError(AppError::$OrderAlreadyPay), [], AppError::$OrderAlreadyPay);
        }
        //4.根据支付方式调起支付并返回对应参数
        if ('jeepay' == $params['pay_channel']) {
            $expiredTime = app(SyssetDao::class)->sysSetsConfig('closeorder', 1);
            $attach = [
                'extParam' => $params['i'] . ':0',
                'expiredTime' => $expiredTime * 24 * 60 * 60,
                'notifyUrl' => mobileUrl('order/pay/jeepayNofify', [], true),
                'returnUrl' => mobileUrl('order/pay/success', ['id' => $order['id']], true),
            ];
            $paytype = $params['pay_type'] ?? PayTypeEnum::WX_JSAPI;
            if (PayTypeEnum::WX_BAR == $paytype) {
                $attach['channelExtra'] = json_encode(['authCode' => $params['auth_code']]);
            } elseif (PayTypeEnum::WX_APP == $paytype) {

            }
            file_put_contents('pay.log', 'paytype: ' . PayTypeEnum::getPayTypeName($paytype) . "\n" . var_export(PayTypeEnum::getPayTypeName(), true), FILE_APPEND);
            $result = Cache::remember('jeepay_pay_' . md5($order['ordersn'] . $order['member_id'] . $paytype), function () use ($order, $attach, $paytype) {
                return app(Pay::class)->unifiedOrder($order['ordersn'] . '|' . $order['member_id'] . $paytype, bcmul($order['price'], 100, 2), '商品订单支付', '全民供享订单支付', PayTypeEnum::getPayTypeName($paytype), $attach);
            });

            if (!empty($result['code'])) {
                return JsonService::fail($result['msg'], [], $result['code']);
            }
            return JsonService::success('success', $result, 0, 0);
        } else {
            if (PayTypeEnum::WX_LITE == $params['pay_type']) { //微信小程序支付
                $attach = [
                    'openid' => $params['openid'],
                    'notify_url' => parent::url('/app/pay/notify/mini', ['i' => $params['i']], false, true),
                    'attach' => 'product',
                ];
                $result = (new WxPayService($params['i'], MemberTerminalEnum::WECHAT_MMP))->unify($order['ordersn'], bcmul($order['price'], 100, 2), '商品订单支付', 'JSAPI', $attach, 'MiniProgram');
                if (!empty($result['code'])) {
                    return JsonService::fail($result['msg'], [], $result['code']);
                }
                return JsonService::success('success', $result, 0, 0);
            } elseif (PayTypeEnum::ALI_PAY == $params['pay_type']) { //支付宝支付
                $result = (new AliPayService($params['i']))->h5($order['ordersn'], '商品订单支付', $order['price'])->toArray();
                return JsonService::success('success', $result, 0, 0);
            } elseif (PayTypeEnum::BALANCE_PAY == $params['pay_type']) { //余额支付
                $result = app(BalancePayService::class)->OrderPay($order, $params['member_uid']);
                if (!empty($result['code'])) {
                    return JsonService::fail($result['msg'], [], $result['code']);
                }
                return JsonService::success('余额支付成功', $result['data'], 0, 0);
            } elseif (PayTypeEnum::WX_APP == $params['pay_type']) { //微信APP支付
                $attach = [
                    'notify_url' => parent::url('/app/pay/notify/app', ['i' => $params['i']], false, true),
                    'attach' => 'product',
                ];
                $result = (new WxPayService($params['i'], MemberTerminalEnum::ANDROID))->unify($order['ordersn'], bcmul($order['price'], 100, 2), '商品订单支付', 'APP', $attach);
                if (!empty($result['code'])) {
                    return JsonService::fail($result['msg'], [], $result['code']);
                }
                return JsonService::success('success', $result, 0, 0);
            } else {
                return JsonService::fail('支付方式错误');
            }
        }
    }
}