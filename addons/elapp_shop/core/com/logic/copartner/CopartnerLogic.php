<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
namespace app\com\logic\copartner;

use app\com\logic\LoginLogic;
use app\com\service\WechatMemberService;
use app\common\logic\BaseLogic;
use app\controller\AppError;
use app\core\model\copartner\CopartnerAccountModel;
use app\core\model\copartner\CopartnerUserModel;
use app\core\model\member\card\MemberCardOrderCardModel;
use app\core\model\member\card\MemberCardOrderModel;
use app\core\model\member\servicefee\MemberServicefeeModel;
use app\core\model\member\servicefee\ServiceFeeOrderFeeModel;
use app\core\model\member\servicefee\ServiceFeeOrderModel;
use app\model\ClerkModel;
use app\model\CopartnerModel;
use app\model\DiyattrsEnums;
use app\model\DiyattrsModel;
use app\model\MemberModel;
use app\model\OrderGoodsModel;
use app\model\OrderModel;
use app\model\SettleModel;
use app\model\SettleWithdrawApplyModel;
use app\order\logic\OrderLogic;
use app\plugin\copartner\core\logic\CopartnerAccountLogic;
use think\db\Query;

/**
 * 店长逻辑
 * Class LoginLogic
 * @package app\com\logic
 */
class CopartnerLogic extends BaseLogic
{
    public function getCopartnerAccount($memberId)
    {
        return CopartnerAccountModel::where(['mid' => $memberId])
            ->with(['copartner'=>['copartnerAccounts','channel'],'member'])
            ->findOrEmpty()->toArray();
    }

    /**
     * 获取子帐号列表（招商经理）
     * @param $memberId
     * @param null|int $status null或'' 则不限制状态
     * @return array
     */
    public function getSubAccounts($page = 1, $copartner_id, $status = null)
    {
        $where = [
            'copartner_id' => $copartner_id,
            'isfounder' => 0,
            'del_at' => 0,
        ];
        if ($status !== null && $status !== '') {
            $where['status']  = $status;
        }
        $accounts = CopartnerAccountModel::where($where)
            ->field(CopartnerAccountModel::scene_fields('list'))
            ->page($page, 10)->findOrEmpty();

        return $accounts->toArray();
    }

    /**
     * @param $id int 子帐号ID
     * @param $founderAccount array 创始人的帐号信息
     * @param $status int 审核状态 0 拒绝 1 审核通过
     * @return int|void
     */
    public function verifySubAccounts($id, $founderAccount, $status)
    {
        $where = [
            'id' => $id,
            'copartner_id' => $founderAccount['copartner_id'],
            'status'=>0,
            'del_at' => 0,
        ];
        $subAccount = CopartnerAccountModel::where($where)
            ->with('member')
            ->findOrEmpty();
        if ($subAccount->isEmpty()) {
            return AppError::$AccessDenied;
        }

        if (!in_array($status, [0,1])) {
            return AppError::$ParamsError;
        }

        if ($status) {
            // 1. 注册招商经理时，也要处理帮扶关系，建立会员-店长关系，
            // 他的推荐人就是机构创始人
            // 本身先判断是否存在，如果用户数据存在字段值则不修改，不存在则把创始人的数据丢给他

            $member = MemberModel::where(['id'=>$subAccount['mid']])->findOrEmpty();

            if ($member->isEmpty()) {
                return AppError::$UserNotFound;
            }

            $member->onmid_create_time = empty($member->onmid_create_time) ? time() : $member->onmid_create_time;
            $member->onmid = empty($member->onmid) ? $founderAccount['member']['id'] : $member->onmid;
            $member->copartner_id_create_time = empty($member['copartner_id']) ? time() : $member['copartner_id_create_time'];
            $member->copartner_id = $founderAccount['copartner_id'];
            $member->copartner_account_id_create_time = empty($member['copartner_account_id']) ? time() : $member['copartner_account_id_create_time'];
            $member->copartner_account_id = empty($member['copartner_account_id']) ? $founderAccount['id'] : $member['copartner_account_id'];


//            // 如果创始人是店员，需要同步招商经理的店员
            if ($founderAccount['member']['is_clerk'] && $founderAccount['member']['clerk_status'] == 1) {
                $member->clerk_id_create_time = empty($member['clerk_id']) ? time() : $member['clerk_id_create_time'];
                $member->clerk_id = empty($member['clerk_id']) ? $founderAccount['member']['id'] : $member['clerk_id'];
                $member->mentor_id_create_time = empty($member['mentor_id']) ? time() : $member['mentor_id_create_time'];
                $member->mentor_id = empty($member['mentor_id']) ? $founderAccount['member']['id'] : $member['mentor_id'];
            }
            $member->save();
            $subAccount->status = 1;
            $subAccount->save();

            //发送消息通知--招商经理
            (new CopartnerModel())->sendMessage($member['openid'], array('nickname' => $member['nickname'], 'audit_time' => time()), 'TM_COPARTNER_BUSINESS_BECOME');
        } else {
            $subAccount->del_at = time();
            $subAccount->save();
        }

        return true;
    }

    /**
     * 获取合伙人基本信息
     * @param $copartnerId
     * @param $count bool 是否统计下级数量
     * @return array
     */
    public function getCopartnerInfo($copartnerId, $subAccountId = 0)
    {
        if ($subAccountId) {
            $f = function ($query) use ($subAccountId) {
                $query->where(['copartner_account_id' => $subAccountId]);
            };
            $withcount = ['subClerks' => $f,'subMembers' => $f];
        } else {
            $withcount = ['subClerks','subMembers'];
        }

        $copartnerUser = CopartnerUserModel::where(['id' => $copartnerId])
            ->field( CopartnerUserModel::scene_fields('default'))
            ->with(['org','member'])
            ->withCount($withcount)
            ->findOrEmpty()->toArray();

        return $this->infoResult($copartnerUser);
    }

    private function infoResult($copartnerUser)
    {
        if (empty($copartnerUser)) {
            return [];
        }

        $copartnerUser['copartner_level_name'] = (new CopartnerModel())->getLevelName($copartnerUser['clerk_level']);
        $copartnerUser['copartner_name'] = $copartnerUser['mcnname'];

        return $copartnerUser;
    }

    /**
     * @deprecated
     * @param $memberId
     * @return array
     */
    public function getMemberInfo($memberId)
    {
        $member = MemberModel::where(['id' => $memberId])
            ->field(MemberModel::scene_fields('detail'))
            ->with(['clerk', 'mentor', 'onmember','copartner'])
            ->withCount(['finishOrders'=>'finish_orders_count'])
            ->withSum(['finishOrders'=>'finish_orders_price'],'price')
            ->withMax(['orders'=>'last_order_create_time'],'createtime')
            ->findOrEmpty()
            ->toArray();

        if (empty($member)) {
            return [];
        }

        return $member;
    }

    /**
     *  opts Array（以下为参数说明）
     *  - copartner_account_id int 合伙人子账户ID（招商经理id)
     *  - clerk_id int 店长ID
     *  - no_clerk_id int 不查询店长ID
     *  - is_clerk int 是否店长 1 是 0 否
     *  - keyword string 关键字搜索
     *
     * @param $copartner_id
     * @param $opts
     * @param $clerk_id
     * @param $is_clerk
     * @param $keyword
     * @param $page
     * @param $size
     * @param $get_total
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getMembers($copartner_id, $opts, $clerk_id, $is_clerk, $keyword = '', $page = 1, $size = 10, $get_total = false)
    {
        $fiels = 'id,openid,copartner_id,avatar,level,clerk_level,realname,nickname,weixin,mobile,clerk_id,onmid,is_clerk,clerk_status,';
        $fiels .= 'clerk_create_time,createtime,onmid_create_time,clerk_id_create_time';

        $where = ['copartner_id'=>$copartner_id];
        if (!empty($opts['copartner_account_id'])) {
            $where['copartner_account_id'] = $opts['copartner_account_id'];
        }
        if (!empty($clerk_id) || $clerk_id !== '') {
            $where['clerk_id'] = $clerk_id;
        }

        if (isset($opts['no_clerk_id']) && $opts['no_clerk_id'] !== '') {
            $where[] = ['clerk_id', '<>', $opts['no_clerk_id']];
        }

        if ($is_clerk !== null && $is_clerk !== '') {
            if ($is_clerk === 1 || $is_clerk === '1') {
                $where['is_clerk'] = 1;
                $where['clerk_status'] = 1;
            } else {
                $where['is_clerk'] = 0;
            }
        }
        if (!empty($keyword)) {
            // 生成独立查询sql，针对多个字段模糊查询
            $where[] = ['realname|nickname|mobile', 'like', "%{$keyword}%"];
        }
        if ($get_total) {
            $total = MemberModel::where($where)->count();
        } else {
            $total = 0;
        }

        $members = MemberModel::where($where)->field($fiels)->with(['clerk','copartner','onmember','mentor'])->page($page, $size)->select()->toArray();

        if (!empty($members)) {
            $members_ids = array_column($members, 'id');

            // onuid_members count group by onuid
            $onuid_members = MemberModel::where(['onmid' => $members_ids])->field('onmid,count(*) as count')->group('onmid')->select()->toArray();
            $onuid_members = array_column($onuid_members, 'count', 'onmid');

            // clerk_id members count group by clerk_id
            $clerk_members = MemberModel::where(['clerk_id' => $members_ids,'is_clerk'=>1, 'clerk_status'=>1])->field('clerk_id,count(*) as count')->group('clerk_id')->select()->toArray();
            $clerk_members = array_column($clerk_members, 'count', 'clerk_id');

            // orders create time group by member_id, order by id desc
            $order_fields = 'member_id,max(createtime) as createtime, count(*) as order_count, sum(price) as total_price';
            $orders = OrderModel::where(['member_id' => $members_ids,'status'=>3])->field($order_fields)->group('member_id')->select()->toArray();
//            $orders = array_column($orders, 'createtime', 'member_id');
            $orders = array_column($orders, null, 'member_id');

            // 获取用户是否参与980活动
            $multi_diy_attrs = (new DiyattrsModel())->getMultiValues(DiyattrsEnums::TYPE_USER, $members_ids, DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
            // 获取店长等级
            $clerk_levels = (new ClerkModel())->getLevelNames(true, true);
            // 获取会员等级
            $member_levels =  (new MemberModel())->getLevelNames(true);

            $times = [
                'onmid_create_time',
                'clerk_id_create_time',
                'clerk_create_time',
            ];

            foreach ($members as &$m) {
                $m['avatar'] = tomedia($m['avatar']);
                $m['level_name'] = $member_levels[$m['level']] ?? '普通会员';
                $m['clerk_level_name'] = ($m['is_clerk']==1 && $m['clerk_status']==1) ? $clerk_levels[$m['clerk_level']] : '';

                $m['last_order_time'] = (isset($orders[$m['id']]) && !empty($orders[$m['id']])) ? date('Y-m-d H:i:s', $orders[$m['id']]['createtime']) : '';
                $m['order_count'] = $orders[$m['id']]['order_count'] ?? 0; // 添加订单数量信息
                $m['total_price'] = $orders[$m['id']]['total_price'] ?? '0.00'; // 订单总金额

//                $m['is_activity_980_user'] = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_USER, $m['id'], DiyattrsEnums::KEY_IS_ACTIVITY_980_USER);
                $m['is_activity_980_user'] = (int)($multi_diy_attrs[$m['id']] ?? 0);
                $m['onuid_member_count'] = $onuid_members[$m['id']] ?? 0;
                $m['clerk_member_count'] = $clerk_members[$m['id']] ?? 0;

                $m['create_time'] = date('Y-m-d H:i:s', $m['createtime']);
                foreach ($times as $time) {
                    $m[$time] = $m[$time] ? date('Y-m-d H:i:s', $m[$time]) : '';
                }
            }
        }
        return [$members, $total];
    }

    /**
     * @param $member_id
     * @param $money_type int 提现金额类型 0: 店长提现，1 合伙人提现
     * @param $status string 状态 空：全部 1待审核 2 成功 -1 失败
     * @param $page
     * @param $pageSize
     * @param $sum_field string 统计字段
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getSettleWithdrawApplyList($member_id, $money_type, $status, $page = 1, $page_size = 10, $sum_field = '')
    {
        $where = ['mid' => $member_id, 'money_type' => $money_type];
        if (!in_array($status, ['',null],true)) {
            $where['status'] = $status;
        }

        $applies = SettleWithdrawApplyModel::where($where)->page($page, $page_size)->select();
        if ($sum_field) {
            $total = SettleWithdrawApplyModel::where($where)->sum($sum_field);
        } else {
            $total = '0.00';
        }
        $list = [];
        foreach ($applies as $apply) {
            $item = $apply->toArray();
            $item['create_time'] = $apply->create_time;
            $list[] = $item;
        }

        return [$list, $total];
    }

    /**
     * 服务费列表
     * @param $ember_id
     * @param $status
     * @param $page
     * @param $pageSize
     * @param string $sum_field 统计字段
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
//    public function getServiceFeeOrders($ember_id, $status, $page, $pageSize, $sum_field = '')
//    {
//        $where = ['member_id' => $ember_id];
//        if (!in_array($status, ['',null],true)) {
//            $where['status'] = $status;
//        }
//        $orders = (new MemberServicefeeOrderModel())->where($where)->page($page, $pageSize)->select();
//        if ($sum_field) {
//            $total = (new MemberServicefeeOrderModel())->where($where)->sum($sum_field);
//        } else {
//            $total = '0.00';
//        }
//
//        $list = [];
//        foreach ($orders as $order) {
//            $item = $order->toArray();
//            $item['create_time'] = date("Y-m-d H:i:s", $order->createtime);
//            $item['pay_time'] = date('Y-m-d H:i:s', $order->paytime);
//            $list[] = $item;
//        }
//
//        return [$list, $total];
//    }

    /**
     * opts Array（以下为参数说明）
     * - status 订单状态 '' 所有 0 待付款 1 待发货 2 待收货 3 已完成 -1 关闭/取消
     * - _total bool 是否统计返回总数
     * - copartner_id int 合伙人ID
     * - clerk_id int 店长ID
     * - member_id int 会员ID
     * - no_member_id int 不查询会员ID
     * - keyword string 关键字搜索
     *
     * @param $opts
     * @param $page
     * @param $page_size
     * @return void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    public function getOrders($opts, $page, $page_size)
    {
        // 参数处理
        $where = ['deleted'=>0,'userdeleted'=>0];
        if (isset($opts['status']) && $opts['status'] !== '') {
            $where['status'] = $opts['status'];
        }

        if (!empty($opts['keyword'])) {
            $where[] = ['ordersn|id|expresssn', 'like', "%{$opts['keyword']}%"];
        }
        $fields = [
            'clerk_id',
            'member_id',
            'goods_type',
            'copartner_id',
            'copartner_account_id',
        ];
        foreach ($fields as $field) {
            if (isset($opts[$field]) && $opts[$field]!=='') {
                $where[$field] = $opts[$field];
            }
        }

        // 查询
        $orders = OrderModel::where($where)
//            ->hasOne(MemberModel::class, ['id'=>16547])
            ->with(['orderGoods' => function(Query $query) {
                $query->with(['goods' => function($query) {
                    $query->field('id,title,thumb');
                }])->field(OrderGoodsModel::scene_fields('list'));
            }, 'member' => ['clerkLevel','level'],'clerk'=>['clerkLevel','level']])
            ->field(OrderModel::scene_fields('list'))
            ->page($page, $page_size)->select();

        // 额外字段
        foreach ($orders as $order) {
            $filter = [SettleModel::ROLE_KEY_CLERK]; // 只显示店长的收益
            // 预计收益
            $order->set('order_expect_commissions',
                (new SettleModel())->getOrderExpectCommissions($order['id'], SettleModel::ORDER_TYPE_GOODS, $filter));
            // 实际结算收益
            $order->set('order_settle_commissions',
                (new SettleModel())->getOrderSettleCommissions($order['id'], SettleModel::ORDER_TYPE_GOODS, $filter));
        }

        // 条数统计
        if (isset($opts['_total']) && $opts['_total']) {
            $total = OrderModel::where($where)->count();
            return ['list'=>$orders, 'total'=> $total];
        } else {
            return ['list'=>$orders];
        }
    }

    public function getIndexData($memberId, $openid, $copartnerId, $accountId = 0)
    {
        $copartner = $this->getCopartnerInfo($copartnerId, $accountId);

        if (empty($copartner)) {
            return [];
        }

        //岗位绩效
        $ClerkModel = new ClerkModel();
        $SettleModel = new SettleModel();
        $saler_commission = $ClerkModel->getOrderIds($memberId,1, 1);
//        $self_order_commission = $SettleModel->getOrderSettleCommission($saler_commission['self_order'],SettleModel::ROLE_KEY_COPARTNER);
        $self_vip_order_commission = $SettleModel->getOrderSettleCommission($saler_commission['self_vip_order'],SettleModel::ROLE_KEY_COPARTNER);
        $self_pop_order_commission = $SettleModel->getOrderSettleCommission($saler_commission['self_pop_order'],SettleModel::ROLE_KEY_COPARTNER);
        $self_activity_order_commission = $SettleModel->getOrderSettleCommission($saler_commission['self_activity_order'],SettleModel::ROLE_KEY_COPARTNER);
        // member_card_order
        $mcorder_ids = pdo_getall('elapp_shop_member_card_order', ['copartner_id' => $copartnerId, 'status'=>1,'is_settle'=>1], 'id');
        $mcorder_ids = $mcorder_ids ? array_column($mcorder_ids, 'id'):[];
        $self_member_card_order_commission = $SettleModel->getOrderSettleCommission($mcorder_ids,SettleModel::ROLE_KEY_COPARTNER, $SettleModel::ORDER_TYPE_MEMBERCARD);

        // service_fee_order
        $sforder_ids = pdo_getall('elapp_shop_member_servicefee_order', ['copartner_id' => $copartnerId, 'status'=>1], 'id');
        $sforder_ids = $mcorder_ids ? array_column($sforder_ids, 'id'):[];
        $self_service_fee_order_commission = $SettleModel->getOrderSettleCommission($sforder_ids,SettleModel::ROLE_KEY_COPARTNER, $SettleModel::ORDER_TYPE_SERVICEFEE);

        // 代扣费用
        $deduction_money = $SettleModel->getSettleDeduction($SettleModel::ROLE_KEY_COPARTNER, $openid);

        // 招商经理统计
        $cop = CopartnerUserModel::where(['id' => $copartnerId])
            ->field('id')
            ->with(['subAccounts'])
            ->withCount('subAccounts')
            ->findOrEmpty()->toArray();
        if (!empty($cop['subAccounts'])) {
            $sub_account_count = count($cop['subAccounts']);
            $need_check_count = 0;
            foreach ($cop['subAccounts'] as $sub) {
                /// 是否已审核
                $need_check_count += $sub['status'] == 0 ? 1 : 0;
            }
        } else {
            $sub_account_count = $need_check_count = 0;
        }

        // 店长收益
        $clerk_commission_total = $SettleModel->getCommissionTotal($memberId, SettleModel::ROLE_KEY_CLERK);
        $copartner_commission_total = $SettleModel->getCommissionTotal($copartnerId, SettleModel::ROLE_KEY_COPARTNER);

        $data = [
            'clerk_commission_total'       => $clerk_commission_total,
            'copartner_commission_total'   => $copartner_commission_total,
            'sub_account_count'            => $need_check_count . ' / ' . ($sub_account_count - $need_check_count),
            'copartner_id'                 => $copartnerId,
            // 代扣费用 消费预留金, 灵工平台服务费
            'withdraw_freeze_fee'          => $deduction_money['withdraw_freeze_fee'] ?? 0,
            'withdraw_personal_fee'        => $deduction_money['withdraw_personal_fee'] ?? 0,
            //            'servicefee_order_deduct' => $deduction_money['service_fee'] ?? 0,
            // 团队管理 我的店长 我的会员
            'sub_clerks_count'             => $copartner['sub_clerks_count'] ?? 0,
            'sub_members_count'            => $copartner['sub_members_count'] ?? 0,
            // 岗位绩效
            //            'self_order_commission'        => $self_order_commission,
            'goods_order_commission'       => $self_pop_order_commission,
            'vip_goods_order_commission'   => $self_vip_order_commission,
            'activity_order_commission'    => $self_activity_order_commission,
            'member_card_order_commission' => $self_member_card_order_commission,
            'service_fee_order_commission' => $self_service_fee_order_commission,
            $saler_commission
        ];
        return $data;
    }

    public function getMemberCardOrders(array $opts, $page, $page_size)
    {
        // 参数处理
        $where = [];
        if (isset($opts['status']) && $opts['status'] !== '') {
            $where['status'] = $opts['status'];
        }

        if (!empty($opts['keyword'])) {
            $where[] = ['orderno|id', 'like', "%{$opts['keyword']}%"];
        }
        $fields = [
            'clerk_id',
            'member_id',
            'copartner_id',
            'copartner_account_id',
        ];
        foreach ($fields as $field) {
            if (isset($opts[$field]) && $opts[$field]!=='') {
                $where[$field] = $opts[$field];
            }
        }

        // 查询
        $orders = MemberCardOrderModel::where($where)
            ->with(['orderGoods' => function(Query $query) {
                $query->with(['card' => function($query) {
                    $query->field('id,name,thumb');
                }])->field(MemberCardOrderCardModel::scene_fields('list'));
            },'member'=>['clerkLevel','level'],'clerk'=>['clerkLevel','level']])
            ->field(MemberCardOrderModel::scene_fields('list'))
            ->page($page, $page_size)->select();

        // 额外字段
        if (empty($opts['no_commissions_calc'])) {
            foreach ($orders as $order) {
                $filter = [SettleModel::ROLE_KEY_CLERK,SettleModel::ROLE_KEY_COPARTNER]; // 只显示店长的收益
                // 预计收益
                $order->set('order_expect_commissions',
                            (new SettleModel())->getOrderExpectCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
                // 实际结算收益
                $order->set('order_settle_commissions',
                            (new SettleModel())->getOrderSettleCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
            }
        }

        // 条数统计
        if (isset($opts['_total']) && $opts['_total']) {
            $total = MemberCardOrderModel::where($where)->count();
            return ['list'=>$orders, 'total'=> $total];
        } else {
            return ['list'=>$orders];
        }
    }

    public function getServicefeeOrders(array $opts, $page, $page_size)
    {
        // 参数处理
        $where = [];
        if (isset($opts['status']) && $opts['status'] !== '') {
            $where['status'] = $opts['status'];
        }

        if (!empty($opts['keyword'])) {
            $where[] = ['orderno|id', 'like', "%{$opts['keyword']}%"];
        }
        $fields = [
            'clerk_id',
            'member_id',
            'copartner_id',
            'copartner_account_id',
        ];
        foreach ($fields as $field) {
            if (isset($opts[$field]) && $opts[$field]!=='') {
                $where[$field] = $opts[$field];
            }
        }

        // 查询
        $orders = ServiceFeeOrderModel::where($where)
            ->with(['orderGoods' => function(Query $query) {
                $query->with(['fee' => function($query) {
                    $query->field('id,name,thumb');
                }])->field(ServiceFeeOrderFeeModel::scene_fields('list'));
            },'member'=>['clerkLevel','level'],'clerk'=>['clerkLevel','level']])
            ->field(MemberServicefeeModel::scene_fields('list'))
            ->page($page, $page_size)->select();

        // 额外字段
        if (empty($opts['no_commissions_calc'])) {
            foreach ($orders as $order) {
                $filter = [SettleModel::ROLE_KEY_CLERK,SettleModel::ROLE_KEY_COPARTNER]; // 只显示店长的收益
                // 预计收益
                $order->set('order_expect_commissions',
                            (new SettleModel())->getOrderExpectCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
                // 实际结算收益
                $order->set('order_settle_commissions',
                            (new SettleModel())->getOrderSettleCommissions($order['id'], SettleModel::ORDER_TYPE_MEMBERCARD, $filter));
            }
        }

        // 条数统计
        if (isset($opts['_total']) && $opts['_total']) {
            $total = MemberCardOrderModel::where($where)->count();
            return ['list'=>$orders, 'total'=> $total];
        } else {
            return ['list'=>$orders];
        }
    }

    public function getStatistics($start_time, $end_time, $copartner_id, $copartner_account_id = 0) {
        $orderLogic = app(OrderLogic::class);
        $where = [
            ['createtime','BETWEEN', [$start_time, $end_time]],
            ['status', '>', 0],
            'copartner_id'=>$copartner_id,
            'deleted'=>0,
            'isparent'=>0,
        ];
        if ($copartner_account_id) {
            $where['copartner_account_id'] = $copartner_account_id;
        }

        $fields = 'sum(price) as price, count(*) as count';
        $result = $orderLogic->getOne($where, $fields);
        $mc_result = MemberCardOrderModel::where($where)->field($fields)->findOrEmpty()->toArray();
        $fields = 'sum(total) as price, count(*) as count';
        unset($where['isparent']);
        $sf_result = ServiceFeeOrderModel::where($where)->field($fields)->findOrEmpty()->toArray();

        return [
            'goods_order_count' => $result['count'] ?? 0,
            'goods_order_price' => $result['price'] ?? '0.00',
            'member_card_order_count' => $mc_result['count'] ?? 0,
            'member_card_order_price' => $mc_result['price'] ?? '0.00',
            'service_fee_order_count' => $sf_result['count'] ?? 0,
            'service_fee_order_price' => $sf_result['price'] ?? '0.00',
        ];
    }

    function login($data)
    {
        $copartner_account = app(CopartnerAccountLogic::class)->getCopartnerAccount(['copartner_account_model.mobile' => $data['mobile'], 'copartner_account_model.del_at' => 0], ['copartner' => ['accounttime', 'status']]);
        $account = $copartner_account->toArray();
        if(empty($account['id'])) {
            return result(7001, '账户不存在');
        }
        if ('account' == $data['type'] && md5($data['password'] . $account['salt']) != $account['pwd']) {
            return result(7002, '账户密码错误');
        }
        if ($account['copartner']['status'] == 2) {
            return result(7003, '帐号暂停中,请联系管理员!');
        }
        if (time() > $account['copartner']['accounttime']) {
            return result(7004, '账号已过期，请联系集团咨询!');
        }
        try {
            if($account){
                $response['phone_info']['purePhoneNumber'] = $data['mobile'];
                $member = app(WechatMemberService::class, [$response, $data['terminal'], $data['i']]);
                $memberInfo = $member->getResopnseByMemberInfo()->getMemberInfo();
                if (!empty($memberInfo)) {
                    // 更新登录信息
                    LoginLogic::updateLoginInfo($memberInfo['id']);
                    $copartner_account->save(['lastvisitime' => time(), 'lastip' => request()->ip()]);
                }
                return result(0, '登录成功', $memberInfo);
            }
        } catch (\Exception  $e) {
            self::$error = $e->getMessage();
            return result(-1, $e->getMessage());
        }
    }
}