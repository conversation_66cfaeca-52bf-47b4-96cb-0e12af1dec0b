<?php

namespace app\core\com\logic\relation;

use app\core\model\MicroEngineModel;

/**
 * 用户关系绑定处理抽象类
 * Class RelationBindingLogic
 * @package app\core\com\logic\relation
 */
abstract class RelationBindingLogic extends MicroEngineModel
{
    protected $name = 'member';
    abstract protected function checkBeforeBinding($member, $referrer);
    abstract protected function updateRelation($member, $member_update,$referrer);
    abstract protected function afterBindingActions($member, $referrer);

}