<?php

namespace app\core\com\logic\relation;

use app\core\model\copartner\CopartnerUserModel;
use app\model\CopartnerModel;
use app\model\MemberModel;
use app\plugin\copartner\core\logic\CopartnerUserLogic;
use Exception;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 会员关系绑定逻辑类
 *
 * 1如果关系链锁定is_lock_relation为1，则表示锁定关系链，不能再绑定新的推荐人，只能记录最后邀请人。
 * 2否则每次访问或扫码带有mid参数的页面都会更新绑定关系。
 * 3关系链锁定is_lock_relation条件是判断被推荐的会员是否有过消费行为(消费商品、购买会员卡等)。
 * 4关系链的改变依赖推荐人onmid为基础，onmid的改变clerk_id、owner_id、copartner_id、copartner_account_id、org_id等字段都会根据推荐的身份或上级推荐人身份而改变。
 * <AUTHOR>
 */
class MemberRelationBindingLogic extends RelationBindingLogic
{
    protected $name = 'member';

    // 绑定前检查、绑定处理、绑定后行为

    /**
     * 绑定前检查
     * @param object $member
     * @param object $referrer
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkBeforeBinding($member, $referrer)
    {
        // 实例化检查器
        $relationBindingChecker = new RelationBindingChecker();
        // 检查绑定的推荐人是否是自己
        $isSelfReferrer = (new RelationBindingChecker())->checkIfSelfReferrer($member, $referrer);
        // 如果是自己就不绑定
        if ($isSelfReferrer) {
            return false;
        }
        // 检查是否已经绑定推荐人
        $isBindReferrer = $relationBindingChecker->checkMemberIfBindReferrer($member);
        // 检查是否已经锁定关系链
        $isLockRelation = (new RelationBindingChecker())->checkIfLockRelation($member);
        // 已经绑定推荐人
        if ($isBindReferrer) {
            // 检查已绑定的推荐人和当前推荐人是否一致
            $isReferrer = (new RelationBindingChecker())->checkIfReferrer($member, $referrer);
            if ($isReferrer) {
                return false;// 如果是同一推荐人就不重复绑定
            }
            // 已经绑定过onmid并且已锁定关系链，不能再绑定，把当前的推荐人记录为最后邀请人
            if ($isLockRelation) {
                // 检查当前邀请人是否是与已记录的最后邀请人为同一人
                $isLastInviter = (new RelationBindingChecker())->checkIfInviter($member, $referrer);
                if ($isLastInviter) {
                    return false;// 如果是同一邀请人就不重复记录
                }
                // 记录最后邀请人
                return $this->updateRelation($member, $this->getBindFieldByInviter($referrer),$referrer);
            }
        }

        // 通过推荐人角色判断要绑定的字段
        return $this->updateRelation($member, $this->getBindFieldByReferrerRole($referrer), $referrer);
    }

    /**
     * 绑定处理
     * @param object $member
     * @param $member_update
     * @param object $referrer
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    public function updateRelation($member, $member_update, $referrer)
    {
        try {
            // 更新绑定关系
            $member->where('id', $member->id)->update($member_update);
            // 记录关系绑定日志
            $this->recordRelationLog($member, $member_update);
            // 执行绑定后的行为,因为update方法不会更新后对象的数据，所以需要手动更新
            foreach ($member_update as $k=>$v){
                $member[$k] = $v;
            }
            $this->afterBindingActions($member, $referrer);
            return result(0, '操作成功');
        } catch (Exception $e) {
            return result(-1, $e->getMessage());
        }
    }

    /**
     * 关系绑定后的行为
     * @param object $member
     * @param object $referrer
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function afterBindingActions($member, $referrer)
    {
        // 执行关系绑定后的逻辑

        // 1、给会员发放会员卡
        if (!$referrer->copartner_id) return false;
        $copartner_user_info = app(CopartnerUserLogic::class)->getCopartnerUserDetail($referrer->copartner_id)->toArray();
        $giveClerkMcardId = $copartner_user_info['relate']['clerk']['isGiveMemberMcard'] ? $copartner_user_info['relate']['clerk']['membermcardid'] : '';
        // 是否开启间推赠送会员卡
        $isGiveMemberMcard = ($copartner_user_info['relate']['clerk']['isGiveMemberMcardIsSelfShareMember'] && $member->is_self_share) || !$copartner_user_info['relate']['clerk']['isGiveMemberMcardIsSelfShareMember'] ?? false;
        // 发卡是否赠送积分、优惠券
        $sendPointsCoupon = $copartner_user_info['relate']['clerk']['isGiveMemberMcardSendPointsCoupon_member'] ?? false;
        // 是否发送会员卡赠送消息
        $sendMsg = $copartner_user_info['relate']['clerk']['isGiveMemberMcardSendMsg_member'] ?? '';

        if (!empty($giveClerkMcardId) && $isGiveMemberMcard) {
            //检测会员是否已有会员卡
            $checkmcard = p('membercard')->check_Hasget($giveClerkMcardId, $member['openid']);
            //如果当前存在会员卡ID等于要赠送的会员卡则不执行操作
            if ($checkmcard['errno'] <> 0 && !empty($member['openid'])) {
                //执行系统发付费会员卡
                p('membercard')->systemCardIssuing($member['openid'], $giveClerkMcardId, $sendMsg, $sendPointsCoupon);
            }
        }

        // 如果开启了默认锁定关系链，直接锁定
        if ((new RelationBindingChecker())->checkIfEnableDefaultLock($referrer)) {
            (new MemberRelationBindingLogic())->lockRelation($member,$referrer);
        }

        return true;
    }

    /**
     * 根据推荐人角色获取要绑定的字段
     * @param object $referrer
     * @return array
     */
    public function getBindFieldByReferrerRole(object $referrer): array
    {
        $relationBindingChecker = new RelationBindingChecker();
        // 根据推荐人角色获取要绑定的字段
        // 检查推荐人角色
        $referrerRoleId = $relationBindingChecker->checkReferrerRole($referrer);
        $bindField = [
            'onmid' => $referrer->id,
            'onmid_create_time' => time(),
            'clerk_id' => $referrer->clerk_id,
            'clerk_id_create_time' => time(),
            'owner_id' => $referrer->owner_id,
            'owner_id_create_time' => time(),
            'copartner_id' => $referrer->copartner_id,
            'copartner_id_create_time' => time(),
            'copartner_account_id' => $referrer->copartner_account_id,
            'copartner_account_id_create_time' => time(),
            'org_id' => $referrer->org_id,
            'org_id_create_time' => time(),
        ];
        switch ($referrerRoleId) {
            case 8:// 集团创始人
                $copartner = (new CopartnerModel())->getCopartnerUserInfo($referrer['openid']);
                $copartner_account = (new CopartnerModel())->isCopartnerAccount($referrer['openid']);
                $bindField['org_id'] = $copartner['org_id'];
                $bindField['copartner_id'] = $copartner['id'];
                $bindField['copartner_account_id'] = $copartner_account['id'];
                $bindField['owner_id'] = $referrer->id;
                $bindField['clerk_id'] = $referrer->id;
                $bindField['is_self_share'] = 1;
                break;
            case 7:// 合伙人子账户
                $copartner = (new CopartnerModel())->getCopartnerUserInfo($referrer['openid']);
                $copartner_account = (new CopartnerModel())->isCopartnerAccount($referrer['openid'],false,$referrer->id);
                $bindField['org_id'] = $copartner['org_id'];
                $bindField['copartner_id'] = $referrer['copartner_id'];
                $bindField['copartner_account_id'] = $copartner_account['id'];
                $bindField['owner_id'] = $referrer->id;
                $bindField['clerk_id'] = $referrer->id;
                $bindField['is_self_share'] = 1;
                break;
            case 6:// 合伙人
                $copartner = (new CopartnerModel())->getCopartnerUserInfo($referrer['openid']);
                $copartner_account = (new CopartnerModel())->isCopartnerAccount($referrer['openid'],true,$referrer->id);
                $bindField['org_id'] = $copartner['org_id']??$referrer['org_id'];
                $bindField['copartner_id'] = $copartner['id']??$referrer['copartner_id'];
                $bindField['copartner_account_id'] = $copartner_account['id']??$referrer['copartner_account_id'];
                $bindField['owner_id'] = $referrer->id;
                $bindField['clerk_id'] = $referrer->id;
                $bindField['is_self_share'] = 1;
                break;
            case 4:// 医生
                unset($bindField['owner_id'], $bindField['owner_id_create_time'], $bindField['clerk_id'], $bindField['clerk_id_create_time']);
                $bindField['doctor_id'] = $referrer->id;
                $bindField['doctor_id_create_time'] = time();
                $bindField['is_self_share'] = 1;
                break;
            case 3:// 店长
                $bindField['owner_id'] = $referrer->id;
                $bindField['clerk_id'] = $referrer->id;
                $bindField['is_self_share'] = 1;

                break;
            case 2:// 店员
                $bindField['clerk_id'] = $referrer->id;
                $bindField['is_self_share'] = 1;
                break;
            case 1:// 会员
                break;
            default:
                break;
        }
        return $bindField;
    }
    /**
     * 根据邀请人获取要绑定的字段
     * @param object $referrer
     * @return array
     */
    public function getBindFieldByInviter(object $referrer): array
    {
        return $bindField = [
            'inviter_id' => $referrer->id,
            'inviter_id_create_time' => time()
        ];
    }

    /**
     * 根据角色获取要锁定关系链的字段
     * @param object $referrer
     * @return array
     */
    public function getLockFieldByReferrerRole(object $referrer): array
    {
        // 暂未完善 只用到is_lock_relation锁定
        $lockField = ['is_lock_relation' => 1];
        return $lockField;
    }

    /**
     * 锁定关系链
     * @param object $member
     * @param object $referrer
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function lockRelation(object $member, object $referrer)
    {
        // 检查会员的推荐人是否是当前推荐人
        $isReferrer = (new RelationBindingChecker())->checkIfReferrer($member, $referrer);
        if (!$isReferrer) {
            return false;
        }
        // 检查是否已经锁定关系链
        $isLockRelation = (new RelationBindingChecker())->checkIfLockRelation($member);
        // 检查绑定的推荐人是否是自己
        $isSelfReferrer = (new RelationBindingChecker())->checkIfSelfReferrer($member, $referrer);
        if ($isLockRelation || $isSelfReferrer) {
            return false;
        }

        // 如果开启了默认锁定关系链，直接锁定，不再判断推荐人是否有推荐成功的商品或会员卡订单
        if (!(new RelationBindingChecker())->checkIfEnableDefaultLock($referrer)) {
            // 检查推荐人是否有推荐成功的商品或会员卡订单，如果有，才能锁定关系链
            $referrerOrder = (new RelationBindingChecker())->checkMemberOfReferrerOrder($member, $referrer);
            if (!$referrerOrder) {
                return false;
            }
        }

        // 锁定关系链
        $member_update = $this->getLockFieldByReferrerRole($referrer);
        $member_update['lock_relation_time'] = time();
        try {
            // 记录关系绑定日志
            $this->recordRelationLog($member, $member_update, 2);

            // 更新绑定关系
            $member_update['fix_onmid'] = 1;
            $member_update['fix_clerk_id'] = 1;
            $member_update['fix_copartner_id'] = 1;
            $member->where('id', $member->id)->update($member_update);

            return result(0, '操作成功');
        } catch (Exception $e) {
            return result(-1, $e->getMessage());
        }
    }

     /**
      * 解锁关系链
      * @param object $member
      * @param object $referrer
      * @return array|false|float|int|mixed|\Services_JSON_Error|string
      * @throws DataNotFoundException
      * @throws DbException
      * @throws ModelNotFoundException
      */
    public function unlockRelation(object $member, object $referrer)
    {
        return $this->updateRelation($member, $this->getLockFieldByReferrerRole($referrer), $referrer);
    }

    /**
     * 记录关系绑定日志
     * @param object $member
     * @param array $member_update
     * @param int $record_type 0:绑定 1:解绑 2:锁定 3:解锁 4:清除
     * @param int $operator_role 0:用户 1:管理员
     * @param string $remark
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    private function recordRelationLog(object $member, array $member_update, int $record_type = 0, int $operator_role = 0, string $remark = '')
    {
        // 记录关系绑定日志

        // 组装日志数据
        $log = [
            'uniacid' => $member->uniacid,
            'type' => $record_type,
            'operator_role' => $operator_role,
            'member_id' => $member->id,
            'openid' => $member->openid,
            'create_time' => time(),
            'remark' => $remark,
            'ip' => request()->ip(),
        ];
        // 合并更新的字段
        $log = array_merge($log, $member_update);
        try {
            pdo_insert('elapp_shop_member_relation_log', $log);
            return result(0, '操作成功');
        } catch (Exception $e) {
            return result(-1, $e->getMessage());
        }
    }

    /**
     * 按角色变更旗下会员关系链
     * ps 只能变更自己推广的会员
     * 云店长、医生、店长、主管经理、合伙人、城市运营商
     * 如果角色是云店长，那就把云店长所推广的会员变更为自己，根据onmid等于自己ID进行变更clerk_id字段
     * 如果角色是医生，那就把医生所推广的会员变更为自己，根据onmid等于自己ID进行变更doctor_id字段
     * 如果角色是店长，那就把店长所推广的会员变更为自己，根据onmid等于自己ID进行变更owner_id字段
     * 如果角色是主管经理，那就把主管经理所推广的会员变更为自己，根据onmid等于自己ID进行变更copartner_account_id字段
     * 如果角色是合伙人，那就把合伙人所推广的会员变更为自己，根据onmid等于自己ID进行变更copartner_id字段
     * 如果角色是城市运营商，那就把城市运营商所推广的会员变更为自己，根据onmid等于自己ID进行变更city_partner_id字段
     * @param int $memberId
     * @param int $memberRole
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function changeMemberRelationByRole(int $memberId , int $memberRole = 0)
    {
        $memberModel = new MemberModel();
        $member = $memberModel->findOrEmpty($memberId);
        if ($member->isEmpty() || !$member->id) {
            return result(-1, '会员不存在');
        }
        // 1.获取$member的角色 如果没有传入角色则自动检测
        $memberRole = $memberRole?:(new RelationBindingChecker())->checkReferrerRole($member);

        // 2.检查角色状态 是否已审批，是否是黑名单
        $memberRoleStatus = (new RelationBindingChecker())->checkMemberRoleStatus($member,$memberRole);
        if ($memberRoleStatus['code'] == 1) {
            return result(1, $memberRoleStatus['msg']);
        }

        // 3.配置角色对应的更新关系字段
        $member_update = (new RelationBindingChecker())->changeRelationFieldByRole($member,$memberRole);
        if ($member_update['code'] == 1) {
            return result(1, '数据错误',$member_update['msg']);
        }
        // 条件以参数形式传入
        $params = $member_update['data']['params'];
        $fields = $member_update['data']['fields'];

        // 4.获取更新的会员ids
        $updateMemberIds = $memberModel->where($params)->column('id');
        if (empty($updateMemberIds)) {
            return result(1, '没有要更新的会员数据');
        }

        // 5.更新会员关系链
        try {
            $updateResult = $memberModel->where($params)->update($fields);
        } catch (Exception $e) {
            return result(-1, $e->getMessage());
        }

        // 6.更新成功后，记录关系更新日志
        if ($updateResult) {
            foreach ($updateMemberIds as $updateMemberId) {
                $updateMember = $memberModel->findOrEmpty($updateMemberId);
                if ($updateMember->isEmpty()) {
                    continue;
                }
                // 插入日志
                try {
                    $this->recordRelationLog($updateMember, $fields, 0,1);
                } catch (Exception $e) {
                    return result(-1, $e->getMessage());
                }
            }
        } else {
            return result(1, '操作失败');
        }

        // 6.返回结果
        return result(0, '操作成功',$updateMemberIds);
    }

}