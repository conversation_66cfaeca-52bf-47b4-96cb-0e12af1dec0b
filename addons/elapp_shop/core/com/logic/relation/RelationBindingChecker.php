<?php

namespace app\core\com\logic\relation;

use app\core\com\enum\member\MemberRoleEnum;
use app\core\model\MicroEngineModel;
use app\model\CopartnerModel;
use app\model\MemberModel;
use app\model\OrderModel;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 关系绑定检查器
 *
 * <AUTHOR>
 */
class RelationBindingChecker extends MicroEngineModel
{
    protected $name = 'member';

    /**
     * 会员绑定状态检查
     * 检查会员是否绑定推荐人
     */
    public function checkMemberIfBindReferrer($member): bool
    {
        // 检查是否已绑定推荐人会员onmid 字段
        if ($member && !empty($member->onmid)) {
            // onmid 已绑定推荐人
            return true;
        } else {
            // onmid 未绑定推荐人
            return false;
        }
    }
    /**
     * 推荐人角色检查
     * 检查推荐人的角色并返回相应的角色类型，例如会员、店员、店长、合伙人、合伙人子账号、集团老板
     */
    public function checkReferrerRole($referrer): int
    {
        // 检查推荐人的角色并返回相应的角色类型，例如会员、店员、店长、合伙人、合伙人子账号、集团老板
        // 从创始人往下的优先级检查

        if ($referrer && $referrer->is_org_founder) {
            return MemberRoleEnum::ORGFOUNDER;
        }

        if ($referrer && $referrer->is_copartner) {
            return MemberRoleEnum::COPARTNER;
        }

        if ($referrer && $referrer->is_copartner_account) {
            return MemberRoleEnum::COPARTNER_ACCOUNT;
        }

        if ($referrer && $referrer->is_owner && $referrer->owner_status) {
            return MemberRoleEnum::OWNER;
        }

        if ($referrer && $referrer->is_clerk && $referrer->clerk_status) {
            return MemberRoleEnum::CLERK;
        }

        if ($referrer && $referrer->is_doctor && $referrer->doctor_status) {
            return MemberRoleEnum::DOCTOR;
        }

        if ($referrer && $referrer->mobileverify) {
            return MemberRoleEnum::MEMBER;
        }

        // 如果以上条件都不满足，则默认返回'NO_MEMBER'
        return MemberRoleEnum::NO_MEMBER;
    }

    /**
     * 检查推荐人是否有推荐购买商品或会员卡订单
     * 只要有推荐购买商品或会员卡订单，则返回true，否则返回false
     * @param object $member 会员对象
     * @param object $referrer 推荐人对象
     * @return bool
     */
    public function checkMemberOfReferrerOrder(object $member, object $referrer): bool
    {
        $referrer_role = $this->checkReferrerRole($referrer);
        // 检查会员卡订单
        $card_order  = (new OrderModel())->checkMemberOfReferrerOrder($member, $referrer, $referrer_role, 1, 'mcard');
        if ($card_order)  return true;
        // 检查商品订单
        return (new OrderModel())->checkMemberOfReferrerOrder($member, $referrer, $referrer_role, 1);
    }

    /**
     * 检查是否锁定关系链
     * @param object $member
     * @return bool
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkIfLockRelation(object $member): bool
    {
        $memberRelation = new MemberRelationBindingLogic();
        // 检查是否锁定关系链
        $lockField = $memberRelation->getLockFieldByReferrerRole($member);
        $lockField = array_keys($lockField);
        $lockField = implode(',', $lockField);
        $lockField = "{$lockField} = 1";
        $lockResult = (new MemberModel())->where('id', $member->id)->where($lockField)->find();
        if ($lockResult) {
            return true;
        }
        return false;
    }

    /**
     * 检查推荐人身份
     *
     */
    public function checkReferrerIdentityStatus($referrer) {
        // 检查推荐人的身份是否正常
        // ...
    }

    /**
     * 检查是否是自荐人
     * @param object $member
     * @param object $referrer
     * @return bool
     */
    public function checkIfSelfReferrer(object $member , object $referrer): bool
    {
        // 检查是否是自荐人
        if ($referrer->id == $member->id) {
            return true;
        }
        return false;
    }

    /**
     * 检查会员的推荐人是否是当前的推荐人
     * 避免重复绑定
     * @param object $member
     * @param object $referrer
     * @return bool
     */
    public function checkIfReferrer(object $member, object $referrer): bool
    {
        // 检查会员的推荐人是否是当前的推荐人
        if ($referrer->id == $member->onmid) {
            return true;
        }
        return false;
    }

    /**
     * 检查会员的邀请人是否与当前的邀请人为同一人
     * 避免重复记录最后邀请人
     * @param object $member
     * @param object $referrer
     * @return bool
     */
    public function checkIfInviter(object $member, object $referrer): bool
    {
        // 检查会员的邀请人是否与当前的邀请人为同一人
        if ($referrer->id == $member->inviter_id) {
            return true;
        }
        return false;
    }

    /**
     * 检查会员的角色状态
     * @param object $member
     * @param int $role
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    public function checkMemberRoleStatus(object $member, int $role)
    {
        if (empty($member->id)){
            return result(1, '会员不存在');
        }
        // 检查身份
        if ($member->clerk_status != 1) {
            return result(1, '不是云店长');
        }
        // 检查状态
        if ($member->clerk_back == 1) {
            return result(1, '云店长已退还');
        }
        return result(0, '云店长正常');
    }

    /**
     * 根据角色配置更新关系字段
     * @param object $member
     * @param int $role
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    public function changeRelationFieldByRole(object $member, int $role)
    {
        if ($member->id == 0){
            return result(1, '会员不存在');
        }
        if ($role < 2 || $role == 5 || $role > 7) {
            return result(1, '角色类型错误');
        }
        $data = [];
        $fields =[];
        $params = [];
        // 根据角色配置更新关系字段
        switch ($role){
            case 2: // 店员
                $params['onmid'] = $member->id;
                $fields['clerk_id'] = $member->id;
                $fields['clerk_id_create_time'] = time();
                break;
            case 3: // 店长
                $params['onmid'] = $member->id;
                $fields['owner_id'] = $member->id;
                $fields['owner_id_create_time'] = time();
                break;
            case 4: // 医生
                $params['onmid'] = $member->id;
                $fields['doctor_id'] = $member->id;
                $fields['doctor_id_create_time'] = time();
                break;
            case 6: // 合伙人
                $params['onmid'] = $member->id;
                $params['clerk_id'] = $member->id;
                $copartner = (new CopartnerModel())->getCopartnerUserInfo($member->openid);
                $fields['copartner_id']  = $copartner['id'];
                $fields['copartner_id_create_time'] = time();
                $copartner_account = (new CopartnerModel())->isCopartnerAccount($member->openid,true,$member->id);
                $fields['copartner_account_id'] = $copartner_account['id'];
                $fields['copartner_account_id_create_time'] = time();
                break;
            case 7: // 合伙人主管经理
                $params['onmid'] = $member->id;
                $copartner_account = (new CopartnerModel())->isCopartnerAccount($member->openid,true,$member->id);
                $fields['copartner_account_id'] = $copartner_account['id'];
                $fields['copartner_account_id_create_time'] = time();
                break;
            default:
                return result(1, '角色类型错误');
        }

        // $params如果有多个参数,把$params参数转or拼接
        if (count($params) > 1) {
            $params = implode(' or ', array_map(function ($v, $k) {
                return $k . '=' . $v;
            }, $params, array_keys($params)));
        }

        $data['fields'] = $fields;
        $data['params'] = $params;
        return result(0, '角色类型正确', $data);

    }

    /**
     * 检查是否启用默认绑定关系即锁定关系设置
     * (合伙人 -> 注册设置 -> 会员关系设置 -> 默认锁定绑定关系)
     * @param object $referrer
     * @return bool
     */
    public function checkIfEnableDefaultLock(object $referrer) {
        $copartner = (new CopartnerModel())->getCopartnerUserInfo($referrer->copartner_id);
        if (empty($copartner['relate'])) {
            return false;
        }
        $copartner['relate'] = json_decode($copartner['relate'], true);
        return isset($copartner['relate']['clerk']['isDefaultLockRelation']) && $copartner['relate']['clerk']['isDefaultLockRelation'] == 1 ? true : false;
    }

}