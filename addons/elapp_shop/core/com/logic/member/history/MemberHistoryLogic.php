<?php

namespace app\core\com\logic\member\history;

use app\common\logic\BaseLogic;
use app\core\com\logic\goods\GoodsLogic;
use app\core\dao\member\history\MemberHistoryDao;
use app\core\model\member\history\MemberHistoryModel;
use app\model\GoodsModel;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

/**
 * 会员浏览历史记录逻辑
 * Class MemberHistoryLogic
 * @package app\core\com\logic\member\history
 * <AUTHOR>
 * @date 2024/7/03 21:06
 */
class MemberHistoryLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(MemberHistoryDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单条)会员浏览历史记录信息
     * @param array|string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberHistoryDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getMemberHistoryDetail($where, $field, $with);
    }

    /**
     * 获取(多条)会员浏览历史记录列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberHistoryList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getMemberHistoryList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

    /**
     * 添加会员浏览历史记录
     * @param int $goodsid
     * @param int $memberId
     * @return array|bool
     */
    public function addHistory(int $goodsid = 0, int $memberId = 0) {
        global $_W;

        if (!$goodsid || !$memberId)  return false;

        try {
            Db::startTrans();
            // 1.浏览总数 goods viewcount+1
            $add_viewcount = GoodsModel::where('id', $goodsid)->update(['viewcount' => DB::raw('viewcount + 1')]);
            if (!$add_viewcount) {
                return result(1, '浏览总数更新失败!');
            }
            // 2.获取商品信息
            $goods = app(GoodsLogic::class)->getGoodsDetail($goodsid, 'id,merchid,viewcount');
            if (!$goods) {
                return result(1, '商品不存在!');
            }
            // 3.浏览记录
            $history = self::getMemberHistoryDetail(['goodsid' => $goodsid, 'member_id' => $memberId], '*', []);
            if ($history->isEmpty()) {
                //插入记录
                $history = array(
                    'merchid' => $goods->merchid,
                    'openid' => $_W['openid'],
                    'member_id' => $memberId,
                    'goodsid' => $goodsid,
                    'deleted' => 0,
                    'createtime' => time(),
                    'times' => 1
                );
                MemberHistoryModel::create($history);
                $msg = 'insert success';
            } else {
                // 更新记录
                $history->save(['times' => $history['times'] + 1]);
                $msg = 'update success';
            }
            Db::commit();

        } catch (\Exception $e) {
            Db::rollback();
            return result(1, $e->getMessage());
        }
        return result(0, $msg);
    }

}