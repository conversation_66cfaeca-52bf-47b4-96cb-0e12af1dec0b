<?php

namespace app\core\com\logic\member\favorite;

use app\common\logic\BaseLogic;
use app\core\dao\member\favorite\MemberFavoriteDao;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 会员收藏夹逻辑
 * Class MemberFavoriteLogic
 * @package app\core\com\logic\member\Favorite
 * <AUTHOR>
 * @date 2024/7/03 21:06
 */
class MemberFavoriteLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(MemberFavoriteDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单条)收藏
     * @param array|string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberHistoryDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getMemberFavoriteDetail($where, $field, $with);
    }

    /**
     * 获取(多条)收藏列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberHistoryList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getMemberFavoriteList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

}