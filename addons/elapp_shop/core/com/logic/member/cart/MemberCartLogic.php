<?php

namespace app\core\com\logic\member\cart;

use app\common\logic\BaseLogic;
use app\core\dao\member\cart\MemberCartDao;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 购物车逻辑
 * Class MemberCartLogic
 * @package app\core\com\logic\member\cart
 * <AUTHOR>
 * @date 2024/7/03 21:06
 */
class MemberCartLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(MemberCartDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单条)购物车信息
     * @param array|string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberCartDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getMemberCartDetail($where, $field, $with);
    }

    /**
     * 获取(多条)购物车列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getMemberCartList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getMemberCartList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

    /**
     * 获取购物车相关的信息
     * @param array $where
     * @param array $withJoin
     * @param string $field
     * @return array|mixed|\think\db\BaseQuery|\think\Model
     */
    function getCart(array $where, array $withJoin = [], string $field = 'id,goodsid,total,marketprice,createtime,selected,selectedadd')
    {
        return $this->dao->getCart($where, $field, array_merge(['member' => ['id', 'nickname', 'mobile', 'avatar']], $withJoin));
    }

    /**
     * 获取购物车列表相关的信息 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $withJoin 要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getCartList(array $where, array $field = ['*'], array $withJoin = [], string $order = 'id DESC')
    {
        // 分离条件，$whereForCount用于getCount方法，$where用于getCartList方法
        $whereForCount = $this->dao->separateWhereForCount($where);

        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getCartList($where, $field, $page, $limit, array_merge(['member' => ['id', 'nickname', 'mobile', 'avatar']], $withJoin), $order);
        $count = $this->dao->getCount($whereForCount);
        return compact('data', 'count');
    }

}