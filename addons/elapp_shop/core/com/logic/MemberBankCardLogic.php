<?php

namespace app\com\logic;

use app\common\logic\BaseLogic;
use app\model\MemberBankCardModel;
use think\api\Client;
use think\Exception;

class MemberBankCardLogic extends BaseLogic
{
    /**
     * @desc 通过member_id获取银行卡列表
     * @param int $member_id
     * @return MemberBankCardModel[]|array|\think\Collection
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function getBankCardList(int $member_id)
    {
        return MemberBankCardModel::where('member_id', $member_id)->order('id desc')->select();
    }

    /**
     * @desc 验证银行卡号
     */
    function authBankCard(string $name, string $idNum, string $cardNo)
    {
        $client = new Client("a4f2a49d-f1ed-404f-a52c-67e533adb2a3");
        try {
            return $client->bankcardAuth()
                ->withName($name)
                ->withIdNum($idNum)
                ->withCardNo($cardNo)
                ->request();
        } catch (Exception $e) {
            return result(-1, '银行卡验证异常，请联系站点管理员');
        }

    }

}