<?php

namespace app\core\com\logic\goods;

use app\common\logic\BaseLogic;
use app\core\dao\goods\GoodsDao;
use app\model\GoodsModel;
use app\order\logic\Exception;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 商品逻辑
 * Class GoodsLogic
 * @package app\core\com\logic\goods
 * <AUTHOR>
 * @date 2024/6/21 21:06
 */
class GoodsLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(GoodsDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单个)商品信息
     * @param string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->dao->getGoodsDetail($key, $field, $with);
    }

    /**
     * 获取商品列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getGoodsList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }
}