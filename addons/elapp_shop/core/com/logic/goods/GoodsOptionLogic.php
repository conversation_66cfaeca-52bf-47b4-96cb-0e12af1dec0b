<?php

namespace app\core\com\logic\goods;

use app\common\logic\BaseLogic;
use app\core\dao\goods\GoodsOptionDao;
use app\order\logic\Exception;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * 商品规格逻辑
 * Class GoodsOptionLogic
 * @package app\core\com\logic\goods
 * <AUTHOR>
 * @date 2024/6/21 21:06
 */
class GoodsOptionLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(GoodsOptionDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取(单个)商品规格信息
     * @param string|array $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsOptionDetail($key, string $field = '*', array $with = [])
    {
        $where = is_array($key) ? $key : ['id' => $key];
        return $this->dao->getGoodsOptionDetail($where, $field, $with);
    }

    /**
     * 获取商品规格列表 [带搜索]
     * @param array  $where  查询条件数组
     * @param array  $field  要获取的字段数组
     * @param array  $with   要加载的关联模型数组
     * @param string $order  排序字段和方向（默认为 'id DESC'）
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsOptionList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getGoodsOptionList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }
}