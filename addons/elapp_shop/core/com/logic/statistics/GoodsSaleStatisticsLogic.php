<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\core\com\logic\statistics;
use app\common\logic\BaseLogic;
use app\core\dao\order\OrderGoodsDao;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Db;

/**
 * 商品销售统计逻辑层
 * Class GoodsSaleStatisticsLogic
 * @package app\com\logic\statistics
 */
class GoodsSaleStatisticsLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(OrderGoodsDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 获取订单商品列表
     * @param array $where 查询条件数组
     * @param array $field 要获取的字段数组
     * @param int $page 页码（默认为 0）
     * @param int $limit 每页数量（默认为 0）
     * @param array $with 要加载的关联模型数组
     * @param string $order 排序字段和方向（默认为 'id DESC'）
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getOrderGoodsList(array $where, array $field, int $page = 0, int $limit = 0, array $with = [], string $order = 'id DESC'): array
    {
        $data = $this->dao->getOrderGoodsList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->getCount($where);
        return compact('data', 'count');
    }

    /**
     * 获取商品销售明细
     * @param array $condition 条件数组
     * @param int $merchId 商户ID
     * @param bool $asJson 是否返回JSON格式数据
     * @return array|false|mixed|\PDOStatement|string|\think\Model
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsSaleDetail(array $condition, int $merchId = 0, bool $asJson = false)
    {
        $pindex = max(1, intval($condition['page']));
        $psize = intval($condition['psize']);

        // 构建查询
        $query = Db::table('ims_elapp_shop_order_goods')
            ->alias('og')
            ->field('og.*, o.ordersn, g.title, g.thumb, op.title as optiontitle, op.goodssn as optiongoodssn')
            ->leftJoin('ims_elapp_shop_order o', 'o.id = og.orderid')
            ->leftJoin('ims_elapp_shop_goods g', 'g.id = og.goodsid')
            ->leftJoin('ims_elapp_shop_goods_option op', 'op.id = og.optionid')
            ->where('o.status', '>=', 1);

        // 处理商户条件
        if (!empty($merchId)) {
            $query->where('o.merchid', $merchId);
        }
        // 处理时间条件
        if (!empty($condition['datetime'])) {
            $starttime = strtotime($condition['datetime']['start']);
            $endtime = strtotime($condition['datetime']['end']);
            if (!empty($starttime)) {
                $query->where('o.createtime', '>=', $starttime);
            }
            if (!empty($endtime)) {
                $query->where('o.createtime', '<=', $endtime);
            }
        } else {
            if (empty($starttime)) {
                $starttime = strtotime('-1 month');
                $query->where('o.createtime', '>=', $starttime);
            }
            if (empty($endtime)) {
                $endtime = time();
                $query->where('o.createtime', '<=', $endtime);
            }
        }

        // 处理搜索条件
        if (!empty($condition['keywords'])) {
            $keywords = trim($condition['keywords']);
            $query->where('g.title', 'like', '%' . $keywords . '%');
        }

        // 排序方式 排序方式 0-降序 1-升序
        $orderway = (!isset($condition['orderway']) ? 'desc' : (empty($condition['orderway']) ? 'desc' : 'asc'));

        // 排序 0-按销售额 1-销售销量
        $orderby = (!isset($condition['orderby']) ? 'og.price' : (empty($condition['orderby']) ? 'og.price' : 'og.total'));
        $query->order($orderby, $orderway);

        // 计算总数
        $total = $query->count();

        // 分页
        if (empty($condition['export'])) {
            $query->page($pindex, $psize);
        }

        // 执行查询
        $list = $query->select()->toArray();

        // 处理结果
        foreach ($list as &$row) {
            if (!empty($row['optiongoodssn'])) {
                $row['goodssn'] = $row['optiongoodssn'];
            }
            $row['create_date'] = date('Y-m-d', $row['createtime']);
            $row['create_time'] = date('H:i:s', $row['createtime']);
            $row['createtime'] = date('Y-m-d H:i', $row['createtime']);
            $row['thumb'] = tomedia($row['thumb']);
        }
        unset($row);

        // 分页器
        $pager = pagination2($total, $pindex, $psize);

        if ($condition['export'] == 1) {
            ca('statistics.goods.export');
            $list[] = array('data' => '商品总计', 'count' => $total);
            m('excel')->export($list, array(
                'title'   => '商品销售报告-' . date('Y-m-d-H-i', time()),
                'columns' => array(
                    array('title' => '订单号', 'field' => 'ordersn', 'width' => 24),
                    array('title' => '商品名称', 'field' => 'title', 'width' => 48),
                    array('title' => '规格名称', 'field' => 'optiontitle', 'width' => 24),
                    array('title' => '商品编号', 'field' => 'goodssn', 'width' => 12),
                    array('title' => '数量', 'field' => 'total', 'width' => 12),
                    array('title' => '价格', 'field' => 'price', 'width' => 12),
                    array('title' => '成交时间', 'field' => 'createtime', 'width' => 24)
                )
            ));
            plog('statistics.goods.export', '导出商品销售明细');
        }
        return result(0, 'success', ['list' => $list, 'pager' => $pager, 'total' => $total, 'pindex' => $pindex, 'psize' => $psize], $asJson);
    }

    /**
     * 获取商品销售排行
     * @param array $condition 条件数组
     * @param int $merchId 商户ID
     * @param bool $asJson 是否返回JSON格式数据
     * @return array|false|mixed|\PDOStatement|string|\think\Model
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsSaleRank(array $condition, int $merchId = 0, bool $asJson = false)
    {
        $pindex = max(1, intval($condition['page']));
        $psize = intval($condition['psize']);

        $where = [];

        // 处理商户条件
        if (!empty($merchId)) {
            $where[] = ['og.merchid', '=', $merchId];
        }
        // 处理时间条件
        if (!empty($condition['datetime'])) {
            $starttime = strtotime($condition['datetime']['start']);
            $endtime = strtotime($condition['datetime']['end']);
            if (!empty($starttime)) {
                $where[] = ['o.createtime', '>=', $starttime];
            }
            if (!empty($endtime)) {
                $where[] = ['o.createtime', '<=', $endtime];
            }
        } else {
            if (empty($starttime)) {
                $starttime = strtotime('-1 month');
                $where[] = ['o.createtime', '>=', $starttime];
            }
            if (empty($endtime)) {
                $endtime = time();
                $where[] = ['o.createtime', '<=', $endtime];
            }
        }

        // 处理搜索条件
        if (!empty($condition['keywords'])) {
            $keywords = trim($condition['keywords']);
            $where[] = ['g.title','like', '%' . $keywords . '%'];
        }

        // 排序方式 0-降序 1-升序
        $orderway = (!isset($condition['orderway']) ? 'desc' : (empty($condition['orderway']) ? 'desc' : 'asc'));

        // 排序依据 0-按销售额 1-销售销量
        $orderby = (!isset($condition['orderby']) ? 'money' : (empty($condition['orderby']) ? 'money' : 'ordercount'));

        // 构建查询
        $query = Db::table('ims_elapp_shop_order_goods')
            ->alias('og')
            ->field('g.id,g.title,g.thumb,g.status,g.checked,g.stock,g.deleted,g.type,g.merchid,sum( og.total ) ordercount,sum( og.price ) money,count( re.id ) refundcount, CASE WHEN o.refundid > 1 THEN CASE WHEN re.price >= og.realprice THEN og.realprice ELSE	re.price END END as refundprice')
            ->leftJoin('ims_elapp_shop_order o', 'og.orderid = o.id')
            ->leftJoin('ims_elapp_shop_order_refund re', 'og.orderid = re.orderid AND re.STATUS = 1')
            ->leftJoin('ims_elapp_shop_goods g', 'g.id = og.goodsid')
            ->where($where)
            ->where(function($query) {
                $query->where('o.status', '>=', 1)
                    ->whereOr('o.refundid', '>', 0);
            })
            ->group('g.id,g.title,g.thumb,g.status,g.checked,g.stock,g.deleted,g.type,g.merchid')
            ->order($orderby, $orderway);

        // 计算总数
        $total = $query->count();

        // 分页
        if (empty($condition['export'])) {
            $query->limit(($pindex - 1) * $psize, $psize);
        }

        // 执行查询
        $list = $query->select()->toArray();

        // 处理结果
        foreach ($list as &$row) {
            if (intval($row['status']) > 0 && intval($row['checked']) == 0 && intval($row['stock']) > 0 && intval($row['deleted']) == 0) {
                $row['goodsstatus'] = '出售中';
            } else if ($row['status'] > 0 && $row['stock'] <= 0 && $row['deleted'] == 0 && $row['type'] != 30) {
                $row['goodsstatus'] = '已售罄';
            } else if (($row['status'] == 0 || $row['checked'] == 1) && $row['deleted'] == 0) {
                $row['goodsstatus'] = '仓库中';
            } else if ($row['deleted'] == 1) {
                $row['goodsstatus'] = '回收站';
            } else if ($row['deleted'] == 0 && $row['merchid'] > 0 && $row['checked'] == 1) {
                $row['goodsstatus'] = '待审核';
            }
            $row['thumb'] = tomedia($row['thumb']);
        }
        unset($row);

        // 分页器
        $pager = pagination2($total, $pindex, $psize);

        if ($condition['export'] == 1) {
            ca('statistics.goods_rank.export');
            $list[] = array('data' => '商品销售排行', 'count' => $total);
            m('excel')->export($list, array(
                "title" => "商品销售报告-" . date('Y-m-d-H-i', time()),
                "columns" => array(
                    array('title' => '商品名称', 'field' => 'title', 'width' => 36),
                    array('title' => '销售量', 'field' => 'ordercount', 'width' => 12),
                    array('title' => '维权订单量', 'field' => 'refundcount', 'width' => 12),
                    array('title' => '销售额', 'field' => 'money', 'width' => 12),
                    array('title' => '维权金额', 'field' => 'refundprice', 'width' => 12)
                )
            ));
            plog('statistics.goods_rank.export', '导出商品销售排行');
        }
        return result(0, 'success', ['list' => $list, 'pager' => $pager, 'total' => $total, 'pindex' => $pindex, 'psize' => $psize], $asJson);
    }

    /**
     * 获取商品销售转化率
     * @param array $condition 条件数组
     * @param int $merchId 商户ID
     * @param bool $asJson 是否返回JSON格式数据
     * @return array|false|mixed|\PDOStatement|string|\think\Model
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function getGoodsSaleTrans(array $condition, int $merchId = 0, bool $asJson = false)
    {
        $pindex = max(1, intval($condition['page']));
        $psize = intval($condition['psize']);

        $where = [];

        // 处理商户条件
        if (!empty($merchId)) {
            $where[] = ['og.merchid', '=', $merchId];
        }
        // 处理时间条件
        if (!empty($condition['datetime'])) {
            $starttime = strtotime($condition['datetime']['start']);
            $endtime = strtotime($condition['datetime']['end']);
            if (!empty($starttime)) {
                $where[] = ['o.createtime', '>=', $starttime];
            }
            if (!empty($endtime)) {
                $where[] = ['o.createtime', '<=', $endtime];
            }
        } else {
            if (empty($starttime)) {
                $starttime = strtotime('-1 month');
                //$where[] = ['o.createtime', '>=', $starttime];
            }
            if (empty($endtime)) {
                $endtime = time();
                //$where[] = ['o.createtime', '<=', $endtime];
            }
        }

        // 处理搜索条件
        if (!empty($condition['keywords'])) {
            $keywords = trim($condition['keywords']);
            $where[] = ['g.title','like', '%' . $keywords . '%'];
        }

        // 排序方式 0-降序 1-升序
        $orderway = (!isset($condition['orderway']) ? 'desc' : (empty($condition['orderway']) ? 'desc' : 'asc'));

        // 排序依据 0-按销售额 1-销售销量
        $orderby = (!isset($condition['orderby']) ? 'money' : (empty($condition['orderby']) ? 'money' : 'ordercount'));

        // 构建查询
        $query = Db::table('ims_elapp_shop_order_goods')
            ->alias('og')
            ->field([
                'g.id',
                'g.title',
                'g.thumb',
                'g.viewcount AS viewcount',
                'SUM(og.total) AS buycount'
            ])
            ->leftJoin('ims_elapp_shop_order o', 'og.orderid = o.id')
            ->leftJoin('ims_elapp_shop_goods g', 'g.id = og.goodsid')
            ->where('o.status', '>=', 1)
            ->where($where)
            ->group('g.id, g.title, g.thumb, g.viewcount')
            ->order(Db::raw('CASE WHEN g.viewcount = 0 THEN 0 ELSE SUM(og.total) / g.viewcount END ' . $orderway));

        // 计算总数
        $total = $query->count();

        // 分页
        if (empty($condition['export'])) {
            $query->limit(($pindex - 1) * $psize, $psize);
        }

        // 执行查询
        $list = $query->select()->toArray();

        // 处理结果
        foreach ($list as &$row) {
            $row['percent'] = round(($row['buycount'] / (empty($row['viewcount']) ? 1 : $row['viewcount'])) * 100, 2);
            $row['thumb'] = tomedia($row['thumb']);
        }
        unset($row);

        // 分页器
        $pager = pagination2($total, $pindex, $psize);

        if ($condition['export'] == 1) {
            ca('statistics.goods_trans.export');
            m('excel')->export($list, array(
                'title'   => '商品转化率报告-' . date('Y-m-d-H-i', time()),
                'columns' => array(
                    array('title' => '商品名称', 'field' => 'title', 'width' => 24),
                    array('title' => '浏览量', 'field' => 'viewcount', 'width' => 12),
                    array('title' => '购买数', 'field' => 'buycount', 'width' => 12),
                    array('title' => '转化率(%)', 'field' => 'percent', 'width' => 12)
                )
            ));
            plog('statistics.goods_trans.export', '导出商品转化率报告');
        }
        return result(0, 'success', ['list' => $list, 'pager' => $pager, 'total' => $total, 'pindex' => $pindex, 'psize' => $psize], $asJson);
    }
}