<?php

namespace app\com\logic\order;

use app\common\logic\BaseLogic;
use app\core\dao\order\OrderPeerpayPayinfoDao;
use app\traits\LogicTrait;
use think\exception\ValidateException;

class OrderPeerpayPayinfoLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(OrderPeerpayPayinfoDao $dao)
    {
        $this->dao = $dao;
    }


    /**
     * 代付订单支付记录
     * @param array $data
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function setPayInfo(array $data)
    {
        $id = (int)$data['id'];
        $pay_info = $this->dao->get(['pid' => $data['pid']]);
        if ($id) {
            if ($this->dao->update($id, $data, 'id')) {
                return ['type' => 'edit', 'msg' => '更新成功', 'data' => array_merge($pay_info,$data)];
            } else {
                throw new ValidateException('更新失败');
            }
        } else {
            if ($pay_info)
                throw new ValidateException('数据已存在');
            if ($pay_info = $this->dao->create($data)) {
                return ['type' => 'add', 'msg' => '添加成功', 'data' => $pay_info];
            } else {
                throw new ValidateException('添加失败');
            }
        }
    }
}