<?php

namespace app\core\com\logic\order;

use app\common\logic\BaseLogic;
use app\core\com\enum\cart\CartEnum;
use app\core\com\logic\exchange\ExchangeCartLogic;
use app\core\com\logic\fullback\FullbackGoodsLogic;
use app\core\com\logic\goods\GoodsLogic;
use app\core\com\logic\goods\GoodsOptionLogic;
use app\core\com\logic\member\cart\MemberCartLogic;
use app\core\com\logic\quick\QuickCartLogic;
use app\core\dao\order\OrderDao;
use app\model\ActivityModel;
use app\model\GoodsModel;
use app\plugin\diyform\core\logic\DiyformTempLogic;
use app\plugin\merch\core\logic\MerchUserLogic;
use app\plugin\prescription\core\logic\PrescriptionCheckGoodsLogic;
use app\plugin\prescription\core\logic\PrescriptionOrderGoodsLogic;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\exception\ValidateException;

/**
 * 商品订单创建 逻辑类
 * Class OrderCreateLogic
 * @package app\core\com\logic\order
 * <AUTHOR>
 * @version 1.0.0
 * @created_at 2024-07-28 23:00:00
 */
class OrderCreateLogic extends BaseLogic
{
    static int $bargain_id;
    use LogicTrait;
    function __construct(OrderDao $dao)
    {
        $this->dao = $dao;
    }

    /**
     * 创建订单
     * @param array $param
     * @param int $memberId
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function createOrder(array $param, int $memberId): array
    {
        global $_W, $_GPC;
        $param = $param ?: request()->param();
        $result = [];
        // 商品ID
        $result['id'] = $param['id'];
        $result['gdid'] = intval($param['gdid']);
        // 用户信息
        $member = m('member')->getMember($memberId);
        if (empty($member)) {
            return result(-1, '会员不存在');
        }
        $result['member'] = $member;
        $member['carrier_mobile'] = empty($member['carrier_mobile']) ? $member['mobile'] : $member['carrier_mobile'];
        $level = m('member')->getLevel($member['id']);
        $result['level'] = $level;
        // 交易配置
        $result['trade'] = m('common')->getSysset('trade');
        // 商城配置
        $result['shop'] = m('common')->getSysset('shop');
        // 分享配置
        $share = m('common')->getSysset('share');
        $result['share'] = $share;
        // 地址配置
        $area_set = m('util')->get_area_config_set();
        $result['new_area'] = intval($area_set['new_area']);// 是否开启新地址
        $result['address_street'] = intval($area_set['address_street']);// 是否显示街道

        $result['show_card'] = true; //是否显示会员卡选项
        $result['is_activity_order'] = false;// 是否是活动订单（目前控制显示协议）
        $predict_time = strtotime($_GPC["predicttime"]);
        $result['show_predict_time'] = date("Y.m.d", $predict_time); //sixu 20231218 送达时间
        // 获取云店长模块配置
        $result['clerk_set'] = p('clerk')->getSet();
        $result['clerk_set']['apply_status'] = $result['clerk_set']['apply_status']??0;
        $result['clerk_set']['applytitle'] = '店长权益包注册协议';
        $result['clerk_set']['applycontent'] = str_replace('text-wrap: nowrap;', '', $result['clerk_set']['applycontent']);
        // 获取分销配置
        $result['commission_set'] = m('common')->getPluginset('commission');
        $result['offic_register'] = false;
        if ($result['commission_set']['become_goodsid'] == $param['id']) {
            $result['offic_register'] = true;
        }

        // 处理兑换中心订单暂存数据
        $result = array_merge($result, $this->exchangeOrderTempData(is_string($param['exchange']) ? $param['exchange'] : '', intval($param['dflag'])));
        // 3N营销
        $result['threenprice'] = $this->checkThreeN($memberId);
        // 快速购买
        $result['checkQuickBuy'] = !intval($param['fromquick']) || $this->checkQuickBuy(intval($param['fromquick']));
        // 处理直播价格 Step.1
        $result = array_merge($result, $this->handleLivePrice(intval($param['liveid'])));

        // 检查是否开启缓存
        $open_redis = function_exists('redis') && !is_error(redis());

        // 允许参加优惠
        $result['allow_sale'] = true;

        // 多商户 临时放
        $merchdata = app(MerchUserLogic::class)->merchData();
        extract($merchdata);
        $result['merch_array'] = array();
        $result['merchs'] = array();
        $result['merch_id'] = 0;
        $result['total_array'] = array();

        // 套餐订单套餐id
        $result['packageid'] = intval($param['packageid']);
        if (!empty($result['packageid'])) {
            $result['post_goods'] = $param['goods'];
        }
        // 批发商品
        $result['iswholesale'] = intval($param['iswholesale']);
        // 砍价商品ID
        $result['bargain_id'] = intval($param['bargainid']);
        // 商品规格ID
        $result['optionid'] = intval($param['optionid']);
        // 购买数量
        $result['total'] = intval($param['total']);
        // 批发商品规格
        $result['buyoptions'] = $param['buyoptions'];
        // 赠品id，订单是否有赠品
        $result['giftid'] = intval($param['giftid']);
        $result['giftGood'] = [];

        // 开始处理普通订单和套餐订单
        if (empty($result['packageid'])) {
            // 处理普通订单
            $result = $this->handleNormalOrder($result, $member);
        } else {
            // 处理套餐订单
            $result = $this->handlePackageOrder($result, $member);
        }

        // 处理综合部分
        $result['goods_list'] = array();
        if ($result['ismerch']) {
            $getListUser = $merch_plugin->getListUser($result['goods']);
            $result['merch_user'] = $getListUser['merch_user'];
            foreach ($getListUser['merch'] as $k => $v) {
                if (empty($result['merch_user'][$k]['merchname'])) {
                    $result['goods_list'][$k]['shopname'] = $_W['shopset']['shop']['name'];
                } else {
                    $result['goods_list'][$k]['shopname'] = $result['merch_user'][$k]['merchname'];
                }
                $result['goods_list'][$k]['goods'] = $v;
            }
        } else {
            if ($result['merchid'] == 0) {
                $result['goods_list'][0]['shopname'] = $_W['shopset']['shop']['name'];
            } else {
                $result['merch_data'] = $merch_plugin->getListUserOne($result['merchid']);
                $result['goods_list'][0]['shopname'] = $result['merch_data']['merchname'];
            }
            $result['goods_list'][0]['goods'] = $result['goods'];
        }

        $_W['shopshare']['hideMenus'] = array('menuItem:share:qq', 'menuItem:share:QZone', 'menuItem:share:email', 'menuItem:copyUrl', 'menuItem:openWithSafari', 'menuItem:openWithQQBrowser', 'menuItem:share:timeline', 'menuItem:share:appMessage');
        if (p('exchange')) {
            $result['exchangecha'] = $result['goodsprice'] - $result['exchangeprice'];
        }
        if ($result['taskgoodsprice']) {
            $result['goodsprice'] = $result['taskgoodsprice'];
        }
        $result['taskreward'] = $_SESSION['taskcut'];

        //任务中心新版下单
        if ($result['taskreward'] && p('task')) {
            if ($result['card_id']) {
                $result['taskcut'] = 0; //有会员卡的时候任务中心优惠的价格为0
            } else {
                $result['taskcut'] = $result['goodsprice'] - $result['taskreward']['price'];
            }
            $result['card_taskcut'] = $result['goodsprice'] - $result['taskreward']['price'];//会员卡使用的值
        }
        if ($result['card_id']) {
            $result['taskreward'] = null;
        }

        if (!p('membercard')) {
            $result['show_card'] = false;
        }
        if (p('membercard')) {
            // 拼装商品id
            $result['goodsids'] = '';
            $goodsids_arr = array_column($result['goods'], 'goodsid');
            // 会员卡折扣member_discount为1的查询，第4个参数
            $result['my_card_list'] = p('membercard')->get_Mycard('', 0, 100, 1, $goodsids_arr);
            if (empty($result['my_card_list']['list'])) {
                $result['show_card'] = false;
            }
        }
        $result['default_cardid'] = 0; //默认使用的会员卡的id

        if ($result['show_card'] && p('membercard')) {
            $result['goodsids'] = '';
            $goodsids_arr = array_column($result['goods'], 'goodsid');
            $result['default_cardid'] = $this->getDefaultMembercardId($goodsids_arr);
        }

        $createInfo['is_activity_order'] = $result['is_activity_order'] ? 1 : 0;
        $createInfo['apply_status'] = $result['apply_status'] ? 1 : 0;
        $createInfo['card_id'] = $result['default_cardid'];
        $createInfo['taskcut'] = max($result['card_taskcut'], 0);
        $createInfo['lotterydiscountprice'] = $result['card_lotterydiscountprice'];
        $createInfo['discountprice'] = $result['discountprice'];
        $createInfo['isdiscountprice'] = $result['isdiscountprice'];
        $createInfo['deductenough_money'] = '';
        $createInfo['deductenough_enough'] = '';
        $createInfo['fulldeductenough_money'] = '';
        $createInfo['fulldeductenough_enough'] = '';
        $createInfo['fulldeductenough_scale'] = '';

        $createInfo['merch_deductenough_enough'] = $result['merch_saleset']['merch_enoughmoney'];
        $createInfo['merch_deductenough_money'] = $result['merch_saleset']['merch_enoughdeduct'];
        if (!empty($result['exchangeOrder'])) {
            $createInfo['dispatch_price'] = $result['exchangepostage'];
        } elseif ($result['taskgoodsprice']) {
            $createInfo['dispatch_price'] = $result['taskgoodsprice'];
        } else {
            $createInfo['dispatch_price'] = $result['dispatch_price'];
        }
        $createInfo['gift_price'] = is_array($result['gift_price']) && !empty($result['gift_price']) ? min($result['gift_price']) : 0;
        $createInfo['show_card'] = $result['show_card'];
        $createInfo['goods_dispatch'] = $result['dispatch_array']['goods_dispatch'];
        // 把$createInfo数组合并到$result['createInfo']中
        if (!isset($result['createInfo'])) {
            $result['createInfo'] = [];
        }
        $result['createInfo'] = array_merge($result['createInfo'], $createInfo);

        return $result;
    }

    /**
     * 处理兑换中心订单暂存数据
     * @param string $exchangeOrder
     * @param int $discount_flag
     * @return array
     */
    public function exchangeOrderTempData(string $exchangeOrder = '', int $discount_flag = 0): array
    {
        if (p('exchange')) {
            $exchangeOrder = trim($exchangeOrder);
            //兑换中心自定义表单
            $exchange_diyform = [];
            $exchangerealprice = 0;
            $show_card = true;
            if (!empty($exchangeOrder)) {
                $show_card = false;//兑换不能使用会员卡
                $_SESSION['exchange'] = 1;
                $exchangepostage = $_SESSION['exchangepostage'];
                $exchangeprice = $_SESSION['exchangeprice'];
                if ($discount_flag == 1) {
                    $exchangeprice = 0;
                }
                $exchangerealprice = $exchangeprice + $exchangepostage;
                if (!empty($_SESSION['diyform'])) {
                    $exchange_diyform = $_SESSION['diyform'];
                }
            } else {
                unset($_SESSION['exchange']);
                unset($_SESSION['exchangeprice']);
                unset($_SESSION['exchangepostage']);
            }
            return ['exchangepostage' => $exchangepostage, 'exchangeprice' => $exchangeprice, 'exchange_diyform' => $exchange_diyform, 'exchangerealprice' => $exchangerealprice,'show_card' => $show_card];
        }
        return [];
    }

    /**
     * 检查是否符合3N营销
     * @param int $memberId
     * @return bool
     */
    public function checkThreeN(int $memberId): bool
    {
        if (p('threen')) {
            $threenvip = p('threen')->getMember($memberId);
            if (!empty($threenvip)) {
                $threenprice = true;
            }
            return isset($threenprice);
        }
        return false;
    }

    /**
     * 检查快速购买页面是否开启
     * @param int $quickid
     * @return bool
     */
    public function checkQuickBuy(int $quickid = 0): bool
    {
        // 快速购买
        if (p('quick')) {
            if (!empty($quickid)) {
                $quickinfo = p("quick")->getQuick($quickid);
                if (empty($quickinfo)) {
                    return false;
                }
                return true;
            }
        }
        return true;
    }

    /**
     * 处理直播价格
     * @param int $liveid 直播间id
     * @return array
     */
    public function handleLivePrice(int $liveid): array
    {
        // 直播价格处理
        if (p('live') && !empty($liveid)) {
            $isliving = p('live')->isLiving($liveid);// 是否正在直播
            if (!$isliving) {
                $liveid = 0;
            }
        }
        return ['liveid' => $liveid];
    }

    /**
     * 处理普通订单
     * @param array $result
     * @param array $member
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function handleNormalOrder(array $result,array $member): array
    {
        global $_W;
        // 检查是否开启缓存
        $open_redis = function_exists('redis') && !is_error(redis());
        // 处理砍价商品
        $_SESSION['bargain_id'] = null;
        if (p('bargain') && !empty($result['bargain_id'])) {
            $result['show_card'] = false;
            $bargain_result = $this->handleBargainOrder($result['bargain_id'], $member);
            if ($bargain_result['code'] == 0) {
                $result = array_merge($result, $bargain_result['data']);
            }
        }
        // 初始化商品信息
        if ($result['total'] < 1) {
            $result['total'] = 1;
        }
        // 购买数量
        $result['buytotal'] = $result['total'];
        // 错误代码 0 正常 1 未找到商品
        $result['errcode'] = 0;
        // 是否为核销单
        $result['isverify'] = false;
        // 是否强制核销选择门店
        $result['isforceverifystore'] = false;
        // 是否为虚拟物品(虚拟或卡密)
        $result['isvirtual'] = false;
        // 是否是虚拟物品自动发货
        $result['isvirtualsend'] = false;
        // 是否为纯记次时商品订单
        $result['isonlyverifygoods'] = true;
        // 是否可调整商品数量
        $result['changenum'] = false;
        // 是否从购物车购买
        $result['fromcart'] = 0;
        // 是否提供提供发票
        $result['hasinvoice'] = false;
        // 最后一个发票名称
        $result['invoicename'] = "";
        // 是否支持优惠
        $result['buyagain_sale'] = true;
        $result['buyagainprice'] = 0;
        // 必须使用资格券
        $result['must_use_ticket_coupon'] = false;
        // 商品资格券ids
        $result['ticket_coupon_ids'] = [];
        // 所有商品
        $result['goods'] = [];
        // 处理下单方式 1、从购物车购买 2.从批发商品购买 3.商品页直接购买
        if (empty($result['id']) && $member['id']) {
            // 1.从购物车购买 商品ID为空就是从购物车结算
            $result = $this->createOrderFromCart($result, $member);

        } elseif (!empty($result['id']) && !empty($result['iswholesale'])) {
            // 2.从批发商品购买
            $result = $this->createOrderFromWholesale($result, $member);
        } else {
            // 3.商品页直接购买
            $result = $this->createOrderFromGoods($result, $member);
        }
        // 如果存在错误码，直接返回给controller,data里有url
        if ($result['code'] == -1) {
            return result(-1, $result['msg'], $result['data']);
        }

        // ====获取到各类型商品后，开始集中处理商品逻辑=====

        $result['goods'] = set_medias($result['goods'], 'thumb');

        // 处理活动商品
        $activity_result = $this->handleActivityOrder($result['goods'], $member);
        $result += $activity_result;

        // 处理处方药
        $prescribe_result = $this->handlePrescriptionOrder($result['goods'], $member);
        if ($prescribe_result['code'] == -1) {
            return result(-1, $prescribe_result['msg'], $prescribe_result['data']);
        }
        if (!empty($prescribe_result)) {
            list($result['goods'], $result['changenum']) = $prescribe_result;
        }

        // 处理营销限购
        foreach ($result['goods'] as &$g) {
            // 预售和批发商品不能使用会员卡
            if (($g['type'] == 4) || ($g['ispresell'] > 0 && ($g['preselltimeend'] == 0 || $g['preselltimeend'] > time()))) {
                $result['show_card'] = false;
            }
            // 处理秒杀、任务商品
            if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                //秒杀不管任务
                $g['is_task_goods'] = 0;
                //秒杀不能使用会员卡
                $result['show_card'] = false;
            } else {
                // 任务商品
                if (p('task')) {
                    $task_id = intval($_SESSION[$result['id'] . '_task_id']);
                    if (!empty($task_id)) {
                        $rewarded = pdo_fetchcolumn("SELECT `rewarded` FROM " . tablename('elapp_shop_task_extension_join') . " WHERE id = :id AND openid = :openid AND  uniacid = :uniacid", array(':id' => $task_id, ':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));
                        $result['taskGoodsInfo'] = unserialize($rewarded);
                        $result['taskGoodsInfo'] = $result['taskGoodsInfo']['goods'][$result['id']];
                        if (!empty($result['optionid']) && !empty($result['taskGoodsInfo']['option']) && $result['optionid'] == $result['taskGoodsInfo']['option']) {
                            $result['taskgoodsprice'] = $result['taskGoodsInfo']['price'];
                        } elseif (empty($result['optionid'])) {
                            $result['taskgoodsprice'] = $result['taskGoodsInfo']['price'];
                        }
                    }
                }
                //任务活动购买商品
                $rank = intval($_SESSION[$result['id'] . '_rank']);
                $log_id = intval($_SESSION[$result['id'] . '_log_id']);
                $join_id = intval($_SESSION[$result['id'] . '_join_id']);
                $task_goods_data = m('goods')->getTaskGoods($member['openid'], $result['id'], $rank, $log_id, $join_id, $result['optionid']);
                if (empty($task_goods_data['is_task_goods'])) {
                    $g['is_task_goods'] = 0;
                } else {
                    // 不允许参加优惠
                    $result['allow_sale'] = false;
                    $g['is_task_goods'] = $task_goods_data['is_task_goods'];
                    $g['is_task_goods_option'] = $task_goods_data['is_task_goods_option'];
                    $g['task_goods'] = $task_goods_data['task_goods'];
                }
            }

            // 备注多商户商品有BUG缺少$g['merchid']>0判断
            if ($result['is_openmerch'] == 1 && $g['merchid'] > 0) {
                $result['merchid'] = $g['merchid'];
                $result['merch_array'][$result['merchid']]['goods'][] = $g['goodsid'];
            }

            if ($g['isverify'] == 2) {
                // 记次时商品
                $result['isverify'] = true;
            }
            if ($g['isforceverifystore']) {
                $result['isforceverifystore'] = true;
            }
            if (!empty($g['virtual']) || $g['type'] == 2 || $g['type'] == 3 || $g['type'] == 20) {
                //虚拟商品
                $result['isvirtual'] = true;
                //是否虚拟物品自动发货
                if ($g['virtualsend']) {
                    $result['isvirtualsend'] = true;
                }
                if ($g['type'] == 3) {
                    $result['isvirtualsend'] = true;
                }
            }

            // 检查商品是否支持发票
            if ($g['invoice']) {
                $result['hasinvoice'] = $g['invoice'];
            }
            // 判断是否为纯记次时商品订单
            if ($g['type'] != 5) {
                $result['isonlyverifygoods'] = false;
            }

            // 检查商品最大购买量
            // 商品库存
            $totalmaxbuy = $g['stock'];
            // 最大购买量 秒杀只读取自己的总购买数限制 无二次购买
            if (!empty($g['seckillinfo']) && $g['seckillinfo']['status'] == 0) {
                $seckilllast = 0;
                if ($g['seckillinfo']['maxbuy'] > 0) {
                    $seckilllast = $g['seckillinfo']['maxbuy'] - $g['seckillinfo']['selfcount'];
                }
                $g['totalmaxbuy'] = $g['total'];
            } else {
                // 最大购买量
                if ($g['maxbuy'] > 0) {
                    if ($totalmaxbuy != -1) {
                        if ($totalmaxbuy > $g['maxbuy']) {
                            $totalmaxbuy = $g['maxbuy'];
                        }
                    } else {
                        $totalmaxbuy = $g['maxbuy'];
                    }
                }
                // 总购买量
                if ($g['usermaxbuy'] > 0) {
                    $order_goodscount = pdo_fetchcolumn('select ifnull(sum(og.total),0)  from ' . tablename('elapp_shop_order_goods') . ' og '
                        . ' left join ' . tablename('elapp_shop_order') . ' o on og.orderid=o.id '
                        . ' where og.goodsid=:goodsid and  o.status>=0 and o.member_id=:member_id and og.uniacid=:uniacid ', array(':goodsid' => $g['goodsid'], ':uniacid' => $_W['uniacid'], ':member_id' => $member['id']));

                    // 剩余可购买数量
                    $last = $g['usermaxbuy'] - $order_goodscount;
                    if ($last <= 0) {
                        $last = 0;
                    }
                    if ($totalmaxbuy != -1) {
                        if ($totalmaxbuy > $last) {
                            $totalmaxbuy = $last;
                        }
                    } else {
                        $totalmaxbuy = $last;
                    }
                }
                if (!empty($g['is_task_goods'])) {
                    if ($totalmaxbuy > $g['task_goods']['total']) {
                        $totalmaxbuy = $g['task_goods']['total'];
                    }
                }
                $g['totalmaxbuy'] = $totalmaxbuy;
                if ($g['total'] > $g['totalmaxbuy'] && !empty($g['totalmaxbuy'])) {
                    $g['total'] = $g['totalmaxbuy'];
                }
                if (floatval($g['buyagain']) > 0 && empty($g['buyagain_sale'])) {
                    //第一次后买东西享受优惠
                    if (m('goods')->canBuyAgain($g)) {
                        $result['buyagain_sale'] = false;
                    }
                }
            }
            // 检查商品是否必须使用资格券
            $ticket_coupons = $g['limitation']['ticket_coupons'] ?? [];
            if ($ticket_coupons['is_open'] == 1 && !empty($ticket_coupons['ids'])) {
                $result['must_use_ticket_coupon'] = true;
                $result['ticket_coupon_ids'] = $ticket_coupons['ids'];
                $result['must_use_ticket_coupon_goods_title'] = $g['title'];
            }
        }
        unset($g);

        // 处理发票信息
        $result['invoice_arr'] = "{}";
        // 支持发票
        if ($result['hasinvoice']) {
            $result['invoicename'] = pdo_fetchcolumn('select invoicename from ' . tablename('elapp_shop_order') . " where member_id=:member_id and uniacid=:uniacid and ifnull(invoicename,'')<>'' order by id desc limit 1", array(':member_id' => $member['id'], ':uniacid' => $_W['uniacid']));
            // 解析发票格式
            $result['invoice_arr'] = m('sale')->parseInvoiceInfo($result['invoicename']);
            if ($result['invoice_arr']['title'] === false) {
                $result['invoicename'] = '';
            }
            $result['invoice_arr'] = json_encode($result['invoice_arr']);
            $invoice_type = m('common')->getSysset('trade');
            $invoice_type = (int)($invoice_type['invoice_entity']);
            if ($invoice_type === 0) {
                $result['invoicename'] = str_replace('电子', '纸质', $result['invoicename']);
            } elseif ($invoice_type === 1) {
                $result['invoicename'] = str_replace('纸质', '电子', $result['invoicename']);
            }
        }
        // 获取多商户
        $merchdata = app(MerchUserLogic::class)->merchData();
        extract($merchdata);
        if ($result['is_openmerch'] == 1) {
            //读取多商户营销设置
            foreach ($result['merch_array'] as $key => $value) {
                if ($key > 0) {
                    $result['merch_id'] = $key;
                    $result['merch_array'][$key]['set'] = $merch_plugin->getSet('sale', $key);
                    $result['merch_array'][$key]['enoughs'] = $merch_plugin->getEnoughs($result['merch_array'][$key]['set']);
                }
            }
        }

        // 初始数量、价格、优惠价格等信息，重新计算 商品总价、运费、优惠价格、积分等逻辑
        $result['weight'] = 0;//商品总重量
        $result['total'] = 0; //商品数量
        $result['goodsprice'] = 0; //商品价格
        $result['realprice'] = 0; //需支付
        $result['deductprice'] = 0; //积分抵扣的
        $result['taskdiscountprice'] = 0; //任务活动优惠
        $result['carddiscountprice'] = 0; //会员卡折扣优惠
        $result['lotterydiscountprice'] = 0; //游戏活动优惠
        $result['card_lotterydiscountprice'] = 0; //会员卡游戏活动优惠
        $result['discountprice'] = 0; //会员优惠
        $result['isdiscountprice'] = 0; //促销优惠
        $result['deductprice2'] = 0; //余额抵扣限额
        $result['stores'] = array(); //核销门店
        $result['address'] = false; //默认地址
        $result['carrier'] = false; //自提地点
        $result['carrier_list'] = array(); //自提点
        $result['dispatch_list'] = false;
        $result['dispatch_price'] = 0; //邮费
        $result['seckill_dispatchprice'] = 0; //秒杀商品的运费
        $result['seckill_price'] = 0;//秒杀减少的金额
        $result['seckill_payprice'] = 0;//秒杀的消费金额
        $result['ismerch'] = 0;//多商户

        // 如果开启多商户 则判断商品是否属于多商户
        if ($result['is_openmerch'] == 1) {
            if (!empty($result['merch_array'])) {
                if (count($result['merch_array']) > 1) {
                    $result['ismerch'] = 1;
                }
            }
        }

        // 如果是多商户，不能选择会员卡
        if (!empty($result['merch_array']) && count($result['goods']) == count($result['merch_array'])) {
            $result['show_card'] = false;
        }

        // 自提门店，如果是 虚拟 或 卡密 或 不同多商户的商品 则不读取自提点
        if (!$result['isverify'] && !$result['isvirtual'] && !$result['ismerch']) {
            if ($result['merch_id'] > 0) {
                // 多商户自提门店
                $result['carrier_list'] = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where  uniacid=:uniacid and merchid=:merchid and status=1 and type in(1,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $result['merch_id']));
            } else {
                // 平台自提门店
                $result['carrier_list'] = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where  uniacid=:uniacid and status=1 and type in(1,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
            }
        }

        // 如果是砍价商品，则不参与促销优惠
        if ($result['if_bargain']) {
            $result['allow_sale'] = false;
        }
        // 获取营销满额立减、满额包邮、满件优惠 配置
        $sale_plugin = com('sale');
        $result['saleset'] = [];
        // 营销插件开启 && 重复购买优惠开启 && 允许参与促销优惠
        if ($sale_plugin && $result['buyagain_sale'] && $result['allow_sale']) {
            $result['saleset'] = m('common')->getPluginset('sale');
            $result['saleset']['fullenoughs'] = $sale_plugin->getFullenoughs();//满件优惠
            $result['saleset']['goods_nofull'] = com_run('sale::getFullenoughsGoods');//不参与满件优惠商品
        }

        // 获取默认会员卡
        $result['card_id'] = $this->getDefaultMembercardId($result['goods']);

        // 计算产品成交价格及是否包邮
        foreach ($result['goods'] as &$g) {
            if (empty($g['total']) || intval($g['total']) < 1) {
                $g['total'] = 1;
            }
            // 秒杀产品无优惠
            if ($taskcut || $g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                // 商品原价
                $gprice = bcmul($g['marketprice'], $g['total'], 2);
                $g['ggprice'] = bcmul($g['seckillinfo']['price'], $g['total'], 2);
                // 秒杀支付金额
                $result['seckill_payprice'] += floatval($g['ggprice']);
                // 秒杀优惠金额 = 原价 - 秒杀价
                $result['seckill_price'] += floatval($gprice) - floatval($g['ggprice']);
            } else {
                // 商品原价
                $gprice = $g['marketprice'] * $g['total'];
                // 促销或会员折扣价
                $prices = m('order')->getGoodsDiscountPrice($g, $result['level'], 0, $result['card_id']);
                $g['ggprice'] = $prices['price'];
                $g['unitprice'] = $prices['unitprice'];
            }
            //多商户
            if ($result['is_openmerch'] == 1) {
                $result['merchid'] = $g['merchid'];
                $result['merch_array'][$result['merchid']]['ggprice'] += $g['ggprice'];
                $result['merchs'][$result['merchid']] += $g['ggprice'];
            }
            // 如果商品成交价格小于原价，则标识为优惠
            $g['dflag'] = intval($g['ggprice'] < $gprice);

            // 处理折扣优惠
            if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                // if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0|| $_SESSION['taskcut']) {
                // 秒杀不管优惠
            } else {
                // 如果不是砍价订单,执行下面的逻辑
                if (empty($result['bargain_id'])) {
                    // 任务活动优惠
                    $result['taskdiscountprice'] += $prices['taskdiscountprice'];
                    // 使用会员卡折扣
                    if ($result['card_id']) {
                        // 游戏活动优惠不参与会员卡折扣
                        $result['lotterydiscountprice'] = 0;
                    } else {
                        // 游戏活动优惠价
                        $result['lotterydiscountprice'] += $prices['lotterydiscountprice'];
                    }
                    // 会员卡使用的游戏活动优惠的价格
                    $result['card_lotterydiscountprice'] += $prices['lotterydiscountprice'];
                    // 会员折扣价格
                    $g['taskdiscountprice'] = $prices['taskdiscountprice'];
                    $g['lotterydiscountprice'] = $prices['lotterydiscountprice'];
                    $g['discountprice'] = $prices['discountprice'];
                    $g['isdiscountprice'] = $prices['isdiscountprice'];
                    $g['discounttype'] = $prices['discounttype'];
                    $g['isdiscountunitprice'] = $prices['isdiscountunitprice'];
                    $g['discountunitprice'] = $prices['discountunitprice'];

                    // 复购优惠价
                    $result['buyagainprice'] += $prices['buyagainprice'];

                    // 检查会员折扣优惠类型
                    if ($prices['discounttype'] == 1) {
                        // 1.促销折扣优惠
                        $result['isdiscountprice'] += $prices['isdiscountprice'];
                    } else if ($prices['discounttype'] == 2 && empty($result['bargain_id'])) {
                        // 2.会员折扣优惠 (且不参与砍价)
                        $result['discountprice'] += $prices['discountprice'];
                    }

                    // 3N营销优惠
                    if ($result['threenprice'] && !empty($result['threenprice']['price'])) {
                        $result['discountprice'] += $g['marketprice'] - $result['threenprice']['price'];
                    } elseif ($result['threenprice'] && !empty($result['threenprice']['discount'])) {
                        $result['discountprice'] += (10 - $result['threenprice']['discount']) / 10 * $g['marketprice'];
                    }

                    // 游戏营销使用会员卡重新获取优惠促销和会员促销
                    //$result['task_goods_data'] = m('goods')->getTaskGoods($member['openid'], $result['id'], $result['rank'], $result['log_id'], $result['join_id'], $result['optionid']);
                    if ($result['task_goods_data']['is_task_goods'] && $result['log_id'] && $result['card_id']) {
                        $g['is_task_goods'] = 0;
                        $youxi_prices = m('order')->getGoodsDiscountPrice($g, $result['level']);
                        $g['discountprice'] = $youxi_prices['discountprice'];
                        $g['isdiscountprice'] = $youxi_prices['isdiscountprice'];
                        $g['discounttype'] = $youxi_prices['discounttype'];
                        $g['isdiscountunitprice'] = $youxi_prices['isdiscountunitprice'];
                        $g['discountunitprice'] = $youxi_prices['discountunitprice'];
                        if ($youxi_prices['discounttype'] == 1) {
                            // 1.促销优惠
                            $result['isdiscountprice'] += $youxi_prices['isdiscountprice'];
                        } else if ($youxi_prices['discounttype'] == 2) {
                            // 2.会员优惠
                            $result['discountprice'] += $youxi_prices['discountprice'];
                        }
                    }

                }
            }

            // 需要支付金额 = 真实付款金额 商品成交价累加
            $result['realprice'] += $g['ggprice'];
            // 商品原价
            if ($gprice > $g['ggprice']) {
                // 如果商品原价大于成交价，则用原价累加(市场价格 * 数量)
                $result['goodsprice'] += $gprice;
            } else {
                // 如果商品原价小于成交价，则用成交价累加(成交价格 * 数量)
                $result['goodsprice'] += $g['ggprice'];
            }
            // 商品数量 所有商品数量累加
            $result['total'] += $g['total'];
            // 如果不是砍价订单,执行下面语句
            if (empty($result['bargain_id'])) {
                // 秒杀与复购、首单优惠
                if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                    // 秒杀不参与二次购买
                    $g['deduct'] = 0;
                } else {
                    if (floatval($g['buyagain']) > 0 && empty($g['buyagain_sale'])) {
                        // 第一次后买东西享受优惠
                        if (m('goods')->canBuyAgain($g)) {
                            $g['deduct'] = 0;
                        }
                    }
                }
                if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                    //秒杀不参与抵扣
                } else {
                    if ($open_redis) {
                        if ($g['deduct'] > $g['ggprice']) {
                            $g['deduct'] = $g['ggprice'];
                        }
                        // 1.积分抵扣
                        if ($g['manydeduct']) {
                            $result['deductprice'] += $g['deduct'] * $g['total'];
                        } else {
                            $result['deductprice'] += $g['deduct'];
                        }
                        // 2.余额抵扣限额
                        $result['deccredit2'] = 0;//可抵扣的余额
                        if ($g['deduct2'] == 0) {
                            // 1.全额抵扣
                            $result['deccredit2'] = $g['ggprice'];
                        } else if ($g['deduct2'] > 0) {
                            $result['temp_ggprice'] = $g['ggprice'] / $g['total'];
                            // 2.取其中较小的一个作为抵扣金额
                            $result['deccredit2'] = min($g['deduct2'], $result['temp_ggprice']);
                        }
                        // 商品开启余额抵扣 则 商品抵扣总金额 = 商品抵扣金额 * 商品数量
                        if ($g['manydeduct2']) {
                            $result['deccredit2'] = $result['deccredit2'] * $g['total'];
                        }
                        $result['deductprice2'] += $result['deccredit2'];
                    }
                }
            }
        }
        unset($g);

        // 处理收货方式
        // 1.上门自提
        if ($result['isverify']) {
            // 核销订单
            // 所有核销门店ID
            $result['storeids'] = [];
            $result['merchid'] = 0;
            foreach ($result['goods'] as $g) {
                $result['merchid'] = $g['merchid'];
                if (!empty($g['storeids'])) {
                    $result['storeids'] = array_merge(explode(',', $g['storeids']), $result['storeids']);
                }
            }
            if (empty($result['storeids'])) {
                // 门店加入支持核销的判断
                if ($result['merchid'] > 0) {
                    $result['stores'] = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where  uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $result['merchid']));
                } else {
                    $result['stores'] = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where  uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            } else {
                if ($result['merchid'] > 0) {
                    $result['stores'] = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where id in (' . implode(',', $result['storeids']) . ') and uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $result['merchid']));
                } else {
                    $result['stores'] = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where id in (' . implode(',', $result['storeids']) . ') and uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            }
        } else {
            // 2.快递配送
            // 获取用户默认收件地址
            $result['address'] = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where member_id=:member_id and deleted=0 and isdefault=1  and uniacid=:uniacid limit 1'
                , array(':uniacid' => $_W['uniacid'], ':member_id' => $member['id']));

            if (!empty($result['carrier_list'])) {
                $result['carrier'] = $result['carrier_list'][0];
            }
            // 实体物品计算运费 不是虚拟物品 && 不是只支持核销的商品
            if (!$result['isvirtual'] && !$result['isonlyverifygoods']) {
                $result['dispatch_array'] = m('order')->getOrderDispatchPrice($result['goods'], $member, $result['address'], $result['saleset'], $result['merch_array'], 0);
                $result['dispatch_price'] = $result['dispatch_array']['dispatch_price'] - $result['dispatch_array']['seckill_dispatch_price'];
                $result['seckill_dispatchprice'] = $result['dispatch_array']['seckill_dispatch_price'];
            }
        }

        // 计算满减
        // 多商户满减
        if ($result['is_openmerch'] == 1) {
            // 不是砍价订单
            if (empty($result['bargain_id'])) {
                $result['merch_enough'] = m('order')->getMerchEnough($result['merch_array']);
                $result['merch_array'] = $result['merch_enough']['merch_array'];
                $result['merch_enough_total'] = $result['merch_enough']['merch_enough_total'];
                $result['merch_saleset'] = $result['merch_enough']['merch_saleset'];
                if ($result['merch_enough_total'] > 0) {
                    $result['realprice'] -= $result['merch_enough_total'];
                }
            }
        }

        if ($result['saleset']) {

            if (empty($result['bargain_id'])) {
                // 满额立减
                foreach ($result['saleset']['enoughs'] as $e) {
                    if ($result['realprice'] - $result['seckill_payprice'] >= floatval($e['enough']) && floatval($e['money']) > 0) { //减掉秒杀的金额再算满减
                        $result['saleset']['showenough'] = true;
                        $result['saleset']['enoughmoney'] = $e['enough'];
                        $result['saleset']['enoughdeduct'] = $e['money'];
                        $result['realprice'] -= floatval($e['money']);
                        break;
                    }
                }
                unset($e);

                // 满件优惠
                foreach ($result['saleset']['fullenoughs'] as $f) {
                    if ($result['saleset']['fullenoughopen'] && $result['total'] >= floatval($f['fullenoughsss']) && floatval($f['fullmoney']) > 0) {
                        self::applyFullEnoughDiscount($result, $f);
                        break;
                    } elseif ($result['saleset']['fullenoughopen'] && $result['total'] >= floatval($f['fullenough']) && floatval($f['fullmoney']) <= 0 && floatval($f['fullscale']) > 0) {
                        self::applyFullEnoughDiscount($result, $f, true);
                        break;
                    }
                }
                unset($f);
            }

            // 含运费
            $result['include_dispath'] = false;
            // 余额抵扣加上运费
            if (empty($result['saleset']['dispatchnodeduct'])) {
                $result['deductprice2'] += $result['dispatch_price'];
                if (!empty($result['dispatch_price'])) {
                    $result['include_dispath'] = true;
                }
            }
        }
        $result['realprice'] += $result['dispatch_price'] + $result['seckill_dispatchprice'];
        $result['deductcredit'] = 0; //抵扣需要扣除的积分
        $result['deductmoney'] = 0; //抵扣的钱
        $result['deductcredit2'] = 0; //余额抵扣的钱
        // 积分抵扣
        if (!empty($result['saleset'])) {
            if (!empty($result['saleset']['creditdeduct'])) {
                $result['credit'] = $member['credit1'];
                if ($result['credit'] > 0) {
                    $result['credit'] = floor($result['credit']);
                }
                $result['pcredit'] = intval($result['saleset']['credit']); //积分比例
                $result['pmoney'] = round(floatval($result['saleset']['money']), 2); //抵扣比例
                if ($result['pcredit'] > 0 && $result['pmoney'] > 0) {
                    if ($result['credit'] % $result['pcredit'] == 0) {
                        $result['deductmoney'] = round(intval($result['credit'] / $result['pcredit']) * $result['pmoney'], 2);
                    } else {
                        $result['deductmoney'] = round((intval($result['credit'] / $result['pcredit']) + 1) * $result['pmoney'], 2);
                    }
                }
                if ($result['deductmoney'] > $result['deductprice']) {
                    $result['deductmoney'] = $result['deductprice'];
                }
                if ($result['deductmoney'] > $result['realprice'] - $result['seckill_payprice']) {  //减掉秒杀的金额再抵扣
                    $result['deductmoney'] = $result['realprice'] - $result['seckill_payprice'];
                }
                if ($result['pmoney'] * $result['pcredit'] != 0) {
                    $result['deductcredit'] = ceil($result['deductmoney'] / $result['pmoney'] * $result['pcredit']);
                }
            }
            if (!empty($result['saleset']['moneydeduct'])) {
                $result['deductcredit2'] = m('member')->getCredit($member['openid'], 'credit2');
                if ($result['deductcredit2'] > $result['realprice'] - $result['seckill_payprice']) {  //减掉秒杀的金额再抵扣
                    $result['deductcredit2'] = $result['realprice'] - $result['seckill_payprice'];
                }
                if ($result['deductcredit2'] > $result['deductprice2']) {
                    $result['deductcredit2'] = $result['deductprice2'];
                }
            }
        }

        //商品数据
        $result['goodsdata'] = [];
        $result['goodsdata_temp'] = [];
        //订单满金额赠品
        $result['gifts'] = [];
        $result['goods'] = array_column($result['goods'], null, 'goodsid');
        foreach ($result['goods'] as $gk => $g) {
            $result['goodsdata'][] = array(
                'goodsid' => $g['goodsid'],
                'total' => $g['total'],
                'optionid' => $g['optionid'],
                'marketprice' => $g['marketprice'],
                'merchid' => $g['merchid'],
                'cates' => $g['cates'],
                'discounttype' => $g['discounttype'],
                'isdiscountprice' => $g['isdiscountprice'],
                'discountprice' => $g['discountprice'],
                'isdiscountunitprice' => $g['isdiscountunitprice'],
                'discountunitprice' => $g['discountunitprice'],
                'type' => $g['type'],
                'intervalfloor' => $g['intervalfloor'],
                'intervalprice1' => $g['intervalprice1'],
                'intervalnum1' => $g['intervalnum1'],
                'intervalprice2' => $g['intervalprice2'],
                'intervalnum2' => $g['intervalnum2'],
                'intervalprice3' => $g['intervalprice3'],
                'intervalnum3' => $g['intervalnum3'],
                'wholesaleprice' => $g['wholesaleprice'],
                'goodsalltotal' => $g['goodsalltotal'],
                'isnodiscount' => $g['isnodiscount'],
                'deduct' => $g['deduct'],
                'deduct2' => $g['deduct2'],
                'ggprice' => $g['ggprice'],
                'manydeduct' => $g['manydeduct'],
                'manydeduct2' => $g['manydeduct2'],
                'bargain' => $g['bargain'],
                'pr_order_id' => $g['prescribe']['orderid'],//处方表订单ID
            );
            if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                //秒杀不管二次购买
            } else {
                if (floatval($g['buyagain']) > 0) {
                    //第一次后买东西享受优惠
                    if (!m('goods')->canBuyAgain($g) || !empty($g['buyagain_sale'])) {
                        $result['goodsdata_temp'][] = array(
                            'goodsid' => $g['goodsid'],
                            'total' => $g['total'],
                            'optionid' => $g['optionid'],
                            'marketprice' => $g['marketprice'],
                            'merchid' => $g['merchid'],
                            'cates' => $g['cates'],
                            'discounttype' => $g['discounttype'],
                            'isdiscountprice' => $g['isdiscountprice'],
                            'discountprice' => $g['discountprice'],
                            'isdiscountunitprice' => $g['isdiscountunitprice'],
                            'discountunitprice' => $g['discountunitprice'],
                            'type' => $g['type'],
                            'intervalfloor' => $g['intervalfloor'],
                            'intervalprice1' => $g['intervalprice1'],
                            'intervalnum1' => $g['intervalnum1'],
                            'intervalprice2' => $g['intervalprice2'],
                            'intervalnum2' => $g['intervalnum2'],
                            'intervalprice3' => $g['intervalprice3'],
                            'intervalnum3' => $g['intervalnum3'],
                            'wholesaleprice' => $g['wholesaleprice'],
                            'goodsalltotal' => $g['goodsalltotal'],
                            'isnodiscount' => $g['isnodiscount']
                        );
                    }
                } else {
                    $result['goodsdata_temp'][] = array(
                        'goodsid' => $g['goodsid'],
                        'total' => $g['total'],
                        'optionid' => $g['optionid'],
                        'marketprice' => $g['marketprice'],
                        'merchid' => $g['merchid'],
                        'cates' => $g['cates'],
                        'discounttype' => $g['discounttype'],
                        'isdiscountprice' => $g['isdiscountprice'],
                        'discountprice' => $g['discountprice'],
                        'isdiscountunitprice' => $g['isdiscountunitprice'],
                        'discountunitprice' => $g['discountunitprice'],
                        'type' => $g['type'],
                        'intervalfloor' => $g['intervalfloor'],
                        'intervalprice1' => $g['intervalprice1'],
                        'intervalnum1' => $g['intervalnum1'],
                        'intervalprice2' => $g['intervalprice2'],
                        'intervalnum2' => $g['intervalnum2'],
                        'intervalprice3' => $g['intervalprice3'],
                        'intervalnum3' => $g['intervalnum3'],
                        'wholesaleprice' => $g['wholesaleprice'],
                        'goodsalltotal' => $g['goodsalltotal'],
                        'isnodiscount' => $g['isnodiscount'],
                    );
                }
            }

            // 计算赠品
            if ($g['seckillinfo'] && $g['seckillinfo']['status'] == 0) {
                // 秒杀不管赠品
            } else if ($g['isverify'] == 2) {
                // 核销不管赠品
            } else {
                // 指定商品赠品
                if ($result['giftid']) {
                    $gift = [];
                    $giftdata = pdo_fetch("select giftgoodsid,buy_give_ratio from " . tablename('elapp_shop_gift') . " where uniacid = " . $_W['uniacid'] . " and id = " . $giftid . " and status = 1 and starttime <= " . time() . " and endtime >= " . time() . " ");
                    if ($giftdata['giftgoodsid']) {
                        $giftgoodsid = explode(',', $giftdata['giftgoodsid']);
                        $buy_give_ratio = explode('/', $giftdata['buy_give_ratio']);
                        foreach ($giftgoodsid as $key => $value) {
                            $giftinfo = pdo_fetch("select id as goodsid,title,thumb from " . tablename('elapp_shop_goods') . " where uniacid = " . $_W['uniacid'] . " and stock > 0 and status = 2 and id = " . $value . " and deleted = 0 ");
                            if ($giftinfo) {
                                $gift[$key] = $giftinfo;
                                $gift[$key]['total'] = !empty($buy_give_ratio[0]) ? intdiv($g['total'], $buy_give_ratio[0]) * intval($buy_give_ratio[1]) : 1;
                                $gift[$key]['total'] = !empty($gift[$key]['total']) ? $gift[$key]['total'] : 1;
                            }
                        }
                        if ($gift) {
                            $gift = array_filter($gift);
                            $result['goodsdata'] = array_merge($result['goodsdata'], $gift);
                        }
                    }
                } else {
                    // 订单满额赠品
                    $result['isgift'] = 0;
                    $result['giftgoods'] = [];
                    $result['gift_price'] = [];
                    //改$goodsprice 为 $realprice 订单真实支付价格
                    $result['isgiftgoods_price'] = $result['realprice'];
                    $result['gifts'] = pdo_fetchall("select id,goodsid,giftgoodsid,thumb,title ,orderprice,nogiftsid from " . tablename('elapp_shop_gift') . " where uniacid = " . $_W['uniacid'] . " and status = 1 and starttime <= " . time() . " and endtime >= " . time() . " and orderprice <= " . $result['realprice'] . " and activity = 1 ");
                    foreach ($result['gifts'] as $key => $value) {
                        $giftgoods = explode(",", $value['giftgoodsid']);
                        $nogiftgoods = explode(",", $value['nogiftsid']);
                        if (!empty($nogiftgoods)) {
                            if (in_array($g['goodsid'], $nogiftgoods)) {
                                $result['goods'][$gk]['nogiftnotice'] = 1;
                            }
                            $intersect = array_intersect(array_keys($result['goods']), $nogiftgoods);
                            foreach ($intersect as $ink => $inv) {
                                $result['isgiftgoods_price'] -= bcmul($result['goods'][$inv]['marketprice'], $result['goods'][$inv]['total']);
                            }
                            if ($result['isgiftgoods_price'] < $value['orderprice']) {
                                unset($result['gifts'][$key]);
                                continue;
                            }
                        }
                        array_push($result['gift_price'], $value['orderprice']);
                        foreach ($giftgoods as $k => $val) {
                            $giftgoodsdetail = pdo_fetch("select id,title,thumb,marketprice,stock from " . tablename('elapp_shop_goods') . " where uniacid = " . $_W['uniacid'] . " and deleted = 0 and status = 2 and id = " . $val . " ");
                            if ($giftgoodsdetail) {
                                $result['gifts'][$key]['gift'][$k] = $giftgoodsdetail;
                                $result['isgift'] = 1;
                            }
                            if ($giftgoodsdetail['stock'] <= 0) {
                                $result['gifts'][$key]['canchose'] = 0;
                            } else {
                                $result['gifts'][$key]['canchose'] = 1;
                            }
                        }
                        $result['gifts'] = array_filter($result['gifts']);
                        $result['gifttitle'] = $result['gifts'][$key]['gift'][$key]['title'] ?: '赠品';
                    }
                }
            }
        }

        if (!empty($result['gifts']) && count($result['gifts']) == 1) {
            $result['giftid'] = $result['gifts'][0]['id'];
        }

        // 可用优惠券数量(减掉秒杀的商品及总价)
        $result['couponcount'] = com_run('coupon::consumeCouponCount', $member['openid'], $result['realprice'], $result['merch_array'], $result['goodsdata_temp']);
        $result['couponcount'] += com_run('wxcard::consumeWxCardCount', $member['openid'], $result['merch_array'], $result['goodsdata_temp']);
        if (empty($result['goodsdata_temp']) || !$result['allow_sale']) {
            $result['couponcount'] = 0;
        }

        // 校验是否有资格券参与购买
        if ($result['must_use_ticket_coupon'] && !empty($result['ticket_coupon_ids']) && empty($result['couponcount'])) {
            return result(-1, '您没有资格券参与购买' . '<br>' . $result['must_use_ticket_coupon_goods_title'] . '<br>请到购物车删除该商品重新结算!', ['url' => mobileUrl('member/cart')]);
        }

        // 强制绑定手机号
        $result['mustbind'] = 0;
        if (!empty($_W['shopset']['wap']['open']) && !empty($_W['shopset']['wap']['mustbind']) && empty($member['mobileverify'])) {
            $result['mustbind'] = 1;
        }
        if ($result['is_openmerch'] == 1) {
            $result['merchs'] = $merch_plugin->getMerchs($result['merch_array']);
        }
        $result['buyagain'] = $result['buyagainprice'];

        // 订单创建数据
        $result['createInfo'] = [
            'id' => $result['id'],
            'gdid' => $result['gdid'],
            'fromcart' => $result['fromcart'],
            'addressid' => !empty($result['address']) && !$result['isverify'] && !$result['isvirtual'] ? $result['address']['id'] : 0,
            'storeid' => 0,
            'couponcount' => $result['couponcount'],
            'coupon_goods' => $result['goodsdata_temp'],
            'isvirtual' => $result['isvirtual'],
            'isverify' => $result['isverify'],
            'isonlyverifygoods' => $result['isonlyverifygoods'],
            'isforceverifystore' => $result['isforceverifystore'],
            'goods' => $result['goodsdata'],
            'merchs' => $result['merchs'],
            'orderdiyformid' => $result['orderdiyformid'],
            'has_fields' => $result['has_fields'],
            'giftid' => $result['giftid'],
            'mustbind' => $result['mustbind'],
            'fromquick' => intval($result['quickid']),
            'liveid' => intval($result['card_live_id']),
            'new_area' => $result['new_area'],
            'address_street' => $result['address_street'],
            'city_express_state' => empty($result['dispatch_array']['city_express_state']) ? 0 : $result['dispatch_array']['city_express_state'],
            'must_use_ticket_coupon' => $result['must_use_ticket_coupon'],
            'ticket_coupon_ids' => $result['ticket_coupon_ids']
        ];

        return $result?:[];
    }

    /**
     * 处理套餐订单
     * @param array $result
     * @param array $member
     * @return array
     */
    public function handlePackageOrder(array $result, array $member): array
    {
        global $_W;
        // 套餐多商户
        $merchdata = app(MerchUserLogic::class)->merchData();
        extract($merchdata);
        $result['merch_array'] = [];
        $result['merchs'] = [];
        // 获取 POST 商品数据
        $postGoods = json_decode(htmlspecialchars_decode($result['post_goods'], ENT_QUOTES), true);
        $package = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_package') . " WHERE uniacid = " . $_W['uniacid'] . " and id = " . $result['packageid'] . " ");
        $package = set_medias($package, array('thumb'));
        if ($package['starttime'] > time()) {
            return result(-1, '套餐活动还未开始，请耐心等待!', ['url' => mobileUrl('member/cart')]);
        }
        if ($package['endtime'] < time()) {
            return result(-1, '套餐活动已结束，谢谢您的关注，请浏览其他套餐或商品！', ['url' => mobileUrl('member/cart')]);
        }
        $result['goods'] = [];
        $result['goodsprice'] = 0;
        $result['marketprice'] = 0;
        $result['allgoods'] = [];
        $result['card_id'] = $this->getDefaultMembercardId($postGoods);
        foreach ($postGoods as $key => $value) {
            $field = 'id as goodsid,type,title,weight,issendfree,isnodiscount,isfullback,ispresell,presellprice,preselltimeend,presellsendstatrttime,presellsendtime,presellsendtype,
                  thumb,marketprice,storeids,isverify,isforceverifystore,deduct,manydeduct,manydeduct2,`virtual`,maxbuy,usermaxbuy,discounts,stock,deduct2,showlevels,showOrgs,
                  ednum,edmoney,edareas,edareas_code,diyformtype,diyformid,diymode,dispatchtype,dispatchid,dispatchprice,cates,minbuy,is_minbuy_times_add,isdiscount,isdiscount_time,
                  isdiscount_time_start,isdiscount_discounts,virtualsend,invoice,needfollow,followtip,followurl,merchid,checked,merchsale,buyagain,buyagain_islong,buyagain_condition, buyagain_sale';
            try {
                $result['goods'][$key] = app(GoodsLogic::class)->getGoodsDetail($value['goodsid'], $field)->toArray();
            } catch (DbException $e) {
                return result(-1, $e->getMessage());
            }
            //多商户
            if ($result['is_openmerch'] == 1) {
                $result['merchid'] = $result['goods'][$key]['merchid'];
                $result['merch_array'][$result['merchid']]['goods'][] = $result['goods'][$key]['id'];
            }
            $result['option'] = [];
            $result['packagegoods'] = [];
            if ($value['optionid'] > 0) {
                $result['option'] = pdo_fetch("select title,packageprice,marketprice from " . tablename('elapp_shop_package_goods_option') . "
                            where optionid = " . $value['optionid'] . " and goodsid=" . $value['goodsid'] . " and uniacid = " . $_W['uniacid'] . " and pid = " . $result['packageid'] . " ");
                $result['goods'][$key]['packageprice'] = $result['option']['packageprice'];
                $result['goods'][$key]['marketprice'] = $result['option']['marketprice'];
            } else {
                $result['packagegoods'] = pdo_fetch("select title,packageprice,marketprice from " . tablename('elapp_shop_package_goods') . "
                            where goodsid=" . $value['goodsid'] . " and uniacid = " . $_W['uniacid'] . " and pid = " . $result['packageid'] . " ");
                $result['goods'][$key]['packageprice'] = $result['packagegoods']['packageprice'];

            }
            $result['goods'][$key]['optiontitle'] = !empty($result['option']['title']) ? $result['option']['title'] : '';
            $result['goods'][$key]['optionid'] = !empty($value['optionid']) ? $value['optionid'] : 0;;
            $result['goods'][$key]['goodsid'] = $value['goodsid'];
            $result['goods'][$key]['total'] = 1;
            if ($result['option']) {
                $result['goods'][$key]['packageprice'] = $result['option']['packageprice'];
            } else {
                $result['goods'][$key]['packageprice'] = $result['goods'][$key]['packageprice'];
            }

            /*
             * 多商户
             * */
            if ($result['is_openmerch'] == 1) {
                $result['merch_array'][$result['merchid']]['ggprice'] += $result['goods'][$key]['packageprice'];
            }
            $result['goodsprice'] += $result['goods'][$key]['packageprice'];
            $result['marketprice'] += $result['goods'][$key]['marketprice'];
        }

        //默认地址
        $result['address'] = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where member_id=:member_id and deleted=0 and isdefault=1  and uniacid=:uniacid limit 1'
            , array(':uniacid' => $_W['uniacid'], ':member_id' => $member['id']));
        $result['total'] = count($result['goods']);

        /*
         * 运费计算
         * */
        if ($result['package']['dispatchtype'] > 0) {
            $result['dispatch_array'] = m('order')->getOrderDispatchPrice($result['goods'], $member, $result['address'], false, $result['merch_array'], 0);
            $result['dispatch_price'] = $result['dispatch_array']['dispatch_price'] - $result['dispatch_array']['seckill_dispatch_price'];
        } else {
            $result['dispatch_price'] = $result['package']['freight'];
        }

        /**
         *  套餐产品使用会员卡的时候按照系统价格计算
         **/
        if ($result['packageid'] && $result['card_id']) {
            //营销插件
            $sale_plugin = com('sale');
            $result['saleset'] = [];
            if ($sale_plugin && $result['allow_sale']) {
                $result['saleset'] = m('common')->getPluginset('sale');
                $result['saleset']['fullenoughs'] = $sale_plugin->getFullenoughs();//满件优惠
                $result['saleset']['goods_nofull'] = com_run('sale::getFullenoughsGoods');//不参与满件优惠商品
            }
            $result['dispatch_array'] = m('order')->getOrderDispatchPrice($result['goods'], $member, $result['address'], $result['saleset'], $result['merch_array'], 0);
            $result['dispatch_price'] = $result['dispatch_array']['dispatch_price'] - $result['dispatch_array']['seckill_dispatch_price'];
        }

        $result['realprice'] = $result['goodsprice'] + $result['dispatch_price'];
        $result['packprice'] = $result['goodsprice'] + $result['dispatch_price'];
        $result['goodsprice'] = 0;
        $result['isdiscountprice'] = 0;
        $result['discountprice'] = 0;
        foreach ($result['goods'] as $key => &$value) {

            //促销或会员折扣
            $prices = m('order')->getGoodsDiscountPrice($value, $result['level']);
            $value['discountprice'] = $prices['discountprice'];
            $value['isdiscountprice'] = $prices['isdiscountprice'];
            $value['discounttype'] = $prices['discounttype'];
            $value['isdiscountunitprice'] = $prices['isdiscountunitprice'];
            $value['discountunitprice'] = $prices['discountunitprice'];

            if ($prices['discounttype'] == 1) {
                //促销优惠
                $result['isdiscountprice'] += $prices['isdiscountprice'];
            } else if ($prices['discounttype'] == 2) {
                //会员优惠
                $result['discountprice'] += $prices['discountprice'];
            }
            $result['goodsprice'] += $value['marketprice'];
        }
        unset($value);
        if ($result['card_id']) {
            $result['packageid'] = 0;
        }
        //订单创建数据
        $result['createInfo'] = array(
            'id' => 0,
            'gdid' => intval($result['gdid']),
            'fromcart' => 0,
            'packageid' => $result['packageid'],
            'card_packageid' => $result['packageid'],
            'addressid' => $result['address']['id'],
            'storeid' => 0,
            'couponcount' => 0,
            'isvirtual' => 0,
            'isverify' => 0,
            'isonlyverifygoods' => 0,
            'goods' => $result['goods'],
            'merchs' => $result['merchs'],
            'orderdiyformid' => 0,
            'mustbind' => 0,
            'fromquick' => intval($result['quickid']),
            'new_area' => $result['new_area'],
            'address_street' => $result['address_street']
        );
        return $result?:[];
    }

    /**
     * 购物车购买
     */
    public function createOrderFromCart($result, $member)
    {
        $result['fromcart'] = 1;
        // 购物车来源
        if (!empty($result['quickid'])) {
            $cart_type = CartEnum::CART_TYPE_2;
        } elseif (p('exchange') && !empty($result['exchangeOrder'])) {
            $cart_type = CartEnum::CART_TYPE_3;
        } else {
            $cart_type = CartEnum::CART_TYPE_1;
        }
        // 获取购物车商品
        $goods = ($this->getCartSource($cart_type, $member, $result['quickid'] ?: 0)) ?: [];

        // 处理购物车商品
        if (!empty($goods)) {
            // 获取多商户
            $merchdata = app(MerchUserLogic::class)->merchData();
            extract($merchdata);
            $merch_dif = array();
            // 获取商品自定义表单用户数据
            $diyformdata = app(DiyformTempLogic::class)->diyformData($member);
            extract($diyformdata);
            foreach ($goods as $k => $v) {
                // 处理价格
                $goods[$k]['gpprice'] = $v['presellprice'];
                $merch_dif[] = $v['merchid'];
                // 如果是批发商品，则获取批发价格
                if ($v['type'] == 4) {
                    $result['intervalprice'] = $v['intervalprice'];
                    if ($v['intervalfloor'] > 0) {
                        $goods[$k]['intervalprice1'] = $result['intervalprice'][0]['intervalprice'];
                        $goods[$k]['intervalnum1'] = $result['intervalprice'][0]['intervalnum'];
                    }
                    if ($v['intervalfloor'] > 1) {
                        $goods[$k]['intervalprice2'] = $result['intervalprice'][1]['intervalprice'];
                        $goods[$k]['intervalnum2'] = $result['intervalprice'][1]['intervalnum'];
                    }
                    if ($v['intervalfloor'] > 2) {
                        $goods[$k]['intervalprice3'] = $result['intervalprice'][2]['intervalprice'];
                        $goods[$k]['intervalnum3'] = $result['intervalprice'][2]['intervalnum'];
                    }
                }

                $opdata = [];
                if ($v['hasoption'] > 0) {
                    $opdata = m('goods')->getOption($v['goodsid'], $v['optionid']);
                    if (empty($opdata) || empty($v['optionid'])) {
                        return result(-1, '商品' . $v['title'] . '的规格不存在,请到购物车删除该商品重新选择规格!');
                    }
                    if (!empty($v['unite_total'])) {
                        $total_array[$v['goodsid']]['total'] += $v['total'];
                    }
                }
                if (!empty($opdata)) {
                    $goods[$k]['marketprice'] = $v['marketprice'];
                }
                if ($v['ispresell'] > 0 && ($v['preselltimeend'] == 0 || $v['preselltimeend'] > time())) {
                    $goods[$k]['marketprice'] = intval($v['hasoption']) > 0 ? $v['presellprice'] : $v['gpprice'];
                }
                // 全返商品
                $fullbackgoods = array();
                if ($v['isfullback']) {
                    $fullbackgoods = app(FullbackGoodsLogic::class)->getFullbackGoods(['goodsid' => $v['goodsid'], 'status' => 1])->toArray();
                }
                // 检查多商户
                if ($is_openmerch == 0) {
                    //未开启多商户的情况下,购物车中是否有多商户的商品
                    if ($v['merchid'] > 0) {
                        return result(-1,$v['title'] . '为商户商品,请到商城购买!');
                    }
                } else {
                    //判断多商户商品是否通过审核
                    if ($v['merchid'] > 0 && $v['checked'] == 1) {
                        return result(-1,$v['title'] . '商户商品未通过审核,请到商城购买!');
                    }
                }
                //读取规格的图片
                if (!empty($v['option_specs'])) {
                    $thumb = m('goods')->getSpecThumb($v['option_specs']);
                    if (!empty($thumb)) {
                        $goods[$k]['thumb'] = $thumb;
                    }
                }
                if (!empty($v['option_virtual'])) {
                    $goods[$k]['virtual'] = $v['option_virtual'];
                }
                if (!empty($v['option_weight'])) {
                    $goods[$k]['weight'] = $v['option_weight'];
                }
                //秒杀信息
                $goods[$k]['seckillinfo'] = plugin_run('seckill::getSeckill', $v['goodsid'], $v['optionid'], true, $_W['openid']);
                if (!empty($goods[$k]['seckillinfo']['maxbuy']) && $goods[$k]['total'] > $goods[$k]['seckillinfo']['maxbuy'] - $goods[$k]['seckillinfo']['selfcount']) {
                    return result(-1,'您已购买了' . $goods[$k]['seckillinfo']['selfcount'] . '最多购买' . $goods[$k]['seckillinfo']['maxbuy'] . '件', [null,'error' => 'danger']);
                }
            }
            $merch_dif = array_flip(array_flip($merch_dif));
            if ($exchangepostage && !is_array($_SESSION['exchange_postage_info'])) {
                // 兑换中心按单计算运费时，有多商户拆单情况下的计算运费
                $exchange_postage_count = count($merch_dif) * $exchangepostage;
                $exchangerealprice = $exchangerealprice - $exchangepostage + $exchange_postage_count;
                $exchangepostage = $exchange_postage_count;
            }
            // 处理批发商品价格
            $goods = m("goods")->wholesaleprice($goods);
            foreach ($goods as $k => $v) {
                if (is_array($v) && isset($v['type']) && $v['type'] == 4) {
                    $goods[$k]['marketprice'] = $v['wholesaleprice'];
                }
            }
            $result = array_merge($result, [
                'goods' => $goods,
                'total_array' => $total_array,
                'diyformdata' => $diyformdata,
                'merch_dif' => $merch_dif,
                'exchange_postage_count' => $exchangepostage,
                'exchangepostage' => $exchangepostage,
                'exchangerealprice' => $exchangerealprice,
                'is_openmerch' => $is_openmerch,
                'fullbackgoods' => $fullbackgoods,
            ]);
        } else {
            return result(-1, '购物车没有商品');
        }

        return $result;

    }

    /**
     * 批发商品购买
     */
    public function createOrderFromWholesale($result, $member): array
    {
        $result['show_card'] = false;
        // 查询商品表字段
        $good_fields = 'id,' . GoodsModel::bindAttrs('common', true) . ',' . GoodsModel::bindAttrs('wholesale', true);
        // 获取商品信息
        $good = app(GoodsLogic::class)->getGoodsDetail($result['id'], $good_fields)->toArray() ?: [];
        // 检查商品是否存在或是否批发商品
        if (empty($good) || $good['type'] != 4) {
            return result(-1, '商品不存在!');
        }

        $good['goodsid'] = $result['id'];

        // 获取商品自定义表单用户数据
        $diyformdata = app(DiyformTempLogic::class)->diyformData($member);
        extract($diyformdata);
        // 处理价格
        $result['intervalprice'] = $good['intervalprice'];
        if ($good['intervalfloor'] > 0) {
            $good['intervalprice1'] = $result['intervalprice'][0]['intervalprice'];
            $good['intervalnum1'] = $result['intervalprice'][0]['intervalnum'];
        }
        if ($good['intervalfloor'] > 1) {
            $good['intervalprice2'] = $result['intervalprice'][1]['intervalprice'];
            $good['intervalnum2'] = $result['intervalprice'][1]['intervalnum'];
        }
        if ($good['intervalfloor'] > 2) {
            $good['intervalprice3'] = $result['intervalprice'][2]['intervalprice'];
            $good['intervalnum3'] = $result['intervalprice'][2]['intervalnum'];
        }

        $optionsdata = json_decode(htmlspecialchars_decode($result['buyoptions'], ENT_QUOTES), true);
        if (empty($optionsdata) || !is_array($optionsdata)) {
            return result(-1, '商品' . $good['title'] . '的规格不存在,请重新选择规格!');
        }
        $follow = m("user")->followed($member['openid']);
        if (!empty($good['needfollow']) && !$follow && is_weixin()) {
            $followtip = empty($good['followtip']) ? "如果您想要购买此商品，需要您关注我们的公众号，点击【确定】关注后再来购买吧~" : $good['followtip'];
            $followurl = empty($good['followurl']) ? $_W['shopset']['share']['followurl'] : $good['followurl'];
            return result(-1, $followtip, ['followtip' => $followurl, 'followurl' => $followurl, 'error' => 'danger']);
        }

        $result['total'] = 0;
        foreach ($optionsdata as $op) {
            $num = intval($op['total']);
            if ($num <= 0) {
                continue;
            }
            $result['total'] = $result['total'] + $num;
            $good['total'] = $num;
            $good['optionid'] = $op['optionid'];
            if ($op['optionid'] > 0) {
                // 获取商品规格信息
                $option_fields = 'id, goodsid, title, marketprice, presellprice, goodssn, productsn, virtual,stock, weight, specs';
                $option = app(GoodsOptionLogic::class)->getGoodsOptionDetail(['id' => intval($op['optionid']), 'goodsid' => intval($good['id'])],$option_fields)->toArray();
                if (!empty($option)) {
                    $good['optiontitle'] = $option['title'];
                    $good['virtual'] = $option['virtual'];
                    if (empty($good['unite_total'])) {
                        $good['stock'] = $option['stock'];
                        if ($option['stock'] < $num && $option['stock'] > -1) {
                            return result(-1, '商品' . $good['title'] . '的购买数量超过库存剩余数量,请重新选择规格!');
                        }
                    }
                    if (!empty($option['weight'])) {
                        $good['weight'] = $option['weight'];
                    }
                    //读取规格的图片 替换商品图片
                    if (!empty($option['specs'])) {
                        $thumb = m('goods')->getSpecThumb($option['specs']);
                        if (!empty($thumb)) {
                            $good['thumb'] = $thumb;
                        }
                    }
                } else {
                    if (!empty($good['hasoption'])) {
                        return result(-1, '商品' . $good['title'] . '的规格不存在,请重新选择规格!');
                    }
                }
            }
            $goods[] = $good;
        }
        // 处理批发商品价格为市场价
        $goods = m("goods")->wholesaleprice($goods);
        foreach ($goods as $k => $v) {
            if (is_array($v) && isset($v['type']) && $v['type'] == 4) {
                $goods[$k]['marketprice'] = $v['wholesaleprice'];
            }
        }
        return array_merge($result, [
            'goods' => $goods,
            'diyformdata' => $diyformdata,
            'is_openmerch' => $is_openmerch,
        ]);

    }

    /**
     * 商品页直接购买
     */
    public function createOrderFromGoods($result, $member)
    {
        // 商品页面直接购买
        global $_W;
        // 获取多商户
        $merchdata = app(MerchUserLogic::class)->merchData();
        extract($merchdata);

        // 3N营销
        $threensql = "";
        if (p('threen') && !empty($result['threenprice'])) {
            $threensql .= ",threen";
        }

        // 查询商品字段
        $field = 'id as goodsid,activity_id,type,goodsClassID,medicineClassID,medicineAttributeID,title,weight,issendfree,isnodiscount,ispresell,presellprice,'
            . ' thumb,marketprice,liveprice,islive,storeids,isverify,isforceverifystore,deduct,hasoption,preselltimeend,presellsendstatrttime,presellsendtime,presellsendtype,'
            . ' manydeduct,manydeduct2,`virtual`,maxbuy,usermaxbuy,discounts,stock,deduct2,showlevels,showOrgs,cycelbuy_goods_id,'
            . ' ednum,edmoney,edareas,edareas_code,unite_total,diyfields,'
            . ' diyformtype,diyformid,diymode,dispatchtype,dispatchid,dispatchprice,cates,minbuy,is_minbuy_times_add, '
            . ' isdiscount,isdiscount_time,isdiscount_time_start,isdiscount_discounts,isfullback, '
            . ' virtualsend,invoice,needfollow,followtip,followurl,merchid,supplyid,isSupplySend,checked,merchsale,erpGoodsID, '
            . ' buyagain,buyagain_islong,buyagain_condition,buyagain_sale,bargain,limitation'. $threensql;

        // 获取商品信息
        try {
            $data = app(GoodsLogic::class)->getGoodsDetail($result['id'], $field)->toArray();
        } catch (DbException $e) {
            return result(-1, $e->getMessage());
        }
        //dump($data);die();
        $threenprice = $data['threen'];
        // 检查商户状态
        if ($data['merchid'] > 0) {
            $merchstatus = app(MerchUserLogic::class)->checkMerchUser($data['merchid']);
            if (!$merchstatus) {
                return result(-1, '商户不存在！');
            }
        }

        if ($data['bargain'] > 0) {
            if ($data['diyformtype'] == 2) {
                //自定义表单
                $diy = $data['diyfields'];
                $diyformdata = app(DiyformTempLogic::class)->diyformData($member, $diy);
            } elseif ($data['diyformtype'] == 1) {
                $diyformdata = app(DiyformTempLogic::class)->diyformData($member, false, $data['diyformid']);
            } else {
                $diyformdata = app(DiyformTempLogic::class)->diyformData($member);
            }
        } else {
            $diyformdata = app(DiyformTempLogic::class)->diyformData($member);
        }
        extract($diyformdata);
        if ($data['ispresell'] > 0 && ($data['preselltimeend'] == 0 || $data['preselltimeend'] > time())) {
            $data['marketprice'] = $data['presellprice'];
            $result['show_card'] = false;//预售商品不能会员卡
        }
        // 直播价格处理 Step.2
        if (!empty($result['liveid'])) {
            $result['isLiveGoods'] = p('live')->isLiveGoods($data['goodsid'], $result['liveid']);
            if (!empty($result['isLiveGoods'])) {
                $defaultcardid = $this->getDefaultMembercardId();
                if ($defaultcardid) {
                    $live_product = pdo_fetch("SELECT *  FROM " . tablename('elapp_shop_goods') . " WHERE id = '{$data['goodsid']}'");
                    if ($live_product) {
                        $data['marketprice'] = $live_product['marketprice'];
                    }
                } else {
                    $data['marketprice'] = price_format($isLiveGoods['liveprice']);
                }
            }
        }
        //批发商品需要走批发流程
        if ($data['type'] == 4) {
            return result(-1, '商品信息错误!');
        }
        //秒杀信息
        $data['seckillinfo'] = plugin_run('seckill::getSeckill', $data['goodsid'], $result['optionid'], true, $_W['openid']);
        //秒杀不能用会员卡
        if ($data['seckillinfo']) {
            $result['show_card'] = false;
        }
        if ($data['seckillinfo'] && $data['seckillinfo']['status'] == 0) {
            //秒杀不管赠品
        } else if ($data['isverify'] == 2) {
            //核销不管赠品
        } else {
            if ($result['giftid']) {
                $gift = pdo_fetch("select id,title,thumb,activity,giftgoodsid,goodsid,buy_give_ratio from " . tablename('elapp_shop_gift') . "
                                    where uniacid = " . $_W['uniacid'] . " and id = " . $result['giftid'] . " and status = 1 and starttime <= " . time() . " and endtime >= " . time() . " ");
                if (!strstr($gift['goodsid'], (string)$result['id'])) {
                    return result(-1, '赠品与商品不匹配或者商品没有赠品!');
                }

                if (!empty($gift['giftgoodsid'])) {
                    $giftGoodsid = explode(',', $gift['giftgoodsid']);
                    $buy_give_ratio = explode('/', $gift['buy_give_ratio']);
                    if ($giftGoodsid) {
                        foreach ($giftGoodsid as $key => $value) {
                            $giftGood[$key] = pdo_fetch("select id,title,thumb,marketprice from " . tablename('elapp_shop_goods') . " where uniacid = " . $_W['uniacid'] . " and stock > 0 and status = 2 and id = " . $value . " and deleted = 0 ");
                            $giftGood[$key]['total'] = !empty($buy_give_ratio[0]) ? intdiv($result['total'], $buy_give_ratio[0]) * intval($buy_give_ratio[1]) : 1;
                            $giftGood[$key]['total'] = !empty($giftGood[$key]['total']) ? $giftGood[$key]['total'] : 1;
                        }
                        $result['giftGood'] = array_filter($giftGood);
                    }
                }
            }
        }
        if (!empty($result['bargain_actor'])) {
            $data['marketprice'] = $result['bargain_actor']['now_price'];//??
        }

        // 全返商品
        $fullbackgoods = array();
        if ($data['isfullback']) {
            $fullbackgoods = app(FullbackGoodsLogic::class)->getFullbackGoods(['goodsid' => $data['goodsid'], 'status' => 1])->toArray();
        }
        if (empty($data) || (!empty($data['showlevels']) && !strexists($data['showlevels'], $member['level'])) || ($data['merchid'] > 0 && $data['checked'] == 1) || ($is_openmerch == 0 && $data['merchid'] > 0) || (!empty($data['showOrgs']) && !strexists($data['showOrgs'], $member['org_id'])) ) {
            $err = true;
            //include $this->template('goods/detail');
            //exit;
            return result(-1, '商品浏览权限不足!');
        }
        $follow = m("user")->followed($member['openid']);
        if (!empty($data['needfollow']) && !$follow && is_weixin()) {
            $followtip = empty($data['followtip']) ? "如果您想要购买此商品，需要您关注我们的公众号，点击【确定】关注后再来购买吧~" : $data['followtip'];
            $followurl = empty($data['followurl']) ? $result['share']['followurl'] : $data['followurl'];
            $followqrcode = empty($result['share']['followqrcode']) ? $_W['account']['qrcode'] : tomedia($result['share']['followqrcode']);
            $followurl = empty($followqrcode) ? $followurl : $followqrcode;//优先跳转二维码页面
            return result(-1, $followtip, ['followtip' => $followurl, 'followurl' => $followurl, 'error' => 'danger']);
        }
        if ($data['minbuy'] > 0 && $result['total'] < $data['minbuy']) {
            $result['total'] = $data['minbuy'];
        }
        //秒杀数量为1
        if ($data['seckillinfo'] && $data['seckillinfo']['status'] == 0) {
            $result['total'] = 1;
        }
        $data['total'] = $result['total'];
        $data['optionid'] = $result['optionid'];
        if (!empty($result['optionid'])) {
            $options_goods_id = $result['id'];
            if($data['cycelbuy_goods_id']){
                $options_goods_id = $data['cycelbuy_goods_id'];
            }
            // 获取商品规格信息
            $option_fields = 'id,title,marketprice,liveprice,islive,presellprice,goodssn,productsn,virtual,stock,weight,specs,day,allfullbackprice,fullbackprice,allfullbackratio,fullbackratio,isfullback,cycelbuy_periodic';
            $option = app(GoodsOptionLogic::class)->getGoodsOptionDetail(['id' => intval($result['optionid']), 'goodsid' => intval($options_goods_id)],$option_fields)->toArray();

            if (!empty($option)) {
                $data['optionid'] = $option['id'];
                $data['optiontitle'] = $option['title'];
                $data['marketprice'] = (intval($data['ispresell']) > 0 && ($data['preselltimeend'] > time() || $data['preselltimeend'] == 0)) ? $option['presellprice'] : $option['marketprice'];
                if ($result['isliving'] && !empty($option['islive']) && $option['liveprice'] > 0) {
                    $data['marketprice'] = $option['liveprice'];
                }
                // 直播价格处理 Step.3
                if (!empty($result['liveid'])) {
                    $liveOption = p('live')->getLiveOptions($data['goodsid'], $result['liveid'], array($option));
                    $defaultcardid = $this->getDefaultMembercardId();
                    if ($defaultcardid) { //有会员卡按照售价来
                        $gopdata = m('goods')->getOption($data['goodsid'], $optionid);
                        if (empty($gopdata) != true) {
                            $data['marketprice'] = price_format($gopdata['marketprice']);
                        }
                    } else {
                        if (!empty($liveOption) && !empty($liveOption[0])) {
                            $data['marketprice'] = price_format($liveOption[0]['marketprice']);
                        }
                    }
                }
                $data['virtual'] = $option['virtual'];
                if ($option['isfullback'] && !empty($fullbackgoods)) {
                    $fullbackgoods['minallfullbackallprice'] = $option['allfullbackprice'];
                    $fullbackgoods['fullbackprice'] = $option['fullbackprice'];
                    $fullbackgoods['minallfullbackallratio'] = $option['allfullbackratio'];
                    $fullbackgoods['fullbackratio'] = $option['fullbackratio'];
                    $fullbackgoods['day'] = $option['day'];
                }
                if (empty($data['unite_total'])) {
                    $data['stock'] = $option['stock'];
                }
                if (!empty($option['weight'])) {
                    $data['weight'] = $option['weight'];
                }
                //读取规格的图片
                if (!empty($option['specs'])) {
                    $thumb = m('goods')->getSpecThumb($option['specs']);
                    if (!empty($thumb)) {
                        $data['thumb'] = $thumb;
                    }
                }
                $cycelbuy_periodic = explode(",", $option["cycelbuy_periodic"]); //获取周期规格 by sixu 20231218
                list($cycelbuy_day, $cycelbuy_unit, $cycelbuy_num) = $cycelbuy_periodic;
            } else {
                if (!empty($data['hasoption'])) {
                    return result(-1, '商品' . $data['title'] . '的规格不存在,请重新选择规格!');
                }
            }
        }
        //可以调整数量
        if ($result['giftid']) {
            $changenum = false;
        } else {
            $changenum = true;
        }
        //秒杀不能修改数量
        if ($data['seckillinfo'] && $data['seckillinfo']['status'] == 0) {
            $changenum = false;
        }

        $goods[] = $data;

        return array_merge($result, [
            'goods' => $goods,
            'diyformdata' => $diyformdata,
            'is_openmerch' => $is_openmerch,
            'changenum' => $changenum,
            'giftGood' => $giftGood,
            'fullbackgoods' => $fullbackgoods,
            'cycelbuy_day' => $cycelbuy_day,
            'cycelbuy_unit' => $cycelbuy_unit,
            'cycelbuy_num' => $cycelbuy_num,
            'threenprice' => $threenprice,
        ]);
    }

    /**
     * 获取购物车商品来源
     * 1、主商城购物车 2、快速购买购物车 3、兑换中心购物车
     * @param int $cart_type 购物车来源
     * @param array $member 会员信息
     * @param int $quickid 快速购买页面ID
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getCartSource(int $cart_type, array $member, int $quickid = 0): array
    {
        // 查询购物车表字段
        $cart_fields = ['id', 'goodsid', 'total', 'optionid', 'selected', 'deleted'];
        // 查询商品表字段
        $goods_fields = array_merge(GoodsModel::bindAttrs('common') , GoodsModel::bindAttrs('cart'));
        // 查询商品规格表字段
        $option_fields = ['option.presellprice' => 'option__option_presellprice', 'option.weight' => 'option__option_weight', 'option.marketprice' => 'option__option_marketprice', 'option.title' => 'option__option_title', 'option.thumb' => 'option__option_thumb', 'option.virtual' => 'option__option_virtual', 'option.specs' => 'option__option_specs'];
        // 根据购物车来源查询购物车商品
        if ($cart_type == CartEnum::CART_TYPE_2) {
            // 快速购买购物车购买
            $where = ['quick_cart_model.member_id' => $member['id'], 'quick_cart_model.selected' => 1, 'quick_cart_model.deleted' => 0, 'quick_cart_model.quickid' => $quickid];
            $goods_res = app(QuickCartLogic::class)->getCartList($where, $cart_fields, ['goods' => $goods_fields, 'option' => $option_fields]);
        } elseif (p('exchange') && $cart_type == CartEnum::CART_TYPE_3) {
            // 兑换中心购物车购买
            $where = ['exchange_cart_model.member_id' => $member['id'], 'exchange_cart_model.selected' => 1, 'exchange_cart_model.deleted' => 0];
            $goods_res = app(ExchangeCartLogic::class)->getCartList($where, $cart_fields, ['goods' => $goods_fields, 'option' => $option_fields]);
        } else {
            // 商城购物车购买
            $where = ['member_cart_model.member_id' => $member['id'], 'member_cart_model.selected' => 1, 'member_cart_model.deleted' => 0];
            $goods_res = app(MemberCartLogic::class)->getCartList($where, $cart_fields, ['goods' => $goods_fields, 'option' => $option_fields]);
        }
        return $goods_res['data']?: [];
    }

    /**
     * 处理砍价商品
     * @param int $bargain_id 砍价商品ID
     * @param array $member 会员信息
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function handleBargainOrder(int $bargain_id , array $member): array
    {
        if (p('bargain') && !empty($bargain_id)) {
            cache_write($member['id'] . '_bargain_id', $bargain_id);
            self::$bargain_id = $bargain_id;
            $bargain_actor = pdo_fetch("SELECT *  FROM " . tablename('elapp_shop_bargain_actor') . " WHERE id = :id AND (openid = :openid or member_id = :member_id) AND status = '0'", array(':id' => $bargain_id, ':openid' => $member['openid'], ':member_id' => $member['id']));
            if (empty($bargain_actor)) {
                return result(-1,'没有这个商品!');
            }
            $bargain_goods = pdo_fetch("SELECT *  FROM " . tablename('elapp_shop_bargain_goods') . " WHERE id = '{$bargain_actor['goods_id']}'");
            if (empty($bargain_goods)) {
                return result(-1,'没有这个商品!');
            }
            $goods = app(GoodsLogic::class)->getGoodsDetail($bargain_goods['goods_id'], 'bargain')->toArray();
            if (empty($goods['bargain'])) {
                return result(-1,'没有这个商品!');
            }
            return result(0, '有这个商品!', ['bargain_goods_id' => $bargain_goods['goods_id'], 'bargain_actor' => $bargain_actor, 'bargain_goods' => $bargain_goods]);
        }
        return result(-1,'没有这个商品!');
    }

    /**
     * 获取默认会员卡ID
     * @param array $goods 商品数组
     * @param array $member 会员信息
     * @return bool|int
     */
    protected function getDefaultMembercardId(array $goods = [], array $member = [])
    {
        global $_W;
        if (p('membercard')) {
            $member_discounts = '1';
            if (!empty($goods)) {
                $goodsids_arr = array_column($goods, 'goodsid');
                $mycard = p('membercard')->get_Mycard($member['openid'], 1, 100, $member_discounts, $goodsids_arr);
            } else {
                $mycard = p('membercard')->get_Mycard($member['openid'], 1, 100, $member_discounts);
            }
            if ($mycard['list']) {
                $all_mycardlist = $mycard['list'];
                $card_info['all_mycardlist'] = $all_mycardlist;

                $availablecard_count = $mycard['total'];
                $c_discount = array();
                $a_discount = array();
                foreach ($all_mycardlist as $ckey => $cvalue) {
                    //跳过没有会员折扣的
                    if (empty($cvalue['member_discount'])) continue;
                    $c_discount[$cvalue['id']] = (string)$cvalue['discount_rate'];
                }
                foreach ($all_mycardlist as $akey => $avalue) {
                    //跳过没有会员折扣并且禁用折上折的
                    if (empty($avalue['member_discount']) || $avalue['discount'] == 0) continue;
                    $a_discount[$avalue['id']] = (string)$avalue['discount_rate'];
                }
                //查找出折扣力度最大的会员卡作为默认选中的会员卡
                $max_discount_cardid = 0;
                if (!empty($a_discount)) {
                    $max_discount = min($a_discount);
                    $ex_discount = @array_flip($a_discount);
                    $max_discount_cardid = $ex_discount[$max_discount];
                } else if (!empty($c_discount)) {
                    $max_discount = min($c_discount);
                    $ex_discount = @array_flip($c_discount);
                    $max_discount_cardid = $ex_discount[$max_discount];
                }
                $default_cardid = empty($max_discount_cardid) ? $all_mycardlist[0]['id'] : $max_discount_cardid;
                return $default_cardid;
            } else {
                return false;
            }
        } else {
            return false;
        }
    }

    /**
     * 处理活动商品
     * @param array $goods 全部商品
     * @param array $member 会员信息
     * @return array
     */
    public function handleActivityOrder(array $goods, array $member): array
    {
        if (p('activity') && !empty($goods)) {
            $activity_id = $goods[0]['activity_id'] ?? 0;
            $activityPlugin = new ActivityModel();
            $activity_config = $activityPlugin->getActivityConfig($activity_id, $member['copartner_id']);
            $is_activity_order = false;
            $activity_data = [];
            $activity_config_plans = $activity_config['config']['plans'] ?? [];
            foreach ($goods as &$g) {
                if ($g['activity_id'] == 1) {
                    $is_activity_order = true; // todo 这里特指 980活动，不是泛指活动订单
                    $activity_data = $activity_config_plans[$g['goodsid']] ?? [];
                }
            }
            unset($g);
            return ['is_activity_order' => $is_activity_order, 'activity_data' => $activity_data];
        }
        return ['is_activity_order' => false, 'activity_data' => []];
    }

    /**
     * 处理处方药商品
     * @param array $goods 全部商品
     * @param array $member 会员信息
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function handlePrescriptionOrder(array $goods, array $member): array
    {
        if (p('prescription') && !empty($goods) && !empty($member)) {
            foreach ($goods as &$g) {
                // 检查处方药是否已开方
                // 1.是否处方药 0否1是
                $g['is_prescription_drug'] = 0;
                $is_prescription_drug = $g['goodsid'] ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescriptionDrug($g['goodsid']) : 0;
                // 2.是否有有效处方单
                if ($is_prescription_drug) {
                    $g['is_prescription_drug'] = 1;
                    $prescribe_info = app(PrescriptionOrderGoodsLogic::class)->getPrescribeByGoodsId($g['goodsid'], $member['id']);
                    if ($prescribe_info['code'] == 1) {
                        // 没有有效处方单不给提交
                        return result(-1, '商品' . $g['title'] . '未开处方单，请先开方后购买!', ['url' => mobileUrl('prescription/order/create/main', array('id' => $g['goodsid']))]);
                    } else {
                        // 有有效处方单 把处方信息赋值给商品
                        $g['prescribe'] = $prescribe_info['data'];
                        $changenum = false;
                    }
                }
            }
            unset($g);
            return [$goods,$changenum];
        }
        return [];
    }

    /**
     * 计算满件优惠价格
     * @param $result
     * @param $e
     * @param bool $useScale 区分不同的满减逻辑
     * @return void
     */
    function applyFullEnoughDiscount(&$result, &$e, bool $useScale = false) {
        // 显示满足条件=1
        $result['saleset']['fullshowenough'] = true;
        // 满足的数量
        $result['saleset']['fullenoughmoney'] = $e['fullenough'];
        // 满减的额度
        $result['saleset']['fullenoughdeduct'] = $e['fullmoney'];

        if ($useScale) {
            // 满减的比例
            $result['saleset']['fullenoughscale'] = $e['fullscale'];
            // 把当前应付金额（含满额优惠后）赋给变量
            $realPrice = $result['realprice'];
            // 计算满减额度
            $discountAmount = $realPrice * ($e['fullscale'] / 100);
            // 更新满减额度和实际价格
            $result['saleset']['fullenoughdeduct'] = $e['fullmoney'] = $discountAmount;
            $result['realprice'] -= floatval($discountAmount);
        } else {
            $result['realprice'] -= floatval($e['fullmoney']);
        }
    }
}