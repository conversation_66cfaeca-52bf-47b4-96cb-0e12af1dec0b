<?php
namespace app\com\logic\order;

use app\common\logic\BaseLogic;
use app\controller\activity\Action;
use app\controller\activity\OrderRefundData;
use app\controller\settle\CommonSettleHandler;
use app\model\ActivityModel;
use app\model\SettleModel;
use app\traits\LogicTrait;

/**
 * 商品订单创建 逻辑类
 * Class OrderCreateLogic
 * @package app\core\com\logic\order
 * <AUTHOR>
 * @version 1.0.0
 * @created_at 2024-07-28 23:00:00
 */
class OrderRefundLogic extends BaseLogic
{
    use LogicTrait;

    public function opData() {

        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $refundid = intval($_GPC['refundid']);

        $item = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_order') . " WHERE id = :id and uniacid=:uniacid Limit 1", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($item)) {
            if ($_W['isajax']) {
                show_json(0, "未找到订单!");
            }
            $this->message('未找到订单!', '', 'error');
        }

        if (empty($refundid)) {
            $refundid = $item['refundid'];
        }

        if (!empty($refundid)) {
            $refund = pdo_fetch('select * from ' . tablename('elapp_shop_order_refund') . ' where id=:id limit 1', array(':id' => $refundid));

            $refund['imgs'] = iunserializer($refund['imgs']);
        }
        $r_type = array( '0' => '退款', '1' => '退货退款', '2' => '换货');

        if ($item['finishtime']) {
            $days = intval((time() - $item['finshtime']) / 3600 / 24);
            //申请退款
            $tradeset = m('common')->getSysset('trade');
            $refunddays = intval($tradeset['refunddays']);
            $days <= $refunddays && $days = 0;
        } else {
            $days = 0;
        }


        return array('id' => $id, 'item' => $item, 'refund' => $refund, 'r_type' => $r_type, 'days'=> $days);
    }


    /**
     * 售后申请
     * @return array|void
     */
    function apply() {
        // todo
    }
    /**
     * 售后申请提交
     * @return array|void
     */
    public function apply_submit($ignore_days = false) {
        global $_W, $_GPC;
        extract($this->globalData($ignore_days));

        if ($order['status'] == '-1')
            show_json(0, '订单已经处理完毕!');
        if ($order['paytype'] == 11) {
            show_json(0, '后台付款订单不允许售后');
        }
        $price = trim($_GPC['price']);
        $rtype = intval($_GPC['rtype']);

        if ($rtype != 2) {
            if (empty($price) && $order['deductprice'] == 0) {
                show_json(0, '退款金额不能为0元');
            }
            if ($price > $order['refundprice']) {
                show_json(0, '退款金额不能超过' . $order['refundprice'] . '元');
            }
        }
        //全返退款，退款退货
        if (($rtype == 0 || $rtype == 1) && $order['status'] >= 3) {
            // if(($fullback_log['price']>=$orderprice || $fullbackgoods['refund'] == 0) && $fullback_log){
            //     show_json(0, "此订单不可退款");
            // }
            //全返管理停止
            if ($fullback_log) {
                m('order')->fullbackstop($orderid);
            }
        }
        $refund = array(
            'uniacid' => $uniacid,
            'merchid' => $order['merchid'],
            'applyprice' => $price,
            'rtype' => $rtype,
            'reason' => trim($_GPC['reason']),
            'content' => trim($_GPC['content']),
            'imgs' => iserializer($_GPC['images']),
            'price' => $price,
        );
        if ($refund['rtype'] == 2) {
            $refundstate = 2;
        } else {
            $refundstate = 1;
        }
        pdo_begin();

        if ($order['refundstate'] == 0) {
            //新建一条退款申请
            $refund['createtime'] = time();
            $refund['orderid'] = $orderid;
            $refund['orderprice'] = $order['refundprice'];
            $refund['refundno'] = m('common')->createNO('order_refund', 'refundno', 'SR');
            pdo_insert('elapp_shop_order_refund', $refund);
            $refundid = pdo_insertid();
            pdo_update('elapp_shop_order', array('refundid' => $refundid, 'refundstate' => $refundstate), array('id' => $orderid, 'uniacid' => $uniacid));
        } else {
            $refund['status'] = 0;
            //修改退款申请
            pdo_update('elapp_shop_order', array('refundstate' => $refundstate), array('id' => $orderid, 'uniacid' => $uniacid));
            pdo_update('elapp_shop_order_refund', $refund, array('id' => $refundid, 'uniacid' => $uniacid));
        }
        pdo_commit();

        //模板消息
//        m('notice')->sendOrderMessage($orderid, true);

        show_json(1, ['url' => webUrl('order/list/main')]);
    }

    public function globalData($ignore_days = false)
    {
        global $_W, $_GPC;
        $uniacid = $_W['uniacid'];
        $openid = $_W['openid'];
        $orderid = intval($_GPC['id']);
        $singleRefund = pdo_fetch("select * from " . tablename("elapp_shop_order_single_refund") . " where orderid=:orderid and uniacid=:uniacid  and status in (0,1) limit 1", array(":orderid" => $orderid, ":uniacid" => $uniacid));
        if (!empty($singleRefund)) {
            show_json(0,"订单已单品维权");
        }

        $order = pdo_fetch("select id,status,activity_id,price,refundid,goodsprice,dispatchprice,deductprice,deductcredit2,finishtime,isverify,`virtual`,refundstate,merchid,random_code,iscycelbuy,paytype,sub_account_status from " . tablename('elapp_shop_order') .
                           ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $orderid, ':uniacid' => $uniacid));
        $orderprice = $order['price'];

        // 如订单参与活动且存在限制签收时间内，则不允许单品退换
        if ($order['activity_id'] != 0) {
            $can_single_refund = m('orderGoods')->canRefundByCheckNotReceiveDays($order);
            if (!$can_single_refund) {
                show_json(0,'活动中商品不可退换!');
            }
        }
        if ($order['iscycelbuy'] == 1) {
            //查询分期订单下面是否存在有开始的周期商品
            $order_goods = pdo_fetch("select * from " . tablename('elapp_shop_cycelbuy_periods') . "where orderid = {$order['id']} and status != 0");
            if (!empty($order_goods)) {
                show_json(0, '订单已经开始，无法进行退款');
            }
        }
        $refund = pdo_fetch("select * from " . tablename('elapp_shop_order_refund') . " where uniacid = :uniacid and orderid = :orderid and status > 0 limit 1 ", array(':uniacid' => $uniacid, ':orderid' => $orderid));
        if (!empty($refund) && $refund['rtype'] == 0) {
            if ($_W['isajax']) {
                show_json(0, '此订单已完成维权，不能申请退款');
            } else {
                show_json(0,'此订单已完成维权，不能申请退款');
            }
        }
        if (empty($order)) {
            show_json(0, '订单未找到');
        }
        $_err = '';
        if ($order['status'] <= 0) {
            $_err = '订单未付款或已关闭，不能申请退款!';
        } else {
            if ($order['status'] == 3) {
                if (!empty($order['virtual']) || $order['isverify'] == 1) {
                    $_err = '此订单不允许退款!';
                } else {
                    if ($order['refundstate'] == 0) {
                        //申请退款
                        $tradeset = m('common')->getSysset('trade');
                        $refunddays = intval($tradeset['refunddays']);
                        if ($refunddays > 0) {
                            $days = intval((time() - $order['finishtime']) / 3600 / 24);
                            if ($days > $refunddays && $order['activity_id']==0 && $ignore_days == false) {
                                $_err = '订单完成已超过 ' . $refunddays . ' 天, 无法发起退款申请!';
                            }
                        } else {
                            $_err = '订单完成, 无法申请退款!';
                        }
                    }
                }
            }
        }
        if (!empty($_err)) {
            show_json(0, $_err);
        }
        //订单不能退货商品
        /*********************************************************************/
        $order['cannotrefund'] = true;
        $refundgoods = array(
            'refund' => true,
            'returngoods' => true,
            'exchange' => true,
        );
        if ($order['status'] >= 1) {
            $goods = pdo_fetchall("select og.goodsid, og.price, og.total, og.optionname, g.cannotrefund,g.refund,g.returngoods,g.exchange,g.type, g.thumb, g.title,g.isfullback from" . tablename("elapp_shop_order_goods") . " og left join " . tablename("elapp_shop_goods") . " g on g.id=og.goodsid where og.orderid=" . $order['id']);
            if (!empty($goods)) {
                foreach ($goods as $g) {
                    /*
                     * 退款优化
                     * */
                    if (empty($g['cannotrefund'])) {
                        $g['refund'] = true;
                        $g['returngoods'] = true;
                        $g['exchange'] = true;
                    }
                    if ($order['status'] >= 2) {
                        /*
                         * 退款优化
                         * */
                        if (!empty($g['cannotrefund']) && empty($g['refund']) && empty($g['returngoods']) && empty($g['exchange'])) {
                            $order['cannotrefund'] = false;
                        }
                    }
                    if ($order['status'] == 1) {
                        /*
                         * 退款优化
                         * */
                        if (!empty($g['cannotrefund']) && empty($g['refund'])) {
                            $order['cannotrefund'] = false;
                        }
                    }
                    //虚拟商品完成订单
                    if ($order['status'] >= 3 && $g['type'] == 2) {
                        $g['returngoods'] = false;
                        $g['exchange'] = false;
                    }
                    $refundgoods['refund'] = empty($refundgoods['refund']) ? false : $g['refund'];
                    $refundgoods['returngoods'] = empty($refundgoods['returngoods']) ? false : $g['returngoods'];
                    $refundgoods['exchange'] = empty($refundgoods['exchange']) ? false : $g['exchange'];
                }
            }
        }
        if ($order['cannotrefund'] && empty($refundgoods['refund']) && empty($refundgoods['returngoods']) && empty($refundgoods['exchange'])) {
            $this->message("此订单不可退换货");
        }
        //是否全返商品，并检测是否允许退款
        $fullback_log = pdo_fetchall("select * from " . tablename('elapp_shop_fullback_log') . " where orderid = " . $orderid . " and uniacid = " . $uniacid . " ");
        $fullbackkprice = 0;
        if ($fullback_log && is_array($fullback_log)) {
            foreach ($fullback_log as $key => $value) {
                $fullbackgoods = pdo_fetch("select refund from " . tablename('elapp_shop_fullback_goods') . " where goodsid = " . $value['goodsid'] . " and uniacid = " . $uniacid . " ");
                if ($fullbackgoods['refund'] == 0) {
                    $this->message("此订单包含全返产品不允许退款");
                }
            }
            foreach ($fullback_log as $k => $val) {
                if ($val['fullbackday'] > 0) {
                    if ($val['fullbackday'] < $val['day']) {
                        $fullbackkprice += $val['priceevery'] * $val['fullbackday'];
                    } else {
                        $fullbackkprice += $val['price'];
                    }
                }
            }
        }
        $order['price'] = $order['price'] - $fullbackkprice;
        //应该退的钱 在线支付的+积分抵扣的+余额抵扣的(运费包含在在线支付或余额里）
        $order['refundprice'] = $order['price'] + $order['deductcredit2'];
        if ($order['status'] >= 2) {
            //如果发货，扣除运费
            $order['refundprice'] -= $order['dispatchprice'];
        }
        $order['refundprice'] = round($order['refundprice'], 2);
        return array(
            'uniacid' => $uniacid,
            'refundgoods' => $refundgoods,
            'openid' => $_W['openid'],
            'orderid' => $orderid,
            'order' => $order,
            'refundid' => $order['refundid'],
            'fullback_log' => $fullback_log,
            'fullbackgoods' => $fullbackgoods,
            'orderprice' => $orderprice
        );
    }

    public function refund_after($item)
    {
        // 调用活动插件
        if ($item['activity_id'] != 0) {
            /** @var ActivityModel $activity */
            $activity = p('activity');
            $activity->hook(Action::ORDER_REFUND, new OrderRefundData($item['id'], 1));
        }

        // 如果已经分润，那么需要扣除分润结算金额
        // 1. 获取结算单ids
//        $ids = pdo_getcolumn('elapp_shop_settle_order_oids', ['uniacid'=> $item['uniacid'],  'id'=>$item['id'],'order_type'=>SettleModel::ORDER_TYPE_GOODS_ID], 'ids');
//        if (!empty($ids)) {
//            // 获取所有结算单
////            $ids = explode(',', $ids);
////            $settle_orders = pdo_getall('elapp_shop_settle_order', ['uniacid' => $item['uniacid'], 'id' => $ids], ['id','role_type','role_id','belong_to']);
//        }
        $records = pdo_getall('elapp_shop_settle_order_handle_record',['orderid'=>$item['id'],'order_type'=>SettleModel::ORDER_TYPE_GOODS]);
        if (is_array($records) && !empty($records)) {
            $handler = new CommonSettleHandler();
            foreach ($records as $v) {
                $v['data'] = empty($v['data']) ? [] : json_decode($v['data'], true);
                foreach ($v['data'] as $k => $data) {
                    if ($handler->isHandled($v, $k)) { // k = copartner, activity_1_1, mentor ...
                        // 如果已结算，需要添加减扣收益的处理
                        if ($data['type'] == CommonSettleHandler::HANDLE_TYPE_SETTLE && empty($data['refund'])) {
                            $sid = $handler->createOrUpdateSettleOrderWithCommission(-$data['commission'],
                                                                                     (new SettleModel())->get_role_refund_id_by_key($k),
                                                                                     (new SettleModel())->get_role_type_id_by_key($k),
                                                                                     $k, $item, SettleModel::ORDER_TYPE_GOODS, date('Y-m-d'));
                            if ($sid) {
                                (new SettleModel())->settleOrderAutoCheck($sid);
                            }
                        }
                    }
                    // 如果未结算，设置为已退款
                    $handler->updateHandleData($v, $k, CommonSettleHandler::HANDLE_DONE, 'refund');
                }
            }
        }
    }
}