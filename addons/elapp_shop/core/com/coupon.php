<?php
namespace app\model;
use app\core\com\logic\goods\GoodsLogic;
use app\core\com\logic\goods\GoodsOptionLogic;
use app\order\logic\OrderLogic;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

class CouponComModel extends ComModel {

    protected function getUrl($do, $query = null) {
        $url = mobileUrl($do, $query, true);
        if (strexists($url, '/addons/elapp_shop/')) {
            $url = str_replace("/addons/elapp_shop/", '/', $url);
        }
        if (strexists($url, '/core/mobile/order/')) {
            $url = str_replace("/core/mobile/order/", '/', $url);
        }
        return $url;
    }

    function get_last_count($couponid = 0) {
        global $_W;
        $coupon = pdo_fetch('SELECT id,total FROM ' . tablename('elapp_shop_coupon') . ' WHERE id=:id and uniacid=:uniacid ', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));
        if (empty($coupon)) {
            return 0;
        }
        if ($coupon['total'] == -1) {
            return -1;
        }
        $gettotal = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_coupon_data') . ' where couponid=:couponid and uniacid=:uniacid  and gettype <> 14 ', array(':couponid' => $couponid, ':uniacid' => $_W['uniacid']));
        return $coupon['total'] - $gettotal;
    }

    function creditshop($logid = 0) {
        global $_W, $_GPC;
        $pcreditshop = p('creditshop');
        if (!$pcreditshop) {
            return;
        }
        $log = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_creditshop_log') . ' WHERE `id`=:id and `uniacid`=:uniacid  limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $logid));
        if (!empty($log)) {

            $member = m('member')->getMember($log['openid']);
            $goods = $pcreditshop->getGoods($log['couponid'], $member);
            $couponlog = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $log['openid'],
                'member_id' => $member['id'],
                'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                'couponid' => $log['couponid'],
                'status' => 1,
                'paystatus' => $log['paystatus'] != 0 ? 0 : -1,
                'creditstatus' => $log['creditpay'] > 0 ? 0 : -1,
                'createtime' => time(),
                'getfrom' => 2
            );
            pdo_insert('elapp_shop_coupon_log', $couponlog);
            $data = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $log['openid'],
                'member_id' => $member['id'],
                'merchid' => $_GPC['merchid'],
                'couponid' => $log['couponid'],
                'gettype' => 2,
                'gettime' => time()
            );
            pdo_insert('elapp_shop_coupon_data', $data);
            $this->sethasnewcoupon($log['openid'],1);
            $coupon = pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id limit 1', array(':id' => $log['couponid']));
            $coupon = $this->setCoupon($coupon, time());
            $this->sendMessage($coupon, 1, $member);
            pdo_update('elapp_shop_creditshop_log', array('status' => 3), array('id' => $logid));
        }
    }


    function taskposter($member, $couponid, $couponnum) {
        global $_W, $_GPC;
        $pposter = p('poster');
        if (!$pposter) {
            return;
        }
        $coupon = $this->getCoupon($couponid);
        if (empty($coupon)) {
            return;
        }

        for ($i = 1; $i <= $couponnum; $i++) {
            $couponlog = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $member['openid'],
                'member_id' => $member['id'],
                'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                'couponid' => $couponid,
                'status' => 1,
                'paystatus' => -1,
                'creditstatus' => -1,
                'createtime' => time(),
                'getfrom' => 3
            );
            pdo_insert('elapp_shop_coupon_log', $couponlog);
            $data = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $member['openid'],
                'member_id' => $member['id'],
                'couponid' => $couponid,
                'gettype' => 3,
                'gettime' => time(),
                'nocount' => 1
            );
            pdo_insert('elapp_shop_coupon_data', $data);
            $this->sethasnewcoupon($member['openid'],1);
        }
    }

    function getAvailableCoupons($type, $money = 0, $merch_array,$goods_array=array(), $openid=0, $uniacid=0) {
        global $_W,$_GPC;
        $time = time();
        $param = array();
        $param[':openid'] = !empty($openid)? $openid : $_W['openid'];
        $param[':uniacid'] = !empty($uniacid) ? $uniacid : $_W['uniacid'];
        $sql = "select d.id,d.couponid,d.gettime,c.timelimit,c.timedays,c.timestart,c.timeend,c.thumb,c.couponname,c.enough,c.backtype,c.deduct,c.discount,c.backmoney,c.backcredit,c.backredpack,c.bgcolor,c.thumb,c.merchid,c.limitgoodcatetype,c.limitgoodtype,c.limitgoodcateids,c.limitgoodids,c.limitdiscounttype,c.goodpricelimit,c.extra from " . tablename('elapp_shop_coupon_data') . " d";
        $sql.=" left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id";
        $sql.=" where d.openid=:openid and d.uniacid=:uniacid and c.merchid=0 and d.merchid=0 and c.coupontype={$type} and d.used=0 ";
        if($type==1) {
            $sql.="and {$money}>=c.enough ";
        }
        $sql.=" and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time})) order by d.gettime desc";
        $list = pdo_fetchall($sql, $param);
        if (!empty($merch_array)) {
            foreach ($merch_array as $key => $value) {
                $merchid = $key;
                if ($merchid > 0) {
                    $param[':merchid'] = $merchid;
                    $sql = "select d.id,d.couponid,d.gettime,c.timelimit,c.timedays,c.timestart,c.timeend,c.thumb,c.couponname,c.enough,c.backtype,c.deduct,c.discount,c.backmoney,c.backcredit,c.backredpack,c.bgcolor,c.thumb,c.merchid,c.limitgoodcatetype,c.limitgoodtype,c.limitgoodcateids,c.limitgoodids,c.goodpricelimit  from " . tablename('elapp_shop_coupon_data') . " d";
                    $sql.=" left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id";
                    $sql.=" where d.openid=:openid and d.uniacid=:uniacid and c.merchid=:merchid and d.merchid=:merchid and c.coupontype={$type}  and d.used=0 ";
                    $sql.=" and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time})) order by d.gettime desc";
                    $merch_list = pdo_fetchall($sql, $param);
                    if (!empty($merch_list)) {
                        $list = array_merge($list, $merch_list);
                    }
                }
            }
        }

        $goodlist = array();
        if (!empty($goods_array)) {
            foreach ($goods_array as $key => $value) {
                $goodparam[':uniacid'] = $_W['uniacid'];
                $goodparam[':id'] = $value['goodsid'];
                $sql = "select id,cates,marketprice,merchid,`type`,discounts,istime,isdiscount,timestart,timeend,isdiscount_time,isdiscount_discounts   from " . tablename('elapp_shop_goods') ;
                $sql.=" where uniacid=:uniacid and id =:id order by id desc LIMIT 1 ";
                $good = pdo_fetch($sql, $goodparam);
                $good['saletotal']= $value['total'];
                $good['optionid']= $value['optionid'];
                if($good['type']==4) {
                    $good['marketprice']=$value['wholesaleprice'];
                }
                if(!empty($good)){
                    $goodlist[] = $good;
                }
            }
        }
        // 如果是消费券则检查限制条件
        if($type == 0) {
            $list = $this->checkcouponlimit($list,$goodlist);
        }

        $list = set_medias($list, 'thumb');
        if (!empty($list)) {
            foreach ($list as &$row) {
                $row['thumb'] = tomedia($row['thumb']);
                $row['timestr'] = "永久有效";
                if (empty($row['timelimit'])) {
                    if (!empty($row['timedays'])) {
                        $row['timestr'] = date('Y-m-d H:i', $row['gettime'] + $row['timedays'] * 86400);
                    }
                } else {
                    if ($row['timestart'] >= $time) {
                        $row['timestr'] = date('Y-m-d H:i', $row['timestart']) . '-' . date('Y-m-d H:i', $row['timeend']);
                    } else {
                        $row['timestr'] = date('Y-m-d H:i', $row['timeend']);
                    }
                }
                if ($row['backtype'] == 0) {
                    $row['backstr'] = '立减';
                    $row['css'] = 'deduct';
                    $row['backmoney'] =  (float)$row['deduct'];
                    $row['backpre'] = true;
                    if ($row['enough']=='0') {
                        $row['color']='org ';
                    } else {
                        $row['color']='blue';
                    }
                } else if ($row['backtype'] == 1) {
                    $row['backstr'] = '折';
                    $row['css'] = 'discount';
                    $row['backmoney'] =  (float)$row['discount'];
                    $row['color']='red ';
                } else if ($row['backtype'] == 2) {
                    if($row['coupontype']=='0') {
                        $row['color']='red ';
                    } else {
                        $row['color']='pink ';
                    }

                    if ($row['backredpack'] > 0) {
                        $row['backstr'] = '返现';
                        $row['css'] = "redpack";
                        $row['backmoney'] =  (float)$row['backredpack'];
                        $row['backpre'] = true;
                    } else if ($row['backmoney'] > 0) {
                        $row['backstr'] = '返利';
                        $row['css'] = "money";
                        $row['backmoney'] =  (float)$row['backmoney'];
                        $row['backpre'] = true;
                    } else if (!empty($row['backcredit'])) {
                        $row['backstr'] = '返积分';
                        $row['css'] = "credit";
                        $row['backmoney'] =  (float)$row['backcredit'];
                    }
                }
            }
            unset($row);
        }
        return $list;
    }

    /**
     * 检查优惠券限制条件
     * @param array $list 优惠券列表数组
     * @param array $goodlist 商品列表数组
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    function checkcouponlimit(array $list , array $goodlist): array
    {
        global $_W;
        $openid = $_W['openid'];
        //会员等级
        $level = m('member')->getLevel($openid);

        foreach($list as $key=> $row){
            $pass = 0;
            $enough =0;

            if($row['limitgoodcatetype']==0 && $row['limitgoodtype']==0 && $row['enough']==0 && $row['limitdiscounttype'] == 0 ) {
                $pass = 1;
            } else {
                foreach($goodlist as $good) {
                    if($row['merchid']>0 && $good['merchid']>0 && $row['merchid'] != $good['merchid']) {
                        continue;
                    }
                    $p=0;
                    $cates = explode(',',$good['cates']);
                    $limitcateids =explode(',',$row['limitgoodcateids']);
                    $limitgoodids =explode(',',$row['limitgoodids']);

                    if($row['limitgoodcatetype']==0 && $row['limitgoodtype']==0) {
                        $p = 1;
                    }
                    // 商品分类限制 limitgoodcatetype 0 不限制 1只允许使用 2不允许使用
                    if(!empty($row['limitgoodcatetype'])) {
                        $result = array_intersect($cates,$limitcateids);
                        // 只允许使用的分类
                        if(count($result)>0 && $row['limitgoodcatetype']==1) {
                            $p = 1;
                        } else {
                            // 不允许使用的分类
                            if(count($result)==0 && $row['limitgoodcatetype']==2) {
                                $p = 1;
                            }
                        }
                    }
                    // 商品限制 limitgoodtype 0 不限制 1只允许使用 2不允许使用
                    if (!empty($row['limitgoodtype'])) {
                        $isin = in_array($good['id'], $limitgoodids);
                        // 只允许使用的商品
                        if ($isin && $row['limitgoodtype'] == 1) {
                            $p = 1;
                        } else {
                            // 不允许使用的商品
                            if (!$isin && $row['limitgoodtype'] == 2) {
                                $p = 1;
                            }
                        }
                    }
                    if($p==1) {
                        $pass=1;
                    }
                    if($row['enough'] > 0 && $p==1) {
                        // 限制消费满额 0零售价 1会员价
                        $goodpricelimit = $row['goodpricelimit'];
                        $goodPrice = 0;

                        if($good['optionid'] > 0 && $good['type'] != 4) {
                            $option = app(GoodsOptionLogic::class)->getGoodsOptionDetail($good['optionid'], 'marketprice')->toArray();
                            if(!empty($option)) {
                                $member_price = app(OrderLogic::class)->getGoodsMemberPrice($good['id'], $level, $good['saletotal'], $good['optionid']);
                                $goodPrice = $this->calculateGoodPrice($goodpricelimit, $member_price, $option['marketprice']);
                            }
                        } else {
                            $member_price = app(OrderLogic::class)->getGoodsMemberPrice($good['id'], $level, $good['saletotal']);
                            $goodPrice = $this->calculateGoodPrice($goodpricelimit, $member_price, $good['marketprice']);
                        }

                        if ($goodPrice > 0) {
                            $enough += ((float)$goodPrice) * $good['saletotal'];
                        }
                    }
                }
                unset($good);
                if(($row['enough'] > 0 && $row['enough'] > $enough) ) {
                    $pass = 0;
                }
            }
            $row['extra'] = empty($row['extra']) ? array() : json_decode($row['extra'], true);
            // 限制使用时间
            if (!empty($row['extra']['limit_use_time']['type']) && $row['extra']['limit_use_time']['type'] == 1) {

                $method = $row['extra']['limit_use_time']['method']; // 获取时间区间，根据， 今天，本周，本月，本年
                $times = $row['extra']['limit_use_time']['times']; // 使用次数限制
                // 根据时间值，获取时间戳区间
                if ($method == 'days') {
                    $start = strtotime(date('Y-m-d 00:00:00', time()));
                    $end = strtotime(date('Y-m-d 23:59:59', time()));
                } else if ($method == 'weeks') {
                    $start = strtotime(date('Y-m-d 00:00:00', strtotime('this week monday')));
                    $end = strtotime(date('Y-m-d 23:59:59', strtotime('this week monday + 6 days')));
                } else if ($method == 'months') {
                    $start = strtotime(date('Y-m-01 00:00:00', time()));
                    $end = strtotime(date('Y-m-t 23:59:59', time()));
                } else if ($method == 'years') {
                    $start = strtotime(date('Y-01-01 00:00:00', time()));
                    $end = strtotime(date('Y-12-31 23:59:59', time()));
                }

                // 读取用户使用优惠券的记录条数
                $w =  array(':openid' => $openid, ':couponid' => $row['couponid'], ':start' => $start, ':end' => $end);
                $useCount = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename('elapp_shop_coupon_data') . ' WHERE openid = :openid AND couponid = :couponid AND usetime >= :start AND usetime <= :end',$w);
                if ($useCount >= $times) {
                    $pass = 0;
                }
            }

            if($pass == 0){
                unset($list[$key]);
            }
        }
        unset($row);
        return array_values($list)?:[];
    }

    /**
     * 获取最小的金额计算优惠券满额金额
     * @param int $goodpricelimit 限制消费金额 0零售价 1会员价
     * @param float $member_price
     * @param float $marketprice
     * @return mixed
     */
    function calculateGoodPrice(int $goodpricelimit, float $member_price, float $marketprice) {
        if(!empty($goodpricelimit)) {
            return min($member_price, $marketprice);
        } else {
            return $marketprice;
        }
    }

    function payResult($logno) {
        global $_W;
        if (empty($logno)) {
            return error(-1);
        }
        $log = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_coupon_log') . ' WHERE `logno`=:logno and `uniacid`=:uniacid  limit 1', array(':uniacid' => $_W['uniacid'], ':logno' => $logno));
        if (empty($log)) {
            return error(-1, '服务器错误!');
        }
        if ($log['status'] >= 1) {
            return true;
        }
        $coupon = pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id limit 1', array(':id' => $log['couponid']));
        $coupon = $this->setCoupon($coupon, time());
        if (empty($coupon['gettype'])) {
            return error(-1, '无法领取');
        }
        if ($coupon['total'] != -1) {
            if ($coupon['total'] <= 0) {
                return error(-1, '优惠券数量不足');
            }
        }
        if (!$coupon['canget']) {
            return error(-1, '您已超出领取次数限制');
        }

        if (empty($log['status'])) {

            $update = array();
            if ($coupon['credit'] > 0 && empty($log['creditstatus'])) {
                m('member')->setCredit($log['openid'], 'credit1', -$coupon['credit'], "购买优惠券扣除积分 {$coupon['credit']}");
                $update['creditstatus'] = 1;
            }
            if ($coupon['money'] > 0 && empty($log['paystatus'])) {
                if ($log['paytype'] == 0) {
                    m('member')->setCredit($log['openid'], 'credit2', -$coupon['money'], "购买优惠券扣除余额 {$coupon['money']}");
                }
                $update['paystatus'] = 1;
            }
            $update['status'] = 1;
            pdo_update('elapp_shop_coupon_log', $update, array('id' => $log['id']));
            $data = array(
                'uniacid' => $_W['uniacid'],
                'merchid' => $log['merchid'],
                'openid' => $log['openid'],
                'member_id' => $log['id'],
                'couponid' => $log['couponid'],
                'gettype' => $log['getfrom'],
                'gettime' => time()
            );
            pdo_insert('elapp_shop_coupon_data', $data);
            $dataid = pdo_insertid();
            $this->sethasnewcoupon($log['openid'],1);

            $coupon['dataid'] = $dataid;
            $member = m('member')->getMember($log['openid']);
            $set = m('common')->getPluginset('coupon');
            $this->sendMessage($coupon, 1, $member);
        }

        $url = mobileUrl('member', null, true);

        if ($coupon['coupontype'] == 0) {
            $coupon['url'] = mobileUrl('goods', null, true);
        } else {
            $coupon['url'] = mobileUrl('member/recharge', null, true);
        }


        return $coupon;
    }

    function sendMessage($coupon, $send_total, $member, $account = null) {
        global $_W;
        $articles = array();
        $title = str_replace('[nickname]', $member['nickname'], $coupon['resptitle']);
        $desc = str_replace('[nickname]', $member['nickname'], $coupon['respdesc']);

        $title = str_replace('[total]', $send_total, $title);
        $desc = str_replace('[total]', $send_total, $desc);

        $siteroot = $_W['siteroot'];
        if(strexists($siteroot,'/addons/elapp_shop/')){
            $replace_str =  array("/addons/elapp_shop/");
            $siteroot = str_replace($replace_str,'/',trim($siteroot));
            $_W['siteroot'] = $siteroot;
        }
        $url = empty($coupon['respurl']) ? mobileUrl('sale/coupon/my/main', null, true) : $coupon['respurl'];
        $picurl = tomedia($coupon['respthumb']);
        if(!strexists($picurl,'http')){
            $picurl = $this->spec_tomedia($coupon['respthumb']);
        }

        if (!empty($coupon['resptitle'])) {
            $articles[] = array(
                "title" => urlencode($title),
                "description" => urlencode($desc),
                "url" => $url,
                "picurl" => $picurl
            );
        }
        if (!empty($articles)) {
            $resp = m('message')->sendNews($member['openid'], $articles, $account);
            if (is_error($resp)) {
                $templateid = $coupon['templateid'];
                $msg = array(
                    'first' => array('value' => "尊敬的".$member['nickname']."恭喜您获得优惠券", "color" => "#ff0000"),
                    'keyword1' => array('title' => '业务类型', 'value' => '优惠券通知', "color" => "#000000"),
                    'keyword2' => array('title' => '处理进度', 'value' => '获得'.$coupon['couponname'], "color" => "#000000"),
                    'keyword3' => array('title' => '处理内容', 'value' => $desc, "color" => "#4b9528"),
                    'keyword4' => array('title' => '操作时间', 'value' => date('Y-m-d H:i:s', time()), "color" => "#4b9528"),
                    'remark' => array('value' => '点击查看详情', "color" => "#000000")
                );
                if(!empty($templateid)){
                    m('message')->sendTplNotice($member['openid'], $templateid, $msg, $url);
                }
            }
        }
    }

    function sendBackMessage($openid, $coupon, $gives) {
        global $_W;
        if (empty($gives)) {
            return;
        }

        $set = m('common')->getPluginset('coupon');
        $templateid = $set['templateid'];
        $content = "您的优惠券【{$coupon['couponname']}】已返利 ";
        $givestr = '';
        if (isset($gives['credit'])) {
            $givestr.=" {$gives['credit']}个积分";
        }
        if (isset($gives['money'])) {
            if (!empty($givestr)) {
                $givestr.="，";
            }
            $givestr.="{$gives['money']}元余额";
        }
        if (isset($gives['redpack'])) {
            if (!empty($givestr)) {
                $givestr.="，";
            }
            $givestr.="{$gives['redpack']}元现金";
        }
        $content.=$givestr;
        $content.="，请查看您的账户，谢谢!";
        $msg = array(
            'keyword1' => array('value' => "优惠券返利", "color" => "#73a68d"),
            'keyword2' => array('value' => $content, "color" => "#73a68d"),
            'keyword3' => array('value' => date('Y-m-d H:i:s'), "color" => "#000000")
        );
        $url = mobileUrl('member', null, true);

        if (!empty($templateid)) {
            m('message')->sendTplNotice($openid, $templateid, $msg, $url);
        } else {
            m('message')->sendCustomNotice($openid, $msg, $url);
        }
    }

    function sendReturnMessage($openid, $coupon) {
        global $_W;
        $set = m('common')->getPluginset('coupon');
        $templateid = $set['templateid'];
        $msg = array(
            'keyword1' => array('value' => "优惠券退回", "color" => "#73a68d"),
            'keyword2' => array('value' => "您的优惠券【{$coupon['couponname']}】已退回您的账户，您可以再次使用, 谢谢!", "color" => "#73a68d"),
            'keyword3' => array('value' => date('Y-m-d H:i:s'), "color" => "#000000")
        );
        $url = mobileUrl('sale/coupon/my/main', null, true);
        if (!empty($templateid)) {
            m('message')->sendTplNotice($openid, $templateid, $msg, $url);
        } else {
            m('message')->sendCustomNotice($openid, $msg, $url);
        }
    }

    function useRechargeCoupon($log) {
        global $_W;
        if (empty($log['couponid'])) {
            return;
        }
        $data = pdo_fetch('select id,openid,couponid,used from ' . tablename('elapp_shop_coupon_data') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $log['couponid'], ':uniacid' => $_W['uniacid']));
        if (empty($data)) {
            return;
        }
        if (!empty($data['used'])) {
            return;
        }
        $coupon = pdo_fetch('select enough,backcredit,backmoney,backredpack,couponname,goodpricelimit from ' . tablename('elapp_shop_coupon') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $data['couponid'], ':uniacid' => $_W['uniacid']));
        if (empty($coupon)) {
            return;
        }
        if ($coupon['enough'] > 0 && $log['money'] < $coupon['enough']) {
            return;
        }
        $gives = array();
        $backcredit = $coupon['backcredit'];
        if (!empty($backcredit)) {
            if (strexists($backcredit, '%')) {
                $backcredit = intval(floatval(str_replace('%', '', $backcredit)) / 100 * $log['money']);
            } else {
                $backcredit = intval($backcredit);
            }
            if ($backcredit > 0) {
                $gives['credit'] = $backcredit;
                m('member')->setCredit($data['openid'], 'credit1', $backcredit, array(0, '充值优惠券返积分'));
            }
        }
        $backmoney = $coupon['backmoney'];
        if (!empty($backmoney)) {
            if (strexists($backmoney, '%')) {
                $backmoney = round(floatval(floatval(str_replace('%', '', $backmoney)) / 100 * $log['money']), 2);
            } else {
                $backmoney = round(floatval($backmoney), 2);
            }
            if ($backmoney > 0) {
                $gives['money'] = $backmoney;
                m('member')->setCredit($data['openid'], 'credit2', $backmoney, array(0, '充值优惠券返利'));
            }
        }

        $backredpack = $coupon['backredpack'];
        if (!empty($backredpack)) {
            if (strexists($backredpack, '%')) {
                $backredpack = round(floatval(floatval(str_replace('%', '', $backredpack)) / 100 * $log['money']), 2);
            } else {
                $backredpack = round(floatval($backredpack), 2);
            }
            if ($backredpack > 0) {

                $gives['redpack'] = $backredpack;
                $backredpack = intval($backredpack * 100);
                m('finance')->pay($data['openid'], 1, $backredpack, '', '充值优惠券-返现金',false);
            }
        }
        pdo_update('elapp_shop_coupon_data', array('used' => 1, 'usetime' => time(), 'ordersn' => $log['logno']), array('id' => $data['id']));
        $this->posterbyusesendtask($data['couponid'],$_W['openid']);
        $this->sendBackMessage($log['openid'], $coupon, $gives);

        if(!empty($backcredit)||!empty($backmoney)) {
            $account = m('common')->getAccount();
            $member = m('member')->getMember($log['openid']);
            $text = "尊敬的".$member['nickname']."，您使用了".$coupon['couponname']."  贈送您";
            if (!empty($backcredit)) {
                $text .= $backcredit."积分  ";
            }
            if (!empty($backmoney)) {
                $text .= $backmoney."余额  ";
            }
            m('message')->sendTexts($log['openid'], $text, '', $account);
        }
    }

    function consumeCouponCount($openid, $money = 0, $merch_array,$goods_array) {
        global $_W, $_GPC;
        $time = time();
        $param = array();
        $param[':openid'] = $openid;
        $param[':uniacid'] = $_W['uniacid'];
        $sql = "select d.id,d.couponid,c.enough,c.merchid,c.limitgoodtype,c.limitgoodcatetype,c.limitgoodcateids,c.limitgoodids,c.limitdiscounttype,c.goodpricelimit,c.extra from " . tablename('elapp_shop_coupon_data') . "d";
        $sql.=" left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id";
        $sql.=" where d.openid=:openid and d.uniacid=:uniacid and c.merchid=0 and d.merchid=0 and (c.coupontype=0 or c.coupontype=3) and d.used=0 ";
        $sql.=" and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time})) order by d.gettime desc";
        $list = pdo_fetchall($sql, $param);
        if (!empty($merch_array)) {
            foreach ($merch_array as $key => $value) {
                $merchid = $key;
                if ($merchid > 0) {
                    $ggprice = $value['ggprice'];
                    $param[':merchid'] = $merchid;
                    $sql = "select d.id,d.couponid,c.enough,c.merchid,c.limitgoodtype,c.limitgoodcatetype,c.limitgoodcateids,c.limitgoodids,c.goodpricelimit from " . tablename('elapp_shop_coupon_data') . "d";
                    $sql.=" left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id";
                    $sql.=" where d.openid=:openid and d.uniacid=:uniacid and c.merchid=:merchid and d.merchid=:merchid and (c.coupontype=0 or c.coupontype=3)  and d.used=0 ";
                    $sql.=" and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time}))";
                    $merch_list = pdo_fetchall($sql, $param);

                    if (!empty($merch_list)) {
                        $list = array_merge($list, $merch_list);
                    }
                }
            }
        }
        $goodlist = array();
        if (!empty($goods_array)) {
            foreach ($goods_array as $key => $value) {
                $goodparam[':uniacid'] = $_W['uniacid'];
                $goodparam[':id'] = $value['goodsid'];
                $sql = "select id,cates,marketprice,merchid,`type`,discounts,discounts,istime,isdiscount,timestart,timeend,isdiscount_time,isdiscount_discounts,hasoption from " . tablename('elapp_shop_goods') ;
                $sql.=" where uniacid=:uniacid and id =:id order by id desc LIMIT 1 ";
                $good = pdo_fetch($sql, $goodparam);
                $good['saletotal']= $value['total'];
                $good['optionid']= $value['optionid'];
                if($good['type']==4) {
                    $good['marketprice']=$value['wholesaleprice'];
                }
                if(!empty($good)){
                    $goodlist[] = $good;
                }
            }
        }
        $list = $this->checkcouponlimit($list,$goodlist);

        return count($list);
    }

    function rechargeCouponCount($openid, $money = 0) {
        global $_W, $_GPC;
        $time = time();
        $sql = "select count(*) from " . tablename('elapp_shop_coupon_data') . " d "
            . "  left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id "
            . "  where d.openid=:openid and d.uniacid=:uniacid and  c.coupontype=1 and {$money}>=c.enough and d.used=0 "
            . " and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time}))";
        return pdo_fetchcolumn($sql, array(':openid' => $openid, ':uniacid' => $_W['uniacid']));
    }

    function useConsumeCoupon($orderid = 0) {
        global $_W, $_GPC;
        if (empty($orderid)) {
            return;
        }
        $order = pdo_fetch('select ordersn,createtime,couponid from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0 and uniacid=:uniacid limit 1', array(':id' => $orderid, ':uniacid' => $_W['uniacid']));
        if (empty($order)) {
            return;
        }
        $coupon = false;
        if (!empty($order['couponid'])) {
            $coupon = $this->getCouponByDataID($order['couponid']);
        }
        if (empty($coupon)) {
            return;
        }
        pdo_update('elapp_shop_coupon_data', array('used' => 1, 'usetime' => $order['createtime'], 'ordersn' => $order['ordersn']), array('id' => $order['couponid']));
        $this->posterbyusesendtask($coupon['id'],$_W['openid']);
    }

    function returnConsumeCoupon($order) {
        global $_W;
        if (!is_array($order)) {
            $order = pdo_fetch('select id,openid,ordersn,createtime,couponid,status,finishtime from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0 and uniacid=:uniacid limit 1', array(':id' => intval($order), ':uniacid' => $_W['uniacid']));
        }
        if (empty($order)) {
            return;
        }
        $coupon = $this->getCouponByDataID($order['couponid']);
        if (empty($coupon)) {
            return;
        }
        if (!empty($coupon['used']) && ($coupon['ordersn'] == $order['ordersn'])) {
            pdo_update('elapp_shop_coupon_data', array('used' => 0, 'usetime' => 0, 'ordersn' => ''), array('id' => $order['couponid']));
            $this->sendReturnMessage($order['openid'], $coupon);
        }
    }

    function backConsumeCoupon($orderid) {
        global $_W;
        if (!is_array($orderid)) {
            $order = pdo_fetch('select id,openid,ordersn,createtime,couponid,couponmerchid,status,finishtime,`virtual`,isparent,parentid,coupongoodprice  from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0 and couponid >0 and uniacid=:uniacid limit 1', array(':id' => intval($orderid), ':uniacid' => $_W['uniacid']));
        }
        if (empty($order)) {
            return;
        }
        $couponid = $order['couponid'];
        $couponmerchid = $order['couponmerchid'];
        $isparent = $order['isparent'];
        $parentid = $order['parentid'];
        $finishtime = $order['finishtime'];
        if (empty($couponid)) {
            return;
        }
        $coupon = $this->getCouponByDataID($order['couponid']);
        if($coupon['backtype']!=2){
            return ;
        }
        if (empty($coupon)) {
            return;
        }
        if (!empty($coupon['back'])) {
            return;
        }
        $coupongoodprice = 0;
        if($parentid == 0) {
            $coupongoodprice = $order['coupongoodprice'];
        }
        if ($isparent == 1 || $parentid != 0) {
            $all_done = 1;
            if ($isparent == 1) {
                if ($couponmerchid > 0) {
                    $sql = 'select id,openid,ordersn,createtime,couponid,couponmerchid,status,finishtime,`virtual`,isparent,parentid from ' . tablename('elapp_shop_order') . ' where parentid=:parentid and couponmerchid=:couponmerchid and status>=0 and uniacid=:uniacid limit 1';
                    $order = pdo_fetch($sql, array(':parentid' => $orderid, ':couponmerchid' => $couponmerchid, ':uniacid' => $_W['uniacid']));
                    if (empty($order)) {
                        return;
                    }
                    if ($order['status'] != 3) {
                        $all_done = 0;
                    } else {
                        $finishtime = $order['finishtime'];
                    }
                } else {
                    $list = m('order')->getChildOrder($orderid);
                }
            } else {
                if ($couponmerchid > 0) {
                    if ($order['status'] != 3) {
                        $all_done = 0;
                    } else {
                        $finishtime = $order['finishtime'];
                    }
                } else {
                    $list = m('order')->getChildOrder($parentid);
                }
            }
            if (!empty($list)) {
                foreach ($list as $k => $v) {
                    if ($v['status'] != 3&&$v['couponid']>0) {
                        $all_done = 0;
                    } else {
                        if ($v['finishtime'] > $finishtime) {
                            $finishtime = $v['finishtime'];
                        }
                    }
                }
            }
        }
        if ($parentid != 0 && $couponmerchid == 0) {
            if ($all_done == 1) {
                $sql = 'select id,openid,ordersn,createtime,couponid,couponmerchid,status,finishtime,`virtual`,isparent,parentid from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0 and uniacid=:uniacid limit 1';
                $order = pdo_fetch($sql, array(':id' => $parentid, ':uniacid' => $_W['uniacid']));
                if (empty($order)) {
                    return;
                }
            }
        }
        $backcredit = $coupon['backcredit'];
        $backmoney = $coupon['backmoney'];
        $backredpack = $coupon['backredpack'];
        $gives = array();
        $canback = false;
        if ($order['status'] == 1 && $coupon['backwhen'] == 2) {
            $canback = true;
        } else {
            $is_done = 0;
            if ($isparent == 1 || $parentid != 0) {
                if ($all_done == 1) {
                    $is_done = 1;
                }
            } else {
                if ($order['status'] == 3) {
                    $is_done = 1;
                }
            }
            if ($is_done == 1) {
                if (!empty($order['virtual'])) {
                    $canback = true;
                } else {
                    if ($coupon['backwhen'] == 1) {
                        $canback = true;
                    } else if ($coupon['backwhen'] == 0) {
                        $canback = true;
                        $tradeset = m('common')->getSysset('trade');
                        $refunddays = intval($tradeset['refunddays']);
                        if ($refunddays > 0) {
                            $days = intval((time() - $finishtime) / 3600 / 24);
                            if ($days <= $refunddays) {
                                $canback = false;
                            }
                        }
                    }
                }
            }
        }

        if ($canback) {
            if($parentid>0) {
                $ordermoney = pdo_fetchcolumn('select coupongoodprice from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0 and couponid >0 and uniacid=:uniacid limit 1', array(':id' => intval($parentid), ':uniacid' => $_W['uniacid']));
            } else {
                $ordermoney = $coupongoodprice;
            }

            if($ordermoney==0) {
                $sql = 'select ifnull( sum(og.realprice),0) from ' . tablename('elapp_shop_order_goods') . ' og ';
                $sql .= ' left join ' . tablename('elapp_shop_order') . ' o on';
                if ($couponmerchid==0 && $isparent == 1) {
                    $sql .= ' o.id=og.parentorderid ';
                } else {
                    $sql .= ' o.id=og.orderid ';
                }
                $sql .= ' where o.id=:orderid and o.openid=:openid and o.uniacid=:uniacid ';

                $ordermoney = pdo_fetchcolumn($sql, array(':uniacid' => $_W['uniacid'], ':openid' => $order['openid'], ':orderid' => $order['id']));

            }

            if (!empty($backcredit)) {
                if (strexists($backcredit, '%')) {
                    $backcredit = intval(floatval(str_replace('%', '', $backcredit)) / 100 * $ordermoney);
                } else {
                    $backcredit = intval($backcredit);
                }
                if ($backcredit > 0) {
                    $gives['credit'] = $backcredit;
                    m('member')->setCredit($order['openid'], 'credit1', $backcredit, array(0, '充值优惠券返积分'));
                }
            }
            if (!empty($backmoney)) {
                if (strexists($backmoney, '%')) {
                    $backmoney = round(floatval(floatval(str_replace('%', '', $backmoney)) / 100 * $ordermoney), 2);
                } else {
                    $backmoney = round(floatval($backmoney), 2);
                }
                if ($backmoney > 0) {
                    $gives['money'] = $backmoney;
                    m('member')->setCredit($order['openid'], 'credit2', $backmoney, array(0, '购物优惠券返利'));
                }
            }

            if (!empty($backredpack)) {
                if (strexists($backredpack, '%')) {
                    $backredpack = round(floatval(floatval(str_replace('%', '', $backredpack)) / 100 * $ordermoney), 2);
                } else {
                    $backredpack = round(floatval($backredpack), 2);
                }
                if ($backredpack > 0) {
                    $gives['redpack'] = $backredpack;
                    $backredpack = intval($backredpack * 100);
                    m('finance')->pay($order['openid'], 1, $backredpack, '', '购物优惠券-返现金',false);
                }
            }
            pdo_update('elapp_shop_coupon_data', array('back' => 1, 'backtime' => time()), array('id' => $order['couponid']));
            $this->sendBackMessage($order['openid'], $coupon, $gives);
        }
    }

    function getCoupon($couponid = 0) {
        global $_W;
        return pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));
    }

    function getCouponByDataID($dataid = 0) {
        global $_W;
        $data = pdo_fetch('select id,openid,couponid,used,back,backtime,ordersn from ' . tablename('elapp_shop_coupon_data') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $dataid, ':uniacid' => $_W['uniacid']));
        if (empty($data)) {
            return false;
        }
        $coupon = pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $data['couponid'], ':uniacid' => $_W['uniacid']));
        if (empty($coupon)) {
            return false;
        }
        $coupon['back'] = $data['back'];
        $coupon['backtime'] = $data['backtime'];
        $coupon['used'] = $data['used'];
        $coupon['usetime'] = $data['usetime'];
        $coupon['ordersn'] = $data['ordersn'];
        return $coupon;
    }

    function setCoupon($row, $time, $withOpenid = true) {
        global $_W;
        if ($withOpenid) {
            $openid = $_W['openid'];
        }
        $row['free'] = false;
        $row['past'] = false;
        $row['thumb'] = tomedia($row['thumb']);
        $row['merchname'] = '';
        $row['total'] = $this->get_last_count($row['id']);
        if ($row['merchid'] > 0) {
            $merch_plugin = p('merch');
            if ($merch_plugin) {
                $merch_user = $merch_plugin->getListUserOne($row['merchid']);
                if (!empty($merch_user)) {
                    $row['merchname'] = $merch_user['merchname'];
                }
            }
        }

        if ($row['money'] > 0 && $row['credit'] > 0) {
            $row['getstatus'] = 0;
            $row['gettypestr'] = "购买";
        } else if ($row['money'] > 0) {
            $row['getstatus'] = 1;
            $row['gettypestr'] = "购买";
        } else if ($row['credit'] > 0) {
            $row['getstatus'] = 2;
            $row['gettypestr'] = "兑换";
        } else {
            $row['getstatus'] = 3;
            $row['gettypestr'] = "领取";
        }
        $row['timestr'] = "0";
        if (empty($row['timelimit'])) {
            if (!empty($row['timedays'])) {
                $row['timestr'] = 1;
            }
        } else {
            if ($row['timestart'] >= $time) {
                $row['timestr'] = date('Y-m-d', $row['timestart']) . '-' . date('Y-m-d', $row['timeend']);
            } else {
                $row['timestr'] = date('Y-m-d', $row['timeend']);
            }
        }
        $row['css'] = 'deduct';
        if ($row['backtype'] == 0) {
            $row['backstr'] = '立减';
            $row['css'] = 'deduct';
            $row['backpre'] = true;
            $row['_backmoney'] = $row['deduct'];
        } else if ($row['backtype'] == 1) {
            $row['backstr'] = '折';
            $row['css'] = 'discount';
            $row['_backmoney'] = $row['discount'];
        } else if ($row['backtype'] == 2) {
            if (!empty($row['backredpack'])) {
                $row['backstr'] = '返现';
                $row['css'] = "redpack";
                $row['backpre'] = true;
                $row['_backmoney'] = $row['backredpack'];
            } else if (!empty($row['backmoney'])) {
                $row['backstr'] = '返利';
                $row['css'] = "money";
                $row['backpre'] = true;
                $row['_backmoney'] = $row['backmoney'];
            } else if (!empty($row['backcredit'])) {
                $row['backstr'] = '返积分';
                $row['css'] = "credit";
                $row['_backmoney'] = $row['backcredit'];
            }
        }
        if ($withOpenid) {
            $row['cangetmax'] = -1;
            $row['canget'] = true;
            if($row['total']!=-1 && $row['total']<=0){
                $row['canget'] = false;
                $row['cangetmax'] = -2;
                return $row;
            }

            if ($row['getmax'] > 0) {
                $gets = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_coupon_data') . ' where couponid=:couponid and openid=:openid and uniacid=:uniacid and gettype=1 limit 1', array(':couponid' => $row['id'], ':openid' => $openid, ':uniacid' => $_W['uniacid']));
                $row['cangetmax'] = $row['getmax'] - $gets;
                if ($row['cangetmax'] <= 0) {
                    $row['cangetmax'] = 0;
                    $row['canget'] = false;
                }
            }

            //可申请的代金券
            if($row['is_apply']){
                $row['canapply'] = 1;
                $row['canget'] = true;
                $apply_count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_coupon_apply_log') . ' where couponid=:couponid and openid=:openid and uniacid=:uniacid and status=0 limit 1', array(':couponid' => $row['id'], ':openid' => $openid, ':uniacid' => $_W['uniacid']));
                if($apply_count>0){
                    $row['canapply'] = 0;
                }
                //未使用的代金券
                $gets = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_coupon_data') . ' where couponid=:couponid and openid=:openid and uniacid=:uniacid and gettype=0 and used=0 limit 1', array(':couponid' => $row['id'], ':openid' => $openid, ':uniacid' => $_W['uniacid']));
                if($gets >= 5){
                    $row['cangetmax'] = 5;
                    $row['canget'] = false;
                }
            }
        }
        return $row;
    }

    function setMyCoupon($row, $time) {
        global $_W;
        $row['past'] = false;
        $row['thumb'] = tomedia($row['thumb']);
        $row['merchname'] = '';
        if ($row['merchid'] > 0) {
            $merch_plugin = p('merch');
            if ($merch_plugin) {
                $merch_user = $merch_plugin->getListUserOne($row['merchid']);
                if (!empty($merch_user)) {
                    $row['merchname'] = $merch_user['merchname'];
                }
            }
        }

        $row['timestr'] = "";
        if (empty($row['timelimit'])) {
            if (!empty($row['timedays'])) {
                $row['timestr'] = date('Y-m-d', $row['gettime'] + $row['timedays'] * 86400);
                if ($row['gettime'] + $row['timedays'] * 86400 < $time) {
                    $row['past'] = true;
                }
            }
        } else {
            if ($row['timestart'] >= $time) {
                $row['timestr'] = date('Y-m-d H:i', $row['timestart']) . '-' . date('Y-m-d', $row['timeend']);
            } else {
                $row['timestr'] = date('Y-m-d H:i', $row['timeend']);
            }

            if ($row['timeend'] < $time) {
                $row['past'] = true;
            }
        }
        $row['css'] = 'deduct';
        if ($row['backtype'] == 0) {
            $row['backstr'] = '立减';
            $row['css'] = 'deduct';
            $row['backpre'] = true;
            $row['_backmoney'] = $row['deduct'];
        } else if ($row['backtype'] == 1) {
            $row['backstr'] = '折';
            $row['css'] = 'discount';
            $row['_backmoney'] = $row['discount'];
        } else if ($row['backtype'] == 2) {
            if (!empty($row['backredpack'])) {
                $row['backstr'] = '返现';
                $row['css'] = "redpack";
                $row['backpre'] = true;
                $row['_backmoney'] = $row['backredpack'];
            } else if (!empty($row['backmoney'])) {
                $row['backstr'] = '返利';
                $row['css'] = "money";
                $row['backpre'] = true;
                $row['_backmoney'] = $row['backmoney'];
            } else if (!empty($row['backcredit'])) {
                $row['backstr'] = '返积分';
                $row['css'] = "credit";
                $row['_backmoney'] = $row['backcredit'];
            }
        }
        if ($row['past']) {
            $row['css'] = 'past';
        }
        return $row;
    }

    function setShare() {
        global $_W, $_GPC;
        $set = m('common')->getPluginset('coupon');
        $openid = $_W['openid'];
        $url = mobileUrl('sale/coupon/index/main', null, true);
        $_W['shopshare'] = array(
            'title' => $set['title'],
            'imgUrl' => tomedia($set['icon']),
            'desc' => $set['desc'],
            'link' => $url
        );
        //分销商
        if (p('commission')) {
            $pset = p('commission')->getSet();
            if (!empty($pset['level'])) {
                $member = m('member')->getMember($openid);
                if (!empty($member) && $member['status'] == 1 && $member['isagent'] == 1) {
                    $_W['shopshare']['link'] = $url . "&mid=" . $member['id'];
                    if (empty($pset['become_reg']) && ( empty($member['realname']) || empty($member['mobile']))) {
                        $trigger = true;
                    }
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = $url . "&mid=" . $_GPC['id'];
                }
            }
        }
        //虚店店员
        if (p('clerk')) {
            $clerkset = p('clerk')->getSet();
            if (!empty($clerkset['level'])) {
                $member = m('member')->getMember($openid);
                if (!empty($member) && $member['clerk_status'] == 1 && $member['is_clerk'] == 1) {
                    $_W['shopshare']['link'] = $url . "&mid=" . $member['id'];
                    if (empty($clerkset['become_reg']) && ( empty($member['realname']) || empty($member['mobile']))) {
                        $trigger = true;
                    }
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = $url . "&mid=" . $_GPC['id'];
                }
            }
        }
    }

    function perms() {
        return array(
            'coupon' => array(
                'text' => m('plugin')->getPluginName('coupon'), 'isplugin' => true,
                'child' => array(
                    'coupon' => array('text' => '优惠券', 'view' => '查看', 'add' => '添加优惠券-log', 'edit' => '编辑优惠券-log', 'delete' => '删除优惠券-log', 'send' => '发放优惠券-log'),
                    'category' => array('text' => '分类', 'view' => '查看', 'add' => '添加分类-log', 'edit' => '编辑分类-log', 'delete' => '删除分类-log'),
                    'log' => array('text' => '优惠券记录', 'view' => '查看', 'export' => '导出-log'),
                    'center' => array('text' => '领券中心设置', 'view' => '查看设置', 'save' => '保存设置-log'),
                    'set' => array('text' => '基础设置', 'view' => '查看设置', 'save' => '保存设置-log'),
                )
            )
        );
    }

    function addtaskdata($orderid) {
        global $_W;
        $pdata = m('common')->getPluginset('coupon');
        $order = pdo_fetch('select id,openid,price  from ' . tablename('elapp_shop_order') . ' where id=:id   and uniacid=:uniacid limit 1', array(':id' => intval($orderid), ':uniacid' => $_W['uniacid']));
        if(empty($order))  return;

        if($pdata['isopensendtask'] == 1)
        {
            $price = $order['price'];
            $sendtasks = pdo_fetch('select id,couponid,sendnum,num,sendpoint  from ' . tablename('elapp_shop_coupon_sendtasks') . '
             where  status =1  and uniacid=:uniacid and starttime< :now and endtime>:now and enough<=:enough   and num>=sendnum
             order by  enough desc,id  limit 1', array(':now' => time(), ':enough' => $price, ':uniacid' => $_W['uniacid']));
            if(!empty($sendtasks)) {
                $data = array(
                    'uniacid' => $_W['uniacid'],
                    'openid' => $_W['openid'],
                    'taskid' => intval($sendtasks['id']),
                    'couponid' => intval($sendtasks['couponid']),
                    'parentorderid' => 0,
                    'sendnum' => intval($sendtasks['sendnum']),
                    'tasktype' => 1,
                    'orderid' => $orderid,
                    'createtime' =>time(),
                    'status' => 0,
                    'sendpoint' => intval($sendtasks['sendpoint'])
                );
                pdo_insert('elapp_shop_coupon_taskdata', $data);
                $num = intval($sendtasks['num'])-intval($sendtasks['sendnum']);
                pdo_update('elapp_shop_coupon_sendtasks', array('num' => $num), array('id' => $sendtasks['id']));
            }
        }

        if($pdata['isopengoodssendtask'] == 1) {
            $goodssendtasks = pdo_fetchall('select  gst.starttime,gst.endtime,og.id,og.goodsid,og.orderid,og.parentorderid,og.total,gst.id as taskid,gst.couponid,gst.sendnum,gst.sendpoint,gst.num
            from '. tablename('elapp_shop_coupon_goodsendtask').' gst
            inner join ' . tablename('elapp_shop_order_goods') . ' og on og.goodsid =gst.goodsid  and (orderid=:orderid or parentorderid=:orderid)
            where  og.uniacid=:uniacid and og.openid=:openid and gst.num>=gst.sendnum and gst.status = 1',
                array( ':uniacid' => $_W['uniacid'],':openid' => $_W['openid'],':orderid' => $orderid));

            foreach($goodssendtasks as $task) {
                if ($task['starttime'] <= TIMESTAMP && $task['endtime'] >= TIMESTAMP){
                    $data = array(
                        'uniacid' => $_W['uniacid'],
                        'openid' => $_W['openid'],
                        'taskid' => intval($task['taskid']),
                        'couponid' => intval($task['couponid']),
                        'sendnum' => intval($task['total'])*intval($task['sendnum']),
                        'tasktype' => 2,
                        'orderid' => intval($task['orderid']),
                        'parentorderid' => intval($task['parentorderid']),
                        'createtime' =>time(),
                        'status' => 0,
                        'sendpoint' => intval($task['sendpoint'])
                    );
                    pdo_insert('elapp_shop_coupon_taskdata', $data);
                    $num = intval($task['num'])- intval($task['total'])*intval($task['sendnum']);
                    pdo_update('elapp_shop_coupon_goodsendtask', array('num' => $num), array('id' => $task['taskid']));
                }
            }
        }
    }

    function sendcouponsbytask($orderid) {
        global $_W;
        if (!is_array($orderid)) {
            $order = pdo_fetch('select id,openid,ordersn,createtime,status,finishtime,`virtual`,isvirtualsend,isparent,parentid  from ' . tablename('elapp_shop_order') . ' where id=:id and status>=0  and uniacid=:uniacid limit 1', array(':id' => intval($orderid), ':uniacid' => $_W['uniacid']));
        }
        if (empty($order)) {
            return;
        }
        $isonlyverifygoods = false;
        if($order['status'] == 2){
            $sql = 'SELECT goodsid FROM '.tablename('elapp_shop_order_goods').' WHERE uniacid=:uniacid AND orderid=:orderid ';
            $goodsids = pdo_fetchall($sql,array(':uniacid'=>$_W['uniacid'],':orderid'=>$order['id']));
            foreach($goodsids as $gidk => $gidv){
                $gtype = pdo_fetch('SELECT type FROM '.tablename('elapp_shop_goods').' WHERE uniacid=:uniacid AND id=:id',array(':uniacid'=>$_W['uniacid'],':id'=>$gidv['goodsid']));
                if($gtype['type'] == 5){
                    $isonlyverifygoods = true;
                }
            }
        }
        $parentid = $order['parentid'];
        $gosendtask = false;
        if ($isonlyverifygoods || $order['status'] == 1) {
            $gosendtask = true;
            $sendpoint=2;
        } else if($order['status'] == 3){
            if($parentid>0) {
                $num = pdo_fetchcolumn('select 1 from '.tablename('elapp_shop_order') .'
                where parentid =:parentid and uniacid=:uniacid  and openid=:openid  and status<>3',
                    array(':parentid' => intval($parentid), ':uniacid' => $_W['uniacid'], ':openid' => $order['openid']));

                if(empty($num)) {
                    $gosendtask = true;
                    $sendpoint=1;
                }
            }else{
                $gosendtask = true;
                $sendpoint=1;
            }
        }

        if ($gosendtask) {
            if($order['status'] == 3&&(!empty($order['isvirtualsend'])||!empty($order['virtual']))) {
                $lista=$this->getOrderSendCoupons($orderid,1,1,$order['openid']);
                $listb=$this->getOrderSendCoupons($orderid,2,1,$order['openid']);

                $list = array_merge($lista, $listb);
            } else {
                $list=$this->getOrderSendCoupons($orderid,$sendpoint,1,$order['openid']);
            }

            if(!empty($list)&&count($list)>0) {
                $this ->posterbylist($list ,$order['openid'],6);
            }
        }

        if($order['status'] == 3&&(!empty($order['isvirtualsend'])||!empty($order['virtual']))) {
            $list2a = $this->getOrderSendCoupons($orderid, 1, 2, $order['openid']);
            $list2b = $this->getOrderSendCoupons($orderid, 2, 2, $order['openid']);

            $list2 = array_merge($list2a, $list2b);
        } else {
            $list2 = $this->getOrderSendCoupons($orderid, $sendpoint, 2, $order['openid']);
        }

        if(!empty($list2)&&count($list2)>0) {
            $this ->posterbylist($list2 ,$order['openid'],6);
        }
    }

    function getOrderSendCoupons($orderid,$sendpoint,$tasktype,$openid) {
        global $_W;

        if($sendpoint ==2) {
            $taskdata = pdo_fetchall('select id, couponid, sendnum  from ' . tablename('elapp_shop_coupon_taskdata') . '
            where  status=0  and openid=:openid and uniacid=:uniacid and sendpoint=:sendpoint and tasktype=:tasktype
            and (orderid=:orderid or parentorderid=:orderid)' ,
                array( ':openid' => $openid,':uniacid' => $_W['uniacid'],':sendpoint' => $sendpoint,':tasktype' => $tasktype,':orderid' => $orderid));
        }else{
            $taskdata = pdo_fetchall('select  id, couponid, sendnum  from ' . tablename('elapp_shop_coupon_taskdata') . '
            where  status=0  and openid=:openid and uniacid=:uniacid and sendpoint=:sendpoint and tasktype=:tasktype
            and orderid=:orderid' ,
                array( ':openid' => $openid,':uniacid' => $_W['uniacid'],':sendpoint' => $sendpoint,':tasktype' => $tasktype,':orderid' => $orderid));
        }

        return $taskdata;
    }

    function poster($member, $couponid, $couponnum,$gettype=0) {
        global $_W, $_GPC;
        $pposter = p('poster');
        if (!$pposter) {
            return;
        }
        $coupon = $this->getCoupon($couponid);
        if (empty($coupon)) {
            return;
        }

        for ($i = 1; $i <= $couponnum; $i++) {
            $couponlog = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $member['openid'],
                'member_id' => $member['id'],
                'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                'couponid' => $couponid,
                'status' => 1,
                'paystatus' => -1,
                'creditstatus' => -1,
                'createtime' => time(),
                'getfrom' => $gettype
            );
            pdo_insert('elapp_shop_coupon_log', $couponlog);
            $data = array(
                'uniacid' => $_W['uniacid'],
                'openid' => $member['openid'],
                'member_id' => $member['id'],
                'couponid' => $couponid,
                'gettype' => $gettype,
                'gettime' => time()
            );
            pdo_insert('elapp_shop_coupon_data', $data);
            $this->sethasnewcoupon($member['openid'],1);
        }
        $set = m('common')->getPluginset('coupon');
        $this->sendMessage($coupon, $couponnum, $member);
    }

    function posterbylist($list ,$openid,$gettype) {
        global $_W, $_GPC;
        $num = 0;
        $showkey=random(20);
        $data = m('common')->getPluginset('coupon');
        if(empty($data['showtemplate'])||$data['showtemplate']==2) {

            $url =$this->getUrl('sale/coupon/my/showcoupons3',array('key'=>$showkey),true);
        }else{
            $url =$this->getUrl('sale/coupon/my/showcoupons',array('key'=>$showkey),true);
        }
        if (strexists($url, '/core/task/order/')) {
            $url = str_replace("/core/task/order/", '/', $url);
        }
        $member = m('member')->getMember($openid);
        foreach($list  as $taskdata) {
            $couponnum = 0;
            $couponnum = intval($taskdata['sendnum']);
            $num+=$couponnum;
            $merchid = pdo_fetchcolumn("select merchid from " . tablename('elapp_shop_coupon') . ' where id=:couponid limit 1', array(':couponid' => $taskdata['couponid']));
            for ($i = 1; $i <= $couponnum; $i++) {
                $couponlog = array(
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'member_id' => $member['id'],
                    'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                    'couponid' => $taskdata['couponid'],
                    'status' => 1,
                    'paystatus' => -1,
                    'creditstatus' => -1,
                    'createtime' => time(),
                    'getfrom' =>intval($gettype),
                    'merchid' => $merchid
                );
                pdo_insert('elapp_shop_coupon_log', $couponlog);
                $data = array(
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'member_id' => $member['id'],
                    'couponid' => $taskdata['couponid'],
                    'gettype' => intval($gettype),
                    'merchid' => $merchid,
                    'gettime' => time()
                );
                pdo_insert('elapp_shop_coupon_data', $data);
                $coupondataid=pdo_insertid();
                $this->sethasnewcoupon($openid,1);
                $data = array(
                    'showkey' => $showkey,
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'coupondataid' => $coupondataid
                );
                pdo_insert('elapp_shop_coupon_sendshow', $data);
            }

            pdo_update('elapp_shop_coupon_taskdata', array('status' => 1), array('id' => $taskdata['id']));
        }

        $msg='恭喜您获得'.$num.'张优惠券!';
        $ret = m('message')->sendCustomNotice($openid, $msg, $url);

    }


    function posterbyusesendtask($couponid,$openid) {
        global $_W, $_GPC;
        $pdata = m('common')->getPluginset('coupon');
        $member = m('member')->getMember($openid);
        if($pdata['isopenusesendtask'] == 0){
            return;
        }

        $list = pdo_fetchall('select  *  from ' . tablename('elapp_shop_coupon_usesendtasks') . '
            where  status=1  and  usecouponid= :usecouponid and uniacid=:uniacid and starttime< :now and endtime>:now   and num>=sendnum
             order by  id' , array( ':usecouponid' => $couponid,':now' => time(), ':uniacid' => $_W['uniacid']));
        if(empty($list)) return;
        $gettype=6;
        $num = 0;
        $showkey=random(20);
        $data = m('common')->getPluginset('coupon');
        if(empty($data['showtemplate'])||$data['showtemplate']==2) {
            $url =$this->getUrl('sale/coupon/my/showcoupons3',array('key'=>$showkey),true);
        }else{
            $url =$this->getUrl('sale/coupon/my/showcoupons',array('key'=>$showkey),true);
        }

        foreach($list  as $taskdata) {
            $couponnum = 0;
            $couponnum = intval($taskdata['sendnum']);
            $num+=$couponnum;
            for ($i = 1; $i <= $couponnum; $i++) {
                $couponlog = array(
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'member_id' => $member['id'],
                    'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
                    'couponid' => $taskdata['couponid'],
                    'status' => 1,
                    'paystatus' => -1,
                    'creditstatus' => -1,
                    'createtime' => time(),
                    'getfrom' =>intval($gettype)
                );

                pdo_insert('elapp_shop_coupon_log', $couponlog);
                $data = array(
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'member_id' => $member['id'],
                    'couponid' => $taskdata['couponid'],
                    'gettype' => intval($gettype),
                    'gettime' => time()
                );
                pdo_insert('elapp_shop_coupon_data', $data);
                $coupondataid=pdo_insertid();
                $this->sethasnewcoupon($openid,1);
                $data = array(
                    'showkey' => $showkey,
                    'uniacid' => $_W['uniacid'],
                    'openid' => $openid,
                    'coupondataid' => $coupondataid
                );
                pdo_insert('elapp_shop_coupon_sendshow', $data);
            }
            $num2 = intval($taskdata['num']) -intval($taskdata['sendnum']);
            pdo_update('elapp_shop_coupon_usesendtasks', array('num' => $num2), array('id' => $taskdata['id']));
        }

        $msg='恭喜您获得'.$num.'张优惠券!';
        $ret = m('message')->sendCustomNotice($openid, $msg, $url);
    }


    function getCashierCoupons($openid, $money = 0, $merchid=0){
        global $_W,$_GPC;
        $time = time();
        $param = array();
        $param[':openid'] = $openid;
        $param[':uniacid'] = $_W['uniacid'];
        $param[':merchid'] = $merchid;
        $sql = "select d.id,d.couponid,d.gettime,c.timelimit,c.timedays,c.timestart,c.timeend,c.thumb,c.couponname,c.enough,c.backtype,c.deduct,c.discount,c.backmoney,c.backcredit,c.backredpack,c.bgcolor,c.thumb,c.merchid,c.limitgoodcatetype,c.limitgoodtype,c.limitgoodcateids,c.limitgoodids,c.goodpricelimit  from " . tablename('elapp_shop_coupon_data') . " d";
        $sql.=" left join " . tablename('elapp_shop_coupon') . " c on d.couponid = c.id";
        $sql.=" where d.openid=:openid and d.uniacid=:uniacid and c.merchid=:merchid and d.merchid=:merchid  and c.coupontype=2 and d.used=0 ";
        if($money>0) {
            $sql.="and {$money}>=c.enough ";
        }
        $sql.=" and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<={$time} && c.timeend>={$time})) order by d.gettime desc";
        $list = pdo_fetchall($sql, $param);
        $list = set_medias($list, 'thumb');
        if (!empty($list)) {
            foreach ($list as &$row) {
                $row['thumb'] = tomedia($row['thumb']);
                $row['timestr'] = "永久有效";
                if (empty($row['timelimit'])) {
                    if (!empty($row['timedays'])) {
                        $row['timestr'] = date('Y-m-d H:i', $row['gettime'] + $row['timedays'] * 86400);
                    }
                } else {
                    if ($row['timestart'] >= $time) {
                        $row['timestr'] = date('Y-m-d H:i', $row['timestart']) . '-' . date('Y-m-d H:i', $row['timeend']);
                    } else {
                        $row['timestr'] = date('Y-m-d H:i', $row['timeend']);
                    }
                }
                if ($row['backtype'] == 0) {
                    $row['backstr'] = '立减';
                    $row['css'] = 'deduct';
                    $row['backmoney'] = $row['deduct'];
                    $row['backpre'] = true;
                    if($row['enough']=='0') {
                        $row['color']='org ';
                    } else {
                        $row['color']='blue';
                    }
                } else if ($row['backtype'] == 1) {
                    $row['backstr'] = '折';
                    $row['css'] = 'discount';
                    $row['backmoney'] = $row['discount'];
                    $row['color']='red ';
                } else if ($row['backtype'] == 2) {
                    if($row['coupontype']=='0') {
                        $row['color']='red ';
                    } else {
                        $row['color']='pink ';
                    }
                    if ($row['backredpack'] > 0) {
                        $row['backstr'] = '返现';
                        $row['css'] = "redpack";
                        $row['backmoney'] = $row['backredpack'];
                        $row['backpre'] = true;
                    } else if ($row['backmoney'] > 0) {
                        $row['backstr'] = '返利';
                        $row['css'] = "money";
                        $row['backmoney'] = $row['backmoney'];
                        $row['backpre'] = true;
                    } else if (!empty($row['backcredit'])) {
                        $row['backstr'] = '返积分';
                        $row['css'] = "credit";
                        $row['backmoney'] = $row['backcredit'];
                    }
                }
            }
            unset($row);
        }
        return $list;
    }

    function getCoupons() {
        global $_W;
        $time = time();
        return pdo_fetchall('select * from ' . tablename('elapp_shop_coupon') . ' where  (timelimit = 0  or  (timelimit =1 and timestart<={$time} && timeend>={$time})) and uniacid=:uniacid', array(':uniacid' => $_W['uniacid']));
    }

    function getInfo(){
        global $_W,$_GPC;
        $openid = $_W['openid'];
        if (empty($openid)) {
            return false;
        }
        $member =  m('member')->getMember($_W['openid']);
        $condition = ' WHERE uniacid = :uniacid AND openid = :openid limit 1';
        $paramso = array(
            ':uniacid' => intval($_W['uniacid']),
            ':openid' => trim($openid),
        );
        $osql = 'SELECT `id` FROM '.tablename('elapp_shop_order').$condition;
        $order = pdo_fetchall($osql,$paramso);
        if (empty($order)) {
            $sql2 = 'SELECT * FROM '.tablename('elapp_shop_sendticket').' WHERE uniacid = '.intval($_W['uniacid']);
            $ticket = pdo_fetch($sql2);
            if ($ticket['status'] == 1) {
                if ($ticket['expiration'] == 1) {
                    if (TIMESTAMP > $ticket['endtime']) {
                        $status = array('status' => 0);
                        pdo_update('elapp_shop_sendticket',$status,array('id' => $ticket['id']));
                        return false;
                    } else {
                        $cpinfo = $this -> getCoupon_new($ticket['cpid']);
                        if (empty($cpinfo)) {
                            return false;
                        } else {
                            $insert = $this -> insertDraw($openid,$cpinfo);
                            if ($insert) {
                                if(count($cpinfo) == count($cpinfo, 1)){
                                    $status = $this -> sendTicket($openid,$cpinfo['id'],14);
                                    if (!$status) {
                                        return false;
                                    } else {
                                        $cpinfo['did'] = $status;
                                    }
                                }else{
                                    foreach ($cpinfo as $cpk => $cpv) {
                                        $status = $this -> sendTicket($openid,$cpv['id'],14);
                                        if (!$status) {
                                            return false;
                                        } else {
                                            $cpinfo[$cpk]['did'] = $status;
                                        }
                                    }
                                }
                                if (count($cpinfo) == count($cpinfo, 1)) {
                                    $cpinfos[] = $cpinfo;
                                } else {
                                    $cpinfos = $cpinfo;
                                }
                                return $cpinfos;
                            } else {
                                return false;
                            }
                        }
                    }
                } else {
                    $cpinfo = $this -> getCoupon_new($ticket['cpid']);
                    if (empty($cpinfo)) {
                        return false;
                    } else {
                        $insert = $this -> insertDraw($openid,$cpinfo);
                        if ($insert) {
                            if(count($cpinfo) == count($cpinfo, 1)){
                                $status = $this -> sendTicket($openid,$cpinfo['id'],14);
                                if (!$status) {
                                    return false;
                                } else {
                                    $cpinfo['did'] = $status;
                                }
                            }else{
                                foreach ($cpinfo as $cpk => $cpv) {
                                    $status = $this -> sendTicket($openid,$cpv['id'],14);
                                    if (!$status) {
                                        return false;
                                    } else {
                                        $cpinfo[$cpk]['did'] = $status;
                                    }
                                }
                            }
                            if (count($cpinfo) == count($cpinfo, 1)) {
                                $cpinfos[] = $cpinfo;
                            } else {
                                $cpinfos = $cpinfo;
                            }
                            return $cpinfos;
                        } else {
                            return false;
                        }
                    }
                }
            } else if ($ticket['status'] == 0) {
                return false;
            }
        } else {
            return false;
        }
    }

    function getCoupon_new($cpid){
        global $_W,$_GPC;
        if (strpos($cpid,',')) {
            $cpids = explode(',',$cpid);
        } else {
            $cpids = $cpid;
        }

        if(is_array($cpids)){
            $cpinfo = array();
            foreach ($cpids as $cpk => $cpv) {
                $cpsql = 'SELECT * FROM '.tablename('elapp_shop_coupon').' WHERE uniacid = '.intval($_W['uniacid']).' AND id = '.intval($cpv);
                $list = pdo_fetch($cpsql);
                if($list['timelimit'] == 1) {
                    if (TIMESTAMP < $list['timeend']) {
                        $cpinfo[$cpk] = $list;
                    }
                }else if($list['timelimit'] == 0){
                    $cpinfo[$cpk] = $list;
                }

            }
            return $cpinfo;
        }else {
            $cpsql = 'SELECT * FROM '.tablename('elapp_shop_coupon').' WHERE uniacid = '.intval($_W['uniacid']).' AND id = '.intval($cpid);
            $cpinfo = pdo_fetch($cpsql);
            return $cpinfo;
        }
    }

    function insertDraw($openid,$cpinfo){
        global $_W,$_GPC;
        if (empty($openid)) {
            return false;
        }

        $drawsql = 'SELECT * FROM '.tablename('elapp_shop_sendticket_draw').' WHERE  openid = :openid AND uniacid = :uniacid ';
        $drawparpams = array(
            ':uniacid' => intval($_W['uniacid']),
            ':openid' => trim($openid),
        );

        $drawdata = pdo_fetch($drawsql,$drawparpams);
        if (empty($drawdata)) {
            $drawcpid = array();
            if (count($cpinfo) == count($cpinfo, 1)) {
                foreach ($cpinfo as $cpk => $cpv) {
                    $drawcpid[$cpk] = $cpv;
                }
                $drawcpids = $cpinfo['id'];
            }else{
                foreach ($cpinfo as $cpk => $cpv) {
                    $drawcpid[$cpk] = $cpv['id'];
                }
                $drawcpids = trim(implode(',',$drawcpid));

            }
            $data = array(
                'uniacid' => intval($_W['uniacid']),
                'cpid' => $drawcpids,
                'openid' => trim($openid),
                'createtime' => TIMESTAMP,
            );
            $insert = pdo_insert('elapp_shop_sendticket_draw',$data);
            return $insert;
        } else {
            return false;
        }
    }

    function sendTicket($openid, $couponid,$gettype=0) {
        global $_W, $_GPC;
        $member = m('member')->getMember($openid);
        $couponlog = array(
            'uniacid' => $_W['uniacid'],
            'openid' => $openid,
            'memberid' => $member['id'],
            'logno' => m('common')->createNO('coupon_log', 'logno', 'CC'),
            'couponid' => $couponid,
            'status' => 1,
            'paystatus' => -1,
            'creditstatus' => -1,
            'createtime' => time(),
            'getfrom' => 3
        );
        $log = pdo_insert('elapp_shop_coupon_log', $couponlog);
        $data = array(
            'uniacid' => $_W['uniacid'],
            'openid' => $openid,
            'memberid' => $member['id'],
            'couponid' => $couponid,
            'gettype' => $gettype,
            'gettime' => time()
        );
        $data = pdo_insert('elapp_shop_coupon_data', $data);
        $did = pdo_insertid();
        $this->sethasnewcoupon($openid,1);
        if ($log && $data) {
            return $did;
        } else {
            return false;
        }
    }

    function share($money){
        $activity = $this -> activity($money);
        if (!empty($activity)) {
            return true;
        }else{
            return false;
        }
    }

    function activity($money){
        global $_W;
        $sql = 'SELECT * FROM '.tablename('elapp_shop_sendticket_share').' WHERE uniacid = '.intval($_W['uniacid']).' AND status = 1 AND (enough = '.$money.' OR enough <= '.$money.') AND (expiration = 0 OR (expiration = 1 AND endtime >= '.TIMESTAMP.')) ORDER BY enough DESC,createtime DESC LIMIT 1';
        $activity = pdo_fetch($sql);
        return $activity;
    }

    function getCanGetCouponNum($merchid=0){
        global $_W;
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        $time = time();
        $param = array();
        $param[':uniacid'] = $_W['uniacid'];

        $sql = "select id,timelimit,coupontype,timedays,timestart,timeend,couponname,enough,backtype,deduct,discount,backmoney,backcredit,backredpack,bgcolor,thumb,credit,money,getmax,merchid,total as t,tagtitle,settitlecolor,titlecolor,goodpricelimit  from " . tablename('elapp_shop_coupon');
        $sql.=" where uniacid=:uniacid";
        if ($is_openmerch == 0) {
            $sql .= ' and merchid=0';
        }else {
            if (!empty($merchid)) {
                $sql .= ' and merchid=:merchid';
                $param[':merchid'] = intval($merchid);
            }
        }
        //分销商
        $plugin_com = p('commission');
        if ($plugin_com) {
            $plugin_com_set = $plugin_com->getSet();
            if(empty($plugin_com_set['level'])) {
                $sql .= ' and ( limitagentlevels = "" or  limitagentlevels is null )';
            }
        } else {
            $sql .= ' and ( limitagentlevels = "" or  limitagentlevels is null )';
        }
        //虚店店员
        $plugin_clerk = p('clerk');
        if ($plugin_clerk) {
            $plugin_clerk_set = $plugin_clerk->getSet();
            if(empty($pluginclerk_set['level'])) {
                $sql .= ' and ( limitclerklevels = "" or  limitclerklevels is null )';
            }
        } else {
            $sql .= ' and ( limitclerklevels = "" or  limitclerklevels is null )';
        }

        $plugin_globonus = p('globonus');
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            if(empty($plugin_globonus_set['open'])) {
                $sql .= ' and ( limitpartnerlevels = ""  or  limitpartnerlevels is null )';
            }
        } else {
            $sql .= ' and ( limitpartnerlevels = ""  or  limitpartnerlevels is null )';
        }

        $plugin_abonus = p('abonus');
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            if(empty($plugin_abonus_set['open']))
            {
                $sql .= ' and ( limitaagentlevels = "" or  limitaagentlevels is null )';
            }
        } else {
            $sql .= ' and ( limitaagentlevels = "" or  limitaagentlevels is null )';
        }

        $sql.=" and gettype=1 and (total=-1 or total>0) and ( timelimit = 0 or  (timelimit=1 and timeend>unix_timestamp()))";

        $sql.=" order by displayorder desc, id desc ";
        $coupons = set_medias(pdo_fetchall($sql, $param), 'thumb');
        if(empty($coupons))return 0 ;
        return count($coupons);
    }

    function sethasnewcoupon($openid,$hasnewcoupon=0){
        global $_W;
        pdo_update('elapp_shop_member', array('hasnewcoupon'=>$hasnewcoupon), array('openid'=>$openid,'uniacid'=>$_W['uniacid']));
    }


    function spec_tomedia($src, $local_path = false){
        global $_W;
        setting_load('remote');
        if (!empty($_W['setting']['remote']['type'])) {
            if ($_W['setting']['remote']['type'] == ATTACH_FTP) {
                $_W['attachurl'] = $_W['attachurl_remote'] = $_W['setting']['remote']['ftp']['url'] . '/';
            } elseif ($_W['setting']['remote']['type'] == ATTACH_OSS) {
                $_W['attachurl'] = $_W['attachurl_remote'] = $_W['setting']['remote']['alioss']['url'] . '/';
            } elseif ($_W['setting']['remote']['type'] == ATTACH_QINIU) {
                $_W['attachurl'] = $_W['attachurl_remote'] = $_W['setting']['remote']['qiniu']['url'] . '/';
            } elseif ($_W['setting']['remote']['type'] == ATTACH_COS) {
                $_W['attachurl'] = $_W['attachurl_remote'] = $_W['setting']['remote']['cos']['url'] . '/';
            }
        }

        if (empty($src)) return '';
        if (strexists($src, "c=utility&a=wxcode&do=image&attach=")) return $src;
        if (strexists($src, 'addons/')) return $_W['siteroot'] . substr($src, strpos($src, 'addons/'));
        if (strexists($src, $_W['siteroot']) && !strexists($src, '/addons/')) {
            $urls = parse_url($src);
            $src = $t = substr($urls['path'], strpos($urls['path'], 'images'));
        }
        $t = strtolower($src);
        if (strexists($t, 'https://mmbiz.qlogo.cn') || strexists($t, 'http://mmbiz.qpic.cn')) {
            $url = url('utility/wxcode/image', array('attach' => $src));
            return $_W['siteroot'] . 'web' . ltrim($url, '.');
        }
        if ((substr($t, 0, 7) == 'http://') || (substr($t, 0, 8) == 'https://') || (substr($t, 0, 2) == '//')) {
            return $src;
        }
        if ($local_path || empty($_W['setting']['remote']['type']) || file_exists(IA_ROOT . '/' . $_W['config']['upload']['attachdir'] . '/' . $src)) {
            $src = $_W['siteroot'] . $_W['config']['upload']['attachdir'] . '/' . $src;
        } else {
            $src = $_W['attachurl_remote'] . $src;
        }
        return $src;
    }
    /**
     * 统计会员同一张优惠券的数量
     * $couponid 优惠券id
     * $openid 会员openid
     * Hlei 20210616
     */
    function get_member_havcoupon_count($couponid = 0, $openid) {
        global $_W;
        $havecoupontotal = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_coupon_data') . ' where couponid=:couponid and uniacid=:uniacid  and openid=:openid ', array(':couponid' => $couponid, ':uniacid' => $_W['uniacid'],':openid' => $openid));
        return $havecoupontotal;
    }

}
