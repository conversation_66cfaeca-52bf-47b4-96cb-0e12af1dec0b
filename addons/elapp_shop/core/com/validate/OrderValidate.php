<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\validate;

use app\common\validate\BaseValidate;

/**
 * @desc 订单基础验证器类
 * Class OrderValidate
 * @package app\com\validate
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/26 10:13
 */
class OrderValidate extends BaseValidate
{
    protected $rule = [
        'order_id' => ['require', 'number'],
    ];

    protected $message = [
        'order_id.require' => 'order_id参数缺失',
    ];

    /**
     * @desc 获取订单信息验证场景
     * @return OrderValidate
     * <AUTHOR> <<EMAIL>>
     * @date 2024/1/26 10:13
     * @package app\com\validate
     */
    function sceneGetOrderInfo()
    {
        return $this->only(['order_id']);
    }
}