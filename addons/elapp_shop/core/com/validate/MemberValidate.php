<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\validate;

use app\common\validate\BaseValidate;

/**
 * @desc 会员验证器类
 * Class MemberValidate
 * @package app\com\validate
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/20 10:13
 */
class MemberValidate extends BaseValidate
{
    protected $rule = [
        'realname' => ['require', 'max' => 20, 'min' => 2],
        'idcard' => ['require' , 'length' => 18],
        'mobile' => ['require', 'mobile'],
        'idcard_front_pic' => ['require', 'url'],
        'idcard_back_pic' => ['require', 'url'],
        'sn' => ['require', 'max' => 50, 'min' => 10],
    ];

    protected $message = [
        'realname.require' => '真实姓名缺失',
        'idcard.length' => '身份证号码无效',
        'mobile.require' => '手机号码无效',
        'idcard_front_pic.url' => '身份证正面照片缺失',
        'idcard_back_pic.url' => '身份证反面照片参数缺失',
        'sn.require' => '合同编号缺失',
    ];
    function sceneUserVerify()
    {
        return $this->only(['realname', 'idcard', 'mobile', 'idcard_front_pic', 'idcard_back_pic']);
    }

    function sceneUserContractSign()
    {
        return $this->only(['realname', 'idcard', 'mobile']);
    }

    function sceneUserContractDetail()
    {
        return $this->only(['sn']);
    }

    /**
     * 验证数字不是手机号码
     * @param $number
     * @return bool
     */
    function validateNumber($number) {
        if (ctype_digit($number) && strlen($number) >= 1 && strlen($number) <= 11) {
            if (!preg_match("/^(13\d|14[579]|15[^4\D]|17[^49\D]|18\d)\d{8}$/", $number)) {
                return true;
            }
        }
        return false;
    }
}