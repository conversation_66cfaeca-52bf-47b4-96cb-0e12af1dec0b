<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\com\validate;

use app\common\validate\BaseValidate;


/**
 * 微信登录验证
 * Class WechatLoginValidate
 * @package app\api\validate
 */
class WechatLoginValidate extends BaseValidate
{
    protected $rule = [
        'i' => 'require',
        'code' => 'require|requireIf:checkNotNullUndefined',
        'nickname' => 'require',
        'headimgurl' => 'require',
        'openid' => 'require',
        'openid_wa' => 'requireIf:checkOpenid',
        'access_token' => 'require',
        'terminal' => 'require',
        'avatar' => 'require',
        'login_type' => 'require',
    ];

    protected $message = [
        'i.require' => 'i参数缺少',
        'code.require' => 'code缺少',
        'code.checkNotNullUndefined' => 'The field must not be undefined or null.',
        'nickname.require' => '昵称缺少',
        'headimgurl.require' => '头像缺少',
        'openid.require' => 'opendid缺少',
        'openid_wa.requireIf' => 'openid_wa缺少或者不等于null',
        'access_token.require' => 'access_token缺少',
        'terminal.require' => '终端参数缺少',
        'avatar.require' => '头像缺少',
        'login_type.require' => '登录类型参数缺少',
    ];

    protected function checkNotNullUndefined($value, $rule, $data = [])
    {
        if ($value === null || $value === 'undefined' || $value === 'null') {
            return false;
        }
        return true;
    }

    function checkOpenid($value, $rule = '', $data = '')
    {
        if ('phone' == $data['login_type']) {
            return !empty($value) && $value !== 'null';
        }
        return true;
    }


    /**
     * @notes 公众号登录场景
     * @return WechatLoginValidate
     * <AUTHOR>
     * @date 2024/2/1 10:57
     */
    public function sceneOa()
    {
        return $this->only(['code', 'i']);
    }


    /**
     * @notes 小程序-授权登录场景
     * @return WechatLoginValidate
     * <AUTHOR>
     * @date 2024/2/1 11:15
     */
    public function sceneMnpLogin()
    {
        return $this->only(['code', 'i', 'login_type']);
    }


    /**
     * @notes
     * @return WechatLoginValidate
     * <AUTHOR>
     * @date 2024/2/1 11:15
     */
    public function sceneWechatAuth()
    {
        return $this->only(['code', 'i']);
    }


    /**
     * @notes 更新用户信息场景
     * @return WechatLoginValidate
     * <AUTHOR>
     * @date 2023/2/22 11:14
     */
    public function sceneUpdateUser()
    {
        return $this->only(['nickname', 'avatar']);
    }


}