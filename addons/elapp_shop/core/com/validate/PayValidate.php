<?php

namespace app\com\validate;

use app\common\validate\BaseValidate;
use app\core\com\enum\payment\PayTypeEnum;

class PayValidate extends BaseValidate
{

    protected $rule = [
        'order_id' => ['require', 'number'],
        'pay_type' => ['require', 'number'],
        'openid' => 'requireIf:checkOpenid',
    ];

    protected $message = [
        'order_id.require' => 'order_id参数缺失',
        'pay_type.require' => 'pay_type参数缺失',
        'openid.requireIf' => 'openid参数缺失',
    ];

    function checkOpenid($value, $rule = '', $data = '')
    {
        if (PayTypeEnum::WECHAT_PAY == $data['pay_type']) {
            return !empty($value) || $value !== 'null';
        }
        return true;
    }

    function scenePay()
    {
        return $this->only(['order_id', 'pay_type', 'openid']);
    }
}