<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\com\service;

use app\com\cache\MemberTokenCache;
use app\core\model\member\MemberSessionModel;
use think\facade\Config;

class MemberTokenService
{

    /**
     * @notes 设置或更新用户token
     * @param $memberId
     * @param $terminal
     * @return array|false|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/12 10:10
     */
    public static function setToken($memberId, $terminal)
    {
        $time = time();
        $memberSession = MemberSessionModel::where([['member_id', '=', $memberId], ['terminal', '=', $terminal]])->find();

        //获取token延长过期的时间
        $expireTime = $time + Config::get('project.member_token.expire_duration');
        $memberTokenCache = new MemberTokenCache();

        //token处理
        if ($memberSession) {
            //清空缓存
            $memberTokenCache->deleteMemberInfo($memberSession->token);
            //重新获取token
            $memberSession->token = create_token($memberId);
            $memberSession->expire_time = $expireTime;
            $memberSession->update_time = $time;
            $memberSession->save();
        } else {
            //找不到在该终端的token记录，创建token记录
            $memberSession = MemberSessionModel::create([
                'member_id' => $memberId,
                'terminal' => $terminal,
                'token' => create_token($memberId),
                'expire_time' => $expireTime
            ]);
        }

        return $memberTokenCache->setMemberInfo($memberSession->token);
    }


    /**
     * @notes 延长token过期时间
     * @param $token
     * @return array|false|mixed
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/12 10:10
     */
    public static function overtimeToken($token)
    {
        $time = time();
        $memberSession = MemberSessionModel::where('token', '=', $token)->find();
        //延长token过期时间
        $memberSession->expire_time = $time + Config::get('project.member_token.expire_duration');
        $memberSession->update_time = $time;
        $memberSession->save();

        return (new MemberTokenCache())->setMemberInfo($memberSession->token);
    }


    /**
     * @notes 设置token为过期
     * @param $token
     * @return bool
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/12 10:10
     */
    public static function expireToken($token)
    {
        $memberSession = MemberSessionModel::where('token', '=', $token)
            ->find();
        if (empty($memberSession)) {
            return false;
        }

        $time = time();
        $memberSession->expire_time = $time;
        $memberSession->update_time = $time;
        $memberSession->save();

        return (new  MemberTokenCache())->deleteMemberInfo($token);
    }

}