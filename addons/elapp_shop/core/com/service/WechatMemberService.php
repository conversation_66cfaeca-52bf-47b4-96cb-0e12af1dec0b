<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\com\service;


use app\com\enum\member\MemberTerminalEnum;
use app\core\dao\member\MemberDao;
use app\model\MemberModel;
use think\Exception;
use think\facade\Db;
use web\model\fans\MappingFansModel;
use web\model\fans\McMemberModel;


/**
 * 用户功能类（主要微信登录后创建和更新用户）
 * Class WechatMemberService
 * @package app\com\service
 */
class WechatMemberService
{
    protected int $uniacid = 0;
    protected int $terminal = MemberTerminalEnum::WECHAT_MMP;
    protected array $response = [];
    protected ?string $code = null;
    protected ?string $openid = null;
    protected ?string $unionid = null;
    protected ?string $nickname = null;
    protected ?string $headimgurl = null;
    protected ?string $moible = null;
    protected ?string $openid_wa = null;
    protected MemberModel $member;


    public function __construct(array $response, int $terminal, int $uniacid)
    {
        $this->uniacid = $uniacid;
        $this->terminal = $terminal;
        $this->setParams($response);
    }


    /**
     * @notes 设置微信返回的用户信息
     * @param $response
     * <AUTHOR>
     * @date 2024/1.31 17:49
     */
    private function setParams($response): void
    {
        $this->response = $response;
        $this->openid = $response['openid'] ?? '';
        $this->unionid = $response['unionid'] ?? '';
        $this->nickname = $response['nickname'] ?? '';
        $this->headimgurl = $response['headimgurl'] ?? '';
        $this->moible = $response['phone_info']['purePhoneNumber'] ?? '';
        $this->openid_wa = $response['openid_wa'] ?? '';
    }


    /**
     * @notes 根据opendid或unionid获取系统用户信息
     * @return $this
     * <AUTHOR>
     * @date 2024/1/31 16:09
     */
    public function getResopnseByMemberInfo(): self
    {
        $openid = $this->openid;

        /*$member = MemberModel::withoutGlobalScope()->alias('m')
            ->join(['ims_mc_mapping_fans' => 'f'], 'm.openid = f.openid', 'left')
            ->field('m.id,m.uniacid,m.openid,m.uid,m.nickname,m.avatar,m.mobile,m.isblack,m.status')
            ->where(function ($query) use ($openid, $unionid) {
                $query->whereOr(['f.openid' => $openid]);
                if (!empty($unionid)) {
                    $query->whereOr(['f.unionid' => $unionid]);
                }
            })->findOrEmpty();
        $member = MemberModel::field('id,uniacid,openid,uid,nickname,avatar,mobile,isblack,status,comefrom,openid_wx,openid_qq,openid_wa')
            ->where(['openid' => $openid])
            ->findOrEmpty();*/
        $key = !empty($openid) ? $openid : $this->moible;
        $member = app(MemberDao::class)->getMemberDetail($key, 'id,uniacid,openid,uid,nickname,avatar,mobile,isblack,status,comefrom,openid_wx,openid_qq,openid_wa,delete_time,is_cancel_account');
        $this->member = $member;
        return $this;
    }


    /**
     * @notes 获取用户信息
     * @param bool $isCheck 是否验证账号是否可用
     * @return array
     * @throws Exception
     * <AUTHOR>
     * @date 2024/1/31 11:42
     */
    public function getMemberInfo($isCheck = true): array
    {
        if (!$this->member->isEmpty() && $isCheck) {
            $this->checkAccount();
        }
        if (!$this->member->isEmpty()) {
            $this->getToken();
        }
        return $this->member->toArray();
    }


    /**
     * @notes 校验账号 isblack,status等状态
     * @throws Exception
     * <AUTHOR>
     * @date 2024/1/31 10:14
     */
    private function checkAccount()
    {
        if ($this->member->isblack || $this->member->status != 0||$this->member->delete_time != 0) {
            throw new Exception('您的账号异常，请联系客服。');
        }
    }


    /**
     * @notes 创建用户
     * @throws Exception
     * <AUTHOR>
     * @date 2024/1/31 10:06
     * @remark todo 表数据处理未完善
     */
    private function createUser()
    {
        $data = [
            'uniacid' => $this->uniacid,
            'avatar' => $this->headimgurl,
            'nickname' => $this->nickname,
            'createtime' => time(),
            'gender' => $this->response['sex'] ?? -1,
            'mobile' => $this->moible,
        ];

        Db::startTrans();
        try {
            // 1.先写入mc_members表
            $uid = McMemberModel::insertGetId($data);
            // 2.分别给不同渠道的用户写入不同的数据
            //公众号
            if ($this->terminal == MemberTerminalEnum::WECHAT_OA) {
                $fans = MappingFansModel::where('openid', $this->openid)->find();

                if (empty($fans)) {
                    MappingFansModel::create([
                        'uniacid' => $this->uniacid,
                        'uid' => $uid,
                        'openid' => $this->openid,
                        'nickname' => $this->nickname,
                        'unionid' => $this->unionid,
                        'user_from' => $this->terminal,
                    ]);
                } else {
                    $uid = $fans->uid;
                }
                $member_data = [
                    'uid' => $uid,
                    'comefrom' => 'sns_wechat',
                    'openid' => $this->openid, // todo 此字段为公众号openid，其余加前缀标记是全局业务查询的时候主要都拿该字段作为业务关键查询字段,需处理前缀标记
                ];
            } elseif ($this->terminal == MemberTerminalEnum::WECHAT_MMP) {
                $member_data = [
                    'uid' => $uid,
                    'comefrom' => 'sns_wa',
                    'openid' => $this->openid_wa, //'sns_wa_' .
                    'openid_wa' => $this->openid_wa,
                    'mobile' => $this->moible,
                ];
            } elseif (in_array($this->terminal, [MemberTerminalEnum::ANDROID, MemberTerminalEnum::IOS])) {
                $member_data = [
                    'uid' => $uid,
                    'comefrom' => 'sns_wx',
                    'openid' => $this->openid, //'sns_wx_' .
                    'openid_wx' => $this->openid,
                ];
            }

            if ($this->terminal != MemberTerminalEnum::WECHAT_MMP && !empty($this->nickname)) {
                $member_data['nickname'] = $this->member->nickname = $this->nickname;
            }
            $this->member = $this->member->create($member_data);
            Db::commit();
        } catch (\Exception $e) {
            Db::rollback();
            throw new \Exception($e->getMessage());
        }
    }


    /**
     * @notes 更新用户信息
     * @throws Exception
     * <AUTHOR>
     * @date 2024/1/31 10:06
     * @remark todo 获取微信头像，昵称，手机号等微信信息更新
     */
    private function updateUser(): void
    {
        $this->member->nickname = $this->nickname;
        $this->member->avatar = $this->avatar;
        if (empty($this->member->openid_wa) || 'null' == $this->member->openid_wa) {
            $this->member->openid_wa = $this->openid_wa;
        }
        if (empty($this->member->openid) || 'null' == $this->member->openid) {
            $this->member->openid = $this->openid_wa;
        }
        $this->member->save();
    }


    /**
     * @notes 获取token
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     * <AUTHOR>
     * @date 2024/1/31 16:45
     */
    private function getToken(): void
    {
        $member = MemberTokenService::setToken($this->member->id, $this->terminal);
        $this->member->token = $member['token'];
        $this->member->expire_time = $member['expire_time'];
    }


    /**
     * @notes 用户授权登录，
     * 如果用户不存在，创建用户；用户存在，更新用户信息，并检查该端信息是否需要写入
     * @return WechatUserService
     * @throws Exception
     * <AUTHOR>
     * @date 2024/1/31 16:35
     */
    public function authMemberLogin(): self
    {
        if ($this->member->isEmpty()) {
            $this->createUser();
        } else {
            $this->updateUser();
        }
        return $this;
    }
}