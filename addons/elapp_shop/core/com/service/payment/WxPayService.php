<?php

namespace app\com\service\payment;

use app\com\enum\member\MemberTerminalEnum;
use app\com\service\MicroEngineService;
use app\com\service\wechat\WechatConfigService;
use app\common\logic\BaseLogic;
use EasyWeChat\Factory;
use think\facade\Event;
use think\Response;

/**
 * 微信支付
 * Class WxPayService
 * @package app\com\service\payment
 */
class WxPayService
{
    public $payment;
    public $config;
    public $uniacid;

    public $isV3PAy = false;
    public $terminal = MemberTerminalEnum::WECHAT_OA;

    function __construct(int $uniacid, $terminal = MemberTerminalEnum::WECHAT_OA)
    {
        $this->uniacid = $uniacid;
        $this->terminal = $terminal;
        $this->config = $this->getConfig($uniacid);
        $this->payment = Factory::payment($this->config);
    }

    public static function instance($terminal)
    {
        return app()->make(static::class, ['uniacid' => MicroEngineService::getUniacid(), 'terminal' => $terminal]);
    }

    function getConfig(int $uniacid)
    {
        $uniacid = $uniacid ?: MicroEngineService::getUniacid();
        if (empty($uniacid)) {
            throw new \Exception('缺少uniacid参数');
        }
        $config = (new WechatConfigService())->getPayConfig($this->terminal);
        return $config;
    }

    /**
     * @desc 统一支付 H5 支付，公众号支付，扫码支付，支付中签约
     * @param string $openid 用户openid
     * @param string $out_trade_no 商户订单号
     * @param int $total_fee 标价金额
     * @param string $body 商品描述
     * @param string $notify_url 通知地址
     * @param string $jssdk_type 发起支付的方式 WeixinJSBridge, JSSDK, MiniProgram
     * @param string $attach 附加数据
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    function unify(string $out_trade_no, int $total_fee, string $body, string $trade_type, array $attach = [], string $jssdk_type = 'JSSDK')
    {
        $params = [
            'trade_type'   => $trade_type,
            'body' => $body,
            'out_trade_no' => $out_trade_no,
            'total_fee' => $total_fee,
        ] + $attach;
        $result =  $this->payment->order->unify($params);

        BaseLogic::logger('微信支付发起接口', $params, $notify);
        if ($result['return_code'] == 'SUCCESS' && $result['result_code'] == 'SUCCESS') {
            if ('WeixinJSBridge' == $jssdk_type) {
                return $this->payment->jssdk->bridgeConfig($result['prepay_id']); //默认返回json
            }
            if ('MiniProgram' == $jssdk_type) {
                return $this->payment->jssdk->bridgeConfig($result['prepay_id'], false);
            }
            return $this->payment->jssdk->sdkConfig($result['prepay_id']);
        }
        if ($result['return_code'] == 'FAIL' && array_key_exists('return_msg', $result)) {
            return result(8021, $result['return_msg']);
        }
        return result(8021, $result['err_code_des']);
    }

    /**
     * @desc 根据商户订单号查询
     * @param string $out_trade_no
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    function queryByOutTradeNumber(string $out_trade_no)
    {
        return $this->payment->order->queryByOutTradeNumber($out_trade_no);
    }

    /**
     * @desc 根据商户订单号退款
     * @param string $out_trade_no 商户订单号、
     * @param string $refund_trade_no 商户退款单号、
     * @param int $total_fee 订单金额、
     * @param int $refund_fee 退款金额、
     * @param array $config 其他参数 eg: ['refund_desc' => '退运费']
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     */
    function byOutTradeNumber(string $out_trade_no, string $refund_trade_no, int $total_fee, int $refund_fee, array $config  = [])
    {
        return $this->payment->refund->byOutTradeNumber($out_trade_no, $refund_trade_no, $total_fee, $refund_fee, $config);
    }

    /**
     * @desc 发送红包 集成普通红包，裂变红包，小程序红包
     * @param array $redpack_data
     * ['mch_billno'   => 'xy123456',
     * 'send_name'    => '测试红包',
     * 're_openid'    => 'oxTWIuGaIt6gTKsQRLau2M0yL16E',
     * 'total_num'    => 1,  //normal 固定为1，可不传 group-大于3，miniprogram-无此参数
     * 'total_amount' => 100]
     * @param $type 红包类型 normal-普通红包，group-裂变红包，miniprogram-小程序红包
     * @return array|\EasyWeChat\Kernel\Support\Collection|object|\Psr\Http\Message\ResponseInterface|string
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * @throws \GuzzleHttp\Exception\GuzzleException
     */
    function sendRedPackets(array $redpack_data, $type = 'normal')
    {
        //裂变红包
        if ($type == 'group') {
            return $this->payment->redpack->sendGroup($redpack_data);
        }
        //小程序红包
        if ($type == 'miniprogram') {
            return $this->payment->redpack->sendMiniprogramNormal($redpack_data);
        }
        //普通红包
        return $this->payment->redpack->sendNormal($redpack_data);
    }

    /**
     * @desc 返回共享收货地址所需的配置
     * @param string $token
     * @return array|string
     */
    function shareAddress(string $token)
    {
        return $this->payment->jssdk->shareAddressConfig($token);
    }

    /**
     * 微信支付成功回调接口
     * @return Response
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    public function handleNotify()
    {
        if ($this->isV3PAy) {
            $response = $this->payment->v3pay->handleNotify(function ($notify, $success) {
                BaseLogic::logger('微信支付成功回调接口', [], $notify);

                if (isset($notify['out_trade_no']) && $success) {
                    $res = Event::until('pay.notify', [$notify]);
                    if ($res) {
                        return $res;
                    } else {
                        return false;
                    }
                }

            });
        } else {
            $response = $this->payment->handlePaidNotify(function ($notify, $fail) {
                BaseLogic::logger('微信支付成功回调接口', [], $notify);

                if (isset($notify['out_trade_no'])) {
                    $notify['terminal'] = $this->terminal;
                    $res = Event::until('pay.notify', [$notify]);
                    if ($res) {
                        return $res;
                    } else {
                        return $fail('支付通知失败');
                    }
                }
            });
        }

        return response($response->getContent());
    }

    /**
     * 退款结果通知
     * @return Response
     * @throws \EasyWeChat\Kernel\Exceptions\Exception
     */
    function handleRefundedNotify()
    {
        $response = $this->payment->handleRefundedNotify(function ($message, $reqInfo, $fail) {

            $res = Event::until('pay.refunded.notify', [$message, $reqInfo]);
            if ($res) {
                return $res;
            } else {
                return $fail('退款结果通知失败');
            }
        });

        return response($response->getContent());
    }
}