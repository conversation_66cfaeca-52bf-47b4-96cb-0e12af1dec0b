<?php

namespace app\com\service\payment;

use app\common\logic\BaseLogic;
use app\core\com\enum\payment\PayTypeEnum;
use app\model\MemberModel;
use app\order\logic\OrderLogic;
use think\exception\ValidateException;

class BalancePayService extends BaseLogic
{
    /*
     * 余额支付（含冻结余额）
     */
    function OrderPay(array $orderInfo, $mid)
    {
        $orderInfo['freeze_pay'] = $fee = $orderInfo['price'];
        $memberModel = app(MemberModel::class);
        $credits = $memberModel->getCredits($mid, ['credit2', 'credit5']);
        if (array_sum($credits) < $fee) {
            return result(8001, '余额不足,请充值');
        }

        //$this->transaction(function () use ($mid, $orderInfo, $fee, $credits, $memberModel) { //暂时无法添加事务处理，以前的旧逻辑有影响
            $unpaid = $credits['credit5'] - $fee;
            if ($unpaid < 0) {
                $credits['credit2'] -= abs($unpaid);
                //冻结余额大于0时用credit5支付
                if ($credits['credit5'] > 0) {
                    $orderInfo['freeze_pay'] = $credits['credit5'];
                    $credit5_result = $memberModel->setCredit($mid, 'credit5', -$credits['credit5'], array($mid, '消费冻结金：' . $credits['credit5'] . ',订单号:' . $orderInfo['ordersn'] . ',订单金额:' . $fee));
                }
                $result = $memberModel->setCredit($mid, 'credit2', $unpaid, array($mid, '消费：' . abs($unpaid) . ',订单号:' . $orderInfo['ordersn'] . ',订单金额:' . $fee));
            } else {
                $result = $memberModel->setCredit($mid, 'credit5', -$fee, array($mid, '消费冻结金：' . $fee . ',订单号:' . $orderInfo['ordersn']));
            }
            $res = app(OrderLogic::class)->paySuccess($orderInfo, PayTypeEnum::BALANCE_PAY);
            if (!$res) {
                throw new ValidateException('余额支付失败!');
            }
        //});
        return result(0, '支付成功');
    }
}