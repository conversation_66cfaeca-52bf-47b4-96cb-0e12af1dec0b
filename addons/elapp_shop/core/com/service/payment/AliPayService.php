<?php

namespace app\com\service\payment;

use app\com\service\MicroEngineService;
use think\facade\Config;
use Yansongda\Pay\Pay;

class AliPayService
{
    public $config;
    public $payment;
    function __construct(int $uniacid)
    {
        $this->config = $this->getConfig($uniacid);
        $this->payment = Pay::alipay($this->config);
    }

    function getConfig(int $uniacid)
    {
        $uniacid = $uniacid ?: MicroEngineService::getUniacid();
        if (empty($uniacid)) {
            throw new \Exception('缺少uniacid参数');
        }
        $pay_config  = Config::get('pay');
        //读取系统设置覆盖alipay配置
        return $pay_config;
    }

    /**
     * @desc H5支付
     * @param string $out_trade_no 订单号
     * @param string $subject 商品名称|订单标题
     * @param float $total_amount 订单金额
     * @param string $quit_url 退出支付返回网站的地址
     * @param string $return_url 同步通知跳转的地址
     * @param string $notify_url 异步通知地址
     * @return mixed
     */
    function h5(string $out_trade_no, string $subject, float $total_amount, string $quit_url = '', string $return_url = '', string $notify_url = '')
    {
        return $this->payment->h5([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject,
            'quit_url' => $quit_url,
            'return_url' => $return_url,
            'notify_url' => $notify_url
        ]);
    }

    /**
     * @desc 网页支付
     * @param string $out_trade_no
     * @param string $subject
     * @param float $total_amount
     * @param string $request_from_url
     * @return \Psr\Http\Message\ResponseInterface
     */
    function web(string $out_trade_no, string $subject, float $total_amount, string $request_from_url = '')
    {
        return $this->payment->web([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject,
            'request_from_url' => $request_from_url
        ]);
    }

    /**
     * @desc app支付
     * @param string $out_trade_no
     * @param string $subject
     * @param float $total_amount
     * @return \Psr\Http\Message\ResponseInterface
     */
    function app(string $out_trade_no, string $subject, float $total_amount)
    {
        return $this->payment->app([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject
        ]);
    }

    /**
     * @desc 小程序支付
     * @param string $out_trade_no
     * @param string $subject
     * @param float $total_amount
     * @param string $buyer_id
     * @return \Yansongda\Supports\Collection
     */
    function mini(string $out_trade_no, string $subject, float $total_amount, string $buyer_id)
    {
        return $this->payment->mini([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject,
            'buyer_id' => $buyer_id
        ]);
    }

    /**
     * @desc 刷卡支付
     * @param string $out_trade_no
     * @param string $subject
     * @param float $total_amount
     * @param string $auth_code
     * @return \Yansongda\Supports\Collection
     */
    function pos(string $out_trade_no, string $subject, float $total_amount, string $auth_code)
    {
        return $this->payment->pos([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject,
            'auth_code' => $auth_code
        ]);
    }

    /**
     * @desc 扫码支付
     * @param string $out_trade_no
     * @param string $subject
     * @param float $total_amount
     * @return \Yansongda\Supports\Collection
     */
    function scan(string $out_trade_no, string $subject, float $total_amount)
    {
        return $this->payment->scan([
            'out_trade_no' => $out_trade_no,
            'total_amount' => $total_amount,
            'subject' => $subject
        ]);
    }

    /**
     * @desc 转账到支付宝账户
     * @param string $out_biz_no
     * @param string $trans_amount
     * @param array $payee_info
     * @param string $product_code
     * @param string $biz_scene
     * @return \Yansongda\Supports\Collection
     */
    function transfer(string $out_biz_no, string $trans_amount, array $payee_info, string $product_code = 'TRANS_ACCOUNT_NO_PWD', string $biz_scene = 'DIRECT_TRANSFER')
    {
        return $this->payment->transfer([
            'out_biz_no' => $out_biz_no,
            'trans_amount' => $trans_amount,
            'payee_info' => $payee_info,
            'product_code' => $product_code,
            'biz_scene' => $biz_scene
        ]);
    }

    /**
     * @desc 退款
     * @param string $out_trade_no
     * @param string $refund_amount
     * @param string $action 'agreement':商家收款退款, 'authorization': 预授权退款, 'transfer': 转账退款
     * @param string $refund_reason
     * @return array|\Psr\Http\Message\MessageInterface|\Yansongda\Supports\Collection|null
     * @throws \Yansongda\Pay\Exception\ContainerException
     * @throws \Yansongda\Pay\Exception\InvalidParamsException
     * @throws \Yansongda\Pay\Exception\ServiceNotFoundException
     */
    function refund(string $out_trade_no, string $refund_amount, string $action = 'agreement', string $refund_reason = '')
    {
        return $this->payment->refund([
            'out_trade_no' => $out_trade_no,
            'refund_amount' => $refund_amount,
            '_action' => $action,
            'refund_reason' => $refund_reason
        ]);
    }

    /**
     * @desc 订单查询[收款，退款，转账]
     * @param string $out_trade_no
     * @param string $action agreement': 商家收款 'authorization': 预授权 transfer': 转账 'face': 刷脸结果信息 'transfer': 转账 'refund': 退款
     * 'web': 网页订单 'app':  APP 订单 'mini': 小程序订单 'pos': 刷卡订单 'scan': 扫码订单 'h5':  H5 订单
     * 'refund': 退款网页订单 'refund_web': 退款网页订单 'refund_app':  APP 退款订单 'refund_mini': 小程序退款订单 'refund_pos': 刷卡退款订单 'refund_scan': 扫码退款订单 'refund_h5':  H5 退款订单
     * @param string $out_request_no 退款单号
     * @return mixed
     */
    function query(string $out_trade_no, string $action = 'h5', string $out_request_no = '')
    {
        $order = [
            'out_trade_no' => $out_trade_no,
            '_action' => $action
        ];
        if (!empty($out_request_no)) {
            $order['out_request_no'] = $out_request_no;
        }
        return $this->payment->query($order);
    }

    /**
     * @desc 关闭订单
     * @param string $out_trade_no
     * @param string $action 'web': 网页订单 'app':  APP 订单 'mini': 小程序订单 'pos': 刷卡订单 'scan': 扫码订单 'h5':  H5 订单
     * @return array|\Psr\Http\Message\MessageInterface|\Yansongda\Supports\Collection|null
     * @throws \Yansongda\Pay\Exception\ContainerException
     * @throws \Yansongda\Pay\Exception\InvalidParamsException
     * @throws \Yansongda\Pay\Exception\ServiceNotFoundException
     */
    function colse(string $out_trade_no, string $action = 'h5')
    {
        return $this->payment->close([
            'out_trade_no' => $out_trade_no,
            '_action' => $action
        ]);
    }

    /**
     * @desc 取消订单
     * @param string $out_trade_no 订单号
     * @param string $action
     * @return array|\Psr\Http\Message\MessageInterface|\Yansongda\Supports\Collection|null
     * @throws \Yansongda\Pay\Exception\ContainerException
     * @throws \Yansongda\Pay\Exception\InvalidParamsException
     * @throws \Yansongda\Pay\Exception\ServiceNotFoundException
     */
    function cancel(string $out_trade_no, string $action = 'h5')
    {
        return $this->payment->cancel([
            'out_trade_no' => $out_trade_no,
            '_action' => $action
        ]);
    }
}