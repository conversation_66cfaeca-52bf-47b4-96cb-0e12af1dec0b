<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\service\wechat;

use app\core\model\setting\SyssetModel;
use app\core\model\wechat\PaymentModel;
use web\model\account\AccountWechatModel;
use web\model\account\AccountWxappModel;

/**
 * Class WechatConfigService
 * @package app\com\service\wechat
 * @desc 微信配置服务
 * <AUTHOR>
 * @date 2024/1/19 10:41
 */
class WechatConfigService
{
    public $config;

    function __construct($config = [])
    {
        if (empty($config)) {
            $default = env('APP_DEBUG', true) ? 'dev' : 'prod';
            $config = [
                'response_type' => 'array',
                'log' => [
                    'default' => $default, // 默认使用的 channel，生产环境可以改为下面的 prod
                    'channels' => [
                        // 测试环境
                        'dev' => [
                            'driver' => 'single',
                            'path' => runtime_path('log/wechat/' . date('Ym')) .  date('d') . '.log',
                            'level' => 'debug',
                        ],
                        // 生产环境
                        'prod' => [
                            'driver' => 'daily',
                            'path' => runtime_path('log/wechat/' . date('Ym')) .  date('d') . '.log',
                            'level' => 'info',
                        ],
                    ],
                ]
            ];
        }
        $this->config = $config;
    }
    /**
     * @desc 获取小程序配置
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function getMiniProgramConfig(int $uniacid) {
        global $_W;
        $mini_program_config = $this->config;
        if (empty($_W['account']['key']) || empty($_W['account']['secret'])) {
            $account_wxapp = (new AccountWxappModel())->getAccountWxappByUniacid($uniacid, ['key', 'secret']);
            $mini_program_config += [
                'app_id' => $account_wxapp['key'],
                'secret' => $account_wxapp['secret'],
            ];
        } else {
            $mini_program_config += [
                'app_id' => $_W['account']['key'],
                'secret' => $_W['account']['secret'],
            ];
        }

        return $mini_program_config;
    }

    /**
     * @desc 获取公众号配置
     * @param int $uniacid
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * <AUTHOR>
     * @date 2024/1/19 10:56
     * @package app\com\service\wechat
     */
    public function getOfficialAccountConfig(int $uniacid) {
        $official_account_config = $this->config;
        $account_wechat = (new AccountWechatModel())->getAccountWechatByUniacid($uniacid, ['key', 'secret']);
        $official_account_config += [
            'app_id' => $account_wechat['key'],
            'secret' => $account_wechat['secret'],
        ];

        return $official_account_config;
    }

    /**
     * 获取微信支付配置，后台管理的支付配置设计很混乱....
     * 按照$terminal【mini-1,wechat-2,3,app-5,6】区分读取不同应用支付配置
     * @return array|false|mixed
     */
    public function getPayConfig($terminal) {
        $payment_config = $this->config;
        $sets = app(SyssetModel::class)->getSysSet('sets', ['app', 'pay']);
        $sec = app(SyssetModel::class)->getSysSet('sec', ['wxapp', 'app_wechat']);
        switch ($terminal) {
            case 1: //小程序
                $payment_config += [
                    'app_id' => $sets['app']['appid'],
                    'mch_id' => $sec['wxapp']['mchid'],
                    'key' => $sec['wxapp']['apikey'],
                    'notify_url' => $sec['wxapp']['notify_url'] ?? '',
                    'cert_path' => $sec['wxapp']['cert_file_path'] ?? '',
                    'key_path' => $sec['wxapp']['key_file_path'] ?? '',
                ];
                break;
            case 2: //公众号
                if (!empty($sets['pay']['weixin_id'])) { // 优先设置读取weixin_id的
                    $payment = PaymentModel::where(['id' => $sets['pay']['weixin_id']])->findOrEmpty()->toArray();
                    if (empty($payment)) {
                        throw new \Exception('未找到微信支付配置');
                    }
                    $wxpay_sec = json_decode($payment['wxpay_sec'], true);
                    $payment_config += [
                        'app_id' => $payment['sub_appid'],
                        'mch_id' => $payment['sub_mch_id'],
                        'key' => $payment['apikey'],
                        'notify_url' => $wxpay_sec['notify_url'] ?? '',
                        'cert_path' => $wxpay_sec['cert_file_path'] ?? '',
                        'key_path' => $wxpay_sec['key_file_path'] ?? '',
                    ];
                }
                break;
            case 6: //App
                $payment_config += [
                    'app_id' => $sec['app_wechat']['appid'],
                    'mch_id' => $sec['app_wechat']['merchid'],
                    'key' => $sec['app_wechat']['apikey'],
                    'notify_url' => $sec['app_wechat']['notify_url'] ?? '',
                    'cert_path' => $sec['app_wechat']['cert_file_path'] ?? '',
                    'key_path' => $sec['app_wechat']['key_file_path'] ?? '',
                ];
                break;
        }

        return $payment_config;
    }
}