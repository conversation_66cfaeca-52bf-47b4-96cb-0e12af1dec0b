<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\service\wechat;

use app\com\service\MicroEngineService;
use EasyWeChat\Factory;

/**
 * Class MiniProgramService
 * @package app\com\service\wechat
 * @desc 小程序服务类
 * <AUTHOR> <<EMAIL>>
 * @date 24/1/19 15:33
 */
class MiniProgramService
{
    public $miniProgram;
    public $config;
    public $uniacid;

    public function __construct(int $uniacid) {
        $this->uniacid = $uniacid ?: MicroEngineService::getUniacid();
        if (empty($this->uniacid)) {
            throw new \Exception('缺少uniacid参数');
        }
        $this->config = $this->getConfig($this->uniacid);
        $this->miniProgram = Factory::miniProgram($this->config);
    }

    /**
     * @return array
     * @throws \think\db\exception\DataNotFoundException
     * <AUTHOR>
     * @date 24/1/19 15:33
     */
    function getConfig(int $uniacid)
    {
        $config = (new WechatConfigService())->getMiniProgramConfig($uniacid);
        if (empty($config['app_id']) || empty($config['secret'])) {
            throw new \Exception('请先设置小程序配置');
        }
        return $config;
    }

    /**
     * @desc 小程序登陆
     * @param string $code
     * @return array
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidConfigException
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function getMnpResByCode(string $code) {
        return $this->miniProgram->auth->session($code);
    }

    /**
     * @desc 获取小程序码
     * @param int $scene
     * @param string $page
     * @param int $width
     * @return array
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function getQrCode(int $scene, string $page, int $width = 430): array {
        return $this->miniProgram->app_code->getUnlimit($scene, $page, $width);
    }

    /**
     * @desc 获取小程序二维码
     * @param int $scene
     * @param string $page
     * @param int $width
     * @return array
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function getQrCode2(int $scene, string $page, int $width = 430): array {
        return $this->miniProgram->app_code->get($scene, $page, $width);
    }

    /**
     * @desc 消息解密
     * @param string $encryptedData
     * @param string $iv
     * @param string $sessionKey
     * @return array
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function decryptData(string $encryptedData, string $iv, string $sessionKey): array {
        return $this->miniProgram->encryptor->decryptData($encryptedData, $iv, $sessionKey);
    }

    /**
     * @desc 获取手机号
     * @param string $code
     * @return array
     * <AUTHOR>
     * @date 2024/1/19 10:43
     */
    public function getPhoneNumber(string $code): array {
        return $this->miniProgram->phone_number->getUserPhoneNumber($code);
    }
}