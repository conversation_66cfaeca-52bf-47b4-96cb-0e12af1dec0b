<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------
declare(strict_types=1);

namespace app\com\service\wechat;

use app\com\service\MicroEngineService;
use EasyWeChat\Factory;
use EasyWeChat\Kernel\Exceptions\Exception;

/**
 * Class OfficialAccountService
 * @desc 公众号服务类
 * @package app\com\service\wechat
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/18 17:56
 */
class OfficialAccountService
{
    public $officialAccount;
    public $config;
    public $uniacid;

    public function __construct(int $uniacid) {
        $this->uniacid = $uniacid;
        $this->config = $this->getConfig($uniacid);
        $this->officialAccount = Factory::officialAccount($this->config);
    }

    /**
     * @notes easywechat服务端
     * @return \EasyWeChat\Kernel\Contracts\Server|\EasyWeChat\OfficialAccount\Server
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * @throws \ReflectionException
     * @throws \Throwable
     * @date 2024/2/1 14:22
     */
    public function getServer()
    {
        return $this->app->getServer();
    }

    /**
     * @desc 获取公众号配置
     * @return array
     * @throws \Exception
     * <AUTHOR>
     * @date 2024/1/19 14:43
     */
    function getConfig(int $uniacid) {
        $uniacid = $uniacid ?: MicroEngineService::getUniacid();
        if (empty($uniacid)) {
            throw new \Exception('缺少uniacid参数');
        }
        $this->uniacid = $uniacid;
        $config = (new WechatConfigService())->getOfficialAccountConfig($uniacid);
        if (empty($config['app_id']) || empty($config['secret'])) {
            throw new \Exception('请先设置公众号配置');
        }
        return $config;
    }

    /**
     * @notes 公众号-根据code获取微信信息
     * @param string $code
     * @return mixed
     * @throws Exception
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * <AUTHOR>
     * @date 2024/2/1 11:04
     */
    public function getOaResByCode(string $code)
    {
        $response = $this->officialAccount->oauth
            ->scopes(['snsapi_userinfo'])
            ->userFromCode($code)
            ->getRaw();

        if (!isset($response['openid']) || empty($response['openid'])) {
            throw new Exception('获取openID失败');
        }

        return $response;
    }

    /**
     * @notes 公众号跳转url
     * @param string $url
     * @return mixed
     * @throws \EasyWeChat\Kernel\Exceptions\InvalidArgumentException
     * <AUTHOR>
     * @date 2024/2/1 10:35
     */
    public function getCodeUrl(string $url)
    {
        return $this->officialAccount->oauth
            ->scopes(['snsapi_userinfo'])
            ->redirect($url);
    }

}