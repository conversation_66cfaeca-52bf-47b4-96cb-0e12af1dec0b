<?php

namespace app\core\com\service\address;
use app\services\AddressService;
use think\db\exception\DbException;
use think\facade\Db;

class AddressConvertService
{
    /**
     * 转换用户地址,主要是将用户输入的地址转换为高德地图的地址
     * @param array $userAddress
     * @param int $member_id 会员信息
     * @param int $uniacid
     * @param int $isdefault
     * @return array 转换后的地址数据
     * @throws DbException
     */
    function convertAddress(array $userAddress, int $member_id, int $uniacid, int $isdefault = 0): array
    {
        // 调用地址服务
        $addressService = new AddressService();
        $addressData = $addressService->convertAddress($userAddress);
        // 校验是否成功
        if ($addressData['code'] !== 200) {
            return result(400, $addressData['msg'], []);
        }

        $member = m('member')->getMember($member_id);

        // 添加用户信息
        $addressData['data'] = array_merge($addressData['data'], [
            'uniacid' => $uniacid, // 系统标识
            'openid' => $member['openid'] ?? '', // 用户标识
            'member_id' => $member_id, // 会员ID,
            'zipcode' => '', // 邮编
            'isdefault' => $isdefault, // 是否默认地址
            'deleted' => 0, // 是否删除
        ]);

        // 校验重复地址
        $addressCount = Db::name('member_address')->where(['member_id' => $member_id, 'realname' => $addressData['data']['realname'], 'mobile' => $addressData['data']['mobile'], 'address' => $addressData['data']['address'], 'deleted' => 0])->count();
        if ($addressCount > 0) {
            // 查询已经存在的默认地址
            $defaultAddress = Db::name('member_address')->where(['member_id' => $member_id, 'isdefault' => 1, 'deleted' => 0])->find();
            if ($defaultAddress) {
                return result(40001, '地址已存在,请勿重复添加', $defaultAddress);
            } else {
                return result(400, '地址已存在,请勿重复添加', []);
            }
        }

        // 如果是默认地址,则更新其他地址的默认状态
        if ($isdefault == 1) {
            try {
                Db::name('member_address')->where(['member_id' => $member_id, 'isdefault' => 1, 'deleted' => 0])
                    ->update(['isdefault' => 0]);
            } catch (\Exception $e) {
                return result(400, $e->getMessage(), []);
            }
        }

        // 插入数据库
        try{
            Db::name('member_address')->insert($addressData['data']);
            // 插入成功后获取id
            $addressData['data']['id'] = Db::name('member_address')->getLastInsID();
        }catch (\Exception $e){
            return result(400, $e->getMessage(), []);
        }

        // 返回成功结果
        return result(200, '地址添加成功', $addressData['data']);
    }
}