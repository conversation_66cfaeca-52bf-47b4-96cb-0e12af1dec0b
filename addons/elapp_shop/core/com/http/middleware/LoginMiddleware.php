<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

declare (strict_types=1);

namespace app\com\http\middleware;


use app\com\cache\MemberTokenCache;
use app\com\service\MemberTokenService;
use app\common\service\JsonService;
use think\facade\Config;

class LoginMiddleware
{
    /**
     * @notes 登录验证
     * @param $request
     * @param \Closure $next
     * @return mixed|\think\response\Json
     * <AUTHOR>
     * @date 2024/1/8 17:33
     */
    public function handle($request, \Closure $next)
    {
        $token = $request->header('token');
        //判断接口是否免登录
        $isNotNeedLogin = $request->controllerObject->isNotNeedLogin();

        //不直接判断$isNotNeedLogin结果，使不需要登录的接口通过，为了兼容某些接口可以登录或不登录访问
        if (empty($token) && !$isNotNeedLogin) {
            //没有token并且该地址需要登录才能访问, 指定show为0，前端不弹出此报错
            return JsonService::fail('请求参数缺token', [], -1001, 0);
        }

        $memberInfo = (new MemberTokenCache())->getMemberInfo($token);

        if (empty($memberInfo) && !$isNotNeedLogin) {
            //token过期无效并且该地址需要登录才能访问
            return JsonService::fail('登录超时，请重新登录', [], -1000, 0);
        }

        //token临近过期，自动续期
        if ($memberInfo) {
            //获取临近过期自动续期时长
            $beExpireDuration = Config::get('project.user_token.be_expire_duration');
            //token续期
            if (time() > ($memberInfo['expire_time'] - $beExpireDuration)) {
                $result = MemberTokenService::overtimeToken($token);
                //续期失败（数据表被删除导致）
                if (empty($result)) {
                    return JsonService::fail('登录过期', [], -1000);
                }
            }
        }

        //给request赋值，用于控制器
        $request->memberInfo = $memberInfo;
        $request->memberId = $memberInfo['member_id'] ?? 0;

        return $next($request);
    }

}