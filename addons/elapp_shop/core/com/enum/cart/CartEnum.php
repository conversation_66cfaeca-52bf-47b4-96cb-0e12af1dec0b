<?php

namespace app\core\com\enum\cart;

/**
 * 购物车枚举类
 * @package app\com\enum\cart
 * <AUTHOR>
 * @version 1.0
 * @date 2024-07-29
 */
class CartEnum
{
    const CART_TYPE_1 = 1;// 主商城购物车
    const CART_TYPE_2 = 2;// 快速购买购物车
    const CART_TYPE_3 = 3;// 兑换中心购物车
    const CART_TYPE_4 = 4;// 积分商城购物车

    const ALL_CART = [
        self::CART_TYPE_1,
        self::CART_TYPE_2,
        self::CART_TYPE_3,
        self::CART_TYPE_4,
    ];

    /**
     * @notes 获取购物车类型名称
     * @param bool|int $asName 参数可以是布尔值或整数类型
     * @return string|string[] 如果参数是布尔值，返回包含状态名称的关联数组；如果参数是整数类型，则返回对应状态的字符串。
     * <AUTHOR>
     * @date 2024/04/08 17:02
     */
    public static function getCartName($asName = true)
    {
        $name = [
            self::CART_TYPE_1 => '主商城购物车',
            self::CART_TYPE_2 => '快速购买购物车',
            self::CART_TYPE_3 => '兑换中心购物车',
            self::CART_TYPE_4 => '积分商城购物车',
        ];
        if (true === $asName) {
            return $name;// 返回包含状态名称的关联数组
        }
        return $desc[$asName] ?? '';// 返回对应状态名称的字符串
    }
}