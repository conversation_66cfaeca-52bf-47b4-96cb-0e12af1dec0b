<?php

namespace app\core\com\enum\common;

/**
 * 系统模块类型枚举
 * Class ModuleTypeEnum
 * @package app\core\com\enum\common
 * 0主商城 1积分商城 2秒杀 后续可扩展
 */
class ModuleTypeEnum
{
    const SHOP = 0;     // 主商城
    const CREDITSHOP = 1;    // 积分商城
    const SECKILL = 2;       // 秒杀

    const ALL_MODULE_TYPE = [
        self::SHOP,
        self::CREDITSHOP,
        self::SECKILL
    ];

    /**
     * @notes 获取模块类型名称
     * @param bool|int $asName 参数可以是布尔值或整数类型
     * @return string|string[] 如果参数是布尔值，返回包含模块名称的关联数组；如果参数是整数类型，则返回对应名称的字符串。
     * <AUTHOR>
     * @date 2025/04/19 04:45
     */
    public static function getModuleTypeName($asName = true)
    {
        $name = [
            self::SHOP => '商城',
            self::CREDITSHOP   => '积分商城',
            self::SECKILL   => '秒杀'
        ];
        if (true === $asName) {
            return $name;// 返回包含状态名称的关联数组
        }
        return $desc[$asName] ?? '';// 返回对应状态名称的字符串
    }
}