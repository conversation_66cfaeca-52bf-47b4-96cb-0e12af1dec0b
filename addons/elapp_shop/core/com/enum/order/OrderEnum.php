<?php

namespace app\com\enum\order;

/**
 * 订单枚举类
 * @package app\com\enum\order
 * <AUTHOR> <<EMAIL>>
 */
class OrderEnum
{
    const ORDER_STATUS_0_1 = -1;// 已关闭
    const ORDER_STATUS_0   = 0;// 待付款
    const ORDER_STATUS_1   = 1;// 待发货
    const ORDER_STATUS_2   = 2;// 待收货
    const ORDER_STATUS_3   = 3;// 已完成
    const ORDER_STATUS_4   = 4;// 维权申请
    const ORDER_STATUS_5   = 5;// 维权完成

    const ALL_STATUS = [
        self::ORDER_STATUS_0_1,
        self::ORDER_STATUS_0,
        self::ORDER_STATUS_1,
        self::ORDER_STATUS_2,
        self::ORDER_STATUS_3,
        self::ORDER_STATUS_4,
        self::ORDER_STATUS_5
    ];

    /**
     * @notes 获取订单状态名称
     * @param bool|int $asName 参数可以是布尔值或整数类型
     * @return string|string[] 如果参数是布尔值，返回包含状态名称的关联数组；如果参数是整数类型，则返回对应状态的字符串。
     * <AUTHOR>
     * @date 2024/04/08 17:02
     */
    public static function getStatusName($asName = true)
    {
        $name = [
            self::ORDER_STATUS_0_1 => '已关闭',
            self::ORDER_STATUS_0   => '待付款',
            self::ORDER_STATUS_1   => '待发货',
            self::ORDER_STATUS_2   => '待收货',
            self::ORDER_STATUS_3   => '已完成',
            self::ORDER_STATUS_4   => '维权申请',
            self::ORDER_STATUS_5   => '维权完成'
        ];
        if (true === $asName) {
            return $name;// 返回包含状态名称的关联数组
        }
        return $desc[$asName] ?? '';// 返回对应状态名称的字符串
    }
}