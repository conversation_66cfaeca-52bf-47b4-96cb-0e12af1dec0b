<?php

namespace app\core\com\enum\member;

/**
 * 系统角色枚举
 * Class MemberRoleEnum
 * @package app\core\com\enum\member
 * 0未注册 1会员 2店员 3店长 4医生 5药剂师 6合伙人 7合伙人子账号 8集团创始人
 */
class MemberRoleEnum
{
    const NO_MEMBER         = 0;// 未注册
    const MEMBER            = 1;// 会员
    const CLERK             = 2;// 店员
    const OWNER             = 3;// 店长
    const DOCTOR            = 4;// 医生
    const PHARMACIST        = 5;// 药剂师
    const COPARTNER         = 6;// 合伙人
    const COPARTNER_ACCOUNT = 7;// 合伙人子账号
    const ORGFOUNDER        = 8;// 集团创始人

    /**
     * @notes 获取角色名称
     * @param bool|int $asName 参数可以是布尔值或整数类型
     * @return string|string[] 如果参数是布尔值，返回包含角色名称的关联数组；如果参数是整数类型，则返回对应角色的字符串。
     * <AUTHOR>
     * @date 2024/3/30 16:56
     */
    public static function getRoleName($asName = true)
    {
        $name = [
            self::NO_MEMBER         => '未注册',
            self::MEMBER            => '会员',
            self::CLERK             => '店员',
            self::OWNER             => '店长',
            self::DOCTOR            => '医生',
            self::PHARMACIST        => '药剂师',
            self::COPARTNER         => '合伙人',
            self::COPARTNER_ACCOUNT => '合伙人子账号',
            self::ORGFOUNDER        => '集团创始人',
        ];
        if (true === $asName) {
            return $name;// 返回包含角色描述的关联数组
        }
        return $desc[$asName] ?? '';// 返回对应角色描述的字符串
    }
}