<?php

namespace app\com\enum\member;

/**
 * 用户登陆终端
 * Class MemberTerminalEnum
 * @package app\common\enum\member
 */
class MemberTerminalEnum
{
    const OTHER = 0; //其他来源
    const WECHAT_MMP = 1; //微信小程序
    const WECHAT_OA  = 2; //微信公众号
    const H5         = 3;//手机H5登录
    const PC         = 4;//电脑PC
    const IOS        = 5;//苹果app
    const ANDROID    = 6;//安卓app


    const ALL_TERMINAL = [
        self::WECHAT_MMP,
        self::WECHAT_OA,
        self::H5,
        self::PC,
        self::IOS,
        self::ANDROID,
    ];

    /**
     * @notes 获取终端
     * @param bool $from
     * @return array|string
     * <AUTHOR>
     * @date 2024/1/15 16:09
     */
    public static function getTermInalDesc($from = true)
    {
        $desc = [
            self::WECHAT_MMP    => '微信小程序',
            self::WECHAT_OA     => '微信公众号',
            self::H5            => '手机H5',
            self::PC            => '电脑PC',
            self::IOS           => '苹果APP',
            self::ANDROID       => '安卓APP',
        ];
        if(true === $from){
            return $desc;
        }
        return $desc[$from] ?? '';
    }

    /**
     * 通过终端的值获取键名
     * @param $value
     * @return string|null
     * <AUTHOR>
     * @date 2024/7/18 5:09
     */
    public static function getTermInalKey($value): ?string
    {
        $reflection = new \ReflectionClass(__CLASS__);
        $constants = $reflection->getConstants();
        foreach ($constants as $key => $val) {
            if ($val === $value) {
                return $key;
            }
        }
        return null;
    }

    /**
     * 获取终端的键值对映射 （中文名 => 常量名）
     * @return array
     * <AUTHOR>
     * @date 2024/7/18 5:09
     */
    public static function getTerminalMap(): array
    {
        $desc = self::getTermInalDesc();
        $map = [];
        foreach ($desc as $key => $value) {
            $constantName = self::getTermInalKey($key);
            if ($constantName !== null) {
                $map[$value] = $constantName;
            }
        }
        return $map;
    }
}