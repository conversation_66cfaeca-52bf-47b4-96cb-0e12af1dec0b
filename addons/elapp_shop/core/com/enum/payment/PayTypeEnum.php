<?php

namespace app\core\com\enum\payment;

/**
 * 系统支付类型枚举
 * Class PayTypeEnum
 * @package app\core\com\enum\payment
 * <AUTHOR>
 * @date 2024/04/08 14:50
 * @version 1.0
 * @description 系统支付类型枚举
 * 0 未支付; 1 余额支付; 2 在线支付(业务中包含微信支付、支付宝支付、银联支付等) 3 货到付款; 4 收银台现金收款; 5 消费预留金; 11 后台付款; 21 微信支付; 22 支付宝支付; 23 银联支付; 37 收银台付款;
 * 当支付方式为3并且是收银台订单时是现金收款
 */
class PayTypeEnum
{
    /**
     * 支付类型 paytype
     */
    const NO_PAY           = 0;// 未支付
    const BALANCE_PAY      = 1;// 余额支付
    const ON_LINE_PAY      = 2;// 在线支付
    const DELIVERY_PAY     = 3;// 货到付款
    const CASHIER_CASH_PAY = 4;// 收银台付款
    const CONSUME_PAY      = 5;// 消费预留金
    const BACKEND_PAY      = 11;// 后台付款
    const WECHAT_PAY       = 21;// 微信支付
    const ALI_PAY          = 22;// 支付宝支付
    const UNION_PAY        = 23;// 银联支付

    /**
     * 支付方式 tradepaytype
     */
    const ALI_BAR           = 31;// 支付宝条码
    const ALI_JSAPI         = 32;// 支付宝生活号
    const ALI_APP           = 33;// 支付宝APP支付
    const ALI_WAP           = 34;// 支付宝WAP支付
    const WX_H5             = 35;// 微信H5

    const WX_JSAPI          = 36;//微信公众号
    const WX_BAR            = 37;// 微信条码
    const WX_LITE           = 38;// 微信小程序
    const WX_APP            = 39;// 微信APP

    /**
     * @notes 获取支付类型名称
     * @param bool|int $asName 参数可以是布尔值或整数类型
     * @return string|string[] 如果参数是布尔值，返回包含支付类型名称的关联数组；如果参数是整数类型，则返回对应类型的字符串。
     * <AUTHOR>
     * @date 2024/04/08 14:50
     */
    public static function getPayTypeName($asName = true)
    {
        $name = [
            self::NO_PAY           => '未支付',
            self::BALANCE_PAY      => '余额支付',
            self::ON_LINE_PAY      => '在线支付',
            self::DELIVERY_PAY     => '货到付款',
            self::CASHIER_CASH_PAY => '收银台付款',
            self::CONSUME_PAY      => '消费预留金',
            self::BACKEND_PAY      => '后台付款',
            self::WECHAT_PAY       => '微信支付',
            self::ALI_PAY          => '支付宝支付',
            self::UNION_PAY        => '银联支付',

            self::ALI_BAR          => 'ALI_BAR',
            self::ALI_JSAPI        => 'ALI_JSAPI',
            self::ALI_APP          => 'ALI_APP',
            self::ALI_WAP          => 'ALI_WAP',
            self::WX_H5            => 'WX_H5',
            self::WX_JSAPI         => 'WX_JSAPI',
            self::WX_BAR           => 'WX_BAR',
            self::WX_LITE          => 'WX_LITE',
            self::WX_APP           => 'WX_APP',
        ];
        if (true === $asName) {
            return $name;// 返回包含支付类型名称的关联数组
        }
        return $name[$asName] ?? '';// 返回对应支付类型名称的字符串
    }
}