<?php

namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;

class PrescriptionOrderPatientinfoModel extends MicroEngineModel
{
    protected $name = 'prescription_order_patientinfo';
    protected $pk   = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
}