<?php

namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;

class PrescriptionPatientinfoModel extends MicroEngineModel
{
    protected $name = 'prescription_patientinfo';
    protected $pk   = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'openid',
                'mid',
                'patientName',
                'patientSex',
                'patientAge',
                'patientPhone',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    // 获取器开始
    public function getImagesAttr($value)
    {
        return iunserializer($value);
    }
    // 获取器结束
}