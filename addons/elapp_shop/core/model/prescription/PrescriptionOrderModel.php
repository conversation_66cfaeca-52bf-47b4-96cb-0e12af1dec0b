<?php

namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;
use think\model\relation\HasOne;

/**
 * 处方订单模型
 */
class PrescriptionOrderModel extends MicroEngineModel
{
    protected $name = 'prescription_order';
    protected $pk   = 'id';

    /**
     * @desc 与处方订单商品一对多关联
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     */
    public function orderGoods()
    {
        return $this->hasMany(PrescriptionOrderGoodsModel::class, 'orderid', 'id')->field(PrescriptionOrderGoodsModel::scene_fields('list'));
    }

    /**
     * @desc 与处方订单病历一对一关联
     * @return HasOne
     * <AUTHOR>
     */
    public function patientinfo(): HasOne
    {
        return $this->hasOne(PrescriptionPatientinfoModel::class, 'id', 'patientinfo_id')->field(PrescriptionPatientinfoModel::scene_fields('list'));
    }

    /**
     * @desc 与处方订单医院信息一对一关联
     * @return HasOne
     * <AUTHOR>
     */
    public function hosp(): HasOne
    {
        return $this->hasOne(PrescriptionHospModel::class, 'id', 'hospid')->field(PrescriptionHospModel::scene_fields('list'));
    }

    /**
     * 获取器【病历信息】
     * @param $value
     * @return array
     */
    public function getPatientInfoAttr($value)
    {
        return json_decode($value,true);
    }
    /**
     * 获取器【处方信息】
     * @param $value
     * @return array
     */
    public function getRpAttr($value)
    {
        return json_decode($value,true);
    }
    /**
     * 获取器【处方药品信息】
     * @param $value
     * @return array
     */
    public function getRpMedicineListAttr($value)
    {
        return json_decode($value,true);
    }
}