<?php
namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use think\facade\Db;

class PrescriptionOrderGoodsModel extends MicroEngineModel {
    protected $name = 'prescription_order_goods';
    protected $pk = 'id';

    /**
     * @desc 与goodsModel对象多对一相应关联
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     */
    public function goods()
    {
        return $this->belongsTo(GoodsModel::class, 'goodsid', 'id');
    }

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'title',
                'orderid',
                'goodsid',
                'price',
                'total',
                'optionid',
                'optionname',
                'expire_time',
                'create_time',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 获取器开始
    public function getDataAttr($value)
    {
        return json_decode($value, true);
    }
    // 获取器结束

}


