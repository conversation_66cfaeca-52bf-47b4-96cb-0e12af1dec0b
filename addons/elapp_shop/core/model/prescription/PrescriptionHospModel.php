<?php

namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;
use think\model\relation\HasOne;

class PrescriptionHospModel extends MicroEngineModel
{
    protected $name = 'prescription_hosp';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'hospname',
                'apiid',
                'createtime',
                'merchid',
                'rptype',
                'config',
                'isdefault',
                'urlType',
                'enabled'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    /**
     * 一对一关联api接口
     * @return HasOne
     */
    function api()
    {
        return $this->hasOne(PrescriptionHospApiModel::class, 'id', 'apiid')
            ->field(PrescriptionHospApiModel::scene_fields('default'));
    }
    /**
     * 获取器【医院配置信息】
     * @param $value
     * @return array
     */
    public function getConfigAttr($value)
    {
        return unserialize($value);
    }

}