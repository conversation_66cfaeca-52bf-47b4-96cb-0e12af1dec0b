<?php

namespace app\core\model\prescription;

use app\core\model\MicroEngineModel;

class PrescriptionHospApiModel extends MicroEngineModel
{
    protected $name = 'prescription_hosp_api';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'hospname',
                'hospid',
                'apiurl',
                'createtime',
                'merchid',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 获取器【接口参数】
     * @param $value
     * @return array
     */
    public function getDataAttr($value)
    {
        return unserialize($value);
    }

}