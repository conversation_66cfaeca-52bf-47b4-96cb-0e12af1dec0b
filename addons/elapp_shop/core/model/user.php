<?php
namespace app\model;

class UserModel
{
	private $sessionid;

	public function __construct()
	{
		global $_W;
		$this->sessionid = '__cookie_elapp_shop_202107200000_' . $_W['uniacid'];
	}

	public function getOpenid()	{
		$userinfo = $this->getInfo(false, true);
		return $userinfo['openid'];
	}

	public function getInfo($base64 = false, $debug = false){
		global $_W, $_GPC;
		$userinfo = array();

		if (ELAPP_SHOP_DEBUG) {
			$userinfo = array('openid' => 'oT-ihv9XGkJbX9owJiLZcZPAJcog', 'nickname' => 'Hlei', 'headimgurl' => 'https://ss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/logo/bd_logo1_31bdc765.png', 'province' => '广西', 'city' => '南宁');
			$userinfo = array('openid' => 'oT-ihv9XGkJbX9owJiLZcZPAJcog', 'nickname' => 'Hlei', 'headimgurl' => 'https://ss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/logo/bd_logo1_31bdc765.png', 'province' => '广西', 'city' => '南宁');
			$userinfo = array('openid' => 'oT-ihv9XGkJbX9owJiLZcZPAJcog', 'nickname' => 'Hlei', 'headimgurl' => 'https://ss0.bdstatic.com/5aV1bjqh_Q23odCf/static/superman/img/logo/bd_logo1_31bdc765.png', 'province' => '广西', 'city' => '南宁');
		}
		else {
			load()->model('mc');
			$userinfo = mc_oauth_userinfo();
			$need_openid = true;

			if ($_W['container'] != 'wechat') {
				if (($_GPC['do'] == 'order') && ($_GPC['p'] == 'pay')) {
					$need_openid = false;
				}

				if (($_GPC['do'] == 'member') && ($_GPC['p'] == 'recharge')) {
					$need_openid = false;
				}

				if (($_GPC['do'] == 'plugin') && ($_GPC['p'] == 'article') && ($_GPC['preview'] == '1')) {
					$need_openid = false;
				}
			}
		}

		if ($base64) {
			return urlencode(base64_encode(json_encode($userinfo)));
		}

		return $userinfo;
	}

	/**
     * 判断是否关注
     * @param type $openid
     * @return type
     */
	public function followed($openid = '')
	{
		global $_W;
        $redis = redis();
        $cache_key = 'cache.followed.' . $openid;
        //$followed = $redis->get('cache.followed_'.$openid);
        if ($redis->exists($cache_key)) {
            $followed = $redis->get($cache_key);
        } else {
            $followed = pdo_fetch('select follow from ' . tablename('mc_mapping_fans') . ' where openid=:openid and uniacid=:uniacid limit 1', array(':openid' => $openid, ':uniacid' => $_W['uniacid']));
            $followed = $followed['follow'] == 1;
            $redis->setex($cache_key,3600, $followed);
        }

//		if ($followed) {
//			$mf = pdo_fetch('select follow from ' . tablename('mc_mapping_fans') . ' where openid=:openid and uniacid=:uniacid limit 1', array(':openid' => $openid, ':uniacid' => $_W['uniacid']));
//			$followed = $mf['follow'] == 1;
//		}

		return $followed;
	}
}


