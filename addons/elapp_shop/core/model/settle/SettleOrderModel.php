<?php

namespace app\core\model\settle;

use app\BaseModel;

class SettleOrderModel extends BaseModel
{
    protected $name = 'settle_order';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'detail'=>'id,order_sn,role_type,role_id,belong_to,settle_date,commission,commissions,orders,create_time',
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }


    // 获取器定义 start
    public function getCommissionsAttr($value)
    {
        return json_decode($value, true);
    }
    public function getOrderCommissionsAttr($value)
    {
        return json_decode($value, true);
    }
    public function getOrdersAttr($value)
    {
        return json_decode($value, true);
    }
    // 获取器 end
}