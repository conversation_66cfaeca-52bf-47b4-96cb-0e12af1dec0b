<?php

namespace app\core\model\member\favorite;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use think\model\relation\HasOne;

/**
 * 会员收藏模型类
 * class MemberFavoriteModel
 * @package app\core\model\member\favorite
 * <AUTHOR>
 * @date 2024/07/03 21:07
 */
class MemberFavoriteModel extends MicroEngineModel
{
    protected $name = 'member_favorite';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'openid',
                'goodsid',
                'createtime',
                'type',
                'thumb',
                'title',
                'marketprice',
                'productprice',
                'deleted',
                'member_id',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid')->field(GoodsModel::scene_fields('default'));
    }
}