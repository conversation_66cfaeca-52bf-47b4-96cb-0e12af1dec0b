<?php
namespace app\core\model\member\sandimeng;

use app\core\model\MicroEngineModel;
use think\facade\Db;

class MemberModel extends MicroEngineModel
{

    protected $table = 'ims_elapp_shop_sandimeng_member';

    /**
     * 根据card_no 查找父级关系
     * 返回上级信息
     * @param  [type] $card_no [description]
     * @return [type]          [description]
     */
    public function getParentsRelationByCardNo($card_no, $retry = false)
    {
        $data = $this->where("card_no = {$card_no}")->find();// todo 报错：Column not found: 1054 Unknown column 'uniacid' in 'where clause' ，待查
        if ($data) {
            $data     = $data->toArray();
            $refer_id = $data['refer_id']; // 上级
            if ($refer_id) {
                $count_order = Db::query("select id from ims_elapp_shop_order where member_id = {$data['member_id']} and status >= 1 and activity_id = 4 limit 1"); //查找上级支付订单的
                if ($retry && $data['member_id'] > 0 && $count_order) {
                    return $data;
                }
                $parentData = $this->getParentsRelationByCardNo($refer_id, true); //获取父级
                if (empty($parentData)) {
                    $parentData = $this->getParentsRelationByCardNo("09569618", true); //默认返回顶级
                }
                if ($parentData['member_id'] > 0) {
                    return $parentData;
                }
            }
        }
        return $data;
    }

    /**
     * 根据card_no 查找父级关系
     * @param  [type] $card_no [description]
     * @return [type]          [description]
     */

    public function getReferRelationByCardNo($card_no, $order = false)
    {
        $data = $this->where("card_no = {$card_no}" . ($order ? ' and member_id > 0' : ''))->find();
        if ($data) {
            $data     = $data->toArray();
            $refer_id = $data['refer_id'];
            if ($refer_id) {
                $parentData = $this->getReferRelationByCardNo($refer_id);
                if ($parentData) {
                    return array_merge([$parentData], $data);
                }
            }
        }
        return $data;
    }

    public function getInfoByCardNo($card_no)
    {
        $data = $this->where("card_no = {$card_no}")->find();
        return $data;
    }

    //递归数据获取用户ID
    public function recursiveGetIds($array)
    {
        $ids = [];
        if (isset($array['member_id']) && !empty($array['member_id'])) {
            $copartner_data = Db::query("select c.id as copartner_id from ims_elapp_shop_member as m inner join ims_elapp_shop_copartner_user as c on c.openid = m.openid where m.id = {$array['member_id']} and c.status = 1 and del_at = 0");
            if ($copartner_data && count($copartner_data)) {
                $ids[] = $copartner_data[0]['copartner_id'];
            }
        }
        if (isset($array[0])) {
            $ids = array_merge($ids, $this->recursiveGetIds($array[0]));
        }
        return $ids;
    }

    public function updateRelationData($data)
    {
        $mentor_id  = intval($data['mentor_id']); // 上级合伙人id
        $member_id  = intval($data['member_id']); // 自己mid
        $onmid      = intval($data['onmid']); // 上级mid
        $openid     = $data['openid']; // 自己
//        $is_org_sub = intval($data['is_org_sub']); //是否为分公司

//        pdo_update('elapp_shop_copartner_user', ['mentor_id' => $mentor_id, 'is_org_sub' => $is_org_sub], ['openid' => $openid]);
        pdo_update('elapp_shop_copartner_user', ['mentor_id' => $mentor_id], ['openid' => $openid]);
        pdo_update('elapp_shop_member', ['is_clerk' => 1, 'clerk_create_time' => time(), 'clerk_status' => 1, 'is_mentor' => 1, 'mentor_status' => 1, 'onmid' => $onmid, 'clerk_id' => $member_id, 'mentor_id' => $onmid], ['openid' => $openid]);
    }

    public function updatePath($openid, $path_data, $parent_path_data)
    {
        $ids = explode('/', $path_data);
        pdo_update('elapp_shop_copartner_user', ['copartner_user_path' => $path_data], ['openid' => $openid]);
        pdo_update('elapp_shop_member', ['copartner_user_path' => $path_data, 'copartner_id' => end($ids)], ['openid' => $openid]);

        $ids = explode('/', $parent_path_data);

        $f = [
            'id', 'mentor_id', 'clerk_id', 'copartner_user_path',
        ];
        $member = pdo_get('elapp_shop_member', ['openid' => $openid], $f);
        $parent = pdo_get('elapp_shop_member', ['id' => $member['mentor_id']], $f);
        // 分润
        // todo order.clerk_id = mentor_id
        if (empty($parent)) {
            dump('用户' . $member['id'] . '的上级' . $member['mentor_id'] . '不存在');
            return;
        }
        pdo_update('elapp_shop_order', ['clerk_id' => $parent['id'], 'mentor_id' => $parent['mentor_id'], 'copartner_user_path' => $parent_path_data, 'copartner_id' => intval(end($ids))], ['openid' => $openid]);
        pdo_update('elapp_shop_member_card_order', ['clerk_id' => $parent['id'], 'mentor_id' => $parent['mentor_id'], 'copartner_user_path' => $parent_path_data, 'copartner_id' => end($ids)], ['openid' => $openid]);
        pdo_update('elapp_shop_member_servicefee_order', ['clerk_id' => $parent['id'], 'mentor_id' => $parent['mentor_id'], 'copartner_user_path' => $parent_path_data, 'copartner_id' => end($ids)], ['openid' => $openid]);
    }

    //查询已支付9800活动所有用户数据数据
    public function all($where = [])
    {
        //$where[] = ['o.status', '>=', '1'];
        $where[] = ['activity_id', '=', '4'];
        $data    = $this->alias('sm')->join('ims_elapp_shop_member m', 'm.id = sm.member_id')
            ->join('ims_elapp_shop_order o', 'o.openid = m.openid')
            ->field('m.id mid,sm.*,m.openid,o.id as orderid')
            ->where($where)
            ->order('m.id asc')
            ->limit(100)
            ->select()->toArray();
        return $data;
    }

    public function list_data($where = [], $page = 1, $size = 20, $field = "sm.*,m.mobile")
    {
        $data = $this->alias('sm')->join('ims_elapp_shop_member m', 'm.id = sm.member_id')->whereOr($where)->page($page, $size)->field($field)->select()->toArray();
        return $data;
    }

    public function list_data_count($where)
    {
        $result = $this->alias('sm')->join('ims_elapp_shop_member m', 'm.id = sm.member_id')->whereOr($where)->count();
        return $result;
    }

    //获取未参与成功活动的用户
    public function unSuccessfulList($page = 1, $size = 20)
    {
        //查找出所有购买订单的用户
        $where[] = ['o.status', '>', '0'];
        $where[] = ['activity_id', '=', '4'];
        $where[] = ['sm.member_id', '>', '0'];
        $data    = $this->alias('sm')->join('ims_elapp_shop_order o', 'o.member_id = sm.member_id')->field('o.member_id,o.id as order_id')->where($where)->group('sm.member_id')->select()->toArray();
        if ($data && count($data)) {
            $order_ids          = array_column($data, 'order_id');
            $success_member_ids = array_column($data, 'member_id');
            //查找出取消订单的用户
            $cancel_where[] = ['o.status', '=', '-1'];
            $cancel_where[] = ['activity_id', '=', '4'];
            $cancel_where[] = ['sm.member_id', '>', '0'];
            $cancel_where[] = ['o.member_id', 'not in', $success_member_ids];
            $cancel_data    = $this->alias('sm')->join('ims_elapp_shop_order o', 'o.member_id = sm.member_id')->field('sm.id as sdm_id,o.member_id,o.id as order_id')->where($cancel_where)->group('sm.member_id')->page($page, $size)->select()->toArray();
            return $cancel_data;
        } else {
            return [];
        }
    }

    //导入三迪梦数据

    /**
     * 导入平台校友资料
     * 格式为data
     **/
    public function daoru($dir = '', $batch = 1)
    {
        exit();
        if (!$dir) {
            $dir = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . "/data/";
        }
        $fp    = opendir($dir);
        $time  = time();
        $index = 1;
        while ($f = readdir($fp)) {
            $file = $dir . '/' . $f;
            if (is_file($file) && substr($f, -4) == '.txt') {
                $f_p = fopen($file, 'r');
                $str = '';
                while ($s = fread($f_p, 1000)) {
                    $str .= $s;
                }
                $rows = explode("\n", $str);
                echo '<pre>';
                //$date = explode('-', substr($f, 0, -4));
                //var_dump($rows);
                unset($data);
                foreach ($rows as $row) {
                    if (strlen($row) == 0) {
                        continue;
                    }
                    $cols               = explode(",", $row);
                    $data['name']       = $cols[0];
                    $data['card_no']    = $cols[1];
                    $data['phone']      = $cols[1];
                    $data['idcard']     = '';
                    $data['refer_id']   = $cols[4];
                    $data['level']      = $cols[5] == '分公司' ? 1 : 2;
                    $data['create_at']  = $time;
                    $data['update_at']  = 0;
                    $data['batch']      = $batch;
                    $data['refer_name'] = $cols[3];

                    $res = $this->where(['card_no' => $data['card_no']])->find();
                    if ($res) {
                        echo '插入失败数据：&nbsp;&nbsp;<font color="blue">:' . $res['id'] . '----' . $res['name'] . '</font>&nbsp;&nbsp;<font color="red">' . $data['card_no'] . '</font><br/>';
                        if ($res['member_id'] == 0) {
                            echo $this->where(['id' => $res['id']])->fetchSql(true)->update(['card_no' => $data['card_no']]);
                            echo '<br/>';
                        }
                        unset($res);
                    } else {
                        ++$index;
                        pdo_insert('elapp_shop_sandimeng_member', $data);
                    }
                    //echo $row . ($id ? '<font color="blue">--添加成功</font>' : '<font color="red">--添加失败</font>') . '<br/>' . M('Grad_studentlist')->getLastSql() . '<br/>';
                }
            }
        }
        echo $index;
    }

    public function getUpdateRelationSql($dir = '', $batch = 1)
    {
        if (!$dir) {
            $dir = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . "/data/";
        }
        $fp    = opendir($dir);
        $time  = time();
        $index = 1;
        $sql   = [];
        while ($f = readdir($fp)) {
            $file = $dir . '/' . $f;
            if (is_file($file) && substr($f, -4) == '.txt') {
                $f_p = fopen($file, 'r');
                $str = '';
                while ($s = fread($f_p, 1000)) {
                    $str .= $s;
                }
                $rows = explode("\n", $str);
                echo '<pre>';
                //$date = explode('-', substr($f, 0, -4));
                //var_dump($rows);
                unset($data);

                foreach ($rows as $row) {
                    if (strlen($row) == 0) {
                        continue;
                    }
                    $cols     = explode(",", $row);
                    $sql_item = "update ims_elapp_shop_sandimeng_member set refer_id = {$cols[1]} where card_no = {$cols[0]};";
                    echo $sql_item;
                    echo '<br/>';
                }
            }
        }
        return $sql;
    }

    /***
     * 清理数据
     * batch 批次
     * 查询该批次是否有上级关系，没有的话就手动删除
     * ***/
    public function cleanData($batch = 1)
    {
        $list = $this->where(['batch' => $batch])->select()->toArray();
        $data = [];
        foreach ($list as $value) {
            $where   = [];
            $where[] = ['card_no', '=', $value['refer_id']];
            $res     = $this->where($where)->find();
            if (!$res) {
                $data[] = $value;
            }
        }
        return $data;
    }
}
