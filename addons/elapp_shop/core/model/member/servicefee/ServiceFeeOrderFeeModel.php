<?php
namespace app\core\model\member\servicefee;

use app\core\model\MicroEngineModel;

class ServiceFeeOrderFeeModel extends MicroEngineModel {
    protected $name = 'member_servicefee_order_fee';
    protected $pk = 'id';

    /**
     * @desc 与goodsModel对象多对一相应关联
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     */
    public function fee()
    {
        return $this->belongsTo(MemberServicefeeModel::class, 'feesid', 'id');
    }

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=>'id,orderid,feesid,price,total,createtime as create_time',
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

}


