<?php

namespace app\core\model\member\servicefee;

use app\core\model\copartner\CopartnerUserModel;
use app\core\model\MicroEngineModel;
use app\model\MemberModel;

class ServiceFeeOrderModel extends MicroEngineModel
{
    protected $name = 'member_servicefee_order';


    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> 'id,member_id,clerk_id,copartner_id,orderno order_no,price,status,paytype,paytime,finishtime,createtime create_time,is_settle',
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    /**
     * @desc 与订单商品一对多关联
     * @return \think\model\relation\HasMany
     * <AUTHOR>
     */
    public function orderGoods()
    {

        return $this->hasMany(ServiceFeeOrderFeeModel::class, 'orderid', 'id');
    }

    public function clerk()
    {
        return $this->belongsTo(MemberModel::class, 'clerk_id', 'id')
            ->field(MemberModel::scene_fields('default'));
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'member_id')
            ->field(MemberModel::scene_fields('default'));
    }

    /**
     * 关联合伙人
     */
    function copartner()
    {
        return $this->belongsTo(CopartnerUserModel::class, 'copartner_id', 'id')
            ->field(CopartnerUserModel::scene_fields('default'));
    }
}