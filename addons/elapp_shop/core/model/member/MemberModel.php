<?php
namespace app\model;
use app\core\com\logic\relation\MemberRelationBindingLogic;
use app\core\com\logic\relation\RelationBindingChecker;
use app\core\com\logic\relation\RelationBindingHookLogic;
use app\core\model\AttrTrait\GetAvatarAttrTrait;
use app\core\model\clerk\ClerkLevelModel;
use app\core\model\copartner\CopartnerAccountModel;
use app\core\model\copartner\CopartnerUserModel;
use app\core\model\member\cart\MemberCartModel;
use app\core\model\MicroEngineModel;
use app\core\model\member\MemberLevelModel;
use framework\classes\account\WeixinAccount;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\facade\Cache;
use think\Model;
use Throwable;
use web\model\fans\MappingFansModel;

class MemberModel extends MicroEngineModel {
    protected $name = 'member';
    protected $pk = 'id';

    protected $insert = ['createtime'];
    protected $createTime = 'createtime';
    protected $autoWriteTimestamp = 'int';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'delete'=> 'id,delete_time,cancel_account_apply_time,is_cancel_account,openid,uniacid',
            'detail'=> 'id,uniacid,openid,realname,nickname,level,clerk_level,mobile,avatar,is_clerk,clerk_status,clerk_id,mentor_id,copartner_id,mentor_id,onmid,createtime',
            'default'=>'id,uniacid,openid,realname,nickname,level,clerk_level,mobile,avatar,is_clerk,clerk_status,clerk_id,mentor_id,copartner_id,mentor_id,onmid,createtime',
        ];
        return $map[$scene] ?? $map['default'];
    }

    /*public function getOpenidAttr($value)
    {
        if (strpos($value, 'sns_') === 0) {
            $value = substr($value, 7);
        }
        if (strpos($value, 'wap_user_') === 0) {
            $pos = strrpos($value, '_');
            $value = substr($value, $pos+1);
        }
        return $value;
    }*/

    /**
     * 废弃字段
     * @var array
     */
    protected $disuse = [];

    /**
     * 与公众号粉丝ims_mc_mapping_fans表一对一映射
     */
    function fans()
    {
        return $this->belongsTo(MappingFansModel::class, 'openid', 'openid');
    }

    /**
     * 关联商品订单
     * @return MemberModel|\think\model\relation\HasMany
     */
    function orders()
    {
        return $this->hasMany(OrderModel::class, 'member_id','id');
    }

    /**
     * 关联已完成的商品订单
     * @return MemberModel|\think\model\relation\HasMany
     */
    function finishOrders()
    {
        return $this->hasMany(OrderModel::class, 'member_id','id')->where(['status'=>3]);
    }

    /**
     * 推荐用户
     * @return \think\model\relation\HasOne
     */
    function onmember() {
        return $this->hasOne(MemberModel::class, 'id', 'onmid')
            ->field(MemberModel::scene_fields('detail'));
    }


    /**
     * 会员身份推广的会员 一对多
     * @return \think\model\relation\HasMany
     */
    function subMembers()
    {
        return $this->hasMany(self::class, 'onmid', 'id')
            ->field(MemberModel::scene_fields('detail'));
    }

    /**
     * 店长身份推广的店长
     * @return \think\model\relation\HasMany
     */
    function subClerks()
    {
        return $this->hasMany(self::class, 'clerk_id', 'id')
            ->where(['is_clerk'=>1,'clerk_status'=>1])
            ->field(MemberModel::scene_fields('detail'));
    }
    
    /**
     * 上级店长
     */
    function clerk()
    {
        return $this->belongsTo(self::class, 'clerk_id', 'id')
            ->field(self::scene_fields('default'));
    }

    /**
     * 上级帮扶店长
     */
    function mentor()
    {
        return $this->belongsTo(self::class, 'mentor_id', 'id')
            ->field(self::scene_fields('default'));
    }

    /**
     * 关联合伙人
     */
    function copartner()
    {
        return $this->belongsTo(CopartnerUserModel::class, 'copartner_id', 'id')
            ->field(CopartnerUserModel::scene_fields('default'));
    }
    /**
     * 关联合伙人帐号
     */
    function copartnerAccount()
    {
        return $this->belongsTo(CopartnerAccountModel::class, 'id', 'mid')
            ->field(CopartnerAccountModel::scene_fields('default'));
    }

    function level()
    {
        return $this->belongsTo(MemberLevelModel::class, 'level', 'id')
            ->field(ClerkLevelModel::scene_fields('list'));
    }

    function clerkLevel()
    {
        return $this->belongsTo(ClerkLevelModel::class, 'clerk_level', 'id')
            ->field(ClerkLevelModel::scene_fields('list'));
    }

    /**
     * 购物车
     */
    function carts() {
        return $this->hasMany(MemberCartModel::class, 'member_id', 'id');
    }

    // 获取器定义 start
    use GetAvatarAttrTrait;
    // 获取器 end

    /**
     * 模糊条件搜索器
     * @param Model $query
     * @param $value
     */
    public function searchLikeAttr($query, $value)
    {
        $query->where('id|nickname|mobile|realname|openid', 'LIKE', "%$value%");
    }

    /**
     * 手机号搜索器
     * @param $query
     * @param $value
     * @return void
     */
    function searchMobileAttr($query, $value)
    {
        if (is_array($value)) {
            $query->whereIn('mobile', $value);
        } else {
            $query->where('mobile', $value);
        }
    }

    /**
     * 是否店员搜索器
     * @param $query
     * @param $is
     * @param $status
     * @param $black
     * @return void
     */
    function searchIsClerkAttr($query, $is = 1, $status = 1, $black = 0)
    {
        $query->where('is_clerk', $is)->where('clerk_status', $status)->where('clerk_black', $black);
    }

    /**
     * 获取会员资料
     */
    public function getInfo($openid = ''){
        global $_W;
        $uid = intval($openid);
        if ($uid == 0) {
            $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where openid=:openid and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            if (empty($info)) {
                if (strexists($openid, 'sns_qq_')) {
                    $openid = str_replace('sns_qq_', '', $openid);
                    $condition = " openid_qq=:openid ";
                    $bindsns = 'qq';
                } elseif (strexists($openid, 'sns_wx_')) {
                    $openid = str_replace('sns_wx_', '', $openid);
                    $condition = " openid_wx=:openid ";
                    $bindsns = 'wx';
                } elseif (strexists($openid, 'sns_wa_')) {
                    $openid = str_replace('sns_wa_', '', $openid);
                    $condition = " openid_wa=:openid ";
                    $bindsns = 'wa';
                }
                if (!empty($condition)) {
                    $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where ' . $condition . '  and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
                    if (!empty($info)) {
                        $info['bindsns'] = $bindsns;
                    }
                }
            }
        } else {
            $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where id=:id  and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $uid));
        }
        if (!empty($info['uid'])) {
            //会员余额积分信息
            load()->model('mc');
            $uid = mc_openid2uid($info['openid']);
            $fans = mc_fetch($uid, array('credit1', 'credit2', 'birthyear', 'birthmonth', 'birthday', 'gender', 'avatar', 'resideprovince', 'residecity', 'nickname'));
            $info['credit1'] = $fans['credit1'];
            $info['credit2'] = $fans['credit2'];
            $info['birthyear'] = empty($info['birthyear']) ? $fans['birthyear'] : $info['birthyear'];
            $info['birthmonth'] = empty($info['birthmonth']) ? $fans['birthmonth'] : $info['birthmonth'];
            $info['birthday'] = empty($info['birthday']) ? $fans['birthday'] : $info['birthday'];
            $info['nickname'] = empty($info['nickname']) ? $fans['nickname'] : $info['nickname'];
            $info['gender'] = empty($info['gender']) ? $fans['gender'] : $info['gender'];
            $info['sex'] = $info['gender'];
            $info['avatar'] = empty($info['avatar']) ? $fans['avatar'] : $info['avatar'];
            $info['headimgurl'] = $info['avatar'];
            $info['province'] = empty($info['province']) ? $fans['resideprovince'] : $info['province'];
            $info['city'] = empty($info['city']) ? $fans['residecity'] : $info['city'];
        }
        if (!empty($info['birthyear']) && !empty($info['birthmonth']) && !empty($info['birthday'])) {
            $info['birthday'] = $info['birthyear'] . '-' . (strlen($info['birthmonth']) <= 1 ? '0' . $info['birthmonth'] : $info['birthmonth']) . '-' . (strlen($info['birthday']) <= 1 ? '0' . $info['birthday'] : $info['birthday']);
        }
        if (empty($info['birthday'])) {
            $info['birthday'] = '';
        }
        if(!empty($info)){
            $info['avatar']  = $this->getAvatar($info['avatar']);
        }
        return $info;
    }

    /**
     * 获取会员信息
     * @param string $openid
     * @param bool $ignore_cache 忽略缓存，强制读取数据库
     */
    public function getMember($openid = '', $ignore_cache = true)
    {
        global $_W;
        $uid = (int)$openid;

        $cachekey = 'cache.member.' . $_W['uniacid'] . '.' . $openid;
        if ($ignore_cache == false) {
            $info = (redis())->get($cachekey);
            if ($info) {
                $info = iunserializer($info);
                return $info;
            }
        }

        if ($uid == 0) {
            $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where  openid=:openid and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            if (empty($info)) {
                if (strexists($openid, 'sns_qq_')) {
                    $openid = str_replace('sns_qq_', '', $openid);
                    $condition = " openid_qq=:openid ";
                    $bindsns = 'qq';
                } elseif (strexists($openid, 'sns_wx_')) {
                    $openid = str_replace('sns_wx_', '', $openid);
                    $condition = " openid_wx=:openid ";
                    $bindsns = 'wx';
                } elseif (strexists($openid, 'sns_wa_')) {
                    $openid = str_replace('sns_wa_', '', $openid);
                    $condition = " openid_wa=:openid ";
                    $bindsns = 'wa';
                }
                if (!empty($condition)) {
                    $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where ' . $condition . '  and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
                    if (!empty($info)) {
                        $info['bindsns'] = $bindsns;
                    }
                }
            }
        } else {
            $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where id=:id and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $openid));
        }

        if (!empty($info)) {
            $info['avatar'] = $this->getAvatar($info['avatar']);

            if(strpos($info['avatar'],'132132')){
                $upgrade2=array();
                $upgrade2['avatar'] = str_replace('132132', '132', $info['avatar']);
                pdo_update('elapp_shop_member', $upgrade2, array('id' => $info['id']));
            }
            $info = $this->updateCredits($info);

            (redis())->setex($cachekey, 3600 , iserializer($info));
        }
        if ($info) {
            $info['credit3'] = $this->getCredit($info['openid'], 'credit3');
            $info['credit5'] = $this->getCredit($info['openid'], 'credit5');
        }

        return $info;
    }

    public function getAvatar($avatar) {
        global $_W;

        if(!strexists($avatar, 'http://') && !strexists($avatar, 'https://')){
            $avatar = tomedia($avatar);
        }

        if (@$_W['ishttps']) {
            $avatar = str_replace('http://', 'https://', $avatar);
        }

        return $avatar;
    }

    public function updateCredits($info)
    {
        global $_W;
        $openid = $info['openid'];
        //自动检测UID情况
        if (empty($info['uid'])) {
            $followed = m('user')->followed($openid);
            if ($followed) {
                load()->model('mc');
                $uid = mc_openid2uid($openid);
                if (!empty($uid)) {
                    $info['uid'] = $uid;
                    $upgrade = array('uid' => $uid);
                    if ($info['credit1'] > 0) {
                        //同步积分
                        mc_credit_update($uid, 'credit1', $info['credit1']);
                        $upgrade['credit1'] = 0;
                    }
                    if ($info['credit2'] > 0) {
                        //同步余额
                        mc_credit_update($uid, 'credit2', $info['credit2']);
                        $upgrade['credit2'] = 0;
                    }
                    if ($info['credit3'] > 0) {
                        //同步收益
                        mc_credit_update($uid, 'credit3', $info['credit3']);
                        $upgrade['credit3'] = 0;
                    }
                    if ($info['credit5'] > 0) {
                        //同步收益
                        mc_credit_update($uid, 'credit5', $info['credit5']);
                        $upgrade['credit5'] = 0;
                    }
                    if (!empty($upgrade)) {
                        pdo_update('elapp_shop_member', $upgrade, array('id' => $info['id']));
                    }
                }
            }
        }
        $credits = $this->getCredits($openid);
        $info['credit1'] = $credits['credit1'];
        $info['credit2'] = $credits['credit2'];
        $info['credit3'] = $credits['credit3'];
        $info['credit5'] = $credits['credit5'];
        return $info;
    }

    /**
     * 通过手机号获取用户信息
     * @param $mobile
     */
    public function getMobileMember($mobile) {
        global $_W;
        $info = pdo_fetch('select * from ' . tablename('elapp_shop_member') . ' where mobile=:mobile and uniacid=:uniacid limit 1', array(':uniacid' => $_W['uniacid'], ':mobile' => $mobile));
        if (!empty($info)){
            $info = $this->updateCredits($info);
        }
        return $info;
    }

    public function getMid() {
        global $_W;
        $openid = $_W['openid'];
        $member = $this->getMember($openid);
        return $member['id'];
    }

    //处理积分或余额
    public function setCredit($openid = '', $credittype = 'credit1', $credits = 0, $log = array()) {

        global $_W;
        load()->model('mc');
        $uid = mc_openid2uid($openid);

        $member = $this->getMember($openid);
        if(empty($uid)){
            $uid = intval($member['uid']);
        }

        if (empty($log)) {
            $log = array($uid, '未记录');
        }elseif (!is_array($log)){
            $log = array(0, $log);
        }
        //积分上限
        if($credittype=='credit1' && empty($log[0]) && $credits>0){
            // 系统充值 判断是否达到积分上限
            $shopset = m('common')->getSysset('trade');

            if(empty($member['diymaxcredit'])){
                // 系统设置
                if($shopset['maxcredit']>0){
                    if($member['credit1']>=$shopset['maxcredit']){
                        return error(-1, "用户积分已达上限");
                    }
                    elseif($member['credit1']+$credits>$shopset['maxcredit']){
                        $credits = $shopset['maxcredit'] - $member['credit1'];
                    }
                }
            }else{
                // 用户设置
                if($member['maxcredit']>0){
                    if($member['credit1']>=$member['maxcredit']){
                        return error(-1, "用户积分已达上限");
                    }
                    elseif($member['credit1']+$credits>$member['maxcredit']){
                        $credits = $member['maxcredit'] - $member['credit1'];
                    }
                }
            }
        }

        if (empty($log)) {
            $log = array($uid, '未记录');
        }elseif (!is_array($log)){
            $log = array(0, $log);
        }

        $log_data = array(
            'uid' => intval($uid),
            'credittype' => $credittype,
            'uniacid' => $_W['uniacid'],
            'num' => $credits,
            'createtime' => TIMESTAMP,
            'module' => 'elapp_shop',
            'operator' => intval($log[0]),
            'remark' => $log[1],
        );
        if (!empty($uid)) {
            //如果已关注
            $value = pdo_fetchcolumn("SELECT {$credittype} FROM " . tablename('mc_members') . " WHERE `uid` = :uid", array(':uid' => $uid));
            $newcredit = $credits + $value;
            if ($newcredit <= 0) {
                // credit3 允许负数
                if ($credittype !== 'credit3') {
                    $newcredit = 0;
                }
            }
            $log_data['remark'] = $log_data['remark']. " 剩余: ".$newcredit;
            pdo_update('mc_members', array($credittype => $newcredit), array('uid' => $uid));
            pdo_update('elapp_shop_member', array($credittype => $newcredit), array('uniacid' => $_W['uniacid'], 'openid' => $openid));
            $a = $newcredit;
            $log_data['presentcredit'] = $a;
            pdo_insert('mc_credits_record', $log_data);
            // 消息通知积分/余额变动
            if ($credittype == 'credit1') {
                $credit_title = '积分变动通知';
                $credit_message = '您充值的积分已到账，当前余额：' . $newcredit . '积分。';
                $changetype = $credits > 0 ? 0 : 1;
                m('notice')->sendMemberPointChange($openid, $credits, $changetype, 0);
            } else if ($credittype == 'credit2') {
                $credit_title = '余额变动通知';
                $credit_message = '您充值的余额已到账，当前余额：' . $newcredit . '元。';
            }

        } else {
            //如果未关注
            $value = pdo_fetchcolumn("SELECT {$credittype} FROM " . tablename('elapp_shop_member') . " WHERE  uniacid=:uniacid and openid=:openid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            $newcredit = $credits + $value;
            if ($newcredit <= 0) {
                // credit3 允许负数
                if ($credittype !== 'credit3') {
                    $newcredit = 0;
                }
            }
            pdo_update('elapp_shop_member', array($credittype => $newcredit), array('uniacid' => $_W['uniacid'], 'openid' => $openid));
            $log_data['remark'] = $log_data['remark']. " OPENID: ".$openid;
            $log_data['remark'] = $log_data['remark']. " 剩余: ".$newcredit;
            $a = $newcredit;
            $log_data['presentcredit'] = $a;
            pdo_insert('mc_credits_record', $log_data);
        }

        //新增商城积分(余额)记录表
        $log_data['openid'] = $openid;
        $log_data['presentcredit'] = $a;
        pdo_insert('elapp_shop_member_credit_record', $log_data);

        // 更新用户缓存
        $cachekey = 'cache.member.' . $_W['uniacid'] . '.' . $openid;
        (redis())->del($cachekey);

        if (p('task')){//##任务中心
            if ($credittype == 'credit1'){//处理积分

            }else if ($credittype == 'credit2') {//处理余额
                    p('task')->checkTaskReward('cost_rechargeenough',$credits,$openid);
                p('task')->checkTaskReward('cost_rechargetotal',$credits,$openid);
                //因为任务为:充值余额完成赠送积分,但是积分达到任务额度也会赠送,需要判断,只充积分,才赠送
                p('task')->checkTaskProgress($credits,'recharge_full',0,$openid);
            }
            p('task')->checkTaskProgress($credits,'recharge_count',0,$openid);
        }

         /*if (p('task')){//##任务中心
            p('task')->checkTaskProgress($credits,'recharge_full',0,$openid);
            p('task')->checkTaskProgress($credits,'recharge_count',0,$openid);
        }

        com('wxcard')->updateMemberCardByOpenid($openid); */
        //com_run('wxcard::updateMemberCardByOpenid',$openid);

        return true;
    }

    //获取积分或余额
    public function getCredit($openid = '', $credittype = 'credit1') {
        global $_W;
        $oldOpenid = $openid;
        $openid = str_replace('sns_wa_','',$openid);

        load()->model('mc');
        $uid = mc_openid2uid($openid);

        if (!empty($uid)) {
            //如果已关注
            return pdo_fetchcolumn("SELECT {$credittype} FROM " . tablename('mc_members') . " WHERE `uid` = :uid", array(':uid' => $uid));

        } else {
            //如果未关注
            $item = pdo_fetch("SELECT {$credittype} FROM " . tablename('elapp_shop_member') . " WHERE openid=:openid and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            if (empty($item)) {
                $item = pdo_fetch("SELECT " . $credittype . " FROM " . tablename("elapp_shop_member") . " WHERE openid=:openid and uniacid=:uniacid limit 1", array(":uniacid" => $_W["uniacid"], ":openid" => $oldOpenid));
            }
            if(empty($item)){
                $item = pdo_fetch("SELECT {$credittype} FROM " . tablename('elapp_shop_member') . " WHERE openid_wa=:openid and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            }

            return empty($item[$credittype])?0:$item[$credittype];
        }
    }

    //获取积分或余额
    public function getCredits($openid = '', $credittypes = array('credit1', 'credit2', 'credit3','credit5')) {

        global $_W;
        $oldOpenid = $openid;
        $openid = str_replace('sns_wa_','',$openid);
        load()->model('mc');
        $uid = mc_openid2uid($openid);

        $types = implode(',', $credittypes);
        if (!empty($uid)) {
            //如果已关注
            return pdo_fetch("SELECT {$types} FROM " . tablename('mc_members') . " WHERE `uid` = :uid limit 1", array(':uid' => $uid));
        } else {
            $item = pdo_fetch("SELECT {$types} FROM " . tablename('elapp_shop_member') . " WHERE openid=:openid and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            if (empty($item)){
                $item = pdo_fetch("SELECT {$types} FROM " . tablename('elapp_shop_member') . " WHERE openid=:openid and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $oldOpenid));
            }
            if(empty($item)){
                $item = pdo_fetch("SELECT {$types} FROM " . tablename('elapp_shop_member') . " WHERE openid_wa=:openid and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            }
            //如果未关注
            //return pdo_fetch("SELECT {$types} FROM " . tablename('elapp_shop_member') . " WHERE  (openid=:openid or openid_wa=:openid) and uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $openid));
            if(empty($item)){
                return array('credit1' => 0,'credit2' => 0);
            }
            return $item;
        }
    }

	/**
	 * 检测用户,注册用户,绑定用户关系
	 */
	public function checkMember() {

		global $_W, $_GPC;

		$member = array();
		$shopset = m('common')->getSysset(array('shop','wap'));
		$openid = $_W['openid'];

        if($_W['routes']=='order.pay_alipay' || $_W['routes']=='creditshop.log.dispatch_complete' || $_W['routes']=='threen.register.threen_complete' || $_W['routes']=='creditshop.detail.creditshop_complete' || $_W['routes']=='order.pay_alipay.recharge_complete' || $_W['routes']=='order.pay_alipay.complete' || $_W['routes']=='newmr.alipay' || $_W['routes']=='newmr.callback.gprs' || $_W['routes']=='newmr.callback.bill' || $_W['routes']=='account.sns' || $_W['plugin']=='mmanage' || $_W['routes']=='live.send.credit' || $_W['routes']=='live.send.coupon' || $_W['routes']=='index.share_url' || strpos($_W["routes"], "openapi") !== false){
            return;
        }

        if($shopset['wap']['open']){
            if($shopset['wap']['inh5app'] && is_h5app() || (empty($shopset['wap']['inh5app']) && empty($openid))){
                return;
            }
        }

		if (empty($openid) && !ELAPP_SHOP_DEBUG) {
			$diemsg = is_h5app() ? "APP正在维护, 请到公众号中访问" : "请在微信客户端打开链接";
			die("<!DOCTYPE html>
                <html>
                    <head>
                        <meta name='viewport' content='width=device-width, initial-scale=1, user-scalable=0'>
                        <title>抱歉，出错了</title><meta charset='utf-8'><meta name='viewport' content='width=device-width, initial-scale=1, user-scalable=0'><link rel='stylesheet' type='text/css' href='https://res.wx.qq.com/connect/zh_CN/htmledition/style/wap_err1a9853.css'>
                    </head>
                    <body>
                    <div class='page_msg'><div class='inner'><span class='msg_icon_wrp'><i class='icon80_smile'></i></span><div class='msg_content'><h4>".$diemsg."</h4></div></div></div>
                    </body>
                </html>");
		}
        $member = $this->getMember($openid);
		$followed = m('user')->followed($openid);
		$uid = 0;
		$mc = array();
		load()->model('mc');
		if ($followed || empty($shopset['shop']['getinfo']) || $shopset['shop']['getinfo'] == 1 ) {
			$uid = mc_openid2uid($openid);
			if( !ELAPP_SHOP_DEBUG ){
				$userinfo = mc_oauth_userinfo();
			} else {
				$userinfo = array(
					'openid' => $member['openid'],
					'nickname' => $member['nickname'],
					'headimgurl' => $member['avatar'],
					'gender' => $member['gender']?$member['gender']:'',
					'province' => $member['province'],
					'city' => $member['city']
				);
			}

			$mc = array();
			$mc['nickname'] = $userinfo['nickname'];
			$mc['avatar'] = $userinfo['headimgurl'];
			$mc['gender'] = !empty($userinfo['sex'])?$userinfo['sex']:'';
			$mc['resideprovince'] = $userinfo['province'];
			$mc['residecity'] = $userinfo['city'];
		}

		if (empty($member) && !empty($openid)) {
			$member = array(
				'uniacid' => $_W['uniacid'],
				'uid' => $uid,
				'openid' => $openid,
				'realname' => !empty($mc['realname']) ? $mc['realname'] : '',
				'mobile' => !empty($mc['mobile']) ? $mc['mobile'] : '',
				'nickname' => !empty($mc['nickname']) ? $mc['nickname'] : '',
				'nickname_wechat' => !empty($mc['nickname']) ? $mc['nickname'] : '',
				'avatar' => !empty($mc['avatar']) ? $mc['avatar'] : '',
				'avatar_wechat' => !empty($mc['avatar']) ? $mc['avatar'] : '',
				'gender' => !empty($mc['gender']) ? $mc['gender'] : '-1',
				'province' => !empty($mc['resideprovince']) ? $mc['resideprovince'] : '',
				'city' => !empty($mc['residecity']) ? $mc['residecity'] : '',
				'area' => !empty($mc['residedist']) ? $mc['residedist'] : '',
				'createtime' => time(),
				'status' => 0
			);

            pdo_insert('elapp_shop_member', $member);

            $this->deleterepeat($openid);

            if(method_exists(m('member'),'memberRadisCountDelete')) {
                m('member')->memberRadisCountDelete(); //清除会员统计radis缓存
            }
			$member['id'] = pdo_insertid();
		} else {
			if ($member['isblack'] == 1) {
				//是黑名单
				show_message("暂时无法访问，请稍后再试!");
			}
			$upgrade = array('uid'=>$uid);
			if (isset($mc['nickname']) && $member['nickname_wechat'] != $mc['nickname']) {
				$upgrade['nickname_wechat'] = $mc['nickname'];
			}
            if(isset($mc['nickname']) && empty($member['nickname'])){
                $upgrade['nickname'] = $mc['nickname'];
            }
			if (isset($mc['avatar']) && $member['avatar_wechat'] != $mc['avatar']) {
				$upgrade['avatar_wechat'] = $mc['avatar'];
			}
            if(isset($mc['avatar']) && empty($member['avatar'])){
                $upgrade['avatar'] = $mc['avatar'];
            }

			if (isset($mc['gender']) && $member['gender'] != $mc['gender']) {
				$upgrade['gender'] = $mc['gender']?$mc['gender']:$member['gender'];
			}
			if (!empty($upgrade)) {
				pdo_update('elapp_shop_member', $upgrade, array('id' => $member['id']));
			}
            $this->deleterepeat($openid);
		}
		//分销商
		if (p('commission')) {
			p('commission')->checkAgent($openid);
		}
        
		//海报图扫描
		if (p('poster')) {
			p('poster')->checkScan($openid);
		}
		if (empty($member)) {
			return false;
		}
        // 关系绑定 Hlei 2024-04-03
        if (!empty($member['id']) && !empty($_GPC['mid'])) {
            $this->relationBinding($member['id'], $_GPC['mid']);
        }
		return array(
			'id' => $member['id'],
			'openid' => $member['openid'],
            'member_info' => $member
		);
	}

    /**
     * 获取所有会员等级
     * @param bool $all 是否返回全部等级,默认只返回启用的等级
     * @param array $where 查询条件
     * @param string $fields 字段
     * @param array $order 排序
     * @param string $key 索引
     * @return array|bool|mixed
     * @throws Throwable
     * @global array $_W
     */
    static function getLevels(bool $all = false, array $where = [], string $fields = '*', array $order = ['level' => 'asc'], string $key = 'id') {
        global $_W;
        $where['uniacid'] = $_W['uniacid'];
        if ($all) {
            $cachekey = 'shop_member_levels_all_'.$_W['uniacid'];
        } else {
            $where['enabled'] = 1;
            $cachekey = 'shop_member_levels_'.$_W['uniacid'];
        }
        return Cache::remember($cachekey, function () use ($where, $fields, $order, $key) {
            return MemberLevelModel::getMemberLevel($where, $fields, $order, $key);
        });
    }

    /**
     * 获取最高会员等级
     * @return int|mixed
     * @throws Throwable
     */
    static function getMaxLevel()
    {
        $levels = self::getLevels();
        $max_level = 0;
        foreach ($levels as $level) {
            $max_level = max($level,$level['level']);
        }
        return $max_level;
    }

    function getLevelNames($all = true, $default = true)
    {
        $levels = $this->getLevels($all);

        if ($default) {
            $set = m('common')->getSysset();
            $default_levelname = empty($set['shop']['levelname']) ? '普通等级' : $set['shop']['levelname'];

            $default = array(
                'id' => '0',
                "levelname" => $default_levelname,
            );
            $levels = array_merge(array($default), $levels);
        }

        $levels = array_column($levels, 'levelname', 'id');
        return $levels;
    }

    /**
     * 根据机构等级id获取等级名,默认取不到则取默认
     */
    function getLevelName($level_id)
    {
        $levels = $this->getLevels(true);
        $levels = array_column($levels, 'levelname', 'id');

        return $levels[$level_id] ?? '普通等级';
    }

    /**
     * 根据会员获取会员等级信息
     * 可传入openid或id
     * @param string|int $openidOrId
     * @global $_W
     */
    function getLevel($openidOrId) {
        global $_W, $_S;
        if (empty($openidOrId)) {
            return false;
        }
        // getMember 方法中已经处理了传入参数是 openid 还是 id
        $member = m('member')->getMember($openidOrId);
        if (!empty($member) && !empty($member['level'])) {
            $level = pdo_fetch('select * from ' . tablename('elapp_shop_member_level') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $member['level'], ':uniacid' => $_W['uniacid']));
            if (!empty($level)) {
                return $level;
            }
        }
        return array(
            'levelname' => empty($_S['shop']['levelname']) ? '普通会员' : $_S['shop']['levelname'],
            'discount' => empty($_S['shop']['leveldiscount']) ? 10 : $_S['shop']['leveldiscount'],
        );
    }

    function getOneGoodsLevel($openid, $goodsid) {
        global $_W;

        $uniacid = $_W['uniacid'];

        $level_info = $this->getLevel($openid);
        $level = intval($level_info['level']);

        $data = array();
        $levels = pdo_fetchall('select * from ' . tablename('elapp_shop_member_level') . ' where uniacid=:uniacid and buygoods=1 and level and level > :level order by level asc', array(':uniacid' => $uniacid, ':level' => $level));

        if (!empty($levels)) {

            foreach ($levels as $k => $v) {
                $goodsids = iunserializer($v['goodsids']);

                if (!empty($goodsids)) {
                    if (in_array($goodsid, $goodsids)) {
                        $data = $v;
                    }
                }
            }
        }
        return $data;
    }

    function getGoodsLevel($openid, $orderid) {
        global $_W;

        $uniacid = $_W['uniacid'];

        $order_goods = pdo_fetchall("select goodsid from " . tablename('elapp_shop_order_goods') . " where orderid=:orderid and uniacid=:uniacid", array(':uniacid' => $uniacid, ':orderid' => $orderid));

        $levels = array();
        $data = array();
        if (!empty($order_goods)) {
            foreach ($order_goods as $k => $v) {
                $item = $this->getOneGoodsLevel($openid, $v['goodsid']);
                if (!empty($item)) {
                    $levels[$item['level']] = $item;
                }
            }
        }

        if (!empty($levels)) {
            $level = max(array_keys($levels));
            $data = $levels[$level];
        }
        return $data;
    }

    /**
     * 会员升级
     *
     * @param int|string $openidOrId
     * @param int $orderid
     * @param $status int 3-订单完成 1-订单付款后 增加了一个付款后和订单完成后升级的选项,所以增加了一个status的状态显示
     */
    function upgradeLevel($openidOrId, int $orderid = 0, int $status = 3) {
        global $_W;
        if (empty($openidOrId)) {
            return;
        }

        $shopset = m('common')->getSysset('shop');
        $leveltype = intval($shopset['leveltype']);
        $member = m('member')->getMember($openidOrId);
        if (empty($member)) {
            return;
        }

        //查找符合条件的新等级
        $level = false;
        if (empty($leveltype)) {
            /*$ordermoney = pdo_fetchcolumn('select ifnull( sum(og.realprice),0) from ' . tablename('elapp_shop_order_goods') . ' og '
                . ' left join ' . tablename('elapp_shop_order') . ' o on o.id=og.orderid '
                . ' where o.openid=:openid and o.status=3 and o.uniacid=:uniacid ', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid']));*/
            //根据订单支付金额来判断   -- yqj --
            $ordermoney = pdo_fetchcolumn('select sum(price) from ' . tablename('elapp_shop_order') . " where uniacid=:uniacid and openid=:openid and status=:status limit 1", array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid'], ':status' => $status));
            $ordermoney = floatval($ordermoney);
            $level = pdo_fetch('select * from ' . tablename('elapp_shop_member_level') . " where uniacid=:uniacid  and enabled=1 and {$ordermoney} >= ordermoney and ordermoney>0  order by level desc limit 1", array(':uniacid' => $_W['uniacid']));
        } else if ($leveltype == 1) {
            $ordercount = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where openid=:openid and status=:status and uniacid=:uniacid ', array(':uniacid' => $_W['uniacid'], ':openid' => $member['openid'],':status' => $status));
            $level = pdo_fetch('select * from ' . tablename('elapp_shop_member_level') . " where uniacid=:uniacid and enabled=1 and {$ordercount} >= ordercount and ordercount>0  order by level desc limit 1", array(':uniacid' => $_W['uniacid']));
        }

        //购买特定商品达到的会员级别
        if (!empty($orderid)) {
            $goods_level = $this->getGoodsLevel($member['openid'], $orderid);
            if (empty($level)) {
                $level = $goods_level;
            } else {
                if (!empty($goods_level)) {
                    if ($goods_level['level'] > $level['level']) {
                        $level = $goods_level;
                    }
                }
            }
        }

        if (empty($level)) {
            return;
        }
        if ($level['id'] == $member['level']) {
            return;
        }
        //旧等级
        $oldlevel = $this->getLevel($openidOrId);
        $canupgrade = false;  //是否可以升级

        if (empty($oldlevel['id'])) {
            //用户没有等级
            $canupgrade = true;
        } else {
            if ($level['level'] > $oldlevel['level']) {
                //新等级权重较大
                $canupgrade = true;
            }
        }
        if ($canupgrade) {
            //会员升级
            pdo_update('elapp_shop_member', array('level' => $level['id']), array('id' => $member['id']));

            //com('wxcard')->updateMemberCardByOpenid($openid);
            com_run('wxcard::updateMemberCardByOpenid',$member['openid']);

            //模板消息
            m('notice')->sendMemberUpgradeMessage($member['openid'], $oldlevel, $level);
        }
    }

    /**
     * 根据会员等级ID升级会员
     * @param int|string $openidOrId
     */
    function upgradeLevelByLevelId($openidOrId, $LevelId) {
        global $_W;
        if (empty($openidOrId)) {
            return;
        }

        $member = m('member')->getMember($openidOrId);
        if (empty($member)) {
            return;
        }

        $level = pdo_fetch('select *  from ' . tablename('elapp_shop_member_level') . " where uniacid=:uniacid and enabled=1 and id=:id",  array(':uniacid' => $_W['uniacid'],':id' =>$LevelId));

        if (empty($level)) {
            return;
        }
        if ($level['id'] == $member['level']) {
            return;
        }

        //旧等级
        $oldlevel = $this->getLevel($openidOrId);
        $canupgrade = false;  //是否可以升级

        if (empty($oldlevel['id'])) {
            //用户没有等级
            $canupgrade = true;
        } else {
            if ($level['level'] > $oldlevel['level']) {
                //新等级权重较大
                $canupgrade = true;
            }
        }
        if ($canupgrade) {
            //会员升级
            pdo_update('elapp_shop_member', array('level' => $level['id']), array('id' => $member['id']));

            //com('wxcard')->updateMemberCardByOpenid($openid);
            com_run('wxcard::updateMemberCardByOpenid',$member['openid']);

            //模板消息
            m('notice')->sendMemberUpgradeMessage($member['openid'], $oldlevel, $level);
        }
    }

    /**
     * 根据会员所购买会员卡的对应等级调整会员的等级id【该方法升级和降级都适用】
     * @param $openid
     * @return void
     * <AUTHOR>
     * @data 20230308
     */
    function downgradeLevel($openid) {
        global $_W;
        if (empty($openid)) return;
        $member = m('member')->getMember($openid);
        if (empty($member)) return;
        //1.获取该会员下所购买的会员卡 card_history/membercardid/get_Mycard
        $member_card_list = p('membercard')->get_Mycard($openid);
        $member_level_id = 0;
        if (!empty($member_card_list['list'])) {
            //2.检查这些购买会员卡的有效期 $this->model->check_Hasget 1.已经检测有效卡了

            //3.过期则降级，获取有效最高等级的会员卡
            $member_level_ids = array_column($member_card_list['list'], 'member_level');
            //if (!in_array($member['level'], $member_level_ids)){
                $member_level_id = pdo_fetchcolumn("select id from " . tablename('elapp_shop_member_level') . " WHERE id in(". implode(',',$member_level_ids) . ") AND uniacid=" . $_W['uniacid']) . "order by level desc";
                /*if ($member_level_id != $member['level']) {
                    $member_level_id = $member_level_id;
                }*/
            //}
        }
        //4.更新该会员的等级
        if (!empty($member['level']) && $member_level_id != $member['level']) {
            pdo_update('elapp_shop_member', array('level' => $member_level_id), array('id' => $member['id']));
        }
    }

    /**
     * 获取所有会员分组
     * @global type $_W
     * @return type
     */
    function getGroups() {
        global $_W;
        return pdo_fetchall('select * from ' . tablename('elapp_shop_member_group') . ' where uniacid=:uniacid order by id asc', array(':uniacid' => $_W['uniacid']));
    }

    //获取会员分组
    function getGroup($openid) {
        if (empty($openid)) {
            return false;
        }

        $member = m('member')->getMember($openid);
        return $member['groupid'];
    }

    //给会员设置分组
    function setGroups($openid,$group_ids,$reason = ''){
        $is_id = false;
        if (intval($openid) > 0){//id
            $openid = m('member')->getInfo($openid);
            if (empty($openid['openid']))return false;
            $openid = $openid['openid'];
        }
        $condition = array('openid'=>$openid);
        if (is_array($group_ids)){
            $group_arr = $group_ids;
            $group_ids = implode(',',$group_ids);
        }elseif(is_string($group_ids) || is_numeric($group_ids)){
            $group_arr = explode(',',$group_ids);
        }else{
            return false;
        }
        $old_group_ids = pdo_getcolumn('elapp_shop_member',$condition,'groupid');
        $diff_ids = explode(',',$group_ids);
        if (!empty($old_group_ids)){
            $old_group_ids = explode(',',$old_group_ids);
            $group_ids = array_merge($old_group_ids , $diff_ids);
            $group_ids = array_flip(array_flip($group_ids));
            $group_ids = implode(',',$group_ids);
            $diff_ids = array_diff($diff_ids,$old_group_ids);
        }
        pdo_update('elapp_shop_member', array('groupid'=>$group_ids), $condition);
        foreach ($diff_ids as $groupid){
            pdo_insert('elapp_shop_member_group_log',
                array('add_time'=>date('Y-m-d H:i:s'),'group_id'=>$groupid,'content'=>$reason,'mid'=>intval($openid),'openid'=>$is_id ? '' : $openid));
        }
        return true;
    }

    //充值积分
    function setRechargeCredit($openid = '', $money = 0) {
        if (empty($openid)) {
            return;
        }
        global $_W;
        $credit = 0;
        $set = m('common')->getSysset(array('trade', 'shop'));

        if ($set['trade']) {
            $tmoney = floatval($set['trade']['money']);
            if(!empty($tmoney)){
                $tcredit = intval($set['trade']['credit']);
                if ($money >= $tmoney) {
                    if ($money % $tmoney == 0) {
                        $credit = intval($money / $tmoney) * $tcredit;
                    } else {
                        $credit = (intval($money / $tmoney) + 1) * $tcredit;
                    }
                }
            }
        }
        if ($credit > 0) {
            $this->setCredit($openid, 'credit1', $credit, array(0, $set['shop']['name'] . '会员充值积分:credit2:' . $credit));
        }
    }

    //计算税费等相关费用
    function getCalculateMoney($money, $set_array): array
    {

        $charge = $set_array['charge'];
        $begin = $set_array['begin'];
        $end = $set_array['end'];

        $array = array();
        $array['deductionmoney'] = round($money * $charge / 100, 2);
        if ($array['deductionmoney'] >= $begin && $array['deductionmoney'] <= $end) {
            $array['deductionmoney'] = 0;
        }
        $array['realmoney'] = round($money - $array['deductionmoney'], 2);
        if ($money == $array['realmoney']) {
            $array['flag'] = 0;
        } else {
            $array['flag'] = 1;
        }
        return $array;
    }

    public function checkMemberFromPlatform($openid = '',$acc='') {
        global $_W;
        if (empty($acc))
        {
            $acc = WeiXinAccount::create();
        }
        $userinfo = $acc->fansQueryInfo($openid);
        $userinfo['avatar'] = $userinfo['headimgurl'];

        $redis = redis();
        if (!is_error($redis)){
            $member = $redis->get($openid.'_checkMemberFromPlatform');
            if (!empty($member)){
                return json_decode($member,true);
            }
        }

        load()->model('mc');
        $uid = mc_openid2uid($openid);
        if (!empty($uid)) {
            pdo_update('mc_members', array(
                'nickname' => $userinfo['nickname'],
                'gender' => $userinfo['sex'],
                'nationality' => $userinfo['country'],
                'resideprovince' => $userinfo['province'],
                'residecity' => $userinfo['city'],
                'avatar' => $userinfo['headimgurl']), array('uid' => $uid)
            );
        }
        pdo_update('mc_mapping_fans', array(
            'nickname' => $userinfo['nickname']
        ), array('uniacid' => $_W['uniacid'], 'openid' => $openid));

        $member = $this->getMember($openid);
        if (empty($member)) {
            $mc = mc_fetch($uid, array('realname', 'nickname', 'mobile', 'avatar', 'resideprovince', 'residecity', 'residedist'));
            $member = array(
                'uniacid' => $_W['uniacid'],
                'uid' => $uid,
                'openid' => $openid,
                'realname' => $mc['realname'],
                'mobile' => $mc['mobile'],
                'nickname' => !empty($mc['nickname']) ? $mc['nickname'] : $userinfo['nickname'],
                'avatar' => !empty($mc['avatar']) ? $mc['avatar'] : $userinfo['avatar'],
                'gender' => !empty($mc['gender']) ? $mc['gender'] : $userinfo['sex'],
                'province' => !empty($mc['resideprovince']) ? $mc['resideprovince'] : $userinfo['province'],
                'city' => !empty($mc['residecity']) ? $mc['residecity'] : $userinfo['city'],
                'area' => $mc['residedist'],
                'createtime' => time(),
                'status' => 0
            );
            pdo_insert('elapp_shop_member', $member);
            if(method_exists(m('member'),'memberRadisCountDelete')) {
                m('member')->memberRadisCountDelete(); //清除会员统计radis缓存
            }
            $member['id'] = pdo_insertid();
            $member['isnew'] = true;
        } else {
            if (isset($userinfo["nickname"]) && $member["nickname_wechat"] != $userinfo["nickname"]) {
                $member["nickname_wechat"] = $userinfo["nickname"];
            }
            if (isset($userinfo["nickname"]) && empty($member["nickname"])) {
                $member["nickname"] = $userinfo["nickname"];
            }
            if (isset($userinfo["headimgurl"]) && $member["avatar_wechat"] != $userinfo["headimgurl"]) {
                $member["avatar_wechat"] = $userinfo["headimgurl"];
            }
            if (isset($userinfo["headimgurl"]) && empty($member["avatar"])) {
                $member["avatar"] = $userinfo["headimgurl"];
            }
            $member["province"] = $userinfo["province"];
            $member["city"] = $userinfo["city"];
            pdo_update("elapp_shop_member", $member, array("id" => $member["id"]));
            if (time() - $member["createtime"] < 60) {
                $member["isnew"] = true;
            } else {
                $member["isnew"] = false;
            }
        }
        if (!is_error($redis)){
            $redis->set($openid.'_checkMemberFromPlatform',json_encode($member),20);
        }
        return $member;
    }

    public function mc_update($mid, $data){
        global $_W;

        if(empty($mid) || empty($data)){
            return;
        }
        $wapset = m('common')->getSysset('wap');
        $member = $this->getMember($mid);
        if(!empty($wapset['open']) && isset($data['mobile']) && $data['mobile']!=$member['mobile']){
            unset($data['mobile']);
        }
        load()->model('mc');
        mc_update($mid, $data);
    }

    public function checkMemberSNS($sns){
        global $_W, $_GPC;

        if(empty($sns)){
            $sns = $_GPC['sns'];
        }
        if(empty($sns)){
            return;
        }
        elseif ($sns=='wx'){
            load()->func('communication');

            $token = trim($_GPC['token']);
            $openid = trim($_GPC['openid']);

            $appid = 'wxc3d9d8efae0ae858';
            $secret = '93d4f6085f301c405b5812217e6d5025';

            if(empty($token) && !empty($_GPC['code'])){
                $codeurl = "https://api.weixin.qq.com/sns/oauth2/access_token?appid=".$appid."&secret=".$secret."&code=".trim($_GPC['code'])."&grant_type=authorization_code";
                $coderesult = $userinfo = ihttp_request($codeurl);
                $coderesult = json_decode($coderesult['content'], true);
                if(empty($coderesult['access_token']) || empty($coderesult['openid'])){
                    return;
                }
                $token = $coderesult['access_token'];
                $openid = $coderesult['openid'];
            }

            if(empty($token) || empty($openid)){
                return;
            }
            $snsurl = "https://api.weixin.qq.com/sns/userinfo?access_token=".$token."&openid=".$openid."&lang=zh_CN";
            $userinfo = ihttp_request($snsurl);
            $userinfo = json_decode($userinfo['content'], true);
            if(empty($userinfo['openid'])){
                return;
            }
            $userinfo['openid'] = 'sns_wx_'.$userinfo['openid'];
        }
        elseif ($sns=='qq'){
            $userinfo = htmlspecialchars_decode($_GPC['userinfo']);
            $userinfo = json_decode($userinfo, true);
            $userinfo['openid'] = 'sns_qq_' . $_GPC['openid'];
            $userinfo['headimgurl'] = $userinfo['figureurl_qq_2'];
            $userinfo['gender'] = $userinfo['gender']=='男'?1:2;
        }

        $data = array(
            'nickname'=>$userinfo['nickname'],
            'avatar'=>$userinfo['headimgurl'],
            'province'=>$userinfo['province'],
            'city'=>$userinfo['city'],
            'gender'=>$userinfo['sex'],
            'comefrom'=>'h5app_sns_'.$sns
        );

        $openid = trim($_GPC['openid']);
        if($sns=='qq'){
            $data['openid_qq'] = trim($_GPC['openid']);
            $openid = 'sns_qq_'.trim($_GPC['openid']);
        }
        if($sns=='wx'){
            $data['openid_wx'] = trim($_GPC['openid']);
            $openid = 'sns_wx_'.trim($_GPC['openid']);
        }

        $member = $this->getMember($openid);

        if (empty($member)){
            $data['openid'] = $userinfo['openid'];
            $data['uniacid'] = $_W['uniacid'];
            $data['comefrom'] = 'sns_'.$sns;
            $data['createtime'] = time();
            $data['salt'] = m('account')->getSalt();
            $data['pwd'] = rand(10000, 99999).$data['salt'];
            pdo_insert('elapp_shop_member', $data);
            if(method_exists(m('member'),'memberRadisCountDelete')) {
                m('member')->memberRadisCountDelete(); //清除会员统计radis缓存
            }
            return pdo_insertid();
        }
        elseif(empty($member['bindsns']) || $member['bindsns']==$sns){
            pdo_update('elapp_shop_member', $data, array('id'=>$member['id'], 'uniacid'=>$_W['uniacid']));
            return $member['id'];
        }
    }

    /**
     * @param array $level 数组中有两个参数 一个是等级,一个是新等级
     * @param array $levels 分销商等级数组
     * @return bool 如果新等级大于 原等级 则返回true 否则 false
     */
    public function compareLevel(array $level,array $levels = array())
    {
        global $_W;
        $levels = !empty($levels) ? $levels : $this->getLevels();
        $old_key = -1;
        $new_key = -1;

        foreach ($levels as $kk=>$vv ){
            if ($vv['id'] == $level[0]){
                $old_key = $vv['level'];
            }
            if ($vv['id'] == $level[1]){
                $new_key = $vv['level'];
            }
        }

        return $new_key>$old_key;
    }

    /**
     * 微信获取网页openid整合
     * @param $appid APPID;
     * @param $secret SECRET;
     * @param $snsapi 类型有snsapi_userinfo,snsapi_base;
     * @param $expired 本地cookie缓存过期时间 默认600秒
     * @return array(openid,nickname,sex,province,city,country,headimgurl,privilege,[unionid])
     */
    public function wxuser($appid,$secret,$snsapi='snsapi_base',$expired='600'){
        global $_W;
        $wxuser = $_COOKIE[$_W['config']['cookie']['pre'] . $appid];
        if ($wxuser === null) {
            $http='http://';
            if (isset($_W['config']['setting']['https']) && !empty($_W['config']['setting']['https'])) {
                $http='https://';
            }
            $code = isset($_GET['code'])?$_GET['code']:'';

            if(!$code){
                $url = $http.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'];
                $oauth_url='https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.urlencode($url).'&response_type=code&scope='.$snsapi.'&state=wxbase#wechat_redirect';
                header('Location: ' . $oauth_url);
                exit;
            }
            load()->func('communication');
            $getOauthAccessToken = ihttp_get('https://api.weixin.qq.com/sns/oauth2/access_token?appid='.$appid.'&secret='.$secret.'&code='.$code.'&grant_type=authorization_code');
            $json = json_decode($getOauthAccessToken['content'],true);

            if (!empty($json['errcode']) && ($json['errcode'] == '40029' || $json['errcode'] == '40163')){
                $url = $http.$_SERVER['HTTP_HOST'].$_SERVER['REQUEST_URI'].(strpos($_SERVER['REQUEST_URI'],'?')?'':'?');
                $parse=parse_url($url);
                if(isset($parse['query'])){
                    parse_str($parse['query'],$params);
                    unset($params['code']);
                    unset($params['state']);
                    $url = $http.$_SERVER['HTTP_HOST'].$parse['path'].'?'.http_build_query($params);
                }
                $oauth_url='https://open.weixin.qq.com/connect/oauth2/authorize?appid='.$appid.'&redirect_uri='.urlencode($url).'&response_type=code&scope='.$snsapi.'&state=wxbase#wechat_redirect';
                header('Location: ' . $oauth_url);
                exit;
            }
            if( $snsapi == "snsapi_userinfo" ){
                $userinfo = ihttp_get('https://api.weixin.qq.com/sns/userinfo?access_token='.$json['access_token'].'&openid='.$json['openid'].'&lang=zh_CN');
                $userinfo = $userinfo['content'];
            }
            elseif ( $snsapi == "snsapi_base" )
            {
                $userinfo = array();
                $userinfo['openid'] = $json['openid'];
            }
            $userinfostr = json_encode($userinfo);
            isetcookie($appid,$userinfostr,$expired);
            return $userinfo;
        }else{
            return json_decode($wxuser,true);
        }
    }

    /**
     * 会员统计缓存创建
     */
    public function memberRadisCount($key,$value=false){
        global $_W;
        $redis = redis();
        if (!is_error($redis)) {
            if(empty($value)){
                if ($redis->get($key) != false) {  //判断key值所对应的值是否存在
                    return $redis->get($key);
                }else{
                    return false;
                }
            }else{
                $redis->set($key, $value, array('nx', 'ex' => '3600'));  //设置缓存  过期时间默认为1小时
            }
        }
    }

    /**
     * 会员统计缓存清空
     */
    public function memberRadisCountDelete(){
        global $_W;
        $open_redis = function_exists('redis') && !is_error(redis());
        if($open_redis){ //判断redisshi
            $redis = redis();
            $keysArr = $redis -> keys("elapp_{$_W['uniacid']}_member*"); //查询出所有会员统计的缓存的key值
            if(!empty($keysArr) && is_array($keysArr)){
                foreach($keysArr as $k => $v){
                    $redis -> del($v);  //清除所有会员统计的缓存
                }
            }
        }
    }

    public function deleterepeat($openid = '')
    {
        global $_W;

        $uid = $openid;

        if (!empty($uid)) {
            $result = pdo_fetchall("SELECT id,openid,createtime FROM ".tablename('elapp_shop_member')." WHERE uniacid=:uniacid and openid=:openid order by createtime DESC",array('uniacid' => $_W['uniacid'],'openid' => $uid));
            $count = count($result);
            if ($count > 1){
                pdo_delete('elapp_shop_member',array('id'=>$result['0']['id'],'uniacid'=>$_W['uniacid']));
            }
        }

    }

    /**
     * 身份证信息工具idCardTool
     */
    #获取性别
    function get_sex($idcard) {
        if(empty($idcard)) return null;
        $sexint = (int) substr($idcard, 16, 1);
        //return $sexint % 2 === 0 ? '女' : '男';
        return $sexint % 2 === 0 ? 0 : 1;
    }      
    #获取生日 
    function get_birthday($idcard) {
        if(empty($idcard)) return null;
        $bir = substr($idcard, 6, 8);
        $year = (int) substr($bir, 0, 4);
        $month = (int) substr($bir, 4, 2);
        $day = (int) substr($bir, 6, 2);
        $birthday = array(
            'birthyear' => intval($year), 
			'birthmonth' => intval($month), 
			'birthday' => intval($day),
        );
        //return $year . "-" . $month . "-" . $day;
        return $birthday;
    }     
    #获取年龄 
    function get_age($idcard){ 
        if(empty($idcard)) return null;
        #  获得出生年月日的时间戳
        $date = strtotime(substr($idcard,6,8));
        #  获得今日的时间戳
        $today = strtotime('today');
        #  得到两个日期相差的大体年数
        $diff = floor(($today-$date)/86400/365);
        #  strtotime加上这个年数后得到那日的时间戳后与今日的时间戳相比
        //$age = strtotime(substr($idcard,6,8).' +'.$diff.'years')-->$today?($diff+1):$diff;
        return $diff;
    }     
    //获取地址 
    function get_address($idcard, $type=1){
        if(empty($idcard)) return null;
        $address = include('./address.php');
        switch ($type) {
            case 1:
            # 截取前六位数(获取基体到县区的地址)
            $key = substr($idcard,0,6);
            if(!empty($address[$key])) return $address[$key];
            # 截取前两位数(没有基体到县区的地址就获取省份)
            $key = substr($idcard,0,2);
            if(!empty($address[$key])) return $address[$key];
            # 都没有
            return '未知地址';
                break;
            case 2:
            # 截取前两位数(只获取省份)
            $key = substr($idcard,0,2);
            if(!empty($address[$key])) return $address[$key];
                break;
            default:
            return null;
                break;
        }
    }      
    #转化大小写 验证是否正确身份证
    function isIdCard($idcard){
        #  转化为大写，如出现x
        $idcard = strtoupper($idcard);
        #  加权因子
        $wi = array(7, 9, 10, 5, 8, 4, 2, 1, 6, 3, 7, 9, 10, 5, 8, 4, 2);
        $ai = array('1', '0', 'X', '9', '8', '7', '6', '5', '4', '3', '2');
        #  按顺序循环处理前17位
        $sigma = 0;
        #  提取前17位的其中一位，并将变量类型转为实数
        for ($i = 0; $i < 17; $i++) {
            $b = (int)$idcard[$i];
            #  提取相应的加权因子
            $w = $wi[$i];
            #  把从身份证号码中提取的一位数字和加权因子相乘，并累加
            $sigma += $b * $w;
        }
        #  计算序号
        $sidcard = $sigma % 11;
        #  按照序号从校验码串中提取相应的字符。
        $check_idcard = $ai[$sidcard];
        if ($idcard[17] == $check_idcard) {
            return true;
        } else {
            return false;
        }
    }      
    #获取生肖 
    function get_zodiac($idcard){ //
        if(empty($idcard)) return null;
        $start = 1901;
        $end = (int)substr($idcard, 6, 4);
        $x = ($start - $end) % 12;
        $val = '';
        if ($x == 1 || $x == -11) $val = '鼠';
        if ($x == 0)$val = '牛';
        if ($x == 11 || $x == -1) $val = '虎';
        if ($x == 10 || $x == -2) $val = '兔';
        if ($x == 9 || $x == -3)  $val = '龙';
        if ($x == 8 || $x == -4)  $val = '蛇';
        if ($x == 7 || $x == -5)  $val = '马';
        if ($x == 6 || $x == -6)  $val = '羊';
        if ($x == 5 || $x == -7)  $val = '猴';
        if ($x == 4 || $x == -8)  $val = '鸡';
        if ($x == 3 || $x == -9)  $val = '狗';
        if ($x == 2 || $x == -10) $val = '猪';
        return $val;
    }      
    #获取星座 
    function get_starsign($idcard){
        if(empty($idcard)) return null;
        $b = substr($idcard, 10, 4);
        $m = (int)substr($b, 0, 2);
        $d = (int)substr($b, 2);
        $val = '';
        if(($m == 1 && $d <= 21) || ($m == 2 && $d <= 19)){
            $val = "水瓶座";
                }else if (($m == 2 && $d > 20) || ($m == 3 && $d <= 20)){
            $val = "双鱼座";
                }else if (($m == 3 && $d > 20) || ($m == 4 && $d <= 20)){
            $val = "白羊座";
                }else if (($m == 4 && $d > 20) || ($m == 5 && $d <= 21)){
            $val = "金牛座";
                }else if (($m == 5 && $d > 21) || ($m == 6 && $d <= 21)){
            $val = "双子座";
                }else if (($m == 6 && $d > 21) || ($m == 7 && $d <= 22)){
            $val = "巨蟹座";
                }else if (($m == 7 && $d > 22) || ($m == 8 && $d <= 23)){
            $val = "狮子座";
                }else if (($m == 8 && $d > 23) || ($m == 9 && $d <= 23)){
            $val = "处女座";
                }else if (($m == 9 && $d > 23) || ($m == 10 && $d <= 23)){
            $val = "天秤座";
                }else if (($m == 10 && $d > 23) || ($m == 11 && $d <= 22)){
            $val = "天蝎座";
                }else if (($m == 11 && $d > 22) || ($m == 12 && $d <= 21)){
            $val = "射手座";
                }else if (($m == 12 && $d > 21) || ($m == 1 && $d <= 20)){
            $val = "魔羯座";
        }
        return $val;
    }

    /**
     * 通过id/openid获取会员的身份角色关系
     * <AUTHOR>
     * @date 23/5/27
     * @param $id_openid id/openid可传数组查询
     * @param $return 返回的查询结果，空则全部返回，元素为$fields的主键
     * @extend $return 如传入除$fields的其他一维数组或二维数组查询 field字段，可加入查询
     * @return false|mixed
     */
    function get_member_role_relation($id_openid=['id' => 0,'openid' => ''], $return = [], $glue = 'and') {
        global $_W;
        $condition  = ['uniacid' => $_W['uniacid']];
        $limit = 1;
        if ('and' == $glue) {
            if (!empty($id_openid['id'])) {
                $condition += ['id' => $id_openid['id']];
                $limit += is_array($id_openid['id']) ? count($id_openid['id']) : 1;
            }
            if (!empty($id_openid['openid'])) {
                $condition += ['openid' => $id_openid['openid']];
                $limit += is_array($id_openid['openid']) ? count($id_openid['openid']) : 1;
            }
        } elseif ('or' == $glue) {
            if (!empty($id_openid['id'])) {
                $condition['or'] = ['id' => $id_openid['id']];
            }
            if (!empty($id_openid['openid'])) {
                $condition['or'] += ['openid' => $id_openid['openid']];
            }

            //OR操作失败，不支持写法？
            /*$condition = array(
                'uniacid' => $_W['uniacid'],
                'OR' => array(
                    'id' => 12087,
                    'openid' => 'oyDB75_oJk90x5VUMTPQla4OOQ-Y'
                )
            );*/
        }
        $fields = [
            'onu' => ['onmid', 'onmid_create_time', 'fix_onmid'],
            'parent_clerk' => ['clerk_id', 'clerk_id_create_time', 'fix_clerk_id'],
            'clerk' => ['is_clerk', 'clerk_status', 'clerk_create_time', 'clerk_level', 'clerk_black'],
            'parent_mentor' => ['mentor_id', 'mentor_id_create_time'],
            'mentor' => ['is_mentor', 'mentor_status', 'mentor_create_time', 'mentor_black'],
            'inviter' => ['inviter_id', 'inviter_id_create_time'],
            'parent_copartner' => ['copartner_id', 'copartner_id_create_time', 'fix_copartner_id', 'copartner_account_id', 'copartner_account_id_create_time'],
            'copartner' => ['is_copartner', 'copartner_status', 'copartner_create_time', 'copartner_level', 'copartner_black'],
            'parent_doctor' => ['doctor_id', 'doctor_id_create_time', 'fix_doctor_id'],
            'doctor' => ['is_doctor', 'doctor_status', 'doctor_create_time', 'doctor_level', 'doctor_black']
        ];
        $query_fields = ['id', 'openid', 'mobile'];
        if (empty($return)) {
            $query_fields += array_reduce($fields, 'array_merge', array());
        } else {
            $intersect = array_intersect($return, array_keys($fields));
            foreach ($intersect as $key) {
                $query_fields += array_merge($query_fields, $fields[$key]);
            }
        }
        $member_role_relation = pdo_getall('elapp_shop_member', $condition, $query_fields, 'id', [], $limit);
        return $member_role_relation;
    }

    /**
     * 会员绑定医生关系 并列优先级关系还是业务递进关系？
     * @extend 可扩展绑定其他身份关系
     * @param $member_id 会员id
     * @param $bind_id 医生id，mid，parent_id
     * @return array|false|float|int|mixed|\Services_JSON_Error|string|void
     */
    function bind_relation($member_id, $bind_id) {
        $member_role_relation = $this->get_member_role_relation(['id' => [$member_id, $bind_id]]);
        if (!empty($member_role_relation)) {
            // 没有绑定clerk_id才允许绑定医生
            if (!empty($member_role_relation[$member_id]['clerk_id'])) {
                return result(1000100, '该用户已绑定店员身份');
            }
            if (!empty($member_role_relation[$member_id]['doctor_id'])) {
                return result(1000101, '该用户已绑定医生身份');
            }
            if (empty($member_role_relation[$bind_id]['is_doctor']) || empty($member_role_relation[$bind_id]['doctor_status']) || !empty($bind_id['doctor_black'])) {
                return result(1000102, '邀请人无医生权限');
            }
            $update_data = [
                'doctor_id' => $bind_id,
                'doctor_id_create_time' => time(),
                'copartner_account_id' => $member_role_relation[$bind_id]['copartner_account_id'],
                'copartner_account_id_create_time' => time(),
                'copartner_id' => $member_role_relation[$bind_id]['copartner_id'],
                'copartner_id_create_time' => time(),
            ];
            if (empty($member_role_relation[$member_id]['onmid'])) {
                $update_data['onmid'] = $bind_id;
                $update_data['onmid_create_time'] = time();
            }
            if (empty($member_role_relation[$member_id]['inviter_id'])) {
                $update_data['inviter_id'] = $bind_id;
                $update_data['inviter_id_create_time'] = time();
            }

            $result = pdo_update('elapp_shop_member', $update_data, ['id' => $member_id]);
            if ($result) {
                return result(0, '数据更新成功', $result);
            } else {
                return result(-1, '数据更新失败', $result);
            }
        }
    }

    /**
     * 关系绑定业务逻辑
     * @param int $memberId
     * @param int $referrerId
     * @return array|false|float|int|mixed|\Services_JSON_Error|string|void
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function relationBinding(int $memberId, int $referrerId): array
    {
        // 实例化绑定会员对象
        $member      = $this->findOrEmpty($memberId);
        $referrer    = $this->findOrEmpty($referrerId);
        if ($member->isEmpty() || $referrer->isEmpty()) {
            return result(-1, '绑定数据不存在');
        }

        // 实例化关系绑定对象
        $memberRelation = new MemberRelationBindingLogic();

        // 进行关系绑定操作
        $result = $memberRelation->checkBeforeBinding($member, $referrer);

        if ($result) {
            return result(0, '绑定成功', $result);
        } else {
            return result(-1, '绑定失败', $result);
        }
    }
    /**
     * 获取虚拟积分
     * @param $openid
     * @param $credittype
     * @param int $user_type 0 用户 1 合伙人
     * @return false|int|mixed
     */
    public function getVirtualPoint($user_id, $point_id, int $user_type = 0) {
        global $_W;

        $table = tablename("elapp_shop_virtual_point_user");
        $item = pdo_fetch("SELECT points FROM {$table} WHERE user_type=:user_type user_id=:user_id and point_id=:point_id limit 1",
            array( ":user_id" => $user_id, ':point_id' => $point_id, ':user_type'=>$user_type));

        $p = (isset($item['points']) && !empty($item['points']))?$item['points']:0;

        if ($user_type == 0) {
            $copartner_id = pdo_getcolumn('elapp_shop_member', ['id' => $user_id], 'copartner_id');
        } else {
            $copartner_id = $user_id;
        }

        // 获取copartner积分配置
        $int_mode = pdo_getcolumn('elapp_shop_virtual_point_copartner_set', ['copartner_id'=>$copartner_id,'point_id'=>$point_id],'int_mode');
        if ($int_mode == 1) {
            $p = (int)$p;
        }
        return $p;
    }

    public function changeVirtualPoint($user_id, $user_type, $points, $point_id, $remark)
    {
        // 用户积分
        $mp = pdo_get('elapp_shop_virtual_point_user', ['user_id' => $user_id,'user_type'=>$user_type, 'point_id' => $point_id]);
//        // 如果mp不存在或小于points，返回false
//        if (!empty($mp) && $mp['points'] + $points < 0) {
//            return false;
//        }

        if (empty($mp)) {
            pdo_insert('elapp_shop_virtual_point_user', [
                'user_id' => $user_id,
                'user_type' => $user_type,
                'point_id' => $point_id,
                'points' => $points,
                'update_time'=> time(),
            ]);
            $after = $points;
        } else {
            $after = $mp['points'] + $points;
            // 更新积分
            pdo_run('update ' . tablename('elapp_shop_virtual_point_user') . ' set points = points + ' . $points . ' where user_type = ' . $user_type . ' and user_id = ' . $user_id . ' and point_id = ' . $point_id);
        }

        // 添加积分变更记录
        $data = [
            //'uniacid' => $_W['uniacid'],
            'user_type' => $user_type,
            'user_id' => $user_id,
            'point_id' => $point_id,
            'points' => $points,
            'reason' => $remark,
            'after' => $after,
            'create_time' => time(),
        ];
        pdo_insert('elapp_shop_virtual_point_user_record', $data);

        return true;
    }

    public function getOpenid(int $memberId)
    {
        $cachekey = 'openid.for.mid.' . $memberId;
        return Cache::remember($cachekey, function () use ($memberId) {
            return pdo_getcolumn('elapp_shop_member', ['id' => $memberId], 'openid');
        }, 3600);
    }
}
