<?php

namespace app\core\model\member\history;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use think\model\relation\HasOne;

/**
 * 会员浏览历史记录模型类
 * class MemberHistoryModel
 * @package app\core\model\member\history
 * <AUTHOR>
 * @date 2024/07/03 21:07
 */
class MemberHistoryModel extends MicroEngineModel
{
    protected $name = 'member_history';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'goodsid',
                'times',
                'createtime',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid')->field(GoodsModel::scene_fields('default'));
    }
}