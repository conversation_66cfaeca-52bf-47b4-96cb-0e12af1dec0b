<?php
namespace app\core\model\member\card;

use app\core\model\MicroEngineModel;

class MemberCardModel extends MicroEngineModel {
    protected $name = 'member_card';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 获取器定义 start
    public function getThumbAttr($value)
    {
        return tomedia($value);
    }
    // 获取器 end

}


