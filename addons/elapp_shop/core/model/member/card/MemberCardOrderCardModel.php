<?php
namespace app\core\model\member\card;

use app\core\model\MicroEngineModel;

class MemberCardOrderCardModel extends MicroEngineModel {
    protected $name = 'member_card_order_card';
    protected $pk = 'id';

    /**
     * @desc 与goodsModel对象多对一相应关联
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     */
    public function card()
    {
        return $this->belongsTo(MemberCardModel::class, 'cardsid', 'id');
    }

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=>'id,orderid,cardsid,price,total,createtime as create_time',
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

}


