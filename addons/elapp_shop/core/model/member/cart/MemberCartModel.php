<?php

namespace app\core\model\member\cart;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use app\model\GoodsOptionModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 购物车模型类
 * class MemberCartModel
 * @package app\core\model\member\cart
 * <AUTHOR>
 * @date 2024/07/03 21:07
 */
class MemberCartModel extends MicroEngineModel
{
    protected $name = 'member_cart';
    protected $pk = 'id';
    protected string $alias = 'cart';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'goodsid',
                'total',
                'marketprice',
                'optionid',
                'createtime',
                'selected',
                'selectedadd',
                'isnewstore',
                'deleted',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid')->field(GoodsModel::scene_fields('default'))->bind((array)array_merge(GoodsModel::bindAttrs('common'), GoodsModel::bindAttrs('cart')));
    }

    /**
     * 一对一关联用户
     * @return HasOne
     */

    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id','member_id')->field(MemberModel::scene_fields('default'));
    }

    /**
     * 一对一关联商品规格
     * @return HasOne
     */
    function option(): HasOne
    {
        return $this->hasOne(GoodsOptionModel::class, 'id', 'optionid')->field(GoodsOptionModel::scene_fields('default'))->bind(GoodsOptionModel::bindAttrs('cart'));
    }
}