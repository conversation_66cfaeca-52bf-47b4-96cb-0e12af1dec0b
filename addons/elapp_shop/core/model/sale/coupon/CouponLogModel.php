<?php

namespace app\core\model\sale\coupon;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;
use app\model\MerchModel;
use think\model\relation\HasOne;

/**
 * 优惠券日志模型类
 * class CouponLogModel
 * @package app\core\model\sale\coupon
 * <AUTHOR>
 * @date 2024/07/23 21:07
 */
class CouponLogModel extends MicroEngineModel
{
    protected $name = 'coupon_log';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'openid',
                'member_id',
                'logno',
                'couponid',
                'status',
                'paystatus',
                'creditstatus',
                'createtime',
                'paytype',
                'getfrom',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商户
     * @return HasOne
     */
    function merch(): HasOne
    {
        return $this->hasOne(MerchModel::class, 'id', 'merchid')->field(MerchModel::scene_fields('default'));
    }
    /**
     * 一对一关联优惠券
     * @return HasOne
     */
    function coupon(): HasOne
    {
        return $this->hasOne(CouponModel::class, 'id', 'couponid')->field(CouponModel::scene_fields('default'));
    }
    /**
     * 一对一关联用户
     * @return HasOne
     */
    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id', 'member_id')->field(MemberModel::scene_fields('default'));
    }
}