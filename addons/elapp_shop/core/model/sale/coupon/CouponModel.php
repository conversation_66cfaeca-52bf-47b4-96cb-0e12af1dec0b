<?php

namespace app\core\model\sale\coupon;

use app\core\model\MicroEngineModel;
use app\model\MerchModel;
use think\model\relation\HasOne;

/**
 * 优惠券模型类
 * class CouponModel
 * @package app\core\model\sale\coupon
 * <AUTHOR>
 * @date 2024/07/23 21:07
 */
class CouponModel extends MicroEngineModel
{
    protected $name = 'coupon';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'catid',
                'couponname',
                'gettype',
                'getmax',
                'usetype',
                'returntype',
                'enough',
                'timelimit',
                'coupontype',
                'timedays',
                'timestart',
                'timeend',
                'discount',
                'deduct',
                'backtype',
                'backmoney',
                'backcredit',
                'backredpack',
                'backwhen',
                'thumb',
                'desc',
                'createtime',
                'total',
                'status',
                'extra',
                'money',
                'credit',
                'usecredit2',
                'displayorder',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商户
     * @return HasOne
     */
    function merch(): HasOne
    {
        return $this->hasOne(MerchModel::class, 'id', 'merchid')->field(MerchModel::scene_fields('default'));
    }
}