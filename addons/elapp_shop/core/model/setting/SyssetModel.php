<?php

namespace app\core\model\setting;

use app\core\model\MicroEngineModel;
use app\model\CacheModel;
use think\facade\Cache;

/**
 * 系统设置模型
 */
class SyssetModel extends MicroEngineModel
{
    protected $name = 'sysset';

    /**
     * @desc 获取系统设置 可全部获取，可指定key获取,可指定type获取,指定key/type不存在返回全部
     * @param string $type [sets plugins sec]
     * @param $key string|array
     * @return mixed|string
     */
    function getSysSet(string $type = '', $key = '')
    {
        $type = $type ? $type : '*';
        $cacheKey = self::$uniacid . '_sysset_' . $type;
        $result = Cache::tag('sysset')->remember($cacheKey, function () use ($type) {
            return $this->field($type)->findOrEmpty()->toArray();
        });

        if ($type == '*') return $result;
        $result = iunserializer($result[$type]);
        if (is_array($key)) {
            return array_intersect_key($result, array_flip($key));
        }
        if ($key == '') return $result;
        return $result[$key] ?? [];
    }

    function onAfterUpdate()
    {
        Cache::tag('sysset')->clear();
    }
}