<?php

namespace app\core\model\quick;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use app\model\GoodsOptionModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 快速购买购物车 模型类
 * class QuickCartModel
 * @package app\core\model\quick
 * <AUTHOR>
 * @date 2024/07/25 21:07
 */
class QuickCartModel extends MicroEngineModel
{
    protected $name = 'quick_cart';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'openid',
                'member_id',
                'quickid',
                'goodsid',
                'total',
                'marketprice',
                'optionid',
                'createtime',
                'selected',
                'selectedadd',
                'deleted'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid')->field(GoodsModel::scene_fields('default'));
    }

    /**
     * 一对一关联用户
     * @return HasOne
     */

    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id','member_id')->field(MemberModel::scene_fields('default'));
    }

    /**
     * 一对一关联商品规格
     * @return HasOne
     */
    function option(): HasOne
    {
        return $this->hasOne(GoodsOptionModel::class, 'id', 'optionid')->field(GoodsOptionModel::scene_fields('default'));
    }
}