<?php

namespace app\core\model\merch;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 多商户注册模型
 * class MerchRegModel
 */
class MerchRegModel extends MicroEngineModel
{
    protected $name = 'copartner_reg';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [

            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'mid')
            ->field(MemberModel::scene_fields('default'));
    }
}