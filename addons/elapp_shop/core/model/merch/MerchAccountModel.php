<?php

namespace app\core\model\merch;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 多商户账户模型
 * Class MerchAccountModel
 * @package app\core\model\merch
 * <AUTHOR>
 * @time 2024-07-29 02:30
 */
class MerchAccountModel extends MicroEngineModel
{
    protected $name = 'merch_account';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [

            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function member() {
        return $this->belongsTo(MemberModel::class, 'mid', 'id')
            ->field(MemberModel::scene_fields('default'));
    }

    public function merch() {
        return $this->belongsTo(MerchUserModel::class, 'merch_id', 'id')
            ->field(MerchUserModel::scene_fields('default'));
    }
}