<?php

namespace app\core\model\merch;

use app\core\model\AttrTrait\GetLogoAttrTrait;
use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 多商户用户模型
 * Class MerchUserModel
 * @package app\core\model\merch
 * <AUTHOR>
 * @date 2024-07-29 02:57
 */
class MerchUserModel extends MicroEngineModel
{
    protected $name = 'merch_user';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [

            ],
            'default' => '*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function merchAccounts() {
        return $this->hasMany(MerchAccountModel::class, 'merch_id', 'id');
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'mid')
            ->field(MemberModel::scene_fields('default'));
    }

    // 获取器开始
    use GetLogoAttrTrait;
    public function getMoneyAttr($value) {
        return json_decode($value, true);
    }
    public function getRelateAttr($value) {
        return json_decode($value, true);
    }
    // 获取器结束
}