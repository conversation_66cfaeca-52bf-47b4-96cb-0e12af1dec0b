<?php

namespace app\core\model\diyform;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use app\model\GoodsOptionModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 自定义表单用户表单收集 模型
 * class DiyformTempModel
 * @package app\core\model\diyform
 * <AUTHOR>
 * @date 2024/07/25 21:07
 */
class DiyformTempModel extends MicroEngineModel
{
    protected $name = 'diyform_temp';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'typeid',
                'cid',
                'diyformfields',
                'fields',
                'openid',
                'type',
                'diyformid',
                'diyformdata',
                'carrier_realname',
                'carrier_mobile'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品ID
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'cid')->field(GoodsModel::scene_fields('default'));
    }

    /**
     * 一对一关联用户
     * @return HasOne
     */

    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id','member_id')->field(MemberModel::scene_fields('default'));
    }

    /**
     * 一对一关联表单类型
     * @return HasOne
     */
    function type(): HasOne
    {
        return $this->hasOne(DiyformTypeModel::class, 'id', 'type')->field(DiyformTypeModel::scene_fields('default'));
    }

    /**
     * 一对一关联表单类型
     * @return HasOne
     */
    function diyform(): HasOne
    {
        return $this->hasOne(DiyformTypeModel::class, 'id', 'diyformid')->field(DiyformTypeModel::scene_fields('default'));
    }

    // 获取器定义 开始
    public function getFieldsAttr($value)
    {
        return unserialize($value);
    }
    public function getDiyformfieldsAttr($value)
    {
        return unserialize($value);
    }
    public function getDiyformdataAttr($value)
    {
        return unserialize($value);
    }
    // 获取器定义 结束
}