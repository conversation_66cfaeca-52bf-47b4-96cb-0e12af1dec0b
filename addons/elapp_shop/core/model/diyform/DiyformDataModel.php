<?php

namespace app\core\model\diyform;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 自定义表单数据记录 模型
 * class DiyformDataModel
 * @package app\core\model\diyform
 * <AUTHOR>
 * @date 2024/07/25 21:07
 */
class DiyformDataModel extends MicroEngineModel
{
    protected $name = 'diyform_data';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'openid',
                'typeid',
                'member_id',
                'cid',
                'diyformfields',
                'fields',
                'type'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联分类
     * @return HasOne
     */
    function cate(): HasOne
    {
        return $this->hasOne(DiyformCategoryModel::class, 'id', 'cate')->field(DiyformCategoryModel::scene_fields('default'));
    }

    /**
     * 一对一关联表单类型
     * @return HasOne
     */
    function type(): HasOne
    {
        return $this->hasOne(DiyformTypeModel::class, 'id', 'typeid')->field(DiyformTypeModel::scene_fields('default'));
    }

    /**
     * 一对一关联用户
     * @return HasOne
     */

    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id','member_id')->field(MemberModel::scene_fields('default'));
    }

    // 获取器定义 开始
    public function getFieldsAttr($value)
    {
        return unserialize($value);
    }
    public function getDiyformfieldsAttr($value)
    {
        return unserialize($value);
    }
    // 获取器定义 结束
}