<?php

namespace app\core\model\diyform;

use app\core\model\MicroEngineModel;

/**
 * 自定义表单分类 模型
 * class DiyformCategoryModel
 * @package app\core\model\diyform
 * <AUTHOR>
 * @date 2024/07/25 21:07
 */
class DiyformCategoryModel extends MicroEngineModel
{
    protected $name = 'diyform_category';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'name',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
}