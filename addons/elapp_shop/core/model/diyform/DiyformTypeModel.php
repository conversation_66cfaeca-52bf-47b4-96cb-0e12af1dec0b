<?php

namespace app\core\model\diyform;

use app\core\model\MicroEngineModel;
use think\model\relation\HasOne;

/**
 * 自定义表单模板 模型
 * class DiyformTypeModel
 * @package app\core\model\diyform
 * <AUTHOR>
 * @date 2024/07/29 16:07
 */
class DiyformTypeModel extends MicroEngineModel
{
    protected $name = 'diyform_type';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'cate',
                'title',
                'fields',
                'usedata',
                'alldata',
                'status',
                'savedata',
                'open_protocol',
                'applytitle',
                'applycontent'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联分类
     * @return HasOne
     */
    function cate(): HasOne
    {
        return $this->hasOne(DiyformCategoryModel::class, 'id', 'cate')->field(DiyformCategoryModel::scene_fields('default'));
    }

    // 获取器定义 开始
    public function getFieldsAttr($value)
    {
        return unserialize($value);
    }
    // 获取器定义 结束
}