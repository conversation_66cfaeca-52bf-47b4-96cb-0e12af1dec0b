<?php
// +----------------------------------------------------------------------
// | QMGXYF 全民共享药房 [ WE CAN DO IT MORE SIMPLE ]
// +----------------------------------------------------------------------
// | 全民共享药房管理系统是一款基于微信公众平台的多功能服务商城，致力于为广大用户提供更高效便捷的平台服务。
// | QMGXYF 官网：https://www.qmgyf.com
// | Copyright (c) 2023-2024 https://www.qmgyf.com All rights reserved.
// | QMGXYF技术团队  版权所有  拥有最终解释权
// +----------------------------------------------------------------------
// | Licensed ( http://www.apache.org/licenses/LICENSE-2.0 )
// +----------------------------------------------------------------------
// | Author: QMGXYF <<EMAIL>>
// +----------------------------------------------------------------------

namespace app\core\model;

use app\BaseModel;
use app\com\service\MicroEngineService;

/**
 * 基础模型
 * Class MicroEngineModel
 * @package app\common\model
 */
class MicroEngineModel extends BaseModel
{
    protected static $uniacid;

    protected $readonly = ['uniacid'];
    protected $globalScope = ['uniacid'];

    protected static function init()
    {
        if (MicroEngineService::isMicroEngine()) {
            global $_W;
            self::$uniacid = $_W['uniacid'];
        }
    }

    /**
     * 只查询当前平台下的数据
     * @param $query
     * @return void
     */
    public function scopeUniacid($query)
    {
        if (MicroEngineService::isMicroEngine()) {
            $query->where('uniacid', self::$uniacid);
        }
    }

    /**
     * 模型事件
     * @param $model
     * @return void
     */
    public static function onBeforeInsert($model)
    {
        if (MicroEngineService::isMicroEngine()) {
            $model->uniacid = self::$uniacid;
        }
    }

}