<?php

namespace app\core\model\exchange;

use app\core\model\MicroEngineModel;
use app\model\GoodsModel;
use app\model\GoodsOptionModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 兑换中心购物车 模型类
 * class ExchangeCartModel
 * @package app\core\model\exchange
 * <AUTHOR>
 * @date 2024/07/25 21:07
 */
class ExchangeCartModel extends MicroEngineModel
{
    protected $name = 'exchange_cart';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'merchid',
                'openid',
                'member_id',
                'goodsid',
                'title',
                'total',
                'marketprice',
                'optionid',
                'groupid',
                'serial',
                'selected',
                'deleted'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联商品
     * @return HasOne
     */
    function goods(): HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid')->field(GoodsModel::scene_fields('default'));
    }

    /**
     * 一对一关联用户
     * @return HasOne
     */

    function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id','member_id')->field(MemberModel::scene_fields('default'));
    }

    /**
     * 一对一关联商品规格
     * @return HasOne
     */
    function option(): HasOne
    {
        return $this->hasOne(GoodsOptionModel::class, 'id', 'optionid')->field(GoodsOptionModel::scene_fields('default'));
    }
}