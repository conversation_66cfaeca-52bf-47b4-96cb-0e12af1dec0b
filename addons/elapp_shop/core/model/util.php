<?php
namespace app\model;

class UtilModel {
    
    public function getExpressList($express, $expresssn,$mobile='') {
        global $_W;
        
        $express_set = $_W['shopset']['express'];
        if (empty($express_set)) return result(-1, '未配置快递公司');
        
        $express = $express=="jymwl" ? "jiayunmeiwuliu" : $express;
        $express = $express=="TTKD" ? "tiantian" : $express;
        $express = $express=="jjwl" ? "jiajiwuliu" : $express;
        $express = $express=="zhongtiekuaiyun" ? "ztky" : $express;
        
        if ($express_set['express_type'] == 0) { //快递鸟
            $list = $this->getExpressBird($express, $expresssn,$mobile, $express_set['express_bird']);
        } else if ($express_set['express_type'] == 1) { //快递100
            $list = $this->getExpressOneHundred($express, $expresssn,$mobile='', $express_set['express_one_hundred']);
        } else if ($express_set['express_type'] == 2) { // 阿里云接口
            $list = $this->getExpressAli($express, $expresssn,$mobile='', $express_set['express_ali']);
        }
        
        return $list;
    }
    
    // 根据IP获取城市
    function getIpAddress() {
        $ipContent = file_get_contents("http://int.dpool.sina.com.cn/iplookup/iplookup.php?format=js");
        $jsonData = explode("=", $ipContent);
        $jsonAddress = substr($jsonData[1], 0, -1);
        return $jsonAddress;
    }
    
    function checkRemoteFileExists($url) {
        $curl = curl_init($url);
        //不取回数据
        curl_setopt($curl, CURLOPT_NOBODY, true);
        //发送请求
        $result = curl_exec($curl);
        $found = false;
        if ($result !== false) {
            //检查http响应码是否为200
            $statusCode = curl_getinfo($curl, CURLINFO_HTTP_CODE);
            if ($statusCode == 200) {
                $found = true;
            }
        }
        curl_close($curl);
        return $found;
    }
    
    /**
     * 计算两组经纬度坐标 之间的距离
     * params ：lat1 纬度1； lng1 经度1； lat2 纬度2； lng2 经度2； len_type （1:m or 2:km);
     * return m or km
     */
    function GetDistance($lat1, $lng1, $lat2, $lng2, $len_type = 1, $decimal = 2)
    {
        $pi = 3.1415926;
        $er = 6378.137;
        
        $radLat1 = $lat1 * $pi / 180.0;
        $radLat2 = $lat2 * $pi / 180.0;
        $a = $radLat1 - $radLat2;
        $b = ($lng1 * $pi / 180.0) - ($lng2 * $pi / 180.0);
        $s = 2 * asin(sqrt(pow(sin($a/2),2) + cos($radLat1) * cos($radLat2) * pow(sin($b/2),2)));
        $s = $s * $er;
        $s = round($s * 1000);
        if ($len_type > 1)
        {
            $s /= 1000;
        }
        return round($s, $decimal);
    }
    
    function multi_array_sort($multi_array, $sort_key, $sort = SORT_ASC){
        if(is_array($multi_array)){
            foreach ($multi_array as $row_array){
                if(is_array($row_array)){
                    $key_array[] = $row_array[$sort_key];
                }else{
                    return false;
                }
            }
        }else{
            return false;
        }
        
        array_multisort($key_array, $sort , $multi_array);
        
        return $multi_array;
    }
    
    
    function get_area_config_data($uniacid = 0){
        global $_W;
        
        if (empty($uniacid)) {
            $uniacid = $_W['uniacid'];
        }
        $sql = 'select * from '. tablename('elapp_shop_area_config').' where uniacid=:uniacid limit 1';
        $data = pdo_fetch($sql, array(':uniacid'=>$uniacid));
        
        
        return $data;
        
    }
    
    function get_area_config_set(){
        global $_W;
        
        $data = m('common')->getSysset('area_config');
        if (empty($data)) {
            $data = $this->get_area_config_data();
            
        }
        return $data;
        
    }
    
    function pwd_encrypt($string, $operation, $key='key'){
        $key=md5($key);
        $key_length=strlen($key);
        $string=$operation=='D'?base64_decode($string):substr(md5($string.$key),0,8).$string;
        $string_length=strlen($string);
        $rndkey=$box=array();
        $result='';
        for($i=0;$i<=255;$i++){
            $rndkey[$i]=ord($key[$i%$key_length]);
            $box[$i]=$i;
        }
        for($j=$i=0;$i<256;$i++){
            $j=($j+$box[$i]+$rndkey[$i])%256;
            $tmp=$box[$i];
            $box[$i]=$box[$j];
            $box[$j]=$tmp;
        }
        for($a=$j=$i=0;$i<$string_length;$i++){
            $a=($a+1)%256;
            $j=($j+$box[$a])%256;
            $tmp=$box[$a];
            $box[$a]=$box[$j];
            $box[$j]=$tmp;
            $result.=chr(ord($string[$i])^($box[($box[$a]+$box[$j])%256]));
        }
        if($operation=='D'){
            if(substr($result,0,8)==substr(md5(substr($result,8).$key),0,8)){
                return substr($result,8);
            }else{
                return'';
            }
        }else{
            return str_replace('=','',base64_encode($result));
        }
    }
    
    function location($lat, $lng){
        
        $newstore_plugin = p('newstore');
        $store_plugin = m('store');
        if ($newstore_plugin) {
            $newstore_data = m('common')->getPluginset('newstore');
            $key = $newstore_data['baidukey'];
        }elseif($store_plugin) {
            $store_data = m('common')->getPluginset('store');
            $key = $store_data['baidukey'];            
        }
        
        if (empty($key)) {
            $key = '81aVwXTQajNSra4BAPxVgGT3oNI8bpay';//修复百度地图AK错误 hlei 20210227
        }
        
        $url = "https://api.map.baidu.com/geocoder/v2/?callback=renderReverse&location=".$lat.",".$lng."&output=json&pois=1&ak=" . $key;
        
        $fileContents = file_get_contents($url);
        $contents = ltrim($fileContents, 'renderReverse&&renderReverse(');
        $contents = rtrim($contents, ')');
        $data =  json_decode($contents,true);
        return $data;
    }
    
    //通过地址信息获取坐标（高德）
    function geocode($address,$key=0){
        if(empty($key)){
            $key = '7e56a024f468a18537829cb44354739f';
        }
        $address = str_replace(' ','',$address);
        $url = "https://restapi.amap.com/v3/geocode/geo?address=".$address."&key=" . $key;
        $contents = file_get_contents($url);
        $data =  json_decode($contents,true);
        return $data;
    }
    
    /**
     * @param $express
     * @param $expresssn
     * @param string $mobile
     * @param $express_set
     * @return array|bool
     */
    private function getExpressBird($express, $expresssn,$mobile='', $express_set) {
        global $_W;
        if (empty($express_set['express_bird_userid']) || empty($express_set['express_bird_apikey'])) {
            return error('参数配置错误');
        }
        
        if (!empty($express_set['express_bird_cache']) && $express_set['express_bird_cache']>0) {
            // 查询缓存 如果缓存中有&&没过期直接使用
            $cache_time = $express_set['cache'] * 60;
            $cache = pdo_fetch("SELECT * FROM".tablename("elapp_shop_express_cache")."WHERE express=:express AND expresssn=:expresssn LIMIT 1", array('express'=>$express, 'expresssn'=>$expresssn));
            if($cache['lasttime']+$cache_time>=time() && !empty($cache['datas'])){
                return iunserializer($cache['datas']);
            }
        }
        
        $url = 'https://api.kdniao.com/Ebusiness/EbusinessOrderHandle.aspx'; //正式
        
        $expressInfo =  pdo_fetch("SELECT * FROM".tablename("elapp_shop_express")."WHERE express=:express LIMIT 1", array('express'=>$express));
        
        if (empty($expressInfo)) {
            return false;
        }
        
        $expressEncoding = $expressInfo['coding'];
        
        if ($expressEncoding =='JITU'){
            $expressEncoding = 'JTSD';
        }
        
        
        $requestData= array(
        //'OrderCode' => '',  //订单编号 可选
            'ShipperCode' => $expressEncoding,
            'LogisticCode' => $expresssn
        );
        if ($expressEncoding == 'SF') {
            $cunstomername = substr($mobile, -4);
            $requestData['CustomerName'] = $cunstomername;
        }
        
        //京东快递添加商户编码
        if ($expressEncoding == 'JD') {
        //if (empty($express_set['express_bird']['customer_id'])) {
        //  return error('商户编码配置错误');
        //}
            $requestData['CustomerName'] = $express_set['express_bird_customer_name'];
        }
        $requestData = json_encode($requestData);
        
        $datas = array(
            'EBusinessID' => $express_set['express_bird_userid'],
            'RequestType' => '1002',
            'RequestData' => urlencode($requestData) ,
            'DataType' => '2',
        );
        //获取签名
        $datas['DataSign'] = urlencode(base64_encode(md5($requestData.$express_set['express_bird_apikey'])));
        $response = ihttp_request($url, $datas);
        $expressData = json_decode($response['content'], true);
        if( $expressData['Success'] ==false){
            //付费接口
            $datas = array(
                'EBusinessID' => $express_set['express_bird_userid'],
                'RequestType' => '8001',
                'RequestData' => urlencode($requestData) ,
                'DataType' => '2',
            );
            //获取签名
            $datas['DataSign'] = urlencode(base64_encode(md5($requestData.$express_set['express_bird_apikey'])));
            $response = ihttp_request($url, $datas);
            $expressData = json_decode($response['content'], true);
        }
        //物流信息
        $list = array();
        if(!empty($expressData['Traces']) && is_array($expressData['Traces'])){
            foreach ($expressData['Traces'] as $index=>$data){
                $list[] = array(
                    'time' => trim($data['AcceptTime']),
                    'step' => trim($data['AcceptStation'])
                );
            }
            $list = array_reverse($list);
        }
        
        
        if($express_set['express_bird_cache']>0 && !empty($list)){
            // 防止并发请求导致重复插入
            $cache = pdo_fetch("SELECT id FROM ".tablename("elapp_shop_express_cache")." WHERE expresssn=:expresssn LIMIT 1", array('expresssn'=>$expresssn));
            
            // 更新缓存
            if(empty($cache)){
                pdo_insert("elapp_shop_express_cache", array('expresssn'=>$expresssn, 'express'=>$express, 'lasttime'=>time(), 'datas'=>iserializer($list),'state'=>$expressData['State'],'state_ex'=>$expressData['StateEx'],'location'=>$expressData['location']));
            }else{
                pdo_update("elapp_shop_express_cache", array('lasttime'=>time(), 'datas'=>iserializer($list),'state'=>$expressData['State'],'state_ex'=>$expressData['StateEx'],'location'=>$expressData['location']), array('id'=>$cache['id']));
            }
        }
        
        return $list;
    }
    
    /**
     * @param $express
     * @param $expresssn
     * @param string $mobile
     * @param $express_set
     * @return array
     */
    private function getExpressOneHundred($express, $expresssn,$mobile='', $express_set) {
        
        load()->func('communication');
        // 如果开启正式接口 则先调用正式接口
        if(!empty($express_set['isopen']) && !empty($express_set['apikey'])){
            if(!empty($express_set['cache']) && $express_set['cache']>0){
                // 查询缓存 如果缓存中有&&没过期直接使用
                $cache_time = $express_set['cache'] * 60;
                $cache = pdo_fetch("SELECT * FROM ".tablename("elapp_shop_express_cache")." WHERE express=:express AND expresssn=:expresssn LIMIT 1", array('express'=>$express, 'expresssn'=>$expresssn));
                if($cache['lasttime']+$cache_time>=time() && !empty($cache['datas'])){
                    return iunserializer($cache['datas']);
                }
            }
            if($express_set['isopen']==1){
                $url = "http://api.kuaidi100.com/api?id={$express_set['apikey']}&com={$express}&num={$expresssn}";
                $params = array();
            }else{
                $url = "http://poll.kuaidi100.com/poll/query.do";
                $params = array('customer' => $express_set['customer'], 'param' => json_encode(array('com' => $express, 'num' => $expresssn)));
                $params['sign'] = md5($params["param"].$express_set['apikey'].$params["customer"]);
                $params['sign'] = strtoupper($params["sign"]);
                $params['phone'] = $mobile;
            }
            
            $response = ihttp_post($url, $params);
            $content = $response['content'];
            $info = json_decode($content, true);
        }
        
        // 未开启正式接口或者正式接口返回数据错误则调用默认接口
        if(!isset($info) || empty($info['data']) || !is_array($info['data'])) {
            //因为这个接口返回的数据都是错乱的 不一定返回什么的快递信息. 所以给他注释掉了
            //$url = "https://www.kuaidi100.com/query?type={$express}&postid={$expresssn}&id=1&valicode=&temp=";
            //$response = ihttp_request($url);
            //$content = $response['content'];
            //$info = json_decode($content, true);
            $useapi = false;
        }else{
            $useapi = true;
        }
        
        $list = array();
        
        if(!empty($info['data']) && is_array($info['data'])){
            foreach ($info['data'] as $index=>$data){
                if ($data['context'] == '查无结果') {
                    continue;
                }
                $list[] = array(
                    'time' => trim($data['time']),
                    'step' => trim($data['context'])
                );
            }
        }
        
        if($useapi && $express_set['cache']>0 && !empty($list)){
            // 防止并发请求导致重复插入
            $cache = pdo_fetch("SELECT id FROM ".tablename("elapp_shop_express_cache")." WHERE expresssn=:expresssn LIMIT 1", array('expresssn'=>$expresssn));

            // 更新缓存
            if(empty($cache)){
                pdo_insert("elapp_shop_express_cache", array('expresssn'=>$expresssn, 'express'=>$express, 'lasttime'=>time(), 'datas'=>iserializer($list)));
            }else{
                pdo_update("elapp_shop_express_cache", array('lasttime'=>time(), 'datas'=>iserializer($list)), array('id'=>$cache['id']));
            }
        }
        
        return $list;
    }
    
    /**
     * @param $express
     * @param $expresssn
     * @param string $mobile
     * @param $express_set
     * @return array
     */
    private function getExpressAli ($express, $expresssn,$mobile='', $express_set) {
        $appcode = $express_set['aliappcode'];
        $url = "http://wdexpress.market.alicloudapi.com/gxali";
        $method = "GET";
        $expressInfo =  pdo_fetch("SELECT * FROM".tablename("elapp_shop_express")."WHERE express=:express LIMIT 1", array('express'=>$express));
        if (empty($expressInfo)) {
            return false;
        }
        $expressEncoding = $expressInfo['coding'];
        
        $headers = array();
        array_push($headers, "Authorization:APPCODE " . $appcode);
        if ($expressEncoding == 'SF') {
            $cunstomername = substr($mobile, -4);
            $querys = "n={$expresssn}:{$cunstomername}&t={$expressEncoding}";
        } else {
            $querys = "n={$expresssn}&t={$expressEncoding}";
        }
        
        $bodys = "";
        $url = $url . "?" . $querys;
        $curl = curl_init();
        curl_setopt($curl, CURLOPT_CUSTOMREQUEST, $method);
        curl_setopt($curl, CURLOPT_URL, $url);
        curl_setopt($curl, CURLOPT_HTTPHEADER, $headers);
        curl_setopt($curl, CURLOPT_FAILONERROR, false);
        curl_setopt($curl, CURLOPT_RETURNTRANSFER, true);
        curl_setopt($curl, CURLOPT_HEADER, false);
        
        $out_put = curl_exec($curl);
        $expressData = json_decode($out_put, true);
        $list = array();
        if(!empty($expressData['Traces']) && is_array($expressData['Traces'])){
            foreach ($expressData['Traces'] as $index=>$data){
                $list[] = array(
                    'time' => trim($data['AcceptTime']),
                    'step' => trim($data['AcceptStation'])
                );
            }
            $list = array_reverse($list);
        }
        
        return $list;
        
    }
    
    /**
     * 随机生成名字 拼团用
     */
    public function getRandomName() {
        $arrXing = array('赵','钱','孙','李','周','吴','郑','王','冯','陈','褚','卫','蒋','沈','韩','杨','朱','秦','尤','许','何','吕','施','张','孔','曹','严','华','金','魏','陶','姜','戚','谢','邹','喻','柏','水','窦','章','云','苏','潘','葛','奚','范','彭','郎','鲁','韦','昌','马','苗','凤','花','方','任','袁','柳','鲍','史','唐','费','薛','雷','贺','倪','汤','滕','殷','罗','毕','郝','安','常','傅','卞','齐','元','顾','孟','平','黄','穆','萧','尹','姚','邵','湛','汪','祁','毛','狄','米','伏','成','戴','谈','宋','茅','庞','熊','纪','舒','屈','项','祝','董','梁','杜','阮','蓝','闵','季','贾','路','娄','江','童','颜','郭','梅','盛','林','钟','徐','邱','骆','高','夏','蔡','田','樊','胡','凌','霍','虞','万','支','柯','管','卢','莫','柯','房','裘','缪','解','应','宗','丁','宣','邓','单','杭','洪','包','诸','左','石','崔','吉','龚','程','嵇','邢','裴','陆','荣','翁','荀','于','惠','甄','曲','封','储','仲','伊','宁','仇','甘','武','符','刘','景','詹','龙','叶','幸','司','黎','溥','印','怀','蒲','邰','从','索','赖','卓','屠','池','乔','胥','闻','莘','党','翟','谭','贡','劳','逄','姬','申','扶','堵','冉','宰','雍','桑','寿','通','燕','浦','尚','农','温','别','庄','晏','柴','瞿','阎','连','习','容','向','古','易','廖','庾','终','步','都','耿','满','弘','匡','国','文','寇','广','禄','阙','东','欧','利','师','巩','聂','关','荆','司马','上官','欧阳','夏侯','诸葛','闻人','东方','赫连','皇甫','尉迟','公羊','澹台','公冶','宗政','濮阳','淳于','单于','太叔','申屠','公孙','仲孙','轩辕','令狐','徐离','宇文','长孙','慕容','司徒','司空');
        $numXing = count($arrXing);
        $arrMing = array('伟','刚','勇','毅','俊','峰','强','军','平','保','东','文','辉','力','明','永','健','世','广','志','义','兴','良','海','山','仁','波','宁','贵','福','生','龙','元','全','国','胜','学','祥','才','发','武','新','利','清','飞','彬','富','顺','信','子','杰','涛','昌','成','康','星','光','天','达','安','岩','中','茂','进','林','有','坚','和','彪','博','诚','先','敬','震','振','壮','会','思','群','豪','心','邦','承','乐','绍','功','松','善','厚','庆','磊','民','友','裕','河','哲','江','超','浩','亮','政','谦','亨','奇','固','之','轮','翰','朗','伯','宏','言','若','鸣','朋','斌','梁','栋','维','启','克','伦','翔','旭','鹏','泽','晨','辰','士','以','建','家','致','树','炎','德','行','时','泰','盛','雄','琛','钧','冠','策','腾','楠','榕','风','航','弘','秀','娟','英','华','慧','巧','美','娜','静','淑','惠','珠','翠','雅','芝','玉','萍','红','娥','玲','芬','芳','燕','彩','春','菊','兰','凤','洁','梅','琳','素','云','莲','真','环','雪','荣','爱','妹','霞','香','月','莺','媛','艳','瑞','凡','佳','嘉','琼','勤','珍','贞','莉','桂','娣','叶','璧','璐','娅','琦','晶','妍','茜','秋','珊','莎','锦','黛','青','倩','婷','姣','婉','娴','瑾','颖','露','瑶','怡','婵','雁','蓓','纨','仪','荷','丹','蓉','眉','君','琴','蕊','薇','菁','梦','岚','苑','婕','馨','瑗','琰','韵','融','园','艺','咏','卿','聪','澜','纯','毓','悦','昭','冰','爽','琬','茗','羽','希','欣','飘','育','滢','馥','筠','柔','竹','霭','凝','晓','欢','霄','枫','芸','菲','寒','伊','亚','宜','可','姬','舒','影','荔','枝','丽','阳','妮','宝','贝','初','程','梵','罡','恒','鸿','桦','骅','剑','娇','纪','宽','苛','灵','玛','媚','琪','晴','容','睿','烁','堂','唯','威','韦','雯','苇','萱','阅','彦','宇','雨','洋','忠','宗','曼','紫','逸','贤','蝶','菡','绿','蓝','儿','翠','烟');
        $numMing = count($arrMing);
        $xing = $arrXing[mt_rand(0,$numXing-1)];
        $ming = $arrMing[mt_rand(0,$numMing-1)];
        return $xing.$ming;
    }
}
