<?php

namespace app\core\model\common;

/**
 * 抽象类 Sorter
 * 通用排序方法，可以对给定的列表进行排序
 * <AUTHOR> <<EMAIL>>
 * @date 2024-03-22
 */
abstract class Sorter {
    /**
     * 对列表进行排序
     *
     * @param array $list 要排序的列表
     * @param string $sortfield 用于排序的字段名
     * @return array 排序后的列表
     */
    public static function sortList($list, $sortfield) {
        usort($list, function($a, $b) use ($sortfield) {
            if ($a[$sortfield] == $b[$sortfield]) {
                return 0;
            }
            return ($a[$sortfield] < $b[$sortfield]) ? -1 : 1;
        });
        return $list;
    }
}