<?php

namespace app\core\model\common;

use DateTimeImmutable;
use Exception;

/**
 * 时间处理类
 * Class DateRangeCalculator
 * 用于根据提供的年、月和日计算日期范围
 * author: Hlei
 * date: 2024-04-8
 */
class DateRangeCalculator
{
    /**
     *
     * @param int $year 日期范围的年份
     * @param int|null $month 日期范围的月份，可选
     * @param int|null $day 日期范围的日期，可选
     * @param bool $asArray 是否返回数组形式
     * @return array 返回包含开始时间和结束时间的 DateTimeImmutable 对象数组
     * @throws Exception
     */
    public static function calculateDateRange(int $year, int $month = null, int $day = null, bool $asArray = false): array
    {
        if (!empty($year)) {
            $start = new DateTimeImmutable($year . '-01-01 00:00:00');
            $end = new DateTimeImmutable($year . '-12-31 23:59:59');

            if (!empty($month)) {
                $start = new DateTimeImmutable($year . '-' . $month . '-01 00:00:00');
                $end = (new DateTimeImmutable($year . '-' . $month))->modify('last day of this month')->setTime(23, 59, 59);

                if (!empty($day)) {
                    $start = new DateTimeImmutable($year . '-' . $month . '-' . $day . ' 00:00:00');
                    $end = new DateTimeImmutable($year . '-' . $month . '-' . $day . ' 23:59:59');
                }
            }
        } else {
            // 处理 $year 为空的情况
            $start = null;
            $end = null;
        }
        if($asArray){
            return ['start' => $start->getTimestamp(), 'end' => $end->getTimestamp()];
        }
        return ['start' => $start, 'end' => $end];
    }
}