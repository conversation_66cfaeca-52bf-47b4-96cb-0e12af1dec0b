<?php
namespace app\model;

class MessageModel{
	/**
     * 模板消息通知
     */
	public function sendTplNotice($touser, $template_id, $postdata, $url = '', $account = NULL, $miniprogram = array()){
		global $_W;
        if (!$account) {
			$account = m('common')->getAccount();
		}
		if (!$account) {
			return NULL;
		}

        $m = m('member')->getMember($touser);
        // todo [临时] 机构和店长的模板消息替换
        $jxwid = (new DiyattrsModel())->getValue(DiyattrsEnums::TYPE_COPARTNER, 0, DiyattrsEnums::KEY_JXW_COPARTNER_ID);
        if ($m && !empty($m['copartner_id']) && ($m['copartner_id'] == $jxwid)) {
            if (is_array($postdata)) {
                foreach ($postdata as &$item) {
                    if (isset($item['value'])) {
                        $item['value'] = str_replace('店长', '经销商', $item['value']);
                        $item['value'] = str_replace('机构', '运营中心', $item['value']);
                    }
                }
            }
        }

		return $account->sendTplNotice($touser, $template_id, $postdata, $url, '#FF683F', $miniprogram);
	}

	public function sendCustomNotice($openid, $msg, $url = '', $account = NULL){
		if (!$account) {
			$account = m('common')->getAccount();
		}
		if (!$account) {
			return NULL;
		}
		$content = '';
		if (is_array($msg)) {
			foreach ($msg as $key => $value) {
				if (!empty($value['title'])) {
					$content .= $value['title'] . ':' . $value['value'] . "\n";
				}else{
					$content .= $value['value'] . "\n";
					if ($key == 0) {
						$content .= "\n";
					}
				}
			}
		}else{
			$content = $msg;
		}
		if (!empty($url)) {
			$content .= '<a href=\'' . $url . '\'>点击查看详情</a>';
		}
		return $account->sendCustomNotice(array(
			'touser'  => $openid,
			'msgtype' => 'text',
			'text'    => array('content' => urlencode($content))
		));
	}

	/**
     * 发送消息图片
     * @param type $openid
     * @param type $mediaid
     * @return type 
     */
	public function sendImage($openid, $mediaid){
		$account = m('common')->getAccount();
		return $account->sendCustomNotice(array(
			'touser'  => $openid,
			'msgtype' => 'image',
			'image'   => array('media_id' => $mediaid)
		));
	}

	public function sendNews($openid, $articles, $account = NULL){
		if (!$account) {
			$account = m('common')->getAccount();
		}
		return $account->sendCustomNotice(array(
			'touser'  => $openid,
			'msgtype' => 'news',
			'news'    => array('articles' => $articles)
		));
	}

	public function sendTexts($openid, $content, $url = '', $account = NULL){
		if (!$account) {
			$account = m('common')->getAccount();
		}
		if (!empty($url)) {
			$content .= "\n<a href='" . $url . '\'>点击查看详情</a>';
		}
		return $account->sendCustomNotice(array(
			'touser'  => $openid,
			'msgtype' => 'text',
			'text'    => array('content' => urlencode($content))
		));
	}
}
