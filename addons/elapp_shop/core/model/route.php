<?php
namespace app\model;

class RouteModel {
    
    function run($isweb = true){
        global $_GPC, $_W;        
        //@include_once ELAPP_SHOP_PATH . 'vendor/autoload.php';
        require_once IA_ROOT . "/addons/elapp_shop/core/inc/page.php";
        if ($isweb) {
            require_once ELAPP_SHOP_CORE . "inc/page_web.php";
            require_once ELAPP_SHOP_CORE . "inc/page_web_com.php";
        } else{
            require_once ELAPP_SHOP_CORE . "inc/page_mobile.php";
            require_once ELAPP_SHOP_CORE . "inc/page_mobile_login.php";
        }
        $r = str_replace("//", "/", trim($_GPC['r'], "/"));
        $routes = explode('.', $r);
        
        $segs = count($routes);
        
        $method = "main";
        $root = $isweb ? ELAPP_SHOP_CORE_WEB : ELAPP_SHOP_CORE_MOBILE;
        
        $isMerch = false;
        $isOrg = false;
        $isCopartner = false;
        $isNewstore = false;
        $isSupply = false;
        if(strexists($_W['siteurl'] ,"/merch.php")) {
            if(empty($r)) {
                $r = "merch.manage";
                $routes = explode('.', $r);
            }
            $isMerch = true;
            $isplugin = true;
        }elseif(strexists($_W['siteurl'] ,"/org.php")) {
            if(empty($r)) {
                $r = "org.manage";
                $routes = explode('.', $r);
            }
            $isOrg = true;
            $isplugin = true;
        }else if(strexists($_W['siteurl'] ,"web/copartnerant.php")) {
            if(empty($r)) {
                $r = "copartner.manage";
                $routes = explode('.', $r);
            }
            $isCopartner = true;
            $isplugin = true;
        }else if(strexists($_W['siteurl'] ,"web/elappsupplyant.php")) {
            if(empty($r)) {
                $r = "supply.manage";
                $routes = explode('.', $r);
            }
            $isSupply = true;
            $isplugin = true;
        } else if(strexists($_W['siteurl'] ,"web/newstoreant.php")) {
            if(empty($r)) {
                $r = "newstore.manage";
                $routes = explode('.', $r);
            }
            $isNewstore = true;
            $isplugin = true;
        } else{
            $isplugin = !empty($r) && is_dir(ELAPP_SHOP_PLUGIN . $routes[0]);
        }
        
        if ($isplugin) {
            if ($isweb) {
                require_once ELAPP_SHOP_CORE . "inc/page_web_plugin.php";
                
            } else {
                require_once ELAPP_SHOP_CORE . "inc/page_mobile_plugin.php";
                require_once ELAPP_SHOP_CORE . "inc/page_mobile_plugin_login.php";
                require_once ELAPP_SHOP_CORE . "inc/page_mobile_plugin_pf.php";
            }
            
            $_W['plugin'] = $routes[0];
            $root = ELAPP_SHOP_PLUGIN . $routes[0] . "/core/" . ( $isweb ? "web" : "mobile") . "/";
            if($isMerch){
                $_W['plugin'] ="merch";
                $root = ELAPP_SHOP_PLUGIN .  "merch/core/web/manage/";
            } elseif ($isOrg){
                $_W['plugin'] ="org";
                $root = ELAPP_SHOP_PLUGIN .  "org/core/web/manage/";
            } elseif ($isCopartner){
                $_W['plugin'] ="copartner";
                $root = ELAPP_SHOP_PLUGIN .  "copartner/core/web/manage/";
            } elseif ($isSupply){
                //供应商 Hlei 20220314
                $_W['plugin'] ="supply";
                $root = ELAPP_SHOP_PLUGIN .  "supply/core/web/manage/";
            } elseif ($isNewstore){
                $_W['plugin'] ="newstore";
                $root = ELAPP_SHOP_PLUGIN .  "newstore/core/web/manage/";
            } else {
                $routes = array_slice($routes, 1);
            }
            $segs = count($routes);
        } elseif ($routes[0]=='system'){
            require_once ELAPP_SHOP_CORE . "inc/page_system.php";
        }
        
        switch ($segs) {
            case 0: {
                $file = $root . "index.php";
                $class = "Index";
            }
            case 1: {
                $file = $root . $routes[0] . ".php";
                
                if (is_file($file)) {
                    $class = ucfirst($routes[0]);
                } elseif (is_dir($root . $routes[0])) {
                    $file = $root . $routes[0] . "/index.php";
                    $class = "Index";
                } else {
                    $method = $routes[0];
                    $file = $root . "index.php";
                    $class = "Index";
                }
                $_W['action'] = $routes[0];
            }
                break;
            case 2: {
                $_W['action'] = $routes[0] . "." . $routes[1];
                $file = $root . $routes[0] . "/" . $routes[1] . ".php";
                
                if (is_file($file)) {
                    $class = ucfirst($routes[1]);
                } elseif (is_dir($root . $routes[0] . "/" . $routes[1])) {
                    $file = $root . $routes[0] . "/" . $routes[1] . "/index.php";
                    $class = "Index";
                } else {
                    $file = $root . $routes[0] . ".php";
                    if (is_file($file)) {
                        $method = $routes[1];
                        $class = ucfirst($routes[0]);
                    } elseif (is_dir($root . $routes[0])) {
                        $method = $routes[1];
                        $file = $root . $routes[0] . "/index.php";
                        $class = "Index";
                    } else {
                        $file = $root . "index.php";
                        $class = "Index";
                    }
                }
                $_W['action'] = $routes[0] . "." . $routes[1];
                break;
            }
            case 3: {
                $_W['action'] = $routes[0] . "." . $routes[1] . "." . $routes[2];
                
                $file = $root . $routes[0] . "/" . $routes[1] . "/" . $routes[2] . ".php";
                if (is_file($file)) {
                    $class = ucfirst($routes[2]);
                    
                    
                } elseif (is_dir($root . $routes[0] . "/" . $routes[1] . "/" . $routes[2])) {
                    $file = $root . $routes[0] . "/" . $routes[1] . "/" . $routes[2] . "/index.php";
                    $class = "Index";
                } else {
                    $method = $routes[2];
                    $file = $root . $routes[0] . "/" . $routes[1] . ".php";
                    if (is_file($file)) {
                        $class = ucfirst($routes[1]);
                    } elseif (is_dir($root . $routes[0] . "/" . $routes[1])) {
                        $file = $root . $routes[0] . "/" . $routes[1] . "/index.php";
                        $class = "Index";
                    }
                    $_W['action'] = $routes[0] . "." . $routes[1];
                }
                break;
            }
            case 4: {
                $_W['action'] = $routes[0] . "." . $routes[1] . "." . $routes[2];
                $method = $routes[3];
                $class = ucfirst($routes[2]);
                $file = $root . $routes[0] . "/" . $routes[1] . "/" . $routes[2] . ".php";
                break;
            }
        }
        
        if (!is_file($file)) {
            show_message("未找到控制器 {$r}");
        }
        
        $_W['routes'] = $r;
        $_W['isplugin'] = $isplugin;
        $_W['controller'] = $routes[0];
        
  
        $global_set= m('cache')->getArray('globalset');
        
        if(empty($global_set)){
            $global_set = m('common')->setGlobalSet();
        }
        if(!is_array($global_set)){
            $global_set = array();
        }
        
        empty($global_set['trade']['credittext']) && $global_set['trade']['credittext'] = "积分";
        empty($global_set['trade']['moneytext']) && $global_set['trade']['moneytext'] = "余额";
        
        $GLOBALS["_S"] = $_W['shopset']  = $global_set;
        
        include $file;
        
        if (isset($_GPC['r']) && strpos($_GPC['r'], 'pc') !== false) {
            $class = ucfirst($class) . "Controller";
        } else {
            //直接读取$file的class_name，无需强硬加入特定字符串，可忽略前缀大小写
            //有特殊，如果上面$class = Index得绕过处理或者兼容处理，这里兼容处理
            $class = get_classname($file);
        }

        //if (!class_exists($class)) {
            $model_namespace = get_namespace($file);
            $class = $model_namespace."\\".$class;
        //}
        
        $instance = new $class();
        if (!method_exists($instance, $method)) {
            show_message("控制器 {$_W['controller']} 方法 {$method} 未找到!");
        }
        
        $response = $instance->$method();
        if (!empty($response)) {
            echo $response;
        }
        die;
    }
}