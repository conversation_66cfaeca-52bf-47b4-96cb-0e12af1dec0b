<?php
namespace app\core\model\fullback;

use think\Model;

/**
 * 全返商品 模型
 * class FullbackGoodsModel
 * @package app\core\model\fullback
 * @create 2024-07-26
 * <AUTHOR>
 */
class FullbackGoodsModel extends Model
{
    protected $name = 'fullback_goods';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list' => [
                'id',
                'uniacid',
                'type',
                'goodsid',
                'titles',
                'thumb',
                'marketprice',
                'minallfullbackallprice',
                'maxallfullbackallprice',
                'minallfullbackallratio',
                'maxallfullbackallratio',
                'day',
                'fullbackprice',
                'fullbackratio',
                'status',
                'displayorder',
                'hasoption',
                'optionid',
                'startday',
                'refund'
            ],
            'default' => '*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 获取器定义 start
    public function getThumbAttr($value)
    {
        return tomedia($value);
    }

    // 获取器 end

}