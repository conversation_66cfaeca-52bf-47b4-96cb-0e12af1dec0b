<?php
namespace app\model;
use PHPExcel,PHPExcel_IOFactory,PHPExcel_Cell;
use PHPExcel_Cell_DataType;

class ExcelModel {
    protected  function column_str($key) {
        $array = array(
            'A', 'B', 'C', 'D', 'E', 'F', 'G', 'H', 'I', 'J', 'K', 'L', 'M', 'N', 'O', 'P', 'Q', 'R', 'S', 'T', 'U', 'V', 'W', 'X', 'Y', 'Z',
            'AA', 'AB', 'AC', 'AD', 'AE', 'AF', 'AG', 'AH', 'AI', 'AJ', 'AK', 'AL', 'AM', 'AN', 'AO', 'AP', 'AQ', 'AR', 'AS', 'AT', 'AU', 'AV', 'AW', 'AX', 'AY', 'AZ',
            'BA', 'BB', 'BC', 'BD', 'BE', 'BF', 'BG', 'BH', 'BI', 'BJ', 'BK', 'BL', 'BM', 'BN', 'BO', 'BP', 'BQ', 'BR', 'BS', 'BT', 'BU', 'BV', 'BW', 'BX', 'BY', 'BZ',
            'CA', 'CB', 'CC', 'CD', 'CE', 'CF', 'CG', 'CH', 'CI', 'CJ', 'CK', 'CL', 'CM', 'CN', 'CO', 'CP', 'CQ', 'CR', 'CS', 'CT', 'CU', 'CV', 'CW', 'CX', 'CY', 'CZ',
            'DA', 'DB', 'DC', 'DD', 'DE', 'DF', 'DG', 'DH', 'DI', 'DJ', 'DK', 'DL', 'DM', 'DN', 'DO', 'DP', 'DQ', 'DR', 'DS', 'DT', 'DU', 'DV', 'DW', 'DX', 'DY', 'DZ',
            'EA', 'EB', 'EC', 'ED', 'EE', 'EF', 'EG', 'EH', 'EI', 'EJ', 'EK', 'EL', 'EM', 'EN', 'EO', 'EP', 'EQ', 'ER', 'ES', 'ET', 'EU', 'EV', 'EW', 'EX', 'EY', 'EZ'
        );
        return $array[$key];
    }

    protected  function column($key, $columnnum = 1) {
        return $this->column_str($key) . $columnnum;
    }

    function export($list, $params = array()) {
        $filePath = $this->exportToFile($list, $params);

        $filename = urlencode($params['title'] . '-' . date('Y-m-d H:i', time()));
        ob_end_clean();
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment;filename="' . $filename . '.xlsx"');
        header('Cache-Control: max-age=0');

        readfile($filePath);
        unlink($filePath);
        exit;
    }

    public function exportToFile($list, $params = array(), $filePath = '')
    {
        global $_W;
        if (PHP_SAPI == 'cli') {
            die('This example should only be run from a Web Browser');
        }

        $data = m('common')->getSysset('shop');
        $excel = new PHPExcel();
        $excel->getProperties()->setCreator(empty($data['name'])?'社交新零售电商系统':$data['name'])
            ->setLastModifiedBy(empty($data['name'])?'社交新零售电商系统':$data['name'])
            ->setTitle("Office 2007 XLSX Test Document")
            ->setSubject("Office 2007 XLSX Test Document")
            ->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")
            ->setKeywords("office 2007 openxml php")
            ->setCategory("report file");
        $sheet = $excel->setActiveSheetIndex(0);
        $rownum = 1;
        foreach ($params['columns'] as $key => $column) {
            $sheet->setCellValue($this->column($key, $rownum), $column['title']);
            if (!empty($column['width'])) {
                $sheet->getColumnDimension($this->column_str($key))->setWidth($column['width']);
            }
        }
        $rownum++;
        $len = count($params['columns']);;
        foreach ($list as $row) {

            for ($i = 0; $i < $len; $i++) {
                $value = isset($row[$params['columns'][$i]['field']])?$row[$params['columns'][$i]['field']]:'';
                if (isset($params['columns'][$i]['type']) && $params['columns'][$i]['type'] === 'string'){
                    $sheet->setCellValueExplicit($this->column($i, $rownum), $value, PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $sheet->setCellValue($this->column($i, $rownum), $value);
                }
                // 如果内容有\n则计算行高
                if (strpos($value, "\n") !== false) {
//                    $sheet->getRowDimension($rownum)->setRowHeight(-1);
//                    // 启用自动换行
                    $cellCoordinate = $this->column($i, $rownum);
                    $sheet->getStyle($cellCoordinate)->getAlignment()->setWrapText(true);
                }
            }
            //$sheet->getRowDimension($rownum)->setRowHeight(-1);
            $rownum++;
        }
        $excel->getActiveSheet()->setTitle($params['title']);
        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        if (empty($filePath)) {
            $filePath = ELAPP_SHOP_DATA . '/' . $_W['uniacid'] . '/' . rand(0, getrandmax()) . rand(0, getrandmax()) . ".tmp";
        }
        $this->SaveViaTempFile($writer, $filePath);
        return $filePath;
    }

    /**
     * 2024-08-08
     * 扩展 exportToFile 实现，支持多sheet导出的版本
     * @param $list
     * @param $params
     * @param $filePath string excel导出文件路径
     * @param $sheetName string 工作表名称
     * @return mixed|string|void
     * @throws \PHPExcel_Exception
     * @throws \PHPExcel_Reader_Exception
     */
    public function exportsToFile($list, $params = array(), $filePath = '', $sheetName = '')
    {
        global $_W;
        if (PHP_SAPI == 'cli') {
            die('This example should only be run from a Web Browser');
        }

        $data = m('common')->getSysset('shop');

        // 如果文件路径为空,则创建一个新的Excel文件
        if (empty($filePath)) {
            $filePath = ELAPP_SHOP_DATA . '/' . $_W['uniacid'] . '/' . rand(0, getrandmax()) . rand(0, getrandmax()) . ".xlsx";
            $excel = new PHPExcel();
            $excel->getProperties()->setCreator(empty($data['name'])?'社交新零售电商系统':$data['name'])
                ->setLastModifiedBy(empty($data['name'])?'社交新零售电商系统':$data['name'])
                ->setTitle("Office 2007 XLSX Test Document")
                ->setSubject("Office 2007 XLSX Test Document")
                ->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")
                ->setKeywords("office 2007 openxml php")
                ->setCategory("report file");
            $excel->removeSheetByIndex(0);
        } else {
            // 如果文件存在,则读取该文件
            if (file_exists($filePath)) {
                $reader = PHPExcel_IOFactory::createReader('Excel2007');
                $excel = $reader->load($filePath);
            } else {
                // 如果文件不存在,则创建一个新的Excel文件
                $excel = new PHPExcel();
                $excel->getProperties()->setCreator(empty($data['name'])?'社交新零售电商系统':$data['name'])
                    ->setLastModifiedBy(empty($data['name'])?'社交新零售电商系统':$data['name'])
                    ->setTitle("Office 2007 XLSX Test Document")
                    ->setSubject("Office 2007 XLSX Test Document")
                    ->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")
                    ->setKeywords("office 2007 openxml php")
                    ->setCategory("report file");
                $excel->removeSheetByIndex(0);
            }
        }

        // 创建新的工作表或选择已有的工作表
        if ($sheetName !== '') {
            if ($excel->sheetNameExists($sheetName)) {
                $sheet = $excel->getSheetByName($sheetName);
            } else {
                $sheet = $excel->createSheet();
                $sheet->setTitle($sheetName);
            }
        } else {
            $sheet = $excel->createSheet();
        }
        $excel->setActiveSheetIndex($excel->getSheetCount() - 1);

        $rownum = 1;
        foreach ($params['columns'] as $key => $column) {
            $sheet->setCellValue($this->column($key, $rownum), $column['title']);
            if (!empty($column['width'])) {
                $sheet->getColumnDimension($this->column_str($key))->setWidth($column['width']);
            }
        }
        $rownum++;
        $len = count($params['columns']);
        foreach ($list as $row) {
            for ($i = 0; $i < $len; $i++) {
                $value = isset($row[$params['columns'][$i]['field']])?$row[$params['columns'][$i]['field']]:'';
                $cellCoordinate = $this->column($i, $rownum);
                if (isset($params['columns'][$i]['type']) && $params['columns'][$i]['type'] === 'string'){
                    $sheet->setCellValueExplicit($cellCoordinate, $value, PHPExcel_Cell_DataType::TYPE_STRING);
                } else {
                    $sheet->setCellValue($cellCoordinate, $value);
                }
                // 启用自动换行
                $sheet->getStyle($cellCoordinate)->getAlignment()->setWrapText(true);
                // 如果内容有\n则自动调整行高
                if (strpos($value, "\n") !== false) {
                    $sheet->getRowDimension($rownum)->setRowHeight(-1);
                }
            }
            $rownum++;
        }

        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel2007');
        $this->SaveViaTempFile($writer, $filePath);

        return $filePath;
    }

    public  function exportCSV($list, $columns = array(), $path = '',$page = 1,$exflag = false)
    {
        set_time_limit(0);
        if (empty($path) && PHP_SAPI == 'cli') {
            throw new \Exception('Excel::exportCSV  should only be run from a Web Browser');
        }
        if(!is_dir($path)) {
            @mkdir($path, 0777);
        }
        $filename = date('Ymd', time());
        if(!empty($columns['title'])){
            $filename = ($columns['title']) . '-' .$filename.'.csv';
        }
        $savepath = $path.$filename;


        if($page ==1) {
            $tableheader = array();
            foreach ($columns['columns'] as $col) {
                $tableheader[] = $col['title'];
            }
            $tableheader_str = '"' . implode('","', $tableheader) . '"' . "\n";
            $html = iconv('UTF-8', 'GBK//TRANSLIT', $tableheader_str);
            // 兼容iconv转换失败的情况下数据返回false，然后得到的数据是空的，此时使用原数据
            if ($html === false) {
                $html = $tableheader_str;
            }
        }
        foreach ($list as $value) {
            foreach ($columns['columns'] as $col) {
                $type = '';
                if (isset($col['type']) && $col['type'] === 'string'){
                    $type = "\t";
                }
                $conv = iconv('UTF-8','GBK//TRANSLIT',$value[$col['field']]);
                // 兼容iconv转换失败的情况下数据返回false，然后得到的数据是空的，此时使用原数据
                if ($conv === false) {
                    $conv = $value[$col['field']];
                }
                $html .= '"'.$conv."{$type}\",";
            }
            $html .= "\n";
        }

        if(!empty($html)){
            file_put_contents($savepath,$html,8);
        }

        if($exflag){
            $file = $savepath;
            if(file_exists($file)){
                header("Content-type:application/octet-stream");
                $filename = basename($file);
                header("Content-Disposition:attachment;filename = ".$filename);
                header("Accept-ranges:bytes");
                header("Accept-length:".filesize($file));
                readfile($file);

                // todo 有些地方调用会显示方法未定义
                // addons/elapp_shop/plugin/org/core/web/manage/structure/MemberController.php::index() 的数据导出显示方法未定义
                if (function_exists('file_delete')) {
                    file_delete($file);
                } else {
                    // 如果文件不是csv,xls,xlsx就不删除
                    $ext = strtolower(pathinfo($file, PATHINFO_EXTENSION));
                    if (in_array($ext, array('csv', 'xls', 'xlsx'))) {
                        @unlink($file);
                    }
                }
                exit();
//            }else{
//                echo "<script>alert('文件不存在')</script>";
            }
        }
        unset($html);
        return true;
    }


    function SaveViaTempFile($objWriter, $filePath)
    {
        // 如果目录不存在则创建
        if (!is_dir(dirname($filePath))) {
            load()->func('file');
            mkdirs(dirname($filePath));
        }
        $objWriter->save($filePath);
        return $filePath;
    }

    function temp($title, $columns = array()) {

        if (PHP_SAPI == 'cli') {
            die('This example should only be run from a Web Browser');
        }

        $excel = new PHPExcel();
        $excel->getProperties()->setCreator("社交新零售电商系统")->setLastModifiedBy("社交新零售电商系统")->setTitle("Office 2007 XLSX Test Document")->setSubject("Office 2007 XLSX Test Document")->setDescription("Test document for Office 2007 XLSX, generated using PHP classes.")->setKeywords("office 2007 openxml php")->setCategory("report file");
        $sheet = $excel->setActiveSheetIndex(0);
        $rownum = 1;

        foreach ($columns as $key => $column) {
            $sheet->setCellValue($this->column($key, $rownum), $column['title']);
            if (!empty($column['width'])) {
                $sheet->getColumnDimension($this->column_str($key))->setWidth($column['width']);
            }
        }
        $rownum++;
        $len = count($columns);;
        for ($k=1;$k<=5000;$k++) {
            for ($i = 0; $i < $len; $i++) {
                $sheet->setCellValue($this->column($i, $rownum), "");
            }
            $rownum++;
        }
        $excel->getActiveSheet()->setTitle($title);
        $filename = urlencode($title);
        header('Content-Type: application/octet-stream');
        header('Content-Disposition: attachment;filename="' .$filename . '.xls"');
        header('Cache-Control: max-age=0');
        $writer = PHPExcel_IOFactory::createWriter($excel, 'Excel5');
        $writer->save('php://output');
        exit;
    }

    public function import($excefile){

        global $_W;

        $path  = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . "/tmp/";
        if(!is_dir($path)){
            load()->func('file');
            mkdirs($path,'0777');
        }

        $filename = $_FILES[$excefile]['name'];
        $tmpname = $_FILES[$excefile]['tmp_name'];
        if(empty($tmpname)){
            message('请选择要上传的Excel文件!','','error');
        }
        $ext =  strtolower( pathinfo($filename, PATHINFO_EXTENSION) );
        if($ext!='xlsx' && $ext!='xls'){
            message('请上传 xls 或 xlsx 格式的Excel文件!','','error');
        }

        $file = time().$_W['uniacid'].".".$ext;
        $uploadfile = $path.$file;

        $result  = move_uploaded_file($tmpname,$uploadfile);
        if(!$result){
            message('上传Excel 文件失败, 请重新上传!','','error');
        }

        $reader = PHPExcel_IOFactory::createReader($ext=='xls'?'Excel5':'Excel2007');

        $excel = $reader->load($uploadfile);
        $sheet = $excel->getActiveSheet();
        $highestRow = $sheet->getHighestRow();
        $highestColumn = $sheet->getHighestColumn();
        $highestColumnCount= PHPExcel_Cell::columnIndexFromString($highestColumn);
        $values = array();
        for ($row = 1;$row <= $highestRow;$row++)
        {
            $rowValue = array();
            for ($col = 0;$col < $highestColumnCount;$col++)
            {
                $rowValue[] = (string)$sheet->getCellByColumnAndRow($col, $row)->getValue();

            }
            $values[] = $rowValue;
        }

        return $values;
    }



}
