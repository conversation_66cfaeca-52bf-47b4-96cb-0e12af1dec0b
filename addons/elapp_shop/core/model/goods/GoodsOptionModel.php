<?php

namespace app\model;

use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;
use think\Model;
use think\model\relation\HasOne;

class GoodsOptionModel extends Model
{
    protected $name = 'goods_option';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'goodsid',
                'title',
                'thumb',
                'productprice',
                'marketprice',
                'costprice',
                'stock',
                'weight',
                'displayorder',
                'specs',
                'skuId',
                'goodssn',
                'productsn',
                'virtual',
                'exchange_stock',
                'exchange_postage',
                'presellprice',
                'day',
                'allfullbackprice',
                'fullbackprice',
                'allfullbackratio',
                'fullbackratio',
                'isfullback',
                'islive',
                'liveprice',
                'cycelbuy_periodic',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 根据不同场景绑定不同属性到父模型
    public static function bindAttrs($scene = 'list')
    {
        $map = [
            'list' => [

            ],
            'default' => [
                'goodsid',
                'title',
                'thumb',
                'productprice',
                'marketprice',
                'costprice',
                'presellprice',
                'stock',
                'weight',
                'displayorder',
                'specs',
                'skuId',
                'goodssn',
                'productsn',
                'virtual',
                'exchange_stock',
                'exchange_postage',
                'day',
                'allfullbackprice',
                'fullbackprice',
                'allfullbackratio',
                'fullbackratio',
                'isfullback',
                'islive',
                'liveprice',
                'cycelbuy_periodic',
            ],
            // 购物车场景 联表查询字段要做别名处理
            'cart' => [
                'option_title',
                'option_thumb',
                'option_productprice',
                'option_marketprice',
                'option_costprice',
                'option_presellprice',
                'option_stock',
                'option_weight',
                'option_specs',
                'option_skuId',
                'option_goodssn',
                'option_productsn',
                'option_virtual',
            ]
        ];
        return $map[$scene] ?? $map['default'];
    }

    /**
     * @param array $goodsid 需要查询的商品ID
     * @param string $fields 需要返回的查询字段
     * @param array $where  补充查询条件
     * @param string $reduce 是否需要以$reduce进行分组操作
     * @return array
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    static function getGoodsOptions(array $goodsid, $fields = ['*'] , array $where = [], string $reduce = 'goodsid'): array
    {
        $where = ['goodsid' => $goodsid];
        $options = GoodsOptionModel::where($where)->field($fields)->select();
        if ($options->isEmpty()) {
            return [];
        }
        if (!empty($reduce)) {
            $options = $options->reduce(function ($result, $item) use ($reduce) {
                if (!isset($result[$item[$reduce]])) {
                    $result[$item[$reduce]] = [];
                }
                $result[$item[$reduce]][] = $item->toArray();
                return $result;
            });
            return $options;
        }
        return $options->toArray();
    }

    /**
     * 一对一关联商品表
     * @return HasOne
     */
    public function goods() : HasOne
    {
        return $this->hasOne(GoodsModel::class, 'id', 'goodsid');
    }
}