<?php
namespace app\model;

use app\plugin\prescription\core\logic\PrescriptionCheckGoodsLogic;
use Exception;
use think\facade\Cache;
use think\facade\Db;
use think\Model;
use think\model\relation\HasMany;

class GoodsModel extends Model {
    protected $name = 'goods';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [

            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 根据不同场景绑定不同属性到父模型,也适用于查询fields字段
    public static function bindAttrs($scene = 'list' ,$asString = false)
    {
        $map = [
            'list' => [

            ],
            'default' => [
                'id',
                'title',
                'subtitle',
                'thumb',
                'marketprice',
                'status',
                'type',
                'unit',
                'weight',
                'description',
                'content',
            ],
            // 公共字段
            'common' => [
                'activity_id',
                'type',
                'goodsClassID',
                'medicineClassID',
                'medicineAttributeID',
                'title',
                'weight',
                'issendfree',
                'isnodiscount',
                'ispresell',
                'presellprice',
                'thumb',
                'marketprice',
                'storeids',
                'isverify',
                'isforceverifystore',
                'deduct',
                'hasoption',
                'preselltimeend',
                'presellsendstatrttime',
                'presellsendtime',
                'presellsendtype',
                'manydeduct',
                'manydeduct2',
                'virtual',
                'maxbuy',
                'usermaxbuy',
                'discounts',
                'stock',
                'deduct2',
                'showlevels',
                'ednum',
                'edmoney',
                'edareas',
                'edareas_code',
                'unite_total',
                'diyformtype',
                'diyformid',
                'diymode',
                'dispatchtype',
                'dispatchid',
                'dispatchprice',
                'isfullback'
            ],
            // 购物车购买场景
            'cart' => [
                /*'title', 'subtitle', 'thumb', 'marketprice', 'status', 'unit',
                'activity_id', 'maxbuy', 'type', 'goodsClassID', 'medicineClassID',
                'medicineAttributeID', 'intervalfloor', 'intervalprice', 'issendfree', 'isnodiscount',
                'ispresell', 'presellprice', 'preselltimeend', 'presellsendstatrttime', 'presellsendtime',
                'presellsendtype', 'weight', 'title', 'thumb', 'marketprice',
                'storeids', 'isverify', 'isforceverifystore', 'deduct', 'manydeduct',
                'manydeduct2', 'virtual', 'deduct2', 'ednum', 'edmoney',
                'edareas', 'edareas_code', 'diyformtype', 'diyformid', 'diymode',
                'dispatchtype', 'dispatchid', 'dispatchprice', 'minbuy', 'is_minbuy_times_add',
                'discounts','isdiscount', 'isdiscount_time', 'isdiscount_time_start', 'isdiscount_discounts', 'cates',
                'isfullback', 'virtualsend', 'invoice', 'merchid', 'checked',
                'merchsale', 'unite_total', 'buyagain', 'buyagain_islong', 'buyagain_condition',
                'buyagain_sale', 'hasoption', 'threen', 'limitation'*/

                'subtitle', 'status', 'unit',
                'intervalfloor', 'intervalprice', 'medicineAttributeID', 'presellprice', 'presellsendstatrttime', 'presellsendtime',
                'presellsendtype', 'manydeduct', 'deduct2', 'ednum', 'edmoney',
                'edareas', 'edareas_code', 'minbuy', 'is_minbuy_times_add',
                'isdiscount', 'isdiscount_time', 'isdiscount_time_start', 'isdiscount_discounts', 'cates',
                'virtualsend', 'invoice', 'merchid', 'checked',
                'merchsale', 'buyagain', 'buyagain_islong', 'buyagain_condition',
                'buyagain_sale', 'threen', 'limitation'
            ],
            // 单品直接购买场景
            'single' => [
                'liveprice',
                'islive',
                'cycelbuy_goods_id',
                'diyfields',
                'isfullback',
                'supplyid',
                'isSupplySend',
                'erpGoodsID',
                'bargain',
                'limitation'
            ],
            // 批发场景
            'wholesale' => [
                'showOrgs',
                'cates',
                'minbuy',
                'is_minbuy_times_add',
                'isdiscount',
                'isdiscount_time',
                'isdiscount_time_start',
                'isdiscount_discounts',
                'virtualsend',
                'invoice',
                'needfollow',
                'followtip',
                'followurl',
                'merchid',
                'checked',
                'merchsale',
                'buyagain',
                'buyagain_islong',
                'buyagain_condition',
                'buyagain_sale',
                'intervalprice',
                'intervalfloor'
            ]
        ];

        $attrs = $map[$scene] ?? $map['default'];
        if ($asString) {
            return implode(',', $attrs);
        }
        return $attrs;
    }

    /**
     * @desc 与订单商品一对多关联
     * @return HasMany
     */
    public function orderGoods(): HasMany
    {
        return $this->hasMany(OrderGoodsModel::class, 'goodsid', 'id');
    }

    /**
     * @desc 与商品规格一对多关联
     * @return HasMany
     */
    public function option(): HasMany
    {
        return $this->hasMany(GoodsOptionModel::class, 'goodsid', 'id');
    }

    // 获取器定义 start
    public function getThumbAttr($value)
    {
        return tomedia($value);
    }
    public function getThumbUrlAttr($value)
    {
        return unserialize($value);
    }
    public function getIntervalpriceAttr($value)
    {
        return unserialize($value);
    }
    public function getMedicinesAttr($value)
    {
        return unserialize($value);
    }
    public function getFunctionsAttr($value)
    {
        return unserialize($value);
    }
    public function getDiyfieldsAttr($value)
    {
        return unserialize($value);
    }
    public function getDiscountsAttr($value)
    {
        return json_decode($value,true);
    }
    public function getIsdiscountDiscountsAttr($value)
    {
        return json_decode($value,true);
    }
    public function getCommissionAttr($value)
    {
        return json_decode($value,true);
    }
    public function getClerkCommissionAttr($value)
    {
        return json_decode($value,true);
    }
    public function getCopartnerCommissionAttr($value)
    {
        return json_decode($value,true);
    }
    public function getDoctorCommissionAttr($value)
    {
        return json_decode($value,true);
    }
    public function getBusinessCommissionAttr($value)
    {
        return json_decode($value,true);
    }
    public function getThreenAttr($value)
    {
        return json_decode($value,true);
    }
    public function getLimitationAttr($value)
    {
        return json_decode($value,true);
    }
    public function getSpecialpriceAttr($value)
    {
        return json_decode($value,true);
    }
    // 获取器 end

    /**
     * 获取商品规格
     * @param int $goodsid
     * @param int $optionid
     * @return array|bool|mixed
     */
    public function getOption(int $goodsid = 0, int $optionid = 0) {
        global $_W;
        return pdo_fetch("select * from " . tablename('elapp_shop_goods_option') . ' where id=:id and goodsid=:goodsid and uniacid=:uniacid Limit 1', array(':id' => $optionid, ':uniacid' => $_W['uniacid'], ':goodsid' => $goodsid));
    }

    /**
     * 获取商品规格的价格
     * @param type $goodsid
     * @param type $optionid
     * @return type
     */
    public function getOptionPirce($goodsid = 0, $optionid = 0) {
        global $_W;
        return pdo_fetchcolumn("select marketprice from " . tablename('elapp_shop_goods_option') . ' where id=:id and goodsid=:goodsid and uniacid=:uniacid', array(':id' => $optionid, ':uniacid' => $_W['uniacid'], ':goodsid' => $goodsid));
    }

    /**
     * 获取宝贝商品列表
     * @param array $args
     * @return array
     */
    public function getList(array $args = array()) {
        //开始执行时间
        $starttime = microtime(true);
        global $_W, $_GPC;
        $openid = $_W['openid'];        
        $page = !empty($args['page']) ? intval($args['page']) : 1;
        $pagesize = !empty($args['pagesize']) ? intval($args['pagesize']) : 10;
        $random = !empty($args['random']) ? $args['random'] : false;
        $tradeSet = m('common')->getSysset('trade');//获取全局交易设置会员购买权限
        $displayorder = 'displayorder';

        //指定商户
        $merchid= !empty($args['merchid']) ? trim($args['merchid']) : '';
        if (!empty($merchid)) {
            $displayorder = 'merchdisplayorder';
        }
        //指定供应商
        $supplyid= !empty($args['supplyid']) ? trim($args['supplyid']) : '';
        if (!empty($supplyid)) {
            $displayorder = 'supplydisplayorder';
        }

        $order = !empty($args['order']) ? $args['order'] : ' ' . $displayorder . ' desc,createtime desc';
        $orderby = empty($args['order']) ? '' : (!empty($args['by']) ? $args['by'] : '' );

        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        //供应商 Hlei 20220328
        $supply_plugin = p('supply');
        $supply_data = m('common')->getPluginset('supply');
        if ($supply_plugin && $supply_data['is_opensupply']) {
            $is_opensupply = 1;
        } else {
            $is_opensupply = 0;
        }

        $condition = '`uniacid` = :uniacid AND `deleted` = 0 and status=1 and is_cloud=0 ';
        $params = array(':uniacid' => $_W['uniacid']);
        $where = [
            'uniacid' => $_W['uniacid'],
            'deleted' => 0,
            'status' => 1,
            'is_cloud' => 0
        ];

        // 限制终端
        $comefrom = !empty($args['comefrom']) ? trim($args['comefrom']) : '';
        // 微信小程序
        if (!empty($comefrom) && $comefrom == 'wechat_mmp') {
            $where['limitation_terminal_wechat_mmp'] = 1;
        }

        //小程序暂不支持批发商品，支持后删除此条件
        if(!empty($args['from']) && $args['from'] == 'miniprogram'){
            $condition.= ' and type <> 4';
            $where[] = ['type', '<>','4'];
        }

        if (!empty($merchid)) {
            $condition.=" and merchid=:merchid and checked=0";
            $params[':merchid'] = $merchid;
            $where['merchid'] = $merchid;
            $where['checked'] = 0;
        } else {
            if ($is_openmerch == 0) {
                //未开启多商户的情况下,只读取平台商品
                $condition .= ' and `merchid` = 0';
                $where['merchid'] = 0;
            } else {
                //开启多商户的情况下,过滤掉未通过审核的商品
                $condition .= ' and `checked` = 0';
                $where['checked'] = 0;
            }
        }

        //供应商Hlei20220328
        if (!empty($supplyid)) {
            $condition.=" and supplyid=:supplyid and checked=0";
            $params[':supplyid'] = $supplyid;
            $where['supplyid'] = $supplyid;
            $where['checked'] = 0;
        } else {
            if ($is_opensupply == 0) {
                //未开启多商户的情况下,只读取平台商品
                $condition .= ' and `supplyid` = 0';
                $where['supplyid'] = 0;
            } else {
                //开启多商户的情况下,过滤掉未通过审核的商品
                $condition .= ' and `checked` = 0';
                $where['checked'] = 0;
            }
        }

        // 类型
        if(empty($args['type'])){
            $condition.=" and type !=10 ";
            $where[] = ['type','<>','10'];
        }else{
            $condition.=" and type = " .$args['type']; //商品类型
            $where['type'] = $args['type'];
        }

        //指定ID
        $ids = !empty($args['ids']) ? trim($args['ids']) : '';
        if (!empty($ids)) {
            $condition.=" and id in ( " . $ids . ")";
            $where[] = ['id','in',$ids];
        }

        //处方药Rx
        $isrx = !empty($args['isrx']) ? 1 : 0;
        if (!empty($isrx)) {
            $condition.=" and goodsClassID=2 and medicineAttributeID=1";
            $where['goodsClassID'] = 2;
            $where['medicineAttributeID'] = 1;
        }
        //非处方药OTC
        $isotc = !empty($args['isotc']) ? 1 : 0;
        if (!empty($isotc)) {
            $condition.=" and goodsClassID=2 and medicineAttributeID=2";
            $where['goodsClassID'] = 2;
            $where['medicineAttributeID'] = 2;
        }        
        //保健品NHC
        $isnhc = !empty($args['isnhc']) ? 1 : 0;
        if (!empty($isnhc)) {
            $condition.=" and goodsClassID=3";
            $where['goodsClassID'] = 3;
        }
        //医疗器械DC
        $isdc = !empty($args['isdc']) ? 1 : 0;
        if (!empty($isdc)) {
            $condition.=" and goodsClassID=4";
            $where['goodsClassID'] = 4;
        }
        //计生用品noun
        $isnoun = !empty($args['isnoun']) ? 1 : 0;
        if (!empty($isnoun)) {
            $condition.=" and goodsClassID=5";
            $where['goodsClassID'] = 5;
        }
        //情趣用品sex
        $issex = !empty($args['issex']) ? 1 : 0;
        if (!empty($issex)) {
            $condition.=" and goodsClassID=6";
            $where['goodsClassID'] = 6;
        }
        //卫生消毒
        $isga = !empty($args['isga']) ? 1 : 0;
        if (!empty($isga)) {
            $condition.=" and goodsClassID=7";
            $where['goodsClassID'] = 7;
        }

        //新品
        $isnew = !empty($args['isnew']) ? 1 : 0;
        if (!empty($isnew)) {
            $condition.=" and isnew=1";
            $where['isnew'] = 1;
        }
        //热销
        $ishot = !empty($args['ishot']) ? 1 : 0;
        if (!empty($ishot)) {
            $condition.=" and ishot=1";
            $where['ishot'] = 1;
        }
        //推荐
        $isrecommand = !empty($args['isrecommand']) ? 1 : 0;
        if (!empty($isrecommand)) {
            $condition.=" and isrecommand=1";
            $where['isrecommand'] = 1;
        }
        //折扣
        $isdiscount = !empty($args['isdiscount']) ? 1 : 0;
        if (!empty($isdiscount)) {
            $condition.=" and isdiscount=1";
            $where['isdiscount'] = 1;
        }
        //包邮
        $issendfree = !empty($args['issendfree']) ? 1 : 0;
        if (!empty($issendfree)) {
            $condition.=" and issendfree=1";
            $where['issendfree'] = 1;
        }

        //限时购
        $istime = !empty($args['istime']) ? 1 : 0;
        if (!empty($istime)) {
            //$condition.=" and istime=1 and " . time() . ">=timestart and " . time() . "<=timeend";
            $condition.=" and istime=1 ";
            $where['istime'] = 1;
        }

        //是否参与分销
        if (isset($args['nocommission'])) {
            $condition .= ' AND `nocommission`=' . intval($args['nocommission']);
            $where['nocommission'] = $args['nocommission'];
        }

        //关键词
        $keywords = !empty($args['keywords']) ? $args['keywords'] : '';
        if (!empty($keywords)) {
            $condition .= ' AND (`title` LIKE :keywords OR `keywords` LIKE :keywords OR `productsn` LIKE :keywords ' .
            'OR `goodssn` LIKE :keywords OR `title_pinyin` like :keywords)';
            $params[':keywords'] = '%' . trim($keywords) . '%';
            $whereOr = [
                ['title', 'like', '%' . trim($keywords) . '%'],
                ['keywords', 'like', '%' . trim($keywords) . '%'],
                ['productsn', 'like', '%' . trim($keywords) . '%'],
                ['goodssn', 'like', '%' . trim($keywords) . '%'],
                ['title_pinyin', 'like', '%' . trim($keywords) . '%']
            ];

            if (empty($merchid)) {
                $condition .= ' AND nosearch=0';
                $where['nosearch'] = 0;
            }
            //供应商 Hlei 20220328
            if (empty($supplyid)) {
                $condition .= ' AND nosearch=0';
                $where['nosearch'] = 0;
            }
        }
        //价格区间
        if ( isset($args['minprice']) && isset($args['maxprice']) && $args['minprice']!='' && $args['maxprice']!='' ) {
            $condition .= " AND marketprice BETWEEN :pricestart AND :priceend";
            $params[':pricestart'] = $args['minprice'] ;
            $params[':priceend'] = $args['maxprice'] ;
            $where['marketprice'] = ['between', [$args['minprice'], $args['maxprice']]];
        }

        //分类
        if(!empty($args['cate'])){
            /*$category = m('shop')->getAllCategory();
            $catearr = array($args['cate']);
            foreach ($category as $index => $row) {
                if ($row['parentid'] == $args['cate']) {
                    $catearr[] = $row['id'];
                    foreach ($category as $ind => $ro) {
                        if ($ro['parentid'] == $row['id']) {
                            $catearr[] = $ro['id'];
                        }
                    }
                }
            }
            $catearr = array_unique($catearr);
            $condition .= " AND ( ";
            foreach ($catearr as $key=>$value){
                if ($key==0) {
                    $condition .= "FIND_IN_SET({$value},cates)";
                }else{
                    $condition .= " || FIND_IN_SET({$value},cates)";
                }
            }
            $condition .= " <>0 )";*/
            $condition .= " AND ( FIND_IN_SET({$args['cate']},cates) <>0 )";
            $where[] = ['', 'exp', Db::raw("FIND_IN_SET('{$args['cate']}',cates) > 0")];

        }
        $shopSysset = m('common')->getSysset('shop');
        $member =m('member')->getMember($openid);
        if($args['ispc']==1){
        }else {
            if(!empty($member)) {
                $levelid = intval($member['level']);
                $groupid = trim($member['groupid']);
                $org_id = intval($member['org_id']);
                $condition .= " and ( ifnull(showlevels,'')='' or FIND_IN_SET( {$levelid},showlevels)<>0 ) ";
                $condition .= " and ( ifnull(showOrgs,'')='' or FIND_IN_SET( {$org_id},showOrgs)<>0 ) ";
                $where[] = ['', 'exp', Db::raw("ifnull(showlevels,'')='' or FIND_IN_SET('{$levelid}',showlevels) > 0")];
                $where[] = ['', 'exp', Db::raw("ifnull(showOrgs,'')='' or FIND_IN_SET('{$org_id}',showOrgs) > 0")];
                if(strpos($groupid, ',') !== false) {
                    $groupidArr = explode(',', $groupid);
                    $groupidStr = '';
                    foreach ($groupidArr as $grk => $grv) {
                        $groupidStr .= "INSTR( showgroups,'{$grv}')<>0 or ";
                        if($grk == count($groupidArr) - 1) {
                            $groupidStr .= "INSTR( showgroups,'{$grv}')<>0 ";
                        }
                    }
                    $condition .= "and ( ifnull(showgroups,'')='' or  {$groupidStr} )";
                    $where[] = ['', 'exp', Db::raw("ifnull(showgroups,'')='' or {$groupidStr}")];
                } else {
                    //修改会员组查看商品问题
                    $condition .= " and ( ifnull(showgroups,'')='' or FIND_IN_SET( '{$groupid}',showgroups)<>0 ) ";
                    $where[] = ['', 'exp', Db::raw("ifnull(showgroups,'')='' or FIND_IN_SET('{$groupid}',showgroups) > 0")];
                }

            } else {
                $condition .= " and ifnull(showlevels,'')='' ";
                $condition .= " and   ifnull(showgroups,'')='' ";
                $condition .= " and ifnull(showOrgs,'')='' ";
                $where[] = ['', 'exp', Db::raw("ifnull(showlevels,'')='' and ifnull(showgroups,'')='' and ifnull(showOrgs,'')=''")];
            }
        }
        $condition .= " and type <> 99 ";
        $where[] = ['type', '<>', 99];

        $total = "";
        $officsql = '';
        if(p('offic')){
            $officsql = ",officthumb";
        }

        //店员预计收益
        if(p('clerk')){
            $clerk = p('clerk');
            $clerk_set = $clerk->getSet();
            $clerk_level = $clerk->getLevel($_W['openid']);
            $myshop = $clerk->getShop($member['id']);
        }
        //医生预计收益
        if(p('doctor')){
            $doctor = p('doctor');
            $doctor_set = $doctor->getSet();
            $doctor_level = $doctor->getLevel($_W['openid']);
            $myshop = $doctor->getShop($member['id']);
        }
		$commissionCondition = '';
        //查询店员商品分润表
        if(!empty($clerk_set['isopen']) && !empty($clerk_set['level'])){
            $commissionCondition .= ',hasClerkCommission,noClerkCommission,clerkCommission,clerkCommission1_rate,clerkCommission1_pay';
        } 
        //查询医生商品分润表
        if(!empty($doctor_set['isopen']) && !empty($doctor_set['level'])){
            $commissionCondition .= ',doctor_has_commission,doctor_no_commission,doctor_commission,doctor_commission_rate,doctor_commission_pay';
        }        

		/*if (!$random) {
            $sql = "SELECT id,title,subtitle,thumb,thumb_url{$officsql}{$commissionCondition},marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sum(sales+salesreal) as salesreal,total,description,bargain,`type`,`goodsClassID`,medicineClassID,medicineAttributeID,ispresell,isdiscount_time_start,isdiscount_time,`virtual`,hasoption,video,bargain,hascommission,nocommission,commission,commission1_rate,commission1_pay,presellprice,buylevels,buygroups,isreclevel,reclevel,merchid,costprice
            FROM " . tablename('elapp_shop_goods') . " where  {$condition} GROUP BY id ORDER BY {$order} {$orderby} LIMIT " . ($page - 1) * $pagesize . ',' . $pagesize;
            $total = pdo_fetchcolumn("select count(*) from " . tablename('elapp_shop_goods') . " where  {$condition} ",$params);
        } else {
            $sql = "SELECT id,title,subtitle,thumb,thumb_url{$officsql}{$commissionCondition},marketprice,productprice,minprice,maxprice,isdiscount,isdiscount_time,isdiscount_discounts,sum(sales+salesreal) as salesreal,total,description,bargain,`type`,`goodsClassID`,medicineClassID,medicineAttributeID,ispresell,isdiscount_time_start,isdiscount_time,`virtual`,hasoption,bargain,hascommission,nocommission,commission,commission1_rate,commission1_pay,presellprice,buylevels,buygroups,isreclevel,reclevel,merchid
            FROM " . tablename('elapp_shop_goods') . " where  {$condition}  GROUP BY id ORDER BY rand() LIMIT " . $pagesize;
            $total  = $pagesize;
        }*/

        $orderRaw = "{$order} {$orderby}";
        if ($random) $orderRaw = 'rand()';
        $field = "id,title,subtitle,thumb,thumb_url{$officsql}{$commissionCondition},marketprice,productprice,minprice,maxprice,isdiscount,discounts,isdiscount_time,isdiscount_discounts,sum(sales+salesreal) as salesreal,stock,description,bargain,`type`,`goodsClassID`,medicineClassID,medicineAttributeID,ispresell,isdiscount_time_start,isdiscount_time,`virtual`,hasoption,video,bargain,hascommission,nocommission,commission,commission1_rate,commission1_pay,presellprice,buylevels,buygroups,isreclevel,reclevel,merchid,costprice";
        $goods_cacheKey = md5(serialize($where).serialize($whereOr).serialize($orderRaw).$field.$page.$pagesize) . '_goods_list';
        $goods_total_cacheKey = md5(serialize($where).serialize($whereOr)) . '_goods_list_total';
        $list = Cache::tag('goods_list')->remember($goods_cacheKey, function () use ($where, $whereOr, $orderRaw, $field, $page, $pagesize) {
            return GoodsModel::where($where)->where(function ($query) use ($whereOr) { $query->whereOr($whereOr);})->group('id')->orderRaw($orderRaw)->fetchsql(false)->limit(($page - 1) * $pagesize, $pagesize)->column($field, 'id');
        }, 7200);
        $total = Cache::tag('goods_list')->remember($goods_total_cacheKey, function () use ($where, $whereOr) {
            return GoodsModel::where($where)->where(function ($query) use ($whereOr) { $query->whereOr($whereOr);})->group('id')->fetchsql(false)->count();
        }, 7200);

        $level = $this->getLevel($_W['openid']);
        $set = $this->getSet();
        //$list = pdo_fetchall($sql, $params, 'id');

        $levelid = intval($member['level']);
        $groupid = intval($member['groupid']);

        //获取商品选项 update by paul
        $goods_options = GoodsOptionModel::getGoodsOptions(array_keys($list), ['id', 'goodsid', 'marketprice', 'presellprice', 'stock'], ['uniacid' => $_W['uniacid']]);

        //获取所有会员等级
        $alllevels = MemberModel::getLevels(false, [], '*', ['level' => 'desc'], 'level');
        // 是否是处方药
        $isRxGoods = 0;

        $result_cacheKey = $goods_cacheKey . '_result';
        if (!empty($level)) $result_cacheKey .= '_level' . $level['id']; //分销商等级
        if (!empty($clerklevel)) $result_cacheKey .= '_clerklevel' . $clerklevel['id'];
        if (!empty($doctor_level)) $result_cacheKey .= '_doctorlevel' . $doctor_level['id'];

        $result_list = Cache::get($result_cacheKey);
        if (empty($result_list)) {
            foreach($list as $lk => $lv){

                //显示预售价格
                if($lv['ispresell']==1){
                    $list[$lk]['minprice'] = $lv['presellprice'];
                }
                //商品划线价是否显示
                $list[$lk]['isMarketPrice'] = intval($_W['shopset']['shop']['isMarketPrice']);

                //判断会员权限  先判断商品设置 商品高于全局权重
                $list[$lk]['levelbuy'] = '1';
                if ($lv['buylevels'] != '') {
                    $buylevels = explode(',', $lv['buylevels']);
                    if (!in_array($levelid, $buylevels)) {
                        $list[$lk]['levelbuy'] = 0;
                        $list[$lk]['canbuy']  = false;
                    }
                }else if ($tradeSet['buylevels'] != '') {
                    $buylevels = explode(',', $tradeSet['buylevels']);
                    if (!in_array($levelid, $buylevels)) {
                        $list[$lk]['levelbuy'] = 0;
                        $list[$lk]['canbuy']  = false;
                    }
                }

                //会员组权限
                $list[$lk]['groupbuy'] = '1';
                if ($lv['buygroups'] != '' && !empty($groupid)) {
                    $buygroups = explode(',', $lv['buygroups']);
                    $groupids = explode(',', $groupid);
                    $intersect = array_intersect($groupids, $buygroups);
                    if (empty($intersect)) {
                        $list[$lk]['groupbuy'] = 0;
                        $list[$lk]['canbuy']  = false;
                    }
                }
                //多规格
                if(1 == $lv['hasoption'] && in_array($lv['id'], array_keys($goods_options))) {
                    $pricemax = array_column($goods_options[$lv['id']], 'marketprice');
                    $lv['marketprice'] = max($pricemax); // 如果导致商品价格显示错误 开启此块注释
                }

                $list[$lk]['reclevel'] = com_run('sale::getReclevel', $lv['reclevel']);//商品营销主推级别
                //分销预计佣金
                if($lv['nocommission']==0){
                    //秒杀不参与佣金
                    if(p('seckill')){
                        if (p('seckill')->getSeckill($lv['id'])){
                            continue;
                        }
                    }
                    //砍价不参与佣金
                    if(p('bargain')){
                        if($lv['bargain']>0){
                            // 微信端禁止显示砍价商品
                            if (is_weixin() && $_GPC['comefrom'] != 'wxapp') {
                                $list[$lk]['bargain'] = 0;
                            }
                            continue;
                        }
                    }
                    $list[$lk]['seecommission'] = $this->getCommission($lv,$level,$set);
                    if($list[$lk]['seecommission'] >0){
                        $list[$lk]['seecommission'] = round($list[$lk]['seecommission'],2);
                    }
                    $list[$lk]['cansee'] = $set['cansee'];
                    $list[$lk]['seetitle'] = $set['seetitle'];
                }else{
                    $list[$lk]['seecommission'] = 0;
                    $list[$lk]['cansee'] = $set['cansee'];
                    $list[$lk]['seetitle'] = $set['seetitle'];
                }

                //店员预计收益
                if($lv['noClerkCommission']==0){
                    //秒杀不参与店员收益
                    if(p('seckill')){
                        if (p('seckill')->getSeckill($lv['id'])){
                            continue;
                        }
                    }
                    //砍价不参与店员收益
                    if(p('bargain')){
                        if($lv['bargain']>0){
                            // 微信端禁止显示砍价商品
                            if (is_weixin() && $_GPC['comefrom'] != 'wxapp') {
                                $list[$lk]['bargain'] = 0;
                            }
                            continue;
                        }
                    }
                    $list[$lk]['clerkSeecommission'] = $clerk->getGoodsCommission($lv, $clerklevel,$goods_options[$lv['id']]);
                    if( $list[$lk]['clerkSeecommission']>0){
                        $list[$lk]['clerkSeecommission'] = round($list[$lk]['clerkSeecommission'],2);
                    }
                    $list[$lk]['clerkCansee'] = $clerk_set['cansee'];
                    $list[$lk]['clerkSeetitle'] = $clerk_set['seetitle'];
                    $list[$lk]['is_show_revenue'] = $myshop['is_show_revenue'];
                }else{
                    $list[$lk]['clerkSeecommission'] = 0;
                    $list[$lk]['clerkCansee'] = $clerk_set['cansee'];
                    $list[$lk]['clerkSeetitle'] = $clerk_set['seetitle'];
                    $list[$lk]['is_show_revenue'] = $myshop['is_show_revenue'];
                }

                //医生预计收益
                if($lv['doctor_no_commission']==0){
                    //秒杀不参与店员收益
                    if(p('seckill')){
                        if (p('seckill')->getSeckill($lv['id'])){
                            continue;
                        }
                    }
                    //砍价不参与店员收益
                    if(p('bargain')){
                        if($lv['bargain']>0){
                            // 微信端禁止显示砍价商品
                            if (is_weixin() && $_GPC['comefrom'] != 'wxapp') {
                                $list[$lk]['bargain'] = 0;
                            }
                            continue;
                        }
                    }
                    $list[$lk]['doctorSeecommission'] = $doctor->getGoodsCommission($lv, $doctor_level,$goods_options[$lv['id']]);
                    if( $list[$lk]['doctorSeecommission']>0){
                        $list[$lk]['doctorSeecommission'] = round($list[$lk]['doctorSeecommission'],2);
                    }
                    $list[$lk]['doctorCansee'] = $doctor_set['cansee'];
                    $list[$lk]['doctorSeetitle'] = $doctor_set['seetitle'];
                    $list[$lk]['is_show_revenue'] = $myshop['is_show_revenue'];
                }else{
                    $list[$lk]['doctorSeecommission'] = 0;
                    $list[$lk]['doctorCansee'] = $doctor_set['cansee'];
                    $list[$lk]['doctorSeetitle'] = $doctor_set['seetitle'];
                    $list[$lk]['is_show_revenue'] = $myshop['is_show_revenue'];
                }

                $list[$lk]['maxlevel'] = $maxlevel = reset($alllevels);
                $list[$lk]['maxMemberLevelPrice'] = $maxMemberLevelPrice = $this->getMemberPrice($lv, $maxlevel);//最大会员等级价格


                if($lv['type'] == 3){
                    $vsql = 'SELECT * FROM '.tablename('elapp_shop_virtual_type').' WHERE uniacid = '.intval($_W['uniacid']).' AND id = '.intval($lv['virtual']);
                    $vData = pdo_fetch($vsql);
                    if($vData['recycled'] == 1){
                        array_splice($list,$lk,1);
                    }
                }
                //促销更新状态
                /*if (!($lv['isdiscount'] == 1 && $lv['isdiscount_time'] > time() && $lv['isdiscount_time_start'] < time() )) {
                    if ( isset($options) && count($options) > 0 && $lv['hasoption']) {
                        $optionids = array();
                        foreach ($options as $val){
                            $optionids[] = $val['id'];
                        }
                        //更新最低价和最高价
                        $sql = "update ".tablename('elapp_shop_goods')." g set g.minprice = (select min(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = ".$lv['id']."), g.maxprice = (select max(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = ".$lv['id'].") where g.id = ".$lv['id']." and g.hasoption=1";
                        pdo_query($sql);
                    } else {
                        $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = ".$lv['id']." and hasoption=0;";
                        pdo_query($sql);
                    }
                    $goods_price = pdo_fetch("select minprice,maxprice from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", array(':id' => $lv['id'], ':uniacid' => $_W['uniacid']));
                    $list[$lk]['maxprice'] = (float)$goods_price['maxprice'];
                    $list[$lk]['minprice'] = (float)$goods_price['minprice'];
                } else{
                    $goodsinfo = m('goods')->getOneMinPrice($lv);
                    pdo_update('elapp_shop_goods',array('minprice'=>$goodsinfo['minprice'],'maxprice'=>$goodsinfo['maxprice']),array('id'=>$lv['id'],'uniacid'=>$_W['uniacid']));
                    $list[$lk]['maxprice'] = (float)$goodsinfo['maxprice'];
                    $list[$lk]['minprice'] = (float)$goodsinfo['minprice'];
                }*/

                $list[$lk]['thumb'] = tomedia($lv['thumb']);
            }
            Cache::tag('goods_list')->set($result_cacheKey, $result_list = $list, 7200);
        }

        // 用户数据差异化处理
        $arr = [];
        // 如果是处方药且后台设置了处方药图片模糊处理
        if (p('prescription')) {
            foreach ($result_list as $lk => $lv) {
                // 检查是否处方药品
                $isRxGoods = $lv['goodsClassID'] == 2 && $lv['medicineAttributeID'] == 1 ? 1 : 0;
                if ($isRxGoods) {
                    $arr[] = $lv;
                    $prescribe = [];
                    $is_prescribe = 0; // 未开过处方

                    // 查询该商品是否在开方有效时间内
                    if ($lv['id'] && $member['id']) {
                        $prescribe_log = app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescribe($lv['id'], $member['id']);
                        if ($prescribe_log['code'] == 0 && $prescribe_log['data']) {
                            $prescribe = $prescribe_log['data'];
                            $is_prescribe = $prescribe['is_prescribe'];
                        }
                    }

                    // 处方药图片模糊处理
                    $isRxGoodsPicOpacity = $shopSysset['isRxGoodsPicOpacity'] == 1 && !$is_prescribe ? 1 : 0;

                    $result_list[$lk]['isRxGoodsPicOpacity'] = $isRxGoodsPicOpacity; // 处方药图片模糊效果
                    $result_list[$lk]['is_prescribe'] = $is_prescribe; // 已开过处方
                    $result_list[$lk]['prescribe'] = $prescribe; // 开方信息
                    $result_list[$lk]['is_rx_goods'] = $isRxGoods; // 是处方药
                }
            }
            unset($lv);
        }
        return array("list"=>$result_list,"total"=>$total, 'array' => $arr);
    }

    /**
     * 计算出此商品的分销佣金
     * @param array $goods
     * @param $level
     * @param array $set
     * @return array|int
     */
    public function getCommission(array $goods, $level, $set){

        global $_W;
        $commission = 0;
        if ($level == 'false') {
            return $commission;
        }
        //商品规格
        if (!empty($goods) && $goods['hasoption']) {
            $option = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $goods['id'], ':uniacid' => $_W['uniacid']));
        }
        if ($goods['hascommission'] == 1) {
            
            $price = $goods['maxprice'];
            $levelid = 'default';

            if ($level) {
                $levelid = 'level' . $level['id'];
            }

            $goods_commission = !empty($goods['commission']) ? json_decode($goods['commission'], true) : array();


            if ($goods_commission['type'] == 0) {
                $commission = $set['level'] >= 1 ? ($goods['commission1_rate'] > 0 ? ($goods['commission1_rate'] * $goods['marketprice'] / 100) : $goods['commission1_pay']) : 0;

            } else if (!empty($option)) {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    $maxkey = array_search(max($value),$value);
                    foreach ($option as $k => $v) {
                        $optioncommission=0;
                        if (('option' . $v['id']) == $key) {
                            if (strexists($value[$maxkey], '%')) {
                                $optioncommission = (floatval(str_replace('%', '', $value[0]) / 100) * $v['marketprice']);

                            } else {
                                $optioncommission = $value[$maxkey];

                            }
                            array_push($price_all, $optioncommission);
                        }
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }else{
                    $commission = 0;
                }

            } else {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    foreach ($value as $k => $v) {
                        if (strexists($v, '%')) {
                            array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                            continue;
                        }
                        array_push($price_all, $v);
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }else{
                    $commission = 0;
                }
            }
        } else {
            if ($level!='false' && !empty($level)) {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            } else {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            }
        }

        return $commission;
    }

    /**
     * 获取分销商等级
     */
    function getLevel($openid){
        global $_W;
        $level = false;
        if (empty($openid)) {
            return false;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['isagent']) || $member['status']==0 || $member['agentblack'] ==1) {
            return false;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_commission_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['agentlevel']));

        return $level;
    }

    /**
     * 计算出此商品的店员提成
     * @param array $goods
     * @param array $level
     * @param array $set
     * @return array|bool
     */
    public function getClerkCommission(array $goods, array $level, array $set){

        global $_W;
        $commission = 0;
        if ($level == 'false') {
            return $commission;
        }
        //商品规格
        if (!empty($goods) && $goods['hasoption']) {
            $option = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $goods['id'], ':uniacid' => $_W['uniacid']));
        }
        if ($goods['hasClerkCommission'] == 1) {
            
            $price = $goods['maxprice'];
            $levelid = 'default';

            if ($level) {
                $levelid = 'level' . $level['id'];
            }

            $goods_commission = !empty($goods['clerkCommission']) ? json_decode($goods['clerkCommission'], true) : array();


            if ($goods_commission['type'] == 0) {
                $commission = $set['level'] >= 1 ? ($goods['clerkCommission1_rate'] > 0 ? ($goods['clerkCommission1_rate'] * $goods['marketprice'] / 100) : $goods['clerkCommission1_pay']) : 0;

            } else if (!empty($option)) {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    $maxkey = array_search(max($value),$value);
                    foreach ($option as $k => $v) {
                        $optioncommission=0;
                        if (('option' . $v['id']) == $key) {
                            if (strexists($value[$maxkey], '%')) {
                                $optioncommission = (floatval(str_replace('%', '', $value[0]) / 100) * $v['marketprice']);

                            } else {
                                $optioncommission = $value[$maxkey];

                            }
                            array_push($price_all, $optioncommission);
                        }
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }

            } else {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    foreach ($value as $k => $v) {
                        if (strexists($v, '%')) {
                            array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                            continue;
                        }
                        array_push($price_all, $v);
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }
            }
        } else {
            if ($level!='false' && !empty($level)) {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            } else {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            }
        }

        return $commission;
    }

    /**
     * 计算出此商品的医生提成
     * @param type $goodsid
     * @return type
     */
    public function getDoctorCommission($goods,$level,$set){

        global $_W;
        $commission = 0;
        if ($level == 'false') {
            return $commission;
        }
        //商品规格
        if (!empty($goods) && $goods['hasoption']) {
            $option = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $goods['id'], ':uniacid' => $_W['uniacid']));
        }
        if ($goods['doctor_has_commission'] == 1) {
            
            $price = $goods['maxprice'];
            $levelid = 'default';

            if ($level) {
                $levelid = 'level' . $level['id'];
            }

            $goods_commission = !empty($goods['doctor_commission']) ? json_decode($goods['doctor_commission'], true) : array();


            if ($goods_commission['type'] == 0) {
                $commission = $set['level'] >= 1 ? ($goods['doctor_commission_rate'] > 0 ? ($goods['doctor_commission_rate'] * $goods['marketprice'] / 100) : $goods['doctor_commission_pay']) : 0;

            }else if(!empty($option)) {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    $maxkey = array_search(max($value),$value);
                    foreach ($option as $k => $v) {
                        $optioncommission=0;
                        if (('option' . $v['id']) == $key) {
                            if (strexists($value[$maxkey], '%')) {
                                $optioncommission = (floatval(str_replace('%', '', $value[0]) / 100) * $v['marketprice']);

                            } else {
                                $optioncommission = $value[$maxkey];

                            }
                            array_push($price_all, $optioncommission);
                        }
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }else{
                    $commission = 0;
                }

            } else {

                $price_all = array();
                foreach ($goods_commission[$levelid] as $key => $value) {
                    foreach ($value as $k => $v) {
                        if (strexists($v, '%')) {
                            array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                            continue;
                        }
                        array_push($price_all, $v);
                    }
                }
                if($price_all){
                    $commission = max($price_all);
                }else{
                    $commission = 0;
                }
            }
        } else {
            if ($level!='false' && !empty($level)) {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            } else {
                if( $goods['marketprice'] <= 0){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            }
        }

        return $commission;
    }

    //获取医生等级
    function getDoctorLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['is_doctor']) || $member['doctor_status']==0 || $member['doctor_black'] ==1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_doctor_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['doctor_level']));

        return $level;
    }

    function getSet(){
        $set = m('common')->getPluginset('commission');

        $set['texts'] = array(
            'agent' => empty($set['texts']['agent']) ? '分销商' : $set['texts']['agent'],
            'shop' => empty($set['texts']['shop']) ? '小店' : $set['texts']['shop'],
            'myshop' => empty($set['texts']['myshop']) ? '我的小店' : $set['texts']['myshop'],
            'center' => empty($set['texts']['center']) ? '分销中心' : $set['texts']['center'],
            'become' => empty($set['texts']['become']) ? '成为分销商' : $set['texts']['become'],
            'withdraw' => empty($set['texts']['withdraw']) ? '提现' : $set['texts']['withdraw'],
            'commission' => empty($set['texts']['commission']) ? '佣金' : $set['texts']['commission'],
            'commission1' => empty($set['texts']['commission1']) ? '分销佣金' : $set['texts']['commission1'],
            'commission_total' => empty($set['texts']['commission_total']) ? '累计佣金' : $set['texts']['commission_total'],
            'commission_ok' => empty($set['texts']['commission_ok']) ? '可提现佣金' : $set['texts']['commission_ok'],
            'commission_apply' => empty($set['texts']['commission_apply']) ? '已申请佣金' : $set['texts']['commission_apply'],
            'commission_check' => empty($set['texts']['commission_check']) ? '待打款佣金' : $set['texts']['commission_check'],
            'commission_lock' => empty($set['texts']['commission_lock']) ? '未结算佣金' : $set['texts']['commission_lock'],
            'commission_detail' => empty($set['texts']['commission_detail']) ? '提现明细' : ($set['texts']['commission_detail'] == '佣金明细' ? '提现明细' : $set['texts']['commission_detail']),
            'commission_pay' => empty($set['texts']['commission_pay']) ? '成功提现佣金' : $set['texts']['commission_pay'],
            'commission_wait' => empty($set['texts']['commission_wait']) ? '待收货佣金' : $set['texts']['commission_wait'],
            'commission_fail' => empty($set['texts']['commission_fail']) ? '无效佣金' : $set['texts']['commission_fail'],
            'commission_charge' => empty($set['texts']['commission_charge']) ? '扣除提现手续费' : $set['texts']['commission_charge'],
            'order' => empty($set['texts']['order']) ? '分销订单' : $set['texts']['order'],
            'c1' => empty($set['texts']['c1']) ? '一级' : $set['texts']['c1'],
            'c2' => empty($set['texts']['c2']) ? '二级' : $set['texts']['c2'],
            'c3' => empty($set['texts']['c3']) ? '三级' : $set['texts']['c3'],
            'mydown' => empty($set['texts']['mydown']) ? '我的下级' : $set['texts']['mydown'],
            'down' => empty($set['texts']['down']) ? '下级' : $set['texts']['down'],
            'up' => empty($set['texts']['up']) ? '推荐人' : $set['texts']['up'],
            'yuan' => empty($set['texts']['yuan']) ? '元' : $set['texts']['yuan'],
            'icode' => empty($set['texts']['icode']) ? '邀请码' : $set['texts']['icode']
        );
        return $set;
    }
    /**
     * 获取宝贝
     * @param type $page
     * @param type $pagesize
     */
    public function getListbyCoupon($args = array()) {

        global $_W;
        $openid = $_W['openid'];
        $page = !empty($args['page']) ? intval($args['page']) : 1;
        $pagesize = !empty($args['pagesize']) ? intval($args['pagesize']) : 10;
        $random = !empty($args['random']) ? $args['random'] : false;

        $order = !empty($args['order']) ? $args['order'] : ' displayorder desc,createtime desc';
        $orderby = empty($args['order']) ? '' : (!empty($args['by']) ? $args['by'] : '' );

        $couponid = empty($args['couponid']) ? '' : $args['couponid'];

        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        //供应商 Hlei 20220328
        $supply_plugin = p('supply');
        $supply_data = m('common')->getPluginset('supply');
        if ($supply_plugin && $supply_data['is_opensupply']) {
            $is_opensupply = 1;
        } else {
            $is_opensupply = 0;
        }
        $condition = ' and g.`uniacid` = :uniacid AND g.`deleted` = 0 and g.status=1 and g.is_cloud=0';
        $params = array(':uniacid' => $_W['uniacid']);

        //指定商户
        $merchid= !empty($args['merchid']) ? trim($args['merchid']) : '';
        if (!empty($merchid)) {
            $condition.=" and g.merchid=:merchid and g.checked=0";
            $params[':merchid'] = $merchid;
        } else {
            if ($is_openmerch == 0) {
                //未开启多商户的情况下,只读取平台商品
                $condition .= ' and g.`merchid` = 0';
            } else {
                //开启多商户的情况下,过滤掉未通过审核的商品
                $condition .= ' and g.`checked` = 0';
            }
        }
        //指定供应商 Hlei 20220328
        $supplyid= !empty($args['supplyid']) ? trim($args['supplyid']) : '';
        if (!empty($supplyid)) {
            $condition.=" and g.supplyid=:supplyid and g.checked=0";
            $params[':supplyid'] = $supplyid;
        } else {
            if ($is_opensupply == 0) {
                //未开启多商户的情况下,只读取平台商品
                $condition .= ' and g.`supplyid` = 0';
            } else {
                //开启多商户的情况下,过滤掉未通过审核的商品
                $condition .= ' and g.`checked` = 0';
            }
        }

        // 类型
        if(empty($args['type'])){
            $condition.=" and g.type !=10 ";
        }

        //指定ID
        $ids = !empty($args['ids']) ? trim($args['ids']) : '';
        if (!empty($ids)) {
            $condition.=" and g.id in ( " . $ids . ")";
        }

        //新品
        $isnew = !empty($args['isnew']) ? 1 : 0;
        if (!empty($isnew)) {
            $condition.=" and g.isnew=1";
        }
        //热销
        $ishot = !empty($args['ishot']) ? 1 : 0;
        if (!empty($ishot)) {
            $condition.=" and g.ishot=1";
        }
        //推荐
        $isrecommand = !empty($args['isrecommand']) ? 1 : 0;
        if (!empty($isrecommand)) {
            $condition.=" and g.isrecommand=1";
        }
        //折扣
        $isdiscount = !empty($args['isdiscount']) ? 1 : 0;
        if (!empty($isdiscount)) {
            $condition.=" and g.isdiscount=1";
        }
        //包邮
        $issendfree = !empty($args['issendfree']) ? 1 : 0;
        if (!empty($issendfree)) {
            $condition.=" and g.issendfree=1";
        }

        //限时购
        $istime = !empty($args['istime']) ? 1 : 0;
        if (!empty($istime)) {
            //$condition.=" and istime=1 and " . time() . ">=timestart and " . time() . "<=timeend";
            $condition.=" and g.istime=1 ";
        }

        //是否参与分销
        if (isset($args['nocommission'])) {
            $condition .= ' AND g.`nocommission`=' . intval($args['nocommission']);
        }

        //是否参与店员销售
        if (isset($args['noClerkCommission'])) {
            $condition .= ' AND g.`noClerkCommission`=' . intval($args['noClerkCommission']);
        }

        //是否参与医生销售
        if (isset($args['doctor_no_commission'])) {
            $condition .= ' AND g.`doctor_no_commission`=' . intval($args['doctor_no_commission']);
        }

        //关键词
        $keywords = !empty($args['keywords']) ? $args['keywords'] : '';
        if (!empty($keywords)) {
            $condition .= ' AND (g.`title` LIKE :keywords OR g.`keywords` LIKE :keywords)';
            $params[':keywords'] = '%' . trim($keywords) . '%';
        }

        //分类
        if(!empty($args['cate'])){
            $category = m('shop')->getAllCategory();
            $catearr = array($args['cate']);
            foreach ($category as $index => $row) {
                if ($row['parentid'] == $args['cate']) {
                    $catearr[] = $row['id'];
                    foreach ($category as $ind => $ro) {
                        if ($ro['parentid'] == $row['id']) {
                            $catearr[] = $ro['id'];
                        }
                    }
                }
            }
            $catearr = array_unique($catearr);
            $condition .= " AND ( ";
            foreach ($catearr as $key=>$value){
                if ($key==0) {
                    $condition .= "FIND_IN_SET({$value},g.cates)";
                }else{
                    $condition .= " || FIND_IN_SET({$value},g.cates)";
                }
            }
            $condition .= " <>0 )";
        }

        $member =m('member')->getMember($openid);
        if (!empty($member)) {
            $levelid = intval($member['level']);
            $org_id = intval($member['org_id']);
            if (!empty($member['groupid'])){
                $groupid = explode(",",$member['groupid']);
                foreach ($groupid as $gid){
                    $groupid_condition = " or FIND_IN_SET('{$gid}',g.showgroups)";
                }
            }else{
                $groupid_condition = '';
            }

            $condition.=" and ( ifnull(g.showlevels,'')='' or FIND_IN_SET( {$levelid},g.showlevels)<>0 ) ";
            $condition.=" and ( ifnull(g.showgroups,'')='' {$groupid_condition} ) ";
            $condition.=" and ( ifnull(g.showOrgs,'')='' or FIND_IN_SET( {$org_id},g.showOrgs)<>0 ) ";
        } else {
            $condition.=" and ifnull(g.showlevels,'')='' ";
            $condition.=" and ifnull(g.showgroups,'')='' ";
            $condition.=" and ifnull(g.showOrgs,'')='' ";
        }
        $table =tablename('elapp_shop_goods').' g';
        if($couponid>0){
            $data = pdo_fetch('select c.*  from ' . tablename('elapp_shop_coupon_data') . '  cd inner join  ' . tablename('elapp_shop_coupon') . ' c on cd.couponid = c.id  where cd.id=:id and cd.uniacid=:uniacid and coupontype =0  limit 1', array(':id' => $couponid, ':uniacid' => $_W['uniacid']));
            if(!empty($data)){
                $i=0;
                $condition2="";

                if($data['limitgoodcatetype']==1&&!empty($data['limitgoodcateids'])){
                    $limitcateids=explode(',',$data['limitgoodcateids']);
                    if(count($limitcateids)>0){
                        foreach($limitcateids as $cateid){
                            $i++;
                            if($i>1){
                                $condition2 .=' or ';
                            }
                            $condition2 .=' FIND_IN_SET('.$cateid.',g.cates)';
                        }
                    }
                }

                if($data['limitgoodtype']==1&&!empty($data['limitgoodids'])){
                    $i++;
                    if($i>1){
                        $condition2 .=' or ';
                    }
                    $condition2 .='  g.id in ('.$data['limitgoodids'].') ';
                }

                if(!empty($condition2)){
                    $condition .='and ('.$condition2.') ';
                }
            }
        }

        if (!$random) {
            $sql = "SELECT  g.*  FROM " .$table . " where 1 {$condition} ORDER BY {$order} {$orderby} LIMIT " . ($page - 1) * $pagesize . ',' . $pagesize;
            $total = pdo_fetchcolumn("select  count(*) from " . $table . " where 1 {$condition} ",$params);
        } else {
            $sql = "SELECT  g.*  FROM " . $table . " where 1 {$condition} ORDER BY rand() LIMIT " . $pagesize;
            $total  = $pagesize;
        }

        $list = pdo_fetchall($sql, $params);
        $list = set_medias($list, 'thumb');
        return array("list"=>$list,"total"=>$total);
    }

    public function getTotals() {
        global $_W;
        return array(
            "sale" => pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_goods') . " where status > 0 and checked=0 and deleted=0 and stock>0 and  type !=30 and uniacid=:uniacid", array(':uniacid' => $_W['uniacid'])),
            "out" => pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_goods') . " where status > 0 and deleted=0 and stock=0 and type !=30 and uniacid=:uniacid", array(':uniacid' => $_W['uniacid'])),
            "stock" => pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_goods') . " where (status=0 or checked=1) and deleted=0 and uniacid=:uniacid and type !=30", array(':uniacid' => $_W['uniacid'])),
            "cycle" => pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_goods') . " where deleted=1 and uniacid=:uniacid and type !=30", array(':uniacid' => $_W['uniacid'])),
            "verify" => pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_goods') . " where deleted=0  and uniacid=:uniacid and type !=30 and merchid>0 and checked=1", array(':uniacid' => $_W['uniacid'])),
        );
    }

    /**
     * 获取宝贝评价
     * @param type $page
     * @param type $pagesize
     */
    public function getComments($goodsid = '0', $args = array()) {

        global $_W;
        $page = !empty($args['page']) ? intval($args['page']) : 1;
        $pagesize = !empty($args['pagesize']) ? intval($args['pagesize']) : 10;

        $condition = ' and `uniacid` = :uniacid AND `goodsid` = :goodsid and deleted=0';
        $params = array(':uniacid' => $_W['uniacid'], ':goodsid' => $goodsid);
        $sql = "SELECT id,nickname,headimgurl,content,images FROM " . tablename('elapp_shop_goods_comment') . " where 1 {$condition} ORDER BY createtime desc LIMIT " . ($page - 1) * $pagesize . ',' . $pagesize;
        $list = pdo_fetchall($sql, $params);
        foreach ($list as &$row) {
            $row['images'] = set_medias(unserialize($row['images']));
        }
        unset($row);
        return $list;
    }

    public function isFavorite($id = '') {
        global $_W;
        $count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_member_favorite') . " where goodsid=:goodsid and deleted=0 and openid=:openid and uniacid=:uniacid limit 1", array(':goodsid' => $id, ':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']));
        return $count > 0;
    }


    public function getCartCount($isnewstore = 0) {

        global $_W, $_GPC;
        //		if (empty($_W['mid'])) {
        //
        //			$carts = m('cookie')->get('carts');
        //			if (!is_array($carts)) {
        //				return 0;
        //			}
        //
        //			$cartcount = 0;
        //			foreach ($carts as $c => $total) {
        //				$cartcount+=$total;
        //			}
        //			return $cartcount;
        //		}
        $paras  = array(':uniacid' =>  $_W['uniacid']);
        $paras[':openid'] =$_W['openid'];

        //如果是O2O的购物车
        $sqlcondition="";
        if($isnewstore !=0){
            $sqlcondition=" and isnewstore=:isnewstore";
            $paras[':isnewstore'] =$isnewstore;
        }
        $count = pdo_fetchcolumn('select sum(total) from ' . tablename('elapp_shop_member_cart') . " where uniacid=:uniacid and openid=:openid ".$sqlcondition." and deleted=0 limit 1", $paras);
        return $count;
    }

    /**
     * 获取商品规格图片
     * @param type $specs
     * @return type
     */
    public function getSpecThumb($specs) {
        global $_W;

        $thumb = '';
        $cartspecs = explode('_', $specs);
        $specid = $cartspecs[0];

        if (!empty($specid)) {
            $spec = pdo_fetch("select thumb from " . tablename('elapp_shop_goods_spec_item') . " "
                . " where id=:id and uniacid=:uniacid limit 1 ", array(':id' => $specid, ':uniacid' => $_W['uniacid']));

            if (!empty($spec)) {
                if (!empty($spec['thumb'])) {
                    $thumb = $spec['thumb'];
                }
            }
        }
        return $thumb;
    }

    /**
     * 获取商品规格图片
     * @param type $specs
     * @return type
     */
    public function getOptionThumb($goodsid = 0, $optionid = 0) {
        global $_W;

        $thumb = '';
        $option  = $this->getOption($goodsid, $optionid);
        if (!empty($option)) {
            $specs = $option['specs'];
            $thumb = $this->getSpecThumb($specs);
        }
        return $thumb;
    }

    public function getAllMinPrice($goods){
        global $_W;

        if (is_array($goods)){
            $openid =$_W['openid'];
            $level = m('member')->getLevel($openid);
            $member = m('member')->getMember($openid);
            $levelid = $member['level'];

            foreach ($goods as &$value){
                $minprice = $value['minprice']; $maxprice = $value['maxprice'] ;

                if($value['isdiscount'] && $value['isdiscount_time']>=time()){
                    $value['oldmaxprice'] = $maxprice;
                    $isdiscount_discounts = json_decode($value['isdiscount_discounts'],true);
                    $prices = array();

                    if (!isset($isdiscount_discounts['type']) || empty($isdiscount_discounts['type'])) {
                        //统一促销
                        $prices_array = m('order')->getGoodsDiscountPrice($value, $level, 1);
                        $prices[] = $prices_array['price'];
                    } else {
                        //详细促销
                        $goods_discounts = m('order')->getGoodsDiscounts($value, $isdiscount_discounts, $levelid);
                        $prices = $goods_discounts['prices'];
                    }
                    $minprice = min($prices);
                    $maxprice = max($prices);
                }

                $value['minprice'] = $minprice;
                $value['maxprice'] = $maxprice;
            }
            unset($value);
        }else{
            $goods = array();
        }
        return $goods;
    }

    public function getOneMinPrice($goods){
        $goods = array($goods);
        $res = $this->getAllMinPrice($goods);
        return $res[0];
    }

    /**
     * 会员等级价格
     * @param goods,@$level
     * @return type
     * Hlei
     **/ 
    public function getMemberPrice($goods, $level){
        global $_W, $_GPC;
        if(!empty($goods['isnodiscount'])){
            return;
        }

        $liveid = intval($_GPC['liveid']);
        if(!empty($liveid)){
            $isliving = p('live')->isLiving($liveid);
            if(!$isliving){
                $liveid = 0;
            }
        }
        $discounts = is_array($goods['discounts']) ?$goods['discounts'] : json_decode($goods['discounts'], true);

        //如果是多商户商品，并且会员等级折扣为空的情况下，模拟空的商品会员等级折扣数据，以便计算折扣
        if(empty($goods['discounts']) && $goods['merchid']>0){
            $goods['discounts']=array(
                'type'=>'0',
                'default'=>'',
                'default_pay'=>''
            );
            if(!empty($level)){
                $goods['discounts']['level'.$level['id']]='';
                $goods['discounts']['level'.$level['id'].'_pay']='';
            }
            $discounts=$goods['discounts'];
        }

        if (is_array($discounts)) {
            $key = !empty($level['id']) ? 'level' . $level['id'] : 'default';
            if (!isset($discounts['type']) || empty($discounts['type'])) {
                // 统一设置折扣

                $memberprice = $goods['minprice'];

                if (!empty($discounts[$key])){
                    $dd = floatval($discounts[$key]); //设置的会员折扣
                    if ($dd > 0 && $dd < 10) {
                        $memberprice = round($dd / 10 * $goods['minprice'], 2);
                    }
                }else{
                    $dd = floatval($discounts[$key.'_pay']); //设置的会员折扣

                    $md = floatval($level['discount']); //会员等级折扣
                    if (!empty($dd)){
                        $memberprice = round($dd, 2);
                    }else if ($md > 0 && $md < 10) {
                        $memberprice = round($md / 10 * $goods['minprice'], 2);
                    }
                }
                if($memberprice==$goods['minprice']){
                    return false;
                }
                return $memberprice;
            } else {
                // 详细设置折扣
                $options = $this->getGoodsOptions($goods);
                $marketprice =  array();
                foreach ($options as $option){
                    // 获取会员等级折扣（带%为折扣否则为优惠后价格）
                    $discount = trim($discounts[$key]['option' . $option['id']]);
                    // 如果设置的会员折扣为空，则使用会员等级默认折扣
                    if($discount==''){
                        $discount = round(floatval($level['discount'])*10,2).'%';
                    }
                    if(!empty($liveid) && !empty($option['islive'])){
                        if($option['liveprice']>0 && $option['liveprice']<$option['marketprice']){
                            $option['marketprice'] = $option['liveprice'];
                        }
                    }
                    $optionprice = m('order')->getFormartDiscountPrice($discount, $option['marketprice']);
                    $marketprice[] =$optionprice;
                }
                if (count($marketprice) > 1 && is_array($marketprice)) {
                    $minprice = min($marketprice);
                    $maxprice = max($marketprice);
                    $memberprice = array(
                        'minprice' => (float)$minprice,
                        'maxprice' => (float)$maxprice
                    );
                    if ($memberprice['maxprice'] > $memberprice['minprice']) {
                        $memberprice = $memberprice['minprice'] . "~" . $memberprice['maxprice'];
                    } else {
                        $memberprice = $memberprice['minprice'];
                    }

                    if ($memberprice == $goods['minprice']) {
                        return false;
                    }
                    return $memberprice;
                }
            }
        }
        return;
    }

    /**
     * 获取产品规格
     * @param array $goods
     * @return array|false|mixed
     */
    public function getGoodsOptions(array $goods){
        global $_W;
        $id = $goods['id'];
        $specs = [];
        $options = [];
        if($goods['cycelbuy_goods_id']){
            $id = $goods['cycelbuy_goods_id'];
        }
        if (!empty($goods) && $goods['hasoption']) {
            $specs = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_spec') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
            foreach($specs as &$spec) {
                $spec['items'] = pdo_fetchall('select * from '.tablename('elapp_shop_goods_spec_item')." where specid=:specid order by displayorder asc",array(':specid'=>$spec['id']));
            }
            unset($spec);
            $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
        }
        if($goods['ispresell']>0 && ($goods['preselltimeend'] == 0 || $goods['preselltimeend'] > time())){
            foreach($options as &$val){
                $val['marketprice'] = $val['presellprice'];
            }
            unset($val);
        }
        return $options;
    }

    /**
     * 商品访问权限
     * @param array $goods
     * @param array $member
     * @return int
     */
    public function visit($goods=array(), $member=array()){
        global $_W;

        if(empty($goods)){
            return 1;
        }
        if(empty($member)){
            $member = m('member')->getMember($_W['openid']);
        }
        $showlevels = $goods['showlevels']!='' ? explode(',', $goods['showlevels']) : array();
        //$showgroups = $goods['showgroups']!='' ? explode(',', $goods['showgroups']) : array();
        $showgroups = $goods['showgroups']!='' ?$goods['showgroups'] : '';  //会员标签组
        $showOrgs = $goods['showOrgs']!='' ? explode(',', $goods['showOrgs']) : array();
        $showgoods = 0;
        //标签组浏览权限修改
        $isShow = false;  //是否可以查看
        $showgroupsArr = array();
        if(!empty($showgroups)){
            $showgroupsArr = explode(',', $showgroups);
        }
        if(empty($member['groupid'])){
            $member['groupid'] = '0';
        }

        if(strpos($member['groupid'],',') !== false || strpos($showgroups,',') !== false){
            $groupidArr = explode(',',$member['groupid']);
            foreach($groupidArr as $grk => $grv){
                if(in_array($grv,$showgroupsArr)){
                    $isShow = true;
                }
            }
        }else{
            if(strpos( $showgroups,(string)$member['groupid']) !== false){
                $isShow = true;
            }
        }

        if(!empty($member)){        
            if ((!empty($showlevels) && in_array($member['level'], $showlevels)) || (!empty($showgroups) && $isShow) || (empty($showlevels) && empty($showgroups)) || (!empty($showOrgs) && in_array($member['org_id'], $showOrgs)) ) {
                $showgoods = 1;
            }             
        }else{
            if(empty($showlevels) && empty($showgroups) && empty($showOrgs)){
                $showgoods = 1;
            }
        }
        return $showgoods;
    }

    /**
     * 批量修改普通商品为云商品
     * @param int $start_id 开始id
     * @param int $end_id 结束id
     * @param array $shield_ids 需要屏蔽的id
     * @return string
     * @throws Exception
     * 2023-07-04 21:26:24
     **/
    public function batchUpdate_cloud_goods(int $start_id, int $end_id, array $shield_ids = array()): string
    {
        //查询云商品cloud_code编码最大的值
        $result_cloud_code = pdo_fetch('SELECT MAX(cloud_code) + 1 as code FROM '.tablename('elapp_shop_goods').' WHERE is_cloud=1 and uniacid=:uniacid',array(':uniacid'=>$_W['uniacid']));
        $new_cloud_code = $result_cloud_code['code'];
        if ($new_cloud_code <= 0) {
            $new_cloud_code = 10000000;//如果 $new_cloud_code 的值小于等于 0，将其补全为 8 位数，并从 10000000 开始重新赋值
        } else {
            $new_cloud_code = max(10000000, $new_cloud_code); // 确保云商品编码大于等于 10000000
        }
        // 补全8位数
        $new_cloud_code_str = str_pad($new_cloud_code, 8, '0', STR_PAD_LEFT);        
        for($i = $start_id; $i <= $end_id; $i++){
            foreach($shield_ids as $sid){
                if($i == $sid) {continue;}                        
                $sql = pdo_query('UPDATE ' . tablename('elapp_shop_goods') . " SET goodsClassID=2,medicineClassID=2,medicineAttributeID=1,merchid=0,is_cloud=1,cloud_code=" . $new_cloud_code_str . " WHERE id=" . $i . "\n");
                $new_cloud_code_str ++;
            }
        }
        return 'ok';
    }
    /**
     *是否已经有重复购买的商品
     * @param $goods
     * @return bool
     **/
    public function canBuyAgain($goods){
        global $_W;
        $condition = '';
        $id = $goods['id'];
        if (isset($goods['goodsid'])){
            $id = $goods['goodsid'];
        }
        if (empty($goods['buyagain_islong'])){
            $condition = ' AND canbuyagain = 1';
        }
        $order_goods = pdo_fetchall("SELECT id,orderid FROM ".tablename('elapp_shop_order_goods')." WHERE uniacid=:uniaicd AND openid=:openid AND goodsid=:goodsid {$condition}",array(':uniaicd'=>$_W['uniacid'],':openid'=>$_W['openid'],':goodsid'=>$id),'orderid');

        if (empty($order_goods)){
            return false;
        }
        $order = pdo_fetchcolumn("SELECT COUNT(*) FROM ".tablename('elapp_shop_order')." WHERE uniacid=:uniaicd AND status>=:status AND id IN (".implode(',',array_keys($order_goods)).")",array(':uniaicd'=>$_W['uniacid'],':status'=>(empty($goods['buyagain_condition'])?'1':'3')));
        return !empty($order);
    }

    /**
     * 使用掉重复购买的变量
     * @param int $orderid
     * @return bool
     **/
    public function useBuyAgain(int $orderid): bool
    {
        global $_W;
        $order_goods = pdo_fetchall("SELECT id,goodsid FROM ".tablename('elapp_shop_order_goods')." WHERE uniacid=:uniaicd AND openid=:openid AND canbuyagain = 1 AND orderid <> :orderid",array(':uniaicd'=>$_W['uniacid'],':openid'=>$_W['openid'],'orderid'=>$orderid),'goodsid');
        if (empty($order_goods)){
            return false;
        }
        pdo_query('UPDATE '.tablename('elapp_shop_order_goods')." SET `canbuyagain`='0' WHERE uniacid=:uniacid AND goodsid IN (".implode(',',array_keys($order_goods)).")",array(':uniacid'=>$_W['uniacid']));
        return true;
    }

    /**
     * 获取任务商品
     * @param $openid
     * @param $goodsid
     * @param $rank
     * @param int $log_id
     * @param int $join_id
     * @param int $optionid
     * @param int $total
     * @return array
     */
    public function getTaskGoods($openid, $goodsid, $rank, int $log_id = 0, int $join_id = 0, int $optionid = 0, int $total = 0): array
    {
        global $_W;

        $is_task_goods = 0;
        $is_task_goods_option = 0;

        if(!empty($join_id)) {
            $task_plugin = p('task');
            $flag = 1;
        } elseif(!empty($log_id)) {
            $task_plugin = p('lottery');
            $flag = 2;
        }

        $param = array();
        $param['openid'] = $openid;
        $param['goods_id'] = $goodsid;
        $param['rank'] = $rank;
        $param['join_id'] = $join_id;
        $param['log_id'] = $log_id;
        $param['goods_spec'] = $optionid;
        $param['goods_num'] = $total;

        if ($task_plugin && (!empty($join_id) || !empty($log_id))) {
            $task_goods = $task_plugin->getGoods($param);
        }

        if (!empty($task_goods) && empty($total) && (!empty($join_id) || !empty($log_id))) {
            if (!empty($task_goods['spec'])) {
                foreach ($task_goods['spec'] as $k => $v) {
                    if (empty($v['total'])) {
                        unset($task_goods['spec'][$k]);
                        continue;
                    }

                    if (!empty($optionid)) {
                        if ($k == $optionid) {
                            $task_goods['marketprice'] = $v['marketprice'];
                            $task_goods['total'] = $v['total'];
                        } else {
                            unset($task_goods['spec'][$k]);
                        }
                    }

                    if (!empty($optionid) && $k != $optionid) {
                        unset($task_goods['spec'][$k]);
                    } else if (!empty($optionid) && $k != $optionid) {
                        $task_goods['marketprice'] = $v['marketprice'];
                        $task_goods['total'] = $v['total'];
                    }
                }
                if (!empty($task_goods['spec'])) {
                    $is_task_goods = $flag;
                    $is_task_goods_option = 1;
                }
            } else {
                if (!empty($task_goods['total'])) {
                    $is_task_goods = $flag;
                }
            }
        }

        $data = array();
        $data['is_task_goods'] = $is_task_goods;
        $data['is_task_goods_option'] = $is_task_goods_option;
        $data['task_goods'] = $task_goods;

        return $data;
    }

    /**
     * 获取批发价格
     * @param array $goods
     * @return array|null|mixed
     */
    public function wholesaleprice(array $goods): ?array
    {
        $goods2= array();

        if (!empty($goods)) {
            foreach($goods as $good) {
                if(is_array($good) && isset($good['type']) && $good['type'] == 4){
                    if(empty($goods2[$good['goodsid']])){
                        $intervalprices = array();
                        if($good['intervalfloor']>0){
                            $intervalprices[]=array("intervalnum"=>intval($good['intervalnum1']),"intervalprice"=>floatval($good['intervalprice1']));
                        }
                        if($good['intervalfloor']>1) {
                            $intervalprices[]=array("intervalnum"=>intval($good['intervalnum2']),"intervalprice"=>floatval($good['intervalprice2']));
                        }
                        if($good['intervalfloor']>2){
                            $intervalprices[]=array("intervalnum"=>intval($good['intervalnum3']),"intervalprice"=>floatval($good['intervalprice3']));
                        }

                        $goods2[$good['goodsid']]=array('goodsid'=>$good['goodsid'],'total'=>$good['total'],'intervalfloor'=>$good['intervalfloor'],'intervalprice'=>$intervalprices);
                    } else {
                        $goods2[$good['goodsid']]['total']+=$good['total'];
                    }
                }
            }
        }

        foreach($goods2 as $good2) {
            $intervalprices2 = $good2['intervalprice'];

            $price=0;
            foreach($intervalprices2 as $intervalprice){
                if($intervalprice['intervalnum']<=$good2['total'])
                {
                    $price=$intervalprice['intervalprice'];
                }
            }
            unset($intervalprice);

            foreach($goods as &$good) {
                if($good['goodsid']==$good2['goodsid']) {
                    $good['wholesaleprice']=$price;
                    $good['goodsalltotal']=$good2['total'];
                }
            }
            unset($good);
        }
        unset($good2);
        return $goods?:null;
    }

    /**
     * 生成二维码
     * @param array $parameter
     * @return string
     */
    public function createcode(array $parameter): string
    {
        global $_W;
        $path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . "/goodscode/" . $_W['uniacid'] . "/";
        $goodsid = $parameter['goodsid'];
        $qrcode = $parameter['qrcode'];
        $data = $parameter['codedata'];
        $mid = $parameter['mid'];
        $codeshare = $parameter['codeshare'];
        $isGoodCodeAloneRules = !empty($parameter['isGoodCodeAloneRules']) ?  true : false;
        if (!is_dir($path)) {
            load()->func('file');
            mkdirs($path);
        }
        //文件名称，如果参数有变动，重新生成
        $md5 = md5(json_encode(array(
            'uniacid' => $_W['uniacid'],
            'goodsid' => $goodsid,
            'title' => $data['title']['text'],
            'price' => $data['price']['text'],
            'shopPriceText' => $data['shopPriceText']['text'],
            'seckillPrice' => $data['seckillPrice']['text'],
            'seckillText' => $data['seckillText']['text'],
            'maxMemberLevelPrice' => $data['maxMemberLevelPrice']['text'],
            'memberCardPriceText' => $data['memberCardPriceText']['text'],
            'oldprice' => $data['oldprice']['text'],
            'oldPriceText' => $data['oldPriceText']['text'],
            'sloganText' => $data['sloganText']['text'],
            'discountPrice' => $data['discountPrice']['text'],
            'discountText' => $data['discountText']['text'],
            'banWechatText' => $data['banWechatText']['text'],
            'comefromText' => $data['comefromText']['text'],
            'shareText' => $data['shareText']['text'],
            'nicknameText' => $data['nicknameText']['text'],
            'codeshare' => $parameter['codeshare'],
            'codedata'=>$data,
            'mid' => $mid,
            'version'=> 2024051101, //增加个版本号，才能生成新下海报
        )));
        $file = $md5 . '.png';
        if (!is_file($path . $file)) {
            //未生成过，或二维码变化
            //生成背景
            set_time_limit(0);
            @ini_set("memory_limit", "256M");
            if($codeshare==1){
                $target = imagecreatetruecolor(640, 1037);
                // $color = imagecolorAllocate($target, 255, 255, 255);   //分配一个白色,参数四为透明度
                // imagefill($target, 0, 0, $color);                 // 从左上角开始填充
                // imagecopy($target, $target, 255, 255, 255, 20, 640, 1060);
                //填充透明背景部分
                $bg = imagecreatetruecolor(640, 880);
                $bgcolor = imagecolorAllocate($target, 255, 255, 255);
                imagefill($bg, 0, 0, $bgcolor);
                imagecopy($target, $bg, 0, 180, 0, 0, 640, 800);
                //创建一条虚线5个橙色像素，4个背景像素
                $bgpx = imagecolorAllocate($target, 204, 204, 204);//虚线背景像素
                $redpx = imagecolorAllocate($target, 237, 109, 15);//虚线橙色像素
                $colorline = array($redpx, $redpx, $redpx, $redpx, $redpx, $bgpx, $bgpx, $bgpx, $bgpx);                
                imagesetstyle($target, $colorline);
                imageline($target,30,965,610,965,IMG_COLOR_STYLED);//画直线

                $target = $this->mergeText($target, $data['shopname'], $data['shopname']['text']);
                $thumb = preg_replace('/\/0$/i', '/96', $data['portrait']['thumb']);

                // 通用
                $thumb = $isGoodCodeAloneRules ? 'https://file.snkdyf.com/images/qrcode_bg_activity2.png' :'https://file.snkdyf.com/images/qrcode_bg_202403020.png';
                
                if ($thumb) {
                    $data['portrait']['wdith'] = 640;
                    $data['portrait']['height'] = 1037;
                    $target = $this->mergeImage($target, $data['portrait'], $thumb);
                }
                $thumb = preg_replace('/\/0$/i', '/96', $data['thumb']['thumb']);
                $target = $this->mergeImage($target, $data['thumb'], $thumb);
                $qrcode = preg_replace('/\/0$/i', '/96', $data['qrcode']['thumb']);
                $target = $this->mergeImage($target, $data['qrcode'], $qrcode);
                $target = $this->mergeText($target, $data['title'], $data['title']['text']);
                
                //创建一条横线
                if(!empty($data['maxMemberLevelPrice'])){
                    $line_color = imagecolorAllocate($target, 153, 153, 153);//实线背景像素
                    $colorline = array($line_color);
                    imagesetstyle($target, $colorline);
                    imagesetthickness($target, 2); //设置线条的粗细为 3 像素
                    imageline($target,424,787,490,787,IMG_COLOR_STYLED);//画直线    
                }else{
                    $data['shopPriceText']['left'] = 60;
                    $data['shopPriceText']['top'] = 780;
                    $data['price']['left'] = 125;
                    $data['price']['top'] = 773;
                    $data['price']['color'] = "#FF6600";
                    $data['price']['size'] = 24;
                }
                

                $target = $this->mergeText($target, $data['price'], $data['price']['text']);
                $target = $this->mergeText($target, $data['shopPriceText'], $data['shopPriceText']['text']);
                $target = $this->mergeText($target, $data['markPrice'] , $data['markPrice']['text']);
                // $target = $this->mergeText($target, $data['seckillPrice'], $data['seckillPrice']['text']);
                // $target = $this->mergeText($target, $data['seckillText'], $data['seckillText']['text']);

                $target = $this->mergeText($target, $data['maxMemberLevelPrice'], $data['maxMemberLevelPrice']['text']);
                $target = $this->mergeText($target, $data['memberCardPriceText'], $data['memberCardPriceText']['text']);
                // $target = $this->mergeText($target, $data['oldprice'], $data['oldprice']['text']);
                // $target = $this->mergeText($target, $data['oldPriceText'], $data['oldPriceText']['text']);
                // $target = $this->mergeText($target, $data['desc'], $data['desc']['text']);
                if(empty($data['discountText']['text']) && empty($data['discountPrice']['text'])){
                    $data['sloganText']['top'] = 920;
                }
                $target = $this->mergeText($target, $data['sloganText'], $data['sloganText']['text']);
                $target = $this->mergeText($target, $data['discountPrice'], $data['discountPrice']['text']);
                $target = $this->mergeText($target, $data['discountText'], $data['discountText']['text']);
                // $target = $this->mergeText($target, $data['banWechatText'], $data['banWechatText']['text']);
                $target = $this->mergeText($target, $data['comefromText'], $data['comefromText']['text']);
                $target = $this->mergeText($target, $data['shareText'], $data['shareText']['text']);
                $target = $this->mergeText($target, $data['nicknameText'], $data['nicknameText']['text']);
                //header("Content-type: image/png");//导致首次扫码访问出现黑现象，屏蔽正常
                imagepng($target, $path . $file);
                imagedestroy($target);//释放图像资源

            }
            if($codeshare==2){
                $target = imagecreatetruecolor(640, 640);
                $color = imagecolorAllocate($target, 255, 255, 255,127);   //分配一个白色
                imagefill($target, 0, 0, $color);                 // 从左上角开始填充

                $colorline = imagecolorallocate($target,0,0,0,127);//创建一个颜色，以供使用
                imageline($target,0,190,640,190,$colorline);

                $red = imagecolorallocate($target,254, 155, 68);//创建一个颜色，以供使用
                //imagerectangle($target,0,560,640,640,$red);
                imagefilledrectangle($target,0,560,640,640,$red);

                imagecopy($target, $target, 0, 0, 0, 0, 640, 640);

                $thumb = preg_replace('/\/0$/i', '/96', $data['thumb']['thumb']);
                $target = $this->mergeImage($target, $data['thumb'], $thumb);
                $target = $this->mergeText($target, $data['title'], $data['title']['text']);
                $target = $this->mergeText($target, $data['price'], $data['price']['text']);
                $target = $this->mergeText($target, $data['shopPriceText'], $data['shopPriceText']['text']);
                $target = $this->mergeText($target, $data['seckillPrice'], $data['seckillPrice']['text']);
                $target = $this->mergeText($target, $data['seckillText'], $data['seckillText']['text']);
                $target = $this->mergeText($target, $data['maxMemberLevelPrice'], $data['maxMemberLevelPrice']['text']);
                $target = $this->mergeText($target, $data['memberCardPriceText'], $data['memberCardPriceText']['text']);
                $qrcode = preg_replace('/\/0$/i', '/96', $data['qrcode']['thumb']);
                $target = $this->mergeImage($target, $data['qrcode'], $qrcode);
                $target = $this->mergeText($target, $data['desc'], $data['desc']['text']);
                $target = $this->mergeText($target, $data['sloganText'], $data['sloganText']['text']);
                $target = $this->mergeText($target, $data['discountPrice'], $data['discountPrice']['text']);
                $target = $this->mergeText($target, $data['discountText'], $data['discountText']['text']);
                $target = $this->mergeText($target, $data['shopname'], $data['shopname']['text'],true);
                $target = $this->mergeText($target, $data['comefromText'], $data['comefromText']['text']);
                $target = $this->mergeText($target, $data['shareText'], $data['shareText']['text'],true);
                $target = $this->mergeText($target, $data['nicknameText'], $data['nicknameText']['text'],true);
                //header("Content-type: image/png");
                imagepng($target, $path . $file);
                imagedestroy($target);//释放图像资源

            }elseif($codeshare==3) {
                $target = imagecreatetruecolor(640, 1060);
                $color = imagecolorAllocate($target, 245, 244, 249,127);   //分配一个灰色
                imagefill($target, 0, 0, $color);                 // 从左上角开始填充
                imagecopy($target, $target, 0, 0, 0, 127, 640, 1008);

                $target = $this->mergeText($target, $data['title'], $data['title']['text']);
                $target = $this->mergeText($target, $data['price'], $data['price']['text']);
                $target = $this->mergeText($target, $data['shopPriceText'], $data['shopPriceText']['text']);
                $target = $this->mergeText($target, $data['seckillPrice'], $data['seckillPrice']['text']);
                $target = $this->mergeText($target, $data['seckillText'], $data['seckillText']['text']);
                $target = $this->mergeText($target, $data['maxMemberLevelPrice'], $data['maxMemberLevelPrice']['text']);
                $target = $this->mergeText($target, $data['memberCardPriceText'], $data['memberCardPriceText']['text']);
                $target = $this->mergeText($target, $data['desc'], $data['desc']['text']);
                $target = $this->mergeText($target, $data['sloganText'], $data['sloganText']['text']);
                $target = $this->mergeText($target, $data['discountPrice'], $data['discountPrice']['text']);
                $target = $this->mergeText($target, $data['discountText'], $data['discountText']['text']);
                $target = $this->mergeText($target, $data['comefromText'], $data['comefromText']['text']);
                $target = $this->mergeText($target, $data['shareText'], $data['shareText']['text']);
                $target = $this->mergeText($target, $data['nicknameText'], $data['nicknameText']['text']);

                $thumb = preg_replace('/\/0$/i', '/96', $data['thumb']['thumb']);
                $target = $this->mergeImage($target, $data['thumb'], $thumb);

                $qrcode = preg_replace('/\/0$/i', '/96', $data['qrcode']['thumb']);
                $target = $this->mergeImage($target, $data['qrcode'], $qrcode);
                //header("Content-type: image/png");
                imagepng($target, $path . $file);
                imagedestroy($target);//释放图像资源
            }elseif($codeshare==4) {
                $target = imagecreatetruecolor(750, 1170);
                $color = imagecolorAllocate($target, 245, 244, 249);   //分配一个灰色
                imagefill($target, 0, 0, $color);                 // 从左上角开始填充
                imagecopy($target, $target, 0, 0, 0, 127, 640, 1008);


                $target = $this->mergeImage($target, ['left'=>0, 'top'=>0,'height'=>1170,'width'=>750], $data['commission_thumb']['thumb']);

                // 来自 xxx 分享
                $target = $this->mergeText($target, $data['nicknameText'], $data['nicknameText']['text']);
                $target = $this->mergeText($target, $data['comefromText'], $data['comefromText']['text']);
                $target = $this->mergeText($target, $data['shareText'], $data['shareText']['text']);


                $qrcode = preg_replace('/\/0$/i', '/96', $data['qrcode']['thumb']);
                $target = $this->mergeImage($target, $data['qrcode'], $qrcode);
                //header("Content-type: image/png");
                imagepng($target, $path . $file);
                imagedestroy($target);//释放图像资源
            }
        }
        //$this->radius(15);
        $img = $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . "/goodscode/" . $_W['uniacid'] . "/" . $file;
        return $img;
    }
    /**
     * 处理圆角图片
     * @param int $radius 圆角半径长度默认为15,处理成圆型
     * @return bool|\GdImage|resource
     */
    public function radius(int $radius = 15) {
        // $wh = getimagesize($imgpath);
        $w = 640;
        $h = 1060;
        // $radius = $radius == 0 ? (min($w, $h) / 2) : $radius;
        $img = imagecreatetruecolor($w, $h);
        //这一句一定要有
        imagesavealpha($img, true);
        //拾取一个完全透明的颜色,最后一个参数127为全透明
        $bg = imagecolorallocatealpha($img, 255, 255, 255, 127);
        imagefill($img, 0, 0, $bg);
        $r = $radius; //圆 角半径
        for ($x = 0; $x < $w; $x++) {
            for ($y = 0; $y < $h; $y++) {
                $rgbColor = imagecolorat($this->im, $x, $y);
                if (($x >= $radius && $x <= ($w - $radius)) || ($y >= $radius && $y <= ($h - $radius))) {
                    //不在四角的范围内,直接画
                    imagesetpixel($img, $x, $y, $rgbColor);
                } else {
                    //在四角的范围内选择画
                    //上左
                    $y_x = $r; //圆心X坐标
                    $y_y = $r; //圆心Y坐标
                    if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
                        imagesetpixel($img, $x, $y, $rgbColor);
                        continue;
                    }
                    //上右
                    $y_x = $w - $r; //圆心X坐标
                    $y_y = $r; //圆心Y坐标
                    if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
                        imagesetpixel($img, $x, $y, $rgbColor);
                        continue;
                    }
                    //下左
                    $y_x = $r; //圆心X坐标
                    $y_y = $h - $r; //圆心Y坐标
                    if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
                        imagesetpixel($img, $x, $y, $rgbColor);
                        continue;
                    }
                    //下右
                    $y_x = $w - $r; //圆心X坐标
                    $y_y = $h - $r; //圆心Y坐标
                    if (((($x - $y_x) * ($x - $y_x) + ($y - $y_y) * ($y - $y_y)) <= ($r * $r))) {
                        imagesetpixel($img, $x, $y, $rgbColor);
                        continue;
                    }
                }
            }
        }
        //$this->im = $img;
        return $img;
    }

    /**
     * 创建图片
     * @param string $imgurl
     * @return bool|\GdImage|resource
     */
    public function createImage(string $imgurl) {
        global $_W;
        ini_set("memory_limit","-1");
        if(strpos($imgurl,str_replace(array('https://','http://'),'',$_W['siteroot']))!==0){
        //if (!strexists($imgurl,str_replace(array('https://','http://'),'',$_W['siteroot']))){
            load()->func('communication');
            $resp = ihttp_request($imgurl);
            if ($resp['code'] == 200 && !empty($resp['content'])) {
                return @imagecreatefromstring($resp['content']);
            }
            //todo why for 3??
            $i = 0;
            while ($i < 3) {
                $resp = ihttp_request($imgurl);
                if ($resp['code'] == 200 && !empty($resp['content'])) {
                    return @imagecreatefromstring($resp['content']);
                }
                $i++;
            }
        }else{
            $count = str_replace($_W['siteroot'],IA_ROOT.'/',$imgurl);
            $imgurl = file_get_contents($count);
            return @imagecreatefromstring($imgurl);
        }
        return "";
    }

    /**
     * 合并图片
     * @param $target
     * @param $data
     * @param $imgurl
     * @return bool
     */
    public function mergeImage($target, $data, $imgurl) {
        $img = $this->createImage($imgurl);
        if (!empty($img)) {
            $w = imagesx($img);
            $h = imagesy($img);
            imagecopyresized($target, $img, $data['left'], $data['top'], 0, 0, $data['width'], $data['height'], $w, $h);
            imagedestroy($img);
        }
        return $target;
    }

    /**
     * 合并文字
     * @param $target
     * @param $data
     * @param $text
     * @param bool $center
     * @return bool|resource|void
     */
    public function mergeText($target, $data, $text, bool $center = false) {
        $font = isset($data['bold']) && $data['bold'] ? realpath(IA_ROOT . "/public/static/application/shop/fonts/opposam-H.ttf") : realpath(IA_ROOT . "/public/static/application/shop/fonts/PingFangBold.ttf");
        $colors = $this->hex2rgb($data['color']);
        $color = imagecolorallocate($target, $colors['red'], $colors['green'], $colors['blue']);
        if($center) {
            $fontBox = imagettfbbox($data['size'], 0, $font, $data['text']);//文字水平居中实质
            imagettftext($target, $data['size'], 0, ceil(($data['width'] - $fontBox[2]) / 2), $data['top'] + $data['size'], $color, $font, $text);
        }else{
            imagettftext($target, $data['size'], 0, $data['left'], $data['top'] + $data['size'], $color, $font, $text);
        }
        return $target;
    }

    /**
     * 颜色转换
     * @param $colour
     * @return array|bool
     * <AUTHOR> 2022/06/07
     */
    function hex2rgb($colour) {
        if ($colour[0] == '#') {
            $colour = substr($colour, 1);
        }
        if (strlen($colour) == 6) {
            list( $r, $g, $b ) = array($colour[0] . $colour[1], $colour[2] . $colour[3], $colour[4] . $colour[5]);
        } elseif (strlen($colour) == 3) {
            list( $r, $g, $b ) = array($colour[0] . $colour[0], $colour[1] . $colour[1], $colour[2] . $colour[2]);
        } else {
            return false;
        }
        $r = hexdec($r);
        $g = hexdec($g);
        $b = hexdec($b);
        return array('red' => $r, 'green' => $g, 'blue' => $b);
    }
    /**
     * 统计已成功购买商品数量
     * @param $openid
     * @param $goodsid
     * @return array|bool
     * <AUTHOR> 2022/06/07
     */    
    function goodsPurchasedCount($openid,$goodsid){
        global $_W;
        if(empty($openid)){
            return false;
        }
        if(empty($goodsid)){
            return false;
        }
        //默认按月计算
        $starttime = strtotime(date('Y-m') . '-1');//开始时间
        $endtime = strtotime(date('Y-m') . '-' . date("t"));//结束时间
        if (isset($starttime) && isset($endtime)) {
            $where_time = ' and og.createtime between ' . $starttime . ' and ' . $endtime;
        }        
        //已购买的商品数
        $count = pdo_fetchcolumn('select sum(og.total) from ' . tablename('elapp_shop_order_goods') . ' og ' . ' left join  ' . tablename('elapp_shop_order') . ' o on o.id = og.orderid' . ' where og.goodsid=:goodsid and o.openid=:openid and o.status>=0 and o.uniacid=:uniacid and o.isparent=0 ' . $where_time . ' ', array(':uniacid' => $_W['uniacid'], ':openid' => $openid, ':goodsid' => $goodsid));
        return $count;
    }

    /**
     * 判断商品是否被全局禁用分润
     * 在后台 [设置 - 交易 - 交易设置 - 禁止药品分润] 处设置
     * @param $og
     * @return bool
     */
    public function isDisabledCommission($og)
    {
        $trade = m('common')->getSysset('trade');
        // medicineClassID medicineAttributeID

        if (isset($og['goodsid']) && (!isset($og['medicineClassID']) || !isset($og['medicineAttributeID']))) {
            $og = pdo_get('elapp_shop_goods', ['id'=>$og['goodsid']]);
        }

        if (isset($og['medicineClassID']) &&
            isset($trade['no_commission_medicine_class_ids']) &&
            is_array($trade['no_commission_medicine_class_ids']) &&
            in_array($og['medicineClassID'], $trade['no_commission_medicine_class_ids'])) {
            return true;
        }

        if (isset($og['medicineAttributeID']) &&
            isset($trade['no_commission_medicine_attribute_ids']) &&
            is_array($trade['no_commission_medicine_attribute_ids']) &&
            in_array($og['medicineAttributeID'], $trade['no_commission_medicine_attribute_ids'])) {
            return true;
        }

        return false;
    }

    /**
     * 获取商品信息
     * @param $where
     * @param string $field
     * @return false|mixed
     */
    public function info($where, string $field = "id"){
        $data = pdo_fetch("select ".$field." from " . tablename('elapp_shop_goods') . ' where '.$where.' Limit 1',array());
        return $data;
    }
}