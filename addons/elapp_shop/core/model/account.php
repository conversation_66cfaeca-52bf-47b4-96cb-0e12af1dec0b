<?php
namespace app\model;

use app\com\enum\member\MemberTerminalEnum;
use app\com\service\MemberTokenService;
use think\facade\Cookie;

class AccountModel {

	public function checkLogin(){

		global $_W,$_GPC;

		if(empty($_W['openid'])) {

			$openid = $this->checkOpenid();
			if(!empty($openid)){
				return $openid;
			}

			$url = urlencode(base64_encode($_SERVER['QUERY_STRING']));
			$loginurl = mobileUrl('account/index/login', array('mid' => $_GPC['mid'], 'backurl'=>$_W['isajax']?"":$url));

			if($_W['isajax']){
				show_json(0, array('url'=>$loginurl, 'message'=>'请先登录!'));
			}

			header('location: ' . $loginurl);
			exit;
		}
	}

	public function checkOpenid(){
		global $_W,$_GPC;

		$key = '__elapp_shop_member_session_' . $_W['uniacid'];


		if (isset($_GPC[$key])) {
			$session = json_decode(base64_decode($_GPC[$key]), true);
			if (is_array($session)) {
				$member = m('member')->getMember($session['openid']);
				if (is_array($member) && $session['elapp_shop_member_hash'] == md5($member['pwd'] . $member['salt'])) {
					$GLOBALS['_W']['elapp_shop_member_hash'] = md5($member['pwd'] . $member['salt']);
					$GLOBALS['_W']['elapp_shop_member'] = $member;
					return $member['openid'];
				} else {
					isetcookie($key, false, -100);
				}
			}
		}
	}

	public function setLogin($member) {
		global $_W;

		if(!is_array($member)){
			$member = m('member')->getMember($member);
		}
		if(!empty($member)){
			$member['elapp_shop_member_hash'] = md5($member['pwd'].$member['salt']);
			$key = '__elapp_shop_member_session_'.$_W['uniacid'];
			$cookie = base64_encode( json_encode($member) );
			isetcookie($key, $cookie,30 * 86400);
            $_SESSION['member_info'] = $_W['member_info'] = m('member')->get_member_role_relation(['id' => $member['id']]);
            $_SESSION['openid'] = $member['openid'];
            $memberInfo = MemberTokenService::setToken($member["id"], MemberTerminalEnum::H5);
            isetcookie("__token__", $memberInfo["token"], $memberInfo["expire_time"]);
            Cookie::set("__token__", $memberInfo["token"], $memberInfo["expire_time"]);
		}
	}

	//清除登录指纹
	public function clearLoginCookie(){
		global $_W;
		$key = '__elapp_shop_member_session_'.$_W['uniacid'];
		isetcookie($key, '',time() - 3600);
		unset($_SESSION['member_info']);
		unset($_SESSION['openid']);
		isetcookie("__token__",'',time() - 3600);
		Cookie::delete("__token__");
	}

	public function getSalt(){
		$salt = random(16);
		while(1)  {
			$count = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_member').' where salt=:salt limit 1',array(':salt'=>$salt));
			if($count<=0){
				break;
			}
			$salt = random(16);
		}
		return $salt;
	}

    public function uni_accounts($uniacid = 0) {
        global $_W;
        $uniacid = empty($uniacid) ? $_W['uniacid'] : intval($uniacid);
        $account_info = pdo_get('account', array('uniacid' => $uniacid));
        if (!empty($account_info)) {
            $account_tablename = uni_account_type($account_info['type']);
            $account_tablename = $account_tablename['table_name'];
            $accounts = pdo_fetchall("SELECT w.*, a.type, a.isconnect FROM " . tablename('account') . " a INNER JOIN " . tablename($account_tablename) . " w USING(acid) WHERE a.uniacid = :uniacid AND a.isdeleted <> 1 ORDER BY a.acid ASC", array(':uniacid' => $uniacid), 'acid');
        }
        return !empty($accounts) ? $accounts : array();
    }

}
