<?php
namespace app\model;

use think\db\exception\DataNotFoundException;

class DispatchModel {
    /**
     * 计算运费
     * @param type $param 重量或者是数量
     * @param type $d
     * @param type $calculatetype -1默认读取$d中的calculatetype值 1按数量计算运费 0按重量计算运费
     */
    public function getDispatchPrice($param, $d, $calculatetype = -1)
    {
        if (empty($d)) {
            return 0;
        }

        $price = 0;

        if ($calculatetype == -1) {
            $calculatetype = $d['calculatetype'];
        }

        if ($calculatetype == 1) {
            if ($param <= $d['firstnum']) {
                $price = floatval($d['firstnumprice']);
            } else {
                $price = floatval($d['firstnumprice']);
                $secondweight = $param - floatval($d['firstnum']);
                $dsecondweight = floatval($d['secondnum']) <= 0 ? 1 : floatval($d['secondnum']);
                $secondprice = 0;

                if ($secondweight % $dsecondweight == 0) {
                    $secondprice = $secondweight / $dsecondweight * floatval($d['secondnumprice']);
                } else {
                    $secondprice = ((int)($secondweight / $dsecondweight) + 1) * floatval($d['secondnumprice']);
                }

                $price += $secondprice;
            }
        } else if ($param <= $d['firstweight']) {
            if (0 <= $param) {
                $price = floatval($d['firstprice']);
            } else {
                $price = 0;
            }
        } else {
            $price = floatval($d['firstprice']);
            $secondweight = $param - floatval($d['firstweight']);
            $dsecondweight = floatval($d['secondweight']) <= 0 ? 1 : floatval($d['secondweight']);
            $secondprice = 0;

            if ($secondweight % $dsecondweight == 0) {
                $secondprice = $secondweight / $dsecondweight * floatval($d['secondprice']);
            } else {
                $secondprice = ((int)($secondweight / $dsecondweight) + 1) * floatval($d['secondprice']);
            }

            $price += $secondprice;
        }

        return $price;
    }

    /**
     * 获取指定城市/地区的运费价格
     *
     * 用途：根据用户收货地址匹配对应的运费规则，并计算运费价格。
     * 逻辑：
     *   1. 检查系统是否启用"新地区编码"模式（兼容新旧数据格式）。
     *   2. 若为旧模式，按城市名称匹配；若为新模式，按行政区域编码匹配。
     *   3. 匹配成功后返回对应运费规则的价格，否则返回默认运费。
     *
     * @param array $areas 运费规则配置数组（包含不同地区的运费设置）
     *      - 结构示例: [['citys'=>'北京;上海', 'firstprice'=>10, ...], ...]
     *      - 或新编码模式: [['citys_code'=>'110000;310000', ...], ...]
     * @param array $address 用户收货地址信息
     *      - 必需字段:
     *          - 'city' => 城市名称（旧模式使用，如"北京市"）
     *          - 'datavalue' => 地区编码（新模式使用，如"110000"）
     * @param array $param 运费计算参数（通常包含商品重量/体积/数量等）
     * @param array $d 默认运费配置（无匹配地区时使用）
     *      - 必需字段: 'calculatetype' => 运费计算类型（按件/按重量/按体积等）
     *
     * @return float 计算后的运费金额
     */
    public function getCityDispatchPrice($areas, $address, $param, $d)
    {
        // 获取当前城市信息
        $city = $address['city'];

        // 检查系统是否启用新地区编码模式
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);

        // 旧模式：按城市名称匹配
        if (empty($new_area)) {
            if (is_array($areas) && 0 < count($areas)) {
                foreach ($areas as $area) {
                    // 解析运费规则中的城市列表（用分号分隔）
                    $citys = explode(';', $area['citys']);
                    $citys = array_filter($citys); // 过滤空值

                    // 如果当前城市在规则列表中，则使用该规则计算运费
                    if (!empty($citys) && in_array($city, $citys)) {
                        return $this->getDispatchPrice($param, $area, $d['calculatetype']);
                    }
                }
            }
        }
        // 新模式：按行政编码匹配
        else {
            $address_datavalue = trim($address['datavalue']);
            if (is_array($areas) && 0 < count($areas)) {
                foreach ($areas as $area) {
                    $citys_code = explode(';', $area['citys_code']);
                    // 匹配地区编码且列表非空
                    if (in_array($address_datavalue, $citys_code) && !empty($citys_code)) {
                        return $this->getDispatchPrice($param, $area, $d['calculatetype']);
                    }
                }
            }
        }

        // 无匹配规则时返回默认运费
        return $this->getDispatchPrice($param, $d);
    }

    /**
     * 获取默认运费模板/快递信息
     *
     * 用途：查询指定商户在当前小程序中启用的默认运费模板
     * 典型场景：
     *   1. 商品未单独设置运费模板时，使用默认模板,商城用全局默认模板，商户用商户默认模板
     *   2. 购物车结算时计算基础运费
     *
     * @param int $merchid 商户ID
     *      - 0 = 平台自营（总后台配置的默认模板）
     *      - >0 = 指定商户的默认模板
     * @return array|false 返回运费模板数据，结构示例：
     *      [
     *          'id' => 1,                     // 模板ID
     *          'dispatchtype' => 1,            // 计费类型（1=统一运费,2=模板运费）
     *          'firstprice' => 10.00,          // 首件运费
     *          'secondprice' => 5.00,          // 续件运费
     *          'nodispatchareas' => '北京;上海' // 不配送区域
     *          ...其他字段见数据表结构
     *      ]
     *      无默认模板时返回false
     */
    public function getDefaultDispatch(int $merchid = 0)
    {
        global $_W;

        // 查询条件说明：
        // - isdefault=1   : 标记为默认模板的记录
        // - uniacid       : 当前小程序ID（多小程序隔离）
        // - merchid       : 匹配传入的商户ID
        // - enabled=1     : 只查询启用状态的模板
        $sql = 'SELECT * FROM ' . tablename('elapp_shop_dispatch') .
            ' WHERE isdefault=1 AND uniacid=:uniacid AND merchid=:merchid AND enabled=1 LIMIT 1';

        $params = array(
            ':uniacid' => $_W['uniacid'],  // 当前公众平台唯一ID
            ':merchid' => $merchid         // 绑定查询的商户ID
        );

        // 执行查询并返回结果（单条记录）
        return pdo_fetch($sql, $params);
    }

    /**
     * 获取最新创建的运费模板
     *
     * 用途：当没有默认模板时，获取全局或该商户最近创建的可用运费模板作为备选方案
     * 典型场景：
     *   1. 商户未设置默认模板时，自动使用最新创建的模板
     *   2. 运费计算时作为fallback方案
     *
     * @param int $merchid 商户ID
     *      - 0 = 平台自营（获取平台最新模板）
     *      - >0 = 指定商户的最新模板
     * @return array|false 返回运费模板数据，结构同getDefaultDispatch()
     *      示例：
     *      [
     *          'id' => 3,                     // 模板ID（最新创建的记录）
     *          'dispatchtype' => 2,            // 计费类型
     *          'firstprice' => 8.00,           // 首件运费
     *          'secondprice' => 3.00,          // 续件运费
     *          ...其他字段
     *      ]
     *      无可用模板时返回false
     */
    public function getNewDispatch(int $merchid = 0)
    {
        global $_W;

        // 查询说明：
        // - 不限制isdefault字段（包含所有非默认模板）
        // - enabled=1 只查询启用状态的模板
        // - 按id降序获取最新创建的记录
        $sql = 'SELECT * FROM ' . tablename('elapp_shop_dispatch') .
            ' WHERE uniacid=:uniacid AND merchid=:merchid AND enabled=1 ' .
            ' ORDER BY id DESC LIMIT 1';

        $params = array(
            ':uniacid' => $_W['uniacid'],  // 当前公众平台唯一ID
            ':merchid' => $merchid         // 目标商户ID
        );

        return pdo_fetch($sql, $params);
    }

    /**
     * 获取指定运费模板详情（优化版）
     *
     * 用途：根据模板ID精确查询运费模板信息，当ID为0时直接调用getDefaultDispatch()
     * 优化点：避免重复代码，提升可维护性
     *
     * @param int $id 模板ID
     *      - 0 = 自动调用getDefaultDispatch()获取默认模板
     *      - >0 = 查询指定ID的模板
     * @return array|false 运费模板数据（结构同getDefaultDispatch）
     */
    public function getOneDispatch(int $id=0)
    {
        global $_W;

        // 优化逻辑：当ID为0时直接调用现有方法
        if ($id == 0) {
            return $this->getDefaultDispatch(0); // 0表示平台默认模板
        }

        // 精确查询指定ID的模板
        $sql = 'SELECT * FROM ' . tablename('elapp_shop_dispatch') .
            ' WHERE id=:id AND uniacid=:uniacid AND enabled=1 LIMIT 1';

        return pdo_fetch($sql, [
            ':uniacid' => $_W['uniacid'],
            ':id' => $id
        ]);
    }

    /**
     * 获取全局不配送地区列表（系统级+商户级）
     *
     * 用途：合并系统默认和商户自定义的不配送地区，用于：
     * 1. 下单时校验地址是否可配送
     * 2. 商品/商户管理页展示完整限制区域
     * 3. 运费计算时排除限制区域
     *
     * @param array|string $areas 商户自定义限制区域（可接受序列化字符串或数组）
     * @param int $type 地区格式类型：
     *     - 0 = 地区名称（如"广东省;深圳市"）
     *     - 1 = 地区编码（如"440000;440300"）
     * @return array 去重后的不配送地区列表，示例：
     *     - 名称模式：['北京市', '上海市']
     *     - 编码模式：['110000', '310000']
     */
    public function getAllNoDispatchAreas($areas = [], int $type = 0)
    {
        global $_W;

        // 1. 获取系统级不配送设置
        $tradeset = m('common')->getSysset('trade');
        $dispatchareas = $type ? $tradeset['nodispatchareas_code'] : $tradeset['nodispatchareas'];
        $dispatchareas = iunserializer($dispatchareas); // 反序列化商城特有格式

        // 2. 处理系统默认限制区域
        $set_citys = [];
        if (!empty($dispatchareas)) {
            $set_citys = explode(';', trim($dispatchareas, ';')); // 拆分为数组
        }

        // 3. 处理商户自定义限制区域
        $dispatch_citys = [];
        if (!empty($areas)) {
            $areas = is_string($areas) ? iunserializer($areas) : $areas;
            if (!empty($areas)) {
                $dispatch_citys = explode(';', trim($areas, ';'));
            }
        }

        // 4. 合并并去重
        $result = $set_citys;
        if (!empty($dispatch_citys)) {
            $result = array_merge($result, $dispatch_citys);
            $result = array_unique($result); // 确保无重复项
        }

        return $result;
    }

    /**
     * 检查用户所在地区是否在不配送区域内（支持新旧地区编码模式）
     *
     * 用途：根据用户所在城市编码，验证该地区是否被运费模板标记为不配送区域
     * 核心逻辑：
     *   1. 根据系统配置决定使用旧版城市名称还是新版行政编码校验
     *   2. 将模板中的不配送区域数据反序列化后与用户地址比对
     *
     * @param string $user_city_code 用户所在城市标识
     *      - 旧模式：城市名称（如"北京市"）
     *      - 新模式：行政区域编码（如"110000"）
     * @param array $dispatch_data 运费模板数据
     *      - 必需字段：
     *          - nodispatchareas     : 旧模式不配送区域（序列化字符串，如"北京;上海"）
     *          - nodispatchareas_code: 新模式不配送编码（序列化字符串，如"110000;310000"）
     * @return int 返回验证结果
     *      - 1 = 可以配送（用户地址不在不配送列表中）
     *      - 0 = 不可配送（命中不配送区域）
     */
    public function checkOnlyDispatchAreas($user_city_code, $dispatch_data)
    {
        global $_W;

        // 获取系统地区配置（决定使用新旧哪种模式）
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);

        // 根据模式选择校验字段
        $areas = $new_area ? $dispatch_data['nodispatchareas_code'] : $dispatch_data['nodispatchareas'];

        // 默认可以配送（当用户地址或配送区域为空时直接放行）
        $isnoarea = 1;

        // 当用户地址和配送区域数据都存在时进行校验
        if (!empty($user_city_code) && !empty($areas)) {
            // 反序列化存储的区域数据
            $areas = iunserializer($areas);
            // 拆分成数组并去除空值
            $citys = explode(';', trim($areas, ';'));

            // 命中不配送区域则返回不可配送
            if (in_array($user_city_code, $citys)) {
                $isnoarea = 0;
            }
        }

        return $isnoarea;
    }

    /**
     * 获取全场或商户不配送的地区 merchid=0为全场不配送，merchid>0为商户不配送
     * @param $goods
     * @param int $moduleType 模块类型（0=主商城，1=积分商城，2=秒杀...）
     * @return array|string
     * @throws DataNotFoundException
     * @data 2025-04-19
     * <AUTHOR>
     */
	public function getNoDispatchAreas($goods, int $moduleType = 0)
	{
		global $_W;

        // 新版不配送区域方法，暂时不用
        /*$dispatch = $dispatchModel->getDispatchDataForGoods($goods);
        $config = $this->getDispatchAreasConfig($dispatch);
        // 只返回黑名单模式下的地区
        return $config['mode'] === 0 ? $config['areas'] : [];*/

        // 根据模块类型决定使用哪个字段判断商品类型
        $typeField = 'type'; // 默认主商城
        switch ($moduleType) {
            case 1: // 积分商城
                $typeField = 'goodstype';
                break;
            case 2: // 秒杀
                $typeField = 'seckill_type'; // todo 假设秒杀模块的商品类型字段是 seckill_type
                break;
            // 可继续扩展其他模块
        }

        // 判断商品是否支持配送
        if (isset($goods[$typeField])) {
            $currentType = $goods[$typeField];
            if ($currentType == 2 || $currentType == 3) {
                return '';
            }
        }

        $onlysent = 0;
        $citys = [];
        $dispatch = [];

		if ($goods['dispatchtype'] == 1) {
            // 统一运费
			$dispatchareas = $this->getAllNoDispatchAreas();
		} else {
            // 运费模板
			if (empty($goods['dispatchid'])) {
				$dispatch = m('dispatch')->getDefaultDispatch($goods['merchid']);
			} else {
				$dispatch = m('dispatch')->getOneDispatch($goods['dispatchid']);
			}

			if (empty($dispatch)) {
				$dispatch = m('dispatch')->getNewDispatch($goods['merchid']);
			}

			if (empty($dispatch['isdispatcharea'])) {
				$onlysent = 0;
				$citys = $this->getAllNoDispatchAreas($dispatch['nodispatchareas']);
			} else {
				$onlysent = 1;
				$dispatchareas = unserialize($dispatch['nodispatchareas']);
				$citys = explode(';', trim($dispatchareas, ';'));
			}
		}

		return array('onlysent' => $onlysent, 'citys' => $citys, 'enabled' => $dispatch['enabled']);
	}

    /**
     * 获取指定城市的包邮条件金额
     *
     * 用途：根据用户收货地址匹配运费模板中的包邮规则，返回该地区享受包邮的最低消费金额
     * 核心逻辑：
     *   1. 根据系统配置决定使用城市名称(old)或行政编码(new)匹配
     *   2. 遍历所有包邮规则，找到第一个匹配的规则
     *   3. 返回对应规则的包邮门槛金额
     *
     * @param array $areas 包邮规则集合，结构示例：
     *     [
     *         [
     *             'citys' => '北京;上海',    // 旧模式城市列表
     *             'citys_code' => '110000',  // 新模式编码列表
     *             'freeprice' => 88.00       // 包邮条件金额
     *         ],
     *         ...
     *     ]
     * @param array $address 收货地址信息，需包含：
     *     - 旧模式: ['city' => '北京市']
     *     - 新模式: ['datavalue' => '110000']
     * @return float|null 返回匹配的包邮金额，未匹配时返回null
     */
    public function getCityfreepricePrice($areas, $address)
    {
        // 获取当前城市信息
        $city = $address['city'];

        // 检查系统是否启用新地区编码
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);

        // 旧模式：按城市名称匹配
        if (empty($new_area)) {
            if (is_array($areas) && count($areas) > 0) {
                foreach ($areas as $area) {
                    // 解析城市列表并过滤空值
                    $citys = explode(';', $area['citys']);
                    $citys = array_filter($citys);

                    // 命中匹配则返回包邮金额
                    if (!empty($citys) && in_array($city, $citys)) {
                        return floatval($area['freeprice']);
                    }
                }
            }
        }
        // 新模式：按行政编码匹配
        else {
            $address_datavalue = trim($address['datavalue']);
            if (is_array($areas) && count($areas) > 0) {
                foreach ($areas as $area) {
                    $citys_code = explode(';', $area['citys_code']);

                    // 需同时满足：编码匹配且城市列表非空
                    if (!empty($citys_code) && in_array($address_datavalue, $citys_code)) {
                        return floatval($area['freeprice']);
                    }
                }
            }
        }

        // 无匹配规则时返回null（区别于返回0，更符合业务语义）
        return null;
    }

    ################## 以下为业务逻辑辅助方法 Hlei 2025-04-19 ##################
    /**
     * 获取配送区域配置（兼容新旧模式）
     * @param array $dispatchData 运费模板数据
     * @param int $type 返回类型 0=名称 1=编码
     * @return array [
     *     'mode' => 0/1, // 0不配送模式 1只配送模式
     *     'areas' => [区域列表],
     *     'field' => 使用的字段名
     * ]
     */
    public function getDispatchAreasConfig(array $dispatchData, int $type = 0): array
    {
        $areaSet = m('util')->get_area_config_set();
        $field = $type ? 'nodispatchareas_code' : 'nodispatchareas';

        return [
            'mode' => (int)($dispatchData['isdispatcharea'] ?? 0),
            'areas' => !empty($dispatchData[$field])
                ? explode(';', trim(iunserializer($dispatchData[$field]), ';'))
                : [],
            'field' => $field
        ];
    }

    ##基础验证方法
    /**
     * 验证商品是否参与单品包邮
     * @param array $goods 商品信息
     * @param float $totalAmount 订单总金额
     * @param int $totalQuantity 订单总数量
     * @return bool
     */
    public function isItemFreeShippingEligible(array $goods, float $totalAmount, int $totalQuantity): bool
    {
        // 检查是否设置了包邮条件
        $hasEdnum = isset($goods['ednum']) && $goods['ednum'] > 0;
        $hasEdmoney = isset($goods['edmoney']) && $goods['edmoney'] > 0;

        if (!$hasEdnum && !$hasEdmoney) {
            return false;
        }

        // 检查是否满足包邮条件
        $meetsNumCondition = $hasEdnum && ($totalQuantity >= $goods['ednum']);
        $meetsMoneyCondition = $hasEdmoney && ($totalAmount >= $goods['edmoney']);

        return $meetsNumCondition || $meetsMoneyCondition;
    }

    /**
     * 检查地址是否在不包邮地区
     *
     * @param array $address 收货地址
     * @param array $goods 商品信息
     * @return bool
     */
    public function isInExcludedFreeShippingArea(array $address, array $goods): bool
    {
        // 获取系统地区配置（决定使用新旧哪种模式）
        $area_set = m('util')->get_area_config_set();
        $new_area = !empty($area_set['new_area']);

        // 根据模式选择校验字段
        $excludedAreas = $new_area ? $goods['edareas_code'] : $goods['edareas'];

        if (empty($excludedAreas)) {
            return false;
        }

        // 反序列化存储的区域数据
        $excludedAreas = iunserializer($excludedAreas);
        if (empty($excludedAreas)) {
            return false;
        }

        // 拆分成数组并去除空值
        $excludedList = array_filter(explode(';', trim($excludedAreas, ';')));

        // 获取地址值
        $addressValue = $new_area ? trim($address['datavalue']) : $address['city'];

        return in_array($addressValue, $excludedList);
    }

    /**
     * 验证地址是否在不配送区域
     * @param array $address 收货地址
     * @param array $goods 商品信息
     * @return bool
     * @throws DataNotFoundException
     */
    public function isInNoDispatchArea(array $address, array $goods): bool
    {
        $noDispatchAreas = $this->getNoDispatchAreas($goods);
        if (empty($noDispatchAreas['citys'])) {
            return false;
        }

        $areaSet = m('util')->get_area_config_set();
        $newArea = (int)$areaSet['new_area'];
        $addressValue = $newArea ? trim($address['datavalue']) : $address['city'];

        return in_array($addressValue, $noDispatchAreas['citys']);
    }

    ##组合验证方法

    /**
     * 综合验证商品配送和包邮条件
     * @param array $goods 商品信息
     * @param array $address 收货地址
     * @param float $totalAmount 订单总金额
     * @param int $totalQuantity 订单总数量
     * @return array [可配送, 可包邮, 不配送原因]
     * @throws DataNotFoundException
     */
    public function validateShippingConditions(
        array $goods,
        array $address,
        float $totalAmount = 0,
        int $totalQuantity = 0
    ): array {
        $canDispatch = !$this->isInNoDispatchArea($address, $goods);
        $canFreeShipping = false;
        $reason = '';

        if (!$canDispatch) {
            $reason = '您所在的地区不支持配送';
            return [false, false, $reason];
        }

        if ($this->isInExcludedFreeShippingArea($address, $goods)) {
            $reason = '您所在的地区不参与包邮活动';
            return [true, false, $reason];
        }

        $canFreeShipping = $this->isItemFreeShippingEligible($goods, $totalAmount, $totalQuantity);

        return [true, $canFreeShipping, $reason];
    }
    ##增强验证方法

    /**
     * 增强版配送条件验证（考虑全局和商户级别限制）
     * @param array $goods 商品信息
     * @param array $address 收货地址
     * @param int $merchId 商户ID
     * @param float $totalAmount 订单总金额
     * @param int $totalQuantity 订单总数量
     * @return array [可配送, 可包邮, 不配送原因]
     * @throws DataNotFoundException
     */
    public function validateEnhancedShippingConditions(
        array $goods,
        array $address,
        int $merchId = 0,
        float $totalAmount = 0,
        int $totalQuantity = 0
    ): array {
        // 1. 验证基础配送条件
        [$canDispatch, $canFreeShipping, $reason] = $this->validateShippingConditions(
            $goods, $address, $totalAmount, $totalQuantity
        );

        if (!$canDispatch) {
            return [false, false, $reason];
        }

        // 2. 检查全局/商户级不配送限制
        $globalNoDispatch = $this->getMergedNoDispatchAreas($merchId, 1);
        $addressValue = trim($address['datavalue']);

        if (in_array($addressValue, $globalNoDispatch)) {
            return [false, false, '您所在的地区暂不支持配送'];
        }

        // 3. 检查全局/商户级包邮排除
        $globalExcludes = $this->getMergedExcludedFreeShippingAreas($merchId, 1);

        if (in_array($addressValue, $globalExcludes)) {
            return [true, false, '您所在的地区不参与包邮活动'];
        }

        return [true, $canFreeShipping, $reason];
    }
    ##信息获取方法
    /**
     * 获取商品不参与包邮的地区列表
     * @param array $goods 商品信息
     * @param int $type 0=地区名称，1=地区编码
     * @return array
     */
    public function getExcludedFreeShippingAreas(array $goods, int $type = 0): array
    {
        $field = $type ? 'edareas_code' : 'edareas';

        if (empty($goods[$field])) {
            return [];
        }

        $areas = iunserializer($goods[$field]);
        return explode(';', trim($areas, ';'));
    }

    /**
     * 获取商品完整配送限制信息
     * @param array $goods 商品信息
     * @param int $moduleType 模块类型
     * @return array
     * @throws DataNotFoundException
     */
    public function getFullShippingRestrictions(array $goods, int $moduleType = 0): array
    {
        $dispatch = $this->getDispatchDataForGoods($goods);
        $config = $this->getDispatchAreasConfig($dispatch);
        $noDispatch = $this->getNoDispatchAreas($goods, $moduleType);

        return [
            'dispatch_mode' => $config['mode'], // 新增模式标识
            'restricted_areas' => $config['areas'],
            'no_dispatch' => [
                'areas' => $noDispatch['citys'] ?? [],
                'only_sent' => $noDispatch['onlysent'] ?? 0,
                'enabled' => $noDispatch['enabled'] ?? 0
            ],
            'excluded_free_shipping' => [
                'names' => $this->getExcludedFreeShippingAreas($goods, 0),
                'codes' => $this->getExcludedFreeShippingAreas($goods, 1)
            ],
            'free_shipping_conditions' => [
                'by_quantity' => $goods['ednum'] ?? 0,
                'by_amount' => $goods['edmoney'] ?? 0
            ]
        ];
    }
    ##数据处理方法
    ##合并不配送地区的方法
    /**
     * 合并全局和商户级别的不配送地区
     * @param int $merchId 商户ID (0=平台)
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array 合并后的不配送地区列表
     */
    public function getMergedNoDispatchAreas(int $merchId = 0, int $type = 0): array
    {
        // 1. 获取系统全局不配送地区
        $systemAreas = $this->getSystemNoDispatchAreas($type);

        // 2. 获取商户自定义不配送地区
        $merchantAreas = $merchId > 0
            ? $this->getMerchantNoDispatchAreas($merchId, $type)
            : [];

        // 3. 合并并去重
        return array_unique(array_merge($systemAreas, $merchantAreas));
    }

    /**
     * 获取系统全局不配送地区
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array
     */
    protected function getSystemNoDispatchAreas(int $type = 0): array
    {
        global $_W;
        $tradeset = m('common')->getSysset('trade');
        $field = $type ? 'nodispatchareas_code' : 'nodispatchareas';

        if (empty($tradeset[$field])) {
            return [];
        }

        $areas = iunserializer($tradeset[$field]);
        return array_filter(
            explode(';', trim($areas, ';')),
            function($item) {
                return !empty(trim($item)); // 过滤所有空值
            }
        );
    }

    /**
     * 获取商户配送配置（含严格数据清洗）
     * @param int $merchId 商户ID
     * @param int $type 返回类型 0=名称 1=编码
     * @return array [
     *     'areas'   => 地区数组（已清洗）,
     *     'mode'    => 0黑名单/1白名单,
     *     'field'   => 使用的字段名,
     *     'cleaned' => 原始数据是否被清洗过（调试用）
     * ]
     */
    protected function getMerchantDispatchConfig(int $merchId, int $type = 0): array
    {
        $dispatch = $this->getDefaultDispatch($merchId);
        if (empty($dispatch)) {
            return [
                'areas' => [],
                'mode' => 0,
                'field' => $type ? 'nodispatchareas_code' : 'nodispatchareas',
                'cleaned' => false
            ];
        }

        // 字段动态选择
        $field = $type ? 'nodispatchareas_code' : 'nodispatchareas';
        $mode = (int)($dispatch['isdispatcharea'] ?? 0);
        $cleaned = false;

        // 数据清洗管道
        $areas = [];
        if (!empty($dispatch[$field])) {
            $raw = iunserializer($dispatch[$field]);

            // 清洗步骤
            $areas = array_values(array_filter(
                array_map('trim', explode(';', trim($raw, ';'))),
                function($item) {
                    return $item !== '' && $item !== null;
                }
            ));

            // 对比原始数据判断是否被清洗
            $original = explode(';', trim($raw, ';'));
            $cleaned = count($original) !== count($areas);
        }

        return [
            'areas' => $areas,
            'mode' => $mode,
            'field' => $field,
            'cleaned' => $cleaned // 调试用，正式环境可移除
        ];
    }

    /**
     * 获取商户自定义不配送地区（兼容旧调用）
     * @param int $merchId 商户ID
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @deprecated 建议使用getMerchantDispatchConfig()
     */
    protected function getMerchantNoDispatchAreas(int $merchId, int $type = 0): array
    {
        $config = $this->getMerchantDispatchConfig($merchId, $type);
        return $config['mode'] === 0 ? $config['areas'] : [];
    }

    ##合并包邮排除地区的方法
    /**
     * 合并全局和商户级别的包邮排除地区
     * @param int $merchId 商户ID (0=平台)
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array 合并后的包邮排除地区列表
     */
    public function getMergedExcludedFreeShippingAreas(int $merchId = 0, int $type = 0): array
    {
        // 1. 获取系统全局包邮排除地区
        $systemExcludes = $this->getSystemExcludedFreeShippingAreas($type);

        // 2. 获取商户自定义包邮排除地区
        $merchantExcludes = $merchId > 0 ? $this->getMerchantExcludedFreeShippingAreas($merchId, $type) : [];

        // 3. 合并并去重
        return array_unique(array_merge($systemExcludes, $merchantExcludes));
    }

    /**
     * 获取系统全局包邮排除地区
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array
     */
    protected function getSystemExcludedFreeShippingAreas(int $type = 0): array
    {
        global $_W;
        $marketingSet = m('common')->getSysset('marketing');
        $field = $type ? 'edareas_code' : 'edareas';

        if (empty($marketingSet[$field])) {
            return [];
        }

        $areas = iunserializer($marketingSet[$field]);
        return explode(';', trim($areas, ';'));
    }

    /**
     * 获取商户自定义包邮排除地区
     * @param int $merchId 商户ID
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array
     */
    protected function getMerchantExcludedFreeShippingAreas(int $merchId = 0, int $type = 0): array
    {
        $merchantSet = p('merch')->getSet();
        $field = $type ? 'edareas_code' : 'edareas';

        if (empty($merchantSet[$field])) {
            return [];
        }

        $areas = iunserializer($merchantSet[$field]);
        return explode(';', trim($areas, ';'));
    }

    ##增强的配送限制获取方法##
    /**
     * 获取商品的完整配送限制（合并系统、商户和商品自身设置）
     * @param array $goods 商品信息（必须包含merchid字段）
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array [
     *     'no_dispatch' => 不配送地区,
     *     'excluded_free' => 不包邮地区,
     *     'source' => 各限制来源明细
     * ]
     */
    public function getGoodsShippingRestrictions(array $goods, int $type = 0): array
    {
        $merchId = (int)$goods['merchid'];
        return [
            // 不配送地区（合并系统、商户和商品设置）
            'no_dispatch' => $this->getGoodsNoDispatchAreas($goods, $type),

            // 不包邮地区（合并系统、商户和商品设置）
            'excluded_free' => $this->getGoodsExcludedFreeAreas($goods, $type),

            // 各限制来源明细（用于调试或前端展示）
            'source' => [
                'system' => $this->getSystemNoDispatchAreas($type),
                'merchant' => $merchId > 0 ? $this->getMerchantNoDispatchAreas($merchId, $type) : [],
                'goods' => $this->getGoodsOwnNoDispatchAreas($goods, $type),
                'system_excluded' => $this->getSystemExcludedFreeShippingAreas($type),
                'merchant_excluded' => $merchId > 0 ? $this->getMerchantExcludedFreeShippingAreas($merchId, $type) : [],
                'goods_excluded' => $this->getGoodsOwnExcludedFreeAreas($goods, $type)
            ]
        ];
    }

    /**
     * 获取商品最终不配送地区（合并系统、商户和商品设置）
     * @param array $goods 商品信息
     * @param int $type 返回类型 (0=地区名称,1=地区编码)
     * @return array
     */
    private function getGoodsNoDispatchAreas(array $goods, int $type): array
    {
        $merchId = (int)$goods['merchid'];
        $areas = [];

        // 1. 系统设置（增加过滤）
        $areas = array_merge($areas, array_filter($this->getSystemNoDispatchAreas($type)));

        // 2. 商户设置（增加过滤）
        if ($merchId > 0) {
            $areas = array_merge($areas, array_filter($this->getMerchantNoDispatchAreas($merchId, $type)));
        }

        // 3. 商品自身设置（增加过滤）
        $areas = array_merge($areas, array_filter($this->getGoodsOwnNoDispatchAreas($goods, $type)));

        // 去重并重新索引数组
        return array_values(array_unique(array_filter($areas)));
    }

    /**
     * 获取商品自身不配送地区（含多级数据清洗）
     * @param array $goods 商品信息（需包含nodispatchareas/nodispatchareas_code字段）
     * @param int $type 返回类型 0=地区名称 1=地区编码
     * @return array 已清洗的地区数组（保证无空值、无重复、已trim）
     */
    private function getGoodsOwnNoDispatchAreas(array $goods, int $type): array
    {
        // 1. 字段动态选择
        $field = $type ? 'nodispatchareas_code' : 'nodispatchareas';

        // 2. 空值快速返回
        if (empty($goods[$field])) {
            return [];
        }

        // 3. 反序列化原始数据
        $areas = iunserializer($goods[$field]);
        if (!is_string($areas)) { // 防御异常数据
            return [];
        }

        // 4. 数据清洗管道
        return array_values( // 重置数字索引
            array_unique( // 去重
                array_filter( // 过滤空值
                    array_map('trim', explode(';', trim($areas, ';'))),
                    function ($item) {
                        // 过滤：空字符串、null、纯空格
                        return $item !== '' && $item !== null;
                    }
                )
            )
        );
    }

    /**
     * 获取商品最终不包邮地区（合并系统、商户和商品设置）
     */
    private function getGoodsExcludedFreeAreas(array $goods, int $type): array
    {
        $merchId = (int)$goods['merchid'];
        $areas = [];

        // 1. 系统全局设置
        $areas = array_merge($areas, $this->getSystemExcludedFreeShippingAreas($type));

        // 2. 所属商户设置（如果是商户商品）
        if ($merchId > 0) {
            $areas = array_merge($areas, $this->getMerchantExcludedFreeShippingAreas($merchId, $type));
        }

        // 3. 商品自身设置
        $areas = array_merge($areas, $this->getGoodsOwnExcludedFreeAreas($goods, $type));

        return array_unique($areas);
    }

    /**
     * 获取商品自身设置的不包邮地区
     */
    private function getGoodsOwnExcludedFreeAreas(array $goods, int $type): array
    {
        $field = $type ? 'edareas_code' : 'edareas';

        if (empty($goods[$field])) {
            return [];
        }

        $areas = iunserializer($goods[$field]);
        return explode(';', trim($areas, ';'));
    }

    ##配送区域数据获取方法##
    /**
     * 获取商品对应的运费模板数据
     * @param array $goods 商品信息
     * @return array 运费模板数据
     */
    private function getDispatchDataForGoods(array $goods): array
    {
        if ($goods['dispatchtype'] == 1) {
            // 统一运费模式
            return [
                'isdispatcharea' => 0, // 默认不配送模式
                'nodispatchareas' => '',
                'nodispatchareas_code' => ''
            ];
        }

        if (!empty($goods['dispatchid'])) {
            $dispatch = $this->getOneDispatch($goods['dispatchid']);
        } else {
            $dispatch = $this->getDefaultDispatch($goods['merchid']);
        }

        return $dispatch ?: [
            'isdispatcharea' => 0,
            'nodispatchareas' => '',
            'nodispatchareas_code' => ''
        ];
    }

    ##增强的验证方法##
    /**
     * 验证地址是否可配送（完整逻辑）
     * @param array $address 收货地址
     * @param array $dispatchData 运费模板
     * @return array [
     *     'result' => bool,
     *     'message' => 提示信息,
     *     'mode' => 当前模式
     * ]
     */
    public function validateDispatchArea(array $address, array $dispatchData): array
    {
        // 获取区域配置
        $config = $this->getDispatchAreasConfig($dispatchData, 1);
        $addressValue = $config['field'] === 'nodispatchareas_code'
            ? trim($address['datavalue'])
            : $address['city'];

        $inList = in_array($addressValue, $config['areas']);

        // 根据模式返回结果
        if ($config['mode'] === 1) {
            return [
                'result' => $inList,
                'message' => $inList ? '' : '您所在地区不在配送范围内',
                'mode' => 1
            ];
        }

        return [
            'result' => !$inList,
            'message' => $inList ? '您所在地区暂不支持配送' : '',
            'mode' => 0
        ];
    }

    /**
     * 完整配送验证（考虑系统、商户和商品三级设置）
     * @param array $goods 商品信息（必须包含merchid）
     * @param array $address 收货地址
     * @param float $totalAmount 订单金额
     * @param int $totalQuantity 订单数量
     * @return array [可配送, 可包邮, 不配送原因]
     */
    public function validateCompleteShippingConditions(
        array $goods,
        array $address,
        float $totalAmount = 0,
        int $totalQuantity = 0
    ): array {
        // 1. 获取运费模板
        $dispatchData = $this->getDispatchDataForGoods($goods);

        // 2. 验证区域配送规则
        if (!$this->validateDispatchArea($address, $dispatchData)) {
            $mode = (int)$dispatchData['isdispatcharea'];
            $reason = $mode === 1
                ? '您所在的地区不在配送范围内'
                : '您所在的地区暂不支持配送';
            return [false, false, $reason];
        }
        // 1. 获取所有限制
        $restrictions = $this->getGoodsShippingRestrictions($goods, 1);
        $addressValue = trim($address['datavalue']);

        // 2. 检查不配送地区
        if (in_array($addressValue, $restrictions['no_dispatch'])) {
            return [false, false, '您所在的地区不支持配送'];
        }

        // 3. 检查包邮排除地区
        if (in_array($addressValue, $restrictions['excluded_free'])) {
            return [true, false, '您所在的地区不参与包邮活动'];
        }

        // 4. 检查商品包邮条件
        $canFreeShipping = $this->isItemFreeShippingEligible($goods, $totalAmount, $totalQuantity);

        return [true, $canFreeShipping, ''];
    }

    /**
     * 商品配送验证总入口
     * @param array $goods 商品信息
     * @param array $address 收货地址
     * @param float $amount 订单金额
     * @param int $quantity 商品数量
     * @return array [
     *     'can_dispatch' => bool,
     *     'can_free' => bool,
     *     'message' => string,
     *     'restrictions' => 限制详情
     * ]
     */
    public function validateGoodsShipping(array $goods, array $address, float $amount = 0, int $quantity = 0): array
    {
        // 1. 获取运费模板
        $dispatchData = $this->getDispatchDataForGoods($goods);

        // 2. 验证区域限制
        $areaCheck = $this->validateDispatchArea($address, $dispatchData);
        if (!$areaCheck['result']) {
            return [
                'can_dispatch' => false,
                'can_free' => false,
                'message' => $areaCheck['message'],
                'restrictions' => $this->getDispatchAreasConfig($dispatchData)
            ];
        }

        // 3. 其他验证逻辑...
        // [保留原有包邮条件验证...]
        return [];
    }

    /**
     * 检查商品是否满足包邮条件（综合增强版）
     *
     * @param array $goods 商品信息数组，需包含以下字段：
     *     - ednum: 满件包邮数量
     *     - edmoney: 满额包邮金额
     *     - money: 商品单价
     *     - edareas: 不包邮地区(序列化字符串)
     *     - edareas_code: 不包邮地区编码(序列化字符串)
     * @param int $quantity 购买数量
     * @param array|null $address 收货地址信息，包含：
     *     - city: 城市名称
     *     - datavalue: 地区编码
     * @return bool 是否满足包邮条件
     */
    public function checkFreeShipping(array $goods, int $quantity, array $address = null): bool
    {
        // 1. 检查是否启用任何包邮条件
        $hasEdnum = isset($goods['ednum']) && $goods['ednum'] > 0;
        $hasEdmoney = isset($goods['edmoney']) && $goods['edmoney'] > 0;

        if (!$hasEdnum && !$hasEdmoney) {
            return false;
        }

        // 2. 检查是否在不包邮地区
        if ($address && $this->isInExcludedFreeShippingArea($address, $goods)) {
            return false;
        }

        // 3. 计算订单总金额
        $totalAmount = isset($goods['money']) ? (float)$goods['money'] * $quantity : 0;

        // 4. 检查包邮条件
        $meetsNumCondition = $hasEdnum && ($quantity >= (int)$goods['ednum']);
        $meetsMoneyCondition = $hasEdmoney && ($totalAmount >= (float)$goods['edmoney']);

        return $meetsNumCondition || $meetsMoneyCondition;
    }

    // 新增辅助方法：获取运费模板数据
    function getDispatchData($goods, $merchid)
    {
        if (!empty($goods['dispatchid'])) {
            return m('dispatch')->getOneDispatch($goods['dispatchid']);
        }
        return m('dispatch')->getDefaultDispatch($merchid) ?: m('dispatch')->getNewDispatch($merchid);
    }

    // 新增辅助方法：计算运费
    function calculateDispatchPrice($dispatch_data, $goods, $num, $address, $member, $new_area)
    {
        $dprice = 0;

        // 计算运费
        $areas = unserialize($dispatch_data['areas']);
        $param = ($dispatch_data['calculatetype'] == 1)
            ? $num
            : floatval($goods['weight']) * $num;

        if (!empty($address)) {
            $dprice = m('dispatch')->getCityDispatchPrice($areas, $address, $param, $dispatch_data);
        } elseif (!empty($member['city'])) {
            $dprice = m('dispatch')->getCityDispatchPrice($areas, $member, $param, $dispatch_data);
        } else {
            $dprice = m('dispatch')->getDispatchPrice($param, $dispatch_data);
        }

        return $dprice;
    }

}


