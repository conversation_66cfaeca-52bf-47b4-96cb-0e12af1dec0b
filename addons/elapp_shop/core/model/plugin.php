<?php
namespace app\model;

use app\core\model\MicroEngineModel;

/**
 * 插件表对象
 * @package app\model
 */
class PluginModel extends MicroEngineModel {
    protected $name = 'plugin';

	/**
	 * 判断是否有插件
	 * @param type $pluginName
	 */
	public function existsPlugin($pluginName = '') {

		$dbplugin = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where identity=:identyty limit  1', array(':identity' => $pluginName));
		if (empty($dbplugin)) {
			return false;
		}
		return true;
	}

	/**
	 * 获取所有插件
	 * @return type
	 */
	public function getAll($iscom = false,$status='') {
		global $_W;
		$plugins = '';
		if ($status !== '')
		{
			$status = 'and status = '.intval($status);
		}
		if ($iscom) {
			$plugins = m('cache')->getArray('coms2', "global");
			if (empty($plugins)) {
				$plugins = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where iscom=1 and deprecated=0 '.$status.' order by displayorder asc');
                $plugins = $this->arrayUnique($plugins);
                m('cache')->set('coms2', $plugins, "global");
			}
		} else {
			$plugins = m('cache')->getArray('plugins2', "global");
			if (empty($plugins)) {
				$plugins = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where iscom=0 and deprecated=0 '.$status.' order by displayorder asc');
                $plugins = $this->arrayUnique($plugins);

				m('cache')->set('plugins2', $plugins, "global");
			}
		}
		return $plugins;
	}

	public function arrayUnique($list){
        $bucket = array();
        foreach ($list as $k => $v){
            if(is_array($v) && in_array($v['name'],$bucket))
                unset($list[$k]);
            else{
                array_push($bucket,$v['name']);
            }
        }
        return  array_values($list);
    }
	public function refreshCache($status='',$iscom = false) {
		if ($status !== '')
		{
			$status = 'and status = '.intval($status);
		}
		$com = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where iscom=1 and deprecated=0 '.$status.' order by displayorder asc');
		m('cache')->set('coms2', $com, "global");

		$plugins = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where iscom=0 and deprecated=0 '.$status.' order by displayorder asc');
		m('cache')->set('plugins2', $plugins, "global");

		if ($iscom)
		{
			return $com;
		}
		else
		{
			return $plugins;
		}
	}

	public function getList($status='') {
        global $_W;

		$list = $this->getCategory();
		$plugins = $this->getAll(false,$status);

        $filename = "../addons/elapp_shop/core/model/grant.php";
        if(file_exists($filename)){
            $item = pdo_fetch('select  plugins from ' . tablename('elapp_shop_perm_plugin') . ' where acid=:acid limit 1', array(':acid' => $_W['uniacid']));
            $setting = pdo_fetch("select * from ".tablename('elapp_shop_system_grant_setting')." where id = 1 limit 1 ");

            foreach ($plugins as $key => $value){
                if(!strstr($item['plugins'],$value['identity']) && !strstr($setting['plugin'],$value['identity']) && !strstr($setting['com'],$value['identity'])){
                    $plugin = pdo_fetch("SELECT max(permendtime) as permendtime FROM " . tablename('elapp_shop_system_grant_log') . " 
                    WHERE `identity` = '".$value['identity']."' and uniacid = ".$_W['uniacid']." and isperm = 1 ");
                    $plugins[$key]['isgrant'] = 1;
                    $plugins[$key]['permendtime'] = $plugin['permendtime'];
                }
            }
        }elseif(p("grant")){
            $acid = pdo_fetch("SELECT acid,uniacid FROM " . tablename('account_wechats') . " WHERE uniacid=:uniacid limit 1", array(':uniacid' => $_W['uniacid']));
            $item = pdo_fetch('select plugins from ' . tablename('elapp_shop_perm_plugin') . ' where acid=:acid limit 1', array(':acid' => $acid['acid']));
            $setting = pdo_fetch("select * from ".tablename('elapp_shop_system_plugingrant_setting')." where 1 = 1 limit 1 ");

            foreach ($plugins as $key => $value){
                if(!strstr($item['plugins'],$value['identity']) && !strstr($setting['plugin'],$value['identity']) && !strstr($setting['com'],$value['identity'])){
                    $plugin = pdo_fetchcolumn("SELECT count(1) FROM " . tablename('elapp_shop_system_plugingrant_log') . "
                    WHERE `identity` = '".$value['identity']."' and uniacid = ".$acid['uniacid']." and isperm = 1 and `month` = 0 ");
                    if($plugin > 0){
                        $plugin = pdo_fetch("SELECT max(permendtime) as permendtime,`month`,isperm FROM " . tablename('elapp_shop_system_plugingrant_log') . "
                        WHERE `identity` = '".$value['identity']."' and uniacid = ".$acid['uniacid']." and isperm = 1 and `month` = 0 ");
                    }else{
                        $plugin = pdo_fetch("SELECT max(permendtime) as permendtime,`month`,isperm FROM " . tablename('elapp_shop_system_plugingrant_log') . " 
                        WHERE `identity` = '".$value['identity']."' and uniacid = ".$acid['uniacid']." and isperm = 1 and permendtime > ".time()." ");
                    }
                    //dump($plugin);
                    $plugins[$key]['isplugingrant'] = 1;
                    $plugins[$key]['month'] = $plugin['month'];
                    $plugins[$key]['isperm'] = $plugin['isperm'];
                    $plugins[$key]['permendtime'] = $plugin['permendtime'];
                    if($value['identity']=='taobao'){
                        //dump($plugins[$key]);
                    }
                }
            }
            //dump($plugins);
        }

		foreach ($list as $ck => &$cv) {

			$ps = array();
			foreach ($plugins as $p) {
				if ($p['category'] == $ck) {
					$ps[] = $p;
				}
			}
			$cv['plugins'] = $ps;
		}
		unset($cv);
		return $list;
	}

	public function getPluginName($identity = '') {
        return pdo_fetchcolumn('select name from ' . tablename('elapp_shop_plugin') . ' where identity=:identity limit 1', array(':identity' => $identity));
	}

	public function loadModel($pluginname = '') {

		static $_model;
		if (!$_model) {
            $plugin_model_dir = IA_ROOT . '/addons/elapp_shop/plugin/' . $pluginname . '/core';
            $plugin_model_files = scandir($plugin_model_dir);
//            $plugin_model_files = glob($plugin_model_dir . '/*.php');
            foreach ($plugin_model_files as $filename) {
                if (preg_match('/model|^[A-Z][a-zA-Z]*Model\.php$/', $filename)) { //仅匹配符合model/PluginModel的类文件
                    $classname = ucfirst($pluginname) . "Model";
                    require_once ELAPP_SHOP_CORE . "inc/plugin_model.php";
                    $modelfile = $plugin_model_dir . '/' . $filename;
//                    $modelfile = $filename;
                    require_once $modelfile;
                    if (!class_exists($classname)) {
                        $model_namespace = get_namespace($modelfile);
                        $classname = $model_namespace."\\".$classname;
                    }
                    $_model = new $classname($pluginname);
                    break;
                }
            }
		}

		return $_model;
	}

	public function getCategory() {
		return array(
			"biz" => array('name' => '业务类'),
			"sale" => array('name' => "营销类"),
			"tool" => array('name' => "工具类"),
			"help" => array('name' => "辅助类")
		);
	}

    public function getCount() {
        $plugins = m('plugin')->getAll();
        $count = 0;
        if(!empty($plugins)){
            foreach ($plugins as $plugin){
                if(com_run('perm::check_plugin',$plugin['identity'])){
                    $count++;
                }
            }
        }
        return $count;
    }

    public function getPluginConfig($pluginname = '') {
        if(empty($pluginname)){
            return false;
        }
        $config_file = $moduleroot = IA_ROOT . "/addons/elapp_shop/plugin/".$pluginname."/config.php";
        if(!is_file($config_file)){
            return false;
        }
        return include $config_file;
    }

    //获取商城组件名称
    public function getComName($identity=''){
        $plugins = $this->getAll(true);

        foreach ($plugins as $p) {
            if ($p['identity'] == $identity) {
                return $p['name'];
            }
        }
        return '';
    }

    /**
     * 检查公众号是否拥有这个插件权限
     * @param $pluginName string 插件名称
     * @param $uniacid int 公众号 uniacid ,0为当前公众号
     * @return bool
     */
    public function permission($pluginName, $uniacid = 0)
    {
        global $_W;

        $permset = intval(m('cache')->getString('permset', 'global'));
        $id = $uniacid == 0 ? $_W['uniacid'] : $uniacid;
        // 系统给的所有可用插件
        $allPlugins = pdo_fetchall('select * from ' . tablename('elapp_shop_plugin') . ' where iscom=0 and deprecated=0 and status=1 order by displayorder asc');
        $allPlugins = array_column($allPlugins, 'identity');

        if($permset) {
            $plugins = pdo_fetchcolumn("SELECT plugins FROM " . tablename('elapp_shop_perm_plugin') . " WHERE acid =:id limit 1", array(':id' => $id));
            $plugins = explode(',', $plugins);
            $plugins = array_intersect($allPlugins,$plugins);
            return in_array($pluginName, $plugins);
        }
        return in_array($pluginName, $allPlugins);
    }
}
