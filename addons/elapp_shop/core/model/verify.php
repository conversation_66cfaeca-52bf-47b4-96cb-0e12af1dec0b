<?php
namespace app\model;

class VerifyModel
{
    // 校验手机号
    public function isMobile($value) {
        $pattern = '/^1[3456789]\d{9}$/';
        return preg_match($pattern, $value);
    }

    // 校验一般帐号格式 字母开头，数字，6-16位长度
    public function verifyUserFormat($value) {
        $pattern = '/^[a-zA-Z][a-zA-Z0-9]{5,15}$/';
        return preg_match($pattern, $value);
    }

    // 校验一般帐号密码 字母开头，数字，8-20位长度
    public function verifyPassFormat($value) {
        $pattern = '/^[a-zA-Z][a-zA-Z0-9]{7,19}$/';
        return preg_match($pattern, $value);
    }

    // 校验邮箱
    public function verifyEmail($value) {
        $pattern = '/^\w+([-+.]\w+)*@\w+([-.]\w+)*\.\w+([-.]\w+)*$/';
        return preg_match($pattern, $value);
    }

}
