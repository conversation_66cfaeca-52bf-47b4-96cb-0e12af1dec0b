<?php
namespace app\model;
error_reporting(0);
class SystemModel{

    private $merch = false;
    private $org = false;
    private $copartner = false;
    private $supply = false;//供应商

    function __construct() {
        global $_W;
        //多商户
        if ($_W['plugin'] == 'merch' && $_W['merch_user']) {
            $this->merch = true;
        }
        //组织管理
        if ($_W['plugin'] == 'org' && $_W['org_user']) {
            $this->org = true;
        }
        //机构合伙人
        if ($_W['plugin'] == 'copartner' && $_W['copartner_user']) {
            $this->copartner = true;
        }
        //供应商
        if ($_W['plugin'] == 'supply' && $_W['supply_user']) {
            $this->supply = true;
        }
    }

    /**
     * 获取 全部菜单带路由
     * @param bool $full 是否返回长URL
     * @return array
     */
    public function getMenu($full = false) {
        global $_W, $_GPC;

        $return_menu = array();
        $return_submenu = array();

        $route = trim($_W['routes']);
        $route = str_replace('/', '.', request()->pathinfo());
        $routes = explode(".", $_W['routes']);
        $top = empty($routes[0]) ? 'shop' : $routes[0];

        if ($this->merch) {
            /*if (strexists($_W['routes'], 'creditshop')) {
                $routes = explode(".", 'merch.manage.' . $_W['routes']);
            }*/
            $route = str_replace('merch.manage.', '', $route);
            $top = empty($routes[2]) ? 'creditshop' : $routes[2];
            $allmenus = $this->pluginMenu('merch', 'manage_menu');
        } elseif ($this->copartner) {
            $allmenus = $this->pluginMenu('copartner', 'manage_menu');
        } elseif ($this->supply) {
            $allmenus = $this->pluginMenu('supply', 'manage_menu');
        } elseif ($this->org) {
            $top = empty($routes[2]) ? 'creditshop' : $routes[2];
            $route = str_replace('org.manage.', '', $route);
            $allmenus = $this->pluginMenu('org', 'manage_menu');
        } elseif (!$_W['isplugin'] && $routes[0] == 'system') {
            $allmenus = $this->systemMenu();
        } else {
            $allmenus = $this->shopMenu();
        }

        if ($routes[0] == 'system') {
            $top = $routes[1];
        }

        if(!empty($allmenus)){
            $submenu = $allmenus[$top];
            if(empty($submenu)){
                $othermenu = $this->otherMenu();
                if(!empty($othermenu[$top])){
                    $submenu = $othermenu[$top];
                }
            }
            if(empty($submenu) && p($top)){
                $submenu = $this->pluginMenu($top);
                $isplugin = true;
            }

            foreach ($allmenus as $key=>$val){
                if(!empty($val['plugincom'])){
                    if($val['plugincom']==1){
                        if(!p($key)){
                            continue;
                        }
                    } elseif ($val['plugincom'] == 2) {
                        if(!com($key)){
                            continue;
                        }
                    }
                }
                if($this->cv($key)){
                    $menu_item = array(
                        'route' => empty($val['route'])?$key:$val['route'],
                        'text' => $val['title']
                    );
                    if($routes[0]=='system'){
                        $menu_item['route'] = "system.".$menu_item['route'];
                    }
                    if(!empty($val['index'])){
                        $menu_item['index'] = $val['index'];
                    }
                    if(!empty($val['param'])){
                        $menu_item['param'] = $val['param'];
                    }
                    if(!empty($val['icon'])){
                        $menu_item['icon'] = $val['icon'];
                        if(!empty($val['iconcolor'])){
                            $menu_item['iconcolor'] = $val['iconcolor'];
                        }
                    }
                    if($top==$menu_item['route'] || $menu_item['route']==$route || "system.".$top==$menu_item['route']){
                        if($this->verifyParam($val) && !empty($_GPC['r'])) {
                            $menu_item['active'] = 1;
                        }
                    }elseif ($key=='plugins' && $isplugin && !$this->merch){
                        $menu_item['active'] = 1;
                    }elseif ($key=='plugins' && $isplugin && !$this->copartner){
                        $menu_item['active'] = 1;
                    }elseif ($key=='plugins' && $isplugin && !$this->supply){
                        $menu_item['active'] = 1;
                    }elseif ($key=='plugins' && $isplugin && !$this->org){
                        $menu_item['active'] = 1;
                    }
                    if($full){
                        $menu_item['url'] = webUrl($menu_item['route'], !empty($menu_item['param'])&&is_array($menu_item['param'])?$menu_item['param']:array());
                    }
                    $return_menu[] = $menu_item;
                }
            }
            unset($key, $val);

            if(!empty($submenu)){
                if(!empty($submenu['plugincom'])){
                    $return_submenu['subtitle'] = m('plugin')->getPluginName($top);
                    $return_submenu['plugin'] = $top;
                    $return_submenu['route'] = $top;
                }else{
                    $return_submenu['subtitle'] = $submenu['subtitle'];
                    if($submenu['main']){
                        $return_submenu['route'] = $top;
                        if(is_string($submenu['main'])){
                            $return_submenu['route'] .= '.'.$submenu['main'];
                        }
                    }
                    if(!empty($submenu['index'])){
                        $return_submenu['route'] = $top. '.'. $submenu['index'];
                    }
                }

                if(!empty($submenu['items'])){
                    foreach($submenu['items'] as $i=>$child){
                        if(!empty($child['isplugin'])){
                            if (!p($child['isplugin'])) {
                                continue;
                            }
                            if(!empty($child['permplugin'])){
                                if (!p($child['permplugin'])) {
                                    continue;
                                }
                            }
                        } elseif (!empty($child['iscom'])) {
                            if (!com($child['iscom'])) {
                                continue;
                            }
                            if(!empty($child['permcom'])){
                                if (!com($child['permcom'])) {
                                    continue;
                                }
                            }
                        }

                        if($this->merch && $child['hidemerch']){
                            continue;
                        }
                        if($this->copartner && $child['hidecopartner']){
                            continue;
                        }
                        if($this->supply && $child['hidesupply']){
                            continue;
                        }
                        if($this->org && $child['hideorg']){
                            continue;
                        }
                        if(!empty($child['top'])){
                            $top = '';
                        }
                        if(empty($child['items'])){
                            $return_submenu_default = $top."";

                            $route_second = $top;
                            if(!empty($child['route'])){
                                if(!empty($top)){
                                    $route_second .= '.';
                                }
                                $route_second .= $child['route'];
                            }

                            $return_menu_child = array(
                                'title' => $child['title'],
                                'route' => empty($child['route'])?$return_submenu_default:$route_second
                            );
                            if(!empty($child['param'])){
                                $return_menu_child['param'] = $child['param'];
                            }
                            if(!empty($child['perm'])){
                                $return_menu_child['perm'] = $child['perm'];
                            }
                            if(!empty($child['permmust'])){
                                $return_menu_child['permmust'] = $child['permmust'];
                            }
                            if($routes[0]=='system'){
                                $return_menu_child['route'] = "system.".$return_menu_child['route'];
                            }

                            $addedit = false;
                            if(!$child['route_must']){
                                if($return_menu_child['route'].".add"==$route || $return_menu_child['route'].".edit"==$route){
                                    $addedit = true;
                                }
                            }
                            $extend = false;
                            if(!empty($child['extend'])){
                                if($child['extend'].".add"==$route || $child['extend'].".edit"==$route || $child['extend']==$route){
                                    $extend = true;
                                }
                            } elseif (!empty($child['extends']) && is_array($child['extends'])) {
                                if(in_array($route, $child['extends']) || in_array($route.".add", $child['extends']) || in_array($route.".edit", $child['extends'])){
                                    $extend = true;
                                }
                            }

                            // $route_second==$second || $route_second==$top ||
                            if($child['route_in'] && strexists($route, $return_menu_child['route'])){
                                $return_menu_child['active'] = 1;
                            } elseif ($return_menu_child['route'] == $route || $addedit || $extend) {
                                if($this->verifyParam($child)) {
                                    $return_menu_child['active'] = 1;
                                }
                            }
                            if($full) {
                                $return_menu_child['url'] = webUrl($return_menu_child['route'], !empty($return_menu_child['param']) && is_array($return_menu_child['param']) ? $return_menu_child['param'] : array());
                            }
                            if(!empty($return_menu_child['permmust']) && !$this->cv($return_menu_child['permmust'])){
                                continue;
                            }
                            if(!$this->cv($return_menu_child['route'])){
                                if(empty($return_menu_child['perm']) || !$this->cv($return_menu_child['perm'])){
                                    continue;
                                }
                            }
                            $return_submenu['items'][] = $return_menu_child;
                            unset($return_submenu_default, $route_second);
                        }else{
                            $return_menu_child = array(
                                'title'=>$child['title'],
                                'items'=>array()
                            );

                            foreach($child['items'] as $ii=>$three){
                                if(!empty($three['isplugin'])){
                                    if (!p($three['isplugin'])) {
                                        continue;
                                    }
                                    if(!empty($three['permplugin'])){
                                        if (!p($three['permplugin'])) {
                                            continue;
                                        }
                                    }
                                } elseif (!empty($three['iscom'])) {
                                    if (!com($three['iscom'])) {
                                        continue;
                                    }
                                    if(!empty($three['permcom'])){
                                        if (!com($three['permcom'])) {
                                            continue;
                                        }
                                    }
                                }
                                if($this->merch && $three['hidemerch']){
                                    continue;
                                }
                                if($this->copartner && $three['hidecopartner']){
                                    continue;
                                }
                                if($this->supply && $three['hidesupply']){
                                    continue;
                                }
                                if($this->org && $three['hideorg']){
                                    continue;
                                }
                                $return_submenu_default = $top."";
                                $route_second = "main";
                                if(!empty($child['route'])){
                                    $return_submenu_default = $top.".".$child['route'];
                                    $route_second = $child['route'];
                                }
                                $return_submenu_three = array(
                                    'title' => $three['title'],
                                );
                                if(!empty($three['route'])){
                                    if(!empty($child['route'])){
                                        if(!empty($three['route_ns'])){
                                            $return_submenu_three['route'] = $top.".".$three['route'];
                                        }else{
                                            $return_submenu_three['route'] = $top.".".$child['route'].".".$three['route'];
                                        }
                                    }else{
                                        //$return_submenu_three['route'] = $top.".".$three['route'];
                                        //$route_second = $three['route'];

                                        if(!empty($three['top'])){
                                            $return_submenu_three['route'] = $three['route'];
                                        }else{
                                            $return_submenu_three['route'] = $top.".".$three['route'];
                                        }
                                        $route_second = $three['route'];
                                    }
                                }else{
                                    $return_submenu_three['route'] = $return_submenu_default;
                                }
                                if(!empty($three['param'])){
                                    $return_submenu_three['param'] = $three['param'];
                                }
                                if(!empty($three['perm'])){
                                    $return_submenu_three['perm'] = $three['perm'];
                                }
                                if(!empty($three['permmust'])){
                                    $return_submenu_three['permmust'] = $three['permmust'];
                                }
                                if($routes[0]=='system'){
                                    $return_submenu_three['route'] = "system.".$return_submenu_three['route'];
                                }

                                $addedit = false;
                                if(!$three['route_must']){
                                    if($return_submenu_three['route'].".add"==$route || $return_submenu_three['route'].".edit"==$route){
                                        $addedit = true;
                                    }
                                }
                                $extend = false;
                                if(!empty($three['extend'])){
                                    if($three['extend'].".add"==$route || $three['extend'].".edit"==$route || $three['extend']==$route){
                                        $extend = true;
                                    }
                                } elseif (!empty($three['extends']) && is_array($three['extends'])) {
                                    if(in_array($route, $three['extends']) || in_array($route.".add", $three['extends']) || in_array($route.".edit", $three['extends'])){
                                        $extend = true;
                                    }
                                }

                                if($three['route_in'] && strexists($route, $return_submenu_three['route'])){
                                    $return_menu_child['active'] = 1;
                                    $return_submenu_three['active'] = 1;
                                } // $route_second==$routes[1] &&
                                elseif($return_submenu_three['route']==$route || $addedit || $extend){
                                    if($this->verifyParam($three)){
                                        $return_menu_child['active'] = 1;
                                        $return_submenu_three['active'] = 1;
                                    }
                                }

                                if(!empty($child['extend'])){
                                    if($child['extend']==$route){
                                        $return_menu_child['active'] = 1;
                                    }
                                } elseif (is_array($child['extends'])) {
                                    if(in_array($route, $child['extends'])){
                                        $return_menu_child['active'] = 1;
                                    }
                                }

                                if($full) {
                                    $return_submenu_three['url'] = webUrl($return_submenu_three['route'], !empty($return_submenu_three['param']) && is_array($return_submenu_three['param']) ? $return_submenu_three['param'] : array());
                                }
                                if(!empty($return_submenu_three['permmust']) && !$this->cv($return_submenu_three['permmust'])){
                                    continue;
                                }
                                if(!$this->cv($return_submenu_three['route'])){
                                    if(empty($return_submenu_three['perm']) || !$this->cv($return_submenu_three['perm'])){
                                        continue;
                                    }
                                }
                                $return_menu_child['items'][] = $return_submenu_three;
                            }
                            if(!empty($child['items']) && empty($return_menu_child['items'])){
                                continue;
                            }
                            $return_submenu['items'][] = $return_menu_child;
                            unset($ii, $three, $route_second);
                        }
                    }
                }
            }
        }
        return array(
            'menu' => $return_menu,
            'submenu' => $return_submenu,
            'shopmenu' => $this->getShopMenu()
        );
    }

    /**
     * 获取 全部菜单带路由
     * @param bool $full 是否返回长URL
     * @return array
     */
    public function getSubMenus($full = false, $plugin = false)
    {
        global $_W;
        $return_submenu = array();

        if(!$this->merch){
            $systemMenu = $this->systemMenu();
            $allmenus = array_merge($this->shopMenu(), $systemMenu);
            if($plugin){
                $allmenus = array_merge($allmenus, $this->allPluginMenu());
            }
        }else{
            $allmenus = $this->pluginMenu('merch', 'manage_menu');
        }
        if($this->merch){
            $iscredit = $_W['merch_user']['iscredit'];
        }

        //组织管理
        if(!$this->org){
            $systemMenu = $this->systemMenu();
            $allmenus = array_merge($this->shopMenu(), $systemMenu);
            if($plugin){
                $allmenus = array_merge($allmenus, $this->allPluginMenu());
            }
        }else{
            $allmenus = $this->pluginMenu('org', 'manage_menu');
        }
        if($this->org){
            $iscredit = $_W['org_user']['iscredit'];
        }

        //机构合伙人
        if(!$this->copartner){
            $systemMenu = $this->systemMenu();
            $allmenus = array_merge($this->shopMenu(), $systemMenu);
            if($plugin){
                $allmenus = array_merge($allmenus, $this->allPluginMenu());
            }
        }else{
            $allmenus = $this->pluginMenu('copartner', 'manage_menu');
        }
        if($this->copartner){
            $iscredit = $_W['copartner_user']['iscredit'];
        }
        //供应商
        if($this->supply){
            $iscredit = $_W['supply_user']['iscredit'];
        }

        if(!empty($allmenus)) {
            foreach ($allmenus as $key => $item) {
                if (!$this->merch && is_array($systemMenu) && array_key_exists($key, $systemMenu)) {
                    $key = 'system.' . $key;
                }
                //机构合伙人
                if (!$this->copartner && is_array($systemMenu) && array_key_exists($key, $systemMenu)) {
                    $key = 'system.' . $key;
                }
                //组织管理
                if (!$this->org && is_array($systemMenu) && array_key_exists($key, $systemMenu)) {
                    $key = 'system.' . $key;
                }
                if(empty($item['items'])){
                    $return_submenu_item = array(
                        'title' => $item['title'],
                        'top' => $key,
                        'toptitle' => $item['title'],
                        'topsubtitle' => $item['subtitle'],
                        'route' => empty($item['route'])?$key:$item['route'],
                    );
                    if(!empty($item['param'])){
                        $return_submenu_item = $item['param'];
                    }
                    if($full) {
                        $return_submenu_item['url'] = webUrl($return_submenu_item['route'], !empty($return_submenu_item['param']) && is_array($return_submenu_item['param']) ? $return_submenu_item['param'] : array());
                    }
                    $return_submenu[] = $return_submenu_item;
                }else{
                    foreach ($item['items'] as $i=>$child){
                        if(empty($child['items'])){
                            $return_submenu_default = $key;
                            $return_submenu_route = $key.".". !empty($child['route']) ?: '';
                            $return_submenu_child = array(
                                'title' => $child['title'],
                                'top' => $key,
                                'toptitle' => $item['title'],
                                'topsubtitle' => !empty($item['subtitle']) ?: '',
                                'route' => empty($child['route'])?$return_submenu_default:$return_submenu_route
                            );
                            if(!empty($child['desc'])){
                                $return_submenu_child['desc'] = $child['desc'];
                            }
                            if(!empty($child['keywords'])){
                                $return_submenu_child['keywords'] = $child['keywords'];
                            }
                            if(!empty($child['param'])){
                                $return_submenu_child['param'] = $child['param'];
                            }
                            if($full) {
                                $return_submenu_child['url'] = webUrl($return_submenu_child['route'], !empty($return_submenu_child['param']) && is_array($return_submenu_child['param']) ? $return_submenu_child['param'] : array());
                            }
                            $return_submenu[] = $return_submenu_child;
                        }else{
                            foreach ($child['items'] as $ii=>$three){
                                $return_submenu_default = $key;
                                if(!empty($child['route'])){
                                    $return_submenu_default = $key.".".$child['route'];
                                }
                                $return_submenu_three = array(
                                    'title' => $three['title'],
                                    'top' => $key,
                                    'topsubtitle' => !empty($item['subtitle']) ?: ''
                                );
                                if(!empty($three['desc'])){
                                    $return_submenu_three['desc'] = $three['desc'];
                                }
                                if(!empty($three['keywords'])){
                                    $return_submenu_three['keywords'] = $three['keywords'];
                                }
                                if(!empty($three['route'])){
                                    if(!empty($child['route'])){
                                        $return_submenu_three['route'] = $key.".".$child['route'].".".$three['route'];
                                    }else{
                                        $return_submenu_three['route'] = $key.".".$three['route'];
                                    }
                                }else{
                                    $return_submenu_three['route'] = $return_submenu_default;
                                }
                                if(!empty($three['param'])){
                                    $return_submenu_three['param'] = $three['param'];
                                }
                                if($full) {
                                    $return_submenu_three['url'] = webUrl($return_submenu_three['route'], !empty($return_submenu_three['param']) && is_array($return_submenu_three['param']) ? $return_submenu_three['param'] : array());
                                }
                                $return_submenu[] = $return_submenu_three;
                            }
                            unset($return_submenu_default, $return_submenu_three);
                        }
                    }
                    unset($return_submenu_default, $return_submenu_route, $return_submenu_child);
                }
            }
        }

        return $return_submenu;
    }

    /**
     * 获取 主商城菜单
     * @return array
     */
    public function getShopMenu()
    {
        $return_menu = array();
        if(!$this->merch){
            $menus = $this->shopMenu();
        }else{
            $menus = $this->pluginMenu('merch', 'manage_menu');
        }
        //机构合伙人
        if(!$this->copartner){
            $menus = $this->shopMenu();
        }else{
            $menus = $this->pluginMenu('copartner', 'manage_menu');
        }
        //组织管理
        if(!$this->org){
            $menus = $this->shopMenu();
        }else{
            $menus = $this->pluginMenu('org', 'manage_menu');
        }
        foreach ($menus as $key=>$val){
            $menu_item = array('title'=>$val['subtitle'], 'items'=>array());
            if($key=='diypage'){
                $menu_item['title'] = m('plugin')->getPluginName('diypage');
            } elseif ($key == 'app') {
                $menu_item['title'] = m('plugin')->getPluginName('app');
            }
            if(empty($val['items'])){
                continue;
            }
            foreach ($val['items'] as $child){

                if(!empty($child['isplugin'])){
                    if (!p($child['isplugin'])) {
                        continue;
                    }
                    if(!empty($child['permplugin'])){
                        if (!com($child['permplugin'])) {
                            continue;
                        }
                    }
                } elseif (!empty($child['iscom'])) {
                    if (!com($child['iscom'])) {
                        continue;
                    }
                    if(!empty($child['permcom'])){
                        if (!com($child['permcom'])) {
                            continue;
                        }
                    }
                }


                $child_route_default = $key;
                if(!empty($child['route'])){
                    $child_route_default = $key.".".$child['route'];
                    if(!empty($child['top'])){
                        $child_route_default = $child['route'];
                    }
                }
                if(empty($child['items'])){
                    $menu_item_child = array(
                        'title' => $child['title'],
                        'route' => $child_route_default
                    );
                    if(!empty($child['param'])){
                        //$menu_item_child['param'] = $child['param'];
                    }
                    $menu_item_child['url'] = webUrl($menu_item_child['route'], !empty($menu_item_child['param']) && is_array($menu_item_child['param']) ? $menu_item_child['param'] : array());
                    $menu_item['items'][] = $menu_item_child;
                }else{
                    foreach ($child['items'] as $three){

                        if(!empty($three['isplugin'])){
                            if (!p($three['isplugin'])) {
                                continue;
                            }
                            if(!empty($three['permplugin'])){
                                if (!com($three['permplugin'])) {
                                    continue;
                                }
                            }
                        } elseif (!empty($three['iscom'])) {
                            if (!com($three['iscom'])) {
                                continue;
                            }
                            if(!empty($three['permcom'])){
                                if (!com($three['permcom'])) {
                                    continue;
                                }
                            }
                        }


                        $menu_item_three = array(
                            'title' => $three['title'],
                            'route' => empty($three['route'])?$child_route_default:$child_route_default.'.'.$three['route']
                        );
                        if(!empty($three['param'])){
                            //$menu_item_three['param'] = $three['param'];
                        }
                        $menu_item_three['url'] = webUrl($menu_item_three['route'], !empty($menu_item_three['param']) && is_array($menu_item_three['param']) ? $menu_item_three['param'] : array());
                        $menu_item['items'][] = $menu_item_three;
                    }
                }
            }
            $return_menu[] = $menu_item;
        }
        return $return_menu;
    }

    /**
     * 定义 商城 菜单
     * @return array
     */
    protected function shopMenu()
    {
        $shopmenu = array(
            'shop' => array(
                'title'=>'店铺',
                'subtitle'=>'店铺首页',
                'icon'=>'store',
                'items'=>array(
                    array(
                        'title'=>'首页',
                        'route'=>'',
                        'items'=>array(
                            array(
                                'title'=>'幻灯片 ',
                                'route'=>'adv.main',
                                'desc'=>'店铺首页幻灯片管理'
                            ),
                            array(
                                'title'=>'导航图标',
                                'route'=>'nav',
                                'desc'=>'店铺首页导航图标管理'
                            ),
                            array(
                                'title'=>'广告',
                                'route'=>'banner',
                                'desc'=>'店铺首页广告管理'
                            ),
                            array(
                                'title'=>'魔方推荐',
                                'route'=>'cube',
                                'desc'=>'店铺首页魔方推荐管理'
                            ),
                            array(
                                'title'=>'商品推荐',
                                'route'=>'recommand',
                                'desc'=>'店铺首页商品推荐管理'
                            ),
                            array(
                                'title'=>'排版设置',
                                'route'=>'sort',
                                'desc'=>'店铺首页排版设置'
                            )
                        )
                    ),
                    array(
                        'title'=>'商城',
                        'items'=>array(

                            array(
                                'title'=>'公告管理',
                                'route'=>'notice',
                                'desc'=>'店铺公告管理'
                            ),
                            array(
                                'title'=>'评价管理',
                                'route'=>'comment',
                                'desc'=>'店铺商品评价管理'
                            ),
                            array(
                                'title'=>'退货地址',
                                'route'=>'refundaddress',
                                'desc'=>'店铺退货地址管理'
                            )
                        )
                    ),
                    array(
                        'title'=>'配送方式',
                        'items'=>array(
                            array(
                                'title'=>'普通快递',
                                'route'=>'dispatch',
                                'desc'=>'店铺普通快递管理'

                            ),
                            array(
                                'title'=>'同城配送',
                                'route'=>'cityexpress',
                                'desc'=>'店铺同城配送管理'
                            )
                        )
                    ),
                    array(
                        'title'=>m('plugin')->getPluginName('diypage'),
                        'isplugin'=>'diypage',
                        'route'=>'diypage',
                        'top'=>true
                    )
                )
            ),
            'goods' => array(
                'title'=>'商品',
                'subtitle'=>'商品管理',
                'icon'=>'goods',
                'items'=>array(
                    array(
                        'title'=>'出售中',
                        'desc'=>'出售中商品管理',
                        'extend'=>'goods.sale', // 路由继承
                        'perm'=>'goods.main'    // 继承权限
                    ),
                    array(
                        'title'=>'已售罄',
                        'route'=>'index.out',
                        'desc'=>'已售罄/无库存商品管理',
                        'perm'=>'goods.main'
                    ),
                    array(
                        'title'=>'仓库中',
                        'route'=>'index.stock',
                        'desc'=>'仓库中商品管理',
                        'perm'=>'goods.main'
                    ),
                    array(
                        'title'=>'回收站',
                        'route'=>'index.cycle',
                        'desc'=>'回收站/已删除商品管理',
                        'perm'=>'goods.main'
                    ),
                    array(
                        'title'=>'云仓库',
                        'route'=>'index.cloud',
                        'desc'=>'云仓库/云仓商品管理',
                        'perm'=>'goods.main'
                    ),
                    array(
                        'title'=>'待审核',
                        'route'=>'index.verify',
                        'desc'=>'多商户待审核商品管理',
                        'perm'=>'goods.main'
                    ),
                    array(
                        'title'=>'商品分类',
                        'route'=>'category'
                    ),
                    array(
                        'title'=>'商品分组',
                        'route'=>'group'
                    ),
                    array(
                        'title'=>'平台药方分组',
                        'route'=>'',
                        'extend'=>'goods',
                        'items'=> [
                            [
                                'title'=>'药方分组',
                                'route'=>'prescription_group',
                                'desc'=>'药方分组',
                                'extend'=>''
                            ],
                            [
                                'title'=>'药方列表',
                                'route'=>'prescription',
                                'desc'=>'药方列表',
                                'extend'=>''
                            ]
                        ]
                    ),
                    array(
                        'title'=>'ERP商品',
                        'route'=>'erpgoods',
                        'iscom'=>'erpgoods',
                        'items'=>array(
                            array(
                                'title'=>'商品资料',
                                'route'=>'index.main',
                                'desc'=>'ERP商品资料管理',
                                'extend'=>'goods.erpgoods.main'
                            ),
                            array(
                                'title'=>'仓库分类',
                                'route'=>'category.main'
                            ),
                            array(
                                'title'=>'仁正仓库',
                                'route'=>'renzheng.main',
                                'extend'=>'goods.erpgoods.data'
                            )
                        )
                    ),
                    array(
                        'title'=>'标签管理',
                        'route'=>'label',
                        'extend'=>'goods.label.style'
                    ),
                    array(
                        'title'=>'固定信息',
                        'route'=>'fixedinfo',
                        //'extend'=>'goods.label.style'
                    ),
                    array(
                        'title'=>'品牌管理',
                        'route'=>'brand',
                        //'extend'=>'goods.brand.style'
                    ),
                    array(
                        'title'=>'产品剂型',
                        'route'=>'formtype',
                    ),
                    array(
                        'title'=>'使用方法',
                        'route'=>'takeDirection',
                    ),
                    array(
                        'title'=>'虚拟卡密',
                        'route'=>'virtual',
                        'iscom'=>'virtual',
                        'items'=>array(
                            array(
                                'title'=>'虚拟卡密',
                                'route'=>'temp.main',
                                'extend'=>'goods.virtual.data'
                            ),
                            array(
                                'title'=>'卡密分类',
                                'route'=>'category.main'
                            ),
                            array(
                                'title'=>'设置',
                                'route'=>'set.main'
                            )
                        )
                    )
                )
            ),
            'member' => array(
                'title'=>'会员',
                'subtitle'=>'会员管理',
                'icon'=>'member',
                'items'=>array(
                    array(
                        'title'=>'会员列表',
                        'route'=>'list',
                        'route_in'=>true
                    ),
                    array(
                        'title'=>'会员等级',
                        'route'=>'level'
                    ),
                    array(
                        'title'=>'标签组',
                        'route'=>'group'
                    ),
                    array(
                        'title'=>'排行榜设置',
                        'route'=>'rank',
                        'desc'=>'会员积分/消费排行榜设置'
                    ),
                    array(
                        'title'=>'微信会员卡',
                        'route'=>'card',
                        'iscom'=>'wxcard',
                        'extends'=>array(   // 继承路由
                            'member.card.post',
                            'member.card.activationset'
                        )
                    )
                )
            ),
            'order' => array(
                'title' => '订单',
                'subtitle' => '订单管理',
                'icon' => 'order',
                'items'=> array(
                    array(
                        'title'=>'待发货',
                        'route'=>'list.status1',
                        'desc'=>'待发货订单管理'
                    ),
                    array(
                        'title'=>'待收货',
                        'route'=>'list.status2',
                        'desc'=>'待收货订单管理'
                    ),
                    array(
                        'title'=>'待付款',
                        'route'=>'list.status0',
                        'desc'=>'待付款订单管理'
                    ),
                    array(
                        'title'=>'已完成',
                        'route'=>'list.status3',
                        'desc'=>'已完成订单管理'
                    ),
                    array(
                        'title'=>'已关闭',
                        'route'=>'list.status_1',
                        'desc'=>'已关闭订单管理'
                    ),
                    array(
                        'title'=>'全部订单',
                        'route'=>'list',
                        'desc'=>'全部订单列表',
                        'permmust'=>'order.list.main'
                    ),
                    array(
                        'title'=>'维权',
                        'route'=>'list',
                        'items'=>array(
                            array(
                                'title'=>'维权申请',
                                'route'=>'status4',
                                'desc'=>'维权申请管理'
                            ),
                            array(
                                'title'=>'维权完成',
                                'route'=>'status5',
                                'desc'=>'维权完成管理'
                            )
                        )
                    ),
                    array(
                        'title'=>'工具',
                        'items'=>array(
                            array(
                                'title'=>'自定义导出',
                                'route'=>'export',
                                'desc'=>'订单自定义导出'
                            ),
                            array(
                                'title'=>'批量发货',
                                'route'=>'batchsend',
                                'desc'=>'订单批量发货'
                            )
                        )
                    )
                )
            ),
            'store' => array(
                'title'=>'门店',
                'subtitle'=>'门店',
                'icon'=>'mendianguanli',
                'items'=>array(
                    array(
                        'title'=>'门店管理',
                        'items'=>array(
                            array(
                                'title'=>'门店管理',
                                'route'=>'',
                                'extends'=>array(
                                    'store.diypage.settings',
                                    'store.diypage.page',
                                    'store.goods',
                                    'store.goods.goodsoption'
                                )
                            ),
                            array(
                                'title'=>'店员管理',
                                'route'=>'saler'
                            ),/*
                            array(
                                'title'=>'门店分类',
                                'route'=>'category',
                                'extends'=>array(
                                    'store.category.detail'
                                )
                            ),
                            array(
                                'title'=>'店员角色管理',
                                'route'=>'perm.role'
                            ),
                            array(
                                'title'=>'服务人员',
                                'route'=>'staff'
                            ),*/
                            array(
                                'title'=>'关键词设置',
                                'route'=>'set'
                            )
                        )
                    ),
                    array(
                        'title'=>'门店商品管理',
                        'items'=>array(
                            /*
                            array(
                                'title'=>'商品组管理',
                                'route'=>'goodsgroup',
                                'extends'=>array(
                                    'store.goodsgroup.detail',
                                    'store.goodsgroup.goods'
                                )
                            ),*/
                            array(
                                'title'=>'记次时商品管理',
                                'route'=>'verifygoods',
                                'extends'=>array(
                                    'store.verifygoods.detail',
                                    'store.verifygoods.verifygoodslog'
                                )
                            )
                        )
                    ),
                    array(
                        'title'=>'记次时商品统计',
                        'route'=>'verify.log'
                    ),
                    array(
                        'title'=>'核销订单记录',
                        'route'=>'verifyorder.log'
                    ),
//                    array(
//                        'title'=>'行业模版',
//                        'isplugin'=>'newstore',
//                        'items'=>array(
//                            array(
//                                'title'=>'模版管理',
//                                'isplugin'=>'newstore',
//                                'route'=>'newstore.temp',
//                                'top'=>true
//                            )
//                        )
//                    ),
                    /*array(
                        'title'=>'预约商品管理',
                        'isplugin'=>'newstore',
                        'items'=>array(
                            array(
                                'title'=>'行业模版管理',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.temp',
                                'top'=>true
                            ),
                            array(
                                'title'=>'出售中',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.ngoods',
                                'top'=>true,
                                'param'=>array(
                                    'goodsfrom'=>'sale'
                                )
                            ),
                            array(
                                'title'=>'仓库中',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.ngoods',
                                'top'=>true,
                                'param'=>array(
                                    'goodsfrom'=>'stock'
                                )
                            ),
                            array(
                                'title'=>'回收站',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.ngoods',
                                'top'=>true,
                                'param'=>array(
                                    'goodsfrom'=>'cycle'
                                )
                            )
                        )
                    ),
                    array(
                        'title'=>'预约商品统计',
                        'isplugin'=>'newstore',
                        'items'=>array(
                            array(
                                'title'=>'待核销',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list.status12',
                                'top'=>true
                            ),
                            array(
                                'title'=>'待付款',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list.status0',
                                'top'=>true
                            ),
                            array(
                                'title'=>'付定金',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list.status1',
                                'top'=>true
                            ),
                            array(
                                'title'=>'付全款',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list.status11',
                                'top'=>true
                            ),
                            array(
                                'title'=>'已完成',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list.status3',
                                'top'=>true
                            ),
                            array(
                                'title'=>'全部订单',
                                'isplugin'=>'newstore',
                                'route'=>'newstore.norder.list',
                                'top'=>true,
                            )
                        )
                    )*/
                    /*
                    array(
                        'title'=>'模版列表',
                        'route'=>'diypage'
                    )*/

                )
            ),
            'sale' => array(
                'title'=>'营销',
                'subtitle'=>'营销设置',
                'icon'=>'yingxiao',
                'items'=>array(
                    array(
                        'title'=>'基本功能',
                        'items'=>array(
                            array(
                                'title'=>'满额立减',
                                'route'=>'index.enough',
                                'desc'=>'满额立减设置',
                                'keywords'=>'营销',
                                'perm' => 'sale.enough'
                            ),
                            array(
                                'title'=>'满额包邮',
                                'route'=>'index.enoughfree',
                                'desc'=>'满额包邮设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.enoughfree'
                            ),
                            /*满件优惠功能-新增 Hlei20210427*/
                            array(
                                'title'=>'满件优惠',
                                'route'=>'index.fullenough',
                                'desc'=>'满件优惠设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.fullenough'
                            ),
                            array(
                                'title'=>'抵扣设置',
                                'route'=>'index.deduct',
                                'desc'=>'抵扣设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.deduct'
                            ),
                            array(
                                'title'=>'充值优惠',
                                'route'=>'index.recharge',
                                'desc'=>'充值优惠设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.recharge'
                            ),
                            array(
                                'title'=>'积分优惠',
                                'route'=>'index.credit1',
                                'desc'=>'积分优惠设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.credit1'
                            ),
                            array(
                                'title'=>'套餐管理',
                                'route'=>'package.index.main',
                                'keywords'=>'营销',
                                'perm'=> 'sale.package'
                            ),
                            array(
                                'title'=>'赠品管理',
                                'route'=>'gift.index.main',
                                'keywords'=>'营销',
                                'perm'=> 'sale.gift'
                            ),
                            array(
                                'title'=>'全返管理',
                                'route'=>'fullback.index.main',
                                'keywords'=>'营销',
                                'perm'=> 'sale.fullback'
                            ),
                            array(
                                'title'=>'找人代付',
                                'route'=>'peerpay',
                                'keywords'=>'营销',
                                'perm'=> 'sale.peerpay'
                            ),
                            array(
                                'title'=>'绑定送积分',
                                'route'=>'index.bindmobile',
                                'perm'=> 'sale.bindmobile'
                            ),
                            array(
                                'title'=>'主推级别',
                                'route'=>'index.reclevel',
                                'desc'=>'主推级别设置',
                                'keywords'=>'营销',
                                'perm'=> 'sale.reclevel'
                            )
                        )
                    ),
                    array(
                        'title'=>'优惠券',
                        'route'=>'coupon',
                        'iscom'=>'coupon',
                        'items'=>array(
                            array(
                                'title'=>'全部优惠券',
                                'route'=>'index.main',
                                'desc'=>'优惠券管理',
                                'perm'=> 'sale.coupon.view'
                            ),
                            array(
                                'title'=>'手动发送',
                                'route'=>'sendcoupon.main',
                                'desc'=>'手动发送优惠券',
                                'perm'=>'sendcoupon.main'
                            ),
                            array(
                                'title'=>'购物送券',
                                'route'=>'shareticket.main',
                                'extends'=>array(
                                    'sale.coupon.goodssend',
                                    'sale.coupon.usesendtask',
                                    'sale.coupon.goodssend.add',
                                    'sale.coupon.usesendtask.add'
                                )
                            ),
                            array(
                                'title'=>'发放记录',
                                'route'=>'log.main',
                                'desc'=>'优惠券发放记录'
                            ),
                            array(
                                'title'=>'申请记录',
                                'route'=>'log.apply',
                                'extends'=>array(
                                    'sale.log.applydo',
                                ),
                                'desc'=>'优惠券申请记录'
                            ),
                            array(
                                'title'=>'分类管理',
                                'route'=>'category.main',
                                'desc'=>'优惠券分类管理'
                            ),
                            array(
                                'title'=>'其他设置',
                                'route'=>'index.set',
                                'desc'=>'优惠券设置',
                                'perm'=> 'sale.coupon.edit'
                            )
                        )
                    ),
                    array(
                        'title'=>'微信卡券',
                        'iscom'=>'wxcard',
                        'items'=>array(
                            array(
                                'title'=>'卡券管理',
                                'route'=>'wxcard.index.main',
                                'perm' => 'sale.wxcard.view'
                            )
                        )
                    ),
                    array(
                        'title'=>'其他工具',
                        'items'=>array(
                            array(
                                'title'=>'关注回复',
                                'route'=>'virtual.index.main',
                                'desc'=>'关注回复设置',
                                'perm'=> 'sale.virtual'
                            )
                        )
                    )
                )
            ),
            'finance' => array(
                'title'=>'财务',
                'subtitle'=>'财务管理',
                'icon'=>'31',
                'items'=>array(
                    array(
                        'title'=>'财务',
                        'route'=>'log',
                        'items'=>array(
                            array(
                                'title'=>'充值记录',
                                'route'=>'recharge'
                            ),
                            array(
                                'title'=>'提现申请',
                                'route'=>'withdraw'
                            )
                        )
                    ),
                    array(
                        'title'=>'明细',
                        'route'=>'credit',
                        'items'=>array(
                            array(
                                'title'=>'积分明细',
                                'route'=>'credit1'
                            ),
                            array(
                                'title'=>'余额明细',
                                'route'=>'credit2'
                            )
                        )
                    ),
                    array(
                        'title'=>'对账单',
                        'items'=>array(
                            array(
                                'title'=>'下载对账单',
                                'route'=>'downloadbill'
                            )
                        )
                    )
                )
            ),
            'statistics' => array(
                'title'=>'数据',
                'subtitle'=>'数据统计',
                'icon'=>'statistics',
                'items'=>array(
                    array(
                        'title'=>'销售统计',
                        'items'=>array(
                            array(
                                'title'=>'销售统计',
                                'route'=>'sale'
                            ),
                            array(
                                'title'=>'销售指标',
                                'route'=>'sale_analysis'
                            ),
                            array(
                                'title'=>'订单统计',
                                'route'=>'order'
                            )
                        )
                    ),
                    array(
                        'title'=>'商品统计',
                        'items'=>array(
                            array(
                                'title'=>'销售明细',
                                'route'=>'goods'
                            ),
                            array(
                                'title'=>'销售排行',
                                'route'=>'goods_rank',
                                'extend'=>'statistics.goods_rank_detail'
                            ),
                            array(
                                'title'=>'销售转化率',
                                'route'=>'goods_trans'
                            )
                        )
                    ),
                    array(
                        'title'=>'会员统计',
                        'items'=>array(
                            array(
                                'title'=>'消费排行',
                                'route'=>'member_cost'
                            ),
                            array(
                                'title'=>'增长趋势',
                                'route'=>'member_increase'
                            )
                        )
                    )
                )
            ),
            'app' => $this->pluginMenu('app'),
            'plugins' => array(
                'title'=>'应用',
                'subtitle'=>'应用管理',
                'icon'=>'plugins'
            ),

            'sysset' => array(
                'title'=>'设置',
                'subtitle'=>'商城设置',
                'icon'=>'sysset',
                'items'=>array(
                    array(
                        'title'=>'商城',
                        'items'=>array(
                            array(
                                'title'=>'基础设置',
                                'route'=>'index.shop'
                            ),
                            array(
                                'title'=>'关注及分享',
                                'route'=>'index.follow'
                            ),
                            array(
                                'title'=>'商城状态',
                                'route'=>'index.close'
                            ),
                            array(
                                'title'=>'模板设置',
                                'route'=>'index.templat'
                            ),
                            array(
                                'title'=>'全网通设置',
                                'route'=>'index.wap',
                                'iscom'=>'wap',
                                'permcom'=>'sms'
                            )
                        )
                    ),
                    array(
                        'title'=>'交易',
                        'items'=>array(
                            array(
                                'title'=>'交易设置',
                                'route'=>'index.trade'
                            ),
                            array(
                                'title'=>'支付设置',
                                'route'=>'index.payset'
                            ),
                            array(
                                'title'=>'支付接口',
                                'route'=>'payment'
                            )
                        )
                    ),
                    array(
                        'title'=>'消息推送',
                        'items'=>array(
                            array(
                                'title'=>'消息提醒',
                                'route'=>'index.notice'
                            ),
                            array(
                                'title'=>'自定义消息库',
                                'route'=>'tmessage'
                            ),
                            array(
                                'title'=>'微信模板库',
                                'route'=>'weixintemplate',
                                'extend'=>'sysset.weixintemplate.post'
                            ),
                            array(
                                'title'=>'消息通知队列',
                                'route'=>'index.notice_redis',
                            )
                        )
                    ),
                    array(
                        'title'=>'短信配置',
                        'route'=>'sms',
                        'iscom'=>'sms',
                        'items'=>array(
                            array(
                                'title'=>'短信消息库',
                                'route'=>'index.main'
                            ),
                            array(
                                'title'=>'短信接口设置',
                                'route'=>'index.set'
                            )
                        )
                    ),
                    array(
                        'title'=>'小票打印机',
                        'route'=>'printer',
                        'iscom'=>'printer',
                        'items'=>array(
                            array(
                                'title'=>'打印机管理',
                                'route'=>'printer_list',
                                'extends'=>array(
                                    'sysset.printer.printer_add'
                                )
                            ),
                            array(
                                'title'=>'打印机模板库'
                            ),
                            array(
                                'title'=>'打印设置',
                                'route'=>'set'
                            )
                        )
                    ),
                    array(
                        'title'=>'其他',
                        'items'=>array(
                            array(
                                'title'=>'会员设置',
                                'route'=>'index.member'
                            ),
                            array(
                                'title'=>'分类层级',
                                'route'=>'index.category'
                            ),
                            array(
                                'title'=>'联系方式',
                                'route'=>'index.contact'
                            ),
                            array(
                                'title'=>'地址库设置',
                                'route'=>'index.area'
                            ),
                            array(
                                'title'=>'物流信息接口',
                                'route'=>'index.express'
                            )
                        )
                    ),
                    array(
                        'title'=>'工具',
                        'items'=>array(
                            array(
                                'title'=>'七牛存储',
                                'route'=>'qiniu',
                                'iscom'=>'qiniu',
                            ),
                            array(
                                'title'=>'商品价格修复',
                                'route'=>'index.goodsprice'
                            ),
                            array(
                                'title'=>'模板消息修复',
                                'route'=>'templatetool'
                            ),
                            array(
                                'title' => '清空海报缓存',
                                'route' => 'postertool'
                            ),
                            array(
                                'title'=>'支付证书验证',
                                'route'=>'index.wxpaycert'
                            ),
                            array(
                                'title'=>'七牛修复',
                                'route'=>'fixQiniu'
                            )
                        )
                    ),
                    array(
                        'title'=>'入口',
                        'route'=>'cover',
                        'items'=>array(
                            array(
                                'title'=>'商城入口',
                                'route'=>'shop'
                            ),
                            array(
                                'title'=>'会员中心入口',
                                'route'=>'member'
                            ),
                            array(
                                'title'=>'订单入口',
                                'route'=>'order'
                            ),
                            array(
                                'title'=>'收藏入口',
                                'route'=>'favorite'
                            ),
                            array(
                                'title'=>'购物车入口',
                                'route'=>'cart'
                            ),
                            array(
                                'title'=>'优惠券入口',
                                'route'=>'coupon'
                            )
                        )
                    )
                )
            )
        );
        if (!p('app')) {
            unset($shopmenu['app']);
        }
        if (!function_exists("redis") || is_error(redis())) {
            if (isset($shopmenu['sale']['items'][0]['items'])) {
                foreach ($shopmenu['sale']['items'][0]['items'] as $key => &$item) {
                    if ($item['route'] == 'deduct') {
                        //不开启redis,抵扣设置菜单设置没显示
                        unset($shopmenu['sale']['items'][0]['items'][$key]);
                    }
                }
                unset($item);
            }

            if (isset($shopmenu['sysset']['items'][2]['items'])) {
                foreach ($shopmenu['sysset']['items'][2]['items'] as $key => &$item) {
                    //不开启redis,消息队列不显示
                    if ($item['route'] == 'notice_redis') {
                        unset($shopmenu['sysset']['items'][2]['items'][$key]);
                    }
                }
                unset($item);
            }


        }
        return $shopmenu;
    }

    /**
     * 获取 系统管理 菜单
     * @return array
     */
    protected function systemMenu()
    {
        return array(
            'plugin' => array(
                'title'=>'应用',
                'route'=>'plugin/index/main',
                'subtitle'=>'应用管理',
                'icon'=>'plugins',
                'items'=>array(
                    array(
                        'title'=>'应用信息',
                        'route'=>'index/main'
                    ),
                    array(
                        'title'=>'组件信息',
                        'route'=>'coms/main'
                    ),
                    array(
                        'title'=>'公众号权限',
                        'route'=>'perm/main'
                    ),
                    array(
                        'title'=>'站点小程序',
                        'route'=>'wxapp/',
                        'isplugin'=>'app'
                    ),
                    array(
                        'title'=>'应用授权管理',
                        'isplugin'=>'grant',
                        'items'=>array(
                            array(
                                'title'=>'幻灯片管理',
                                'route'=>'pluginadv/main'
                            ),
                            array(
                                'title'=>'授权应用管理',
                                'route'=>'pluginmanage/main'
                            ),
                            array(
                                'title'=>'授权套餐管理',
                                'route'=>'pluginpackage/main'
                            ),
                            array(
                                'title'=>'销售记录',
                                'route'=>'pluginsale/main'
                            ),
                            array(
                                'title'=>'系统授权管理',
                                'route'=>'plugingrant/main'
                            ),
                            array(
                                'title'=>'授权管理设置',
                                'route'=>'pluginsetting/main'
                            )
                        )
                    )
                )
            ),
            'copyright' => array(
                'title'=>'版权',
                'route'=>'copyright/index/main',
                'subtitle'=>'版权设置',
                'icon'=>'banquan',
                'items'=>array(
                    array(
                        'title'=>'手机端',
                        'route'=>'index/main'
                    ),
                    array(
                        'title'=>'管理端',
                        'route'=>'manage/main'
                    ),
                    array(
                        'title'=>'公告管理',
                        'items'=>array(
                            array(
                                'title'=>'公告管理',
                                'route'=>'notice/main'
                            )
                        )
                    )
                )
            ),
            'data' => array(
                'title'=>'数据',
                'route'=>'data/index/main',
                'subtitle'=>'数据管理',
                'icon'=>'statistics',
                'items'=>array(
                    array(
                        'title'=>'数据清理',
                        'route'=>'index/main'
                    ),
                    array(
                        'title'=>'数据转移',
                        'route'=>'transfer/main'
                    ),
                    array(
                        'title'=>'计划任务',
                        'items'=>array(
                            array(
                                'title'=>'计划任务',
                                'route'=>'task/main'
                            )
                        )
                    ),
                    array(
                        'title'=>'工具',
                        'items'=>array(
                            array(
                                'title'=>'七牛存储',
                                'route'=>'qiniu/main',
                                'iscom' => 'qiniu'
                            )
                        )
                    )
                )
            ),
            'site' => array(
                'title'=>'网站',
                'route'=>'site/index/main',
                'subtitle'=>'网站设置',
                'icon'=>'wangzhan',
                'items'=>array(
                    array(
                        'title'=>'系统设置',
                        'items'=>array(
                            array(
                                'title'=>'基本设置'
                            ),
							array(
                                'title'=>'布局设置',
								'route'=>'layout'
                            ),
							array(
                                'title'=>'主题设置',
								'route'=>'theme'
                            ),
							array(
                                'title'=>'首页设置',
								'route'=>'homecontrol'
                            ),
							array(
                                'title'=>'底部设置',
								'route'=>'bottom'
                            ),
							array(
                                'title'=>'模板切换',
								'route'=>'template'
                            )
                        )
                    ),
                    array(
                        'title'=>'站点管理',
                        'items'=>array(
                            array(
                                'title'=>'站点列表',
                                'route'=>'mysite.list'
                            ),
                            array(
                                'title'=>'添加站点',
                                'route'=>'mysite.add'
                            )
                        )
                    ),
                    array(
                        'title'=>'菜单/轮播',
                        'items'=>array(
                            array(
                                'title'=>'幻灯片',
                                'route'=>'banner'
                            ),
                            array(
                                'title'=>'菜单列表',
                                'route'=>'menu'
                            )
                        )
                    ),
                    array(
                        'title'=>'图文管理',
                        'items'=>array(
                            array(
                                'title'=>'优势列表',
                                'route'=>'advantage'
                            ),
                            array(
                                'title'=>'服务项目',
                                'route'=>'service'
                            ),
							array(
                                'title'=>'应用中心',
                                'route'=>'app'
                            ),
							array(
                                'title'=>'应用分类',
                                'route'=>'apptype'
                            ),
                            array(
                                'title'=>'案例分类',
                                'route'=>'casecategory'
                            ),
                            array(
                                'title'=>'案例列表',
                                'route'=>'case'
                            ),
                        )
                    ),
                    array(
                        'title'=>'新闻帮助',
                        'items'=>array(
                            array(
                                'title'=>'新闻中心',
                                'route'=>'news'
                            ),
                            array(
                                'title'=>'新闻分类',
                                'route'=>'newstype'
                            ),
                            array(
                                'title'=>'帮助文档',
                                'route'=>'help'
                            ),
                            array(
                                'title'=>'帮助分类',
                                'route'=>'helptype'
                            )
                        )
                    ),
                    array(
                        'title'=>'功能套餐',
                        'items'=>array(
                            array(
                                'title'=>'套餐列表',
                                'route'=>'functiontype'
                            ),
                            array(
                                'title'=>'功能列表',
                                'route'=>'function'
                            )
                        )
                    ),
                    array(
                        'title'=>'资讯文章',
                        'items'=>array(
                            array(
                                'title'=>'文章分类',
                                'route'=>'category'
                            ),
                            array(
                                'title'=>'文章管理',
                                'route'=>'article'
                            )
                        )
                    ),
                    array(
                        'title'=>'企业信息',
                        'items'=>array(
                            array(
                                'title'=>'信息分类',
                                'route'=>'companycategory'
                            ),
                            array(
                                'title'=>'信息管理',
                                'route'=>'companyarticle'
                            )
                        )
                    ),
                    array(
                        'title'=>'关于我们',
                        'items'=>array(
                            array(
                                'title'=>'关于我们',
                                'route'=>'we'
                            ),
                            array(
                                'title'=>'团队风采',
                                'route'=>'team'
                            ),
                            array(
                                'title'=>'荣誉证书',
                                'route'=>'honor'
                            ),
							array(
                                'title'=>'招聘管理',
                                'route'=>'job'
                            ),
							array(
                                'title'=>'客户评价',
                                'route'=>'customer'
                            )
                        )
                    ),
					array(
                        'title'=>'招商加盟',
                        'items'=>array(
                            array(
                                'title'=>'加盟设置',
                                'route'=>'join'
                            ),
							array(
                                'title'=>'盈利模式',
                                'route'=>'model'
                            ),
							array(
                                'title'=>'加盟申请',
                                'route'=>'message'
                            )
                        )
                    ),
					array(
                        'title'=>'友情链接',
                        'items'=>array(
                            array(
                                'title'=>'友情链接',
                                'route'=>'link'
                            )
                        )
                    ),
                    array(
                        'title'=>'留言管理',
                        'items'=>array(
                            array(
                                'title'=>'留言内容',
                                'route'=>'guestbook'
                            )
                        )
                    ),
                    array(
                        'title'=>'其它设置',
                        'items'=>array(
                            array(
                                'title'=>'基础设置',
                                'route'=>'setting'
                            )
                        )
                    )
                )
            )
        );
    }

    /**
     * 获取 其他 菜单
     * @return array
     */
    protected function otherMenu()
    {
        return array(
            'perm' => array(
                'title'=>'权限',
                'subtitle'=>'权限系统',
                'icon'=>'store',
                'items'=>array(
                    array(
                        'title'=>'角色管理',
                        'route'=>'role'
                    ),
                    array(
                        'title'=>'操作员管理',
                        'route'=>'user'
                    ),
                    array(
                        'title'=>'操作日志',
                        'route'=>'log'
                    )
                )
            )
        );
    }
    /**
     * 获取 PC端 菜单
     * @return array
     */
    protected function pcmenu(){
        return array(
            'version' => '1.0',
            'id' => 'pc',
            'name' => 'pc商城',
            'menu' => array(
                'title' => '页面',
                'plugincom' => 1,
                'icon' => 'page',
                'items' => array(
                    array(
                        'title' => '商品组列表',
                        'route' => 'goods',
                        'items' => array(
                            array(
                                'title' => '商品组编辑',

                            ),

                        )
                    ),
                    array(
                        'title' => '菜单管理',
                        'route' => 'menu',
                        'items' => array(
                            array(
                                'title' => '顶部导航',
                                'route' => 'top',
                            ),
                            array(
                                'title' => '底部导航',
                                'route' => 'bottom',
                            ),

                        )
                    ),
                    array(
                        'title' => '广告管理',
                        'route' => 'adv',
                        'items' => array(
                            array(
                                'title' => '首页轮播',
                                'route' => 'banner',
                            ),
                            array(
                                'title' => '推荐广告',
                                'route' => 'recommend'
                            )
                        )
                    ),
                    array(
                        'title' => '排版设置',
                        'route' => 'typesetting'
                    )
                )
            )
        );
    }
    /**
     * 获取 插件 菜单
     * @param array $plugin 要获取的插件标识
     * @return array
     */
    protected function pluginMenu($plugin = array(), $key = 'menu')
    {
        global $_W;
        if(empty($plugin)){
            return array();
        }

        $config = m('plugin')->getPluginConfig($plugin);
        //pc模板
//        if(p('pc') && $_W['routes']!='pc'&& $_W['routes']!='pc.setting' && $_W['plugin'] =='pc'){
//            $config = $this->pcmenu();
//        }
        //多商户积分商城菜单去掉待收货
        if($plugin == 'creditshop'){
            if($_W['merchid'] > 0){
                unset($config[$key]['items'][5]['items'][1]);
            }
        }
        //多商户的积分商城提现。如果未开启在在路由中关闭
        if(p('merch')){
            $params = array(':uniacid' => $_W['uniacid'],':merchid'=>$_W['merchid']);
            $_condition = "and uniacid=:uniacid and id=:merchid";
            $_sql = "select iscredit from".tablename('elapp_shop_merch_user')."where 1 {$_condition}";
            $iscredit = pdo_fetch($_sql, $params);
            if (!empty($iscredit)) {
                $iscredit = $iscredit['iscredit'];
                if($iscredit==1) {
                    unset($config['manage_menu']['apply']['items'][1]);
                }
            }
        }
        //机构合伙人的积分商城提现。如果未开启在在路由中关闭
        if(p('copartner')){
            $params = array(':uniacid' => $_W['uniacid'],':copartner_id'=>$_W['copartner_id']);
            $_condition = "and uniacid=:uniacid and id=:copartner_id";
            $_sql = "select iscredit from".tablename('elapp_shop_copartner_user')."where 1 {$_condition}";
            $iscredit = pdo_fetch($_sql, $params);
            $iscredit = $iscredit['iscredit'];
            if($iscredit==1) {
                unset($config['manage_menu']['apply']['items'][1]);
            }
        }
        //供应商的积分商城提现。如果未开启在在路由中关闭
        if(p('supply')){
            $params = array(':uniacid' => $_W['uniacid'],':supplyid'=>$_W['supplyid']);
            $_condition = "and uniacid=:uniacid and id=:supplyid";
            $_sql = "select iscredit from".tablename('elapp_shop_supply_user')."where 1 {$_condition}";
            $iscredit = pdo_fetch($_sql, $params);
            if (!empty($iscredit)) {
                $iscredit = $iscredit['iscredit'];
                if($iscredit==1) {
                    unset($config['manage_menu']['apply']['items'][1]);
                }
            }
        }

        return empty($config[$key])?array():$config[$key];
    }

    /**
     * 获取 全部插件 菜单
     * @return array
     */
    protected function allPluginMenu()
    {
        return array();
    }

    /**
     * 判断二级、三级带参的Active状态
     * @param array $item
     * @return bool
     */
    protected function verifyParam($item = array())
    {
        global $_GPC;

        if(empty($item['param'])){
            return true;
        }
        $return = true;
        foreach ($item['param'] as $k=>$v){
            if($_GPC[$k]!=$v){
                $return = false;
                break;
            }
        }
        return $return;
    }

    /**
     * 初始化右侧顶部菜单
     */
    protected function initRightMenu($routes)
    {
        global $_W;

        $return_arr = array(
            'system'=>0,
            'menu_title'=>'',
            'menu_items'=>array(),
            'logout'=>''
        );

        if($this->merch){
            // 判断多商户显示右侧菜单
            $return_arr['menu_title'] = $_W['merch_username']."//".$_W['uniaccount']['username'];
            $return_arr['menu_items'][] = array(
                'text'=>'修改密码',
                'href'=>webUrl('index/updatepassword')
            );
            $return_arr['logout']=webUrl('index/quit');
        }elseif($this->copartner){
            // 判断机构合伙人显示右侧菜单
            $return_arr['menu_title'] = $_W['copartner_username']."//".$_W['uniaccount']['username'];
            $return_arr['menu_items'][] = array(
                'text'=>'修改密码',
                'href'=>copartnerUrl('updatepassword')
            );
            $return_arr['logout']=copartnerUrl('quit');
        }elseif($this->supply){
            // 判断供应商显示右侧菜单
            $return_arr['menu_title'] = $_W['supply_username']."//".$_W['uniaccount']['username'];
            $return_arr['menu_items'][] = array(
                'text'=>'修改密码',
                'href'=>supplyUrl('updatepassword')
            );
            $return_arr['logout']=supplyUrl('quit');
        }elseif($this->org){
            // 判断组织管理显示右侧菜单
            $return_arr['menu_title'] = $_W['org_username']."//".$_W['uniaccount']['username'];
            $return_arr['menu_items'][] = array(
                'text'=>'修改密码',
                'href'=>webUrl('index/updatepassword')
            );
            $return_arr['logout']=webUrl('index/quit');
        }else{
            // 判断主商城显示右侧菜单
            $return_arr['menu_title'] = $_W['uniaccount']['name'];
            if($_W['role'] == 'founder' && $routes[0]!='system'){
                $return_arr['system'] = 1;
            }
            if($routes[0]=='system'){
                $return_arr['menu_items'][] = array(
                    'text'=>'返回商城',
                    'href'=>webUrl(),
                    'icow'=>'icow-qiehuan'
                );
            }else{
                $return_arr['menu_items'][] = array(
                    'text'=>'切换公众号',
                    'href'=>webUrl('sysset/account/main'),
                    'icow'=>'icow-qiehuan'
                );
                if($_W['role'] == 'manager' || $_W['role'] == 'founder'){
                    $return_arr['menu_items'][] = array(
                        'text'=>'编辑公众号',
                        'href'=>webUrl('account/post/main', ['uniacid' => $_W['uniacid'], 'acid' => $_W['acid']]),
                        'blank'=>'true',
                        'icow'=>'icow-bianji5'
                    );
                    $return_arr['menu_items'][] = array(
                        'text'=>'支付方式',
                        'href'=>webUrl('sysset/index/payset'),
                        'icow'=>'icow-zhifu'
                    );
                }
                $permset = intval(m('cache')->getString('permset', 'global'));

                if (com('perm') && cv('perm')) {
                    $return_arr['menu_items'][] = 'line';
                    $return_arr['menu_items'][] = array(
                        'text'=>'权限管理',
                        'href'=>webUrl('perm'),
                        'icow'=>'icow-quanxian'
                    );
                }
                if(p('grant')){
                    $return_arr['menu_items'][] = 'line';
                    $return_arr['menu_items'][] = array(
                        'text'=>'应用授权',
                        'href'=>webUrl('plugingrant'),
                        'icow'=>'icow-shouquan'
                    );
                }

                $return_arr['menu_items'][] = array(
                    'text'=>'修改密码',
                    'href'=> url('user/profile'),
                    'blank'=>true,
                    'icow'=>'icow-quanxian1'
                );
            }
            $return_arr['logout'] = url('user/logout');
        }

        return $return_arr;
    }

    /**
     * 获取后台数据
     * @return array
     */
    public function init()
    {
        global $_W, $_GPC;

        $routes = explode(".", $GLOBALS['_W']['routes']);

        $arr = array(
            'merch'=>$this->merch?1:0,
            'org'=>$this->org?1:0,
            'copartner'=>$this->copartner?1:0,
            'supply'=>$this->supply?1:0,
            'order1'=>0,
            'order4'=>0,
            'notice'=>array(),
            'commission1'=>0,
            'commission2'=>0,
            'clerkCommission1'=>0,
            'clerkCommission2'=>0,
            'copartnerCommission1'=>0,
            'copartnerCommission2'=>0,
            'comment'=>0,
            'foldnav'=>intval($_COOKIE['foldnav']),
            'foldpanel'=>intval($_COOKIE['foldpanel']),
            'routes'=>$routes,
            'funbar'=>array(
                'open'=>intval($_W['shopset']['shop']['funbar'])
            ),

            // 顶部右侧菜单
            'right_menu'=>$this->initRightMenu($routes)
        );


        if($this->cv('order.list.status1')){
            $arr['order1'] = $this->getOrderTotal(1);
        }
        if($this->cv('order.list.status4')){
            $arr['order4'] = $this->getOrderTotal(4);
        }

        if(!$this->merch || !$this->org || !$this->supply || !$this->copartner){
            $arr['notice'] = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_system_copyright_notice') . " ORDER BY displayorder DESC,createtime DESC LIMIT 5" );
            //分销商
            if(p('commission')) {
                if ($this->cv('commission.apply.view1')) {
                    $arr['commission1'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_commission_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_commission_level') . " l on l.id = m.agentlevel"
                        . " where a.uniacid=:uniacid and a.status=:status", array(':uniacid' => $_W['uniacid'], ':status' => 1));
                }
                if ($this->cv('commission.apply.view2')) {
                    $arr['commission2'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_commission_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_commission_level') . " l on l.id = m.agentlevel"
                        . " where a.uniacid=:uniacid and a.status=:status", array(':uniacid' => $_W['uniacid'], ':status' => 2));
                }
            }

            //云店长
            if(p('clerk')) {
                if ($this->cv('clerk.apply.view1')) {
                    $arr['clerkCommission1'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
                        . " where a.uniacid=:uniacid and a.status=:status and money_type=:money_type", array(':uniacid' => $_W['uniacid'], ':status' => 0, ':money_type' => 0));
                }
                if ($this->cv('clerk.apply.view2')) {
                    $arr['clerkCommission2'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
                        . " where a.uniacid=:uniacid and a.status=:status and money_type=:money_type", array(':uniacid' => $_W['uniacid'], ':status' => 2, ':money_type' => 0));
                }
            }

            //合伙人
            if(p('copartner')) {
                if ($this->cv('copartner.apply.view1')) {
                    $arr['copartnerCommission1'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
                        . " where a.uniacid=:uniacid and a.status=:status and money_type=:money_type", array(':uniacid' => $_W['uniacid'], ':status' => 0, ':money_type' => 1));
                }
                if ($this->cv('clerk.apply.view2')) {
                    $arr['copartnerCommission2'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_settle_withdraw_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_clerk_level') . " l on l.id = m.clerk_level"
                        . " where a.uniacid=:uniacid and a.status=:status and money_type=:money_type", array(':uniacid' => $_W['uniacid'], ':status' => 2, ':money_type' => 1));
                }
            }

            //医生
            if(p('doctor')) {
                if ($this->cv('doctor.apply.view1')) {
                    $arr['doctorCommission1'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_tcmd_doctor_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_doctor_level') . " l on l.id = m.doctor_level"
                        . " where a.uniacid=:uniacid and a.status=:status", array(':uniacid' => $_W['uniacid'], ':status' => 1));
                }
                if ($this->cv('doctor.apply.view2')) {
                    $arr['doctorCommission2'] = pdo_fetchcolumn("select count(1) from" . tablename('elapp_shop_tcmd_doctor_apply') . " a "
                        . " left join " . tablename('elapp_shop_member') . " m on m.uid = a.mid"
                        . " left join " . tablename('elapp_shop_doctor_level') . " l on l.id = m.doctor_level"
                        . " where a.uniacid=:uniacid and a.status=:status", array(':uniacid' => $_W['uniacid'], ':status' => 2));
                }
            }

            if($this->cv('shop.comment')){
                $arr['comment'] = pdo_fetchcolumn("SELECT COUNT(1) FROM " . tablename('elapp_shop_order_comment') . "c LEFT JOIN ". tablename('elapp_shop_goods') ." g ON g.id=c.goodsid WHERE (c.checked=1 OR c.replychecked=1) AND c.deleted=0 AND c.uniacid=:uniacid AND g.merchid=:merchid", array(':uniacid'=>$_W['uniacid'], ':merchid'=>0));
            }
        }else{
            $arr['notice']='none';
            if($this->cv('shop.comment')){
                $arr['comment'] = pdo_fetchcolumn("SELECT COUNT(1) FROM " . tablename('elapp_shop_order_comment') . "c LEFT JOIN ". tablename('elapp_shop_goods') ." g ON g.id=c.goodsid WHERE (c.checked=1 OR c.replychecked=1) AND c.deleted=0 AND c.uniacid=:uniacid AND g.merchid=:merchid", array(':uniacid'=>$_W['uniacid'], ':merchid'=>$_W['merchid']));
            }
        }

        if(!empty($arr['funbar']['open'])){
            $funbardata = pdo_fetch('select * from '. tablename('elapp_shop_funbar').' where uid=:uid and uniacid=:uniacid limit 1', array(':uid'=>$_W['uid'], ':uniacid'=>$_W['uniacid']));
            if(!empty($funbardata['datas']) && !is_array($funbardata['datas'])){
                if(strexists($funbardata['datas'], '{"')){
                    $funbardata['datas'] = json_decode($funbardata['datas'], true);
                }else{
                    $funbardata['datas'] = unserialize($funbardata['datas']);
                }
            }
            $arr['funbar']['data'] = $funbardata['datas'];
        }

        $arr['url'] = str_replace($_W['siteroot'].'web.php/', './', $_W['siteurl']);

        if(!$this->merch){
            $history_url = htmlspecialchars_decode($_GPC['history_url']);
        }else{
            $history_url = htmlspecialchars_decode($_GPC['merch_history_url']);
        }
        if(!empty($history_url)){
            $arr['history'] = json_decode($history_url, true);
        }

        return $arr;
    }

    protected function getOrderTotal($status = 0)
    {
        global $_W;

        $total = 0;
        $condition = 'uniacid = :uniacid and isparent=0 and ismr=0  and deleted=0 and istrade=0 and iscycelbuy=0';
        $params = array(':uniacid' => $_W['uniacid']);
        if($this->merch){
            $condition .= ' and merchid=:merchid';
            $params[':merchid'] = intval($_W['merchid']);
        }
        if($status==1){
            $condition .= ' and ( status=1 or ( status=0 and paytype=3) )';
        } elseif ($status == 4) {
            $condition .= ' and ((refundstate>0 and refundid<>0 and refundtime=0) or (refundtime=0 and refundstate=3))';
        }
        $total = pdo_fetchcolumn('SELECT COUNT(1) FROM ' . tablename('elapp_shop_order') . " WHERE ". $condition, $params);
        return $total;
    }

    public function cv($str){
        if($str=='plugins'){
            $str = $this->isOpenPlugin();
        }

        if($this->merch || $this->org || $this->copartner){
            return mcv($str);
        }else{
            return cv($str);
        }

    }

    public function isOpenPlugin()
    {
        if (!com('perm')) {
            return array();
        }
        $name = com_run('perm::allPerms');
        unset($name['shop']);
        unset($name['goods']);
        unset($name['member']);
        unset($name['order']);
        unset($name['finance']);
        unset($name['statistics']);
        unset($name['sysset']);
        unset($name['sale']);
        $name_keys = array_keys($name);
        return implode('|',$name_keys);
    }

    /**
     * 处理历史记录
     */
    public function history_url()
    {
        global $_W, $_GPC;

        $history_url = '';
        if(!$this->merch){
            $history_url = @$_GPC['history_url'] ?? "";
        }else{
            $history_url = @$_GPC['merch_history_url'] ?? "";
        }

        if(!$this->org){
            $history_url = @$_GPC['history_url'] ?? "";
        }else{
            $history_url = @$_GPC['org_history_url'];
        }

        if(empty($history_url)){
            $history_url = array();
        }else{
            $history_url = htmlspecialchars_decode($history_url);
            $history_url = json_decode($history_url, true);
        }

        if(!empty($history_url)){
            $this_url = str_replace($_W['siteroot'].'web/', './', $_W['siteurl']);
            foreach ($history_url as $index=>$history_url_item){
                $item_url = str_replace($_W['siteroot'].'web/', './', $history_url_item['url']);
                if($item_url==$this_url){
                    unset($history_url[$index]);
                }
            }
        }

        $submenu = $this->getSubMenus(true);
        $thispage = array();

        if(!empty($submenu)){
            foreach ($submenu as $submenu_item){
                if(!empty($_GPC['r']) && $_GPC['r'] == $submenu_item['route'] && $this->verifyParam($submenu_item)){
                    $submenu_item['url'] = str_replace($_W['siteroot'].'web/', './', $submenu_item['url']);
                    $thispage = $submenu_item;
                    if(!empty($submenu_item['toptitle'])){
                        $thispage['title'] = $submenu_item['toptitle'].'-'.$submenu_item['title'];
                    }
                    break;
                }
            }
        }

        if($thispage){
            $thispage_item = array(array(
                'title'=>$thispage['title'],
                'url'=>$thispage['url'],
            ));
            $history_url = array_merge($thispage_item, $history_url);
            if(is_array($history_url) && count($history_url)>10){
                $history_url = array_slice($history_url,0,10);
            }

            isetcookie(!$this->merch?'history_url':'merch_history_url', json_encode($history_url), 7 * 86400);
        }
    }

    /**
     * 设置用户版本
     */
    public function set_version($type = 0){
        $GLOBALS['_W']['shopversion'] = 1;
    }

    /**
     * 获取 插件 移动端菜单
     * @param array|string $plugin 要获取的插件标识
     * @param string $key
     * @return array
     */
    public function pluginMobileMenu($plugin = array(), string $key = 'mobile_menu'): array
    {
        global $_W;
        if (empty($plugin)) {
            return array();
        }
        $config = m('plugin')->getPluginConfig($plugin);
        return empty($config[$key])?array():$config[$key];
    }
}
