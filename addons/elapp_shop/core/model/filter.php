<?php
namespace app\model;

class FilterModel {
    // 1. [用户|订单]  createtime > 某个时间
    // 2. copartner_id not in (?) 限制访问机构
    const TABLE_MEMBER              = 'elapp_shop_member';
    const TABLE_ORDER               = 'elapp_shop_order';
    const TABLE_MEMBER_CARD         = 'elapp_shop_member_card';
    const TABLE_SHOP_GOODS          = 'elapp_shop_goods';
    const TABLE_SHOP_COPARTNER_USER = 'elapp_shop_copartner_user';


    private function show_cfg() {
        return;
        $config = [];
        $config['filter'] = [];
        $config['filter']['open'] = true;
        // 限制访问数据时间
//            $config['filter']['limit_access_time'] = time() - 3600 * 24 * 30 * 2;
        $config['filter']['limit_access_time'] = 1656019749; // 时间之后的数据 1个月
        $config['filter']['limit_no_access_copartnerids'] = [2,3];
        echo json_encode($config);exit;
    }

    // 获取角色配置
    public function getRoleFilterConfig()
    {
        global $_W;
        $uid = $_W['uid'];

        session_start();
        // 从 session 读取授权数据，防止多次重复数据库，读取或已过期则读取数据库
//        if (isset($_SESSION['auth']) &&
//            $_SESSION['auth']['uid'] == $uid &&
//            $_SESSION['auth']['role_config'] &&
//            time() < $_SESSION['auth']['expiretime']) {
//            return $_SESSION['auth']['role_config'];
//        } else {
//            $_SESSION['auth'] = [
//                'uid'=> $uid
//            ];
//        }

        $roleid = pdo_getcolumn('elapp_shop_perm_user', ['uid'=>$uid],'roleid');

        $config = pdo_get('elapp_shop_perm_role', ['id'=>$roleid]);
        if (isset($config['config']) && !empty($config['config'])) {
            $config = json_decode($config['config'], true);
            $_SESSION['auth']['role_config'] = $config;
            $_SESSION['auth']['expiretime'] = time() + 3600; // 有效期3600
        } else {
            return [];
        }

        return $config;
    }

    // 注入 参数到 condition 到 sql （实时更改参数）
    public function injectConditions(&$condition, &$params,$table, $table_alias = '')
    {
        if (empty($condition)) {
            $condition = ' 1=1 '; // todo 这里不添加 where，如果其他地方调用的地方condition有where，需要注意
        }
        $conds = $this->getConditions($table, $table_alias);

        if (!empty($conds)) {
            foreach ($conds as $cond) {
                $condition .= $cond['cond'];
                if ($cond['key'] && $cond['value']) {
                    $params[ $cond['key'] ] = $cond['value'];
                }
            }
        }
    }

    // 注入 condition 到 sql，此种方式为无参方式，将参数注入到sql中
    // 一般情况下使用有参数的方式会更好，但是部分代码可能没有使用参数查询方式，因此提供此项用作兼容处理
    public function injectConditionsSql(&$condition, $table, $table_alias = '')
    {
        if (empty($condition)) {
            $condition = '';
        }
        $sql = $this->getConditionsSql($table, $table_alias);
        if (!empty($sql)) {
            $condition .= $sql;
        }
    }

    public function getConditions($t, $t_alias = '')
    {
        return $this->conditions_map($t, $t_alias);
    }

    /**
     * @param $t string
     * @param $t_alias string
     * @return string
     */
    public function getConditionsSql($t, $t_alias='')
    {
        $maps =  $this->conditions_map($t, $t_alias);
        if (empty($maps)) return '';

        $sqlString = '';
        foreach ($maps as $map) {
            $sql = $map['cond'];

            if (is_string($map['value'])) {
                $value = str_replace("'", "\'", $map['value']);
                $sql = str_replace($map['key'], "'{$value}'", $sql);
            } else {
                $sql = str_replace($map['key'], $map['value'], $sql);
            }

            $sqlString .= $sql;
        }
        return $sqlString;
    }

    /**
     * 所有限制条件map
     * @param $table string 限制表
     * @param $alias string 表别名，只有一个表时可以不传
     * @return array
     */
    private function conditions_map($table, $alias='')
    {
        if (!empty($alias)) {
            $alias = $alias . '.';
        } else {
            $alias = '';
        }

        $config = $this->getRoleFilterConfig();
        if (empty($config) || empty($config['filter']) || empty($config['filter']['open'])) {
            return [];
        }
        $filter = $config['filter'];

        // todo yh 参数检查(空）
        $maps = [
            self::TABLE_MEMBER => [
                // 限制条件写法，注意：in和not in的值必须是数组
                // [ 字段, 操作符, 条件 ]  => 生成语句，例： "and m.create > 1656019749"
//                ['createtime', '>', $filter['limit_access_time']],
                ['copartner_id', 'not in', $filter['limit_no_access_copartnerids']],
            ],
            self::TABLE_ORDER => [
                //['createtime', '>', $filter['limit_access_time']],
                // 6月和8月的数据
                '( {createtime} between 1685548800 and 1688140799 or {createtime} between 1690819200 and 1693497599)',
                ['copartner_id', 'not in', $filter['limit_no_access_copartnerids']],
            ],
            self::TABLE_MEMBER_CARD => [
                // 6月和8月的数据
                '( {createtime} between 1685548800 and 1688140799 or {createtime} between 1690819200 and 1693497599)',
                //['createtime', '>', $filter['limit_access_time']],
                ['copartner_id', 'not in', $filter['limit_no_access_copartnerids']],
            ],
            self::TABLE_SHOP_GOODS => [
                // 这是另一种特殊需求，需要联合条件过滤，这里是需要过滤非中药处方商品，所以不能使用单一条件
                // 需要3个条件同时检查，如果有一个不满足，则允许
                // goodsClassID = 2 and medicineClassID = 2 and medicineAttributeID = 1
                // 字符串类型暂不支持参数处理
                '( {goodsClassID} != 2 or {medicineClassID} != 2 or {medicineAttributeID} != 1 )'
                // todo 此种写法可能后续提供支持，暂时不实现
//                ['(',
//                 ['goodsClassID', '!=', 2], 'or',
//                 ['medicineClassID', '!=', 2], 'or',
//                 ['medicineAttributeID', '!=', 1],')']
            ],
            self::TABLE_SHOP_COPARTNER_USER => [
                ['id', 'not in', $filter['limit_no_access_copartnerids']],
            ]
        ];
        $condition_map = $maps[$table];

        if (empty($condition_map)) return [];

        $conds = [];
        foreach ($condition_map as $item) {

            if (is_string($item)) {

                $pattern = '/\{([A-Za-z0-9\_]+)\}/';
                preg_match_all($pattern, $item, $matches);
                if (!empty($matches)) {
                    foreach ($matches[1] as $mat) {
                        // 替换字段 添加别名
                        $item = str_replace('{' . $mat . '}', $alias . $mat, $item);
                    }
                }
                $key = $value = '';
                $cond = " AND {$item} ";

            } else if ($item[1] == 'not in' || $item[1] == 'in') {

                $_value = implode(',', $item[2]);
                $cond   = " AND {$alias}{$item[0]} {$item[1]} ($_value) ";
                $key    = $value = '';
            } else {
                $cond = " AND {$alias}{$item[0]} {$item[1]} :filter_{$item[0]} ";
                $key = ":filter_{$item[0]}";
                $value = $item[2];
            }

            $conds[] = $this->condition($cond, $key, $value);
        }
        return $conds;
    }

    // todo 太复杂了，没时间，暂时不实现了，用简单的方案实现 ( ###.id = 1 or ###.name = 'hello')
    // 获取将条件数组转换为表达式
    // ['(', ['a', '!=', 1], 'or', ['b', '=', 2],')']
    // 将输出
    // (a != 1 or b = 2)
    public function getExpression($map, $alias = '')
    {
        // 1. 条件数组 （普通条件，in/not in条件
        // 2. 组合数组

        // 组合条件数组
        if ($map[0] == '(' && $map[count($map)-1] == ')') {
            //$map = array_slice($map, 1, count($map)-2);
            $index = 0;
            $cons = [];
            foreach ($map as $m) {
                $index++;
                //如果是奇数直接拼接
                if ($index % 2 == 1) {
                    $cons[] = $m;
                } else {
                    $cons[] = $this->getExpression($m, $alias);
                }
            }

        } else if ($map[1] == 'not in' || $map[1] == 'in') {
            $_value = implode(',', $map[2]);
            $cond   = " AND {$alias}{$map[0]} {$map[1]} ($_value)";
            $key    = $value = '';
            // 如果第一个元素是(，最后一个元素也是)，则表示是一个子查询
        } else {
            $cond = " AND {$alias}{$map[0]} {$map[1]} :filter_{$map[0]} ";
            $key = ":filter_{$map[0]}";
            $value = $map[2];
        }

//        $expression = '';
//        foreach ($conds as $cond) {
////            if (in_array($cond, ['(',')','or','not in', 'in']))
//            if (is_array($cond)) {
//                $expression .= ' ' . $this->getExpression($cond);
//            } else {
//                $expression .= ' ' . $cond;
//            }
//        }
//        return $expression;
    }

    private function condition($condition, $key, $value)
    {
        return [
            'cond' => $condition,
            'key' => $key,
            'value' => $value,
        ];
    }
}
