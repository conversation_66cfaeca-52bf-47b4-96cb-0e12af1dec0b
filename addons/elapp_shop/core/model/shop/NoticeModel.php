<?php

namespace app\core\model\shop;

use app\core\model\MicroEngineModel;
use app\model\CommonModel;

/**
 * 商城公告模型类
 * class NoticeModel
 * @package app\core\model\shop
 * <AUTHOR>
 * @date 2024/09/26 20:07
 */
class NoticeModel extends MicroEngineModel
{
    protected $name = 'notice';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'displayorder',
                'title',
                'thumb',
                'link',
                'detail',
                'status',
                'createtime'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 获取器定义 start
    public function getDetailAttr($value)
    {
        return htmlspecialchars($value);
    }
    public function getThumbAttr($value)
    {
        return tomedia(empty($value) ? (new CommonModel)->getSysset('shop')['logo'] : $value);
    }
    // 获取器定义 end
}