<?php
namespace app\model;

class BindModel {
    public function iswxm($member=array()) {
        if(empty($member) || !is_array($member)){
            return true;
        }
        if(strexists($member['openid'], 'sns_wx_') || strexists($member['openid'], 'sns_qq_') || strexists($member['openid'], 'sns_wa_') || strexists($member['openid'], 'wap_user_')) {
            return false;
        }
        return true;
    }
    public function update($mid=0, $arr=array()){
        global $_W;
        if(empty($mid) || empty($arr) || !is_array($arr)){
            return;
        }
        pdo_update('elapp_shop_member', $arr, array('id'=>$mid,'uniacid'=>$_W['uniacid']));
    }
    public function merge($a=array(), $b=array()){
        global $_W;

        if(empty($a) || empty($b) || $a['id']==$b['id']){
            return error(0, "params error");
        }

 
        $createtime = $a['createtime'] > $b['createtime'] ? $b['createtime'] : $a['createtime'];
        $childtime = $a['childtime'] > $b['childtime'] ? $b['childtime'] : $a['childtime'];
        $comparelevel = m('member')->compareLevel(array($a['level'], $b['level']));
        $level = $comparelevel ? $b['level'] : $a['level'];
 
        $isblack = !empty($a['isblack']) || !empty($b['isblack']) ? 1 : 0;

  
        $openid_qq = !empty($b['openid_qq']) && empty($a['openid_qq']) ? $b['openid_qq'] : $a['openid_qq'];
        $openid_wx = !empty($b['openid_wx']) && empty($a['openid_wx']) ? $b['openid_wx'] : $a['openid_wx'];
        $openid_wa = !empty($b['openid_wa']) && empty($a['openid_wa']) ? $b['openid_wa'] : $a['openid_wa'];

        //分销商
        if(!empty($a['isagent']) && empty($b['isagent'])){
            $isagent = 1;
            $agentid = $a['agentid'];
            $status = !empty($a['status']) ? 1 : 0;
            $agenttime = $a['agenttime'];
            $agentlevel = $a['agentlevel'];
            $agentblack = $a['agentblack'];
            $fixagentid = $a['fixagentid'];
        }
 
        elseif(!empty($b['isagent']) && empty($a['isagent'])){
            $isagent = 1;
            $agentid = $b['agentid'];
            $status = !empty($b['status']) ? 1 : 0;
            $agenttime = $b['agenttime'];
            $agentlevel = $b['agentlevel'];
            $agentblack = $b['agentblack'];
            $fixagentid = $b['fixagentid'];
        }
  
        elseif(!empty($b['isagent']) && !empty($a['isagent'])){
           
            $compare = p('commission')->compareLevel(array($a['agentlevel'], $b['agentlevel']));
            $isagent = 1;
            if($compare){
                $agentid = $b['agentid'];
                if(empty($b['agentid']) && !empty($a['agentid'])){
                    $agentid = $a['agentid'];
                }
                if ($agentid == $b['id'] || $agentid == $a['id']) {
                    $agentid = 0;
                }
                $status = !empty($b['status']) ? 1 : 0;
                $agentblack = !empty($b['agentblack']) ? 1 : 0;
                $fixagentid = !empty($b['fixagentid']) ? 1 : 0;
            }else{
                $agentid = $a['agentid'];
                if(empty($a['agentid']) && !empty($b['agentid'])){
                    $agentid = $b['agentid'];
                }
                if ($agentid == $b['id'] || $agentid == $a['id']) {
                    $agentid = 0;
                }
                $status = !empty($a['status']) ? 1 : 0;
                $agentblack = !empty($a['agentblack']) ? 1 : 0;
                $fixagentid = !empty($a['fixagentid']) ? 1 : 0;
            }
            $agenttime = $compare ? $b['agenttime'] : $a['agenttime'];
            $agentlevel = $compare ? $b['agentlevel'] : $a['agentlevel'];
        }
        
        elseif (empty($b['isagent']) && empty($a['isagent'])){
            if(!empty($a['agentid']) && !empty($b['agentid'])){
                $agentid = $a['agentid'];
            }
            elseif(empty($a['agentid']) && !empty($b['agentid'])){
                $agentid = $b['agentid'];
            }
            elseif (!empty($a['agentid']) && empty($b['agentid'])){
                $agentid = $a['agentid'];
            }
            if(!empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
            elseif(empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $b['inviter_id'];
            }
            elseif (!empty($a['inviter_id']) && empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
        }
        //店员
        if(!empty($a['is_clerk']) && empty($b['is_clerk'])){
            $is_clerk = 1;
            $clerk_id = $a['clerk_id'];
            $clerk_status = !empty($a['clerk_status']) ? 1 : 0;
            $clerk_create_time = $a['clerk_create_time'];
            $clerk_level = $a['clerk_level'];
            $clerk_black = $a['clerk_black'];
            $fix_clerk_id = $a['fix_clerk_id'];
        }
 
        elseif(!empty($b['is_clerk']) && empty($a['is_clerk'])){
            $is_clerk = 1;
            $clerk_id = $b['clerk_id'];
            $clerk_status = !empty($b['clerk_status']) ? 1 : 0;
            $clerk_create_time = $b['clerk_create_time'];
            $clerklevel = $b['clerk_level'];
            $clerk_black = $b['clerk_black'];
            $fix_clerk_id = $b['fix_clerk_id'];
        }
  
        elseif(!empty($b['is_clerk']) && !empty($a['is_clerk'])){
           
            $clerkcompare = p('clerk')->compareLevel(array($a['clerk_level'], $b['clerk_level']));
            $is_clerk = 1;
            if($clerkcompare){
                $clerk_id = $b['clerk_id'];
                if(empty($b['clerk_id']) && !empty($a['clerk_id'])){
                    $clerk_id = $a['clerk_id'];
                }
                if ($clerk_id == $b['id'] || $clerk_id == $a['id']) {
                    $clerk_id = 0;
                }
                $clerk_status = !empty($b['clerk_status']) ? 1 : 0;
                $clerk_black = !empty($b['clerk_black']) ? 1 : 0;
                $fix_clerk_id = !empty($b['fix_clerk_id']) ? 1 : 0;
            }else{
                $clerk_id = $a['clerk_id'];
                if(empty($a['clerk_id']) && !empty($b['clerk_id'])){
                    $clerk_id = $b['clerk_id'];
                }
                if ($clerk_id == $b['id'] || $clerk_id == $a['id']) {
                    $clerk_id = 0;
                }
                $clerk_status = !empty($a['clerk_status']) ? 1 : 0;
                $clerk_black = !empty($a['clerk_black']) ? 1 : 0;
                $fix_clerk_id = !empty($a['fix_clerk_id']) ? 1 : 0;
            }
            $clerk_create_time = $clerkcompare ? $b['clerk_create_time'] : $a['clerk_create_time'];
            $clerklevel = $clerkcompare ? $b['clerk_level'] : $a['clerk_level'];
        }
        
        elseif (empty($b['is_clerk']) && empty($a['is_clerk'])){
            if(!empty($a['clerk_id']) && !empty($b['clerk_id'])){
                $clerk_id = $a['clerk_id'];
            }
            elseif(empty($a['clerk_id']) && !empty($b['clerk_id'])){
                $clerk_id = $b['clerk_id'];
            }
            elseif (!empty($a['clerk_id']) && empty($b['clerk_id'])){
                $clerk_id = $a['clerk_id'];
            }
            if(!empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
            elseif(empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $b['inviter_id'];
            }
            elseif (!empty($a['inviter_id']) && empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
        }

        //医生
        if(!empty($a['is_doctor']) && empty($b['is_doctor'])){
            $is_doctor = 1;
            $doctor_id = $a['doctor_id'];
            $doctor_status = !empty($a['doctor_status']) ? 1 : 0;
            $doctor_create_time = $a['doctor_create_time'];
            $doctor_level = $a['doctor_level'];
            $doctor_black = $a['doctor_black'];
            $fix_doctor_id = $a['fix_doctor_id'];
        }
 
        elseif(!empty($b['is_doctor']) && empty($a['is_doctor'])){
            $is_doctor = 1;
            $doctor_id = $b['doctor_id'];
            $doctor_status = !empty($b['doctor_status']) ? 1 : 0;
            $doctor_create_time = $b['doctor_create_time'];
            $doctor_level = $b['doctor_level'];
            $doctor_black = $b['doctor_black'];
            $fix_doctor_id = $b['fix_doctor_id'];
        }
  
        elseif(!empty($b['is_doctor']) && !empty($a['is_doctor'])){
           
            $doctorcompare = p('doctor')->compareLevel(array($a['doctor_level'], $b['doctor_level']));
            $is_doctor = 1;
            if($doctorcompare){
                $doctor_id = $b['doctor_id'];
                if(empty($b['doctor_id']) && !empty($a['doctor_id'])){
                    $doctor_id = $a['doctor_id'];
                }
                if ($doctor_id == $b['id'] || $doctor_id == $a['id']) {
                    $doctor_id = 0;
                }
                $doctor_status = !empty($b['doctor_status']) ? 1 : 0;
                $doctor_black = !empty($b['doctor_black']) ? 1 : 0;
                $fix_doctor_id = !empty($b['fix_doctor_id']) ? 1 : 0;
            }else{
                $doctor_id = $a['doctor_id'];
                if(empty($a['doctor_id']) && !empty($b['doctor_id'])){
                    $doctor_id = $b['doctor_id'];
                }
                if ($doctor_id == $b['id'] || $doctor_id == $a['id']) {
                    $doctor_id = 0;
                }
                $doctor_status = !empty($a['doctor_status']) ? 1 : 0;
                $doctor_black = !empty($a['doctor_black']) ? 1 : 0;
                $fix_doctor_id = !empty($a['fix_doctor_id']) ? 1 : 0;
            }
            $doctor_create_time = $doctorcompare ? $b['doctor_create_time'] : $a['doctor_create_time'];
            $doctor_level = $doctorcompare ? $b['doctor_level'] : $a['doctor_level'];
        }
        
        elseif (empty($b['is_doctor']) && empty($a['is_doctor'])){
            if(!empty($a['doctor_id']) && !empty($b['doctor_id'])){
                $doctor_id = $a['doctor_id'];
            }
            elseif(empty($a['doctor_id']) && !empty($b['doctor_id'])){
                $doctor_id = $b['doctor_id'];
            }
            elseif (!empty($a['doctor_id']) && empty($b['doctor_id'])){
                $doctor_id = $a['doctor_id'];
            }
            if(!empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
            elseif(empty($a['inviter_id']) && !empty($b['inviter_id'])){
                $inviter = $b['inviter_id'];
            }
            elseif (!empty($a['inviter_id']) && empty($b['inviter_id'])){
                $inviter = $a['inviter_id'];
            }
        }

        //创始人
        if(!empty($a['isauthor']) && empty($b['isauthor'])){
            $isauthor = $a['isauthor'];
            $authorstatus = !empty($a['authorstatus']) ? 1 : 0;
            $authortime = $a['authortime'];
            $authorlevel = $a['authorlevel'];
            $authorblack = $a['authorblack']; 
        }
        elseif(!empty($b['isauthor']) && empty($a['isauthor'])){
            $isauthor = $b['isauthor'];
            $authorstatus = !empty($b['authorstatus']) ? 1 : 0;
            $authortime = $b['authortime'];
            $authorlevel = $b['authorlevel'];
            $authorblack = $b['authorblack'];
        }
        elseif(!empty($b['isauthor']) && !empty($a['isauthor'])){
            return error(0, "此手机号已绑定另一用户(a1)<br>请联系管理员");
        }
        if(!empty($a['ispartner']) && empty($b['ispartner'])){
            $ispartner = 1;
            $partnerstatus = !empty($a['partnerstatus']) ? 1 : 0;
            $partnertime = $a['partnertime'];
            $partnerlevel = $a['partnerlevel'];
            $partnerblack = $a['partnerblack'];
        }
        elseif(!empty($b['ispartner']) && empty($a['ispartner'])){
            $ispartner = 1;
            $partnerstatus = !empty($b['partnerstatus']) ? 1 : 0;
            $partnertime = $b['partnertime'];
            $partnerlevel = $b['partnerlevel'];
            $partnerblack = $b['partnerblack'];
        }
        elseif(!empty($b['ispartner']) && !empty($a['ispartner'])){
            return error(0, "此手机号已绑定另一用户(p)<br>请联系管理员");
        }
        if(!empty($a['isaagent']) && empty($b['isaagent'])){
            $isaagent = $a['isaagent'];
            $aagentstatus = !empty($a['aagentstatus']) ? 1 : 0;
            $aagenttime = $a['aagenttime'];
            $aagentlevel = $a['aagentlevel'];
            $aagenttype = $a['aagenttype'];
            $aagentprovinces = $a['aagentprovinces'];
            $aagentcitys = $a['aagentcitys'];
            $aagentareas = $a['aagentareas'];
        }
        elseif(!empty($b['isaagent']) && empty($a['isaagent'])){
            $isaagent = $b['isaagent'];
            $aagentstatus = !empty($b['aagentstatus']) ? 1 : 0;
            $aagenttime = $b['aagenttime'];
            $aagentlevel = $b['aagentlevel'];
            $aagenttype = $b['aagenttype'];
            $aagentprovinces = $b['aagentprovinces'];
            $aagentcitys = $b['aagentcitys'];
            $aagentareas = $b['aagentareas'];
        }
        elseif(!empty($b['isaagent']) && !empty($a['isaagent'])){
            return error(0, "此手机号已绑定另一用户(a2)<br>请联系管理员");
        }

        $arr = array();
        if(isset($createtime)){
            $arr['createtime'] = $createtime;
        }
        if(isset($childtime)){
            $arr['childtime'] = $childtime;
        }
        if(isset($level)){
            $arr['level'] = $level;
        }
        if(isset($groupid)){
            $arr['groupid'] = $groupid;
        }
        if(isset($isblack)){
            $arr['isblack'] = $isblack;
        }
        if(isset($openid_qq)){
            $arr['openid_qq'] = $openid_qq;
        }
        if(isset($openid_wx)){
            $arr['openid_wx'] = $openid_wx;
        }
        if(isset($openid_wa)){
            $arr['openid_wa'] = $openid_wa;
        }
        //分销商
        if(isset($status)){
            $arr['status'] = $status;
        }
        if(isset($isagent)){
            $arr['isagent'] = $isagent;
        }
        if(isset($agentid)){
            $arr['agentid'] = $agentid;
        }
        if(isset($agenttime)){
            $arr['agenttime'] = $agenttime;
        }
        if(isset($agentlevel)){
            $arr['agentlevel'] = $agentlevel;
        }
        if(isset($agentblack)){
            $arr['agentblack'] = $agentblack;
        }
        if(isset($fixagentid)){
            $arr['fixagentid'] = $fixagentid;
        }
        //店员
        if(isset($clerk_status)){
            $arr['clerk_status'] = $clerk_status;
        }
        if(isset($is_clerk)){
            $arr['is_clerk'] = $is_clerk;
        }
        if(isset($clerk_id)){
            $arr['clerk_id'] = $clerk_id;
        }
        if(isset($clerk_create_time)){
            $arr['clerk_create_time'] = $clerk_create_time;
        }
        if(isset($clerklevel)){
            $arr['clerk_level'] = $clerklevel;
        }
        if(isset($clerk_black)){
            $arr['clerk_black'] = $clerk_black;
        }
        if(isset($fix_clerk_id)){
            $arr['fix_clerk_id'] = $fix_clerk_id;
        }

        //医生
        if(isset($doctor_status)){
            $arr['doctor_status'] = $doctor_status;
        }
        if(isset($is_doctor)){
            $arr['is_doctor'] = $is_doctor;
        }
        if(isset($doctor_id)){
            $arr['doctor_id'] = $doctor_id;
        }
        if(isset($doctor_create_time)){
            $arr['doctor_create_time'] = $doctor_create_time;
        }
        if(isset($doctor_level)){
            $arr['doctor_level'] = $doctor_level;
        }
        if(isset($doctor_black)){
            $arr['doctor_black'] = $doctor_black;
        }
        if(isset($fix_doctor_id)){
            $arr['fix_doctor_id'] = $fix_doctor_id;
        }

        
        if(isset($isauthor)){
            $arr['isauthor'] = $isauthor;
        }
        if(isset($authorstatus)){
            $arr['authorstatus'] = $authorstatus;
        }
        if(isset($authortime)){
            $arr['authortime'] = $authortime;
        }
        if(isset($authorlevel)){
            $arr['authorlevel'] = $authorlevel;
        }
        if(isset($authorblack)){
            $arr['authorblack'] = $authorblack;
        }
        if(isset($ispartner)){
            $arr['ispartner'] = $ispartner;
        }
        if(isset($partnerstatus)){
            $arr['partnerstatus'] = $partnerstatus;
        }
        if(isset($partnertime)){
            $arr['partnertime'] = $partnertime;
        }
        if(isset($partnerlevel)){
            $arr['partnerlevel'] = $partnerlevel;
        }
        if(isset($partnerblack)){
            $arr['partnerblack'] = $partnerblack;
        }
        if(isset($isaagent)){
            $arr['isaagent'] = $isaagent;
        }
        if(isset($aagentstatus)){
            $arr['aagentstatus'] = $aagentstatus;
        }
        if(isset($aagenttime)){
            $arr['aagenttime'] = $aagenttime;
        }
        if(isset($aagentlevel)){
            $arr['aagentlevel'] = $aagentlevel;
        }
        if(isset($aagenttype)){
            $arr['aagenttype'] = $aagenttype;
        }
        if(isset($aagentprovinces)){
            $arr['aagentprovinces'] = $aagentprovinces;
        }
        if(isset($aagentcitys)){
            $arr['aagentcitys'] = $aagentcitys;
        }
        if(isset($aagentareas)){
            $arr['aagentareas'] = $aagentareas;
        }
        if(isset($inviter)){
            $arr['inviter_id'] = $inviter;
        }

        if(!empty($arr) && is_array($arr)){
            pdo_update('elapp_shop_member', $arr, array('id'=>$b['id']));
        }

        pdo_update('elapp_shop_commission_apply', array('mid' => $b['id']), array('uniacid' => $_W['uniacid'], 'mid' => $a['id']));
        pdo_update('elapp_shop_clerk_apply', array('mid' => $b['id']), array('uniacid' => $_W['uniacid'], 'mid' => $a['id']));
        //todo 变更医生结算表
        //pdo_update('elapp_shop_tcmd_doctor_apply', array('mid' => $b['id']), array('uniacid' => $_W['uniacid'], 'mid' => $a['id']));

        pdo_update('elapp_shop_order',array('agentid'=>$b['id']),array('agentid'=>$a['id']));
        pdo_update('elapp_shop_order',array('clerk_id'=>$b['id']),array('clerk_id'=>$a['id']));
        pdo_update('elapp_shop_order',array('doctor_id'=>$b['id']),array('doctor_id'=>$a['id']));

        pdo_update('elapp_shop_member',array('agentid'=>$b['id']),array('agentid'=>$a['id']));
        pdo_update('elapp_shop_member',array('clerk_id'=>$b['id']),array('clerk_id'=>$a['id']));
        pdo_update('elapp_shop_member',array('doctor_id'=>$b['id']),array('doctor_id'=>$a['id']));


        $mergeinfo = ' 合并前用户: '.$a['nickname'].'('.$a['id'].') 合并后用户: '.$b['nickname'].'('.$b['id'].')';
    
        if($a['credit1']>0){
            m('member')->setCredit($b['openid'], 'credit1', abs($a['credit1']), '全网通会员数据合并增加积分 +' . $a['credit1']. $mergeinfo);
        }
        if($a['credit2']>0) {
            m('member')->setCredit($b['openid'], 'credit2', abs($a['credit2']), '全网通会员数据合并增加余额 +' . $a['credit2']. $mergeinfo);
        }
        pdo_delete('elapp_shop_member', array('id' => $a['id'], 'uniacid' => $_W['uniacid']));
        if(method_exists(m('member'),'memberRadisCountDelete')) {
            m('member')->memberRadisCountDelete();
        }
        $tables = pdo_fetchall("SHOW TABLES like '%_elapp_shop_%'");
        foreach ($tables as $k => $v) {
            $v = array_values($v);
            $tablename = str_replace($_W['config']['db']['tablepre'], '', $v[0]);
            if (pdo_fieldexists($tablename, 'openid') && pdo_fieldexists($tablename, 'uniacid')) {
                pdo_update($tablename, array('openid' => $b['openid']), array('uniacid' => $_W['uniacid'], 'openid' => $a['openid']));
            }
            if (pdo_fieldexists($tablename, 'openid') && pdo_fieldexists($tablename, 'acid')) {
                pdo_update($tablename, array('openid' => $b['openid']), array('acid' => $_W['acid'], 'openid' => $a['openid']));
            }
            if (pdo_fieldexists($tablename, 'mid') && pdo_fieldexists($tablename, 'uniacid')) {
                pdo_update($tablename, array('mid' => $b['id']), array('uniacid' => $_W['uniacid'], 'mid' => $a['id']));
            }
        }

        $c = m('member')->getMember($b['openid']);
        pdo_insert("elapp_shop_member_mergelog", array(
            'uniacid'=>$_W['uniacid'],
            'mergetime'=>time(),
            'openid_a'=>$a['openid'],
            'openid_b'=>$b['openid'],
            'mid_a'=>$a['id'],
            'mid_b'=>$b['id'],
            'detail_a'=>iserializer($a),
            'detail_b'=>iserializer($b),
            'detail_c'=>iserializer($c)
        ));

        return error(1);
    }
    public function sendCredit($member = array()) {
        if(empty($member)){
            return;
        }
        $data = m('common')->getPluginset('sale');
        if(!empty($data['bindmobile']) && intval($data['bindmobilecredit'])>0){
            m('member')->setCredit($member['openid'], 'credit1', abs($data['bindmobilecredit']), '绑定手机号送积分 +'. $data['bindmobilecredit']);
        }
    }

    public function mergeforuniacid($a=array(), $b=array()){
        global $_W;

        if(empty($a) || empty($b) || $a['id']==$b['id']){
            return error(0, "params error");
        }

        if(!empty($b['mobileverify']))
        {
            return error(0, "params error");
        }

        $createtime = $a['createtime'] > $b['createtime'] ? $b['createtime'] : $a['createtime'];
        $childtime = $a['childtime'] > $b['childtime'] ? $b['childtime'] : $a['childtime'];
        $comparelevel = m('member')->compareLevel(array($a['level'], $b['level']));
        $level = $comparelevel ? $b['level'] : $a['level'];
        $isblack = !empty($a['isblack']) || !empty($b['isblack']) ? 1 : 0;

        $openid_qq = !empty($b['openid_qq']) && empty($a['openid_qq']) ? $b['openid_qq'] : $a['openid_qq'];
        $openid_wx = !empty($b['openid_wx']) && empty($a['openid_wx']) ? $b['openid_wx'] : $a['openid_wx'];
        $openid_wa = !empty($b['openid_wa']) && empty($a['openid_wa']) ? $b['openid_wa'] : $a['openid_wa'];

        //分销商
        if(!empty($a['isagent']) && empty($b['isagent'])){
            $isagent = 1;
            $agentid = $a['agentid'];
            $status = !empty($a['status']) ? 1 : 0;
            $agenttime = $a['agenttime'];
            $agentlevel = $a['agentlevel'];
            $agentblack = $a['agentblack'];
            $fixagentid = $a['fixagentid'];
        }
    
        elseif(!empty($b['isagent']) && empty($a['isagent'])){
            $isagent = 1;
            $agentid = $b['agentid'];
            $status = !empty($b['status']) ? 1 : 0;
            $agenttime = $b['agenttime'];
            $agentlevel = $b['agentlevel'];
            $agentblack = $b['agentblack'];
            $fixagentid = $b['fixagentid'];
        }
        elseif(!empty($b['isagent']) && !empty($a['isagent'])){
            $compare = p('commission')->compareLevel(array($a['agentlevel'], $b['agentlevel']));
            $isagent = 1;
            if($compare){
                $agentid = $b['agentid'];
                if(empty($b['agentid']) && !empty($a['agentid'])){
                    $agentid = $a['agentid'];
                }
                $status = !empty($b['status']) ? 1 : 0;
                $agentblack = !empty($b['agentblack']) ? 1 : 0;
                $fixagentid = !empty($b['fixagentid']) ? 1 : 0;
            }else{
                $agentid = $a['agentid'];
                if($a['agentid'] && !empty($b['agentid'])){
                    $agentid = $b['agentid'];
                }
                $status = !empty($a['status']) ? 1 : 0;
                $agentblack = !empty($a['agentblack']) ? 1 : 0;
                $fixagentid = !empty($a['fixagentid']) ? 1 : 0;
            }
            $agenttime = $compare ? $b['agenttime'] : $a['agenttime'];
            $agentlevel = $compare ? $b['agentlevel'] : $a['agentlevel'];
        }

        //店员
        if(!empty($a['is_clerk']) && empty($b['is_clerk'])){
            $is_clerk = 1;
            $clerk_id = $a['clerk_id'];
            $clerk_status = !empty($a['clerk_status']) ? 1 : 0;
            $clerk_create_time = $a['clerk_create_time'];
            $clerklevel = $a['clerk_level'];
            $clerk_black = $a['clerk_black'];
            $fix_clerk_id = $a['fix_clerk_id'];
        }
    
        elseif(!empty($b['is_clerk']) && empty($a['is_clerk'])){
            $is_clerk = 1;
            $clerk_id = $b['clerk_id'];
            $clerk_status = !empty($b['clerk_status']) ? 1 : 0;
            $clerk_create_time = $b['clerk_create_time'];
            $clerklevel = $b['clerk_level'];
            $clerk_black = $b['clerk_black'];
            $fix_clerk_id = $b['fix_clerk_id'];
        }
        elseif(!empty($b['is_clerk']) && !empty($a['is_clerk'])){
            $clerkcompare = p('commission')->compareLevel(array($a['clerk_level'], $b['clerk_level']));
            $is_clerk = 1;
            if($clerkcompare){
                $clerk_id = $b['clerk_id'];
                if(empty($b['clerk_id']) && !empty($a['clerk_id'])){
                    $clerk_id = $a['clerk_id'];
                }
                $clerk_status = !empty($b['clerk_status']) ? 1 : 0;
                $clerk_black = !empty($b['clerk_black']) ? 1 : 0;
                $fix_clerk_id = !empty($b['fix_clerk_id']) ? 1 : 0;
            }else{
                $clerk_id = $a['clerk_id'];
                if($a['clerk_id'] && !empty($b['clerk_id'])){
                    $clerk_id = $b['clerk_id'];
                }
                $clerk_status = !empty($a['clerk_status']) ? 1 : 0;
                $clerk_black = !empty($a['clerk_black']) ? 1 : 0;
                $fix_clerk_id = !empty($a['fix_clerk_id']) ? 1 : 0;
            }
            $clerk_create_time = $clerkcompare ? $b['clerk_create_time'] : $a['clerk_create_time'];
            $clerklevel = $clerkcompare ? $b['clerk_level'] : $a['clerk_level'];
        }

        //医生
        if(!empty($a['is_doctor']) && empty($b['is_doctor'])){
            $is_doctor = 1;
            $doctor_id = $a['doctor_id'];
            $doctor_status = !empty($a['doctor_status']) ? 1 : 0;
            $doctor_create_time = $a['doctor_create_time'];
            $doctor_level = $a['doctor_level'];
            $doctor_black = $a['doctor_black'];
            $fix_doctor_id = $a['fix_doctor_id'];
        }
    
        elseif(!empty($b['is_doctor']) && empty($a['is_doctor'])){
            $is_doctor = 1;
            $doctor_id = $b['doctor_id'];
            $doctor_status = !empty($b['doctor_status']) ? 1 : 0;
            $doctor_create_time = $b['doctor_create_time'];
            $doctor_level = $b['doctor_level'];
            $doctor_black = $b['doctor_black'];
            $fix_doctor_id = $b['fix_doctor_id'];
        }
        elseif(!empty($b['is_doctor']) && !empty($a['is_doctor'])){
            $doctorcompare = p('commission')->compareLevel(array($a['doctor_level'], $b['doctor_level']));
            $is_doctor = 1;
            if($doctorcompare){
                $doctor_id = $b['doctor_id'];
                if(empty($b['doctor_id']) && !empty($a['doctor_id'])){
                    $doctor_id = $a['doctor_id'];
                }
                $doctor_status = !empty($b['doctor_status']) ? 1 : 0;
                $doctor_black = !empty($b['doctor_black']) ? 1 : 0;
                $fix_doctor_id = !empty($b['fix_doctor_id']) ? 1 : 0;
            }else{
                $doctor_id = $a['doctor_id'];
                if($a['doctor_id'] && !empty($b['doctor_id'])){
                    $doctor_id = $b['doctor_id'];
                }
                $doctor_status = !empty($a['doctor_status']) ? 1 : 0;
                $doctor_black = !empty($a['doctor_black']) ? 1 : 0;
                $fix_doctor_id = !empty($a['fix_doctor_id']) ? 1 : 0;
            }
            $doctor_create_time = $doctorcompare ? $b['doctor_create_time'] : $a['doctor_create_time'];
            $doctor_level = $doctorcompare ? $b['doctor_level'] : $a['doctor_level'];
        }

        $arr = array();
        $arr['ishb'] = 1;
        if(isset($createtime)){
            $arr['createtime'] = $createtime;
        }
        if(isset($childtime)){
            $arr['childtime'] = $childtime;
        }
        if(isset($level)){
            $arr['level'] = $level;
        }
        if(isset($groupid)){
            $arr['groupid'] = $groupid;
        }
        if(isset($isblack)){
            $arr['isblack'] = $isblack;
        }
        if(isset($openid_qq)){
            $arr['openid_qq'] = $openid_qq;
        }
        if(isset($openid_wx)){
            $arr['openid_wx'] = $openid_wx;
        }
        if(isset($openid_wa)){
            $arr['openid_wa'] = $openid_wa;
        }
        //分销商
        if(isset($status)){
            $arr['status'] = $status;
        }
        if(isset($isagent)){
            $arr['isagent'] = $isagent;
        }
        if(isset($agentid)){
            $arr['agentid'] = $agentid;
        }
        if(isset($agenttime)){
            $arr['agenttime'] = $agenttime;
        }
        if(isset($agentlevel)){
            $arr['agentlevel'] = $agentlevel;
        }
        if(isset($agentblack)){
            $arr['agentblack'] = $agentblack;
        }
        if(isset($fixagentid)){
            $arr['fixagentid'] = $fixagentid;
        }
        //店员
        if(isset($clerk_status)){
            $arr['clerk_status'] = $clerk_status;
        }
        if(isset($is_clerk)){
            $arr['is_clerk'] = $is_clerk;
        }
        if(isset($clerk_id)){
            $arr['clerk_id'] = $clerk_id;
        }
        if(isset($clerk_create_time)){
            $arr['clerk_create_time'] = $clerk_create_time;
        }
        if(isset($clerklevel)){
            $arr['clerk_level'] = $clerklevel;
        }
        if(isset($clerk_black)){
            $arr['clerk_black'] = $clerk_black;
        }
        if(isset($fix_clerk_id)){
            $arr['fix_clerk_id'] = $fix_clerk_id;
        }

        //医生
        if(isset($doctor_status)){
            $arr['doctor_status'] = $doctor_status;
        }
        if(isset($is_doctor)){
            $arr['is_doctor'] = $is_doctor;
        }
        if(isset($doctor_id)){
            $arr['doctor_id'] = $doctor_id;
        }
        if(isset($doctor_create_time)){
            $arr['doctor_create_time'] = $doctor_create_time;
        }
        if(isset($doctor_level)){
            $arr['doctor_level'] = $doctor_level;
        }
        if(isset($doctor_black)){
            $arr['doctor_black'] = $doctor_black;
        }
        if(isset($fix_doctor_id)){
            $arr['fix_doctor_id'] = $fix_doctor_id;
        }
       
        if(!empty($arr) && is_array($arr)){
            pdo_update('elapp_shop_member', $arr, array('id'=>$b['id']));
        }

        pdo_update('elapp_shop_commission_apply', array('mid' => $b['id']), array('mid' => $a['id']));
        pdo_update('elapp_shop_order',array('agentid'=>$b['id']),array('agentid'=>$a['id']));
        pdo_update('elapp_shop_member',array('agentid'=>$b['id']),array('agentid'=>$a['id']));

        pdo_update('elapp_shop_clerk_apply', array('mid' => $b['id']), array('mid' => $a['id']));
        pdo_update('elapp_shop_order',array('clerk_id'=>$b['id']),array('clerk_id'=>$a['id']));
        pdo_update('elapp_shop_member',array('clerk_id'=>$b['id']),array('clerk_id'=>$a['id']));

        //todo 更新医生结算表
        //pdo_update('elapp_shop_tcmd_doctor_apply', array('mid' => $b['id']), array('mid' => $a['id']));
        pdo_update('elapp_shop_order',array('doctor_id'=>$b['id']),array('doctor_id'=>$a['id']));
        pdo_update('elapp_shop_member',array('doctor_id'=>$b['id']),array('doctor_id'=>$a['id']));


        $mergeinfo = ' 合并前用户: '.$a['nickname'].'('.$a['id'].') 合并后用户: '.$b['nickname'].'('.$b['id'].')';
        if($a['credit1']>0){
            m('member')->setCredit($b['openid'], 'credit1', abs($a['credit1']), '数据迁移会员数据合并增加积分 +' . $a['credit1']. $mergeinfo);
        }
        if($a['credit2']>0) {
            m('member')->setCredit($b['openid'], 'credit2', abs($a['credit2']), '数据迁移会员数据合并增加余额 +' . $a['credit2']. $mergeinfo);
        }
        $tables = pdo_fetchall("SHOW TABLES like '%_elapp_shop_%'");
        foreach ($tables as $k => $v) {
            $v = array_values($v);
            $tablename = str_replace($_W['config']['db']['tablepre'], '', $v[0]);
            if (pdo_fieldexists($tablename, 'openid') && pdo_fieldexists($tablename, 'uniacid')) {
                if($tablename !='elapp_shop_member')
                {
                    pdo_update($tablename, array('openid' => $b['openid']), array('uniacid' => $_W['uniacid'], 'openid' => $a['openid']));
                }
            }
            if (pdo_fieldexists($tablename, 'openid') && pdo_fieldexists($tablename, 'acid')) {
                pdo_update($tablename, array('openid' => $b['openid']), array('acid' => $_W['acid'], 'openid' => $a['openid']));
            }
            if (pdo_fieldexists($tablename, 'mid') && pdo_fieldexists($tablename, 'uniacid')) {
                pdo_update($tablename, array('mid' => $b['id']), array('uniacid' => $_W['uniacid'], 'mid' => $a['id']));
            }
        }

        $c = m('member')->getMember($b['openid']);
        pdo_insert("elapp_shop_member_mergelog", array(
            'uniacid'=>$_W['uniacid'],
            'fromuniacid'=>$_W['uniacid'],
            'mergetime'=>time(),
            'openid_a'=>$a['openid'],
            'openid_b'=>$b['openid'],
            'mid_a'=>$a['id'],
            'mid_b'=>$b['id'],
            'detail_a'=>iserializer($a),
            'detail_b'=>iserializer($b),
            'detail_c'=>iserializer($c)
        ));

        return error(1);
    }

}
