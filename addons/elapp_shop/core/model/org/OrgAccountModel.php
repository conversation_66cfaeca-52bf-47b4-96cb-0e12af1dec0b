<?php

namespace app\core\model\org;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 集团子账户模型
 * class OrgAccountModel
 */
class OrgAccountModel extends MicroEngineModel
{
    protected $name = 'org_account';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'default'=>'id,openid,mid,org_id,orgname,status,isfounder,mobile',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function member() {
        return $this->belongsTo(MemberModel::class, 'mid', 'id')
            ->field(MemberModel::scene_fields('default'));
    }

    public function org() {
        return $this->belongsTo(OrgUserModel::class, 'org_id', 'id')
            ->field(OrgUserModel::scene_fields('default'));
    }
}