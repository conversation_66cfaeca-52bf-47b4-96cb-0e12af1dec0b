<?php

namespace app\core\model\org;

use app\core\model\AttrTrait\GetLogoAttrTrait;
use app\core\model\AttrTrait\GetPluginsetAttrTrait;
use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 集团用户模型
 * class OrgUserModel
 */
class OrgUserModel extends MicroEngineModel
{
    protected $name = 'org_user';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function orgAccounts() {
        return $this->hasMany(OrgAccountModel::class, 'org_id', 'id');
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'mid')
            ->field(MemberModel::scene_fields('default'));
    }

    // 获取器开始
    use GetLogoAttrTrait;
    use GetPluginsetAttrTrait;
    public function getConfigAttr($value)
    {
        return $value? json_decode($value, true) : '';
    }
    // 获取器结束

}