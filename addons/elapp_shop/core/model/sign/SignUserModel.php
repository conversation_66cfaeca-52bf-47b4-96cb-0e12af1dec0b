<?php

namespace app\core\model\sign;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;
use think\model\relation\HasOne;

/**
 * 积分签到记录 模型类
 * class SignUserModel
 * @package app\core\model\sign
 * <AUTHOR>
 * @date 2024/07/08 21:07
 */
class SignUserModel extends MicroEngineModel
{
    protected $name = 'sign_user';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'openid',
                'member_id',
                'order',
                'orderday',
                'sum',
                'signdate',
                'comefrom',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
    /**
     * 一对一关联会员表
     * @return HasOne
     */
    public function member(): HasOne
    {
        return $this->hasOne(MemberModel::class, 'id', 'member_id')->field(MemberModel::scene_fields('default'));
    }
}