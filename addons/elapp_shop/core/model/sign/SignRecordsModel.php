<?php

namespace app\core\model\sign;

use app\core\model\MicroEngineModel;

/**
 * 积分签到奖励记录 模型类
 * class SignRecordsModel
 * @package app\core\model\sign
 * <AUTHOR>
 * @date 2024/07/08 21:07
 */
class SignRecordsModel extends MicroEngineModel
{
    protected $name = 'sign_records';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'create_time',
                'openid',
                'member_id',
                'credit',
                'log',
                'type',
                'day',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }
}