<?php

namespace app\core\model\sign;

use app\core\model\MicroEngineModel;

/**
 * 积分签到配置 模型类
 * class SignSetModel
 * @package app\core\model\sign
 * <AUTHOR>
 * @date 2024/07/08 21:07
 */
class SignSetModel extends MicroEngineModel
{
    protected $name = 'sign_set';
    protected $pk = 'id';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'uniacid',
                'iscenter',
                'iscreditshop',
                'keyword',
                'title',
                'thumb',
                'desc',
                'isopen',
                'signold',
                'signold_price',
                'signold_type',
                'textsign',
                'textsignold',
                'textsigned',
                'textsignforget',
                'maincolor',
                'cycle',
                'reward_default_first',
                'reward_default_day',
                'reword_order',
                'reword_sum',
                'reword_special',
                'sign_rule',
                'share'
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    // 获取器开始
    public function getRewordOrderAttr($value)
    {
        return unserialize($value);
    }
    public function getSignRuleAttr($value)
    {
        return htmlspecialchars_decode(unserialize($value));
    }
    // 获取器结束
}