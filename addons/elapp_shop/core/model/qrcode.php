<?php
namespace app\model;
use app\framework\library\qrcode\QRcode;

class QrcodeModel{
	/**
     * 商城二维码
     * @global type $_W
     * @param type $mid
     * @return string
     */
	public function createShopQrcode($mid = 0, $posterid = 0){
		global $_W, $_GPC;
//		$path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/';
//		if (!is_dir($path)) {
//			load()->func('file');
//			mkdirs($path);
//		}

		$url = mobileUrl('', array('mid' => $mid), true);

		if (!empty($posterid)) {
			$url .= '&posterid=' . $posterid;
		}

		$file = 'shop_qrcode_' . $posterid . '_' . $mid . '.png';
//		$qrcode_file = $path . $file;
        $paths = create_qrimg_path($file, 'qrcode');
        $qrcode_file = $paths["file"];

		if (!is_file($qrcode_file)) {
//			require_once IA_ROOT . '/extend/framework/library/qrcode/phpqrcode.php';
			QRcode::png($url, $qrcode_file, QR_ECLEVEL_L, 4);
		}

//		return $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/' . $file;
        return $paths["url"];
	}

	/**
     * 产品二维码
     * 支持商品、会员卡
     * @param int $mid
     * @param int $goodsid
     * @param int $posterid
     * @param int $type 0 商品 1 会员卡
     * @return string
     *@global $_W，$_GPC
     */
	public function createGoodsQrcode(int $mid = 0, int $goodsid = 0, int $posterid = 0, int $type = 0)
	{
		global $_W, $_GPC;
//		$path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'];
//		if (!is_dir($path)) {
//			load()->func('file');
//			mkdirs($path);
//		}

		$url = mobileUrl('goods/detail', array('id' => $goodsid, 'mid' => $mid), true);
        if ($type == 1) {
            $url = mobileUrl('membercard/detail/main', array('id' => $goodsid,'k' => 0, 'type' => 'all', 'mid' => $mid), true);
        }

		if (!empty($posterid)) {
			$url .= '&posterid=' . $posterid;
		}

		$file = (!empty($type) ? 'cards_qrcode_' : 'goods_qrcode_') . $posterid . '_' . $mid . '_' . $goodsid . '.png';
//		$qrcode_file = $path . '/' . $file;
        $paths = create_qrimg_path($file, 'qrcode');
        $qrcode_file = $paths["file"];

		if (!is_file($qrcode_file)) {
//			require_once IA_ROOT . '/extend/framework/library/qrcode/phpqrcode.php';
			QRcode::png($url, $qrcode_file, QR_ECLEVEL_L, 4);
		}

//		return $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/' . $file;
        return $paths["url"];
	}

	//文章二维码
	public function createArcicleQrcode($mid = 0, $aid = 0, $posterid = 0){
		global $_W, $_GPC;
//		$path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'];
//		if (!is_dir($path)) {
//			load()->func('file');
//			mkdirs($path);
//		}

		$url = mobileUrl('article/index/main', array('aid' => $aid, 'mid' => $mid), true);

		if (!empty($posterid)) {
			$url .= '&posterid=' . $posterid;
		}

		$file = 'article_qrcode_' . $posterid . '_' . $mid . '_' . $aid . '.png';
//		$qrcode_file = $path . '/' . $file;
        $paths = create_qrimg_path($file, 'qrcode');
        $qrcode_file = $paths["file"];

		if (!is_file($qrcode_file)) {
			QRcode::png($url, $qrcode_file, QR_ECLEVEL_L, 4);
		}

//		return $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/' . $file;
        return $paths["url"];
	}

	public function createQrcode($url)
	{
		global $_W, $_GPC;
//		$path = DATA_ROOT . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/';
//		if (!is_dir($path)) {
//			load()->func('file');
//			mkdirs($path);
//		}

		$file = md5(base64_encode($url)) . '.jpg';
//		$qrcode_file = $path . $file;
        $paths = create_qrimg_path($file, 'qrcode');
        $qrcode_file = $paths["file"];

		if (!is_file($qrcode_file)) {
//			require_once IA_ROOT . '/extend/framework/library/qrcode/phpqrcode.php';
			QRcode::png($url, $qrcode_file, QR_ECLEVEL_L, 4);
		}

//		return $_W['siteroot'] . "data/application/" . ELAPP_SHOP_MODULE_NAME . '/qrcode/' . $_W['uniacid'] . '/' . $file;
        return $paths["url"];
	}
}
