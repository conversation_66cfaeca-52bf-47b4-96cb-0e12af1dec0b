<?php

namespace app\core\model\copartner;

use app\core\model\AttrTrait\GetLogoAttrTrait;
use app\core\model\MicroEngineModel;
use app\core\model\org\OrgUserModel;
use app\model\MemberModel;

class CopartnerUserModel extends MicroEngineModel
{
    protected $name = 'copartner_user';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'config' => 'id,relate,money',
            'default'=>'id,uniacid,mid,mcnname,comname,logo,mentor_id,is_mentor,levelid,create_at,settle_money,org_id,channelid',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function copartnerAccounts() {
        return $this->hasMany(CopartnerAccountModel::class, 'copartner_id', 'id');
    }

    /**
     * 招商经理或者子帐号
     * @return \think\model\relation\HasMany
     */
    public function subAccounts() {
        return $this->hasMany(CopartnerAccountModel::class, 'copartner_id', 'id')
            ->where(['isfounder'=>0,'del_at'=>0,'status'=>1]);
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'mid')
            ->field(MemberModel::scene_fields('default'));
    }

    function org() {
        return $this->belongsTo(OrgUserModel::class, 'org_id', 'id');
    }


    function channel() {
        return $this->belongsTo(CopartnerChannelModel::class, 'channelid', 'id');
    }

    /**
     * 推广的店长
     * @return \think\model\relation\HasMany
     */
    function subClerks()
    {
        return $this->hasMany(MemberModel::class, 'copartner_id', 'id')
            ->where(['is_clerk'=>1,'clerk_status'=>1])
            ->field(MemberModel::scene_fields('detail'));
    }

    /**
     * 推广的会员
     * @return \think\model\relation\HasMany
     */
    function subMembers()
    {
        return $this->hasMany(MemberModel::class, 'copartner_id', 'id')
            ->field(MemberModel::scene_fields('detail'));
    }

    // 获取器开始
    use GetLogoAttrTrait;
    public function getMoneyAttr($value) {
        return json_decode($value, true);
    }
    public function getRelateAttr($value) {
        return json_decode($value, true);
    }
    // 获取器结束
}