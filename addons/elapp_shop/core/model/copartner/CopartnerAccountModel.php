<?php

namespace app\core\model\copartner;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;

class CopartnerAccountModel extends MicroEngineModel
{
    protected $name = 'copartner_account';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'default'=>'id,openid,mid,copartner_id,username,realname,status,isfounder,mobile',
        ];
        return $map[$scene] ?? $map['default'];
    }

    public function member() {
        return $this->belongsTo(MemberModel::class, 'mid', 'id')
            ->field(MemberModel::scene_fields('default'));
    }

    public function copartner() {
        return $this->belongsTo(CopartnerUserModel::class, 'copartner_id', 'id')
            ->field(CopartnerUserModel::scene_fields('default'));
    }
}