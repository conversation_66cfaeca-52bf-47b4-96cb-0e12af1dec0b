<?php

namespace app\core\model\copartner;

use app\core\model\MicroEngineModel;
use app\model\MemberModel;

/**
 * 合伙人注册模型
 * class CopartnerRegModel
 */
class CopartnerRegModel extends MicroEngineModel
{
    protected $name = 'copartner_reg';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> null,
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

    /**
     * 一对一关联会员
     * @return \think\model\relation\HasOne
     */
    function member()
    {
        return $this->hasOne(MemberModel::class, 'id', 'mid')
            ->field(MemberModel::scene_fields('default'));
    }
}