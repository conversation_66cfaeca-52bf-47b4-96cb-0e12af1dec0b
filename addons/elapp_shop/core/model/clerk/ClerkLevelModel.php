<?php

namespace app\core\model\clerk;

use app\core\model\MicroEngineModel;

class ClerkLevelModel extends MicroEngineModel
{
    protected $name = 'clerk_level';

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> 'id,levelname,level',
            'default'=>'id,levelname,level',
        ];
        return $map[$scene] ?? $map['default'];
    }
}