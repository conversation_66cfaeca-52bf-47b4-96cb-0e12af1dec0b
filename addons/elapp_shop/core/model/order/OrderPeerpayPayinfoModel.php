<?php

namespace app\core\model\order;

use app\BaseModel;

/**
 * 订单代付支付信息
 * @package app\core\model\order
 */
class OrderPeerpayPayinfoModel extends BaseModel
{
    protected $name = 'order_peerpay_payinfo';

    protected $id = 'id';

    protected $insert = ['createtime'];

    protected $createTime = 'createtime';

    protected $autoWriteTimestamp = 'int';

    protected function setCreatetimeAttr()
    {
        return time();
    }

    public function getCreatetimeAttr($value)
    {
        if (!empty($value)) {
            return date('Y-m-d H:i:s', (int)$value);
        }
        return '--';
    }
}