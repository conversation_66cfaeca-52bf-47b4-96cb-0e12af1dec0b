<?php

namespace app\core\model\order;

use app\core\model\MicroEngineModel;

/**
 * 订单代付
 * @package app\core\model\order
 */
class OrderPeerpayModel extends MicroEngineModel
{
    protected $name = 'order_peerpay';

    protected $pk = 'id';

    protected $insert = ['createtime'];

    protected $createTime = 'createtime';

    protected $autoWriteTimestamp = 'int';

    protected function setCreatetimeAttr()
    {
        return time();
    }

    public function getCreatetimeAttr($value)
    {
        if (!empty($value)) {
            return date('Y-m-d H:i:s', (int)$value);
        }
        return '--';
    }


}