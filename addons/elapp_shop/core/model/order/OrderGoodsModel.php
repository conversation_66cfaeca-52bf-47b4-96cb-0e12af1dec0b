<?php
namespace app\model;

use app\core\model\MicroEngineModel;
use ReflectionClass;
use think\facade\Db;

class OrderGoodsModel extends MicroEngineModel {
    protected $name = 'order_goods';
    protected $pk = 'id';

    /**
     * @desc 与goodsModel对象多对一相应关联
     * @return \think\model\relation\BelongsTo
     * <AUTHOR>
     */
    public function goods()
    {
        return $this->belongsTo(GoodsModel::class, 'goodsid', 'id');
    }

    // 根据不同场景返回不同字段
    public static function scene_fields($scene = 'list')
    {
        $map = [
            'list'=> [
                'id',
                'orderid',
                'goodsid',
                'price',
                'total',
                'optionid',
                'createtime create_time',
                'optionname',
                'single_refundid',
                'single_refundstate',
                'single_refundtime',
            ],
            'default'=>'*',
        ];
        return $map[$scene] ?? $map['default'];
    }

	/**
     * 计算订单中商品累计赠送的积分
     * @param type $order
     */
	public function getGoodsCredit1($goods)	{
		$credit1 = 0;
		$gcredit = trim($goods['credit']);

		if (!empty($gcredit)) {
			if (strexists($gcredit, '%')) {
				$credit1 += intval(floatval(str_replace('%', '', $gcredit)) / 100 * $goods['realprice']);
			}
			else {
				$credit1 += intval($goods['credit']) * $goods['total'];
			}
		}

		return $credit1;
	}

	/**
     * 计算订单中商品累计赠送的余额
     * @param type $order
     */
	public function getGoodsCredit2($goods){
		$credit2 = 0;
		$gbalance = trim($goods['money']);

		if (!empty($gbalance)) {
			if (strexists($gbalance, '%')) {
				$credit2 += round(floatval(str_replace('%', '', $gbalance)) / 100 * $goods['realprice'], 2);
			}
			else {
				$credit2 += round($goods['money'], 2) * $goods['total'];
			}
		}

		return $credit2;
	}

	/**
     * //处理订单库存情况(单品退换货)
     * @param type $goods
     */
	public function setStock($goods){
		global $_W;

		if (!empty($goods['optionid'])) {
			$option = m('goods')->getOption($goods['goodsid'], $goods['optionid']);
			if (!empty($option) && $option['stock'] != -1) {
				$stock = $option['stock'] + $goods['total'];

				if (!empty($stock)) {
					pdo_update('elapp_shop_goods_option', array('stock' => $stock), array('uniacid' => $_W['uniacid'], 'goodsid' => $goods['goodsid'], 'id' => $goods['optionid']));
				}
			}
		}

		$totalstock = $goods['goodstotal'] + $goods['total'];

		if (!empty($totalstock)) {
			pdo_update('elapp_shop_goods', array('stock' => $totalstock), array('uniacid' => $_W['uniacid'], 'id' => $goods['goodsid']));
		}
	}

	public function canReceiveByCheckNotReceiveDays($order, $goods = []) {
		global $_W;
		if (!is_array($order) && is_int($order)) {
			$order = pdo_get('elapp_shop_order',['id'=>$order], ['id','createtime']);
		}

		if (!isset($order['createtime'])) {
			$order = pdo_get('elapp_shop_order',['id'=>$order['id']], ['id','createtime']);
		}

		if (empty($order)) {
			return false;
		}

		$orderid = $order['id'];

		if (empty($goods)) {
			$param = ['uniacid'=> $_W['uniacid'],':orderid'=>$orderid];
			$goods = pdo_fetchall("select g.not_receive_days from " . tablename('elapp_shop_order_goods') . " og "
				. " left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid "
				. " where og.orderid = :orderid and og.uniacid=:uniacid ", $param);
		}

		// 获取goods里面not_receive_days最大的值
		$not_receive_days = 0;
		foreach ($goods as $g) {
			if($g['not_receive_days'] > $not_receive_days) {
				$not_receive_days = $g['not_receive_days'];
			}
		}
		// 如果商品有限制签收时间，那么则判断当前订单是否超过限定时间，超过则可签收，否则不可签收
		if ($not_receive_days > 0) {
			// 判断当前时间是否超过创建时间N天内不可签收
			$can_receive = (time() - $order['createtime']) > (86400 * $not_receive_days);
		} else {
			$can_receive = true;
		}
		return $can_receive;
	}

	public function canRefundByCheckNotReceiveDays($order, $goods = []) {
        // todo yh 这个要求取消了
        return true;
		global $_W;

		if (!is_array($order) && is_int($order)) {
			$order = pdo_get('elapp_shop_order',['id'=>$order], ['id','createtime']);
		}

		if (!isset($order['createtime'])) {
			$order = pdo_get('elapp_shop_order',['id'=>$order['id']], ['id','createtime']);
		}

		if (empty($order)) {
			return false;
		}

		$orderid = $order['id'];

		if (empty($goods)) {
			$param = ['uniacid'=> $_W['uniacid'],':orderid'=>$orderid];
			$goods = pdo_fetchall("select g.not_receive_days from " . tablename('elapp_shop_order_goods') . " og "
				. " left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid "
				. " where og.orderid = :orderid and og.uniacid=:uniacid ", $param);
		}

		// 获取goods里面not_receive_days最大的值
		$not_receive_days = 0;
		foreach ($goods as $g) {
			if($g['not_receive_days'] > $not_receive_days) {
				$not_receive_days = $g['not_receive_days'];
			}
		}
		// 如果商品有限制签收时间，那么则判断当前订单是否超过限定时间，超过则可签收，否则不可签收
		if ($not_receive_days > 0) {
			// 判断当前时间是否超过创建时间N天内不可签收
			$can_receive = (time() - $order['createtime']) > (86400 * $not_receive_days);
		} else {
			$can_receive = true;
		}
		return $can_receive;
	}

    /**
     * @desc 查询订单超库存的商品
     * @param int $orderid
     * @return mixed
     */
    static function overStockGoods(int $orderid)
    {
        return Db::name('order_goods')
            ->alias('o')
            ->field('o.orderid, o.goodsid, o.total, g.shorttitle,
             CASE 
                WHEN o.optionid = 0 THEN g.stock
                ELSE go.stock
             END AS stock')
            ->leftJoin('ims_elapp_shop_goods g', 'o.goodsid = g.id AND o.optionid = 0')
            ->leftJoin('ims_elapp_shop_goods_option go', 'o.goodsid = go.goodsid AND o.optionid = go.id')
            ->where(function ($query) {
                $query->where('(o.optionid = 0 AND o.total > g.stock)')
                    ->whereOr('(o.optionid <> 0 AND o.total > go.stock)');
            })
            ->where('o.orderid', $orderid)
            ->select()->toArray();
    }

    // 获取器开始

    public function getClerkCommission1Attr($value)
    {
        return iunserializer($value);
    }
    public function getClerkCommission2Attr($value)
    {
        return iunserializer($value);
    }
    public function getClerkCommission3Attr($value)
    {
        return iunserializer($value);
    }
    public function getClerkCommissionsAttr($value)
    {
        return iunserializer($value);
    }

    public function getcopartnerCommission1Attr($value)
    {
        return iunserializer($value);
    }
    public function getcopartnerCommission2Attr($value)
    {
        return iunserializer($value);
    }
    public function getcopartnerCommission3Attr($value)
    {
        return iunserializer($value);
    }
    public function getcopartnerCommissionsAttr($value)
    {
        return iunserializer($value);
    }
    public function getMentorCommissionAttr($value)
    {
        return iunserializer($value);
    }
    public function getMentorCommissionsAttr($value)
    {
        return iunserializer($value);
    }
    public function getDoctorCommissionAttr($value)
    {
        return iunserializer($value);
    }
    public function getDoctorCommissionsAttr($value)
    {
        return iunserializer($value);
    }
    public function getCommission1Attr($value)
    {
        return iunserializer($value);
    }
    public function getCommission2Attr($value)
    {
        return iunserializer($value);
    }
    public function getCommission3Attr($value)
    {
        return iunserializer($value);
    }
    public function getCommissionsAttr($value)
    {
        return iunserializer($value);
    }
    public function getConsumeAttr($value)
    {
        return iunserializer($value);
    }

    // 获取器结束

}


