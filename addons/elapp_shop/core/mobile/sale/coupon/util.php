<?php
namespace app\controller\sale\coupon;
use app\controller\MobilePage;

class UtilController extends MobilePage {
	public function query()	{
		global $_W, $_GPC;
		$type = $_GPC['type'] ?? null;
		$money = floatval($_GPC['money']);
		$merchs = $_GPC['merchs'];
		$goods = $_GPC['goods'];
		$openid  = $_W['openid'] ?? 0;
		$uniacid = $_W['uniacid'] ?? 0;
        $list = [];
        $list2 = [];
        // 判断 $type 是数组还是单个整数
        if (is_array($type)) {
            // 遍历 $type 数组并处理每个类型
            foreach ($type as $t) {
                $t = intval($t);
                if ($t == 0 || $t == 3) {
                    // 消费券 或者 资格券
                    $list = array_merge($list, com_run('coupon::getAvailableCoupons', $t, 0, $merchs, $goods, $openid, $uniacid));
                } elseif ($t == 1) {
                    $list = array_merge($list, com_run('coupon::getAvailableCoupons', $t, $money, $merchs));
                }
            }
            unset($t);
        } else {
            // 处理单个整数
            $type = intval($type);
            if ($type == 0) {
                $list = com_run('coupon::getAvailableCoupons', $type, 0, $merchs, $goods, $openid, $uniacid);
            } elseif ($type == 1) {
                $list = com_run('coupon::getAvailableCoupons', $type, $money, $merchs);
            }
        }
        // 这里获取微信会员卡功能，导致会员卡获取很慢 。 平台下单没用到
        // com_run('wxcard::getAvailableWxcards', $type, 0, $merchs, $goods, $openid, $uniacid);
		show_json(1, array('coupons' => $list, 'wxcards' => $list2));
	}

	public function picker(){
		include $this->template('mobile/sale/coupon/util/picker');
	}
}