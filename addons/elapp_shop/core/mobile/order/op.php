<?php
namespace app\controller\order;
use app\controller\activity\Action;
use app\controller\activity\OrderSignOffData;
use app\controller\MobileLoginPage;
use app\model\ActivityModel;

class OpController extends MobileLoginPage
{

    /**
     * 取消订单
     * @global type $_W
     * @global type $_GPC
     */
    public function cancel()
    {
        global $_W, $_GPC;
        global $_S;
        $orderid = intval($_GPC['id']);
        $peerid = intval($_GPC['id']);
        $uniacid = $_W['uniacid'];
        $refundid = intval($_GPC['refundid']);
        $order = pdo_fetch('select id,ordersn,openid,status,deductcredit,deductcredit2,deductprice,couponid,isparent,`virtual`,`virtual_info`,merchid from ' . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1', array(':id' => $orderid, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
        if (empty($refundid)) {
            $refundid = $order['refundid'];
        }
        if (!empty($refundid)) {
            $refund = pdo_fetch('select * from ' . tablename('elapp_shop_order_refund') . ' where id=:id limit 1', array(':id' => $refundid));
        }
        $peerpay = pdo_fetch('select p.*,o.openid from ' . tablename('elapp_shop_order_peerpay') . ' p join ' . tablename('elapp_shop_order') . ' o on o.id=p.orderid where p.orderid=:id and p.uniacid=:uniacid limit 1', array(':id' => $peerid, ':uniacid' => $uniacid));
        $peerpay_info = pdo_fetchall('select * from ' . tablename('elapp_shop_order_peerpay_payinfo') . ' where pid=:pid', array(':pid' => $peerpay['id']));
        $peerpay_info1 = (double)pdo_fetchcolumn('select SUM(price) price from ' . tablename('elapp_shop_order_peerpay_payinfo') . ' where pid=:pid limit 1', array(':pid' => $peerpay['id']));
        $rate_price = round($peerpay['peerpay_realprice'] - $peerpay_info1, 2);
        if (0 < $rate_price) {
            $shopset = $_S['shop'];
            foreach ($peerpay_info as $v) {
                if ($v['paytype'] == 1) {
                    m('member')->setCredit($v['openid'], 'credit2', $v['price'], array(0, $shopset['name'] . ('退款: ' . $v['price'] . '元 代付订单号: ') . $order['ordersn']));
                } else {
                    if ($v['paytype'] == 21) {
                        m('finance')->refund($v['openid'], $v['tid'], $refund['refundno'] . $v['id'], $v['price'] * 100, $v['price'] * 100, !empty($order['apppay']) ? true : false);
                    }
                }
            }
        }
        if (empty($order)) {
            show_json(0, '订单未找到');
        }
        if (0 < $order['status']) {
            show_json(0, '订单已支付，不能取消!');
        }
        if ($order['status'] < 0) {
            show_json(0, '订单已经取消!');
        }
        if (!empty($order['virtual']) && ($order['virtual'] != 0)) {
            $goodsid = pdo_fetch('SELECT goodsid FROM ' . tablename('elapp_shop_order_goods') . ' WHERE uniacid = ' . $_W['uniacid'] . ' AND orderid = ' . $order['id']);
            $typeid = $order['virtual'];
            $vkdata = ltrim($order['virtual_info'], '[');
            $vkdata = rtrim($vkdata, ']');
            $arr = explode('}', $vkdata);
            foreach ($arr as $k => $v) {
                if (!$v) {
                    unset($arr[$k]);
                }
            }
            $vkeynum = count($arr);
            //未付款卡密变为未使用
            pdo_query('update ' . tablename('elapp_shop_virtual_data') . ' set openid="",usetime=0,orderid=0,ordersn="",price=0,merchid=0 where typeid=' . intval($typeid) . ' and orderid = ' . $order['id']);
            //模板减少使用数据
            pdo_query('update ' . tablename('elapp_shop_virtual_type') . ' set usedata=usedata-' . $vkeynum . ' where id=' . intval($typeid));
        }
        //返还抵扣积分
        if (0 < $order['deductprice']) {
            m('member')->setCredit($order['openid'], 'credit1', $order['deductcredit'], array('0', $_W['shopset']['shop']['name'] . '购物返还抵扣积分 积分: ' . $order['deductcredit'] . ' 抵扣金额: ' . $order['deductprice'] . ' 订单号: ' . $order['ordersn']));
        }
        //返还抵扣余额
        if (0 < $order['deductcredit2']) {
            m('member')->setCredit($order['openid'], 'credit2', $order['deductcredit2'], array('0', $_W['shopset']['shop']['name'] . ('购物返还抵扣余额 余额: ' . $order['deductcredit2'] . ' 订单号: ' . $order['ordersn'])));
        }
        //m('order')->setDeductCredit2($order);
        //处理订单库存及用户积分情况(赠送积分)
        m('order')->setStocksAndCredits($orderid, 2);
        //退还优惠券 退还之前先检测
        if (com('coupon') && !empty($order['couponid'])) {
            //检测当前优惠券有没有使用过
            $plugincoupon = com('coupon');
            if ($plugincoupon) {
                /* $coupondata=  $plugincoupon->getCouponByDataID($order['couponid']);
                 if($coupondata['used']!=1){
                     com('coupon')->returnConsumeCoupon($orderid); //手机关闭订单
                 }*/
                $plugincoupon->returnConsumeCoupon($orderid);
            }
        }
        pdo_update('elapp_shop_order', array('status' => -1, 'canceltime' => time(), 'closereason' => trim($_GPC['remark'])), array('id' => $order['id'], 'uniacid' => $_W['uniacid']));
        if (!empty($order['isparent'])) {
            pdo_update('elapp_shop_order', array('status' => -1, 'canceltime' => time(), 'closereason' => trim($_GPC['remark'])), array('parentid' => $order['id'], 'uniacid' => $_W['uniacid']));
        }
        //模板消息
        m('notice')->sendOrderMessage($orderid);
        show_json(1);
    }

    /**
     * 确认收货
     * @global type $_W
     * @global type $_GPC
     */
    public function finish()
    {
        global $_W, $_GPC;
        $orderid = intval($_GPC['id']);
        $order = pdo_get('elapp_shop_order', ['id' => $orderid]);
        // 判断是否存在限制签收时间内的商品
        $can = m('orderGoods')->canReceiveByCheckNotReceiveDays($orderid);
        /** @var ActivityModel $activityPlugin */
        $activityPlugin = p('activity');
        if ($activityPlugin && $order['activity_id'] == 1) {
            // 如果商品参与活动且满足条件,或超过天数，允许签收
            $can = $can || $activityPlugin->orderCanReceive($order);
        }
        if (!$can) {
            show_json(0, '目前订单暂时不能确认收货!');
        }
        pdo_begin();

        //单品退换货，确认收货后取消维权
        pdo_update('elapp_shop_order_goods', array('single_refundstate' => 0), array('orderid' => $orderid, 'uniacid' => $_W['uniacid']));
        // 如果存在单品退换货, 取消维权
        $single_refund_goods = pdo_getall('elapp_shop_order_goods', array('orderid' => $orderid, 'uniacid' => $_W['uniacid'], 'single_refundid >' => 0));
        if (!empty($single_refund_goods)) {
            foreach ($single_refund_goods as $single_refund_goods_item) {
                pdo_update('elapp_shop_order_goods', array('single_refundid' => 0), array('id' => $single_refund_goods_item['id']));
                pdo_update('elapp_shop_order_single_refund', array('status' => -2, 'refundtime' => time()), array('id' => $single_refund_goods_item['single_refundid'], 'uniacid' => $_W['uniacid'], 'status' => 0));
            }
        }
        $order = pdo_fetch('select id,status,openid,couponid,refundstate,refundid,ordersn,price,onmid from ' . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1', array(':id' => $orderid, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
        if (empty($order)) {
            show_json(0, '订单未找到');
        }
        if ($order['status'] != 2) {
            show_json(0, '订单不能确认收货');
        }
        if ((0 < $order['refundstate']) && !empty($order['refundid'])) {
            $change_refund = array();
            $change_refund['status'] = -2;
            $change_refund['refundtime'] = time();
            pdo_update('elapp_shop_order_refund', $change_refund, array('id' => $order['refundid'], 'uniacid' => $_W['uniacid']));
        }

        pdo_update('elapp_shop_order', array('status' => 3, 'finishtime' => time(), 'refundstate' => 0), array('id' => $order['id'], 'uniacid' => $_W['uniacid']));
        //处理积分
        m('order')->setStocksAndCredits($orderid, 3, true);

        //商品全返
        m('order')->fullback($orderid);
        //show_json(0, $res);
        //会员升级
        $memberSetting = m('common')->getSysset('member');
        if ((int)$memberSetting['upgrade_condition'] === 1 || empty($memberSetting['upgrade_condition'])) {
            m('member')->upgradeLevel($order['openid'], $orderid);
        }
        //余额赠送
        m('order')->setGiveBalance($orderid, 1);
        //发送赠送优惠券
        if (com('coupon')) {
            $refurnid = com('coupon')->sendcouponsbytask($orderid); //订单支付
        }
        //优惠券返利
        if (com('coupon') && !empty($order['couponid'])) {
            com('coupon')->backConsumeCoupon($orderid); //手机收货
        }
        //模板消息
        m('notice')->sendOrderMessage($orderid);
        //打印机打印
        com_run('printer::sendOrderMessage', $orderid);
        //排队全返
        if (p('lineup')) {
            p('lineup')->checkOrder($order);
        }

        // 活动签收
        $activityPlugin = new ActivityModel();
        if ($activityPlugin) {
            $activityPlugin->hook(Action::ORDER_SIGNOFF, new OrderSignOffData($orderid));
        }

        //分销检测
        if (p('commission')) {
            p('commission')->checkOrderFinish($orderid);
        }
        //虚店店长检测
        if (p('vrshop')) {
            p('vrshop')->checkOrderFinish($orderid);
        }
        //虚店店员检测
        if (p('clerk')) {
            p('clerk')->checkOrderFinish($orderid);
        }
        //虚店合伙人检测
        if (p('copartner')) {
            p('copartner')->checkOrderFinish($orderid);
        }
        //扶植分红检测
        if (p('mentor')) {
            p('mentor')->checkOrderFinish($order['id']);
        }
        //医生检测
        if (p('doctor')) {
            p('doctor')->checkOrderFinish($orderid);
        }
        //抽奖
        if (p('lottery')) {
            //type 1:消费 2:签到 3:任务 4:其他
            $res = p('lottery')->getLottery($_W['openid'], 1, array('money' => $order['price'], 'paytype' => 2));
            if ($res) {
                p('lottery')->getLotteryList($_W['openid'], array('lottery_id' => $res));
            }
        }
        // 任务中心
        if (p('task')) {
            p('task')->checkTaskProgress($order['price'], 'order_full', '', $order['openid']);
        }
        if (p('userpromote')) {
            p('userpromote')->setCredits($order['onmid'], $orderid);//分享积分
            p('userpromote')->shopSetcredits($orderid);//消费积分
        }

        pdo_commit();
        show_json(1, array('url' => mobileUrl('order', array('status' => 3))));
    }

    /**
     * 删除或恢复订单
     * @global type $_W
     * @global type $_GPC
     */
    public function delete()
    {
        global $_W, $_GPC;
        //删除订单
        $orderid = intval($_GPC['id']);
        $userdeleted = intval($_GPC['userdeleted']);
        $order = pdo_fetch('select id,status,refundstate,refundid from ' . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1', array(':id' => $orderid, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
        if (empty($order)) {
            show_json(0, '订单未找到!');
        }
        if ($userdeleted == 0) {
            if ($order['status'] != 3) {
                show_json(0, '无法恢复');
            }
        } else {
            if (($order['status'] != 3) && ($order['status'] != -1)) {
                show_json(0, '无法删除');
            }
            if ((0 < $order['refundstate']) && !empty($order['refundid'])) {
                $change_refund = array();
                $change_refund['status'] = -2;
                $change_refund['refundtime'] = time();
                pdo_update('elapp_shop_order_refund', $change_refund, array('id' => $order['refundid'], 'uniacid' => $_W['uniacid']));
            }
        }
        pdo_update('elapp_shop_order', array('userdeleted' => $userdeleted, 'refundstate' => 0), array('id' => $order['id'], 'uniacid' => $_W['uniacid']));
        show_json(1);
    }
}