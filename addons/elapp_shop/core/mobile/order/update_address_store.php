<?php
namespace app\controller\order;
use app\controller\MobileLoginPage;

class UpdateAddressStoreController extends MobileLoginPage
{
    function main()
    {
        global $_W, $_GPC;
        $openid = $_W['openid'];
        $uniacid = $_W['uniacid'];
        $member = m('member')->getMember($openid, true);
        $member["carrier_mobile"] = (empty($member["carrier_mobile"]) ? $member["mobile"] : $member["carrier_mobile"]);
        extract($this->globalData());
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $address_street = intval($area_set['address_street']);

        //多商户
        $merchdata = $this->merchData();
        extract($merchdata);
        $merch_array = array();
        $merchs = array();
        $merch_id = 0;
        $total_array = array();

        $is_openmerch = 1;

        //处理补充收件地址
        $stores = array(); //核销门店
        $address = false; //默认地址
        $carrier = false; //自提地点
        $carrier_list = array(); //自提点
        $dispatch_list = false;
        $dispatch_price = 0; //邮费

        $ismerch = 0;
        $carrier_list = pdo_fetchall("select * from " . tablename("elapp_shop_store") . " where  uniacid=:uniacid and status=1 and type in(1,3) order by displayorder desc,id desc", array(":uniacid" => $_W["uniacid"]));

        //错误代码 0 正常 1 未找到商品
        $errcode = 0;
        //是否为核销单
        $isverify = false;
        //是否强制核销选择门店
        $isforceverifystore = false;
        //是否为虚拟物品(虚拟或卡密)
        $isvirtual = false;
        //是否是虚拟物品自动发货
        $isvirtualsend = false;
        //是否为纯记次时商品订单
        $isonlyverifygoods = true;

        $order = pdo_fetch("select * from " . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1'
            , array(':id' => $orderid, ':uniacid' => $uniacid, ':openid' => $openid));

        $param = array();
        $param[':uniacid'] = $_W['uniacid'];
        $param[':orderid'] = $orderid;
        $scondition = " og.orderid=:orderid";
        $goodsid_array =array();
        $merch_dif = array();
        foreach ($goods as $k => &$g) {
            $merch_dif[] = $g['merchid'];
            $merch_dif = array_flip(array_flip(($merch_dif)));

            //备注多商户商品有BUG缺少$g['merchid']>0判断
            if ($is_openmerch == 1 && $g['merchid'] > 0) {
                $merchid = $g['merchid'];
                $merch_array[$merchid]['goods'][] = $g['goodsid'];
            }

            if ($g['isverify'] == 2) {
                //记次时商品
                $isverify = true;
            }
            if ($g['isforceverifystore']) {
                $isforceverifystore = true;
            }
            if (!empty($g['virtual']) || $g['type'] == 2 || $g['type'] == 3 || $g['type'] == 20) {
                //虚拟商品
                $isvirtual = true;
                //是否虚拟物品自动发货
                if ($g['virtualsend']) {
                    $isvirtualsend = true;
                }
                if ($g['type'] == 3) {
                    $isvirtualsend = true;
                }
            }
            //判断是否为纯记次时商品订单
            if ($g['type'] != 5) {
                $isonlyverifygoods = false;
            }
        }
        unset($g);

        if ($is_openmerch == 1) {
            //读取多商户营销设置
            foreach ($merch_array as $key => $value) {
                if ($key > 0) {
                    $merch_id = 1;
                    $merch_array[$key]['set'] = $merch_plugin->getSet('sale', $key);
                    $merch_array[$key]['enoughs'] = $merch_plugin->getEnoughs($merch_array[$key]['set']);
                }
            }
        }



        //判断是否可修改地址
        if ($order['status'] == 2 && $order['price'] == $order['dispatchprice']) {
            $canreturns = 1;
        }

        //多商户
        $ismerch = 0;
        if ($is_openmerch == 1) {
            if (!empty($merch_array)) {
                if (count($merch_array) > 1) {
                    $ismerch = 1;
                }
            }
        }
        //虚拟 或 卡密 或 不同多商户的商品 不读取自提点
        if (!$isverify && !$isvirtual && !$ismerch) {
            if ($merch_id > 0) {
                $carrier_list = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where  uniacid=:uniacid and merchid=:merchid and status=1 and type in(1,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => 1));
            } else {
                $carrier_list = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where  uniacid=:uniacid and status=1 and type in(1,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
            }
        }
        if ($isverify) {
            //核销单 所有核销门店
            $storeids = array();
            $merchid = 0;
            foreach ($goods as $g) {
                $merchid = $g['merchid'];
                if (!empty($g['storeids'])) {
                    $storeids = array_merge(explode(',', $g['storeids']), $storeids);
                }
            }
            if (empty($storeids)) {
                //门店加入支持核销的判断
                if ($merchid > 0) {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where  uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $merchid));
                } else {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where  uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            } else {
                if ($merchid > 0) {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where id in (' . implode(',', $storeids) . ') and uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $merchid));
                } else {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where id in (' . implode(',', $storeids) . ') and uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            }
        } else {
            //默认地址
            $address = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where openid=:openid and deleted=0 and isdefault=1  and uniacid=:uniacid limit 1'
                , array(':uniacid' => $uniacid, ':openid' => $openid));

            if (!empty($carrier_list)) {
                $carrier = $carrier_list[0];
            }
            //实体物品计算运费
            if (!$isvirtual && !$isonlyverifygoods) {
                $dispatch_array = m('order')->getOrderDispatchPrice($goods, $member, $address, $saleset, $merch_array, 0);
                $dispatch_price = $dispatch_array['dispatch_price'] - $dispatch_array['seckill_dispatch_price'];
                $seckill_dispatchprice = $dispatch_array['seckill_dispatch_price'];
            }
        }

        //多商户
        if ($is_openmerch == 1) {
            $merchs = $merch_plugin->getMerchs($merch_array);
        }

        //订单已选的门店
        $store = false;
        if (!empty($order['storeid'])) {
            if ($merchid > 0) {
                $store = pdo_fetch('select * from  ' . tablename('elapp_shop_merch_store') . ' where id=:id limit 1', array(':id' => $order['storeid']));
            } else {
                $store = pdo_fetch('select * from  ' . tablename('elapp_shop_store') . ' where id=:id limit 1', array(':id' => $order['storeid']));
            }
        }
        //订单信息
        $createInfo = array(
            'orderid' => $orderid,
            'addressid' => !empty($address) && !$isverify && !$isvirtual ? $address['id'] : 0,
            'storeid' => 0,
            'new_area' => $new_area,
            'address_street' => $address_street,
            'city_express_state' => empty($dispatch_array['city_express_state']) ? 0 : $dispatch_array['city_express_state'],
            'isvirtual' => $isvirtual,
            'isverify' => $isverify,
            'isonlyverifygoods' => $isonlyverifygoods,
            'isforceverifystore' => $isforceverifystore,
            'merchs' => $merchs,
            'dispatchid' => $dispatchid,
        );

        $trade = m('common')->getSysset('trade', $_W['uniacid']);
        include $this->template();
    }

    protected function globalData()
    {
        global $_W, $_GPC;
        $uniacid = $_W['uniacid'];
        $openid = $_W['openid'];
        $orderid = intval($_GPC['id']);

        $order = pdo_fetch("select id,status,activity_id,price,refundid,goodsprice,dispatchprice,deductprice,deductcredit2,finishtime,isverify,`virtual`,refundstate,merchid,random_code,iscycelbuy,paytype,sub_account_status,activity_id from " . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1', array(':id' => $orderid, ':uniacid' => $uniacid, ':openid' => $openid));

        $goods = pdo_fetchall("select g.*,og.goodsid, og.price, og.total, og.optionname from" . tablename("elapp_shop_order_goods") . " og left join " . tablename("elapp_shop_goods") . " g on g.id=og.goodsid where og.orderid=" . $order['id']);
        $_err = '';
        if ($order['status'] >= 3) {
            $_err = '订单已完成，无需修改地址!';
        }
        if (!empty($_err)) {
            if ($_W['isajax']) {
                show_json(0, $_err);
            } else {
                $this->message($_err, '', 'error');
            }
        }
        //订单不能修改地址
        /*********************************************************************/

        return array(
            'uniacid' => $uniacid,
            'openid' => $_W['openid'],
            'orderid' => $orderid,
            'order' => $order,
            'goods' => $goods
        );
    }

    function submit()
    {
        global $_W, $_GPC;
        $openid = $_W['openid'];
        $uniacid = $_W['uniacid'];
        extract($this->globalData());
        $orderid = intval($_GPC['id']);

        //多商户
        $merchdata = $this->merchData();
        extract($merchdata);
        $merch_array = array();
        $ismerch = 0;
        $discountprice_array = array();

        $dispatchid = intval($_GPC['dispatchid']);//运费模板ID
        //配送方式
        $dispatchtype = intval($_GPC['dispatchtype']);
        //自提地点
        $carrierid = intval($_GPC['carrierid']);

        $goodsid = @array_column($goods, 'goodsid');

        $isvirtual = false;
        $isverify = false;
        //是否为纯记次时商品订单
        $isonlyverifygoods = true;
        $isendtime = 0;
        $endtime = 0;
        $verifytype = 0; //核销类型
        $isvirtualsend = false;

        //地址
        $addressid = intval($_GPC['addressid']);
        $address = false;
        if (!empty($addressid) && $dispatchtype == 0) {
            $address = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where id=:id and openid=:openid and uniacid=:uniacid   limit 1'
                , array(':uniacid' => $uniacid, ':openid' => $openid, ':id' => $addressid));
        }

        foreach ($goods as $g) {
            //判断是不是核销
            if ($g['isverify'] == 2 && $g['type'] != 3) {
                $isverify = true;
                $verifytype = $g['verifytype'];
                $isendtime = $g['isendtime'];
                if ($isendtime == 0) {
                    if ($g['usetime'] > 0) {
                        $endtime = time() + 3600 * 24 * intval($g['usetime']);
                    } else {
                        $endtime = 0;
                    }
                } else {
                    $endtime = $g['endtime'];
                }
            }
            if (!empty($g['virtual']) || $g['type'] == 2 || $g['type'] == 3 || $g['type'] == 20) {
                $isvirtual = true;
                if ($g['virtualsend']) {
                    $isvirtualsend = true;
                }
            }
        }

        //地址
        if (!empty($addressid)) {
            if (empty($address)) {
                show_json(0, '未找到地址');
            } else {
                if (empty($address['province']) || empty($address['city'])) {
                    show_json(0, '地址请选择省市信息');
                }
                //新地址库判断
                $area_set = m('util')->get_area_config_set();
                $new_area = intval($area_set['new_area']);
                if (!empty($new_area)) {
                    if (empty($address['datavalue']) || trim($address['datavalue'] == 'null null null')) {
                        //如果新地址库，code为空的情况
                        show_json(-1, '地址库信息已升级，需要您重新编辑保存您的地址');
                    }
                }
            }
        }
        //生成核销消费码
        $verifyinfo = array();
        $verifycode = "";
        $verifycodes = array();
        if (($isverify || $dispatchtype)) {
            if ($isverify) {
                if ($verifytype == 0 || $verifytype == 1) {
                    //一次核销+ 按次核销（一个码 )
                    $verifycode = random(8, true);
                    while (1) {
                        $count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where verifycode=:verifycode and uniacid=:uniacid limit 1', array(':verifycode' => $verifycode, ':uniacid' => $_W['uniacid']));
                        if ($count <= 0) {
                            break;
                        }
                        $verifycode = random(8, true);
                    }
                } elseif ($verifytype == 2) {
                    //按码核销
                    $totaltimes = intval($allgoods[0]['total']);
                    if ($totaltimes <= 0) {
                        $totaltimes = 1;
                    }
                    for ($i = 1; $i <= $totaltimes; $i++) {
                        $verifycode = random(8, true);
                        while (1) {
                            $count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where concat(verifycodes,\'|\' + verifycode +\'|\' ) like :verifycodes and uniacid=:uniacid limit 1', array(':verifycodes' => "%{$verifycode}%", ':uniacid' => $_W['uniacid']));
                            if ($count <= 0) {
                                break;
                            }
                            $verifycode = random(8, true);
                        }
                        $verifycodes[] = "|" . $verifycode . "|";
                        $verifyinfo[] = array(
                            'verifycode' => $verifycode,
                            'verifyopenid' => '',
                            'verifytime' => 0,
                            'verifystoreid' => 0
                        );
                    }
                }
            } elseif ($dispatchtype) {
                //自提码
                $verifycode = random(8, true);
                while (1) {
                    $count = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order') . ' where verifycode=:verifycode and uniacid=:uniacid limit 1', array(':verifycode' => $verifycode, ':uniacid' => $_W['uniacid']));
                    if ($count <= 0) {
                        break;
                    }
                    $verifycode = random(8, true);
                }
            }
        }
        $carrier = $_GPC['carriers'];
        $carriers = is_array($carrier) ? iserializer($carrier) : iserializer(array());


        pdo_begin();
        // todo 这里存在创建订单是会有一个订单或多个订单的字段 以isparent区分
        $address_updata = array(
            'storeid' => $carrierid,
            'carrier' => $carriers,
            'dispatchtype' => $dispatchtype,
            'dispatchid' => $dispatchid,
            'addressid' => empty($dispatchtype) ? $addressid : 0,
            'address' => $address?iserializer($address):'',
            'isverify' => $isverify ? 1 : 0,
            'verifytype' => $verifytype,
            'verifyendtime' => $endtime,
            'verifycode' => $verifycode,
            'verifycodes' => implode('', $verifycodes),
            'verifyinfo' => iserializer($verifyinfo),
            'virtual' => $virtualid,
            'isvirtual' => $isvirtual ? 1 : 0,
            'isvirtualsend' => $isvirtualsend ? 1 : 0,
            'city_express_state' => empty($dispatch_array['city_express_state']) ? 0 : $dispatch_array['city_express_state'],
        );

        if ($orderid) {
            pdo_update('elapp_shop_order', $address_updata, array('id' => $orderid, 'uniacid' => $uniacid));
        }

        pdo_commit();
        show_json(1);
    }

    //订单统一模板

    protected function merchData()
    {
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        return array(
            'is_openmerch' => $is_openmerch,
            'merch_plugin' => $merch_plugin,
            'merch_data' => $merch_data
        );
    }
}