<?php
namespace app\controller\order;
use app\controller\activity\Action;
use app\controller\activity\OrderRefundApplyData;
use app\controller\activity\OrderRefundData;
use app\controller\MobileLoginPage;
use app\model\ActivityModel;

class RefundController extends MobileLoginPage
{
    function main()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        if ($order['status'] == 2 && $order['price'] == $order['dispatchprice']) {
            $canreturns = 1;
        }
        if ($order['status'] == '-1')
            $this->message('请不要重复提交!', '', 'error');
        $refund = false;
        $imgnum = 0;
        if ($order['refundstate'] > 0) {
            if (!empty($refundid)) {
                $refund = pdo_fetch("select * from " . tablename('elapp_shop_order_refund') . ' where id=:id and uniacid=:uniacid and orderid=:orderid limit 1'
                    , array(':id' => $refundid, ':uniacid' => $uniacid, ':orderid' => $orderid));
                if (!empty($refund['refundaddress'])) {
                    $refund['refundaddress'] = iunserializer($refund['refundaddress']);
                }
            }
            if (!empty($refund['imgs'])) {
                $refund['imgs'] = iunserializer($refund['imgs']);
            }
        }
        if (empty($refund)) {
            $show_price = round($order['refundprice'], 2);
        } else {
            $show_price = round($refund['applyprice'], 2);
        }
        if ($order["sub_account_status"] == 1) {
            $isSubAccount = false;
        } else {
            $isSubAccount = true;
        }
        $express_list = m('express')->getExpressList();
        $trade = m('common')->getSysset('trade', $_W['uniacid']);
        include $this->template();
    }

    protected function globalData()
    {
        global $_W, $_GPC;
        $uniacid = $_W['uniacid'];
        $openid = $_W['openid'];
        $orderid = intval($_GPC['id']);
        $singleRefund = pdo_fetch("select * from " . tablename("elapp_shop_order_single_refund") . " where orderid=:orderid and uniacid=:uniacid  and status in (0,1) limit 1", array(":orderid" => $orderid, ":uniacid" => $uniacid));
        if (!empty($singleRefund)) {
            show_message("订单已单品维权");
        }
        $order = pdo_fetch("select id,status,activity_id,price,refundid,goodsprice,dispatchprice,deductprice,deductcredit2,finishtime,isverify,`virtual`,refundstate,merchid,random_code,iscycelbuy,paytype,sub_account_status,activity_id from " . tablename('elapp_shop_order') . ' where id=:id and uniacid=:uniacid and openid=:openid limit 1', array(':id' => $orderid, ':uniacid' => $uniacid, ':openid' => $openid));
        $orderprice = $order['price'];
        // 如订单参与活动且存在限制签收时间内，则不允许单品退换
        if ($order['activity_id'] == 1) {
            $can_single_refund = m('orderGoods')->canRefundByCheckNotReceiveDays($order);
            if (!$can_single_refund) {
                show_message('活动中商品不可退换!');
            }
        }
        if ($order['iscycelbuy'] == 1) {
            //查询分期订单下面是否存在有开始的周期商品
            $order_goods = pdo_fetch("select * from " . tablename('elapp_shop_cycelbuy_periods') . "where orderid = {$order['id']} and status != 0");
            if (!empty($order_goods)) {
                show_json(0, '订单已经开始，无法进行退款');
            }
        }
        $refund = pdo_fetch("select * from " . tablename('elapp_shop_order_refund') . " where uniacid = :uniacid and orderid = :orderid and status > 0 limit 1 ", array(':uniacid' => $uniacid, ':orderid' => $orderid));
        if (!empty($refund) && $refund['rtype'] == 0) {
            if ($_W['isajax']) {
                show_json(0, '此订单已完成维权，不能申请退款');
            } else {
                show_message('此订单已完成维权，不能申请退款','','error',$this);
            }
        }
        if (empty($order)) {
            if (!$_W['isajax']) {
                header('location: ' . mobileUrl('order'));
                exit;
            } else {
                show_json(0, '订单未找到');
            }
        }
        $_err = '';
        if ($order['status'] <= 0) {
            $_err = '订单未付款或已关闭，不能申请退款!';
        } else {
            if ($order['status'] == 3) {
                if (!empty($order['virtual']) || $order['isverify'] == 1) {
                    $_err = '此订单不允许退款!';
                } else {
                    if ($order['refundstate'] == 0) {
                        //申请退款
                        $tradeset = m('common')->getSysset('trade');
                        $refunddays = intval($tradeset['refunddays']);
                        if ($refunddays > 0) {
                            $days = intval((time() - $order['finishtime']) / 3600 / 24);
                            if ($days > $refunddays && $order['activity_id']==0) {
                                $_err = '订单完成已超过 ' . $refunddays . ' 天, 无法发起退款申请!';
                            }
                        } else {
                            $_err = '订单完成, 无法申请退款!';
                        }
                    }
                }
            }
        }
        if (!empty($_err)) {
            if ($_W['isajax']) {
                show_json(0, $_err);
            } else {
                $this->message($_err, '', 'error');
            }
        }
        //订单不能退货商品
        /*********************************************************************/
        $order['cannotrefund'] = true;
        $refundgoods = array(
            'refund' => true,
            'returngoods' => true,
            'exchange' => true,
        );
        if ($order['status'] >= 1) {
            $goods = pdo_fetchall("select og.goodsid, og.price, og.total, og.optionname, g.cannotrefund,g.refund,g.returngoods,g.exchange,g.type, g.thumb, g.title,g.isfullback from" . tablename("elapp_shop_order_goods") . " og left join " . tablename("elapp_shop_goods") . " g on g.id=og.goodsid where og.orderid=" . $order['id']);
            if (!empty($goods)) {
                foreach ($goods as $g) {
                    /*
                     * 退款优化
                     * */
                    if (empty($g['cannotrefund'])) {
                        $g['refund'] = true;
                        $g['returngoods'] = true;
                        $g['exchange'] = true;
                    }
                    if ($order['status'] >= 2) {
                        /*
                         * 退款优化
                         * */
                        if (!empty($g['cannotrefund']) && empty($g['refund']) && empty($g['returngoods']) && empty($g['exchange'])) {
                            $order['cannotrefund'] = false;
                        }
                    }
                    if ($order['status'] == 1) {
                        /*
                         * 退款优化
                         * */
                        if (!empty($g['cannotrefund']) && empty($g['refund'])) {
                            $order['cannotrefund'] = false;
                        }
                    }
                    //虚拟商品完成订单
                    if ($order['status'] >= 3 && $g['type'] == 2) {
                        $g['returngoods'] = false;
                        $g['exchange'] = false;
                    }
                    $refundgoods['refund'] = empty($refundgoods['refund']) ? false : $g['refund'];
                    $refundgoods['returngoods'] = empty($refundgoods['returngoods']) ? false : $g['returngoods'];
                    $refundgoods['exchange'] = empty($refundgoods['exchange']) ? false : $g['exchange'];
                }
            }
        }
        if ($order['cannotrefund'] && empty($refundgoods['refund']) && empty($refundgoods['returngoods']) && empty($refundgoods['exchange'])) {
            $this->message("此订单不可退换货");
        }
        //是否全返商品，并检测是否允许退款
        $fullback_log = pdo_fetchall("select * from " . tablename('elapp_shop_fullback_log') . " where orderid = " . $orderid . " and uniacid = " . $uniacid . " ");
        $fullbackkprice = 0;
        if ($fullback_log && is_array($fullback_log)) {
            foreach ($fullback_log as $key => $value) {
                $fullbackgoods = pdo_fetch("select refund from " . tablename('elapp_shop_fullback_goods') . " where goodsid = " . $value['goodsid'] . " and uniacid = " . $uniacid . " ");
                if ($fullbackgoods['refund'] == 0) {
                    $this->message("此订单包含全返产品不允许退款");
                }
            }
            foreach ($fullback_log as $k => $val) {
                if ($val['fullbackday'] > 0) {
                    if ($val['fullbackday'] < $val['day']) {
                        $fullbackkprice += $val['priceevery'] * $val['fullbackday'];
                    } else {
                        $fullbackkprice += $val['price'];
                    }
                }
            }
        }
        $order['price'] = $order['price'] - $fullbackkprice;
        //应该退的钱 在线支付的+积分抵扣的+余额抵扣的(运费包含在在线支付或余额里）
        $order['refundprice'] = $order['price'] + $order['deductcredit2'];
        if ($order['status'] >= 2) {
            //如果发货，扣除运费
            $order['refundprice'] -= $order['dispatchprice'];
        }
        $order['refundprice'] = round($order['refundprice'], 2);
        return array(
            'uniacid' => $uniacid,
            'refundgoods' => $refundgoods,
            'openid' => $_W['openid'],
            'orderid' => $orderid,
            'order' => $order,
            'refundid' => $order['refundid'],
            'fullback_log' => $fullback_log,
            'fullbackgoods' => $fullbackgoods,
            'orderprice' => $orderprice
        );
    }

    function submit()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        if ($order['status'] == '-1')
            show_json(0, '订单已经处理完毕!');
        if ($order['paytype'] == 11) {
            show_json(0, '后台付款订单不允许售后');
        }
        $price = trim($_GPC['price']);
        $rtype = intval($_GPC['rtype']);
        if ($rtype != 2) {
            if (empty($price) && $order['deductprice'] == 0) {
                show_json(0, '退款金额不能为0元');
            }
            if ($price > $order['refundprice']) {
                show_json(0, '退款金额不能超过' . $order['refundprice'] . '元');
            }
        }
        pdo_begin();
        //全返退款，退款退货
        if (($rtype == 0 || $rtype == 1) && $order['status'] >= 3) {
            // if(($fullback_log['price']>=$orderprice || $fullbackgoods['refund'] == 0) && $fullback_log){
            //     show_json(0, "此订单不可退款");
            // }
            //全返管理停止
            if ($fullback_log) {
                m('order')->fullbackstop($orderid);
            }
        }
        $refund = array(
            'uniacid' => $uniacid,
            'merchid' => $order['merchid'],
            'applyprice' => $price,
            'rtype' => $rtype,
            'reason' => trim($_GPC['reason']),
            'content' => trim($_GPC['content']),
            'imgs' => iserializer($_GPC['images']),
            'price' => $price,
        );
        if ($refund['rtype'] == 2) {
            $refundstate = 2;
        } else {
            $refundstate = 1;
        }
        if ($order['refundstate'] == 0) {
            //新建一条退款申请
            $refund['createtime'] = time();
            $refund['orderid'] = $orderid;
            $refund['orderprice'] = $order['refundprice'];
            $refund['refundno'] = m('common')->createNO('order_refund', 'refundno', 'SR');
            pdo_insert('elapp_shop_order_refund', $refund);
            $refundid = pdo_insertid();
            pdo_update('elapp_shop_order', array('refundid' => $refundid, 'refundstate' => $refundstate), array('id' => $orderid, 'uniacid' => $uniacid));

            // 调用活动插件
            if ($order['activity_id'] != 0) {
                /** @var ActivityModel $activity */
                $activity = p('activity');
                $activity->hook(Action::ORDER_REFUND_APPLY, new OrderRefundApplyData($order['id']));
            }

        } else {
            //修改退款申请
            pdo_update('elapp_shop_order', array('refundstate' => $refundstate), array('id' => $orderid, 'uniacid' => $uniacid));
            pdo_update('elapp_shop_order_refund', $refund, array('id' => $refundid, 'uniacid' => $uniacid));
        }

        //模板消息
        m('notice')->sendOrderMessage($orderid, true);
        pdo_commit();
        show_json(1);
    }

    //取消
    function cancel()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        pdo_begin();
        $refund = false;
        if ($order['refundstate'] > 0) {
            if (!empty($refundid)) {
                $refund = pdo_fetch("select * from " . tablename('elapp_shop_order_refund') . ' where id=:id and uniacid=:uniacid and orderid=:orderid limit 1'
                    , array(':id' => $refundid, ':uniacid' => $uniacid, ':orderid' => $orderid));
            }
        }
        $change_refund = array();
        $change_refund['status'] = -2;
        $change_refund['refundtime'] = time();
        pdo_update('elapp_shop_order_refund', $change_refund, array('id' => $refundid, 'uniacid' => $uniacid));
        pdo_update('elapp_shop_order', array('refundstate' => 0), array('id' => $orderid, 'uniacid' => $uniacid));
        
        // 调用活动插件
        if ($order['activity_id'] != 0 && $refund['status'] == 0) {
            /** @var ActivityModel $activity */
            $activity = p('activity');
            $activity->hook(Action::ORDER_REFUND, new OrderRefundData($order['id'], OrderRefundData::REFUND_CANCEL));
        }
        pdo_commit();
        show_json(1);
    }

    //填写快递单号
    function express()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        if (empty($refundid)) {
            show_json(0, '参数错误!');
        }
        if (empty($_GPC['expresssn'])) {
            show_json(0, '请填写快递单号');
        }
        $refund = array(
            'status' => 4,
            'express' => trim($_GPC['express']),
            'expresscom' => trim($_GPC['expresscom']),
            'expresssn' => trim($_GPC['expresssn']),
            'sendtime' => time()
        );
        pdo_update('elapp_shop_order_refund', $refund, array('id' => $refundid, 'uniacid' => $uniacid));
        show_json(1);
    }

    //收到换货商品
    function receive()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        $refundid = intval($_GPC['refundid']);
        $refund = pdo_fetch("select * from " . tablename('elapp_shop_order_refund') . ' where id=:id and uniacid=:uniacid and orderid=:orderid limit 1'
            , array(':id' => $refundid, ':uniacid' => $uniacid, ':orderid' => $orderid));
        if (empty($refund)) {
            show_json(0, '换货申请未找到!');
        }
        $time = time();
        $refund_data = array();
        $refund_data['status'] = 1;
        $refund_data['refundtime'] = $time;
        pdo_update('elapp_shop_order_refund', $refund_data, array('id' => $refundid, 'uniacid' => $uniacid));
        $order_data = array();
        $order_data['refundstate'] = 0;
        $order_data['status'] = 3;
        $order_data['refundtime'] = $time;
        $order_data['finishtime'] = $time;
        pdo_update('elapp_shop_order', $order_data, array('id' => $orderid, 'uniacid' => $uniacid));
        show_json(1);
    }

    //查询商家重新发货快递
    function refundexpress()
    {
        global $_W, $_GPC;
        extract($this->globalData());
        $express = trim($_GPC['express']);
        $expresssn = trim($_GPC['expresssn']);
        $expresscom = trim($_GPC['expresscom']);
        $expresslist = m('util')->getExpressList($express, $expresssn);
        include $this->template('order/refundexpress');
    }
}