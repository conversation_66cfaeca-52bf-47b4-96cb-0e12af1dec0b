<?php

namespace app\order\logic;

use app\com\enum\order\OrderEnum;
use app\com\logic\MemberLogic;
use app\com\service\wechat\MiniProgramService;
use app\common\logic\BaseLogic;
use app\core\com\enum\payment\PayTypeEnum;
use app\core\com\logic\goods\GoodsLogic;
use app\core\com\logic\goods\GoodsOptionLogic;
use app\core\dao\order\OrderDao;
use app\model\ExpressModel;
use app\model\GoodsModel;
use app\model\MemberAddressModel;
use app\model\MemberModel;
use app\model\OrgModel;
use app\traits\LogicTrait;
use think\db\exception\DataNotFoundException;
use think\db\exception\DbException;
use think\db\exception\ModelNotFoundException;

/**
 * H5端订单业务逻辑
 * Class OrderLogic
 * @package app\order\logic
 * <AUTHOR> <<EMAIL>>
 * @date 2024/1/29 15:13
 */
class OrderLogic extends BaseLogic
{
    use LogicTrait;
    function __construct(OrderDao $dao)
    {
        $this->dao = $dao;
    }
    /**
     * 查询会员是否购买过某商品
     * 根据会员ID和商品ID获取订单信息
     * @param $memberId
     * @param $goodsId
     * @param int $order_status
     * @param int $time
     * @param bool $asOrder
     * @return array
     */
    public function checkMemberGoodsOrder($memberId, $goodsId, int $order_status, int $time, bool $asOrder = false): array
    {
        global $_W;
        $member = m('member')->getMember($memberId);
        $openid = $member['openid'];
        $time = empty($time) ? time() : $time;
        $goods = pdo_fetchall('select id,title,thumb,marketprice from' . tablename('elapp_shop_goods') . ' where id=:id and uniacid=:uniacid ', array('id' => $goodsId, ':uniacid' => $_W['uniacid']));

        $sql = 'SELECT COUNT(*) FROM ' . tablename('elapp_shop_order_goods') . ' og '
            . 'LEFT JOIN ' . tablename('elapp_shop_order') . ' o ON o.id = og.orderid '
            . 'WHERE og.goodsid=:goodsid '
            . 'AND o.openid = :openid '
            . 'AND o.status >= :order_status '
            . 'AND og.createtime <= :time';
        $params = array(':goodsid' => $goodsId, ':openid' => $openid, ':order_status' => $order_status, ':time' => $time);

        $goodscount = pdo_fetchcolumn($sql, $params);
        if (0 < $goodscount && $asOrder) {
            return ['success' => true, 'order' => $this->getOrderInfoByGoodsId($goodsId, $openid, $order_status, $time)];
        }
        return 0 < $goodscount ? ['success' => true] : ['success' => false];
    }
    /**
     * 根据商品ID获取订单信息。
     *
     * @param int $goodsid 商品ID。
     * @param string $openid 用户openid。
     * @param int $order_status 订单状态。
     * @param int $time 时间戳。
     * @return array 包含订单信息的数组。
     */
    public function getOrderInfoByGoodsId(int $goodsid, string $openid, int $order_status, int $time): array
    {
        $sql = 'SELECT o.*, og.*, og.orderid AS id FROM ' . tablename('elapp_shop_order_goods') . ' og '
            . 'LEFT JOIN ' . tablename('elapp_shop_order') . ' o ON o.id = og.orderid '
            . 'WHERE og.goodsid=:goodsid '
            . 'AND o.openid = :openid '
            . 'AND o.status >= :order_status '
            . 'AND og.createtime <= :time';
        $params = array(':goodsid' => $goodsid, ':openid' => $openid, ':order_status' => $order_status, ':time' => $time);

        return pdo_fetchall($sql, $params);
    }

    /**
     * 三第梦退款用户处理
     * 1. 删除合伙人数据 copartner_user, copartner_reg, copartner_account
     * 2. member.is_clerk = 0, member.is_copartner=0，会员等级=0
     * 3. 删除会员卡数据
     * 4. 删除自定义属性
     * delete from ims_elapp_shop_diyattrs_data where type=0 and object_id=用户id
     * delete from ims_elapp_shop_diyattrs_data where type=1 and object_id=合伙人id
     * 5. 删除虚拟积分
     * delete from ims_elapp_shop_virtual_point_copartner_set where copartner_id = 合伙人id
     */
    /**
     * @param int $member_id
     * @param int $copartner_id
     * @param string $openid
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     */
    function delMemberEquity(int $member_id, int $copartner_id, string $openid)
    {
        try {
            //1.删除虚拟积分
            pdo_delete('elapp_shop_virtual_point_copartner_set', ['copartner_id' => $copartner_id]);
            //2.删除自定义属性
            pdo_delete('elapp_shop_diyattrs_data', ['type' => 0, 'object_id' => $member_id]);
            pdo_delete('elapp_shop_diyattrs_data', ['type' => 1, 'object_id' => $copartner_id]);
            //3. 删除会员卡数据
            pdo_delete('elapp_shop_member_card_order', ['member_id' => $member_id]);
            //4. 变更会员身份状态
            MemberModel::where(['id' => $member_id])->update(['is_clerk' => 0, 'clerk_status' => 0, 'is_copartner' => 0, 'copartner_status' => 0, 'level' => 0]);
            //5. 变更合伙人相关数据状态
            pdo_update('elapp_shop_copartner_user', ['status' => 0], ['id' => $copartner_id]);
            pdo_update('elapp_shop_copartner_reg', ['status' => 0], ['openid' => $openid]);
            pdo_update('elapp_shop_copartner_account', ['status' => 0], ['copartner_id' => $copartner_id]);
            return result(0, '操作成功');
        } catch (Exception $e) {
            return result(-1, $e->getMessage());
        }
    }

    function getOrderList(array $where, array $field = ['*'], array $with = [], string $order = 'id DESC')
    {
        [$page, $limit] = $this->getPageValue();
        $data = $this->dao->getOrderList($where, $field, $page, $limit, $with, $order);
        $count = $this->dao->count($where);
        return compact('data', 'count');
    }

    /**
     * 获取(单条)订单（详情）
     * @param string $key
     * @param string $field
     * @param array $with
     * @return array|false|float|int|mixed|\Services_JSON_Error|string
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function getOrderDetail(string $key, string $field = '*', array $with = [])
    {
        return $this->dao->getOrderDetail($key, $field, array_merge(['orderGoods'], $with));
    }

    public function getOrderInfoByOrderId($orderId) {

        $order = $this->getModel()->with(['orderGoods.goods'])->find(['id' => $orderId]);
        return $order;
    }

    /**
     * 支付成功
     * @param array $orderInfo
     * @param string $paytype
     * @return bool
     */
    public function paySuccess(array $orderInfo, string $paytype = PayTypeEnum::WECHAT_PAY, array $other = [])
    {
        //['is_wxapp' => 1 || 0]
        $updata = ['status' => OrderEnum::ORDER_STATUS_1, 'paytype' => $paytype, 'paytime' => time()];
        $updata['freeze_pay'] = $orderInfo['freeze_pay'] ??= 0;
        if ($other && isset($other['transid'])) {
            $updata['transid'] = $other['transid'];
        }
        $res1 = $this->dao->update($orderInfo['id'], $updata);
        $orderInfo['trans_id'] = $other['transid'] ?? '';
        $orderInfo['pay_time'] = time();
        $orderInfo['pay_type'] = $paytype;

        //订单支付成功事件--后续订单处理交由其他逻辑处理，如：发货通知，清空购物车，计算佣金等
        $memberInfo = app(MemberLogic::class)->get($orderInfo['member_id']);
        event('order.pay', [$orderInfo, $memberInfo]);
        $res = $res1;
        return false !== $res;
    }

    /**
     * 小程序订单推送发货信息
     * @param array $order
     * @return void
     */
    function uploadShippingInfo(array $order)
    {
        if (1 == $order['apppay']) { //小程序支付订单
            $express = app(ExpressModel::class)->column('coding', 'express');
            $express_mobile = app(MemberAddressModel::class)->where(['id' => $order['addressid']])->value('mobile');
            date_default_timezone_set('Asia/Shanghai');

            $logistics_type = $order['isvirtual'] ? 3 : 1;
            $logistics_type = $order['dispatchtype'] ? 4 : $logistics_type;
            $shipping = [
                'order_key' => [
                    'order_number_type' => 2,
                    'transaction_id' => $order['wxapp_prepay_id'],
                ],
                'logistics_type' => $logistics_type,
                'delivery_mode' => $order['sendtype'] ? 2 : 1,
                'is_all_delivered' => true,
                'shipping_list' => [
                    'tracking_no' => $logistics_type == 1 ? $order['expresssn'] : '',
                    'express_company' => $express[$order['express']] ?? '',
                    'item_desc' => '全民供享药房订单',
                    'contact' => [
                        ///'consignor_contact' => '189****1234',
                        'receiver_contact' => substr_replace($express_mobile, '****', 3, 4)
                    ],
                ],
                'upload_time' => date('Y-m-d\TH:i:s.vP', time()),
                'payer' => [
                    'openid' => $order['openid']
                ]
            ];
            $mnp_service = app()->make(MiniProgramService::class, ['uniacid' => $params['i']]);
            $mnp_service->miniProgram->shipping->uploadShippingInfo($shipping);
        }
    }

    /**
     * 计算商品会员折扣金额
     * @param $discount
     * @param $goods_price
     * @param int $buy_total
     * @param array $level
     * @return float
     */
    function calculateGoodsMemberDiscountPrice($discount, $goods_price, int $buy_total = 1, array $level = []): float
    {
        $member_price = $goods_price;
        // 如果折扣为空则不参与折扣价格计算
        if ($discount === '') {
            return $member_price;
        }

        // 计算会员折扣价格
        if (!empty($discount)) {
            if (strpos($discount, '%') !== false) {
                // 比例折扣价格
                $ratio = floatval(str_replace('%', '', $discount));
                if ($ratio > 0 && $ratio < 100) {
                    $member_price = round($ratio / 100 * $goods_price * $buy_total, 2);
                } elseif ($ratio == -1) {
                    $member_price = isset($level['discount']) ? round(floatval($level['discount']) / 100 * $goods_price * $buy_total, 2) : $goods_price * $buy_total;
                }
            } else {
                // 固定金额价格
                $fixed_discount = floatval($discount);
                if ($fixed_discount > 0 && $fixed_discount < $goods_price) {
                    $member_price = round($fixed_discount * $buy_total, 2);
                } elseif ($fixed_discount == -1) {
                    $member_price = isset($level['discount']) ? round(floatval($level['discount']) / 100 * $goods_price * $buy_total, 2) : $goods_price  * $buy_total;
                }
            }
        }

        return $member_price;
    }

    /**
     * 获取商品规格的会员等级折扣
     * 直接通过商品规格id获取会员折扣价格
     * @param int $option_id 商品规格id
     * @param array $level 会员等级数组
     * @param int $buy_total 购买数量
     * @return array|false|float|int|mixed|Services_JSON_Error|string
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsOptionMemberPrice(int $option_id, array $level, int $buy_total = 1) {
        $option = app(GoodsOptionLogic::class)->getGoodsOptionDetail($option_id, '*', ['goods'])->toArray();
        $goods = $option['goods'];
        if (empty($goods)) {
            return result(-1, '商品不存在');
        }
        if (!empty($goods['isnodiscount'])) {
            return result(-1, '该商品不参与会员折扣');
        }
        $discounts = is_array($goods['discounts']) !== null ? $goods['discounts'] : [];
        $key = !empty($level['id']) ? 'level' . $level['id'] : 'default';
        // 获取会员等级折扣（空或0或0%为不参与折扣，大于0%小于100%为折扣, -1或-1%为系统默认会员折扣，整数为优惠后价格）
        $discount = trim($discounts[$key]['option' . $option['id']]);
        return app(OrderLogic::class)->calculateGoodsMemberDiscountPrice($discount, $option['marketprice'], $buy_total);
    }

    /**
     * 获取商品会员等级价格
     * @param int $goodsid 商品id
     * @param array $level 会员等级数组
     * @param int $buy_total 购买数量
     * @param int $optionid 商品规格id 指定规格ID则返回该规格的会员折扣价格
     * @param int $liveid 直播间id
     * @return false|float|mixed|string|void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsMemberPrice(int $goodsid, array $level, int $buy_total = 1, int $optionid = 0, int $liveid = 0)
    {
        $goods = app(GoodsLogic::class)->getGoodsDetail($goodsid, '*', ['option'])->toArray();
        if (!empty($goods['isnodiscount'])) {
            return result(-1, '该商品不参与会员折扣');
        }
        // 检查是否直播间商品
        if (!empty($liveid)) {
            $isliving = p('live')->isLiving($liveid);
            if (!$isliving) {
                $liveid = 0;
            }
        }
        $discounts = is_array($goods['discounts']) ? $goods['discounts'] : json_decode($goods['discounts'], true);

        //如果是多商户商品，并且会员等级折扣为空的情况下，模拟空的商品会员等级折扣数据，以便计算折扣
        if (empty($goods['discounts']) && $goods['merchid'] > 0) {
            $goods['discounts'] = array(
                'type' => '0',
                'default' => '',
                'default_pay' => ''
            );
            if (!empty($level)) {
                $goods['discounts']['level' . $level['id']] = '';
                $goods['discounts']['level' . $level['id'] . '_pay'] = '';
            }
            $discounts = $goods['discounts'];
        }

        if (is_array($discounts)) {
            $key = !empty($level['id']) ? 'level' . $level['id'] : 'default';

            if (empty($discounts['type'])) {
                // 统一设置折扣
                $member_price = $goods['minprice'];
                if (!empty($discounts[$key])) {
                    $dd = floatval($discounts[$key]); //设置的会员折扣
                    if ($dd > 0 && $dd < 10) {
                        $member_price = round($dd / 10 * $goods['minprice'], 2);
                    }
                } else {
                    $dd = floatval($discounts[$key . '_pay']); //设置的会员折扣

                    $md = floatval($level['discount']); //会员等级折扣
                    if (!empty($dd)) {
                        $member_price = round($dd, 2);
                    } else if ($md > 0 && $md < 10) {
                        $member_price = round($md / 10 * $goods['minprice'], 2);
                    }
                }
                if ($member_price == $goods['minprice']) {
                    return false;
                }
                return $member_price;
            } else {
                // 详细设置折扣
                $options = (new GoodsModel())->getGoodsOptions($goods);
                $marketprice = array();
                foreach ($options as $option) {
                    // 获取会员等级折扣（带%为折扣否则为优惠后价格）
                    $discount = trim($discounts[$key]['option' . $option['id']]);
                    if (!empty($liveid) && !empty($option['islive'])) {
                        if ($option['liveprice'] > 0 && $option['liveprice'] < $option['marketprice']) {
                            $option['marketprice'] = $option['liveprice'];
                        }
                    }
                    $optionprice = self::getGoodsOptionMemberPrice($option['id'], $level, $buy_total);
                    // 如果指定规格的会员折扣价格，则返回该规格的会员折扣价格
                    if ($optionid > 0 && $option['id'] == $optionid) {
                        return $optionprice;
                    }
                    $marketprice[] = $optionprice;
                }
                // 如果有多个规格，则取最低最高价格
                if (count($marketprice) > 1 && is_array($marketprice)) {
                    $minprice = min($marketprice);
                    $maxprice = max($marketprice);
                    $member_price = array(
                        'minprice' => (float)$minprice,
                        'maxprice' => (float)$maxprice
                    );
                    if ($member_price['maxprice'] > $member_price['minprice']) {
                        $member_price = $member_price['minprice'] . "~" . $member_price['maxprice'];
                    } else {
                        $member_price = $member_price['minprice'];
                    }

                    if ($member_price == $goods['minprice']) {
                        return false;
                    }
                    return $member_price;
                }
            }
        }
        return result(-1, '会员折扣设置错误');
    }

    /**
     * 获取订单商品的特价通折扣价格
     * @param array $goods 订单商品
     * @param string $role 用户对象角色
     * @return false|float|mixed|string|void
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function getGoodsSpecialPrice(array $goods, string $role = 'clerk'){
        global $_W;

        // 订单购买数量
        $total = $goods['total'] ?? 1;
        // 订单商品的原价总价
        $goods_price = $goods['marketprice'] * $total;

        // 成交的价格
        $price = $goods_price;
        $special_price = $goods_price;

        $isd = false;
        // 特价通折扣数组
        $specialprice = $goods['specialprice']?? [];
        // 判断role是否存在$goods['specialprice']数组中，不存在则返回flase,表示没有特价通折扣
        if (empty($specialprice) || (!isset($specialprice[$role]) || !isset($specialprice[$role.'_pay']))) {
            return false;
        }


        if (is_array($specialprice)) {
            if (empty($specialprice['type'])) {
                // 统一折扣处理
                if (!empty($specialprice[$role])) {
                    // 按比例折扣
                    $discountRatio = floatval($specialprice[$role]); // 折扣比例值

                    // 折扣比例有效范围检查 (0-10折)
                    if ($discountRatio > 0 && $discountRatio < 10) {
                        $special_price = round($discountRatio / 10 * $goods_price, 2);
                    }
                } else {
                    // 按金额折扣
                    $unitDiscount = 0; // 单件商品折扣金额

                    // 获取单件折扣金额配置
                    if (isset($specialprice[$role . '_pay']) && !empty($specialprice[$role . '_pay'])) {
                        $unitDiscount = floatval($specialprice[$role . '_pay']);
                    }

                    // 计算总折扣金额 = 单件折扣 × 数量
                    $totalDiscountAmount = $unitDiscount * intval($total);

                    if (!empty($totalDiscountAmount)) {
                        $special_price = round($totalDiscountAmount, 2);
                    }
                }
            } else {
                // 详细折扣
                $isd = trim($specialprice[$role]['option' . $goods['optionid']]);
                if (!empty($isd)) {
                    $special_price = $this->getFormartDiscountPrice($isd, $goods_price, $total);
                }
            }
        }
        //判断特价价是否低于原价
        if ($special_price >= $goods_price) {
            return false;
        } else {
            return $special_price;
        }
    }

    /**
     * 检查用户是否有特价通权限
     * @param int $goodsid 商品id
     * @param int $optionid 商品规格id
     * @param int $member_id 会员id
     * @param string $role 用户角色
     * @return bool|mixed|string|void|null|array|object
     * @throws DataNotFoundException
     * @throws DbException
     * @throws ModelNotFoundException
     */
    public function checkSpecialPriceAuth(int $goodsid, int $optionid = 0, int $member_id, string $role = 'clerk'): bool
    {
        // 检查商品
        $goods = app(GoodsLogic::class)->getGoodsDetail($goodsid, '*', ['option'])->toArray();
        if (empty($goods)) {
            return false;
        }
        // 检查是否有特价通配置
        if (empty($goods['specialprice'])) {
            return false;
        }
        // 获取用户信息
        $member = app(MemberLogic::class)->get($member_id)->toArray();
        // 检查用户是否存在
        if (empty($member)) {
            return false;
        }
        // 检查用户口是否有角色权限
        if (!in_array('is_' . $role, $member)) {
            return false;
        }
        // 检查用户是否有所属集团
        if (!empty($member['org_id']) && $member['org_id'] > 0) {
            // 获取集团插件配置信息
            $orgSet = m('common')->getPluginSet('org');
            if (empty($orgSet) || empty($orgSet['is_enabled_specialprice'])) {
                return false;
            }
            // 获取集团账号信息
            $org = p('org')->getListUserOne($member['org_id']);
            $org_config = $org ? json_decode($org['config'], true) : [];
            // 检查集团是否存在或是否启用特价通
            if (empty($org_config) || empty($org_config['is_enabled_specialprice'])) {
                return false;
            }
        } else {
            return false;
        }
        return true;
    }
}