<?php
namespace app\controller\shop;
use app\controller\MobilePage;

class CityexpressController extends MobilePage {
	public function map(){
		global $_W, $_GPC;
		$cityexpress = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_city_express') . ' WHERE uniacid=:uniacid AND merchid=:merchid', array(':uniacid' => $_W['uniacid'], ':merchid' => 0));
		$address = m('common')->getSysset('contact');
		$shop = m('common')->getSysset('shop');
		if (!empty($address)) {
			$cityexpress['address'] = $address['province'] . $address['city'] . $address['address'];
		}
		if (!empty($shop)) {
			$cityexpress['name'] = $shop['name'];
			$cityexpress['logo'] = $shop['logo'];
		}
		include $this->template();
	}
}