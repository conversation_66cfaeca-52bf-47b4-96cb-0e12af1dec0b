<?php
namespace app\controller\goods;
use app\controller\MobilePage;
use app\plugin\prescription\core\logic\PrescriptionCheckGoodsLogic;

class PickerController extends MobilePage {

    function main(){
        global $_W, $_GPC;
        $memberSysset = m('common')->getSysset('member');
        $memberPriceText = empty($memberSysset['memberPriceText'])?'会员价':$memberSysset['memberPriceText'];//会员价文本
        $memberCardText = empty($memberSysset['memberCardText'])?'会员卡':$memberSysset['memberCardText']; //会员卡文本
        $id = intval($_GPC['id']);
        $action = trim($_GPC['action']);
        $rank = intval($_SESSION[$id . '_rank']);
        $log_id = intval($_SESSION[$id . '_log_id']);
        $join_id = intval($_SESSION[$id . '_join_id']);
        $cremind = false;
        $seckillinfo = false;
        $seckill  = p('seckill');
        if( $seckill){
            $time = time();
            $seckillinfo = $seckill->getSeckill($id);
            if(!empty($seckillinfo)){
                if($time >= $seckillinfo['starttime'] && $time<$seckillinfo['endtime']){
                    $seckillinfo['status'] = 0;
                }elseif( $time < $seckillinfo['starttime'] ){
                    $seckillinfo['status'] = 1;
                }else {
                    $seckillinfo['status'] = -1;
                }
            }
        }

        /* 直播间商品 处理Step.1 */
        $liveid = intval($_GPC['liveid']);
        if(!empty($liveid)){
            $isliving=false;
            if(p('live')){
                $isliving = p('live')->isLiving($liveid);
            }
            if(!$isliving){
                $liveid = 0;
            }
        }

        //商品
        $goods = pdo_fetch('select id,thumb,title,marketprice,stock,maxbuy,minbuy,is_minbuy_times_add,unit,hasoption,showtotal,diyformid,diyformtype,diyfields,isdiscount,presellprice,isdiscount_time,isdiscount_time_start,isdiscount_discounts,discounts,hascommission,nocommission,commission,commission1_rate,commission1_pay,hasClerkCommission,noClerkCommission,clerkCommission,clerkCommission1_rate,clerkCommission1_pay,doctor_has_commission,doctor_no_commission,doctor_commission,doctor_commission_rate,doctor_commission_pay,marketprice,needfollow, followtip, followurl, `type`, isverify, maxprice, minprice, merchsale,ispresell,preselltimeend,unite_total,threen,preselltimestart,presellovertime,presellover,islive,liveprice,minliveprice,maxliveprice,isnodiscount,costprice,activity_id,can_add_cart,cycelbuy_goods_id,goodsClassID,medicineAttributeID from ' . tablename('elapp_shop_goods') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        //$goods = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($goods)) {
            show_json(0);
        }
        $threenprice = json_decode($goods['threen'],1);
        $goods['thistime'] = time();
        $goods = set_medias($goods, 'thumb');
        /* 直播间商品 处理Step.2 */
        if(!empty($liveid)){
            $islive =false;
            if(p('live')){
                $islive = p('live')->getLivePrice($goods, $liveid);
            }
            if($islive){
                $goods['minprice'] = $islive['minprice'];
                $goods['maxprice'] = $islive['maxprice'];
            }
        }
        $openid = $_W['openid'];
        if (is_weixin()) {
            $follow = m("user")->followed($openid);
            if (!empty($goods['needfollow']) && !$follow) {
                $followtip = empty($goods['followtip']) ? "如果您想要购买此商品，需要您关注我们的公众号，点击【确定】关注后再来购买吧~" : $goods['followtip'];
                $followqrcode = $_W['shopset']['share']['followqrcode'];
                $followqrcode = tomedia($followqrcode);
                $followurl = empty($goods['followurl']) ? $_W['shopset']['share']['followurl'] : $goods['followurl'];
                show_json(2, array('followtip' => $followtip, 'followurl' => $followurl, 'followqrcode' => $followqrcode));
            }
        }

        $openid =$_W['openid'];
        $member = m('member')->getMember($openid);
        //  验证是否登录
        if(empty($openid)){
            $sendtime = $_SESSION['verifycodesendtime'];
            if(empty($sendtime) || $sendtime+60<time()){
                $endtime = 0;
            }else{
                $endtime = 60 - (time() - $sendtime);
            }
            show_json(4, array(
                'endtime'=>$endtime,
                'imgcode'=>$_W['shopset']['wap']['smsimgcode']
            ));
        }

        //  验证手机号
        $mustbind = 0;
        if(!empty($_W['shopset']['wap']['open']) && !empty($_W['shopset']['wap']['mustbind']) && empty($member['mobileverify'])){
            $mustbind = 1;
            $sendtime = $_SESSION['verifycodesendtime'];
            if(empty($sendtime) || $sendtime+60<time()){
                $endtime = 0;
            }else{
                $endtime = 60 - (time() - $sendtime);
            }
        }
        //机构会员一次性资料填写 #180 修改机构会员一次性资料填写逻辑为创建订单时执行 https://ones.cn/project/#/team/X1uxuvHU/task/8zVou8amtBHFodkD
        $mustquickinfo = 0;
        if (p('copartner')){
            if (!empty($member['copartner_id'])){
                $copartner = p('copartner')->getCopartnerInfo($member['copartner_id']);
                if($copartner){
                    $relate = json_decode($copartner['relate'],true);
                    $copartnerClerkSet = $relate['clerk'];
                    $mustquickinfo = $copartnerClerkSet['mustquickinfo'];
                    if($copartnerClerkSet['mustquickinfo'] == 1 && empty($member['mobileverify'])){                        
                        $mustquickinfo = 1;
                    }
                }
            } 
        }
        if($mustbind == 1){
            //机构会员一次性填写
            if($mustquickinfo == 1){                
                $Url = 'member/quickinfo';
                show_json(9,array(
                    'url' => $Url,
                    'title' => !empty($copartnerClerkSet['mustquickinfoTextTitle']) ? $copartnerClerkSet['mustquickinfoTextTitle'] : '资料填写',
                    'content' => !empty($copartnerClerkSet['mustquickinfoTextContent']) ? $copartnerClerkSet['mustquickinfoTextContent'] : '请先填写基本资料！',
                    'buttontext' => !empty($copartnerClerkSet['mustquickinfoTextButtontext']) ? $copartnerClerkSet['mustquickinfoTextButtontext'] : '马上填写',
                ));
            }else{

                // 是否是代客下单链接请求
                $isOrderFromClerk = strpos($_SERVER['HTTP_REFERER'], 'clerk.goods.detail/main') !== false ||
                                    strpos($_SERVER['HTTP_REFERER'], 'clerk.goods.create/main') !== false;

                // 如果不是代客下单链接请求，需要经历以下流程
                if (!$isOrderFromClerk) {
                    //全局设置填写流程
                    show_json(3, array(
                        'endtime'=>$endtime,
                        'imgcode'=>$_W['shopset']['wap']['smsimgcode']
                    ));
                }
            }
        }
        //预售
        if($goods['ispresell'] > 0){
            $times = $goods['presellovertime'] * 60 * 60 * 24 + $goods['preselltimeend'];
            if(!($goods['presellover']>0 && $times <= time())){
                if($goods['preselltimestart'] > 0 && $goods['preselltimestart'] > time()){
                    show_json(5,'预售未开始');
                }
                if($goods['preselltimeend'] > 0 && $goods['preselltimeend'] < time()){
                    show_json(5,'预售已结束');
                }
            }
            //预售结束转为正常销售
            /*$times = $goods['presellovertime'] * 60 * 60 * 24 + $goods['preselltimeend'];
            if($goods['presellover']>0 && $times <= time() && $goods['preselltimeend'] > 0 && $goods['preselltimeend'] < time()){

            }else{
                show_json(5,'预售已结束');
            }*/
        }

        if($goods['isdiscount'] && $goods['isdiscount_time']>=time() && $goods['isdiscount_time_start'] < time() ){
            //有促销
            $isdiscount = true;
            $isdiscount_discounts = json_decode($goods['isdiscount_discounts'],true);
            $levelid = $member['level'];
            $key = empty($levelid)?'default':'level'.$levelid;
        } else {
            $isdiscount = false;
        }

        //任务活动购买商品
        $task_goods_data = m('goods')->getTaskGoods($openid, $id, $rank, $log_id, $join_id);
        if (empty($task_goods_data['is_task_goods'])) {
            $is_task_goods = 0;
        } else {
            $is_task_goods = $task_goods_data['is_task_goods'];
            $is_task_goods_option = $task_goods_data['is_task_goods_option'];
            $task_goods = $task_goods_data['task_goods'];
        }

        $specs =false;
        $options = false;
        //查询规格商品id， 如果关联分期购商品，采用分期购商品id
        $options_goods_id = $id;
        if($goods['cycelbuy_goods_id']){
            $options_goods_id = $goods['cycelbuy_goods_id'];
        }
        if (!empty($goods) && $goods['hasoption']) {

            $specs = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_spec') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $options_goods_id, ':uniacid' => $_W['uniacid']));
            foreach($specs as &$spec) {
                $spec['items'] = pdo_fetchall('select * from '.tablename('elapp_shop_goods_spec_item')." where specid=:specid and `show`=1 order by displayorder asc",array(':specid'=>$spec['id']));
            }
            unset($spec);
            $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $options_goods_id, ':uniacid' => $_W['uniacid']));
        }
        if (!empty($options) && !empty($goods['unite_total'])) {
            foreach($options as &$option){
                $option['stock'] = $goods['stock'];
            }
            unset($option);
        }

        /* 直播间商品 处理Step.3 */
        if(!empty($liveid) && !empty($options)){
            // 重新获取直播商品规格价格
            //$options =array();
            if(p('live')){
                $options = p('live')->getLiveOptions($goods['id'], $liveid, $options);
            }
            $prices = array();
            foreach ($options as $option){
                $prices[] = price_format($option['marketprice']);
            }
            unset($option);
            $goods['minprice'] = min($prices);
            $goods['maxprice'] = max($prices);
        }
        if( $seckillinfo && $seckillinfo['status']==0){
            $minprice = $maxprice = $goods['marketprice'] = $seckillinfo['price'];
            if(count($seckillinfo['options'])>0 && !empty($options)){
                foreach($options as &$option){
                    foreach($seckillinfo['options'] as $so){
                        if($option['id']==$so['optionid']){
                            $option['marketprice'] = $so['price'];
                        }
                    }
                }
                unset($option);
            }
        } else{
            $minprice = $goods['minprice'];
            $maxprice = $goods['maxprice'] ;
        }


        //价格显示
        if (!empty($is_task_goods)) {
            if ( isset($options) && count($options) > 0 && $goods['hasoption']) {
                $prices = array();
                foreach ($task_goods['spec'] as $k => $v) {
                    $prices[] = $v['marketprice'];
                }
                $minprice = min($prices);
                $maxprice = max($prices);
                foreach ($options as $k => $v) {
                    $option_id = $v['id'];
                    if (array_key_exists($option_id, $task_goods['spec'])) {
                        if($goods['ispresell']>0 && ($goods['preselltimeend'] == 0 || $goods['preselltimeend'] > time())){
                            $options[$k]['marketprice'] = $task_goods['spec'][$option_id]['presellprice'];
                        }else{
                            $options[$k]['marketprice'] = $task_goods['spec'][$option_id]['marketprice'];
                        }
                        $options[$k]['stock'] = $task_goods['spec'][$option_id]['stock'];
                    }
                    $prices[] = $v['marketprice'];
                }
            } else {
                $minprice = $task_goods['marketprice'];
                $maxprice = $task_goods['marketprice'];
            }
        } else {
            if($goods['isdiscount'] && $goods['isdiscount_time']>=time() && $goods['isdiscount_time_start'] < time() ){
                $goods['oldmaxprice'] = $maxprice;
                $isdiscount_discounts = json_decode($goods['isdiscount_discounts'],true);
                $prices = array();
                if (!isset($isdiscount_discounts['type']) || empty($isdiscount_discounts['type'])) {
                    //统一促销
                    $level = m('member')->getLevel($openid);
                    $prices_array = m('order')->getGoodsDiscountPrice($goods, $level, 1);
                    $prices[] = $prices_array['price'];
                } else {
                    //详细促销
                    $goods_discounts = m('order')->getGoodsDiscounts($goods, $isdiscount_discounts, $levelid, $options);
                    $prices = $goods_discounts['prices'];
                    $options = $goods_discounts['options'];
                }
                $minprice = min($prices);
                $maxprice = max($prices);
            }
        }
        //取出后台设置会员折扣的额度,如果商品没有设置就走后台的额度
        $leveldiscount = pdo_fetch('SELECT * FROM' . tablename('elapp_shop_member_level') . 'where id=:id and uniacid=:uniacid limit 1 ',array(':id' =>$member['level'], ':uniacid' => $_W['uniacid']));
        $leveldis = $leveldiscount['discount'];
        //获取商品的会员价
        if($goods['isnodiscount'] ==0){
            //获取会员等级
            $member_levelid = intval($member['level']);
            if(!empty($member_levelid)){
                $member_level = pdo_fetch('select * from ' . tablename('elapp_shop_member_level') . ' where id=:id and uniacid=:uniacid and enabled=1 limit 1', array(':id' =>$member_levelid, ':uniacid' => $_W['uniacid']));
                $member_level = empty($member_level)? array(): $member_level;
            }
            $discounts = json_decode($goods['discounts'], true);
            //判断是否开启折扣状态
            if (empty($leveldiscount['enabled'])){
                $discounts = json_decode($goods['discounts'], true);
            }else{
                //将会员折扣赋值给商品折扣中
                if ($discounts['default'] == 0){
                    $discounts['default'] = $leveldis;
                }
            }

            /* if (empty($discounts['default'])){

            }*/
            if (is_array($discounts)) {
                $key = !empty($member_level['id']) ? 'level' . $member_level['id'] : 'default';
                if (!isset($discounts['type']) || empty($discounts['type'])) {
                    $memberprice_dis = 0;
                    if (!empty($discounts[$key])){
                        $dd = floatval($discounts[$key]); //设置的会员折扣
                        if ($dd > 0 && $dd < 10) {
                            $memberprice_dis = round($dd / 10 * $goods['minprice'], 2);
                        }
                    }else{
                        $dd = floatval($discounts[$key.'_pay']); //设置的会员折扣
                        $md = floatval($member_level['discount']); //会员等级折扣
                        if (!empty($dd)){
                            $memberprice_dis = round($dd, 2);
                        }else if ($md > 0 && $md < 10) {
                            $memberprice_dis = round($md / 10 * $goods['minprice'], 2);
                        }
                    }
                    $goods['show'] =0;
                    $goods['member_discount'] =  number_format($memberprice_dis,2,'.','');
                }
                if($goods['hasoption'] ==1&$discounts['type']==1 & empty($isdiscount)) {
                    //详细折扣
                    $options = m('goods')->getGoodsOptions($goods);
                    foreach ($options as &$option){
                        $discount = trim($discounts[$key]['option' . $option['id']]);
                        if($discount==''){
                            $discount = round(floatval($member_level['discount'])*10,2).'%';
                        }
                        if (!empty($discount)) {
                            if (strexists($discount, '%')) {
                                //促销折扣
                                $dd = floatval(str_replace('%', '', $discount));

                                if ($dd > 0 && $dd < 100) {
                                    $price = round($dd / 100 * $option['marketprice'], 2);
                                }
                            } else if (floatval($discount) > 0) {
                                //促销价格
                                $price = round(floatval($discount), 2);
                            }
                        }
                        if($price>0){
                            $option['member_discount'] = number_format($price,2,'.','');
                        }else{
                            $option['member_discount'] = 0;
                        }
                        $price = 0;
                    }
                    unset($goods['member_discount']);
                    $goods['show'] =1;
                    unset($option);
                }elseif($goods['hasoption'] ==1&$discounts['type']==0 & empty($isdiscount)){
                    $options = m('goods')->getGoodsOptions($goods);
                    foreach ($options as &$option){
                        if (!empty($discounts[$key])){
                            $dd = floatval($discounts[$key]); //设置的会员折扣
                            if ($dd > 0 && $dd < 10) {
                                $memberprice = round($dd / 10 * $option['marketprice'], 2);
                            }
                        }else{
                            $dd = floatval($discounts[$key.'_pay']); //设置的会员折扣
                            $md = floatval($member_level['discount']); //会员等级折扣
                            if (!empty($dd)){
                                $memberprice = round($dd, 2);
                            }else if ($md > 0 && $md < 10) {
                                $memberprice = round($md / 10 * $option['marketprice'], 2);
                            }
                        }
                        if($memberprice>0){
                            $option['member_discount'] = number_format($memberprice,2,'.','');
                        }else{
                            $option['member_discount'] = 0;
                        }
                    }
                    unset($option);
                    unset($goods['member_discount']);
                    $goods['show'] =1;
                }
            }
        }

        //获取不同规格的不同佣金
        $clevel = $this->getcLevel($_W['openid']);
        $set = array();
        //分销佣金显示
        if(p('commission')) {
            $set = $this->getSet();
            $goods['cansee'] = $set['cansee'];
            $goods['seetitle'] = $set['seetitle'];
        }else{
            $goods['cansee'] = 0;
            $goods['seetitle'] = '';
        }
        //店员收益显示
        $clerklevel = false; 
        $clerk_set = array();     
        if(p('clerk')) {
            $clerk = p('clerk');
            $clerk_set = $clerk->getSet();
            $clerklevel = $clerk->getLevel($_W['openid']);
            $goods['clerkCansee'] = $clerk_set['cansee'];
            $goods['clerkSeetitle'] = $clerk_set['seetitle'];
            $myshop = $clerk->getShop($member['id']);
            $goods['is_show_revenue'] = $myshop['is_show_revenue'];
        }else{
            $goods['clerkCansee'] = 0;
            $goods['clerkSeetitle'] = '';
            $goods['is_show_revenue'] = 0;
        }
        //医生收益显示
        $doctor_level = false;
        $doctor_set = array();     
        if(p('doctor')) {
            $doctor = p('doctor');
            $doctor_set = $doctor->getSet();
            $doctor_level = $doctor->getLevel($_W['openid']);
            $goods['doctorCansee'] = $doctor_set['cansee'];
            $goods['doctorSeetitle'] = $doctor_set['seetitle'];
            $myshop = $doctor->getShop($member['id']);
            $goods['is_show_revenue'] = $myshop['is_show_revenue'];
        }else{
            $goods['doctorCansee'] = 0;
            $goods['doctorSeetitle'] = '';
            $goods['is_show_revenue'] = 0;
        }
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        //商品最高会员等级价格
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);
        if(p('seckill')){
            //秒杀
            if(!p('seckill')->getSeckill($goods['id'])){
                //分销预计佣金
                if($goods['nocommission'] ==1){
                    $seecommission = 0;
                }else if($goods['hascommission'] == 1 && $goods['nocommission'] ==0){
                    $price = $goods['maxprice'];
                    $levelid = 'default';
                    if($clevel == 'false'){
                        $seecommission = 0;
                    }else {
                        if($clevel) {
                            $levelid = 'level' . $clevel['id'];
                        }
                        $goods_commission = !empty($goods['commission']) ? json_decode($goods['commission'], true) : array();
                        if($goods_commission['type'] == 0) {
                            $seecommission = $set['level'] >= 1 ? ($goods['commission1_rate'] > 0 ? ($goods['commission1_rate'] * $goods['marketprice'] / 100) : $goods['commission1_pay']) : 0;
                            if(is_array($options) && !empty($options)){
                                foreach ($options as $k => $v) {
                                    $seecommission = $set['level'] >= 1 ? ($goods['commission1_rate'] > 0 ? ($goods['commission1_rate'] * $v['marketprice'] / 100) : $v['commission1_pay']) : 0;
                                    $options[$k]['seecommission'] = $seecommission;
                                }
                            }
                        } else {
                            //获取每个规格的佣金
                            if(is_array($options)) {
                                foreach ($goods_commission[$levelid] as $key => $value) {
                                    foreach ($options as $k => $v) {
                                        if(('option' . $v['id']) == $key) {
                                            if(strexists($value[0], '%')) {
                                                $options[$k]['seecommission'] = (floatval(str_replace('%', '', $value[0]) / 100) * $v['marketprice']);
                                                continue;
                                            } else {
                                                $options[$k]['seecommission'] = $value[0];
                                                continue;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }elseif($goods['hasoption'] ==1&&$goods['hascommission'] == 0 && $goods['nocommission'] ==0){
                    foreach($options as $ke=>$vl){
                        if ($clevel!='false' && $clevel) {
                            $options[$ke]['seecommission'] = $set['level'] >= 1 ? round($clevel['commission1'] * $vl['marketprice'] / 100, 2) : 0;
                        } else {
                            $options[$ke]['seecommission'] = $set['level'] >= 1 ? round($set['commission1'] * $vl['marketprice'] / 100, 2) : 0;
                        }
                    }
                }else{
                    if ($clevel!='false' && $clevel) {
                        $seecommission = $set['level'] >= 1 ? round($clevel['commission1'] * $goods['marketprice'] / 100, 2) : 0;
                    } else {
                        $seecommission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
                    }
                }

                ##店员预计收益
                $clerkSeecommission = 0;
                $price = $goods['maxprice'];
                //是否减掉商品成本再计算提成
                if(!empty($clerk_set['isReduceGoodsCostprice'])){
                    $price = $price - $goods['costprice'];
                }
                //提成计算价格规则 1最高会员折扣价 0零售价
                $calculatePriceType = $clerk_set['calculatePriceType'];
                //不参与分润
                if($goods['noClerkCommission'] ==1){
                    $clerkSeecommission = 0;
                }else if($goods['hasClerkCommission'] == 1 && $goods['noClerkCommission'] ==0){
                    //参与分润且启用独立比例设置
                    $clerklevelid = 'default';
                    if($clerklevel == 'false'){
                        $clerkSeecommission = 0;
                    }else {
                        if($clerklevel) {
                            $clerklevelid = 'level' . $clerklevel['id'];
                        }
                        $goods_clerkCommission = !empty($goods['clerkCommission']) ? json_decode($goods['clerkCommission'], true) : array();
                        //统一设置
                        if($goods_clerkCommission['type'] == 0) {
                            //如果零售价 <= 最大商品价格
                            if($goods['marketprice'] <= $goods['maxprice']){
                                $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                            }
                            //按最高会员等级折扣
                            if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                                $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                            }else{
                                $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                            }                
                            //是否减掉商品成本再计算提成
                            if(!empty($clerk_set['isReduceGoodsCostprice'])){
                                $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                            }                            
                            $clerkSeecommission = $clerk_set['isopen'] >= 1 && $clerk_set['level'] >= 1 ? (0 < $goods['clerkCommission1_rate'] && 100 < $goods['clerkCommission1_rate'] ? ($goods['clerkCommission1_rate'] * $commissionCalculatePrice / 100) : $goods['clerkCommission1_pay']) : 0;
                            $goods['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');

                            
                        } else {//详细设置
                            //获取每个规格的佣金
                            if(is_array($options) && !empty($options)) {
                                foreach ($goods_clerkCommission[$clerklevelid] as $key => $value) {
                                    $maxkey = array_search(max($value),$value);
                                    foreach ($options as $k => $v) {
                                        if(('option' . $v['id']) == $key) {
                                            //按最高会员等级折扣或价格
                                            //拼接最高会员等级                                            
                                            $maxlevelkey = !empty($maxlevel['id']) ? 'level' . $maxlevel['id'] : 'default';                                           
                                            $maxdiscounts = trim($discounts[$maxlevelkey]['option' . $v['id']]);
                                            if($maxdiscounts==''){
                                                $maxdiscounts = round(floatval($maxlevel['discount'])*10,2).'%';
                                            }
                                            if (!empty($maxdiscounts)) {
                                                if (strexists($maxdiscounts, '%')) {
                                                    //会员折扣
                                                    $dd = floatval(str_replace('%', '', $maxdiscounts));
                                                    if ($dd > 0 && $dd < 100) {
                                                        $maxmemberprice = round($dd / 100 * $v['marketprice'], 2);
                                                    }
                                                } else if (floatval($maxdiscounts) > 0) {
                                                    //会员价格
                                                    $maxmemberprice = round(floatval($maxdiscounts), 2);
                                                }
                                            }
                                            if($calculatePriceType == 1 && $maxmemberprice > 0){
                                                $commissionCalculatePrice = $maxmemberprice;//使用最高的会员等级价格
                                            }else{
                                                $commissionCalculatePrice = $v['marketprice'];//商品最低零售价
                                            }
                                            //是否减掉商品成本再计算收益
                                            if(!empty($clerk_set['isReduceGoodsCostprice'])){
                                                $commissionCalculatePrice = $commissionCalculatePrice - $v['costprice'];
                                            }
                                            if(strexists($value[$maxkey], '%')) {//判断有%符号则按百分比
                                                $clerkSeecommission = (floatval(str_replace('%', '', $value[0]) / 100) * $commissionCalculatePrice);
                                                $options[$k]['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');
                                                continue;
                                            } else {
                                                $options[$k]['clerkSeecommission'] = number_format($value[0],2,'.','');
                                                continue;
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }elseif($goods['hasoption'] ==1 && $goods['hasClerkCommission'] == 0 && $goods['noClerkCommission'] ==0){
                    # 多规格
                    foreach($options as $ke=>$vl){
                        if($vl['marketprice'] <= $vl['maxprice']){
                            $commissionCalculatePrice = $vl['marketprice'] = $vl['maxprice'];
                        }
                        //按最高会员等级折扣
                        if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                            $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                        }else{
                            $commissionCalculatePrice = $vl['minprice'];//商品最低零售价
                        }
                        //是否减掉商品成本再计算提成
                        if(!empty($clerk_set['isReduceGoodsCostprice'])){
                            $price = $commissionCalculatePrice = $commissionCalculatePrice - $vl['costprice'];
                        }
                        if ($clerklevel!='false' && $clerklevel) {
                            $clerkSeecommission = $clerk_set['level'] >= 1 ? round($clerklevel['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
                            $options[$ke]['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');
                        } else {
                            $clerkSeecommission = $clerk_set['level'] >= 1 ? round($clerk_set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
                            $options[$ke]['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');
                        }
                    }
                }else{
                    # 单规格
                    if($goods['marketprice'] <= $goods['maxprice']){
                        $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                    }
                    //按最高会员等级折扣
                    if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                        $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                    }else{
                        $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                    }
                    //是否减掉商品成本再计算提成
                    if(!empty($clerk_set['isReduceGoodsCostprice'])){
                        $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                    }
                    if ($clerklevel!='false' && $clerklevel) {
                        $clerkSeecommission = $clerk_set['level'] >= 1 ? round($clerklevel['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
                        $goods['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');
                    } else {
                        $clerkSeecommission = $clerk_set['level'] >= 1 ? round($clerk_set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
                        $goods['clerkSeecommission'] = number_format($clerkSeecommission,2,'.','');
                    }
                }
            }
        }

        if($goods['ispresell']>0 && ($goods['preselltimeend'] == 0 || $goods['preselltimeend'] > time())){
            $presell = pdo_fetch("select min(presellprice) as minprice,max(presellprice) as maxprice from ".tablename('elapp_shop_goods_option')." where goodsid = ".$id);
            $minprice = $presell['minprice'];
            $maxprice = $presell['maxprice'];
        }
        $goods['minprice'] = number_format( $minprice,2); $goods['maxprice'] =number_format(  $maxprice,2);
        $diyformhtml = "";
        if ($action == 'cremind') {
            $cremind_plugin = p('cremind');
            $cremind_data = m('common')->getPluginset('cremind');
            if ($cremind_plugin && $cremind_data['remindopen']) {
                $cremind = true;
            }
            ob_start();
            include $this->template('cremind/formfields');
            $cremindformhtml = ob_get_contents();
            ob_clean();
        } else {
            //自定义表单
            $diyform_plugin = p('diyform');
            if($diyform_plugin){
                $fields = false;
                if($goods['diyformtype'] == 1){
                    //模板
                    if(!empty($goods['diyformid'])){
                        $diyformid = $goods['diyformid'];
                        $formInfo = $diyform_plugin->getDiyformInfo($diyformid);
                        $fields = $formInfo['fields'];
                    }
                } else if($goods['diyformtype'] == 2){
                    //自定义
                    $diyformid = 0;
                    $fields = iunserializer($goods['diyfields']);
                    if(empty($fields)){
                        $fields = false;
                    }
                }
                if(!empty($fields)){
                    ob_start();
                    $inPicker = true;
                    $openid = $_W['openid'];
                    $member = m('member')->getMember($openid, true);
                    $f_data = $diyform_plugin->getLastData(3, 0, $diyformid, $id, $fields, $member);
                    $flag = 0;
                    if (!empty($f_data)) {
                        foreach ($f_data as $k => $v) {
                            if (!empty($v)) {
                                $flag = 1;
                                break;
                            }
                        }
                    }
                    if (empty($flag)) {
                        $f_data = $diyform_plugin->getLastCartData($id);
                    }
                    $area_set = m('util')->get_area_config_set();
                    $new_area = intval($area_set['new_area']);
                    $address_street = intval($area_set['address_street']);

                    include $this->template('diyform/formfields');
                    $diyformhtml = ob_get_contents();
                    ob_clean();
                }
            }
        }
        if (!empty($specs)){
            foreach ($specs as $key => $value){
                foreach ($specs[$key]['items'] as $k=>&$v){
                    $v['thumb'] = tomedia($v['thumb']);
                }
            }
        }
        //是否可以加入购物车
        $goods['canAddCart'] = true;
        if ($goods['isverify'] == 2 || $goods['type'] == 2 || $goods['type'] == 3 || $goods['type'] == 20 || empty($goods['can_add_cart'])) {
            $goods['canAddCart'] = false;
        }
        if(!empty($seckillinfo) && empty($seckillinfo['can_add_cart'])){
            $goods['canAddCart'] = false;
        }
        if (p('task')){
            $task_id = intval($_SESSION[$id . '_task_id']);
            if (!empty($task_id)){
                $rewarded = pdo_fetchcolumn("SELECT `rewarded` FROM ".tablename('elapp_shop_task_extension_join')." WHERE id = :id AND uniacid = :uniacid",array(':id'=>$task_id,':uniacid'=>$_W['uniacid']));
                $taskGoodsInfo = unserialize($rewarded);
                $taskGoodsInfo = $taskGoodsInfo['goods'][$id];
                if (empty($taskGoodsInfo['option'])){
                    $goods['marketprice'] = $taskGoodsInfo['price'];
                }else{//有规格
                    foreach($options as $gk =>$gv){
                        if ($options[$gk]['id'] == $taskGoodsInfo){
                            $options[$gk]['marketprice'] = $taskGoodsInfo['price'];
                        }
                    }
                }
            }
        }

        //赠品
        $sale_plugin = com('sale');
        $giftid = 0;
        $goods['cangift']  = false;
        $gifttitle = '';
        if($sale_plugin){
            $giftinfo = array();
            $isgift = 0;
            $gifts = array();
            $giftgoods = array();
            $gifts = pdo_fetchall("select id,goodsid,giftgoodsid,thumb,title from ".tablename('elapp_shop_gift')." where uniacid = ".$_W['uniacid']." and activity = 2 and status = 1 and starttime <= ".time()." and endtime >= ".time()."  ");
            foreach($gifts as $key => &$value){
                $gid = explode(",",$value['goodsid']);
                foreach ($gid as $ke => $val){
                    if($val==$id){
                        $giftgoods = explode(",",$value['giftgoodsid']);
                        foreach($giftgoods as $k => $v){
                            $giftdata = pdo_fetch("select id,title,thumb,marketprice,stock from ".tablename('elapp_shop_goods')." where uniacid = ".$_W['uniacid']." and deleted = 0 and stock > 0 and status = 2 and id = ".$v." ");
                            if(!empty($giftdata)){
                                $isgift = 1;
                                $gifts[$key]['gift'][$k] = $giftdata;
                                $gifts[$key]['gift'][$k]['thumb'] = tomedia( $gifts[$key]['gift'][$k]['thumb']);
                                $gifttitle = !empty($value['gift'][$k]['title']) ? $value['gift'][$k]['title'] : '赠品';
                            }
                        }
                    }
                }
                if(empty($value['gift'])){
                    unset($gifts[$key]);
                }
            }
            if($isgift){
                if($_GPC['cangift']){
                    $goods['cangift'] = true;
                }
                $gifts = array_values($gifts);
                $giftid = $gifts[0]['id'];
                $giftinfo = $gifts;
            }
        }
        $goods['giftid'] = $giftid;
        $goods['giftinfo'] = $giftinfo;
        $goods['gifttitle'] = $gifttitle;
        $goods['gifttotal'] = count($goods['giftinfo']);
        $cycelbuy_sys = m("common")->getSysset("cycelbuy");
        $ahead_goods = intval($cycelbuy_sys["ahead_goods"]);
        if( empty($ahead_goods) ) {
            $ahead_goods = 3;
        }

        // 用户数据差异化处理
        if (p('prescription')) {
            // 检查是否处方商品
            $is_prescription_drug = $id ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescriptionDrug($id) : 0;
            // 如果是处方药进行处理
            if ($is_prescription_drug) {
                $goods['is_prescription_drug'] = 1;
                // 查询该商品是否开方有效时间内
                $prescribe_log = $id && $this->memberId ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescribe($id, $this->memberId) : array();
                if ($prescribe_log['code'] == 0 && !empty($prescribe_log['data'])){
                    $goods['is_prescribe'] = $prescribe_log['data']['is_prescribe'];
                }
            }
        }
        show_json(1, array(
            'goods' => $goods,
            'seckillinfo'=>$seckillinfo,
            'specs' => $specs,
            'options' => $options,
            'diyformhtml'=>$diyformhtml,
            'cremind'=>$cremind,
            'cremindformhtml'=>$cremindformhtml,
            'ahead_goods' =>$ahead_goods,
        ));       
		
    }
    //获取分销商等级
    function getcLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['isagent']) || $member['status']==0 || $member['agentblack'] ==1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_commission_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['agentlevel']));
        return $level;
    }

    function getSet(){
        $set = m('common')->getPluginset('commission');
        $set['texts'] = array(
            'agent' => empty($set['texts']['agent']) ? '分销商' : $set['texts']['agent'],
            'shop' => empty($set['texts']['shop']) ? '小店' : $set['texts']['shop'],
            'myshop' => empty($set['texts']['myshop']) ? '我的小店' : $set['texts']['myshop'],
            'center' => empty($set['texts']['center']) ? '分销中心' : $set['texts']['center'],
            'become' => empty($set['texts']['become']) ? '成为分销商' : $set['texts']['become'],
            'withdraw' => empty($set['texts']['withdraw']) ? '提现' : $set['texts']['withdraw'],
            'commission' => empty($set['texts']['commission']) ? '佣金' : $set['texts']['commission'],
            'commission1' => empty($set['texts']['commission1']) ? '分销佣金' : $set['texts']['commission1'],
            'commission_total' => empty($set['texts']['commission_total']) ? '累计佣金' : $set['texts']['commission_total'],
            'commission_ok' => empty($set['texts']['commission_ok']) ? '可提现佣金' : $set['texts']['commission_ok'],
            'commission_apply' => empty($set['texts']['commission_apply']) ? '已申请佣金' : $set['texts']['commission_apply'],
            'commission_check' => empty($set['texts']['commission_check']) ? '待打款佣金' : $set['texts']['commission_check'],
            'commission_lock' => empty($set['texts']['commission_lock']) ? '未结算佣金' : $set['texts']['commission_lock'],
            'commission_detail' => empty($set['texts']['commission_detail']) ? '提现明细' : ($set['texts']['commission_detail'] == '佣金明细' ? '提现明细' : $set['texts']['commission_detail']),
            'commission_pay' => empty($set['texts']['commission_pay']) ? '成功提现佣金' : $set['texts']['commission_pay'],
            'commission_wait' => empty($set['texts']['commission_wait']) ? '待收货佣金' : $set['texts']['commission_wait'],
            'commission_fail' => empty($set['texts']['commission_fail']) ? '无效佣金' : $set['texts']['commission_fail'],
            'commission_charge' => empty($set['texts']['commission_charge']) ? '扣除提现手续费' : $set['texts']['commission_charge'],
            'order' => empty($set['texts']['order']) ? '分销订单' : $set['texts']['order'],
            'c1' => empty($set['texts']['c1']) ? '一级' : $set['texts']['c1'],
            'c2' => empty($set['texts']['c2']) ? '二级' : $set['texts']['c2'],
            'c3' => empty($set['texts']['c3']) ? '三级' : $set['texts']['c3'],
            'mydown' => empty($set['texts']['mydown']) ? '我的下级' : $set['texts']['mydown'],
            'down' => empty($set['texts']['down']) ? '下级' : $set['texts']['down'],
            'up' => empty($set['texts']['up']) ? '推荐人' : $set['texts']['up'],
            'yuan' => empty($set['texts']['yuan']) ? '元' : $set['texts']['yuan'],
            'icode' => empty($set['texts']['icode']) ? '邀请码' : $set['texts']['icode']
        );
        return $set;
    }
}