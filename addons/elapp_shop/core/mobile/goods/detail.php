<?php
namespace app\controller\goods;
use app\controller\MobilePage;
use app\core\com\logic\member\history\MemberHistoryLogic;
use app\model\GoodsModel;
use app\model\PosterModel;
use app\model\PrescriptionModel;
use app\model\QrcodeModel;
use app\plugin\clerk\core\logic\ClerkLevelLogic;
use app\plugin\prescription\core\logic\PrescriptionCheckGoodsLogic;
use app\plugin\prescription\core\logic\PrescriptionOrderLogic;

class DetailController extends MobilePage {

    function main() {
        global $_W, $_GPC;
        $memberSysset = m('common')->getSysset('member');
        $openid =$_W['openid'];
        $uniacid = $_W['uniacid'];
        $id = intval($_GPC['id']);
        $rank = intval($_GPC['rank']);
        $log_id = intval($_GPC['log_id']);
        $join_id = intval($_GPC['join_id']);
        $task_id = intval($_GPC['task_id']);

        if (!empty($join_id)) {
            $_SESSION[$id . '_rank'] = $rank;
            $_SESSION[$id . '_join_id'] = $join_id;
        }elseif(!empty($log_id)) {
            $_SESSION[$id . '_log_id'] = $log_id;
        }elseif(!empty($task_id)){
            $_SESSION[$id . '_task_id'] = $task_id;
        }

        if (p('task')){
            if (!empty($task_id)){
                $rewarded = pdo_fetchcolumn("SELECT `rewarded` FROM ".tablename('elapp_shop_task_extension_join')." WHERE id = :id AND uniacid = :uniacid",array(':id'=>$task_id,':uniacid'=>$_W['uniacid']));
                $taskGoodsInfo = unserialize($rewarded);
                $taskGoodsInfo = $taskGoodsInfo['goods'][$id];
                if (!empty($taskGoodsInfo['option'])){
                    $taskGoodsInfo = null;
                }
            }
            //新版任务中心
            $taskrewardgoodsid = (int)$_GPC['taskrewardgoodsid'];
            $taskGoodsInfo = pdo_fetch('select price,reward_data,id from '.tablename('elapp_shop_task_reward')." where id = :id and openid = :openid and senttime = 0 and gettime > 0",array(':id'=>$taskrewardgoodsid,':openid'=>$_W['openid']));
            @session_start();
            $_SESSION['taskcut'] = $taskGoodsInfo;
            if (empty($taskGoodsInfo)){
                $_SESSION['taskcut'] = null;
            }
        }

        if (p('threen')){
            $threenvip = p('threen')->getMember($_W['openid']);
            if (!empty($threenvip)){
                $threen = true;
            }
        }
        
        $err = false;
        $member = m('member')->getMember($openid);
        //多商户
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        #分销
        $commission_data = m('common')->getPluginset('commission');
        #虚店店长
        $vrshop_data = m('common')->getPluginset('vrshop');
        #虚店店员
        $clerk_data = m('common')->getPluginset('clerk');
        #虚店合伙人
        $copartner_data = m('common')->getPluginset('copartner');
        #医生
        $doctor_data = m('common')->getPluginset('doctor');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        $merchdata = $this->merchData();
        extract($merchdata);
        //赠品活动
        $isgift = 0;
        $gifts = array();
        $giftgoods = array();
        $gifts = pdo_fetchall("select id,goodsid,giftgoodsid,thumb,title from ".tablename('elapp_shop_gift')." where uniacid = ".$uniacid." and activity = 2 and status = 1 and starttime <= ".time()." and endtime >= ".time()."  ");
        foreach($gifts as $key => &$value){
            $gid = explode(",",$value['goodsid']);
            foreach ($gid as $ke => $val){
                if($val==$id){
                    $giftgoods = explode(",",$value['giftgoodsid']);
                    foreach($giftgoods as $k => $val){
                        $giftinfo = pdo_fetch("select id,title,thumb,marketprice from ".tablename('elapp_shop_goods')." where uniacid = ".$uniacid." and deleted = 0 and stock > 0 and status = 2 and id = ".$val." ");
                        if($giftinfo){
                            $isgift = 1;
                            $gifts[$key]['gift'][$k] = $giftinfo;
                            $gifttitle = !empty($value['gift'][$k]['title']) ? $value['gift'][$k]['title'] : '赠品';
                        }
                    }
                }
            }
            if(empty($value['gift'])){
                unset($gifts[$key]);
            }
        }
        unset($value);

        //商品
        $goods = pdo_fetch("select * from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        //如果是核销商品 那就释放掉赠品
        $goods['medicines'] = unserialize($goods['medicines']);//药品商品信息表
        //获取营销主推级别 Hlei 20210702-->        
        if($goods['isreclevel']==1){
            $reclevelname = com_run('sale::getReclevel', $goods['reclevel']);//传商品reclevel参数
        }        
        if($goods['ispresell']>0 && ($goods['preselltimeend'] == 0 || $goods['preselltimeend'] > time()) && $goods['hasoption']==1){
            //预售商品价格处理
            if(!empty($goods['hasoption'])){
                $presell = pdo_fetch("select min(presellprice) as minprice,max(presellprice) as maxprice from ".tablename('elapp_shop_goods_option')." where goodsid = ".$id);
                $goods['minpresellprice'] = $presell['minprice'];
                $goods['maxpresellprice'] = $presell['maxprice'];
            }
        }
        //获取全局设置的[宝贝]文本名称 Hlei 20220415
        $tradeSysset = m('common')->getSysset('trade');
        $goodsNameText = empty($tradeSysset['goodsNameText'])?'宝贝':$tradeSysset['goodsNameText'];
        /**
         * 会员价格权益显示
         * 提示会员升级成为XX等级会员可享受XX价格 显示 /着急写，逻辑不一定完全正确 抽空验证
         * 
         */

        //没有推荐人不显示会员权益信息 默认显示
        $is_not_show_member_equity = 0;
        $not_show_member_equity = empty($memberSysset['not_show_member_equity']) ? 0 : $memberSysset['not_show_member_equity'];
        if(empty($member['onmid']) && $not_show_member_equity == 1){
            $is_not_show_member_equity = 1;
        }

        $isMembeCanSeeVip = false;//默认关闭
        $memberPriceText = empty($memberSysset['memberPriceText'])?'会员价':$memberSysset['memberPriceText'];//会员价文本
        $memberCardText = empty($memberSysset['memberCardText'])?'会员卡':$memberSysset['memberCardText']; //会员卡文本

        if(!empty($memberSysset['isMembeCanSeeVipText'])){
            $isMembeCanSeeVip = true;
        }else{
            if(empty($goods['isMembeCanSeeVipText']) || $goods['isMembeCanSeeVipText'] == 0){
                $isMembeCanSeeVip = false;               
            }elseif(!empty($goods['isMembeCanSeeVipText']) && ($goods['hasMembeCanSeeVip'] == 0 || empty($goods['hasMembeCanSeeVip']))){
                $isMembeCanSeeVip = true;              
            }elseif(!empty($goods['isMembeCanSeeVipText']) && !empty($goods['hasMembeCanSeeVip'])){
                $isMembeCanSeeVip = true;
                $memberPriceText = empty($goods['memberPriceText'])?$memberSysset['memberPriceText']:$goods['memberPriceText'];
                $memberCardText = empty($goods['memberCardText'])?$memberSysset['memberCardText']:$goods['memberCardText'];
            }else{
                $isMembeCanSeeVip = false;
            }
        }        

        $form_type = $goods['medicines']['formType'];

        if( $goods['isverify'] == 2 ){
            unset( $gifts );
        }        

        /*周期购商品详情*/
        if($goods['type'] == 9) {
            header('location: ' . mobileUrl('cycelbuy/goods/detail/main', array('id' => $goods['id'])));
            exit;
        }
        /*文案*/
        if(p("offic")){
            $marketprice = $goods['marketprice'];
            $goods['marketprice'] = $goods['minprice'];
            $commission_price = p("commission")->getCommission($goods);
            $goods['marketprice'] = $marketprice;
        }
        //多商户暂停不销售商品
        if($is_openmerch==1){
            $set =  m('plugin')->loadModel('merch')->getListUserOne($goods['merchid']);
            if($set['status']!=1){
                $is_openmerch=0;
            }
        }
        $threenprice = json_decode($goods['threen'],1);
        if($goods['ispresell']>0 && (($goods['presellend'] > 0 && $goods['preselltimeend'] > time()) || ($goods['preselltimeend'] == 0))){
            $goods['minprice'] = $goods['presellprice'];
            if($goods['hasoption']==0){
                $goods['maxprice'] = $goods['presellprice'];
            }
        }
        if($goods['type'] == 30) {
            header('location: ' . mobileUrl('newstore/trade/detail', array('id' => $goods['id'])));
            exit;
        }
        if($goods['type']==4){
            $intervalprice = iunserializer($goods['intervalprice']);
            if($goods['intervalfloor']>0){
                $goods['intervalprice1']=$intervalprice[0]['intervalprice'];
                $goods['intervalnum1']=$intervalprice[0]['intervalnum'];
            }
            if($goods['intervalfloor']>1){
                $goods['intervalprice2']=$intervalprice[1]['intervalprice'];
                $goods['intervalnum2']=$intervalprice[1]['intervalnum'];
            }
            if($goods['intervalfloor']>2){
                $goods['intervalprice3']=$intervalprice[2]['intervalprice'];
                $goods['intervalnum3']=$intervalprice[2]['intervalnum'];
            }
        }

        //记次时商品核销时间限制提示
        $timeout = false;  //超过时间限制
        $access_time = false;  //临近时间限制
        if($goods['type'] == 5){
            if($goods['verifygoodslimittype'] == 1){
                $limittime = $goods['verifygoodslimitdate'];
                $now = time();
                if($limittime < time()){
                    $timeout = true;
                    $hint = '您选择的记次时商品的使用时间已经失效，无法购买！';
                }else if(($limittime-$now) > 1800 && ($limittime-$now) < 7200){
                    $access_time = true;
                    $hint = '您选择的记次时商品到期日期是"'.date('Y-m-d H:i:s',$limittime).'",请确保有足够的时间抵达核销门店进行核销，以免耽误您的使用。';
                }else if(($limittime-$now) < 1800){
                    $timeout = true;
                    $hint = '您选择的记次时商品的使用时间即将失效，无法购买！';
                }
            }
        }

        //是否全返商品
        $isfullback = false;
        if($goods['isfullback']){
            $isfullback = true;
            $fullbackgoods = pdo_fetch("SELECT * FROM ".tablename('elapp_shop_fullback_goods')." WHERE uniacid = ".$uniacid." and goodsid = ".$id." and status=1 limit 1 ");
            if(empty($fullbackgoods)){
                $isfullback = false;
            }else{
                if($goods['hasoption']==1){
                    $fullprice = pdo_fetch("select min(allfullbackprice) as minfullprice,max(allfullbackprice) as maxfullprice,min(allfullbackratio) as minfullratio
                            ,max(allfullbackratio) as maxfullratio,min(fullbackprice) as minfullbackprice,max(fullbackprice) as maxfullbackprice
                            ,min(fullbackratio) as minfullbackratio,max(fullbackratio) as maxfullbackratio,min(`day`) as minday,max(`day`) as maxday
                            from ".tablename('elapp_shop_goods_option')." where goodsid = ".$id."");
                    $fullbackgoods['minallfullbackallprice'] = $fullprice['minfullprice'];
                    $fullbackgoods['maxallfullbackallprice'] = $fullprice['maxfullprice'];
                    $fullbackgoods['minallfullbackallratio'] = $fullprice['minfullratio'];
                    $fullbackgoods['maxallfullbackallratio'] = $fullprice['maxfullratio'];
                    $fullbackgoods['minfullbackprice'] = $fullprice['minfullbackprice'];
                    $fullbackgoods['maxfullbackprice'] = $fullprice['maxfullbackprice'];
                    $fullbackgoods['minfullbackratio'] = $fullprice['minfullbackratio'];
                    $fullbackgoods['maxfullbackratio'] = $fullprice['maxfullbackratio'];
                    $fullbackgoods['fullbackratio'] = $fullprice['minfullbackratio'];
                    $fullbackgoods['fullbackprice'] = $fullprice['minfullbackprice'];
                    $fullbackgoods['minday'] = $fullprice['minday'];
                    $fullbackgoods['maxday'] = $fullprice['maxday'];
                }else{
                    $fullbackgoods['maxallfullbackallprice'] = $fullbackgoods['minallfullbackallprice'];
                    $fullbackgoods['maxallfullbackallratio'] = $fullbackgoods['minallfullbackallratio'];
                    $fullbackgoods['minday'] = $fullbackgoods['day'];
                }
            }
        }

        $merchid = $goods['merchid'];
        if(json_decode($goods['labelname'],true)){
            $labelname = json_decode($goods['labelname'],true);
        }else{
            $labelname = unserialize($goods['labelname']);
        }
        $style = pdo_fetch("SELECT id,uniacid,style FROM " . tablename('elapp_shop_goods_labelstyle') . " WHERE uniacid=" . $uniacid);

        if ($is_openmerch == 0) {
            //未开启多商户的情况下,此商品是否有多商户的商品
            if ($merchid > 0) {
                $err = true;
                include $this->template('goods/detail');

                exit;
            }
        } else {
            //判断多商户商品是否通过审核
            if ($merchid > 0 && $goods['checked'] == 1) {
                $err = true;
                include $this->template('goods/detail');
                exit;
            }
        }

        $member = m('member')->getMember($openid);
        if(empty($member['updateaddress'])) {
            $address_list = pdo_fetchall('select id,datavalue from ' . tablename('elapp_shop_member_address') . ' where openid=:openid and uniacid=:uniacid'
                , array(':uniacid' => $uniacid, ':openid' => $openid));
            if (!empty($address_list)) {
                $areas = m('common')->getAreas();
                $datacode = array();
                foreach ($areas['province'] as $value) {
                    $pname = $value['@attributes']['name'];
                    $pcode = $value['@attributes']['code'];
                    $datacode[$pcode] = $pname;
                    foreach ($value['city'] as $city) {
                        $cname = $city['@attributes']['name'];
                        $ccode = $city['@attributes']['code'];
                        $datacode[$ccode] = $cname;
                        if (is_array($city['county'])) {
                            foreach ($city['county'] as $county) {
                                $aname = $county['@attributes']['name'];
                                $acode = $county['@attributes']['code'];
                                $datacode[$acode] = $aname;
                            }
                        }
                    }
                }
                $change_data = array();
                foreach ($address_list as $k1 => $v1) {
                    if(!empty($v1['datavalue'])) {
                        $datavalue = explode(' ',$v1['datavalue']);
                        $change_data['province'] = $datacode[$datavalue[0]];
                        $change_data['city'] = $datacode[$datavalue[1]];
                        $change_data['area'] = $datacode[$datavalue[2]];
                        if (!empty($change_data['province']) && !empty($change_data['city']) && !empty($change_data['area'])) {
                            pdo_update('elapp_shop_member_address', $change_data, array('id' => $v1['id']));
                        }
                    }
                }
                pdo_update('elapp_shop_member', array('updateaddress' => 1), array('id' => $member['id']));
            }
        }
   
        $showgoods = m('goods')->visit($goods, $member);
        if (empty($goods)){
            $err = true;
            include $this->template();
            exit;
        }
        if(empty($showgoods)){
            $this->message(array(
                'message' => '您当前会员等级无浏览权限，去商城逛逛吧~',
                'buttontext' => '去逛逛'
            ), mobileUrl());
        }

        $seckillinfo = false;
        $seckill  = p('seckill');
        if($seckill){
            $time = time();
            $seckillinfo = $seckill->getSeckill($goods['id'],0,false, $_W['openid']);
            if(!empty($seckillinfo)){
                if($time >= $seckillinfo['starttime'] && $time<$seckillinfo['endtime']){
                    $seckillinfo['status'] = 0;
                    unset($_SESSION[$id . '_log_id']);
                    unset($_SESSION[$id . '_task_id']);
                    //unset($log_id);
                }elseif( $time < $seckillinfo['starttime'] ){
                    $seckillinfo['status'] = 1;
                }else {
                    $seckillinfo['status'] = -1;
                }
            }
        }
        //任务活动购买商品
        $task_goods_data = m('goods')->getTaskGoods($openid, $id, $rank, $log_id, $join_id);
        if (empty($task_goods_data['is_task_goods'])) {
            $is_task_goods = 0;
            if (p('bargain') && !is_weixin()){
                //如果是砍价商品,自动跳转
                $bargain = pdo_fetch("SELECT * FROM ".tablename('elapp_shop_bargain_goods')." WHERE id = :id AND unix_timestamp(start_time)<".time()." AND unix_timestamp(end_time)>".time()." AND status = 0",array(':id'=>$goods['bargain']));
                if ($bargain!=false){echo "<script>window.location.href = '".mobileUrl('bargain/detail',array('id'=>$goods['bargain']))."'</script>";return;}
            }
        } else {
            $is_task_goods = $task_goods_data['is_task_goods'];
            $is_task_goods_option = $task_goods_data['is_task_goods_option'];
            $task_goods = $task_goods_data['task_goods'];
        }
        $goods['sales'] = $goods['sales'] + $goods['salesreal'];
        $goods['content'] = m('ui')->lazy($goods['content']);
        $buyshow = 0;
        if ($goods['buyshow'] == 1) {
            $sql = "select o.id from " . tablename('elapp_shop_order') . " o left join " . tablename('elapp_shop_order_goods') . " g on o.id = g.orderid";
            $sql .= " where o.openid=:openid and g.goodsid=:id and o.status>0 and o.uniacid=:uniacid limit 1";
            $buy_goods = pdo_fetch($sql, array(':openid' => $openid, ':id' => $id, ':uniacid' => $_W['uniacid']));
            if (!empty($buy_goods)) {
                $buyshow = 1;
                $goods['buycontent'] = m('ui')->lazy($goods['buycontent']);
            }
        }
        $goods['unit'] = empty($goods['unit'])?'件':$goods['unit'];
        //使用的快递是否有特殊配送区域
        $dispatch_areas = m('dispatch')->getNoDispatchAreas($goods);
        $citys = empty($dispatch_areas) ? '' : $dispatch_areas['citys'];
        if (!empty($citys)&&$dispatch_areas['enabled']) {
            $onlysent = $dispatch_areas['onlysent'];
            $has_city = 1;
        } else {
            $has_city = 0;
        }
        //套餐
        $package_goods = pdo_fetch("select pg.id,pg.pid,pg.goodsid,p.displayorder from ".tablename('elapp_shop_package_goods')." as pg
                        left join ".tablename('elapp_shop_package')." as p on pg.pid = p.id
                        where pg.uniacid = ".$uniacid." and pg.goodsid = ".$id." and  p.starttime <= ".time()." and p.endtime >= ".time()." and p.deleted = 0 and p.status = 1 ORDER BY p.displayorder desc,pg.id desc limit 1 ");
        if($package_goods['pid']){
            $packages = pdo_fetchall("SELECT id,title,thumb,packageprice FROM ".tablename('elapp_shop_package_goods')."
                    WHERE uniacid = ".$uniacid." and pid = ".$package_goods['pid']."  ORDER BY id DESC");
            $packages = set_medias($packages,array('thumb'));
        }
        //运费
        $goods['dispatchprice'] = $this->getGoodsDispatchPrice($goods,$seckillinfo);
        //幻灯片
        $thumbs = iunserializer($goods['thumb_url']);
        if(empty($thumbs)){
            $thumbs = array( $goods['thumb'] );
            if (!empty($goods['thumb_first'])&&!empty($goods['thumb'])) {
                $thumbs =array_merge( array($goods['thumb']), $thumbs );
            }
            if(is_array($thumbs)&& count($thumbs)==2 && !empty($goods['thumb_first'])){
                $thumbs =  array_unique($thumbs);
            }
            $thumbs = array_values($thumbs);
        }else{
            if (!empty($goods['thumb_first'])&&!empty($goods['thumb'])) {
                $thumbs =array_merge( array($goods['thumb']), $thumbs );
            }
            $thumbs = array_values($thumbs);
        }

        $shopSysset = m('common')->getSysset('shop');

        if (p('prescription')) {
            // 检查是否处方商品
            $is_prescription_drug = $id ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescriptionDrug($id) : 0;
            $is_prescribe = $isRxGoodsPicOpacity = 0;//未开过处方/处方药图片不开启模糊效果
            // 如果是处方药进行处理
            if ($is_prescription_drug) {
                // 查询该商品是否开方有效时间内
                $prescribe_log = $id && $this->memberId ? app(PrescriptionCheckGoodsLogic::class)->checkGoodsIsPrescribe($id, $this->memberId) : array();
                $prescribe = $prescribe_log['data'];
                $is_prescribe = $prescribe['is_prescribe'];
                // 处方药图片模糊处理
                if ($shopSysset['isRxGoodsPicOpacity'] == 1) {
                    // 处方药图片模糊处理 如果已开过处方则不开启模糊效果
                    $isRxGoodsPicOpacity = $is_prescribe ? 0 : 1;
                }
            }
        }
        //规格specs
        $specs = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_spec') . " where goodsid=:goodsid and  uniacid=:uniacid order by displayorder asc", array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
        $spec_titles = array();
        foreach ($specs as $key => $spec) {
            if ($key >= 2) {
                break;
            }
            $spec_titles[] = $spec['title'];
        }
        if($goods['hasoption']>0){
            $spec_titles = implode('、', $spec_titles);
        }else{
            $spec_titles = '';
        }

        //参数
        $params = pdo_fetchall("SELECT * FROM " . tablename('elapp_shop_goods_param') . " WHERE uniacid=:uniacid and goodsid=:goodsid order by displayorder asc", array(':uniacid' => $uniacid, ":goodsid" => $goods['id']));

        $goods = set_medias($goods, 'thumb');
        //$goods['canbuy'] = !empty($goods['status']) && empty($goods['deleted']);
        $goods['canbuy'] = $goods['status'] == 1 && empty($goods['deleted']);

        if (!empty($goods['hasoption'])){
            $options = pdo_fetchall('select id,stock,marketprice,islive,liveprice from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $goods['id'], ':uniacid' => $_W['uniacid']),'stock');
            $options_stock = array_keys($options);
            //if($options_stock){
            //    $goods['stock'] = max($options_stock);
            //}else{
            //    $goods['stock'] = 0;
            //}
        }

        /* 直播价格计算 */
        $liveid = intval($_GPC['liveid']);
        if(p('live') && !empty($liveid)){
            $islive = p('live')->getLivePrice($goods, $liveid);
            if($islive){
                $goods['minprice'] = $islive['minprice'];
                $goods['maxprice'] = $islive['maxprice'];
                if(empty($options)){
                    $goods['marketprice'] = $islive['minprice'];
                }
            }
        }
        $liveid = !empty($islive) && !empty($liveid)? $liveid :0;

        if ($goods['stock'] <= 0) {
            $goods['canbuy'] = false;
        }
        $ispresell = 0;
        if($goods['ispresell'] > 0){
            $ispresell = 1;
            if($goods['preselltimestart'] > 0 && $goods['preselltimestart'] > time()){
                $goods['canbuy'] = false;
            }
            if($goods['preselltimeend'] > 0 && $goods['preselltimeend'] < time()){
                $goods['canbuy'] = false;
            }
            //预售结束转为正常销售
            $times = $goods['presellovertime'] * 60 * 60 * 24 + $goods['preselltimeend'];
            if($goods['presellover']>0 && $times <= time()){
                $goods['canbuy'] = true;
                $ispresell = 0;
            }
        }

        // 判断980活动用户是否绑定推广用户
        if ($goods['activity_id'] == 1 && $member['onmid']==0) {
            $goods['canbuy'] = false;
        }

        if ($goods['isverify']==2 && $goods['isendtime'] > 0 && $goods['endtime'] > 0 && $goods['endtime'] < time()) {
            $goods['canbuy'] = false;
            $goods['overdue']=true;
        }

        $goods['timestate'] = '';

        //判断用户最大购买量
        $goods['userbuy'] = '1';
        if ($goods['usermaxbuy'] > 0) {
            $order_goodscount = pdo_fetchcolumn('select ifnull(sum(og.total),0)  from ' . tablename('elapp_shop_order_goods') . ' og '
                . ' left join ' . tablename('elapp_shop_order') . ' o on og.orderid=o.id '
                . ' where og.goodsid=:goodsid and  o.status>=1 and o.openid=:openid  and og.uniacid=:uniacid ', array(':goodsid' => $goods['id'], ':uniacid' => $uniacid, ':openid' => $openid));
            if ($order_goodscount >= $goods['usermaxbuy']) {
                $goods['userbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }
        $levelid = $member['level'];
        $org_id = $member['org_id'];
        if ($member['groupid'] == ''){
            $groupid = array();
        }else{
            $groupid = explode(',',$member['groupid']);
        }

        //获取所有会员等级
        $levels = m('member')->getLevels();
        foreach($levels as &$l){
            $l['key'] ='level'.$l['id'];
        }
        unset($l);
        $levels =array_merge(array(
            array(
                'id'=>0,
                'key'=>'default',
                'levelname'=>empty($_W['shopset']['shop']['levelname'])?'默认会员':$_W['shopset']['shop']['levelname']
            )
        ),$levels);
        $tradeSet = m('common')->getSysset('trade');//获取全局交易设置会员购买权限
        //判断会员权限 先判断商品设置 商品高于全局权重
        $goods['levelbuy'] = '1';
        //商品独立会员等级购买权限
        $buylevels = array();
        if ($goods['buylevels'] != '') {
            $buylevels = explode(',', $goods['buylevels']);
            if (!in_array($levelid, $buylevels)) {
                $goods['levelbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }else if($tradeSet['buylevels'] != ''){
            //全局商品会员等级购买权限
            $buylevels = explode(',', $tradeSet['buylevels']);
            if (!in_array($levelid, $buylevels)) {
                $goods['levelbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }
        //限制购买权限的会员等级名称
        foreach($levels as $lv){
            if (in_array($lv['id'], $buylevels)) {
                $goods['nobuylevelname'] = $lv['levelname'];
            }
        }
        //会员组商品购买权限
        $goods['groupbuy'] = '1';
        if ($goods['buygroups'] != '' && !empty($groupid)) {
            $buygroups = explode(',', $goods['buygroups']);
            $intersect = array_intersect($groupid, $buygroups);
            if (empty($intersect)) {
                $goods['groupbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }elseif($tradeSet['buygroups'] != ''){
            //全局商品会员组购买权限
            $buylevels = explode(',', $tradeSet['buygroups']);
            if (!in_array($levelid, $buylevels)) {
                $goods['groupbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }
        $goods['orgbuy'] = '1';
        $buyOrgs = array();
        if ($goods['buyOrgs'] != '') {
            $buyOrgs = explode(',', $goods['buyOrgs']);
            if (!in_array($org_id, $buyOrgs)) {
                $goods['orgbuy'] = 0;
                $goods['canbuy']  = false;
            }
        }
        $orgs = pdo_fetchall('select * from ' . tablename('elapp_shop_org_user') . ' where uniacid=:uniacid ', array(':uniacid' => $_W['uniacid']));
        foreach($orgs as $org){
            if (in_array($org['id'], $buyOrgs)) {
                $goods['nobuyorgname'] = $org['orgname'];
            }
        }
        //判断限时购
        $goods['timebuy'] = '0';
        if(empty($seckillinfo)) {
            if ($goods['istime'] == 1) {
                if (time() < $goods['timestart']) {
                    $goods['timebuy'] = '-1';
                    $goods['canbuy'] = false;
                } else if (time() > $goods['timeend']) {
                    $goods['timebuy'] = '1';
                    $goods['canbuy'] = false;
                }
            }
        }

        if(com('coupon')){
            $coupons =$this->getCouponsbygood($goods['id']);
        }

        //是否可以加入购物车
        $canAddCart = true;
        if ($goods['isverify'] == 2 || $goods['type'] == 2 || $goods['type'] == 3 || $goods['type'] == 20 || !empty($is_task_goods) || !empty($gifts) || (!empty($seckillinfo) && empty($seckillinfo['can_add_cart'])) || empty($goods['can_add_cart'])) {
            $canAddCart = false;
        }
        if ($goods['type'] == 2 && empty($specs)) {
            $gflag = 1;
        } else {
            $gflag = 0;
        }
        //营销活动
        $enoughs = com_run('sale::getEnoughs'); //满额立减
        $fullenoughs = com_run('sale::getFullenoughs'); //满件优惠方法
        $fullenoughdata = m('common')->getPluginset('sale');//获取sale数据
        $fullenoughopen = $fullenoughdata['fullenoughopen'];//获取满件优惠开关
        $goods_nofree = com_run('sale::getEnoughsGoods');//不参与满额包邮商品 Hlei20210504
        $goods_nofull = com_run('sale::getFullenoughsGoods');//不参与满件优惠商品 Hlei20210504
        if (empty($is_task_goods)) {
            $enoughfree = com_run('sale::getEnoughFree'); //满额包邮
        }
        if ($is_openmerch == 1 && $goods['merchid'] > 0) {
            $merch_set = $merch_plugin->getSet('sale', $goods['merchid']);
            if ($merch_set['enoughfree']){
                $enoughfree = $merch_set['enoughorder'];
                if ($merch_set['enoughorder'] == 0){
                    $enoughfree = -1;
                }
            }
        }
        //合并第一条多商户满减数据
        $one=array(array('enough'=>$merch_set['enoughmoney'],'give'=>$merch_set['enoughdeduct']));
        $merchenoughs = $merch_set['enoughs'];
        if(empty($merchenoughs)) {
            $merchenoughs =array();
        }
        $merch_set['enoughs']=array_merge_recursive($one,$merchenoughs);
        //满额包邮
        if(!empty($goods_nofree)) {
            if (in_array($id, $goods_nofree)) {
                $enoughfree = false;
            }
        }
        //满件优惠商品
        if(!empty($goods_nofull)) {
            if (in_array($id, $goods_nofull)) {
                $fullenoughs = false;//如果数组存在，则满件优惠为假
            }
        }
        //运费
        if ($enoughfree && $goods['minprice'] > $enoughfree && empty($seckillinfo)) {
            $goods['dispatchprice'] = 0;
        }
        $hasSales = false;
        if ($goods['ednum'] > 0 || $goods['edmoney'] > 0) {
            $hasSales = true;
        }
        if ($enoughfree || ($enoughs && count($enoughs) > 0) || $fullenoughs && count($fullenoughs) > 0 && $fullenoughopen ==1) {
            $hasSales = true;
        }
        //价格显示
        $minprice = $goods['minprice']; 
        $maxprice = $goods['maxprice'] ;
        //会员等级
        $level = m('member')->getLevel($openid);
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        if (empty($is_task_goods)) {
            $memberprice = m('goods')->getMemberPrice($goods, $level);
            $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);//最大会员等级价格
            // 解析 $maxMemberLevelPrice 字符串
            if (is_string($memberprice) && strpos($memberprice, '~') !== false) {
                list($minMemberprice, $maxMemberprice) = explode('~', $memberprice);
            }
        }
        if($goods['isdiscount'] && $goods['isdiscount_time']>=time() && $goods['isdiscount_time_start'] < time()){
            $goods['oldmaxprice'] = $maxprice;
            $prices = array();
            $isdiscount_discounts = json_decode($goods['isdiscount_discounts'],true);
            if (!isset($isdiscount_discounts['type']) || empty($isdiscount_discounts['type'])) {
                //统一促销
                $prices_array = m('order')->getGoodsDiscountPrice($goods, $level, 1);
                $prices[] = $prices_array['price'];
            } else {
                //详细促销
                $goods_discounts = m('order')->getGoodsDiscounts($goods, $isdiscount_discounts, $levelid);
                $prices = $goods_discounts['prices'];
            }
            $goods['isdiscount_price'] = min($prices); //促销价格
            $minprice = min($prices);
            $maxprice = max($prices);
        }else{
            if ( isset($options) && count($options) > 0 && $goods['hasoption']) {
                $optionids = array();
                foreach ($options as $val){
                    $optionids[] = $val['id'];
                }
                //更新最低价和最高价
                $sql = "update ".tablename('elapp_shop_goods')." g set g.minprice = (select min(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id), g.maxprice = (select max(marketprice) from ".tablename('elapp_shop_goods_option')." where goodsid = $id) where g.id = $id and g.hasoption=1";
                pdo_query($sql);
            } else {
                $sql = "update ".tablename('elapp_shop_goods')." set minprice = marketprice,maxprice = marketprice where id = $id and hasoption=0;";
                pdo_query($sql);
            }
            $goods_price = pdo_fetch("select minprice,maxprice from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", array(':id' => $id, ':uniacid' => $_W['uniacid']));
            $maxprice = (float)$goods_price['maxprice'];
            $minprice = (float)$goods_price['minprice'];
            /* 直播价格计算 */
            if($islive){
                $minprice = $islive['minprice'];
                $maxprice = $islive['maxprice'];
            }
        }

        if (!empty($is_task_goods)) {
            if ( isset($options) && count($options) > 0 && $goods['hasoption']) {
                $prices = array();
                foreach ($task_goods['spec'] as $k => $v) {
                    $prices[] = $v['marketprice'];
                }
                $minprice2 = min($prices);
                $maxprice2 = max($prices);
                if ($minprice2 < $minprice) {
                    $minprice = $minprice2;
                }
                if ($maxprice2 > $maxprice) {
                    $maxprice = $maxprice2;
                }
            } else {
                $minprice = $task_goods['marketprice'];
                $maxprice = $task_goods['marketprice'];
            }
        }
        if($goods['ispresell']>0 && $goods['hasoption'] && ($goods['preselltimeend'] == 0 || $goods['preselltimeend'] > time())){
            $presell = pdo_fetch("select min(presellprice) as minprice,max(presellprice) as maxprice from ".tablename('elapp_shop_goods_option')." where goodsid = ".$id);
            $minprice = $presell['minprice'];
            $maxprice = $presell['maxprice'];
        }
        //如果是多规格，获取规格原价最大值
        if($goods['hasoption']>0){
            $productprice = pdo_fetchcolumn("select max(productprice) as productprice from ".tablename('elapp_shop_goods_option')." where goodsid = :goodsid",array(':goodsid'=>$id));
            if(!empty($productprice)){
                $goods['productprice']=$productprice;
            }
        }

        $goods['minprice'] = $minprice;
        $goods['maxprice'] =$maxprice;
        //是否显示商品评论
        $getComments = empty($_W['shopset']['trade']['closecommentshow']);
        //是否有配套服务
        $hasServices = $goods['cash'] || $goods['seven'] || $goods['repair'] || $goods['invoice'] || $goods['quality'];
        //是否收藏了
        $isFavorite = m('goods')->isFavorite($id);
        //购物车数量
        $cartCount = m('goods')->getCartCount();
        //浏览量 + 浏览记录
        if (!empty($this->memberId)) {
            app(MemberHistoryLogic::class)->addHistory($id, $this->memberId);
        }
        //店铺信息
        $shop = set_medias(m('common')->getSysset('shop'), 'logo');
        $shop['is_show_revenue'] = pdo_getcolumn('elapp_shop_clerk_shop', array('uniacid' => $_W['uniacid'], 'mid' => $member['id']), 'is_show_revenue');
        $shop['url'] = mobileUrl('',null,true);
        $mid = intval($_GPC['mid']);
        //判断是否开启分销
        $opencommission = false;
        if (p('commission')) {
            if (empty($member['agentblack'])) { //不在黑名单
                $cset = p('commission')->getSet();
                $opencommission = intval($cset['level']) > 0;
                /*
                 * 用户是否为分销商
                 * */
                if ($opencommission) {
                    if ($member['isagent'] == 1 && $member['status'] == 1) {
                        $mid = $member['id'];
                    }
                    if (!empty($mid)) {
                        if (empty($cset['closemyshop'])) {
                            $shop = set_medias( p('commission')->getShop($mid), 'logo');
                            $shop['url'] = mobileUrl('commission/myshop/index/main', array('mid' => $mid),true);
                        }
                    }
                }
            }
        }
        //判断是否开启虚店店员
        $openclerk = false;
        if (p('clerk')) {
            if (empty($member['clerk_black'])) { //不在黑名单
                $clerk_set = p('clerk')->getSet();
                $openclerk = intval($clerk_set['level']) > 0;
                //用户是否为店员
                if ($openclerk) {
                    if ($member['is_clerk'] == 1 && $member['clerk_status'] == 1) {
                        $mid = $member['id'];
                    }
                    if (!empty($mid)) {
                        if (empty($clerk_set['closemyshop'])) {
                            $shop = set_medias( p('clerk')->getShop($mid), 'logo');
                            $shop['url'] = mobileUrl('clerk/myshop/index/main', array('mid' => $mid),true);
                        }
                    }
                }
            }
        }
        /*
         * 分销文案
         * 
         * */
        $is_offic = false;
        if(p("offic")){
            //判断用户是否为分销商，素材仅分销商可见
            if ($member['isagent'] == 1 && $member['status'] == 1) {
                $is_offic = true;
                $shop['url'] = mobileUrl('offic/myshop/index/main', array('mid' => $mid),true);
            }
        }
        
        $is_clerk = false;
        $is_owner = false;
        $is_copartner = false;
        if(p("clerk")){
            //判断用户是否为店员，查看商品预计收益仅店员可见
            if ($member['is_clerk'] == 1 && $member['clerk_status'] == 1) {
                $is_clerk = true;
                $shop['url'] = mobileUrl('clerk/myshop/index/main', array('mid' => $mid),true);
            }
            if ($member['is_owner'] == 1 && $member['owner_status'] == 1) {
                $is_owner = true;
            }
            if ($member['is_copartner'] == 1 && $member['copartner_status'] == 1) {
                $is_copartner = true;
            }
        }
        /*
         * 分销文案
         * 插件
         * */
        $shopGoodsTotals = m('goods')->getTotals();//平台商品数量
        $merchGoodsTotals = p('merch')->getGoodsTotalsAll($goods['merchid']);//商户商品数量
        $merchFollowTotal = p('merch')->getFollowTotal($goods['merchid']);  //商户关注粉丝量
        $open_redis = function_exists('redis') && !is_error(redis());        
        if($open_redis) {
            $redis_key = "elapp_{$_W['uniacid']}_member_list";
            $memberTotals = m('member')->memberRadisCount($redis_key,false);
            if(!$memberTotals){
                $memberTotals = pdo_fetchcolumn("select count(*) from" . tablename('elapp_shop_member') . " dm join " . tablename('mc_mapping_fans') . " f on f.openid=dm.openid where 1 and dm.uniacid=:uniacid ", array(':uniacid' => $_W['uniacid']));
                m('member') -> memberRadisCount($redis_key,$memberTotals);
            }
        }
        if (empty($this->merch_user)) {
            $merch_flag = 0;
            if ($is_openmerch == 1 && $goods['merchid'] > 0) {
                $merch_user = pdo_fetch("select * from ".tablename('elapp_shop_merch_user')." where id=:id limit 1",array(':id'=>intval($goods['merchid'])));
                if (!empty($merch_user)) {
                    $shop = $merch_user;
                    $merch_flag = 1;
                }
            }
            $shop['description'] = empty($shop['desc'])?$shop['description']:$shop['desc'];
            
            if ($merch_flag == 1) { 
                               
                $shopdetail = array(
                    'logo' => !empty($goods['detail_logo']) ? tomedia($goods['detail_logo']) : tomedia($shop['logo']),
                    'shopname' => !empty($goods['detail_shopname']) ? $goods['detail_shopname'] : $shop['merchname'],
                    'description' =>!empty($goods['detail_totaltitle']) ? $goods['detail_totaltitle'] : $shop['description'],
                    'btntext1' => trim($goods['detail_btntext1']),
                    'btnurl1' => !empty($goods['detail_btnurl1']) ? $goods['detail_btnurl1'] : mobileUrl('goods'),
                    'btntext2' => trim($goods['detail_btntext2']),
                    'btnurl2' => !empty($goods['detail_btnurl2']) ? $goods['detail_btnurl2'] : mobileUrl('merch',array('merchid'=> $goods['merchid'])),
                    'goodstotals' => $merchGoodsTotals,
                    'memberTotals' => $merchFollowTotal
                );
            } else {
                $shopdetail = array(
                    'logo' => !empty($goods['detail_logo']) ? tomedia($goods['detail_logo']) : $shop['logo'],
                    'shopname' => !empty($goods['detail_shopname']) ? $goods['detail_shopname'] : $shop['name'],
                    'description' =>!empty($goods['detail_totaltitle']) ? $goods['detail_totaltitle'] : $shop['description'],
                    'btntext1' => trim($goods['detail_btntext1']),
                    'btnurl1' => !empty($goods['detail_btnurl1']) ? $goods['detail_btnurl1'] : mobileUrl('goods'),
                    'btntext2' => trim($goods['detail_btntext2']),
                    'btnurl2' => !empty($goods['detail_btnurl2']) ? $goods['detail_btnurl2'] : $shop['url'],
                    'goodstotals' => $shopGoodsTotals['sale'],
                    'memberTotals' => $memberTotals
                );
            }
            $param = array(':uniacid'=>$_W['uniacid']);
            if ($merch_flag == 1) {
                $sqlcon = " and merchid=:merchid";
                $param[':merchid'] = $goods['merchid'];
            }
            //统计
            if (empty($shop['selectgoods'])) {
                $statics = array(
                    'all'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid {$sqlcon} and status=1 and deleted=0", $param),
                    'new'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid {$sqlcon} and isnew=1 and status=1 and deleted=0", $param),
                    'discount'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid {$sqlcon} and isdiscount=1 and status=1 and deleted=0", $param)
                );
            } else {
                $goodsids = explode(",", $shop['goodsids']);
                $goodsids = array_filter($goodsids);
                $shop['goodsids'] = implode(",",$goodsids);
                $statics = array(
                    'all'=>count($goodsids),
                    'new'=>pdo_fetchcolumn("select count(1) from ".tablename('elapp_shop_goods')." where uniacid=:uniacid {$sqlcon} and id in(".$shop['goodsids'].") and isnew=1 and status=1 and deleted=0", $param),
                    'discount'=>pdo_fetchcolumn("select count(1) from ".tablename('elapp_shop_goods')." where uniacid=:uniacid {$sqlcon} and id in(".$shop['goodsids'].") and isdiscount=1 and status=1 and deleted=0", $param)
                );
            }
        } else {
            if ($goods['checked'] == 1) {
                $err = true;
                include $this->template();
                exit;
            }
            $shop = $this->merch_user;
            $shopdetail = array(
                'logo' => !empty($goods['detail_logo']) ? tomedia($goods['detail_logo']) : tomedia($shop['logo']),
                'shopname' => !empty($goods['detail_shopname']) ? $goods['detail_shopname'] : $shop['merchname'],
                'description' =>!empty($goods['detail_totaltitle']) ? $goods['detail_totaltitle'] : $shop['description'],
                'btntext1' => trim($goods['detail_btntext1']),
                'btnurl1' => !empty($goods['detail_btnurl1']) ? $goods['detail_btnurl1'] : mobileUrl('goods'),
                'btntext2' => trim($goods['detail_btntext2']),
                'btnurl2' => !empty($goods['detail_btnurl2']) ? $goods['detail_btnurl2'] : mobileUrl('merch',array('merchid'=> $goods['merchid'])),
                'goodstotals' => $merchGoodsTotals,
                'memberTotals' => $merchFollowTotal
            );
            //统计
            if (empty($shop['selectgoods'])) {
                $statics = array(
                    'all'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid and merchid=:merchid and status=1 and deleted=0",array(':uniacid'=>$_W['uniacid'],':merchid'=>$goods['merchid'])),
                    'new'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid and merchid=:merchid and isnew=1 and status=1 and deleted=0",array(':uniacid'=>$_W['uniacid'],':merchid'=>$goods['merchid'])),
                    'discount'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid and merchid=:merchid and isdiscount=1 and status=1 and deleted=0",array(':uniacid'=>$_W['uniacid'],':merchid'=>$goods['merchid']))
                );
            } else {
                $goodsids = explode(",", $shop['goodsids']);
                $statics = array(
                    'all'=>count($goodsids),
                    'new'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid and merchid=:merchid and id in( {$shop['goodsids']} ) and isnew=1 and status=1 and deleted=0",array(':uniacid'=>$_W['uniacid'],':merchid'=>$goods['merchid'])),
                    'discount'=>pdo_fetchcolumn('select count(1) from '.tablename('elapp_shop_goods')." where uniacid=:uniacid and merchid=:merchid and id in( {$shop['goodsids']} ) and isdiscount=1 and status=1 and deleted=0",array(':uniacid'=>$_W['uniacid'],':merchid'=>$goods['merchid']))
                );
            }
        }

        //分享
        $goodsdesc = !empty($goods['description']) ? $goods['description']  : $goods['subtitle'];
        $_W['shopshare'] = array(
            'title' => !empty($goods['share_title']) ? $goods['share_title'] : $goods['title'],
            'imgUrl' => !empty($goods['share_icon']) ? tomedia($goods['share_icon']) : tomedia($goods['thumb']),
            'desc' => !empty($goodsdesc) ? $goodsdesc  : $_W['shopset']['shop']['name'],
            'link' => mobileUrl('goods/detail', array('id' => $goods['id']),true)
        );
        //商品多规格
        if ($goods['hasoption'] == 1) {
            $pricemax = array();            
            $options = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
            foreach ($options as $k => $v) {
                array_push($pricemax, $v['marketprice']);
            }
            $goods['marketprice'] = empty($pricemax) ? 0 : max($pricemax); // 如果导致商品价格显示错误 开启此块注释
        }
        //分销商查看佣金
        $com = p('commission');
        if ($com) {
            $cset = $_W['shopset']['commission'];
            if (!empty($cset)) {
                if ($member['isagent'] == 1 && $member['status'] == 1) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $member['id']),true);
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $_GPC['mid']),true);
                }
            }
            // 获取分销商品佣金
            if($goods['nocommission']==0){
                $glevel = $this->getLevel($openid);

                if (!empty($goods) && $goods['hasoption']) {
                    $optioncomm = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
                }
                $goods['seecommission'] = $this->getCommission($optioncomm,$goods,$glevel,$cset);
            }else{
                $goods['seecommission'] = 0;
            }
            $goods['cansee'] = $cset['cansee'];
            $goods['seetitle'] = $cset['seetitle'];
        }else{
            $goods['cansee'] = 0;
        }
        if($goods['seecommission'] >0){
            $goods['seecommission'] = round($goods['seecommission'],2);
        }
        //店员查看佣金
        $p_clerk = p('clerk');
        if ($p_clerk) {
            $p_clerk_set = $_W['shopset']['clerk'];
            if (!empty($p_clerk_set)) {
                if ($member['is_clerk'] == 1 && $member['clerk_status'] == 1) {
                    $myshop = $p_clerk->getShop($member['id']);
                    //print_r($myshop);die;
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $member['id']),true);
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $_GPC['mid']),true);
                }
            }
            // 获取店员商品提成
            if($goods['noClerkCommission']==0){
                $clerklevel = app(ClerkLevelLogic::class)->getClerkLevel($this->memberInfo['clerk_level'])->toArray();
                if (!empty($goods) && $goods['hasoption']) {
                    $optioncomm = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
                }
                $goods['clerkSeecommission'] = $this->getClerkCommission($optioncomm,$goods,$clerklevel,$p_clerk_set);
            }else{
                $goods['clerkSeecommission'] = 0;
            }
            $goods['clerkCansee'] = $p_clerk_set['cansee'];
            $goods['clerkSeetitle'] = $p_clerk_set['seetitle'];
            $goods['is_show_revenue'] = $myshop['is_show_revenue'];
        }else{
            $goods['clerkCansee'] = 0;
        }
        if($goods['clerkSeecommission'] >0){
            $goods['clerkSeecommission'] = round($goods['clerkSeecommission'],2);
        }

        //医生查看佣金
        $p_doctor = p('doctor');
        if ($p_doctor) {
            $p_doctor_set = $_W['shopset']['doctor'];
            if (!empty($p_doctor_set)) {
                if ($member['is_doctor'] == 1 && $member['doctor_status'] == 1) {
                    $myshop = $p_doctor->getShop($member['id']);
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $member['id']),true);
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $_GPC['mid']),true);
                }
            }
            // 获取医生商品提成
            if($goods['doctor_no_commission']==0){
                $doctor_level = $this->getDoctorLevel($openid);
                if (!empty($goods) && $goods['hasoption']) {
                    $optioncomm = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
                }
                $goods['doctorSeecommission'] = $this->getDoctorCommission($optioncomm,$goods,$doctor_level,$p_doctor_set);
            }else{
                $goods['doctorSeecommission'] = 0;
            }
            $goods['doctorCansee'] = $p_doctor_set['cansee'];
            $goods['doctorSeetitle'] = $p_doctor_set['seetitle'];
            $goods['is_show_revenue'] = $myshop['is_show_revenue'];
        }else{
            $goods['doctorCansee'] = 0;
        }
        if($goods['doctorSeecommission'] >0){
            $goods['doctorSeecommission'] = round($goods['doctorSeecommission'],2);
        }

        //店长查看佣金
        $p_owner = p('vrshop');
        if ($p_owner) {
            $p_owner_set = $_W['shopset']['vrshop'];
            if (!empty($p_owner_set)) {
                if ($member['is_owner'] == 1 && $member['owner_status'] == 1) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $member['id']),true);
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $_GPC['mid']),true);
                }
            }
            // 获取店员商品提成
            if($goods['noOwnerCommission']==0){
                $owner_level = $this->getOwnerLevel($openid);
                if (!empty($goods) && $goods['hasoption']) {
                    $optioncomm = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
                }
                $goods['ownerSeecommission'] = $this->getOwnerCommission($optioncomm,$goods,$owner_level,$p_owner_set);
            }else{
                $goods['ownerSeecommission'] = 0;
            }
            $goods['ownerCansee'] = $p_owner_set['cansee'];
            $goods['ownerSeetitle'] = $p_owner_set['seetitle'];
        }else{
            $goods['ownerCansee'] = 0;
        }
        if($goods['ownerSeecommission'] >0){
            $goods['ownerSeecommission'] = round($goods['ownerSeecommission'],2);
        }

        //合伙人查看佣金
        $p_copartner = p('copartner');
        if ($p_copartner) {
            $p_copartner_set = $_W['shopset']['copartner'];
            if (!empty($p_copartner_set)) {
                if ($member['is_copartner'] == 1 && $member['copartner_status'] == 1) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $member['id']),true);
                } else if (!empty($_GPC['mid'])) {
                    $_W['shopshare']['link'] = mobileUrl('goods/detail', array('id' => $goods['id'], 'mid' => $_GPC['mid']),true);
                }
            }
            // 获取店员商品提成
            if($goods['noCopartnerCommission']==0){
                $copartnerlevel = $this->getCopartnerLevel($openid);
                if (!empty($goods) && $goods['hasoption']) {
                    $optioncomm = pdo_fetchall('select * from ' . tablename('elapp_shop_goods_option') . ' where goodsid=:goodsid and uniacid=:uniacid order by displayorder asc', array(':goodsid' => $id, ':uniacid' => $_W['uniacid']));
                }
                $goods['copartnerSeecommission'] = $this->getCopartnerCommission($optioncomm,$goods,$copartnerlevel,$p_copartner_set);
            }else{
                $goods['copartnerSeecommission'] = 0;
            }
            $goods['copartnerCansee'] = $p_copartner_set['cansee'];
            $goods['copartnerSeetitle'] = $p_copartner_set['seetitle'];
        }else{
            $goods['copartnerCansee'] = 0;
        }
        if($goods['copartnerSeecommission'] >0){
            $goods['copartnerSeecommission'] = round($goods['copartnerSeecommission'],2);
        }
        $mSysSet = iunserializer($member['mSysSet']);
        //会员消费、分享预计积分 Hlei 2022/04/23 -->
        if(p('userpromote')){
            $userpromote_set = m('common')->getPluginset('userpromote');
            $plugin_userpromote = p('userpromote');            
        }
   
        //会员消费分享预计积分 Hlei2022/04/23-------->
        if ($plugin_userpromote && ($goods['sfjoin6'] == 1 || $goods['sfjoin4'] == 1)) {
            //调用预计积分计算方法
            $up_EstimateCreditsInfo = $plugin_userpromote->getGoodsEstimateCredits($id, $this->memberId);
            $goods['shelbuyCredits'] = $up_EstimateCreditsInfo['shelbuyCredits'];//消费积分
            $goods['shareCreditsOne'] = $up_EstimateCreditsInfo['shareCreditsOne'];//一级分享积分
            $goods['shareCreditsTwo'] = $up_EstimateCreditsInfo['shareCreditsTwo'];//二级分享积分
            $goods['canSeeCredits'] = $userpromote_set['canSeeCredits'];//积分显示总开关
            $goods['canSeeCreditsTitle'] = $userpromote_set['canSeeCreditsTitle'];//积分标题
            $goods['canSeeCreditsTitleShelbuy'] = $userpromote_set['canSeeCreditsTitleShelbuy'];//消费标题
            $goods['canSeeCreditsTitleShare'] = $userpromote_set['canSeeCreditsTitleShare'];//分享标题
            $goods['canSeeCreditsTitleOne'] = $userpromote_set['canSeeCreditsTitleOne'];//一级积分标题
            $goods['canSeeCreditsTitleTwo'] = $userpromote_set['canSeeCreditsTitleTwo'];//二级积分标题
            $goods['isOpenCreditsText'] = $mSysSet['isOpenCreditsText'];//会员积分显示开关
        } else {
            $goods['shelbuyCredits'] = 0;//消费积分
            $goods['shareCreditsOne'] = 0;//一级分享积分
            $goods['shareCreditsTwo'] = 0;//二级分享积分
            $goods['canSeeCredits'] = $userpromote_set['canSeeCredits'];
            $goods['canSeeCreditsTitle'] = $userpromote_set['canSeeCreditsTitle'];
            $goods['isOpenCreditsText'] =  $mSysSet['isOpenCreditsText'];
        }
        
        //核销门店
        $stores = array();
        if ($goods['isverify'] == 2) {
            $storeids = array();
            if (!empty($goods['storeids'])) {
                $storeids = array_merge(explode(',', $goods['storeids']), $storeids);
            }
            if (empty($storeids)) {
                //全部门店
                if ($merchid > 0) {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where  uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $merchid));
                } else {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where  uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            } else {
                if ($merchid > 0) {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_merch_store') . ' where id in (' . implode(',', $storeids) . ') and uniacid=:uniacid and merchid=:merchid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid'], ':merchid' => $merchid));
                } else {
                    $stores = pdo_fetchall('select * from ' . tablename('elapp_shop_store') . ' where id in (' . implode(',', $storeids) . ') and uniacid=:uniacid and status=1 and type in(2,3) order by displayorder desc,id desc', array(':uniacid' => $_W['uniacid']));
                }
            }
        }

        $share = m('common')->getSysset('share');
        $share['goods_detail_text'] = nl2br($share['goods_detail_text']);
        if (p('ccard') && $goods['type'] == 20) {
            //自定义表单
            $diyformhtml = "";
            $diyform_plugin = p('diyform');
            if($diyform_plugin){
                $fields = false;
                if($goods['diyformtype'] == 1){
                    //模板
                    if(!empty($goods['diyformid'])){
                        $diyformid = $goods['diyformid'];
                        $formInfo = $diyform_plugin->getDiyformInfo($diyformid);
                        $fields = $formInfo['fields'];
                    }
                } else if($goods['diyformtype'] == 2){
                    //自定义
                    $diyformid = 0;
                    $fields = iunserializer($goods['diyfields']);
                    if(empty($fields)){
                        $fields = false;
                    }
                }
                if(!empty($fields)){
                    ob_start();
                    $inPicker = true;
                    $openid = $_W['openid'];
                    $member = m('member')->getMember($openid, true);
                    $f_data = $diyform_plugin->getLastData(3, 0, $diyformid, $id, $fields, $member);
                    $flag = 0;
                    if (!empty($f_data)) {
                        foreach ($f_data as $k => $v) {
                            if (!empty($v)) {
                                $flag = 1;
                                break;
                            }
                        }
                    }
                    if (empty($flag)) {
                        $f_data = $diyform_plugin->getLastCartData($id);
                    }

                    $f_data['diychongzhijine'] = $goods['minprice'];
                    include $this->template('ccard/formfields');
                    $diyformhtml = ob_get_contents();
                    ob_clean();
                }
            }
            include $this->template('ccard/ccard_detail');
            exit();
        }
        //if (p('ccard') && !empty($commission_data['become_goodsid']) && $commission_data['become_goodsid'] == $goods['id']) {
        //include $this->template('ccard/cmember_detail');
        //exit();
        //}
        // 模板类型
        $new_temp = !is_mobile()?1:intval($_W['shopset']['template']['detail_temp']);
        if($new_temp && $getComments){
            $showComments = pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and level>=0 and deleted=0 and checked=0 and uniacid=:uniacid", array(':goodsid'=>$id,':uniacid'=>$_W['uniacid']));
        }
        //关闭商品图片预览
        $close_preview = intval($_W['shopset']['shop']['close_preview']);
        //商品划线价是否显示
        $isMarketPrice = intval($_W['shopset']['shop']['isMarketPrice']);
        //判断是否支持同城配送
        $goods['city_express_state']=1;
        $city_express = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_city_express') . " WHERE uniacid=:uniacid and merchid=0 limit 1", array(':uniacid' => $_W['uniacid']));
        //没设置同城配送或者禁用时
        if(empty($city_express)||$city_express['enabled']==0||$goods['merchid']>0 || $goods['type']!=1){
            $goods['city_express_state']=0;
        }elseif(empty($city_express['is_dispatch'])){
            $goods['dispatchprice']=array('min'=>$city_express['start_fee'],'max'=>$city_express['fixed_fee']);
        }
        // 获取商户固定信息
        if(p('merch')){            
            if(0 != $goods['merchid']){
                $merchUser = p('merch')->getListUserOne($goods['merchid']);
                $merchFixedInfo = iunserializer($merchUser['fixedInfo']);
                //print_r($merchFixedInfo);die;
                $goods['merchFixedInfoStatus']= $merchFixedInfo['fixedinfoStatus'];
                $goods['merchFixedInfoImageUrls'] = empty($merchFixedInfo['urls']) ? array() : $merchFixedInfo['urls'];
                $goods['merchFixedInfoDesc'] = empty($merchFixedInfo['fixedDesc']) ? array() : $merchFixedInfo['fixedDesc'];
            } else {
                $goods['merchFixedInfoImageUrls'] = array();
                $goods['merchFixedInfoDesc'] = array();
            }
        }
        // 获取商品详情页底部固定图片
        $buttonFixedImageSetting = m('common')->getGoodsBottomFixedImageSetting();
        // 主商城应用并且开启了底部信息展示
        if (empty($goods['merchid']) && $buttonFixedImageSetting['shopStatus']) {
            $goods['bottomFixedImageUrls'] = empty($buttonFixedImageSetting['urls']) ? array() : $buttonFixedImageSetting['urls'];
            $goods['bottomFixedPricedesc'] = empty($buttonFixedImageSetting['pricedesc']) ? array() : $buttonFixedImageSetting['pricedesc'];
        } else if ($goods['merchid'] != 0 && $buttonFixedImageSetting['merchStatus']) {
            $goods['bottomFixedImageUrls'] = empty($buttonFixedImageSetting['urls']) ? array() : $buttonFixedImageSetting['urls'];
            $goods['bottomFixedPricedesc'] = empty($buttonFixedImageSetting['pricedesc']) ? array() : $buttonFixedImageSetting['pricedesc'];
        } else {
            $goods['bottomFixedImageUrls'] = array();
        }
        $plugin_diypage = p('diypage');
        if($plugin_diypage){
            $diypage = $plugin_diypage->detailPage($goods['diypage']);
            p('diypage')->setShare($diypage,$goods);
            if($diypage){
                $startadv = $plugin_diypage->getStartAdv($diypage['diyadv']);
                include $this->template('diypage/detail');
                exit();
            }
        }
        //商品二维码
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);//最大会员等级价格
        $goodscode = $this->get_code($maxMemberLevelPrice,$seckillinfo['price']);
        include $this->template();
    }

    /*
     * 获取商品二维码
     * */
    function get_code($maxMemberLevelPrice = '',$seckillinfoPrice = '', $miniApp = false) {
        /*
         * 商品二维码图片定制
         * */
        global $_W,$_GPC;
        $id = intval($_GPC['id']);
        $uniacid = intval($_W['uniacid']);
        $openid = trim($_W['openid']);
        // 兼容小程序调用这块代码的逻辑，获取不到openid用memberId替换
        $memberId = $this->memberId;
        $openid = empty($openid)?$memberId:$openid;
        $goods = pdo_fetch("select id,activity_id,minprice,maxprice,hasoption,productprice,thumb_url,thumb,commission_thumb,title,isBanWechat,isGoodCodeAloneRules,hasGoodCodeBottomSlogan,hasShowGoodCodeBottomSlogan,upgradeMemberLevelText,vipMemberText,discountText from " . tablename('elapp_shop_goods') . " where id=:id and uniacid=:uniacid limit 1", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if($goods['hasoption']>0){
            $productprice = pdo_fetchcolumn("select max(productprice) as productprice from ".tablename('elapp_shop_goods_option')." where goodsid = :goodsid",array(':goodsid'=>$id));
            if(!empty($productprice)){
                $goods['productprice']=$productprice;
            }
        }
        if($seckillinfoPrice){
            $seckillPrice = "￥".number_format($seckillinfoPrice,2);
        }
        $goods['minprice'] = round($goods['minprice'],2);
        $goods['maxprice'] = round($goods['maxprice'],2);
        $member = m('member')->getMember($openid);
        $commission_data = m('common')->getPluginset('commission');
        $shareData = m('common')->getSysset('share');//获取系统分享设置
        //会员等级
        $level = m('member')->getLevel($openid);
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }        
        //优惠金额
        $discountPrice = 0;
        if($seckillinfoPrice > 0){
            $discountPrice = $goods['maxprice'] - $seckillinfoPrice;
        }elseif($maxMemberLevelPrice > 0){
            $discountPrice = $goods['maxprice'] - $maxMemberLevelPrice;
        }else{
            $discountPrice = 0;
        }
        $goodscode = '';
        $parameter = array();
        if(com('goodscode')){
            if($goods['minprice']==$goods['maxprice']){
                $price = "￥".$goods['minprice'];
            }else{
                $price = "￥".$goods['minprice']." ~ ".$goods['maxprice'];
            }
            $goods['thumb_url'] = array_values(unserialize($goods['thumb_url']));
            $goods['thumb'] = $goods['thumb_url'][0];

            if ($miniApp) {
                $url = (new PosterModel())->miniAppQrcodeUrl($id, 0, 'goods', $member['id']);
            } else {
                $url = mobileUrl('goods/detail', array('id'=>$id,'mid'=>$member['id']),true);
            }
            $qrcode = m('qrcode')->createQrcode($url);

            if($commission_data['codeShare'] == 1){
                $title = $this->codeShareTitle(1, $goods['title']);

                // todo [优化] [laiyongheng] [2023-04-14] 这里的codedata可以提取出来，导致无关重要的内容太长了影响阅读，下面的代码也是，在这里说明一下，有空有时间再优化
                $codedata = array(
                    'portrait'=>array(
                        'thumb'=>tomedia($_W['shopset']['shop']['logo']) ? tomedia($_W['shopset']['shop']['logo']) : tomedia($member['avatar']),
                        'left'=>40,
                        'top'=>40,
                        'width'=>100,
                        'height'=>100
                    ),
                    'shopname'=>array(
                        'text'=>$_W['shopset']['shop']['name'],
                        'left'=>160,
                        'top'=>80,
                        'size'=>28,
                        'width'=>360,
                        'height'=>50,
                        'color'=>'#333'
                    ),
                    'thumb'=>array(
                        'thumb'=>tomedia($goods['thumb']),
                        'left'=>40,
                        'top'=>160,
                        'width'=>560,
                        'height'=>560
                    ),
                    'qrcode'=>array(
                        'thumb'=>tomedia($qrcode),
                        'left'=>23,
                        'top'=>730,
                        'width'=>200,
                        'height'=>200
                    ),
                    'title'=>array(
                        'text'=>$title,
                        'left'=>220,
                        'top'=>770,
                        'size'=>20,
                        'width'=>380,
                        'height'=>50,
                        'color'=>'#333'
                    ),
                    'price'=>array(
                        'text'=>$price,
                        'left'=>270,
                        'top'=>880,
                        'size'=>30,
                        'color'=>'#f20'
                    ),
                    //优惠价
                    'discountPrice'=>array(
                        'text'=>$maxMemberLevelPrice?($discountPrice < 0?$discountPrice:''):'',
                        'left'=>545,
                        'top'=>975,
                        'size'=>24,
                        'color'=>'#f20'
                    ),
                    'desc'=>array(
                        'text'=>'长按二维码扫码购买',
                        'left'=>210,
                        'top'=>980,
                        'size'=>18,
                        'color'=>'#666'
                    ),
                );
            }else if($commission_data['codeShare'] == 2){
                $title = $this->codeShareTitle(2, $goods['title']);

                $codedata = array(
                    'thumb'=>array(
                        'thumb'=>tomedia($goods['thumb']),
                        'left'=>20,
                        'top'=>20,
                        'width'=>150,
                        'height'=>150
                    ),
                    'title'=>array(
                        'text'=>$title,
                        'left'=>170,
                        'top'=>30,
                        'size'=>22,
                        'width'=>430,
                        'height'=>90,
                        'color'=>'#333'
                    ),
                    'price'=>array(
                        'text'=>$price,
                        'left'=>210,
                        'top'=>120,
                        'size'=>30,
                        'color'=>'#f20'
                    ),
                    'qrcode'=>array(
                        'thumb'=>tomedia($qrcode),
                        'left'=>170,
                        'top'=>200,
                        'width'=>300,
                        'height'=>300
                    ),
                    //优惠价
                    'discountPrice'=>array(
                        'text'=>$maxMemberLevelPrice?($discountPrice < 0?$discountPrice:''):'',
                        'left'=>545,
                        'top'=>975,
                        'size'=>24,
                        'color'=>'#f20'
                    ),
                    'desc'=>array(
                        'text'=>'长按二维码扫码购买',
                        'left'=>205,
                        'top'=>510,
                        'size'=>18,
                        'color'=>'#666'
                    ),
                    'shopname'=>array(
                        'text'=>$_W['shopset']['shop']['name'],
                        'left'=>0,
                        'top'=>585,
                        'size'=>28,
                        'width'=>640,
                        'height'=>50,
                        'color'=>'#fff'
                    ),
                );

            }else if($commission_data['codeShare'] == 3){
                $title = $this->codeShareTitle(3, $goods['title']);

                $codedata = array(
                    'title'=>array(
                        'text'=>$title,
                        'left'=>27,
                        'top'=>40,
                        'size'=>22,
                        'width'=>600,
                        'height'=>90,
                        'color'=>'#333'
                    ),
                    'thumb'=>array(
                        'thumb'=>tomedia($goods['thumb']),
                        'left'=>0,
                        'top'=>150,
                        'width'=>640,
                        'height'=>640
                    ),
                    'qrcode'=>array(
                        'thumb'=>tomedia($qrcode),
                        'left'=>20,
                        'top'=>810,
                        'width'=>220,
                        'height'=>220
                    ),
                    'price'=>array(
                        'text'=>$price,
                        'left'=>280,
                        'top'=>870,
                        'size'=>30,
                        'color'=>'#000'
                    ),
                    //优惠价
                    'discountPrice'=>array(
                        'text'=>$maxMemberLevelPrice?($discountPrice < 0?$discountPrice:''):'',
                        'left'=>545,
                        'top'=>975,
                        'size'=>24,
                        'color'=>'#f20'
                    ),
                    'desc'=>array(
                        'text'=>'长按二维码扫码购买',
                        'left'=>280,
                        'top'=>950,
                        'size'=>18,
                        'color'=>'#666'
                    ),
                );
            }

            $this->filterPricesShow($shareData, $codedata);

            $parameter = array(
                'goodsid' => $id,
                'qrcode' => $qrcode,
                'codedata' => $codedata,
                'mid' => $member['id'],
                'codeshare' => $commission_data['codeShare'],
            );
            $goodscode = com('goodscode')->createcode($parameter);
        }else{
            if($goods['minprice']==$goods['maxprice']){
                $price = "￥".number_format($goods['minprice'],2);
                $goods['productprice'] = (float)$goods['productprice'];
                if (empty($goods['productprice'])){
                    $oldprice = '';
                }else{
                    $oldprice = "￥".$goods['productprice'];
                }
            }else{
                $price = "￥".$goods['minprice']." ~ ".$goods['maxprice'];

                if (empty($goods['productprice'])){
                    $oldprice = '';
                }else{
                    $oldprice = "￥".$goods['productprice'];
                }
            }
            if(substr($goods['thumb'], 0, 2) == '//'){
                $goods['thumb'] = 'http:'.substr($goods['thumb'], stripos($goods['thumb'], '//'));
            }

            if ($miniApp) {
                $url = (new PosterModel())->miniAppQrcodeUrl($id, 0, 'goods', $member['id']);
            } else {
                $url = mobileUrl('goods/detail', array('id'=>$id,'mid'=>$member['id']),true);
            }
            /** @var QrcodeModel $qrcodem */
            $qrcodem = m('qrcode');
            $qrcode = $qrcodem->createQrcode($url);

            if($commission_data['codeShare'] == 1){
                // 代码是后期整理的，将title部分提出来，codeShare的值和type的值不是意义对应的需要理解
                $title = $this->codeShareTitle(4, $goods['title']);    
                $codedata = array(
                    'portrait'=>array(
                        'thumb'=>$shareData['logoicon']?tomedia($shareData['logoicon']):(tomedia($_W['shopset']['shop']['logo']) ? tomedia($_W['shopset']['shop']['logo']) : tomedia($member['avatar'])),
                        'left'=>0,
                        'top'=>0,
                        'width'=>$shareData['logoicon']?640:100,
                        'height'=>$shareData['logoicon']?180:100
                    ),
                    'shopname'=>array(
                        'text'=>$shareData['logoicon']?'':$_W['shopset']['shop']['name'],
                        'left'=>160,
                        'top'=>75,
                        'size'=>28,
                        'width'=>360,
                        'height'=>50,
                        'color'=>'#333'
                    ),
                    'thumb'=>array(
                        'thumb'=>tomedia($goods['thumb']),
                        'left'=>70,
                        'top'=>180,
                        'width'=>500,
                        'height'=>500
                    ),
                    'qrcode'=>array(
                        'thumb'=>tomedia($qrcode),
                        'left'=> 486,
                        'top'=> 866,
                        'width'=>122,
                        'height'=>122
                    ),
                    //商品标题
                    'title'=>array(
                        'text'=>trim($title),
                        'left'=> 64,
                        'top'=> strlen($title) > 70 ? 680 : 700,
                        'size'=> 24,
                        'width'=>460,
                        'height'=>84,
                        'color'=>'#000',
                    ),

                    //零售价
                    'price'=>array(
                        'text'=>$price,
                        'left'=>420,
                        'top'=> 777,
                        'size'=>16,
                        'color'=>($maxMemberLevelPrice or $seckillinfoPrice)?'#000':'#000'
                    ),
                    //零售价文字
                    'shopPriceText'=>array(
                        'text'=>empty($shareData['shopPriceText'])?'零售价:':$shareData['shopPriceText'].':',
                        'left'=>350,
                        'top'=>780,
                        'size'=>14,
                        'color'=>'#999'
                    ),
                    //扫码说明
                    'desc'=>array(
                        'text'=>empty($shareData['descText'])?'长按二维码扫码购买':$shareData['descText'],
                        'left'=>448,
                        'top'=>926,
                        'size'=>16,
                        'color'=>'#666'
                    ),

                    //分享文字
                    'shareText'=>array(
                        'text'=>'',
                        'left'=>350,
                        'top'=>140,
                        'size'=>18,
                        'color'=>'#fff'
                    ),

                    //原价
                    'oldprice'=>array(
                        'text' => $oldprice,
                        'left'=>300,
                        'top'=>926,
                        'size'=>18,
                        'color'=>'#d0d0d0'
                    ),
                    //原价文字
                    'oldPriceText'=>array(
                        'text' => $oldprice?(empty($shareData['oldPriceText'])?'原价':$shareData['oldPriceText']):'',
                        'left'=>250,
                        'top'=>926,
                        'size'=>18,
                        'color'=>'#d0d0d0'
                    ),
                    'markPrice' => array(
                        'text'=> $maxMemberLevelPrice ? '￥' :'',
                        'left' => 220,
                        'top' => 963,
                        'bold' => true,
                        'color' =>'#fff',
                        'size'=>15
                    ),
                );
                //秒杀价
                if($seckillinfoPrice > 0){
                    $codedata['seckillPrice']['text'] = $seckillPrice;
                    $codedata['seckillPrice']['left'] = 185;
                    $codedata['seckillPrice']['top'] = 842;
                    $codedata['seckillPrice']['size'] = 22;
                    $codedata['seckillPrice']['color'] = '#f20';
                    $codedata['seckillText']['text'] = empty($shareData['seckillText'])?'秒杀价':$shareData['seckillText'];
                    $codedata['seckillText']['left'] = 40;
                    $codedata['seckillText']['top'] = 845;
                    $codedata['seckillText']['size'] = 18;
                    $codedata['seckillText']['color'] = '#333';
                }
                if($maxMemberLevelPrice > 0){
                    $codedata['maxMemberLevelPrice']['text'] = "￥".number_format($maxMemberLevelPrice,2);
                    $codedata['maxMemberLevelPrice']['left'] = 160;
                    $codedata['maxMemberLevelPrice']['top'] = 773;
                    $codedata['maxMemberLevelPrice']['size'] = 24;
                    $codedata['maxMemberLevelPrice']['color'] = $seckillinfoPrice?'#FF8020':'#FF8020';
                    $codedata['memberCardPriceText']['text'] = empty($shareData['memberCardPriceText'])?'VIP会员价:':$shareData['memberCardPriceText'].':';
                    $codedata['memberCardPriceText']['left'] = 64;
                    $codedata['memberCardPriceText']['top'] = 780;
                    $codedata['memberCardPriceText']['size'] = 14;
                    $codedata['memberCardPriceText']['color'] = '#999';
                }
                if ($goods['hasoption'] > 0){
                    $codedata['oldprice']['top'] = 914;
                    $codedata['oldprice']['left'] = 380;
                    $codedata['oldPriceText']['top'] = 914;
                    $codedata['oldPriceText']['left'] = 350;
                    $codedata['price']['top'] = 870;
                    $codedata['price']['color'] = '#f20';
                    $codedata['shopPriceText']['top'] = 874;
                    $codedata['oldPriceText']['left'] = 255;
                    $codedata['oldprice']['left'] = 374;
                }
                //禁止发圈文字
                if($goods['isBanWechat'] > 0){
                    $codedata['banWechatText']['text'] = empty($shareData['banWechatText'])?'禁圈':$shareData['banWechatText'];
                    $codedata['banWechatText']['left'] = 350;
                    $codedata['banWechatText']['top'] = 135;
                    $codedata['banWechatText']['size'] = 28;
                    $codedata['banWechatText']['color'] = '#fff';
                }

                //分享人信息显示
                //来自文字
                $codedata['comefromText']['text'] = empty($shareData['comefromText'])?'来自':$shareData['comefromText'];
                $codedata['comefromText']['left'] = 103;
                $codedata['comefromText']['top'] = 114;
                $codedata['comefromText']['size'] = 20;
                $codedata['comefromText']['color'] = '#fff';
                //会员昵称
                $codedata['nicknameText']['text'] = empty($member['realname'])?$member['nickname']:$member['realname'].(empty($shareData['shareText'])?' 分享':$shareData['shareText']);
                $codedata['nicknameText']['left'] = 158;
                $codedata['nicknameText']['top'] = 114;
                $codedata['nicknameText']['size'] = 20;
                $codedata['nicknameText']['color'] = '#fff';
                //如果开启商品海报独立规则则按商品设置
                if(!empty($goods['isGoodCodeAloneRules'])){
                    //==海报底部优惠宣传语 独立规则==
                    //
                    $codedata['maxMemberLevelPrice']['color'] = '#C70401';
                    //启用独立规则 + 显示开关
                    if(!empty($goods['hasGoodCodeBottomSlogan']) && !empty($goods['hasShowGoodCodeBottomSlogan'])){
                        //宣传语
                        $codedata['sloganText']['text'] = $maxMemberLevelPrice?(!empty($goods['upgradeMemberLevelText'])?$goods['upgradeMemberLevelText']:$shareData['upgradeMemberLevelText'].$maxlevel['levelname']):$shareData['sloganText'];
                        $codedata['sloganText']['left'] = 56;
                        $codedata['sloganText']['top'] = 880;
                        $codedata['sloganText']['size'] = 28;
                        $codedata['sloganText']['color'] = '#fff';
                        $codedata['sloganText']['bold'] = true;
                        //立省优惠文字
                        $codedata['discountText']['text'] = $maxMemberLevelPrice?'为您'.(!empty($goods['discountText'])?$goods['discountText']:$shareData['discountText']):'';
                        $codedata['discountText']['left'] = 56;
                        $codedata['discountText']['top'] = 949;
                        $codedata['discountText']['size'] = 28;
                        $codedata['discountText']['color'] = '#fff';
                        $codedata['discountText']['bold'] = true;
                        //优惠价
                        $codedata['discountPrice']['text'] = $maxMemberLevelPrice?($discountPrice > 0?number_format($discountPrice,2):''):'';
                        $codedata['discountPrice']['left'] = 236;
                        $codedata['discountPrice']['top'] = 949;
                        $codedata['discountPrice']['size'] = 28;
                        $codedata['discountPrice']['color'] = '#fff';
                        $codedata['discountPrice']['bold'] = true;
                    }else{
                        //否则为空
                    }
                }else{
                    //否则==海报底部优惠宣传语==全局默认设置
                    //宣传语
                    $codedata['sloganText']['text'] = $maxMemberLevelPrice?(empty($shareData['isMemberShareCodeText'])?'成为':$shareData['upgradeMemberLevelText'].$maxlevel['levelname']):$shareData['sloganText'];
                    $codedata['sloganText']['left'] = 56;
                    $codedata['sloganText']['top'] = 880;
                    $codedata['sloganText']['size'] = 28;
                    $codedata['sloganText']['color'] = '#FFE471';
                    $codedata['sloganText']['bold'] = true;
                    //立省优惠文字
                    $codedata['discountText']['text'] = $maxMemberLevelPrice?'为您'.(empty($shareData['discountText'])?'优惠':$shareData['discountText']):'';
                    $codedata['discountText']['left'] = 56;
                    $codedata['discountText']['top'] = 949;
                    $codedata['discountText']['size'] = 28;
                    $codedata['discountText']['color'] = '#FFE471';
                    $codedata['discountText']['bold'] = true;
                    //优惠价
                    $codedata['discountPrice']['text'] = $maxMemberLevelPrice?($discountPrice > 0?number_format($discountPrice,2):''):'';
                    $codedata['discountPrice']['left'] = 236;
                    $codedata['discountPrice']['top'] = 949;
                    $codedata['discountPrice']['size'] = 28;
                    $codedata['discountPrice']['color'] = '#fff';
                    $codedata['discountPrice']['bold'] = true;
                }
                //print_r($codedata);die;
            }else if($commission_data['codeShare'] == 2) {
                $title = $this->codeShareTitle(2,$goods['title']);

                $codedata = array(
                    'thumb'=>array(
                        'thumb'=>tomedia($goods['thumb']),
                        'left'=>20,
                        'top'=>20,
                        'width'=>150,
                        'height'=>150
                    ),
                    'title'=>array(
                        'text'=>$title,
                        'left'=>170,
                        'top'=>30,
                        'size'=>22,
                        'width'=>430,
                        'height'=>90,
                        'color'=>'#333'
                    ),
                    'price'=>array(
                        'text'=>$price,
                        'left'=>210,
                        'top'=>120,
                        'size'=>30,
                        'color'=>'#f20'
                    ),
                    'qrcode'=>array(
                        'thumb'=>tomedia($qrcode),
                        'left'=>170,
                        'top'=>200,
                        'width'=>300,
                        'height'=>300
                    ),
                    'desc'=>array(
                        'text'=>'长按二维码扫码购买',
                        'left'=>205,
                        'top'=>510,
                        'size'=>18,
                        'color'=>'#666'
                    ),
                    //优惠价
                    'discountPrice'=>array(
                        'text'=>$maxMemberLevelPrice?($discountPrice < 0?$discountPrice:''):'',
                        'left'=>545,
                        'top'=>975,
                        'size'=>24,
                        'color'=>'#f20'
                    ),
                    'shopname'=>array(
                        'text'=>$_W['shopset']['shop']['name'],
                        'left'=>0,
                        'top'=>585,
                        'size'=>28,
                        'width'=>640,
                        'height'=>50,
                        'color'=>'#fff'
                    ),
                    'oldprice'=>array(
                        'text' => $oldprice,
                        'left'=>210,
                        'top'=>80,
                        'size'=>20,
                        'color'=>'#d0d0d0'
                    ),
                );
            }

            // 过滤零售价，原价 显示
            $this->filterPricesShow($shareData, $codedata);
            if ($goods['activity_id'] == 1 && !empty($goods['commission_thumb'])) {
                // todo 980 活动专属（只有图片和二维码）
                $commission_data['codeShare'] = 4;
                $codedata['commission_thumb'] = [
                    'thumb'  => tomedia($goods['commission_thumb']),
                ];
                $codedata['qrcode']['left'] = 485;
                $codedata['qrcode']['top'] = 955;
                $codedata['qrcode']['width'] = 120;
                $codedata['qrcode']['height'] = 120;

                $codedata['nicknameText']['left'] += 40;
                $codedata['nicknameText']['size'] += 2;
                $codedata['comefromText']['left'] += 40;
                $codedata['comefromText']['size'] += 2;
                $codedata['shareText']['left']    += 40;
                $codedata['shareText']['size']    += 2;
            }
            $parameter = array(
                'goodsid' => $id,
                'qrcode' => $qrcode,
                'codedata' => $codedata,
                'mid' => $member['id'],
                'codeshare' => $commission_data['codeShare'],
                'oldprice' => $oldprice,
                'isGoodCodeAloneRules' => $goods['isGoodCodeAloneRules']
            );            
            /** @var GoodsModel $goodsModel */
            $goodsModel = m('goods');
            $goodscode = $goodsModel->createcode($parameter);
        }
        /*if( $_W['ispost'] ){
            show_json(1,array('image'=>$goodscode,'test'=>'111'));
        }else{
            return $goodscode;
        }*/
        return $goodscode;
    }

    /**
     * 过滤价格显示输出
     * @time 2023/04/14 14:30
     * @param $shareData array 获取系统分享设置 m('common')->getSysset('share');//
     * @param $codedata array 分享数据参数
     * @return void
     */
    private function filterPricesShow($shareData, &$codedata) {
        // 如果零售价不显示，原价显示，则把原价的位置调整到零售价的位置
        if ($shareData['shopPriceShow']!=1 && $shareData['oldPriceShow']==1
            && isset($codedata['price']['top']) && $codedata['price']['top'] == 924) {
            // 因为这里存在多种输出样式，为了不影响其他样式，所以这里只针对特定的样式进行处理 （商品分享页面）
            $codedata['oldprice']['left'] = $codedata['price']['left'];
            $codedata['oldprice']['top'] = $codedata['price']['top'];
            $codedata['oldPriceText']['left'] = $codedata['shopPriceText']['left'];
            $codedata['oldPriceText']['top'] = $codedata['shopPriceText']['top'];
        }
        // 如果后台关闭启 零售价显示 则过滤输出文字
        if ($shareData['shopPriceShow']!=1) {
            unset($codedata['price']);
            unset($codedata['shopPriceText']);
        }
        // 如果后台关闭启 原价显示 则过滤输出文字
        if ($shareData['oldPriceShow']!=1) {
            unset($codedata['oldprice']);
            unset($codedata['oldPriceText']);
        }
    }

    // 提取生成title代码
    private function codeShareTitle($type, $goodstitle) {

        switch ($type) {
            case 1:
                $title0 = mb_substr($goodstitle, 0, 10,'utf-8');
                $title1 = mb_substr($goodstitle, 10, 10,'utf-8');
                $title = <<<EOF
    {$title0}
    {$title1}
EOF;
                break;
            case 2:
                $title0 = mb_substr($goodstitle, 0, 14,'utf-8');
                $title1 = mb_substr($goodstitle, 14, 14,'utf-8');
                $title = <<<EOF
    {$title0}
    {$title1}
EOF;
                break;
            case 3:
                $title0 = mb_substr($goodstitle, 0, 12,'utf-8');
                $title1 = mb_substr($goodstitle, 12, 12,'utf-8');
                $title = <<<EOF
                {$title0}
                {$title1}
EOF;
                break;
            case 4:
                $title0 = mb_substr($goodstitle, 0, 20,'utf-8');
                $title1 = mb_substr($goodstitle, 20, 40,'utf-8');
                $title = <<<EOF
    {$title0}
{$title1}
EOF;
        }

        return $title;
    }

    //获取赠品信息
    function querygift(){
        global $_W,$_GPC;
        $uniacid = intval($_W['uniacid']);
        $giftid = intval($_GPC['id']);
        $gift = pdo_fetch("select * from ".tablename('elapp_shop_gift')." where id=:id and status=1 and  uniacid =:uniacid limit 1",array(':id'=>$giftid,':uniacid'=>$_W['uniacid']));
        show_json(1,$gift);
    }

    /* 计算一个商品的运费
     * @param type $goods 商品数据
     * @param type $param 重量或数量
     * @param type $areas 特殊的地区
     * @param type $city 邮寄到的城市
     * @return type
     */

    protected function getGoodsDispatchPrice($goods,$is_seckill=false) {
        if (!empty($goods['issendfree']) && empty($is_seckill) ) {
            //包邮
            return 0;
        }
        if ($goods['type'] == 2 || $goods['type'] == 3 || $goods['type'] == 20) {
            //虚拟物品或虚拟卡密
            return 0;
        }
        if ($goods['dispatchtype'] == 1) {
            //统一运费
            return $goods['dispatchprice'];
        } else {
            //运费模板
            if (empty($goods['dispatchid'])) {
                //默认快递
                $dispatch = m('dispatch')->getDefaultDispatch($goods['merchid']);
            } else {
                $dispatch = m('dispatch')->getOneDispatch($goods['dispatchid']);
            }
            if (empty($dispatch)) {
                //最新的一条快递信息
                $dispatch = m('dispatch')->getNewDispatch($goods['merchid']);
            }
            $areas = iunserializer($dispatch['areas']);
            if (!empty($areas) && is_array($areas)) {
                $firstprice = array();
                foreach ($areas as $val){
                    //判断计费方式
                    if(empty($dispatch['calculatetype'])){
                        $firstprice[] = $val['firstprice'];
                    }else{
                        $firstprice[] = $val['firstnumprice'];
                    }
                }
                array_push($firstprice,m('dispatch')->getDispatchPrice(1, $dispatch));
                $ret = array(
                    'min' => round(min($firstprice),2),
                    'max' => round(max($firstprice),2)
                );
            } else {
                $ret = m('dispatch')->getDispatchPrice(1, $dispatch);
            }
            return $ret;
        }
    }

    function get_detail() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $goods = pdo_fetch('select * from '.tablename('elapp_shop_goods').' where id=:id and uniacid=:uniacid limit 1',array(':id'=>$id,':uniacid'=>$_W['uniacid']));
        die(m('ui')->lazy($goods['content']));
    }

    function get_comments(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $percent = 100;
        $params = array(':goodsid'=>$id,':uniacid'=>$_W['uniacid']);
        $count = array(
            "all"=>pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and level>=0 and deleted=0 and checked=0 and uniacid=:uniacid",$params),
            "good"=>pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and level>=5 and deleted=0 and checked=0 and uniacid=:uniacid",$params),
            "normal"=>pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and level>=2 and level<=4 and deleted=0 and checked=0 and uniacid=:uniacid",$params),
            "bad"=>pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and level<=1 and deleted=0 and checked=0 and uniacid=:uniacid",$params),
            "pic"=>pdo_fetchcolumn('select count(*) from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and ifnull(images,'a:0:{}')<>'a:0:{}' and deleted=0 and checked=0 and uniacid=:uniacid",$params)
        ) ;
        $list = array();
        if($count['all']>0){
            $percent = intval( $count['good'] / (empty($count['all'])?1:$count['all']) * 100);
            $list = pdo_fetchall('select nickname,level,content,images,createtime from '.tablename('elapp_shop_order_comment')." where goodsid=:goodsid and deleted=0 and checked=0 and uniacid=:uniacid order by istop desc, createtime desc, id desc limit 2",array(':goodsid'=>$id,':uniacid'=>$_W['uniacid']));
            foreach($list as &$row){
                $row['createtime'] = date('Y-m-d H:i',$row['createtime']);
                $row['images'] = set_medias(iunserializer($row['images']));
                $row['nickname'] = cut_str($row['nickname'], 1, 0).'**'.cut_str($row['nickname'], 1, -1);
            }
            unset($row);
        }
        show_json(1,array('count'=>$count, 'percent'=>$percent, 'list'=>$list));
    }
    /**
     * 计算出此商品的分销佣金
     * @param type $goodsid
     * @return type
     */
    public function getCommission($option=array(),$goods,$level,$set){
        global $_W;
        $commission = 0;
        if($level == 'false'){
            return $commission;
        }
        if ($goods['hascommission'] == 1) {
            $price = $goods['maxprice'];
            $levelid = 'default';
            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['commission']) ? json_decode($goods['commission'], true) : array();
            if ($goods_commission['type'] == 0) {
                if($goods['marketprice']<=$goods['maxprice']){
                    $goods['marketprice'] = $goods['maxprice'];
                }
                $commission = $set['level'] >= 1 ? ($goods['commission1_rate'] > 0 ? ($goods['commission1_rate'] * $goods['marketprice'] / 100) : $goods['commission1_pay']) : 0;
            } else {
                $price_all = array();
                if(!empty($option)){
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        $maxkey = array_search(max($value),$value);
                        foreach ($option as $k => $v) {
                            $optioncommission=0;
                            if(('option' . $v['id']) == $key) {
                                if(strexists($value[$maxkey], '%')) {
                                    $optioncommission= (floatval(str_replace('%', '', $value[0]) / 100) * $v['marketprice']);
                                } else {
                                    $optioncommission = $value[$maxkey];
                                }
                            }
                            array_push($price_all, $optioncommission);
                        }
                    }
                }else{
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        foreach ($value as $k => $v) {
                            if (strexists($v, '%')) {
                                array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                                continue;
                            }
                            array_push($price_all, $v);
                        }
                    }
                }
                $commission = max($price_all);
            }
        } else {
            if($goods['marketprice']<=$goods['maxprice']){
                $goods['marketprice'] = $goods['maxprice'];
            }
            if ($level!='false' && !empty($level)) {
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            } else {
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $goods['marketprice'] / 100, 2) : 0;
            }
        }

        return $commission;
    }

    /**
     * 计算出此商品的店员提成
     * @param type $goodsid
     * @return type
     */
    public function getClerkCommission($option=array(),$goods,$level,$set){
        global $_W;
        $commission = 0;
        if($level == 'false'){
            return $commission;
        }
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        //商品最高会员等级价格
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);
        //提成计算价格规则 1最高会员折扣价 0零售价
        $calculatePriceType = $set['calculatePriceType'];
        if ($goods['hasClerkCommission'] == 1) {//商品独立规则
            $price = $goods['maxprice'];
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $price - $goods['costprice'];
            }
            $levelid = 'default';
            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['clerkCommission']) ? json_decode($goods['clerkCommission'], true) : array();
             if ($goods_commission['type'] == 0) {//商品统一设置提成
                //如果零售价 <= 最大商品价格
                if($goods['marketprice'] <= $goods['maxprice']){
                    $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                }
                //按最高会员等级折扣
                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                }else{
                    $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                }                
                //是否减掉商品成本再计算提成
                if(!empty($set['isReduceGoodsCostprice'])){
                    $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                }
                $commission = $set['level'] >= 1 ? ($goods['clerkCommission1_rate'] > 0 ? ($goods['clerkCommission1_rate'] * $commissionCalculatePrice / 100) : $goods['clerkCommission1_pay']) : 0;
            } else {//商品详细设置
                $price_all = array();
                if(!empty($option)){//多规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        $maxkey = array_search(max($value),$value);
                        foreach ($option as $k => $v) {
                            $optioncommission = 0;
                            if(('option' . $v['id']) == $key) {                                
                                //按最高会员等级折扣
                                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                                }else{
                                    $commissionCalculatePrice = $v['marketprice'];//商品最低零售价
                                }
                                //是否减掉商品成本再计算提成
                                if(!empty($set['isReduceGoodsCostprice'])){
                                    $commissionCalculatePrice = $commissionCalculatePrice - $v['costprice'];
                                }
                                if(strexists($value[$maxkey], '%')) {//判断有%符号则按百分比
                                    $optioncommission= (floatval(str_replace('%', '', $value[0]) / 100) * $commissionCalculatePrice);
                                } else {//否则按固定金额
                                    $optioncommission = $value[$maxkey];
                                }
                            }
                            //向数组尾部插入规格
                            array_push($price_all, $optioncommission);
                        }
                    }
                }else{//单规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        foreach ($value as $k => $v) {
                            if (strexists($v, '%')) {//判断有%符号则按百分比
                                array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                                continue;//退出
                            }
                            array_push($price_all, $v);//否则按固定金额
                        }
                    }
                }
                $commission = round(max($price_all), 2);
            }
        } else {//全局默认
            if($goods['marketprice'] <= $goods['maxprice']){
                $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
            }
            //按最高会员等级折扣
            if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
            }else{
                $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
            }
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
            }
            if ($level!='false' && !empty($level)) {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            } else {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            }
        }
        return $commission;
    }

    /**
     * 计算出此商品的医生提成
     * @param type $goodsid
     * @return type
     */
    public function getDoctorCommission($option=array(),$goods,$level,$set){
        global $_W;
        $commission = 0;
        if($level == 'false'){
            return $commission;
        }
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        //商品最高会员等级价格
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);
        //提成计算价格规则 1最高会员折扣价 0零售价
        $calculatePriceType = $set['calculatePriceType'];
        if ($goods['doctor_has_commission'] == 1) {//商品独立规则
            $price = $goods['maxprice'];
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $price - $goods['costprice'];
            }
            $levelid = 'default';
            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['doctor_commission']) ? json_decode($goods['doctor_commission'], true) : array();
             if ($goods_commission['type'] == 0) {//商品统一设置提成
                //如果零售价 <= 最大商品价格
                if($goods['marketprice'] <= $goods['maxprice']){
                    $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                }
                //按最高会员等级折扣
                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                }else{
                    $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                }                
                //是否减掉商品成本再计算提成
                if(!empty($set['isReduceGoodsCostprice'])){
                    $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                }
                $commission = $set['isopen'] == 1 ? ($goods['doctor_commission_rate'] > 0 ? ($goods['doctor_commission_rate'] * $commissionCalculatePrice / 100) : $goods['doctor_commission_pay']) : 0;
            } else {//商品详细设置
                $price_all = array();
                if(!empty($option)){//多规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        $maxkey = array_search(max($value),$value);
                        foreach ($option as $k => $v) {
                            $optioncommission = 0;
                            if(('option' . $v['id']) == $key) {                                
                                //按最高会员等级折扣
                                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                                }else{
                                    $commissionCalculatePrice = $v['marketprice'];//商品最低零售价
                                }
                                //是否减掉商品成本再计算提成
                                if(!empty($set['isReduceGoodsCostprice'])){
                                    $commissionCalculatePrice = $commissionCalculatePrice - $v['costprice'];
                                }
                                if(strexists($value[$maxkey], '%')) {//判断有%符号则按百分比
                                    $optioncommission= (floatval(str_replace('%', '', $value[0]) / 100) * $commissionCalculatePrice);
                                } else {//否则按固定金额
                                    $optioncommission = $value[$maxkey];
                                }
                            }
                            //向数组尾部插入规格
                            array_push($price_all, $optioncommission);
                        }
                    }
                }else{//单规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        foreach ($value as $k => $v) {
                            if (strexists($v, '%')) {//判断有%符号则按百分比
                                array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                                continue;//退出
                            }
                            array_push($price_all, $v);//否则按固定金额
                        }
                    }
                }
                $commission = round(max($price_all), 2);
            }
        } else {//全局默认
            if($goods['marketprice'] <= $goods['maxprice']){
                $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
            }
            //按最高会员等级折扣
            if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
            }else{
                $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
            }
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
            }
            if ($level!='false' && !empty($level)) {//商品不参与提成默认
                $commission = $set['isopen'] == 1 ? round($level['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            } else {//商品不参与提成默认
                $commission = $set['isopen'] == 1 ? round($set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            }
        }

        return $commission;
    }

    /**
     * 计算出此商品的店长绩效
     * @param type $goodsid
     * @return type
     */
    public function getOwnerCommission($option=array(),$goods,$level,$set){
        global $_W;
        $commission = 0;
        if($level == 'false'){
            return $commission;
        }

        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        //商品最高会员等级价格
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);
        //提成计算价格规则 1最高会员折扣价 0零售价
        $calculatePriceType = $set['calculatePriceType'];
        if ($goods['hasOwnerCommission'] == 1) {//商品独立规则
            $price = $goods['maxprice'];
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $price - $goods['costprice'];
            }
            $levelid = 'default';
            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['ownerCommission']) ? json_decode($goods['ownerCommission'], true) : array();
             if ($goods_commission['type'] == 0) {//商品统一设置提成
                //如果零售价 <= 最大商品价格
                if($goods['marketprice'] <= $goods['maxprice']){
                    $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                }
                //按最高会员等级折扣
                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                }else{
                    $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                }                
                //是否减掉商品成本再计算提成
                if(!empty($set['isReduceGoodsCostprice'])){
                    $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                }
                $commission = $set['level'] >= 1 ? ($goods['ownerCommission1_rate'] > 0 ? ($goods['ownerCommission1_rate'] * $commissionCalculatePrice / 100) : $goods['ownerCommission1_pay']) : 0;
            } else {//商品详细设置
                $price_all = array();
                if(!empty($option)){//多规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        $maxkey = array_search(max($value),$value);
                        foreach ($option as $k => $v) {
                            $optioncommission = 0;
                            if(('option' . $v['id']) == $key) {                                
                                //按最高会员等级折扣
                                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                                }else{
                                    $commissionCalculatePrice = $v['marketprice'];//商品最低零售价
                                }
                                //是否减掉商品成本再计算提成
                                if(!empty($set['isReduceGoodsCostprice'])){
                                    $commissionCalculatePrice = $commissionCalculatePrice - $v['costprice'];
                                }
                                if(strexists($value[$maxkey], '%')) {//判断有%符号则按百分比
                                    $optioncommission= (floatval(str_replace('%', '', $value[0]) / 100) * $commissionCalculatePrice);
                                } else {//否则按固定金额
                                    $optioncommission = $value[$maxkey];
                                }
                            }
                            //向数组尾部插入规格
                            array_push($price_all, $optioncommission);
                        }
                    }
                }else{//单规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        foreach ($value as $k => $v) {
                            if (strexists($v, '%')) {//判断有%符号则按百分比
                                array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                                continue;//退出
                            }
                            array_push($price_all, $v);//否则按固定金额
                        }
                    }
                }
                $commission = round(max($price_all), 2);
            }
        } else {//全局默认
            if($goods['marketprice'] <= $goods['maxprice']){
                $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
            }
            //按最高会员等级折扣
            if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
            }else{
                $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
            }
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
            }
            if ($level!='false' && !empty($level)) {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            } else {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            }
        }
        return $commission;
    }
    
    /**
     * 计算出此商品的合伙人收益
     * @param type $goodsid
     * @return type
     */
    public function getCopartnerCommission($option=array(),$goods,$level,$set){
        global $_W;
        $commission = 0;
        if($level == 'false'){
            return $commission;
        }
        //获取所有会员等级
        $alllevels = m('member')->getLevels();
        foreach($alllevels as $v){
            //获取最高的会员等级
            $maxlevel = max($v,$v['level']);
        }
        //商品最高会员等级价格
        $maxMemberLevelPrice = m('goods')->getMemberPrice($goods, $maxlevel);
        //提成计算价格规则 1最高会员折扣价 0零售价
        $calculatePriceType = $set['calculatePriceType'];
        if ($goods['hasCopartnerCommission'] == 1) {//商品独立规则
            $price = $goods['maxprice'];
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $price - $goods['costprice'];
            }
            $levelid = 'default';
            if ($level) {
                $levelid = 'level' . $level['id'];
            }
            $goods_commission = !empty($goods['copartnerCommission']) ? json_decode($goods['copartnerCommission'], true) : array();
             if ($goods_commission['type'] == 0) {//商品统一设置提成
                //如果零售价 <= 最大商品价格
                if($goods['marketprice'] <= $goods['maxprice']){
                    $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
                }
                //按最高会员等级折扣
                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                }else{
                    $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
                }                
                //是否减掉商品成本再计算提成
                if(!empty($set['isReduceGoodsCostprice'])){
                    $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
                }
                $commission = $set['level'] >= 1 ? ($goods['copartnerCommission1_rate'] > 0 ? ($goods['copartnerCommission1_rate'] * $commissionCalculatePrice / 100) : $goods['copartnerCommission1_pay']) : 0;
            } else {//商品详细设置
                $price_all = array();
                if(!empty($option)){//多规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        $maxkey = array_search(max($value),$value);
                        foreach ($option as $k => $v) {
                            $optioncommission = 0;
                            if(('option' . $v['id']) == $key) {                                
                                //按最高会员等级折扣
                                if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                                    $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
                                }else{
                                    $commissionCalculatePrice = $v['marketprice'];//商品最低零售价
                                }
                                //是否减掉商品成本再计算提成
                                if(!empty($set['isReduceGoodsCostprice'])){
                                    $commissionCalculatePrice = $commissionCalculatePrice - $v['costprice'];
                                }
                                if(strexists($value[$maxkey], '%')) {//判断有%符号则按百分比
                                    $optioncommission= (floatval(str_replace('%', '', $value[0]) / 100) * $commissionCalculatePrice);
                                } else {//否则按固定金额
                                    $optioncommission = $value[$maxkey];
                                }
                            }
                            //向数组尾部插入规格
                            array_push($price_all, $optioncommission);
                        }
                    }
                }else{//单规格
                    foreach ($goods_commission[$levelid] as $key => $value) {
                        foreach ($value as $k => $v) {
                            if (strexists($v, '%')) {//判断有%符号则按百分比
                                array_push($price_all, floatval(str_replace('%', '', $v) / 100) * $price);
                                continue;//退出
                            }
                            array_push($price_all, $v);//否则按固定金额
                        }
                    }
                }
                $commission = round(max($price_all), 2);
            }
        } else {//全局默认
            if($goods['marketprice'] <= $goods['maxprice']){
                $commissionCalculatePrice = $goods['marketprice'] = $goods['maxprice'];
            }
            //按最高会员等级折扣
            if($calculatePriceType == 1 && $maxMemberLevelPrice > 0){
                $commissionCalculatePrice = $maxMemberLevelPrice;//使用最高的会员等级价格
            }else{
                $commissionCalculatePrice = $goods['minprice'];//商品最低零售价
            }
            //是否减掉商品成本再计算提成
            if(!empty($set['isReduceGoodsCostprice'])){
                $price = $commissionCalculatePrice = $commissionCalculatePrice - $goods['costprice'];
            }
            if ($level!='false' && !empty($level)) {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($level['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            } else {//商品不参与提成默认
                $commission = $set['level'] >= 1 ? round($set['commission1'] * $commissionCalculatePrice / 100, 2) : 0;
            }
        }
        return $commission;
    }

    //分销商等级
    function getLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['isagent']) || $member['status']==0 || $member['agentblack'] ==1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_commission_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['agentlevel']));
        return $level;
    }

    //店长等级
    function getOwnerLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['is_owner']) || $member['owner_status']==0 || $member['owner_black'] ==1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_vrshop_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['owner_level']));
        return $level;
    }

    //合伙人等级
    function getCopartnerLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['is_copartner']) || $member['copartner_status']==0 || $member['copartner_black'] ==1) {
            return $level;
        }

        $level = pdo_fetch('select * from ' . tablename('elapp_shop_copartner_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['copartner_level']));

        return $level;
    }

    //医生等级
    function getDoctorLevel($openid){
        global $_W;
        $level = 'false';
        if (empty($openid)) {
            return $level;
        }
        $member = m('member')->getMember($openid);
        if (empty($member['is_doctor']) || $member['doctor_status']==0 || $member['doctor_black'] ==1) {
            return $level;
        }
        $level = pdo_fetch('select * from ' . tablename('elapp_shop_doctor_level') . ' where uniacid=:uniacid and id=:id limit 1', array(':uniacid' => $_W['uniacid'], ':id' => $member['doctor_level']));
        return $level;
    }

    /*
     * 华仔定制（分销文案）
     * */
    function get_offic_list(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $type = intval($_GPC['type']);
        $openid = trim($_W['openid']);
        $params = array(':goodsid'=>$id,':uniacid'=>$_W['uniacid']);
        $pindex = max(1, intval($_GPC['page']));
        $psize = 10;
        $condition = " and o.uniacid = :uniacid and o.goodsid = :goodsid and o.enabled = 1 ";
        if($type>0){
            $condition .= " and o.openid = :openid ";
            $params[':openid'] = $openid;
        }else{
            $condition .= " and o.chosen = 1 ";
        }
        $total = pdo_fetchcolumn('select count(1) from ' . tablename('elapp_shop_offic') . " as o where 1 {$condition}", $params);
        $list = array();
        if($total>0){
            $list = pdo_fetchall("SELECT o.*,m.avatar,m.nickname,g.thumb,g.title,g.minprice FROM ".tablename('elapp_shop_offic')." as o
                    left join ".tablename('elapp_shop_member')." as m on m.openid = o.openid
                    left join ".tablename('elapp_shop_goods')." as g on g.id = o.goodsid
                    where 1 {$condition} group by o.id order by o.displayorder desc, o.createtime desc, o.id desc LIMIT " . ($pindex - 1) * $psize . ',' . $psize, $params);
            $list = set_medias($list, 'avatar,thumb');
            foreach($list as &$row){
                if(empty($row['openid'])){
                    if(empty($row['avatar'])){
                        $row['avatar'] = $_W['shopset']['shop']['logo'];
                        $row['nickname'] = $_W['shopset']['shop']['name'];
                    }
                }
                $row['images'] = unserialize($row['images']);
                $row['images'] = set_medias($row['images']);
                $row['createtime'] = date("Y-m-d H:i",$row['createtime']);
            }
            unset($row);
        }
        show_json(1,array('officlist'=>$list,'total'=>$total,'pagesize'=>$psize));
    }

    function get_comment_list(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $level = trim($_GPC['level']);
        $params = array(':goodsid'=>$id,':uniacid'=>$_W['uniacid']);
        $pindex = max(1, intval($_GPC['page']));
        $psize = 10;
        $condition = "";
        if($level=='good'){
            $condition=" and level=5";
        } else if($level=='normal'){
            $condition=" and level>=2 and level<=4";
        }else if($level=='bad'){
            $condition=" and level<=1";
        }else if($level=='pic'){
            $condition=" and ifnull(images,'a:0:{}')<>'a:0:{}'";
        }
        $list = pdo_fetchall("select * from ".tablename('elapp_shop_order_comment')." "
            . "  where goodsid=:goodsid and uniacid=:uniacid and deleted=0 and checked=0 $condition order by istop desc, createtime desc, id desc LIMIT " . ($pindex - 1) * $psize . ',' . $psize, $params);
        foreach($list as &$row){
            $row['headimgurl'] = tomedia($row['headimgurl']);
            $row['createtime'] = date('Y-m-d H:i',$row['createtime']);
            $row['images'] = set_medias(iunserializer($row['images']));
            $row['reply_images'] = set_medias(iunserializer($row['reply_images']));
            $row['append_images'] = set_medias(iunserializer($row['append_images']));
            $row['append_reply_images'] = set_medias(iunserializer($row['append_reply_images']));
            $row['nickname'] = cut_str($row['nickname'], 1, 0).'**'.cut_str($row['nickname'], 1, -1);
        }
        unset($row);
        $total = pdo_fetchcolumn('select count(*) from ' . tablename('elapp_shop_order_comment') . " where goodsid=:goodsid  and uniacid=:uniacid and deleted=0 and checked=0 {$condition}", $params);
        show_json(1,array('list'=>$list,'total'=>$total,'pagesize'=>$psize));
    }
    function qrcode() {
        global $_W, $_GPC;
        $url = $_W['root'];
        show_json(1, array('url' => m('qrcode')->createQrcode($url)));
    }

    //多商户
    protected function merchData() {
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        return array(
            'is_openmerch' => $is_openmerch,
            'merch_plugin' => $merch_plugin,
            'merch_data' => $merch_data
        );
    }

    //获取当前商品及当前用户组可领取的免费优惠券
    function getCouponsbygood($goodid){
        global $_W, $_GPC;
        //多商户
        $merchdata = $this->merchData();
        extract($merchdata);
        // 读取 优惠券
        $time = time();
        $param = array();
        $param[':uniacid'] = $_W['uniacid'];
        $sql = "select id,timelimit,coupontype,timedays,timestart,timeend,thumb,couponname,enough,backtype,deduct,discount,backmoney,backcredit,backredpack,bgcolor,thumb,credit,money,getmax,merchid,total as t,islimitlevel,limitmemberlevels,limitagentlevels,limitpartnerlevels,limitaagentlevels,limitgoodcatetype,limitgoodcateids,limitgoodtype,limitgoodids,tagtitle,settitlecolor,titlecolor from " . tablename('elapp_shop_coupon') . " c ";
        $sql.=" where uniacid=:uniacid and money=0 and credit = 0 and coupontype=0";
        if ($is_openmerch == 0) {
            $sql .= ' and merchid=0';
        }else {
            if (!empty($_GPC['merchid'])) {
                $sql .= ' and merchid=:merchid';
                $param[':merchid'] = intval($_GPC['merchid']);
            }else{
                $sql .= ' and merchid=0';
            }
        }

        //分销商限制
        $hascommission = false;
        $plugin_com = p('commission');
        if ($plugin_com) {
            $plugin_com_set = $plugin_com->getSet();
            $hascommission = !empty($plugin_com_set['level']);
            if(empty($plugin_com_set['level'])){
                $sql .= ' and ( limitagentlevels = "" or  limitagentlevels is null )';
            }
        }else{
            $sql .= ' and ( limitagentlevels = "" or  limitagentlevels is null )';
        }
        //店员限制
        $hasclerk = false;
        $plugin_clerk = p('clerk');
        if ($plugin_clerk) {
            $plugin_clerk_set = $plugin_clerk->getSet();
            $hasclerk = !empty($plugin_clerk_set['level']);
            if(empty($plugin_clerk_set['level'])){
                $sql .= ' and ( limitclerklevels = "" or  limitclerklevels is null )';
            }
        }else{
            $sql .= ' and ( limitclerklevels = "" or  limitclerklevels is null )';
        }
        //医生限制
        $hasdoctor = false;
        $plugin_doctor = p('doctor');
        if ($plugin_doctor) {
            $plugin_doctor_set = $plugin_doctor->getSet();
            $hasdoctor = !empty($plugin_doctor_set['level']);
            if(empty($plugin_doctor_set['level'])) {
                $sql .= ' and ( limitdoctorlevels = "" or  limitdoctorlevels is null )';
            }
        }else{
            $sql .= ' and ( limitdoctorlevels = "" or  limitdoctorlevels is null )';
        }
        //店长限制
        $hasowner = false;
        $plugin_owner = p('vrshop');
        if ($plugin_owner) {
            $plugin_owner_set = $plugin_owner->getSet();
            $hasowner = !empty($plugin_owner_set['level']);
            if(empty($plugin_owner_set['level'])) {
                $sql .= ' and ( limitownerlevels = "" or  limitownerlevels is null )';
            }
        }else{
            $sql .= ' and ( limitownerlevels = "" or  limitownerlevels is null )';
        }
        //合伙人限制
        $hascopartner = false;
        $plugin_copartner = p('copartner');
        if ($plugin_copartner) {
            $plugin_copartner_set = $plugin_copartner->getSet();
            $hascopartner = !empty($plugin_copartner_set['level']);
            if(empty($plugin_copartner_set['level'])) {
                $sql .= ' and ( limitcopartnerlevels = "" or  limitcopartnerlevels is null )';
            }
        }else{
            $sql .= ' and ( limitcopartnerlevels = "" or  limitcopartnerlevels is null )';
        }

        //股东限制
        $hasglobonus = false;
        $plugin_globonus = p('globonus');
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !empty($plugin_globonus_set['open']);
            if(empty($plugin_globonus_set['open'])) {
                $sql .= ' and ( limitpartnerlevels = ""  or  limitpartnerlevels is null )';
            }
        }else{
            $sql .= ' and ( limitpartnerlevels = ""  or  limitpartnerlevels is null )';
        }
        //区域代理限制
        $hasabonus = false;
        $plugin_abonus = p('abonus');
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !empty($plugin_abonus_set['open']);
            if(empty($plugin_abonus_set['open'])){
                $sql .= ' and ( limitaagentlevels = "" or  limitaagentlevels is null )';
            }
        }else{
            $sql .= ' and ( limitaagentlevels = "" or  limitaagentlevels is null )';
        }
        $sql.=" and gettype=1 and (total=-1 or total>0) and ( timelimit = 0 or  (timelimit=1 and timeend>unix_timestamp()))";
        $sql.=" order by displayorder desc, id desc  ";
        $list = set_medias(pdo_fetchall($sql, $param), 'thumb');
        if(empty($list)){
            $list=array();
        }
        if(!empty($goodid)){
            $goodparam[':uniacid'] = $_W['uniacid'];
            $goodparam[':id'] = $goodid;
            $sql = "select id,cates,marketprice,merchid   from " . tablename('elapp_shop_goods') ;
            $sql.=" where uniacid=:uniacid and id =:id order by id desc LIMIT 1 "; //类型+最低消费+示使用
            $good = pdo_fetch($sql, $goodparam);
        }
        $cates = explode(',',$good['cates']);
        if (!empty($list)) {
            foreach ($list as $key =>&$row) {
                $row = com('coupon')->setCoupon($row, time());
                $row['thumb'] = tomedia($row['thumb']);
                $row['timestr'] = "永久有效";
                if (empty($row['timelimit'])) {
                    if (!empty($row['timedays'])) {
                        $row['timestr'] = "自领取日后".$row['timedays']."天有效";
                    }
                } else {
                    if ($row['timestart'] >= $time) {
                        $row['timestr'] = '有效期至:'.date('Y-m-d', $row['timestart']) . '-' . date('Y-m-d', $row['timeend']);
                    } else {
                        $row['timestr'] = '有效期至:'.date('Y-m-d', $row['timeend']);
                    }
                }
                if ($row['backtype'] == 0) {
                    $row['backstr'] = '立减';
                    $row['backmoney'] = (float)$row['deduct'];
                    $row['backpre'] = true;
                    if($row['enough']=='0' || $row['fullenough']=='0'){
                        $row['color']='org ';
                    }else{
                        $row['color']='blue';
                    }
                } else if ($row['backtype'] == 1) {
                    $row['backstr'] = '折';
                    $row['backmoney'] = (float)$row['discount'];
                    $row['color']='red ';
                } else if ($row['backtype'] == 2) {
                    if($row['coupontype']=='0'){
                        $row['color']='red ';
                    }else{
                        $row['color']='pink ';
                    }
                    if ($row['backredpack'] > 0) {
                        $row['backstr'] = '返现';
                        $row['backmoney'] = (float)$row['backredpack'];
                        $row['backpre'] = true;
                    } else if ($row['backmoney'] > 0) {
                        $row['backstr'] = '返利';
                        $row['backmoney'] = (float)$row['backmoney'];
                        $row['backpre'] = true;
                    } else if (!empty($row['backcredit'])) {
                        $row['backstr'] = '返积分';
                        $row['backmoney'] = (float)$row['backcredit'];
                    }
                }
                //分类限制
                $limitmemberlevels =explode(",", $row['limitmemberlevels']);
                $limitagentlevels =explode(",", $row['limitagentlevels']);
                $limitpartnerlevels=explode(",", $row['limitpartnerlevels']);
                $limitaagentlevels=explode(",", $row['limitaagentlevels']);
                $p= 0;
                if($row['islimitlevel'] ==1) {
                    $openid = trim($_W['openid']);
                    $member = m('member')->getMember($openid);
                    if(!empty($row['limitmemberlevels'])||$row['limitmemberlevels']=='0'){
                        //会员等级
                        $level1 = pdo_fetchall('select * from ' . tablename('elapp_shop_member_level') . ' where uniacid=:uniacid and  id in ('.$row['limitmemberlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if (in_array($member['level'],$limitmemberlevels)){
                            $p= 1;
                        }
                    };
                    if((!empty($row['limitagentlevels'])||$row['limitagentlevels']=='0')&&$hascommission) {
                        //分销商等级
                        $level2 = pdo_fetchall('select * from ' . tablename('elapp_shop_commission_level') . ' where uniacid=:uniacid and id  in ('.$row['limitagentlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['isagent']=='1' && $member['status']=='1'){
                            if (in_array($member['agentlevel'],$limitagentlevels)){
                                $p= 1;
                            }
                        }
                    }

                    if((!empty($row['limitpartnerlevels'])||$row['limitpartnerlevels']=='0')&&$hasglobonus) {
                        //股东等级
                        $level3 = pdo_fetchall('select * from ' . tablename('elapp_shop_globonus_level') . ' where uniacid=:uniacid and  id in('.$row['limitpartnerlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['ispartner']=='1'&&$member['partnerstatus']=='1'){
                            if (in_array($member['partnerlevel'],$limitpartnerlevels)){
                                $p= 1;
                            }
                        }
                    }
                    if((!empty($row['limitaagentlevels'])||$row['limitaagentlevels']=='0')&&$hasabonus) {
                        //区域代理
                        $level4 = pdo_fetchall('select * from ' . tablename('elapp_shop_abonus_level') . ' where uniacid=:uniacid and  id in ('.$row['limitaagentlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['isaagent']=='1'&&$member['aagentstatus']=='1'){
                            if (in_array($member['aagentlevel'],$limitaagentlevels)){
                                $p= 1;
                            }
                        }
                    }
                    if((!empty($row['limitclerklevels'])||$row['limitclerklevels']=='0')&&$hasclerk) {
                        //店员等级
                        $level5 = pdo_fetchall('select * from ' . tablename('elapp_shop_clerk_level') . ' where uniacid=:uniacid and id  in ('.$row['limitclerklevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['is_clerk']=='1'&&$member['clerk_status']=='1'){
                            if (in_array($member['clerk_level'],$limitclerklevels)){
                                $p= 1;
                            }
                        }
                    }
                    if((!empty($row['limitownerlevels'])||$row['limitownerlevels']=='0')&&$hasowner) {
                        //店长等级
                        $level6 = pdo_fetchall('select * from ' . tablename('elapp_shop_vrshop_level') . ' where uniacid=:uniacid and id  in ('.$row['limiownerlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['is_owner']=='1'&&$member['owner_status']=='1'){
                            if (in_array($member['owner_level'],$limitownerlevels)){
                                $p= 1;
                            }
                        }
                    }
                    if((!empty($row['limitcopartnerlevels'])||$row['limitcopartnerlevels']=='0')&&$hascopartner) {
                        //合伙人等级
                        $level7 = pdo_fetchall('select * from ' . tablename('elapp_shop_copartner_level') . ' where uniacid=:uniacid and id  in ('.$row['limitcopartnerlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['is_copartner']=='1'&&$member['copartner_status']=='1'){
                            if (in_array($member['copartner_level'],$limitcopartnerlevels)){
                                $p= 1;
                            }
                        }
                    }
                    if((!empty($row['limitdoctorlevels'])||$row['limitdoctorlevels']=='0')&&$hasdoctor) {
                        //医生等级
                        $level8 = pdo_fetchall('select * from ' . tablename('elapp_shop_doctor_level') . ' where uniacid=:uniacid and id  in ('.$row['limitdoctorlevels'].') ', array(':uniacid' => $_W['uniacid']));
                        if($member['is_doctor']=='1'&&$member['doctor_status']=='1'){
                            if (in_array($member['doctor_level'],$limitdoctorlevels)){
                                $p= 1;
                            }
                        }
                    }
                }else{
                    $p= 1;
                }
                if($p== 1){
                    $p=0;
                    $limitcateids =explode(',',$row['limitgoodcateids']);
                    $limitgoodids =explode(',',$row['limitgoodids']);
                    if($row['limitgoodcatetype']==0&&$row['limitgoodtype']==0){
                        $p= 1;
                    }
                    if($row['limitgoodcatetype']==1){
                        $result = array_intersect($cates,$limitcateids);
                        if(count($result)>0) {
                            $p= 1;
                        }
                    }
                    if($row['limitgoodtype']==1){
                        $isin = in_array($good['id'],$limitgoodids);
                        if($isin){
                            $p= 1;
                        }
                    }
                    //判断当前优惠券是否有可以生效的商品;
                    if($p==0){
                        unset($list[$key]);
                    }
                }else{
                    unset($list[$key]);
                }
            }
            unset($row);
        }
        return array_values($list);
    }

    //商品详情页领取可用优惠券
    public function pay($a=array(), $b=array()){
        global $_W, $_GPC;
        $openid = $_W['openid'];
        $id = intval($_GPC['id']);
        $coupon = pdo_fetch('select * from ' . tablename('elapp_shop_coupon') . ' where id=:id and uniacid=:uniacid  limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        $coupon = com('coupon')->setCoupon($coupon, time());
        //无法从领券中心领取
        if (empty($coupon['gettype'])) {
            show_json(-1, '无法'.$coupon['gettypestr']);
        }
        if ($coupon['total'] != -1) {
            if ($coupon['total'] <= 0) {
                show_json(-1, '优惠券数量不足'); //数量不足
            }
        }
        if (!$coupon['canget']) {
            show_json(-1, "您已超出{$coupon['gettypestr']}次数限制"); //已经领取完
        }
        if($coupon['money'] > 0||$coupon['credit']>0){
            show_json(-1, '此优惠券需要前往领卷中心兑换'); //数量不足
        }
        $logno = m('common')->createNO('coupon_log', 'logno', 'CC');
        //生成日志
        $log = array(
            'uniacid' => $_W['uniacid'],
            'merchid' => $coupon['merchid'],
            'openid' => $openid,
            'logno' => $logno,
            'couponid' => $id,
            'status' => 0,
            'paystatus' => -1 ,
            'creditstatus' => -1 ,
            'createtime' => time(),
            'getfrom'=>1
        );
        pdo_insert('elapp_shop_coupon_log', $log);
        $result = com('coupon')->payResult($log['logno']);
        if(is_error($result)){
            show_json($result['errno'],$result['message']);
        }
        show_json(1,array('url'=>$result['url'],'dataid'=>$result['dataid'],'coupontype'=>$result['coupontype']));
    }
}