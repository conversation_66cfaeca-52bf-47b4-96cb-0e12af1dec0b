<?php
namespace app\controller\goods;

use app\controller\MobilePage;
use app\model;

class IndexController extends MobilePage
{
    public function main()
    {
        global $_W, $_GPC;
        $shopSysset  = m('common')->getSysset('shop');
        $allcategory = m('shop')->getCategory();
        $member      = m('member')->getMember($_W['openid']);
        //没有推荐人不显示会员权益信息 默认显示
        $memberSysset              = m('common')->getSysset('member');
        $is_not_show_member_equity = 0;
        $not_show_member_equity    = empty($memberSysset['not_show_member_equity']) ? 0 : $memberSysset['not_show_member_equity'];
        if (empty($member['onmid']) && $not_show_member_equity == 1) {
            $is_not_show_member_equity = 1;
        }
        //商品划线价是否显示
        $isMarketPrice     = intval($_W['shopset']['shop']['isMarketPrice']);
        $catlevel          = intval($_W['shopset']['category']['level']);
        $opencategory      = true;
        $plugin_commission = p('commission');
        if ($plugin_commission && (0 < intval($_W['shopset']['commission']['level']))) {
            $mid = intval($_GPC['mid']);

            if (!(empty($mid)) && empty($_W['shopset']['commission']['closemyshop']) && !(empty($_W['shopset']['commission']['select_goods']))) {
                $shop = p('commission')->getShop($mid);

                if (empty($shop['selectcategory']) && !(empty($shop['selectgoods']))) {
                    $opencategory = false;
                }
            }
        }
        //历史搜索记录
        if ($redis = redis()) {
            $redis_key    = 'search-' . $_W['openid'];
            $keywordsData = $redis->zRevRange($redis_key, 0, 9);
        } else {
            if (!empty($_COOKIE['keywords'])) {
                $keywordsData = explode(',', $_COOKIE['keywords']);
            }
        }
        //多商户
        $merchdata = $this->merchData();
        extract($merchdata);

        include $this->template();
    }
    //赠品
    public function gift()
    {
        global $_W, $_GPC;
        $uniacid     = $_W['uniacid'];
        $giftid      = intval($_GPC['id']);
        $gift        = pdo_fetch('select * from ' . tablename('elapp_shop_gift') . ' where uniacid = ' . $uniacid . ' and id = ' . $giftid . ' and starttime <= ' . time() . ' and endtime >= ' . time() . ' and status = 1 ');
        $giftgoodsid = explode(',', $gift['giftgoodsid']);
        $giftgoods   = [];
        if (!(empty($giftgoodsid))) {
            foreach ($giftgoodsid as $key => $value) {
                $giftgoods[$key] = pdo_fetch('select id,status,title,thumb,marketprice,stock from ' . tablename('elapp_shop_goods') . ' where uniacid = ' . $uniacid . ' and deleted = 0 and stock > 0 and id = ' . $value . ' and status = 2 ');
            }
            $giftgoods = array_filter($giftgoods);
        }
        include $this->template();
    }

    public function get_list()
    {
        global $_GPC;
        global $_W;
        $args = [
            'pagesize'    => 10,
            'page'        => intval($_GPC['page']),
            'isnew'       => trim($_GPC['isnew']),
            'ishot'       => trim($_GPC['ishot']),
            'isrecommand' => trim($_GPC['isrecommand']),
            'isdiscount'  => trim($_GPC['isdiscount']),
            'istime'      => trim($_GPC['istime']),
            'issendfree'  => trim($_GPC['issendfree']),
            'keywords'    => trim($_GPC['keywords']),
            'cate'        => trim($_GPC['cate']),
            'order'       => trim($_GPC['order']),
            'by'          => trim($_GPC['by']),
            'isrx'        => trim($_GPC['isrx']),
            'isotc'       => trim($_GPC['isotc']),
            'isdc'        => trim($_GPC['isdc']),
            'isnhc'       => trim($_GPC['isnhc']),
            'isnoun'      => trim($_GPC['isnoun']),
            'issex'       => trim($_GPC['issex']),
            'isga'        => trim($_GPC['isga']),
        ];

        if (!empty(trim($_GPC['keywords']))) {
            $keywords = trim($_GPC['keywords']);
            // redis扩展版本低于5.0，不兼容 zPopMin 函数
            if (($redis = redis()) && version_compare(phpversion('redis'), '5.0', '>=')) {
                $redis_key = 'search-' . $_W['openid'];
                if ($redis->zCard($redis_key) >= 10) {
                    $del = $redis->zPopMin($redis_key);
                }

                $redis->zAdd($redis_key, time(), $keywords);
            } else {
                if (!empty($_COOKIE['keywords'])) {
                    $history = explode(',', $_COOKIE['keywords']);
                    if (count($history) >= 10) {
                        array_pop($history);
                    }

                    array_unshift($history, $keywords);
                    $history = array_unique($history);
                    setcookie('keywords', implode(',', $history), time() + 3600 * 24 * 30, '/');
                } else {
                    setcookie('keywords', $keywords, time() + 3600 * 24 * 30, '/');
                }
            }
        }
        //分销小店 Hlei
        $plugin_commission = p('commission');
        if ($plugin_commission && (0 < intval($_W['shopset']['commission']['level'])) && empty($_W['shopset']['commission']['closemyshop']) && !(empty($_W['shopset']['commission']['select_goods']))) {
            $frommyshop = intval($_GPC['frommyshop']);
            $mid        = intval($_GPC['mid']);
            if (!(empty($mid)) && !(empty($frommyshop))) {
                $shop = p('commission')->getShop($mid);
                if (!(empty($shop['selectgoods']))) {
                    $args['ids'] = $shop['goodsids'];
                }
            }
        }
        //店员小店
        $plugin_clerk = p('clerk');
        if ($plugin_clerk && (0 < intval($_W['shopset']['clerk']['level'])) && empty($_W['shopset']['clerk']['closemyshop']) && !(empty($_W['shopset']['clerk']['select_goods']))) {
            $frommyshop = intval($_GPC['frommyshop']);
            $mid        = intval($_GPC['mid']);
            if (!(empty($mid)) && !(empty($frommyshop))) {
                $shop = p('clerk')->getShop($mid);
                if (!(empty($shop['selectgoods']))) {
                    $args['ids'] = $shop['goodsids'];
                }
            }
        }
        $this->_condition($args);
    }

    public function query()
    {
        global $_GPC;
        global $_W;
        $args = ['pagesize' => 10, 'page' => intval($_GPC['page']), 'isnew' => trim($_GPC['isnew']), 'ishot' => trim($_GPC['ishot']), 'isrecommand' => trim($_GPC['isrecommand']), 'isdiscount' => trim($_GPC['isdiscount']), 'istime' => trim($_GPC['istime']), 'keywords' => trim($_GPC['keywords']), 'cate' => trim($_GPC['cate']), 'order' => trim($_GPC['order']), 'by' => trim($_GPC['by'])];
        $this->_condition($args);
    }

    private function _condition($args)
    {
        global $_GPC;
        $merch_plugin = p('merch');
        $merch_data   = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $args['merchid'] = intval($_GPC['merchid']);
        }
        if (isset($_GPC['nocommission'])) {
            $args['nocommission'] = intval($_GPC['nocommission']);
        }
        $goods = m('goods')->getList($args);
        show_json(1, ['list' => array_values($goods['list']), 'total' => $goods['total'], 'pagesize' => $args['pagesize']]);
    }
    //多商户
    protected function merchData()
    {
        $merch_plugin = p('merch');
        $merch_data   = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $is_openmerch = 1;
        } else {
            $is_openmerch = 0;
        }
        return [
            'is_openmerch' => $is_openmerch,
            'merch_plugin' => $merch_plugin,
            'merch_data'   => $merch_data,
        ];
    }

}
