<?php
namespace app\controller\goods;
use app\controller\MobilePage;

class HistoryController extends MobilePage {
	public function main(){
		global $_W, $_GPC;
		$redis = redis();
		// 接收请求类型参数的值
		$type = $_GET['type'];
		// 模拟用户的id，因为每个用户搜索的内容不同，需要进行区分
		$user_id = 'user-1';
		// 如果请求类型为添加
		if ($type == 'add') {
			// 接收输入的关键字
			$keyword = $_GET['keyword'];
			// 读取当前用户队列中存储的关键字个数，即队列的长度
			$len = $redis->llen($user_id);
			// 如果个数大于等于 5 个，则删除最开始搜索的关键字，加入最新搜索的关键字
			if ($len >= 5) {
				// 移除队列左侧的第一个关键字
				$redis->lPop($user_id);
				// 在队列右侧加入新的关键字
				$redis->rPush($user_id, $keyword);
			} else {
				// 不多于 5 个直接在队列右侧加入新的关键字
				$redis->rPush($user_id, $keyword);
			}
		}
		// 如果请求类型为读取
		if ($type == 'read') {
			// 从队列左侧依次取出 5 个关键字
			$history = $redis->lrange($user_id, 0, 4);
			// 转为 json 格式的数据并输出到页面中供 Ajax 使用
			echo json_encode($history, JSON_UNESCAPED_UNICODE);
		}

	}
	//赠品
	public function gift(){
		global $_W, $_GPC;
		$uniacid = $_W['uniacid'];
		$giftid = intval($_GPC['id']);
		$gift = pdo_fetch('select * from ' . tablename('elapp_shop_gift') . ' where uniacid = ' . $uniacid . ' and id = ' . $giftid . ' and starttime <= ' . time() . ' and endtime >= ' . time() . ' and status = 1 ');
		$giftgoodsid = explode(',', $gift['giftgoodsid']);
		$giftgoods = array();
		if (!(empty($giftgoodsid))) {
			foreach ($giftgoodsid as $key => $value ) {
				$giftgoods[$key] = pdo_fetch('select id,status,title,thumb,marketprice from ' . tablename('elapp_shop_goods') . ' where uniacid = ' . $uniacid . ' and deleted = 0 and stock > 0 and id = ' . $value . ' and status = 2 ');
			}
			$giftgoods = array_filter($giftgoods);
		}
		include $this->template();
	}

	public function get_list(){
		global $_GPC;
		global $_W;
		$args = array('pagesize' => 10, 'page' => intval($_GPC['page']), 'isnew' => trim($_GPC['isnew']), 'ishot' => trim($_GPC['ishot']), 'isrecommand' => trim($_GPC['isrecommand']), 'isdiscount' => trim($_GPC['isdiscount']), 'istime' => trim($_GPC['istime']), 'issendfree' => trim($_GPC['issendfree']), 'keywords' => trim($_GPC['keywords']), 'cate' => trim($_GPC['cate']), 'order' => trim($_GPC['order']), 'by' => trim($_GPC['by']));
		$plugin_commission = p('commission');
		if ($plugin_commission && (0 < intval($_W['shopset']['commission']['level'])) && empty($_W['shopset']['commission']['closemyshop']) && !(empty($_W['shopset']['commission']['select_goods']))) {
			$frommyshop = intval($_GPC['frommyshop']);
			$mid = intval($_GPC['mid']);
			if (!(empty($mid)) && !(empty($frommyshop))) {
				$shop = p('commission')->getShop($mid);
				if (!(empty($shop['selectgoods']))) {
					$args['ids'] = $shop['goodsids'];
				}
			}
		}
		$plugin_clerk = p('clerk');
		if ($plugin_clerk && (0 < intval($_W['shopset']['clerk']['isopen'])) && (0 < intval($_W['shopset']['clerk']['level'])) && empty($_W['shopset']['clerk']['closemyshop']) && !(empty($_W['shopset']['clerk']['select_goods']))) {
			$frommyshop = intval($_GPC['frommyshop']);
			$mid = intval($_GPC['mid']);
			if (!(empty($mid)) && !(empty($frommyshop))) {
				$shop = p('clerk')->getShop($mid);
				if (!(empty($shop['selectgoods']))) {
					$args['ids'] = $shop['goodsids'];
				}
			}
		}
		$this->_condition($args);
	}

	public function query(){
		global $_GPC;
		global $_W;
		$args = array('pagesize' => 10, 'page' => intval($_GPC['page']), 'isnew' => trim($_GPC['isnew']), 'ishot' => trim($_GPC['ishot']), 'isrecommand' => trim($_GPC['isrecommand']), 'isdiscount' => trim($_GPC['isdiscount']), 'istime' => trim($_GPC['istime']), 'keywords' => trim($_GPC['keywords']), 'cate' => trim($_GPC['cate']), 'order' => trim($_GPC['order']), 'by' => trim($_GPC['by']));
		$this->_condition($args);
	}

	private function _condition($args){
		global $_GPC;
		$merch_plugin = p('merch');
		$merch_data = m('common')->getPluginset('merch');
		if ($merch_plugin && $merch_data['is_openmerch']) {
			$args['merchid'] = intval($_GPC['merchid']);
		}
		if (isset($_GPC['nocommission'])) {
			$args['nocommission'] = intval($_GPC['nocommission']);
		}
		$goods = m('goods')->getList($args);
		show_json(1, array('list' => $goods['list'], 'total' => $goods['total'], 'pagesize' => $args['pagesize']));
	}
	//多商户
	protected function merchData() {
		$merch_plugin = p('merch');
		$merch_data = m('common')->getPluginset('merch');
		if ($merch_plugin && $merch_data['is_openmerch']) {
			$is_openmerch = 1;
		} else {
			$is_openmerch = 0;
		}
		return array(
			'is_openmerch' => $is_openmerch,
			'merch_plugin' => $merch_plugin,
			'merch_data' => $merch_data
		);
	}
}