<?php
namespace app\controller\verify;
use app\controller\MobilePage;

class VerifyorderController extends MobilePage{

    public function orderData(){
        global $_W, $_GPC;
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;
        $openid = $_W['openid'];
        $condition = " o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0 AND ( o.status = 3 and (isverify=1 or o.istrade=1)) and o.verifyopenid = :openid";
        $uniacid = $_W['uniacid'];
        $paras = $paras1 = array(':uniacid' => $uniacid, ':openid' => $openid);
        //订单号，核销员，核销门店，核销码，会员姓名，收件人姓名，快递单号，商品名称，商品编码
        if (!empty($_GPC['searchfield']) && !empty($_GPC['keyword'])) {
            $searchfield = trim(strtolower($_GPC['searchfield']));
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'], ENT_QUOTES);
            $sqlcondition = '';
            if ($searchfield == 'ordersn') {
                $condition .= " AND locate(:keyword,o.ordersn)>0";
            } else if ($searchfield == 'member') {
                $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0)";
            } else if ($searchfield == 'address') {
                $condition .= " AND ( locate(:keyword,a.realname)>0 or locate(:keyword,a.mobile)>0 or locate(:keyword,o.carrier)>0)";
            } else if ($searchfield == 'expresssn') {
                $condition .= " AND locate(:keyword,o.expresssn)>0";
            } else if ($searchfield == 'saler') {
                $condition .= " AND (locate(:keyword,sm.realname)>0 or locate(:keyword,sm.mobile)>0 or locate(:keyword,sm.nickname)>0 or locate(:keyword,s.salername)>0 )";
            } else if ($searchfield == 'verifycode') {
                $condition .= " AND (verifycode=:keyword or locate(:keyword,o.verifycodes)>0)";
            } else if ($searchfield == 'store') {
                $condition .= " AND (locate(:keyword,store.storename)>0)";
                $sqlcondition = " left join " . tablename('elapp_shop_store') . " store on store.id = o.verifystoreid and store.uniacid=o.uniacid";
            } else if ($searchfield == 'goodstitle') {
                $sqlcondition = " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (locate(:keyword,g.title)>0)) gs on gs.orderid=o.id";
            } else if ($searchfield == 'goodssn') {
                $sqlcondition = " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (((locate(:keyword,g.goodssn)>0)) or (locate(:keyword,og.goodssn)>0))) gs on gs.orderid=o.id";
            }
        }

        $authorid = intval($_GPC['authorid']);
        $author = p('author');
        if ($author && !empty($authorid)) {
            $condition .= " and o.authorid = :authorid";
            $paras[':authorid'] = $authorid;
        }

        if ($condition != ' o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0 AND ( o.status = 3 and (isverify=1 or o.istrade=1)) and o.verifyopenid = :openid' || !empty($sqlcondition)) {
            if ($this->merchPluginExists()) {
                $sql = "select o.* , a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress, d.dispatchname,m.nickname,m.id as mid,m.realname as mrealname,m.mobile as mmobile,sm.id as salerid,sm.nickname as salernickname,s.salername,
                  r.rtype,r.status as rstatus,o.sendtype,0 as isverifygoods_order from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " left join " . tablename('elapp_shop_saler') . " s on s.openid = o.verifyopenid and s.uniacid=o.uniacid"
                    . " left join " . tablename('elapp_shop_merch_saler') . " ms on ms.openid = o.verifyopenid and ms.uniacid=o.uniacid"
                    . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                    . " $sqlcondition where $condition  GROUP BY o.id ORDER BY o.createtime DESC  ";
            } else {
                $sql = "select o.* , a.realname as arealname,a.mobile as amobile,a.province as aprovince ,a.city as acity , a.area as aarea, a.street as astreet,a.address as aaddress, d.dispatchname,m.nickname,m.id as mid,m.realname as mrealname,m.mobile as mmobile,sm.id as salerid,sm.nickname as salernickname,s.salername,
                  r.rtype,r.status as rstatus,o.sendtype,0 as isverifygoods_order from " . tablename('elapp_shop_order') . " o"
                    . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                    . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                    . " left join " . tablename('elapp_shop_member_address') . " a on a.id=o.addressid "
                    . " left join " . tablename('elapp_shop_dispatch') . " d on d.id = o.dispatchid "
                    . " left join " . tablename('elapp_shop_saler') . " s on s.openid = o.verifyopenid and s.uniacid=o.uniacid"
                    //. " left join " . tablename('elapp_shop_merch_saler') . " ms on ms.openid = o.verifyopenid and ms.uniacid=o.uniacid"
                    . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                    . " $sqlcondition where $condition  GROUP BY o.id ORDER BY o.createtime DESC  ";
            }

            if (empty($_GPC['export'])) {
                $sql .= "LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            }
            $list = pdo_fetchall($sql, $paras);
        } else {
            if ($this->merchPluginExists()) {
                $sql = "SELECT o.*,0 as isverifygoods_order FROM" . tablename("elapp_shop_order") . " o "
                    . " LEFT JOIN " . tablename("elapp_shop_saler") . " s ON o.verifyopenid=s.openid"
                    . " LEFT JOIN " . tablename("elapp_shop_merch_saler") . " ms ON o.verifyopenid=ms.openid"
                    . "   WHERE (s.openid=:openid or ms.openid=:openid) AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   GROUP BY o.id ORDER BY o.createtime DESC  ";
            } else {
                $sql = "SELECT o.*,0 as isverifygoods_order FROM" . tablename("elapp_shop_order") . " o "
                    . " LEFT JOIN " . tablename("elapp_shop_saler") . " s ON o.verifyopenid=s.openid"
                    //." LEFT JOIN ".tablename("elapp_shop_merch_saler")." ms ON o.verifyopenid=ms.openid"
                    . "   WHERE o.verifyopenid = :openid AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   GROUP BY o.id ORDER BY o.createtime DESC  ";
            }
            if (empty($_GPC['export'])) {
                $sql .= "LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            }
            $list = pdo_fetchall($sql, $paras);
            if (!empty($list)) {
                $refundid = '';
                $openid = '';
                $addressid = '';
                $dispatchid = '';
                $verifyopenid = '';
                foreach ($list as $key => $value) {
                    $refundid .= ",'{$value['refundid']}'";
                    $openid .= ",'{$value['openid']}'";
                    $addressid .= ",'{$value['addressid']}'";
                    $dispatchid .= ",'{$value['dispatchid']}'";
                    $verifyopenid .= ",'{$value['verifyopenid']}'";
                }
                $refundid = ltrim($refundid, ',');
                $openid = ltrim($openid, ',');
                $addressid = ltrim($addressid, ',');
                $dispatchid = ltrim($dispatchid, ',');
                $verifyopenid = ltrim($verifyopenid, ',');
                $refundid_array = pdo_fetchall("SELECT id,rtype,status as rstatus FROM " . tablename('elapp_shop_order_refund') . " WHERE id IN ({$refundid})", array(), 'id');
                $openid_array = pdo_fetchall("SELECT openid,nickname,id as mid,realname as mrealname,mobile as mmobile FROM " . tablename('elapp_shop_member') . " WHERE openid IN ({$openid}) AND uniacid={$_W['uniacid']}", array(), 'openid');
                $addressid_array = pdo_fetchall("SELECT id,realname as arealname,mobile as amobile,province as aprovince ,city as acity , area as aarea,address as aaddress FROM " . tablename('elapp_shop_member_address') . " WHERE id IN ({$addressid})", array(), 'id');
                $dispatchid_array = pdo_fetchall("SELECT id,dispatchname FROM " . tablename('elapp_shop_dispatch') . " WHERE id IN ({$dispatchid})", array(), 'id');
                $verifyopenid_array = pdo_fetchall("SELECT sm.id as salerid,sm.nickname as salernickname,sm.openid,s.salername FROM " . tablename('elapp_shop_saler') . " s LEFT JOIN " . tablename('elapp_shop_member') . " sm ON sm.openid = s.openid and sm.uniacid=s.uniacid WHERE s.openid IN ({$verifyopenid})", array(), 'openid');
                foreach ($list as $key => &$value) {
                    $list[$key]['rtype'] = $refundid_array[$value['refundid']]['rtype'];
                    $list[$key]['rstatus'] = $refundid_array[$value['refundid']]['rstatus'];
                    $list[$key]['nickname'] = $openid_array[$value['openid']]['nickname'];
                    $list[$key]['mid'] = $openid_array[$value['openid']]['mid'];
                    $list[$key]['mrealname'] = $openid_array[$value['openid']]['mrealname'];
                    $list[$key]['mmobile'] = $openid_array[$value['openid']]['mmobile'];
                    $list[$key]['arealname'] = $addressid_array[$value['addressid']]['arealname'];
                    $list[$key]['amobile'] = $addressid_array[$value['addressid']]['amobile'];
                    $list[$key]['aprovince'] = $addressid_array[$value['addressid']]['aprovince'];
                    $list[$key]['acity'] = $addressid_array[$value['addressid']]['acity'];
                    $list[$key]['aarea'] = $addressid_array[$value['addressid']]['aarea'];
                    $list[$key]['astreet'] = $addressid_array[$value['addressid']]['astreet'];
                    $list[$key]['aaddress'] = $addressid_array[$value['addressid']]['aaddress'];
                    $list[$key]['dispatchname'] = $dispatchid_array[$value['dispatchid']]['dispatchname'];
                    $list[$key]['salerid'] = $verifyopenid_array[$value['verifyopenid']]['salerid'];
                    $list[$key]['salernickname'] = $verifyopenid_array[$value['verifyopenid']]['salernickname'];
                    $list[$key]['salername'] = $verifyopenid_array[$value['verifyopenid']]['salername'];
                }
                unset($value);
            }
        }
        $paytype = array(
            '0' => array('css' => 'default', 'name' => '未支付'),
            '1' => array('css' => 'danger', 'name' => '余额支付'),
            '11' => array('css' => 'default', 'name' => '后台付款'),
            '2' => array('css' => 'danger', 'name' => '在线支付'),
            '21' => array('css' => 'success', 'name' => '微信支付'),
            '22' => array('css' => 'warning', 'name' => '支付宝支付'),
            '23' => array('css' => 'warning', 'name' => '银联支付'),
            '3' => array('css' => 'primary', 'name' => '货到付款'),
        );
        $orderstatus = array(
            '-1' => array('css' => 'default', 'name' => '已关闭'),
            '0' => array('css' => 'danger', 'name' => '待付款'),
            '1' => array('css' => 'info', 'name' => '待发货'),
            '2' => array('css' => 'warning', 'name' => '待收货'),
            '3' => array('css' => 'success', 'name' => '已完成')
        );


        $is_merch = array();
        if (!empty($list)) {
            $diy_title_data = array();
            $diy_data = array();
            foreach ($list as &$value) {
                //if ($is_merchname == 1) {
                    //$value['merchname'] = $merch_user[$value['merchid']]['merchname'] ? $merch_user[$value['merchid']]['merchname'] : '';
                //}
                $value['createtime'] = date("Y/m/d H:i:s", $value['createtime']);
                $value['verifytime'] = date("Y/m/d H:i:s", $value['verifytime']);

                $s = $value['status'];
                $pt = $value['paytype'];
                $value['statusvalue'] = $s;
                $value['statuscss'] = $orderstatus[$value['status']]['css'];
                $value['status'] = $orderstatus[$value['status']]['name'];
                if ($pt == 3 && empty($value['statusvalue'])) {
                    $value['statuscss'] = $orderstatus[1]['css'];
                    $value['status'] = $orderstatus[1]['name'];
                }
                if ($s == 1) {
                    if ($value['isverify'] == 1) {
                        $value['status'] = "待使用";
                        if ($value['sendtype'] > 0) {
                            $value['status'] = "部分使用";
                        }
                    } else if (empty($value['addressid'])) {
                        $value['status'] = "待取货";
                    } else {
                        if ($value['sendtype'] > 0) {
                            $value['status'] = "部分发货";
                        }
                    }
                }

                if ($s == -1) {
                    if (!empty($value['refundtime'])) {
                        $value['status'] = '已退款';
                    }
                }

                $value['paytypevalue'] = $pt;
                $value['css'] = $paytype[$pt]['css'];
                $value['paytype'] = $paytype[$pt]['name'];
                $value['dispatchname'] = empty($value['addressid']) ? '自提' : $value['dispatchname'];
                if (empty($value['dispatchname'])) {
                    $value['dispatchname'] = '快递';
                }
                if ($pt == 3) {
                    $value['dispatchname'] = "货到付款";
                } else if ($value['isverify'] == 1) {
                    $value['dispatchname'] = "线下核销";
                } else if ($value['isvirtual'] == 1) {
                    $value['dispatchname'] = "虚拟物品";
                } else if (!empty($value['virtual'])) {
                    $value['dispatchname'] = "虚拟物品(卡密)<br/>自动发货";
                }

                if ($value['dispatchtype'] == 1 || !empty($value['isverify']) || !empty($value['virtual']) || !empty($value['isvirtual'])) {
                    $value['address'] = '';
                    $carrier = iunserializer($value['carrier']);
                    if (is_array($carrier)) {
                        $value['addressdata']['realname'] = $value['realname'] = $carrier['carrier_realname'];
                        $value['addressdata']['mobile'] = $value['mobile'] = $carrier['carrier_mobile'];
                    }
                } else {
                    $address = iunserializer($value['address']);
                    $isarray = is_array($address);
                    $value['realname'] = $isarray ? $address['realname'] : $value['arealname'];
                    $value['mobile'] = $isarray ? $address['mobile'] : $value['amobile'];
                    $value['province'] = $isarray ? $address['province'] : $value['aprovince'];
                    $value['city'] = $isarray ? $address['city'] : $value['acity'];
                    $value['area'] = $isarray ? $address['area'] : $value['aarea'];
                    $value['street'] = $isarray ? $address['street'] : $value['astreet'];
                    $value['address'] = $isarray ? $address['address'] : $value['aaddress'];
                    $value['address_province'] = $value['province'];
                    $value['address_city'] = $value['city'];
                    $value['address_area'] = $value['area'];
                    $value['address_street'] = $value['street'];
                    $value['address_address'] = $value['address'];
                    $value['address'] = $value['province'] . " " . $value['city'] . " " . $value['area'] . " " . $value['address'];
                    $value['addressdata'] = array(
                        'realname' => $value['realname'],
                        'mobile' => $value['mobile'],
                        'address' => $value['address'],
                    );
                }
                //分销商
                $commission1 = -1;
                $commission2 = -1;
                $commission3 = -1;
                $m1 = false;
                $m2 = false;
                $m3 = false;
                if (!empty($level) && empty($agentid)) {
                    if (!empty($value['agentid'])) {
                        $m1 = m('member')->getMember($value['agentid']);
                        $commission1 = 0;
                        if (!empty($m1['agentid']) && $level > 1) {
                            $m2 = m('member')->getMember($m1['agentid']);
                            $commission2 = 0;
                            if (!empty($m2['agentid']) && $level > 2) {
                                $m3 = m('member')->getMember($m2['agentid']);
                                $commission3 = 0;
                            }
                        }
                    }
                }

                if (!empty($agentid)) {
                    $magent = m('member')->getMember($agentid);
                }
                //虚店店员
                $clerkCommission1 = -1;
                $clerkCommission2 = -1;
                $clerkCommission3 = -1;
                $clerkm1 = false;
                $clerkm2 = false;
                $clerkm3 = false;
                if (!empty($clerklevel) && empty($clerk_id)) {
                    if (!empty($value['clerk_id'])) {
                        $clerkm1 = m('member')->getMember($value['clerk_id']);
                        $clerkCommission1 = 0;
                        if (!empty($clerkm1['clerk_id']) && $clerklevel > 1) {
                            $clerkm2 = m('member')->getMember($clerkm1['clerk_id']);
                            $clerkCommission2 = 0;
                            if (!empty($clerkm2['clerk_id']) && $clerklevel > 2) {
                                $clerkm3 = m('member')->getMember($clerkm2['clerk_id']);
                                $clerkCommission3 = 0;
                            }
                        }
                    }
                }
                if (!empty($clerk_id)) {
                    $mclerk = m('member')->getMember($clerk_id);
                }
                //医生
                $doctorCommission1 = -1;
                $doctorCommission2 = -1;
                $doctorCommission3 = -1;
                $doctorm1 = false;
                $doctorm2 = false;
                $doctorm3 = false;
                if (!empty($doctor_level) && empty($doctor_id)) {
                    if (!empty($value['doctor_id'])) {
                        $doctorm1 = m('member')->getMember($value['doctor_id']);
                        $doctorCommission1 = 0;
                        if (!empty($doctorm1['doctor_id']) && $doctor_level > 1) {
                            $doctorm2 = m('member')->getMember($doctorm1['doctor_id']);
                            $doctorCommission2 = 0;
                            if (!empty($doctorm2['doctor_id']) && $doctor_level > 2) {
                                $doctorm3 = m('member')->getMember($doctorm2['doctor_id']);
                                $doctorCommission3 = 0;
                            }
                        }
                    }
                }
                if (!empty($doctor_id)) {
                    $mdoctor = m('member')->getMember($doctor_id);
                }

                //订单商品
                $order_goods = pdo_fetchall('select g.id,g.title,g.thumb,g.goodssn,og.goodssn as option_goodssn, g.productsn,og.productsn as option_productsn, og.total,
                    og.price,og.optionname as optiontitle, og.realprice,og.changeprice,og.oldprice,og.commission1,og.commission2,og.commission3,og.commissions,og.diyformdata,
                    og.diyformfields,op.specs,g.merchid,og.seckill,og.seckill_taskid,og.seckill_roomid,g.ispresell from ' . tablename('elapp_shop_order_goods') . ' og '
                    . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                    . ' left join ' . tablename('elapp_shop_goods_option') . ' op on og.optionid = op.id '
                    . ' where og.uniacid=:uniacid and og.orderid=:orderid ', array(':uniacid' => $uniacid, ':orderid' => $value['id']));
                $goods = '';
                foreach ($order_goods as &$og) {
                    $og['seckill_task'] = false;
                    $og['seckill_room'] = false;
                    if ($og['seckill']) {
                        $og['seckill_task'] = plugin_run('seckill::getTaskInfo', $og['seckill_taskid']);
                        $og['seckill_room'] = plugin_run('seckill::getRoomInfo', $og['seckill_taskid'], $og['seckill_roomid']);
                    }
                    //读取规格的图片
                    if (!empty($og['specs'])) {
                        $thumb = m('goods')->getSpecThumb($og['specs']);
                        if (!empty($thumb)) {
                            $og['thumb'] = $thumb;
                        }
                    }
                    $goods .= "" . $og['title'] . "\r\n";
                    if (!empty($og['optiontitle'])) {
                        $goods .= " 规格: " . $og['optiontitle'];
                    }
                    if (!empty($og['option_goodssn'])) {
                        $og['goodssn'] = $og['option_goodssn'];
                    }
                    if (!empty($og['option_productsn'])) {
                        $og['productsn'] = $og['option_productsn'];
                    }
                    if (!empty($og['goodssn'])) {
                        $goods .= ' 商品编号: ' . $og['goodssn'];
                    }
                    if (!empty($og['productsn'])) {
                        $goods .= ' 商品条码: ' . $og['productsn'];
                    }
                    $goods .= ' 单价: ' . ($og['price'] / $og['total']) . ' 折扣后: ' . ($og['realprice'] / $og['total']) . ' 数量: ' . $og['total'] . ' 总价: ' . $og['price'] . " 折扣后: " . $og['realprice'] . "\r\n ";

                    if (p('diyform') && !empty($og['diyformfields']) && !empty($og['diyformdata'])) {
                        $diyformdata_array = p('diyform')->getDatas(iunserializer($og['diyformfields']), iunserializer($og['diyformdata']), 1);
                        $diyformdata = "";

                        $dflag = 1;
                        foreach ($diyformdata_array as $da) {
                            if (!empty($diy_title_data)) {
                                if (array_key_exists($da['key'], $diy_title_data)) {
                                    $dflag = 0;
                                }
                            }
                            if ($dflag == 1) {
                                $diy_title_data[$da['key']] = $da['name'];
                            }
                            $og['goods_' . $da['key']] = $da['value'];
                            $diyformdata .= $da['name'] . ": " . $da['value'] . " \r\n";
                        }
                        $og['goods_diyformdata'] = $diyformdata;
                    }
                }
                unset($og);
                //分销商
                if (!empty($level) && empty($agentid)) {
                    $value['commission1'] = $commission1;
                    $value['commission2'] = $commission2;
                    $value['commission3'] = $commission3;
                }
                //虚店店员
                if (!empty($clerklevel) && empty($clerk_id)) {
                    $value['clerkCommission1'] = $clerkCommission1;
                    $value['clerkCommission2'] = $clerkCommission2;
                    $value['clerkCommission3'] = $clerkCommission3;
                }
                //医生
                if (!empty($doctor_level) && empty($doctor_id)) {
                    $value['doctorCommission1'] = $doctorCommission1;
                    $value['doctorCommission2'] = $doctorCommission2;
                    $value['doctorCommission3'] = $doctorCommission3;
                }
                $value['goods'] = set_medias($order_goods, 'thumb');
                $value['goods_str'] = $goods;
                $value['goodscount'] = count($order_goods);
            }
        }
        unset($value);
        if ($condition != ' o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0' || !empty($sqlcondition)) {
            $t = pdo_fetch(
                'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid  and m.uniacid =  o.uniacid"
                . " left join " . tablename('elapp_shop_member_address') . " a on o.addressid = a.id "
                . " left join " . tablename('elapp_shop_saler') . " s on s.openid = o.verifyopenid and s.uniacid=o.uniacid"
                . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                . " $sqlcondition WHERE $condition ", $paras);
        } else {
            $t = pdo_fetch(
                'SELECT COUNT(*) as count, ifnull(sum(price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " WHERE uniacid = :uniacid and ismr=0 and deleted=0 and isparent=0 {$status_condition}", $paras);
        }
        $total = $t['count'];
        $totalmoney = $t['sumprice'];
        $pager = pagination2($total, $pindex, $psize);
        $stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_store') . ' where uniacid=:uniacid ', array(':uniacid' => $uniacid));
        $r_type = array('0' => '退款', '1' => '退货退款', '2' => '换货');
        show_json(1, array('list' => $list, 'total' => $total, 'pagesize' => $psize));
    }


    public function orderDataVerify()
    {

        global $_W, $_GPC;
        $uniacid = $_W['uniacid'];
        $openid = $_W['openid'];
        $pindex = max(1, intval($_GPC['page']));
        $psize = 20;

        //当前核销员
        $saler = pdo_fetch('select id,openid from ' . tablename('elapp_shop_saler') . ' where status=1  and openid=:openid and uniacid=:uniacid limit 1', array(
            ':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']
        ));
        $condition = " log.salerid = :salerid and log.uniacid = :uniacid ";
        $paras = $paras1 = array(':uniacid' => $uniacid, ':salerid' => $saler['id']);

        //订单号，核销员，核销门店，核销码，会员姓名，联系人，快递单号，商品名称，商品编码
        if (!empty($_GPC['searchfield']) && !empty($_GPC['keyword'])) {
            $searchfield = trim(strtolower($_GPC['searchfield']));
            $_GPC['keyword'] = trim($_GPC['keyword']);
            $paras[':keyword'] = htmlspecialchars_decode($_GPC['keyword'], ENT_QUOTES);
            $sqlcondition = '';
            if ($searchfield == 'ordersn') {
                $condition .= " AND locate(:keyword,o.ordersn)>0";
            } else if ($searchfield == 'member') {
                $condition .= " AND (locate(:keyword,m.realname)>0 or locate(:keyword,m.mobile)>0 or locate(:keyword,m.nickname)>0 or  locate(:keyword,o.carrier>0) )";
            } else if ($searchfield == 'store') {
                $condition .= " AND (locate(:keyword,store.storename)>0)";
                $sqlcondition = " left join " . tablename('elapp_shop_store') . " store on store.id = o.verifystoreid and store.uniacid=o.uniacid";
            } else if ($searchfield == 'goodstitle') {
                $sqlcondition = " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (locate(:keyword,g.title)>0)) gs on gs.orderid=o.id";
            } else if ($searchfield == 'goodssn') {
                $sqlcondition = " inner join ( select DISTINCT(og.orderid) from " . tablename('elapp_shop_order_goods') . " og left join " . tablename('elapp_shop_goods') . " g on g.id=og.goodsid where og.uniacid = '$uniacid' and (((locate(:keyword,g.goodssn)>0)) or (locate(:keyword,og.goodssn)>0))) gs on gs.orderid=o.id";
            }
        }

        if ($condition != ' log.salerid = :salerid and log.uniacid = :uniacid ' || !empty($sqlcondition)) {
            $sql = "select o.* ,1 as isverifygoods_order,g.id as verifygoodsid from " . tablename('elapp_shop_verifygoods_log') . " log "
                . " left join  " . tablename('elapp_shop_verifygoods') . " g on log.verifygoodsid = g.id "
                . " left join  " . tablename('elapp_shop_order') . " o on g.orderid = o.id  "
                . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid and m.uniacid =  o.uniacid "
                . " $sqlcondition where $condition GROUP BY o.id ORDER BY log.verifydate DESC  ";

            if (empty($_GPC['export'])) {
                $sql .= "LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            }
            $list = pdo_fetchall($sql, $paras);
        } else {
            $sql = "select o.* ,1 as isverifygoods_order,g.id as verifygoodsid from " . tablename('elapp_shop_verifygoods_log') . " log "
                . " left join  " . tablename('elapp_shop_verifygoods') . " g on log.verifygoodsid = g.id "
                . " left join  " . tablename('elapp_shop_order') . " o on g.orderid = o.id where log.uniacid = :uniacid and log.salerid=:salerid GROUP BY o.id ORDER BY log.verifydate DESC  ";
            if (empty($_GPC['export'])) {
                $sql .= "LIMIT " . ($pindex - 1) * $psize . ',' . $psize;
            }
            $list = pdo_fetchall($sql, $paras);
            if (!empty($list)) {
                $refundid = '';
                $openid = '';
                $addressid = '';
                $dispatchid = '';
                $verifyopenid = '';
                foreach ($list as $key => $value) {
                    $refundid .= ",'{$value['refundid']}'";
                    $openid .= ",'{$value['openid']}'";
                    $addressid .= ",'{$value['addressid']}'";
                    $dispatchid .= ",'{$value['dispatchid']}'";
                    $verifyopenid .= ",'{$value['verifyopenid']}'";
                }
                $refundid = ltrim($refundid, ',');
                $openid = ltrim($openid, ',');
                $addressid = ltrim($addressid, ',');
                $dispatchid = ltrim($dispatchid, ',');
                $verifyopenid = ltrim($verifyopenid, ',');
                $refundid_array = pdo_fetchall("SELECT id,rtype,status as rstatus FROM " . tablename('elapp_shop_order_refund') . " WHERE id IN ({$refundid})", array(), 'id');
                $openid_array = pdo_fetchall("SELECT openid,nickname,id as mid,realname as mrealname,mobile as mmobile FROM " . tablename('elapp_shop_member') . " WHERE openid IN ({$openid}) AND uniacid={$_W['uniacid']}", array(), 'openid');
                $addressid_array = pdo_fetchall("SELECT id,realname as arealname,mobile as amobile,province as aprovince ,city as acity , area as aarea,address as aaddress FROM " . tablename('elapp_shop_member_address') . " WHERE id IN ({$addressid})", array(), 'id');
                $dispatchid_array = pdo_fetchall("SELECT id,dispatchname FROM " . tablename('elapp_shop_dispatch') . " WHERE id IN ({$dispatchid})", array(), 'id');
                $verifyopenid_array = pdo_fetchall("SELECT sm.id as salerid,sm.nickname as salernickname,sm.openid,s.salername FROM " . tablename('elapp_shop_saler') . " s LEFT JOIN " . tablename('elapp_shop_member') . " sm ON sm.openid = s.openid and sm.uniacid=s.uniacid WHERE s.openid IN ({$verifyopenid})", array(), 'openid');
                foreach ($list as $key => &$value) {
                    $list[$key]['rtype'] = $refundid_array[$value['refundid']]['rtype'];
                    $list[$key]['rstatus'] = $refundid_array[$value['refundid']]['rstatus'];
                    $list[$key]['nickname'] = $openid_array[$value['openid']]['nickname'];
                    $list[$key]['mid'] = $openid_array[$value['openid']]['mid'];
                    $list[$key]['mrealname'] = $openid_array[$value['openid']]['mrealname'];
                    $list[$key]['mmobile'] = $openid_array[$value['openid']]['mmobile'];
                    $list[$key]['arealname'] = $addressid_array[$value['addressid']]['arealname'];
                    $list[$key]['amobile'] = $addressid_array[$value['addressid']]['amobile'];
                    $list[$key]['aprovince'] = $addressid_array[$value['addressid']]['aprovince'];
                    $list[$key]['acity'] = $addressid_array[$value['addressid']]['acity'];
                    $list[$key]['aarea'] = $addressid_array[$value['addressid']]['aarea'];
                    $list[$key]['astreet'] = $addressid_array[$value['addressid']]['astreet'];
                    $list[$key]['aaddress'] = $addressid_array[$value['addressid']]['aaddress'];
                    $list[$key]['dispatchname'] = $dispatchid_array[$value['dispatchid']]['dispatchname'];
                    $list[$key]['salerid'] = $verifyopenid_array[$value['verifyopenid']]['salerid'];
                    $list[$key]['salernickname'] = $verifyopenid_array[$value['verifyopenid']]['salernickname'];
                    $list[$key]['salername'] = $verifyopenid_array[$value['verifyopenid']]['salername'];
                }
                unset($value);
            }
        }
        $paytype = array(
            '0' => array('css' => 'default', 'name' => '未支付'),
            '1' => array('css' => 'danger', 'name' => '余额支付'),
            '11' => array('css' => 'default', 'name' => '后台付款'),
            '2' => array('css' => 'danger', 'name' => '在线支付'),
            '21' => array('css' => 'success', 'name' => '微信支付'),
            '22' => array('css' => 'warning', 'name' => '支付宝支付'),
            '23' => array('css' => 'warning', 'name' => '银联支付'),
            '3' => array('css' => 'primary', 'name' => '货到付款'),
        );
        $orderstatus = array(
            '-1' => array('css' => 'default', 'name' => '已关闭'),
            '0' => array('css' => 'danger', 'name' => '待付款'),
            '1' => array('css' => 'info', 'name' => '待发货'),
            '2' => array('css' => 'warning', 'name' => '待收货'),
            '3' => array('css' => 'success', 'name' => '已完成')
        );
        $is_merch = array();
        if (!empty($list)) {
            $diy_title_data = array();
            $diy_data = array();
            foreach ($list as &$value) {
                //核销次数统计
                $verifygoodlogs = pdo_fetchall('select *  from ' . tablename('elapp_shop_verifygoods_log') . '    where verifygoodsid =:id  ', array(':id' => $value['verifygoodsid']));
                $verifynum = 0;
                foreach ($verifygoodlogs as $verifygoodlog) {
                    $verifynum += intval($verifygoodlog['verifynum']);
                }
                $verifygood = pdo_fetch('select *  from ' . tablename('elapp_shop_verifygoods') . '    where id =:id  ', array(':id' => $value['verifygoodsid']));
                $value['verifygoods_times'] = intval($verifygood['limitnum']) - $verifynum;
                $value['verifygoods_times_total'] = intval($verifygood['limitnum']);
                //判断时间是否过期
                if (empty($verifygood['limittype'])) {
                    $limitdate = date('Y-m-d H:i:s', intval($verifygood['starttime']) + intval($verifygood['limitdays']) * 86400);

                } else {
                    $limitdate = date('Y-m-d H:i:s', $verifygood['limitdate']);
                }
                $value['verifygoods_endtime'] = $limitdate;
                $value['createtime'] = date("Y/m/d H:i:s", $value['createtime']);
                $value['verifytime'] = date("Y/m/d H:i:s", $value['verifytime']);
                $s = $value['status'];
                $pt = $value['paytype'];
                $value['statusvalue'] = $s;
                $value['statuscss'] = $orderstatus[$value['status']]['css'];
                $value['status'] = $orderstatus[$value['status']]['name'];
                if ($pt == 3 && empty($value['statusvalue'])) {
                    $value['statuscss'] = $orderstatus[1]['css'];
                    $value['status'] = $orderstatus[1]['name'];
                }
                if ($s == 1) {
                    if ($value['isverify'] == 1) {
                        $value['status'] = "待使用";
                        if ($value['sendtype'] > 0) {
                            $value['status'] = "部分使用";
                        }
                    } else if (empty($value['addressid'])) {
                        $value['status'] = "待取货";
                    } else {
                        if ($value['sendtype'] > 0) {
                            $value['status'] = "部分发货";
                        }
                    }
                }
                if ($s == -1) {
                    if (!empty($value['refundtime'])) {
                        $value['status'] = '已退款';
                    }
                }

                $value['paytypevalue'] = $pt;
                $value['css'] = $paytype[$pt]['css'];
                $value['paytype'] = $paytype[$pt]['name'];
                $value['dispatchname'] = empty($value['addressid']) ? '自提' : $value['dispatchname'];
                if (empty($value['dispatchname'])) {
                    $value['dispatchname'] = '快递';
                }
                if ($pt == 3) {
                    $value['dispatchname'] = "货到付款";
                } else if ($value['isverify'] == 1) {
                    $value['dispatchname'] = "线下核销";
                } else if ($value['isvirtual'] == 1) {
                    $value['dispatchname'] = "虚拟物品";
                } else if (!empty($value['virtual'])) {
                    $value['dispatchname'] = "虚拟物品(卡密)<br/>自动发货";
                }

                if ($value['dispatchtype'] == 1 || !empty($value['isverify']) || !empty($value['virtual']) || !empty($value['isvirtual'])) {
                    $value['address'] = '';
                    $carrier = iunserializer($value['carrier']);
                    if (is_array($carrier)) {
                        $value['addressdata']['realname'] = $value['realname'] = $carrier['carrier_realname'];
                        $value['addressdata']['mobile'] = $value['mobile'] = $carrier['carrier_mobile'];
                    }
                } else {
                    $address = iunserializer($value['address']);
                    $isarray = is_array($address);
                    $value['realname'] = $isarray ? $address['realname'] : $value['arealname'];
                    $value['mobile'] = $isarray ? $address['mobile'] : $value['amobile'];
                    $value['province'] = $isarray ? $address['province'] : $value['aprovince'];
                    $value['city'] = $isarray ? $address['city'] : $value['acity'];
                    $value['area'] = $isarray ? $address['area'] : $value['aarea'];
                    $value['street'] = $isarray ? $address['street'] : $value['astreet'];
                    $value['address'] = $isarray ? $address['address'] : $value['aaddress'];
                    $value['address_province'] = $value['province'];
                    $value['address_city'] = $value['city'];
                    $value['address_area'] = $value['area'];
                    $value['address_street'] = $value['street'];
                    $value['address_address'] = $value['address'];
                    $value['address'] = $value['province'] . " " . $value['city'] . " " . $value['area'] . " " . $value['address'];
                    $value['addressdata'] = array(
                        'realname' => $value['realname'],
                        'mobile' => $value['mobile'],
                        'address' => $value['address'],
                    );
                }
                //分销商
                $commission1 = -1;
                $commission2 = -1;
                $commission3 = -1;
                $m1 = false;
                $m2 = false;
                $m3 = false;
                if (!empty($level) && empty($agentid)) {
                    if (!empty($value['agentid'])) {
                        $m1 = m('member')->getMember($value['agentid']);
                        $commission1 = 0;
                        if (!empty($m1['agentid']) && $level > 1) {
                            $m2 = m('member')->getMember($m1['agentid']);
                            $commission2 = 0;
                            if (!empty($m2['agentid']) && $level > 2) {
                                $m3 = m('member')->getMember($m2['agentid']);
                                $commission3 = 0;
                            }
                        }
                    }
                }

                if (!empty($agentid)) {
                    $magent = m('member')->getMember($agentid);
                }
                //虚店店员
                $clerkCommission1 = -1;
                $clerkCommission2 = -1;
                $clerkCommission3 = -1;
                $clerkm1 = false;
                $clerkm2 = false;
                $clerkm3 = false;
                if (!empty($clerklevel) && empty($clerk_id)) {
                    if (!empty($value['clerk_id'])) {
                        $clerkm1 = m('member')->getMember($value['clerk_id']);
                        $clerkCommission1 = 0;
                        if (!empty($clerkm1['clerk_id']) && $clerklevel > 1) {
                            $clerkm2 = m('member')->getMember($clerkm1['clerk_id']);
                            $clerkCommission2 = 0;
                            if (!empty($clerkm2['clerk_id']) && $clerklevel > 2) {
                                $clerkm3 = m('member')->getMember($clerkm2['clerk_id']);
                                $clerkCommission3 = 0;
                            }
                        }
                    }
                }

                if (!empty($clerk_id)) {
                    $mclerk = m('member')->getMember($clerk_id);
                }
                //医生
                $doctorCommission1 = -1;
                $doctorCommission2 = -1;
                $doctorCommission3 = -1;
                $doctorm1 = false;
                $doctorm2 = false;
                $doctorm3 = false;
                if (!empty($doctor_level) && empty($doctor_id)) {

                    if (!empty($value['doctor_id'])) {
                        $doctorm1 = m('member')->getMember($value['doctor_id']);
                        $doctorCommission1 = 0;
                        if (!empty($doctorm1['doctor_id']) && $doctor_level > 1) {
                            $doctorm2 = m('member')->getMember($doctorm1['doctor_id']);
                            $doctorCommission2 = 0;
                            if (!empty($doctorm2['doctor_id']) && $doctor_level > 2) {
                                $doctorm3 = m('member')->getMember($doctorm2['doctor_id']);
                                $doctorCommission3 = 0;
                            }
                        }
                    }
                }
                if (!empty($doctor_id)) {
                    $mdoctor = m('member')->getMember($doctor_id);
                }
                //订单商品
                $order_goods = pdo_fetchall('select g.id,g.title,g.thumb,g.goodssn,g.type,og.goodssn as option_goodssn, g.productsn,og.productsn as option_productsn, og.total,
                    og.price,og.optionname as optiontitle, og.realprice,og.changeprice,og.oldprice,og.commission1,og.commission2,og.commission3,og.commissions,og.diyformdata,
                    og.diyformfields,op.specs,g.merchid,og.seckill,og.seckill_taskid,og.seckill_roomid,g.ispresell from ' . tablename('elapp_shop_order_goods') . ' og '
                    . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                    . ' left join ' . tablename('elapp_shop_goods_option') . ' op on og.optionid = op.id '
                    . ' where og.orderid=:orderid and og.uniacid=:uniacid and g.type=5 ', array(':orderid' => $value['id'], ':uniacid' => $uniacid));
                $goods = '';
                foreach ($order_goods as &$og) {
                    $og['seckill_task'] = false;
                    $og['seckill_room'] = false;
                    if ($og['seckill']) {
                        $og['seckill_task'] = plugin_run('seckill::getTaskInfo', $og['seckill_taskid']);
                        $og['seckill_room'] = plugin_run('seckill::getRoomInfo', $og['seckill_taskid'], $og['seckill_roomid']);
                    }
                    //读取规格的图片
                    if (!empty($og['specs'])) {
                        $thumb = m('goods')->getSpecThumb($og['specs']);
                        if (!empty($thumb)) {
                            $og['thumb'] = $thumb;
                        }
                    }
                    $goods .= "" . $og['title'] . "\r\n";

                    if (!empty($og['optiontitle'])) {
                        $goods .= " 规格: " . $og['optiontitle'];
                    }
                    if (!empty($og['option_goodssn'])) {
                        $og['goodssn'] = $og['option_goodssn'];
                    }
                    if (!empty($og['option_productsn'])) {
                        $og['productsn'] = $og['option_productsn'];
                    }

                    if (!empty($og['goodssn'])) {
                        $goods .= ' 商品编号: ' . $og['goodssn'];
                    }
                    if (!empty($og['productsn'])) {
                        $goods .= ' 商品条码: ' . $og['productsn'];
                    }
                    $goods .= ' 单价: ' . ($og['price'] / $og['total']) . ' 折扣后: ' . ($og['realprice'] / $og['total']) . ' 数量: ' . $og['total'] . ' 总价: ' . $og['price'] . " 折扣后: " . $og['realprice'] . "\r\n ";

                    if (p('diyform') && !empty($og['diyformfields']) && !empty($og['diyformdata'])) {
                        $diyformdata_array = p('diyform')->getDatas(iunserializer($og['diyformfields']), iunserializer($og['diyformdata']), 1);
                        $diyformdata = "";

                        $dflag = 1;
                        foreach ($diyformdata_array as $da) {
                            if (!empty($diy_title_data)) {
                                if (array_key_exists($da['key'], $diy_title_data)) {
                                    $dflag = 0;
                                }
                            }
                            if ($dflag == 1) {
                                $diy_title_data[$da['key']] = $da['name'];
                            }
                            $og['goods_' . $da['key']] = $da['value'];
                            $diyformdata .= $da['name'] . ": " . $da['value'] . " \r\n";
                        }
                        $og['goods_diyformdata'] = $diyformdata;
                    }
                }
                unset($og);
                //分销商
                if (!empty($level) && empty($agentid)) {
                    $value['commission1'] = $commission1;
                    $value['commission2'] = $commission2;
                    $value['commission3'] = $commission3;
                }
                //虚店店员
                if (!empty($clerklevel) && empty($clerk_id)) {
                    $value['clerkCommission1'] = $clerkCommission1;
                    $value['clerkCommission2'] = $clerkCommission2;
                    $value['clerkCommission3'] = $clerkCommission3;
                }
                //医生
                if (!empty($doctor_level) && empty($doctor_id)) {
                    $value['doctorCommission1'] = $doctorCommission1;
                    $value['doctorCommission2'] = $doctorCommission2;
                    $value['doctorCommission3'] = $doctorCommission3;
                }
                $value['goods'] = set_medias($order_goods, 'thumb');
                $value['goods_str'] = $goods;
                $value['goodscount'] = count($order_goods);
            }
        }
        unset($value);
        if ($condition != ' o.uniacid = :uniacid and o.ismr=0 and o.deleted=0 and o.isparent=0' || !empty($sqlcondition)) {
            //重新赋值，因为此处没有log表，sql会报错
            $condition = ' s.id = :salerid and s.uniacid = :uniacid';
            $t = pdo_fetch(
                'SELECT COUNT(DISTINCT(o.id)) as count, ifnull(sum(o.price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " o "
                . " left join " . tablename('elapp_shop_order_refund') . " r on r.id =o.refundid "
                . " left join " . tablename('elapp_shop_member') . " m on m.openid=o.openid  and m.uniacid =  o.uniacid"
                . " left join " . tablename('elapp_shop_member_address') . " a on o.addressid = a.id "
                . " left join " . tablename('elapp_shop_saler') . " s on s.openid = o.verifyopenid and s.uniacid=o.uniacid"
                . " left join " . tablename('elapp_shop_member') . " sm on sm.openid = s.openid and sm.uniacid=s.uniacid"
                . " $sqlcondition WHERE $condition ", $paras);
        } else {
            $t = pdo_fetch(
                'SELECT COUNT(*) as count, ifnull(sum(price),0) as sumprice   FROM ' . tablename('elapp_shop_order') . " WHERE uniacid = :uniacid and ismr=0 and deleted=0 and isparent=0", $paras);
        }
        $total = $t['count'];
        $totalmoney = $t['sumprice'];
        $pager = pagination2($total, $pindex, $psize);
        $stores = pdo_fetchall('select id,storename from ' . tablename('elapp_shop_store') . ' where uniacid=:uniacid ', array(':uniacid' => $uniacid));
        $r_type = array('0' => '退款', '1' => '退货退款', '2' => '换货');
        show_json(1, array('list' => $list, 'total' => $total, 'pagesize' => $psize));
    }


    function log()
    {
        global $_W, $_GPC;
        $sysset = m('common')->getSysset('trade');
        if ($sysset['verify_type'] == 0) {
            $order0 = $this->selectOrderPrice(0);
            $order7 = $this->selectOrderPrice(7);
            $order30 = $this->selectOrderPrice(30);
        } else {
            $order0 = $this->selectOrderPriceOld(0);
            $order7 = $this->selectOrderPriceOld(7);
            $order30 = $this->selectOrderPriceOld(30);
        }
        include $this->template('verify/verifyorder/log');
    }


    /**
     * 查询订单金额
     * @param int $day 查询天数
     * @return bool
     */

    /**
     * 查询订单金额
     * @param int $day 查询天数
     * @return bool
     */
    protected function selectOrderPrice($day = 0)
    {
        global $_W;
        $day = (int)$day;
        if ($day != 0) {
            $createtime1 = strtotime(date('Y-m-d', strtotime('-' . $day . ' days')));
            $createtime2 = strtotime(date('Y-m-d 23:59:59'));
        } else {
            $createtime1 = strtotime(date('Y-m-d'));
            $createtime2 = strtotime(date('Y-m-d  23:59:59'));
        }
        $param = array(
            ':uniacid' => $_W['uniacid'],
            ':createtime1' => $createtime1,
            ':createtime2' => $createtime2,
            ':openid' => $_W['openid'],
        );
        // 是否存在多商户表
        if ($this->merchPluginExists()) {

            $sql = "SELECT count(o.id) FROM" . tablename("elapp_shop_order") . " o"
                . " LEFT JOIN " . tablename("elapp_shop_saler") . " s ON o.verifyopenid=s.openid"
                . " LEFT JOIN " . tablename("elapp_shop_merch_saler") . " ms ON o.verifyopenid=ms.openid"
                . "   WHERE (s.openid=:openid or ms.openid = :openid) and o.createtime between :createtime1 and :createtime2 AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   limit 1";
        } else {
            //echo 2;
            $sql = "SELECT count(o.id) FROM" . tablename("elapp_shop_order") . " o"
                . " LEFT JOIN " . tablename("elapp_shop_saler") . " s ON o.verifyopenid=s.openid"
                . "   WHERE o.verifyopenid = :openid and o.createtime between :createtime1 and :createtime2 AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   limit 1";
        }
        // 判断 merch_saler表是否存在
        $count = pdo_fetchcolumn($sql, $param);

        //记次/时
        //当前核销员
        $saler = pdo_fetch('select id,openid from ' . tablename('elapp_shop_saler') . ' where status=1  and openid=:openid and uniacid=:uniacid limit 1', array(
            ':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']
        ));
        $sql = "SELECT sum(verifynum) FROM" . tablename("elapp_shop_verifygoods_log") . "    WHERE 
          salerid=:salerid and verifydate between :createtime1 and :createtime2 and uniacid=:uniacid limit 1";

        $param = array(
            ':uniacid' => $_W['uniacid'],
            ':createtime1' => $createtime1,
            ':createtime2' => $createtime2,
            ':salerid' => $saler['id'],
        );

        $count1 = pdo_fetchcolumn($sql, $param);
        $result = array(
            'count' => number_format($count, 0),
            'count1' => number_format($count1, 0)
        );

        return $result;
    }


    /**
     * 查询订单金额
     * @param int $day 查询天数
     * @return bool
     */
    protected function selectOrderPriceOld($day = 0)
    {
        global $_W;
        $day = (int)$day;
        if ($day != 0) {
            $createtime1 = strtotime(date('Y-m-d', strtotime('-' . $day . ' days')));
            $createtime2 = strtotime(date('Y-m-d 23:59:59'));
        } else {
            $createtime1 = strtotime(date('Y-m-d'));
            $createtime2 = strtotime(date('Y-m-d  23:59:59'));
        }

        $param = array(
            ':uniacid' => $_W['uniacid'],
            ':createtime1' => $createtime1,
            ':createtime2' => $createtime2,
            ':openid' => $_W['openid'],
        );
        // 是否存在多商户表
        //        if( $this->merchPluginExists() ) {
        //
        //            $sql= "SELECT count(o.id) FROM". tablename("elapp_shop_order")." o"
        //                ." LEFT JOIN ".tablename("elapp_shop_saler")." s ON o.verifyopenid=s.openid"
        //                ." LEFT JOIN ".tablename("elapp_shop_merch_saler")." ms ON o.verifyopenid=ms.openid"
        //                ."   WHERE (s.openid=:openid or ms.openid = :openid) and o.createtime between :createtime1 and :createtime2 AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   limit 1";
        //        } else {
        //            //echo 2;
        //            $sql= "SELECT count(o.id) FROM". tablename("elapp_shop_order")." o"
        //                ." LEFT JOIN ".tablename("elapp_shop_saler")." s ON o.verifyopenid=s.openid"
        //                ."   WHERE o.verifyopenid = :openid and o.createtime between :createtime1 and :createtime2 AND o.ismr=0  AND   o.status =3  AND o.deleted=0 and o.uniacid=:uniacid   limit 1";
        //        }
        //        $saler = pdo_fetch('select id,openid from ' . tablename('elapp_shop_saler') . ' where status=1  and openid=:openid and uniacid=:uniacid limit 1', array(
        //            ':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']
        //        ));
        //        $merch_saler =  pdo_fetchall('select id,openid from ' . tablename('elapp_shop_merch_saler') . ' where status=1  and openid=:openid and uniacid=:uniacid limit 1', array(
        //            ':openid' => $_W['openid'], ':uniacid' => $_W['uniacid']
        //        ));

        //        $param['salerid'] = $saler['id'];
        $sql = "select count(*) from " . tablename('elapp_shop_saler_verify_log') . " where uniacid = :uniacid and openid = :openid  and  verify_time > :createtime1 and verify_time < :createtime2 and type = 0";
        // 判断 merch_saler表是否存在
        $count = pdo_fetchcolumn($sql, $param);
        //记次/时
        //当前核销员
        //$sql= "SELECT sum(verifynum) FROM". tablename("elapp_shop_verifygoods_log")."    WHERE
        //salerid=:salerid and verifydate between :createtime1 and :createtime2 and uniacid=:uniacid limit 1";
        $sql = "select count(*) from " . tablename('elapp_shop_saler_verify_log') . " where uniacid = :uniacid and openid = :openid  and  verify_time > :createtime1 and verify_time < :createtime2 and type = 1";

        $param = array(
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':createtime1' => $createtime1,
            ':createtime2' => $createtime2,
            //':salerid'=>$saler['id'],
        );

        $count1 = pdo_fetchcolumn($sql, $param);
        $result = array(
            'count' => number_format($count, 0),
            'count1' => number_format($count1, 0)
        );
        return $result;
    }

    public function detail()
    {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $p = p('commission');
        $p_clerk = p('clerk');
        $p_doctor = p('doctor');
        $item = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_order') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $id, ':uniacid' => $_W['uniacid']));
        $item['statusvalue'] = $item['status'];
        $item['paytypevalue'] = $item['paytype'];
        //部分发货时间
        $order_goods = array();
        if ($item['sendtype'] > 0) {
            $order_goods = pdo_fetchall("SELECT orderid,goodsid,sendtype,expresssn,expresscom,express,sendtime FROM " . tablename('elapp_shop_order_goods') . "
            WHERE orderid = " . $id . " and sendtime > 0 and uniacid=" . $_W['uniacid'] . " and sendtype > 0 group by sendtype order by sendtime desc ");
            foreach ($order_goods as $key => $value) {
                $order_goods[$key]['goods'] = pdo_fetchall('select g.id,g.title,g.thumb,og.sendtype,g.ispresell from ' . tablename('elapp_shop_order_goods') . ' og '
                    . ' left join ' . tablename('elapp_shop_goods') . ' g on g.id=og.goodsid '
                    . ' where og.uniacid=:uniacid and og.orderid=:orderid and og.sendtype=' . $value['sendtype'] . ' ', array(':uniacid' => $_W['uniacid'], ':orderid' => $id));
            }
            $item['sendtime'] = $order_goods[0]['sendtime'];
        }

        $shopset = m('common')->getSysset('shop');
        if (empty($item)) {

            $this->message("抱歉，订单不存在!", referer(), "error");
        }

        $member = m('member')->getMember($item['openid']);
        $dispatch = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_dispatch') . " WHERE id = :id and uniacid=:uniacid and merchid=0", array(':id' => $item['dispatchid'], ':uniacid' => $_W['uniacid']));
        if (empty($item['addressid'])) {
            $user = unserialize($item['carrier']);
        } else {
            $user = iunserializer($item['address']);
            if (!is_array($user)) {
                $user = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_member_address') . " WHERE id = :id and uniacid=:uniacid", array(':id' => $item['addressid'], ':uniacid' => $_W['uniacid']));
            }
            $address_info = $user['address'];
            $user['address'] = $user['province'] . ' ' . $user['city'] . ' ' . $user['area'] . ' ' . $user['street'] . ' ' . $user['address'];
            $item['addressdata'] = array(
                'realname' => $user['realname'],
                'mobile' => $user['mobile'],
                'address' => $user['address'],
            );
        }
        $refund = pdo_fetch("SELECT * FROM " . tablename('elapp_shop_order_refund') . " WHERE orderid = :orderid and uniacid=:uniacid order by id desc", array(':orderid' => $item['id'], ':uniacid' => $_W['uniacid']));
        $diyformfields = "";
        if (p('diyform')) {
            $diyformfields = ",o.diyformfields,o.diyformdata";
        }
        $goods = pdo_fetchall("SELECT g.*, o.goodssn as option_goodssn, o.productsn as option_productsn,o.total,g.type,o.optionname,o.optionid,o.price as orderprice,o.realprice,o.changeprice,o.oldprice,o.commission1,o.commission2,o.commission3,o.commissions,o.seckill,o.seckill_taskid,o.seckill_roomid{$diyformfields} FROM " . tablename('elapp_shop_order_goods') .
            " o left join " . tablename('elapp_shop_goods') . " g on o.goodsid=g.id "
            . " WHERE o.orderid=:orderid and o.uniacid=:uniacid", array(':orderid' => $id, ':uniacid' => $_W['uniacid']));
        $is_merch = false;
        foreach ($goods as &$r) {
            $r['seckill_task'] = false;
            if ($r['seckill']) {
                $r['seckill_task'] = plugin_run('seckill::getTaskInfo', $r['seckill_taskid']);
                $r['seckill_room'] = plugin_run('seckill::getRoomInfo', $r['seckill_taskid'], $r['seckill_roomid']);
            }
            if (!empty($r['option_goodssn'])) {
                $r['goodssn'] = $r['option_goodssn'];
            }
            if (!empty($r['option_productsn'])) {
                $r['productsn'] = $r['option_productsn'];
            }
            $r['marketprice'] = $r['orderprice'] / $r['total'];
            if (p('diyform')) {
                $r['diyformfields'] = iunserializer($r['diyformfields']);
                $r['diyformdata'] = iunserializer($r['diyformdata']);
            }
            if (!empty($r['merchid'])) {
                $is_merch = true;
            }
        }
        unset($r);
        $item['goods'] = $goods;
        //分销商
        $agents = array();
        if ($p) {
            $agents = $p->getAgents($id);
            $m1 = isset($agents[0]) ? $agents[0] : false;
            $m2 = isset($agents[1]) ? $agents[1] : false;
            $m3 = isset($agents[2]) ? $agents[2] : false;
            $commission1 = 0;
            $commission2 = 0;
            $commission3 = 0;
            foreach ($goods as &$og) {
                $oc1 = 0;
                $oc2 = 0;
                $oc3 = 0;
                $commissions = iunserializer($og['commissions']);
                if (!empty($m1)) {
                    if (is_array($commissions)) {
                        $oc1 = isset($commissions['level1']) ? floatval($commissions['level1']) : 0;
                    } else {
                        $c1 = iunserializer($og['commission1']);
                        $l1 = $p->getLevel($m1['openid']);
                        $oc1 = isset($c1['level' . $l1['id']]) ? $c1['level' . $l1['id']] : $c1['default'];
                    }
                    $og['oc1'] = $oc1;
                    $commission1 += $oc1;
                }
                if (!empty($m2)) {
                    if (is_array($commissions)) {
                        $oc2 = isset($commissions['level2']) ? floatval($commissions['level2']) : 0;
                    } else {
                        $c2 = iunserializer($og['commission2']);
                        $l2 = $p->getLevel($m2['openid']);
                        $oc2 = isset($c2['level' . $l2['id']]) ? $c2['level' . $l2['id']] : $c2['default'];
                    }
                    $og['oc2'] = $oc2;
                    $commission2 += $oc2;
                }
                if (!empty($m3)) {
                    if (is_array($commissions)) {
                        $oc3 = isset($commissions['level3']) ? floatval($commissions['level3']) : 0;
                    } else {
                        $c3 = iunserializer($og['commission3']);
                        $l3 = $p->getLevel($m3['openid']);
                        $oc3 = isset($c3['level' . $l3['id']]) ? $c3['level' . $l3['id']] : $c3['default'];
                    }
                    $og['oc3'] = $oc3;
                    $commission3 += $oc3;
                }
            }
            unset($og);
            $commission_array = array(0 => $commission1, 1 => $commission2, 2 => $commission3);
            foreach ($agents as $key => $value) {
                $agents[$key]['commission'] = $commission_array[$key];
                if ($key > 2) {
                    unset($agents[$key]);
                }
            }
        }

        //虚店店员
        $clerks = array();
        if ($p_clerk) {
            $clerks = $p_clerk->getClerks($id);

            $clerk_m1 = isset($clerks[0]) ? $clerks[0] : false;
            $clerk_m2 = isset($clerks[1]) ? $clerks[1] : false;
            $clerk_m3 = isset($clerks[2]) ? $clerks[2] : false;
            $clerkCommission1 = 0;
            $clerkCommission2 = 0;
            $clerkCommission3 = 0;
            foreach ($goods as &$og) {
                $clerk_oc1 = 0;
                $clerk_oc2 = 0;
                $clerk_oc3 = 0;
                $clerkCommissions = iunserializer($og['clerkCommissions']);
                if (!empty($clerk_m1)) {
                    if (is_array($clerkCommissions)) {
                        $clerk_oc1 = isset($clerkCommissions['level1']) ? floatval($clerkCommissions['level1']) : 0;
                    } else {
                        $clerk_c1 = iunserializer($og['clerkCommission1']);
                        $clerk_l1 = $p_clerk->getLevel($clerk_m1['openid']);
                        $clerk_oc1 = isset($clerk_c1['level' . $clerk_l1['id']]) ? $clerk_c1['level' . $clerk_l1['id']] : $clerk_c1['default'];
                    }
                    $og['clerk_oc1'] = $clerk_oc1;
                    $clerkCommission1 += $clerk_oc1;
                }
                if (!empty($clerk_m2)) {
                    if (is_array($clerkCommissions)) {
                        $clerk_oc2 = isset($clerkCommissions['level2']) ? floatval($clerkCommissions['level2']) : 0;
                    } else {
                        $clerk_c2 = iunserializer($og['clerkCommission2']);
                        $clerk_l2 = $p_clerk->getLevel($clerk_m2['openid']);
                        $clerk_oc2 = isset($clerk_c2['level' . $clerk_l2['id']]) ? $clerk_c2['level' . $clerk_l2['id']] : $clerk_c2['default'];
                    }
                    $og['clerk_oc2'] = $clerk_oc2;
                    $clerkCommission2 += $clerk_oc2;
                }
                if (!empty($clerk_m3)) {
                    if (is_array($clerkCommissions)) {
                        $clerk_oc3 = isset($clerkCommissions['level3']) ? floatval($clerkCommissions['level3']) : 0;
                    } else {
                        $clerk_c3 = iunserializer($og['clerkCommission3']);
                        $clerk_l3 = $p_clerk->getLevel($clerk_m3['openid']);
                        $clerk_oc3 = isset($clerk_c3['level' . $clerk_l3['id']]) ? $clerk_c3['level' . $clerk_l3['id']] : $clerk_c3['default'];
                    }
                    $og['clerk_oc3'] = $clerk_oc3;
                    $clerkCommission3 += $clerk_oc3;
                }
            }
            unset($og);
            $clerkCommission_array = array(0 => $clerkCommission1, 1 => $clerkCommission2, 2 => $clerkCommission3);
            foreach ($clerks as $key => $value) {
                $clerks[$key]['clerkCommission'] = $clerkCommission_array[$key];
                if ($key > 2) {
                    unset($clerks[$key]);
                }
            }
        }

        //医生
        $doctors = array();
        if ($p_doctor) {
            $doctors = $p_doctor->getDoctors($id);
            $doctor_m1 = isset($doctors[0]) ? $doctors[0] : false;
            $doctor_m2 = isset($doctors[1]) ? $doctors[1] : false;
            $doctor_m3 = isset($doctors[2]) ? $doctors[2] : false;
            $doctorCommission1 = 0;
            $doctorCommission2 = 0;
            $doctorCommission3 = 0;
            foreach ($goods as &$og) {
                $doctor_oc1 = 0;
                $doctor_oc2 = 0;
                $doctor_oc3 = 0;
                $doctorCommissions = iunserializer($og['doctor_commissions']);
                if (!empty($doctor_m1)) {
                    if (is_array($doctorCommissions)) {
                        $doctor_oc1 = isset($doctorCommissions['level1']) ? floatval($doctorCommissions['level1']) : 0;
                    } else {
                        $doctor_c1 = iunserializer($og['doctor_commission']);
                        $doctor_l1 = $p_doctor->getLevel($doctor_m1['openid']);
                        $doctor_oc1 = isset($doctor_c1['level' . $doctor_l1['id']]) ? $doctor_c1['level' . $doctor_l1['id']] : $doctor_c1['default'];
                    }
                    $og['doctor_oc1'] = $doctor_oc1;
                    $doctorCommission1 += $doctor_oc1;
                }
                if (!empty($doctor_m2)) {
                    if (is_array($doctorCommissions)) {
                        $doctor_oc2 = isset($doctorCommissions['level2']) ? floatval($doctorCommissions['level2']) : 0;
                    } else {
                        $doctor_c2 = iunserializer($og['doctor_commission2']);
                        $doctor_l2 = $p_doctor->getLevel($doctor_m2['openid']);
                        $doctor_oc2 = isset($doctor_c2['level' . $doctor_l2['id']]) ? $doctor_c2['level' . $doctor_l2['id']] : $doctor_c2['default'];
                    }
                    $og['doctor_oc2'] = $doctor_oc2;
                    $doctorCommission2 += $doctor_oc2;
                }
                if (!empty($doctor_m3)) {
                    if (is_array($doctorCommissions)) {
                        $doctor_oc3 = isset($doctorCommissions['level3']) ? floatval($doctorCommissions['level3']) : 0;
                    } else {
                        $doctor_c3 = iunserializer($og['doctor_commission3']);
                        $doctor_l3 = $p_doctor->getLevel($doctor_m3['openid']);
                        $doctor_oc3 = isset($doctor_c3['level' . $doctor_l3['id']]) ? $doctor_c3['level' . $doctor_l3['id']] : $doctor_c3['default'];
                    }
                    $og['doctor_oc3'] = $doctor_oc3;
                    $doctorCommission3 += $doctor_oc3;
                }
            }
            unset($og);

            $doctorCommission_array = array(0 => $doctorCommission1, 1 => $doctorCommission2, 2 => $doctorCommission3);
            foreach ($doctors as $key => $value) {
                $doctors[$key]['doctorCommission'] = $doctorCommission_array[$key];
                if ($key > 2) {
                    unset($doctors[$key]);
                }
            }
        }

        $condition = " o.uniacid=:uniacid and o.deleted=0";
        $paras = array(':uniacid' => $_W['uniacid']);
        $totals = array();
        $coupon = false;
        if (com('coupon') && !empty($item['couponid'])) {
            $coupon = com('coupon')->getCouponByDataID($item['couponid']);
        }
        $order_fields = false;
        $order_data = false;
        if (p('diyform')) {
            $diyform_set = p('diyform')->getSet();
            foreach ($goods as $g) {
                if (!empty($g['diyformdata'])) {
                    break;
                }
            }
            //订单统一模板
            if (!empty($item['diyformid'])) {
                $orderdiyformid = $item['diyformid'];
                if (!empty($orderdiyformid)) {
                    $order_fields = iunserializer($item['diyformfields']);
                    $order_data = iunserializer($item['diyformdata']);
                }
            }
        }
        if (com('verify')) {
            $verifyinfo = iunserializer($item['verifyinfo']);

            if (!empty($item['verifyopenid'])) {
                $saler = m('member')->getMember($item['verifyopenid']);
                $saler['salername'] = pdo_fetchcolumn('select salername from ' . tablename('elapp_shop_saler') . ' where openid=:openid and uniacid=:uniacid limit 1 ', array(':uniacid' => $_W['uniacid'], ':openid' => $item['verifyopenid']));
            }
            if (!empty($item['verifystoreid'])) {
                $store = pdo_fetch('select * from ' . tablename('elapp_shop_store') . ' where id=:storeid limit 1 ', array(':storeid' => $item['verifystoreid']));
            }

            //核销
            if ($item['isverify']) {
                if (is_array($verifyinfo)) {
                    if (empty($item['dispatchtype'])) {
                        foreach ($verifyinfo as &$v) {
                            if ($v['verified'] || $item['verifytype'] == 1) {
                                $v['storename'] = pdo_fetchcolumn('select storename from ' . tablename('elapp_shop_store') . " where id=:id limit 1", array(':id' => $v['verifystoreid']));
                                if (empty($v['storename'])) {
                                    $v['storename'] = "总店";
                                }
                                $v['nickname'] = pdo_fetchcolumn('select nickname from ' . tablename('elapp_shop_member') . " where openid=:openid and uniacid=:uniacid limit 1", array(':openid' => $v['verifyopenid'], ':uniacid' => $_W['uniacid']));
                                $v['salername'] = pdo_fetchcolumn('select salername from ' . tablename('elapp_shop_saler') . " where openid=:openid and uniacid=:uniacid limit 1", array(':openid' => $v['verifyopenid'], ':uniacid' => $_W['uniacid']));
                            }
                        }
                        unset($v);
                    }
                }
            }
        }
        include $this->template();
    }

    // 判断是否存在多商户插件
    private function merchPluginExists()
    {
        return p('merch') ? true : false;
    }
}
