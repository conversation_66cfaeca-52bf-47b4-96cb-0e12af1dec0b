<?php
namespace app\controller\member;

use app\controller\MobileLoginPage;

class MerchfollowController extends MobileLoginPage {
    protected $member;

	public function __construct(){
		global $_W, $_GPC;
		parent::__construct();
		$this->member = m('member')->getInfo($_W['openid']);
	}
    function main() {
        global $_W, $_GPC;
        include $this->template();
    }

    function get_list() {
        global $_W, $_GPC;
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        $pindex = max(1, intval($_GPC['page']));
        $psize = 10;
        $condition = ' and f.uniacid = :uniacid and f.openid=:openid and f.deleted=0';
        if ($merch_plugin && $merch_data['is_openmerch']) {
            $condition = ' and f.uniacid = :uniacid and f.openid=:openid and f.deleted=0 and f.type=0';
        }
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $sql = 'SELECT COUNT(*) FROM ' . tablename('elapp_shop_member_merchfollow') . " f where 1 {$condition}";
        $total = pdo_fetchcolumn($sql, $params);
        $list = array();
        if (!empty($total)) {
            $sql = 'SELECT f.id,f.merchid,f.logo as favthumb,f.merchname as favtitle,g.merchname,g.logo,g.id,
    CASE
	g.deleted 
	WHEN g.merchname IS NULL THEN 0 
	ELSE 1 
	END
	 as deleted
 FROM ' . tablename('elapp_shop_member_merchfollow') . ' f '
                . ' left join ' . tablename('elapp_shop_merch_user') . ' g on f.merchid = g.id '
                . ' where 1 ' . $condition . ' ORDER BY deleted asc ,`id` DESC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize;

            $list = pdo_fetchall($sql, $params);
            //判断如果商品被删除了 就走默认的图片
            if (!empty($list)) {
                foreach ($list as &$item) {
                    if (empty($item['merchname'])){
                        $item['merchname'] = $item['favtitle'];
                        $item['delete'] = 1;
                    }
                    if (empty($item['logo'])){
                        $item['logo'] = $item['favthumb'];
                    }
                }
                unset($item);
            }
            $list = set_medias($list, 'logo');
            if (!empty($list) && $merch_plugin && $merch_data['is_openmerch']) {
                $merch_user = $merch_plugin->getListUser($list, 'merch_user');
                foreach ($list as &$row) {
                    $row['merchname'] = $merch_user[$row['merchid']]['merchname'] ? $merch_user[$row['merchid']]['merchname'] : $_W['shopset']['shop']['name'];
                }
                unset($row);
            }
        }
        show_json(1, array('list' => $list, 'total' => $total, 'pagesize' => $psize));
    }

    function toggle(){
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $member = m('member')->getInfo($_W['openid']);
        $ismerchfollow = intval($_GPC['ismerchfollow']);
        $merchs = pdo_fetch('select * from ' . tablename('elapp_shop_merch_user') . ' where id=:id and uniacid=:uniacid limit 1', array(':id' => $id, ':uniacid' => $_W['uniacid']));
        if (empty($merchs)) {
            show_json(0, '店铺未找到');
        }
        $data = pdo_fetch('select id,deleted from ' . tablename('elapp_shop_member_merchfollow') . ' where uniacid=:uniacid and merchid=:id and openid=:openid limit 1', array(
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':id' => $id
        ));
        if (empty($data)) {
            if (!empty($ismerchfollow)) {
                $data = array(
                    'uniacid' => $_W['uniacid'],
                    'merchid' => $id,
                    'openid' => $_W['openid'], //兼容1.x
                    'mid' => $member['id'],
                    'createtime' => time(),
                    'logo' => $merchs['logo'],
                    'merchname' => $merchs['merchname'],
                );
                pdo_insert('elapp_shop_member_merchfollow', $data);
            }
        } else {
            pdo_update('elapp_shop_member_merchfollow', array('deleted' => $ismerchfollow ? 0 : 1), array('id' => $data['id'], 'uniacid' => $_W['uniacid']));
        }
        show_json(1, array('ismerchfollow' => $ismerchfollow == 1));
    }

    function remove() {
        global $_W, $_GPC;
        $ids = $_GPC['ids'];
        if (empty($ids) || !is_array($ids)) {
            show_json(0, '参数错误');
        }
        // 遍历强转int
        foreach ($ids as &$id) {
            $id = (int)$id;
        }
        // 去重、去空
        $ids = array_unique(array_filter($ids));
        if (empty($ids)) {
            show_json(0, '参数错误');
        }
        $sql = "update " . tablename('elapp_shop_member_merchfollow') . ' set deleted=1 where openid=:openid and id in (' . implode(',', $ids) . ')';
        pdo_query($sql, array(':openid' => $_W['openid']));
        show_json(1);
    }
}