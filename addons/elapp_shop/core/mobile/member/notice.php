<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;

class NoticeController extends MobileLoginPage {
	public function main(){
		global $_W, $_GPC;
		$openid = $_W['openid'];
		$uniacid = $_W['uniacid'];
		$member = m('member')->getMember($openid);
		$notice = iunserializer($member['noticeset']);
		$hascommission = false;
		if (p('commission')) {
			$cset = p('commission')->getSet();
			$hascommission = !empty($cset['level']);
		}
		#虚店店长
		$hasvrshop = false;
		if (p('vrshop')) {
			$vrshopSet = p('vrshop')->getSet();
			$hasvrshop = !empty($vrshopSet['level']);
		}
		#虚店店员
		$hasclerk = false;
		if (p('clerk')) {
			$clerkSet = p('clerk')->getSet();
			$hasclerk = !empty($clerkSet['level']);
		}
		#虚店合伙人
		$hascopartner = false;
		if (p('copartner')) {
			$copartnerSet = p('copartner')->getSet();
			$hascopartner = !empty($copartnerSet['level']);
		}
		#医生
		$hasdoctor = false;
		if (p('doctor')) {
			$doctorSet = p('doctor')->getSet();
			$hasdoctor = !empty($doctorSet['level']);
		}
		if ($_W['ispost']) {
			$type = trim($_GPC['type']);
			if (empty($type)) {
				show_json(0, '参数错误');
			}
			$checked = intval($_GPC['checked']);
			if (empty($checked)) {
				$notice[$type] = 1;
			}else{
				unset($notice[$type]);
			}
			pdo_update('elapp_shop_member', array('noticeset' => iserializer($notice)), array('openid' => $openid, 'uniacid' => $uniacid));
			show_json(1);
		}
		include $this->template();
	}
}