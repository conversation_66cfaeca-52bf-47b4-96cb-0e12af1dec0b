<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;

class HistoryController extends MobileLoginPage
{

    function main()
    {
        global $_W, $_GPC;
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if ($merch_plugin && $merch_data['is_openmerch']) {
            include $this->template('merch/member/history');
        } else {
            include $this->template();
        }
    }

    function get_list()
    {
        global $_W, $_GPC;
        $pindex = max(1, intval($_GPC['page']));
        $psize = 10;
        $condition = ' and f.uniacid = :uniacid and f.openid=:openid and f.deleted=0';
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $sql = 'SELECT COUNT(*) FROM ' . tablename('elapp_shop_member_history') . " f where 1 {$condition}";
        $total = pdo_fetchcolumn($sql, $params);
        $sql = 'SELECT f.id,f.goodsid,g.title,g.thumb,g.marketprice,g.productprice,f.createtime,g.merchid FROM ' . tablename('elapp_shop_member_history') . ' f '
            . ' left join ' . tablename('elapp_shop_goods') . ' g on f.goodsid = g.id '
            . ' where 1 ' . $condition . ' ORDER BY `id` DESC LIMIT ' . ($pindex - 1) * $psize . ',' . $psize;
        $list = pdo_fetchall($sql, $params);
        $merch_plugin = p('merch');
        $merch_data = m('common')->getPluginset('merch');
        if (!empty($list) && $merch_plugin && $merch_data['is_openmerch']) {
            $merch_user = $merch_plugin->getListUser($list, 'merch_user');
        }
        foreach ($list as &$row) {
            $row['createtime'] = date('Y-m-d H:i:s', $row['createtime']);
            $row['thumb'] = tomedia($row['thumb']);
            $row['merchname'] = $merch_user[$row['merchid']]['merchname'] ? $merch_user[$row['merchid']]['merchname'] : $_W['shopset']['shop']['name'];
        }
        unset($row);
        show_json(1, array('list' => $list, 'total' => $total, 'pagesize' => $psize));
    }

    function remove()
    {
        global $_W, $_GPC;
        $ids = $_GPC['ids'];
        if (empty($ids) || !is_array($ids)) {
            show_json(0, '参数错误');
        }
        foreach ($ids as &$id) {
            $id = (int)$id;
        }
        $ids = array_unique(array_filter($ids));
        if (empty($ids)) {
            show_json(0, '参数错误');
        }
        $sql = "update " . tablename('elapp_shop_member_history') . ' set deleted=1 where openid=:openid and id in (' . implode(',', $ids) . ')';
        pdo_query($sql, array(':openid' => $_W['openid']));
        show_json(1);
    }
}
