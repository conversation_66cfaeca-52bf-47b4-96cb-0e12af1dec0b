<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;

class QuickinfoController extends MobileLoginPage {
	protected $member;

	public function __construct(){
		global $_W, $_GPC;
		parent::__construct();
		$this->member = m('member')->getInfo($_W['openid']);
	}

	protected function diyformData(){
		$template_flag = 0;
		$diyform_plugin = p('diyform');
		if ($diyform_plugin) {
			$set_config = $diyform_plugin->getSet();
			$user_diyform_open = $set_config['user_diyform_open'];
			if ($user_diyform_open == 1) {
				$template_flag = 1;
				$diyform_id = $set_config['user_diyform'];
				if (!empty($diyform_id)) {
					$formInfo = $diyform_plugin->getDiyformInfo($diyform_id);
					$fields = $formInfo['fields'];
					$diyform_data = iunserializer($this->member['diymemberdata']);
					$f_data = $diyform_plugin->getDiyformData($diyform_data, $fields, $this->member);
				}
			}
		}
		return array('template_flag' => $template_flag, 'set_config' => $set_config, 'diyform_plugin' => $diyform_plugin, 'formInfo' => $formInfo, 'diyform_id' => $diyform_id, 'diyform_data' => $diyform_data, 'fields' => $fields, 'f_data' => $f_data);
	}
    
    function main() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
		$prid = intval($_GPC['prid']);
		$diyform_data = $this->diyformData();
		extract($diyform_data);
		$returnurl = urldecode(trim($_GPC['returnurl']));
		$member = $this->member;
		$wapset = m('common')->getSysset('wap');
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $address_street = intval($area_set['address_street']);
        $intellectAddress = m("common")->getSysset("trade")["intellect_address"];
        $is_from_wx = $_GPC['is_from_wx'];
        if($is_from_wx){
            $wx_province = $_GPC['province'];
            $wx_city = $_GPC['city'];
            $wx_area = $_GPC['area'];
            $wx_address = $_GPC['address'];
            $wx_name = $_GPC['realname'];
            $wx_mobile = $_GPC['mobile'];
            $wx_address_info = array(
                'province'  => $wx_province,
                'city'  => $wx_city,
                'area'  => $wx_area,
                'address'  => $wx_address,
                'realname'  => $wx_name,
                'mobile'  => $wx_mobile,
            );
        }
		if($prid){
			$patientInfo = pdo_fetch('select * from ' . tablename('elapp_shop_prescription_patientinfo') . ' where id=:id and openid=:openid and uniacid=:uniacid limit 1 ', array(':id' => $prid, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
		}else{
			$patientInfo = pdo_fetch('select * from ' . tablename('elapp_shop_prescription_patientinfo') . ' where isdefault=1 and openid=:openid and uniacid=:uniacid limit 1 ', array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
		}
        if(!empty($id) || !empty($wx_address_info)){
            if(empty($wx_address_info)){
                $address = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where id=:id and openid=:openid and uniacid=:uniacid limit 1 ', array(':id' => $id, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
            }else{
                $address = $wx_address_info;
            }
            //如果地址code为空的情况
            if(empty($address['datavalue'])){
                //读取新版地址库获取code
                $provinceName=$address['province'];
                $citysName=$address['city'];
                $countyName=$address['area'];
                //地址code
                $province_code=0;
                $citys_code=0;
                $county_code=0;
                $path = IA_ROOT .'/public'.  STATIC_ROOT . "shop/js/dist/area/AreaNew.xml";
                $xml = file_get_contents($path);
                $array = xml2array($xml);
                $newArr = array();
                if(is_array($array['province'])) {
                    foreach ($array['province'] as $i=>$v) {
                        if($i>0) {
                            if($v['@attributes']['name']==$provinceName && !is_null($provinceName) && $provinceName!="") {
                                $province_code = $v['@attributes']['code'];
                                if(is_array($v['city'])) {
                                    if(!isset($v['city'][0])){
                                        $v['city'] = array(0=>$v['city']);
                                    }
                                    foreach ($v['city'] as $ii=>$vv) {
                                        if($vv['@attributes']['name']==$citysName && !is_null($citysName) && $citysName!="") {
                                            $citys_code= $vv['@attributes']['code'];
                                            if(is_array($vv['county'])) {
                                                if(!isset($vv['county'][0])) {
                                                    $vv['county'] = array(0=>$vv['county']);
                                                }
                                                foreach ($vv['county'] as $iii=>$vvv) {
                                                    if($vvv['@attributes']['name']==$countyName && !is_null($countyName) && $countyName!="") {
                                                        $county_code= $vvv['@attributes']['code'];
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }

                if($province_code!=0 &&$citys_code!=0&&$county_code!=0){
                    $address['datavalue']=$province_code." ".$citys_code." ".$county_code;
                    if(empty($wx_address_info)){
                        pdo_update('elapp_shop_member_address', $address, array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
                    }
                }
            }
            $show_data = 1;
            if((!empty($new_area) && empty($address['datavalue'])) || (empty($new_area) && !empty($address['datavalue']))) {
                $show_data = 0;
            }
        }else{
			$address = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where isdefault=1 and openid=:openid and uniacid=:uniacid limit 1 ', array( ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
			$id = $address['id'];
			//如果地址code为空的情况
            if(empty($address['datavalue'])){
                //读取新版地址库获取code
                $provinceName=$address['province'];
                $citysName=$address['city'];
                $countyName=$address['area'];
                //地址code
                $province_code=0;
                $citys_code=0;
                $county_code=0;
                $path = IA_ROOT .'/public'. STATIC_ROOT . "shop/js/dist/area/AreaNew.xml";
                $xml = file_get_contents($path);
                $array = xml2array($xml);
                $newArr = array();
                if(is_array($array['province'])) {
                    foreach ($array['province'] as $i=>$v) {
                        if($i>0) {
                            if($v['@attributes']['name']==$provinceName && !is_null($provinceName) && $provinceName!="") {
                                $province_code = $v['@attributes']['code'];
                                if(is_array($v['city'])) {
                                    if(!isset($v['city'][0])){
                                        $v['city'] = array(0=>$v['city']);
                                    }
                                    foreach ($v['city'] as $ii=>$vv) {
                                        if($vv['@attributes']['name']==$citysName && !is_null($citysName) && $citysName!="") {
                                            $citys_code= $vv['@attributes']['code'];
                                            if(is_array($vv['county'])) {
                                                if(!isset($vv['county'][0])) {
                                                    $vv['county'] = array(0=>$vv['county']);
                                                }
                                                foreach ($vv['county'] as $iii=>$vvv) {
                                                    if($vvv['@attributes']['name']==$countyName && !is_null($countyName) && $countyName!="") {
                                                        $county_code= $vvv['@attributes']['code'];
                                                    }
                                                }
                                            }
                                        }
                                    }
                                }
                            }
                        }
                    }
                }
                if($province_code!=0 &&$citys_code!=0&&$county_code!=0){
                    $address['datavalue']=$province_code." ".$citys_code." ".$county_code;
                    if(empty($wx_address_info)){
                        pdo_update('elapp_shop_member_address', $address, array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
                    }
                }
            }
            $show_data = 1;
            if((!empty($new_area) && empty($address['datavalue'])) || (empty($new_area) && !empty($address['datavalue']))) {
                $show_data = 0;
            }
		}
        include $this->template();
    }
    //设置默认地址
    function setdefault() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $data = pdo_fetch('select id from ' . tablename('elapp_shop_member_address') . ' where id=:id and deleted=0 and uniacid=:uniacid limit 1', array(
            ':uniacid' => $_W['uniacid'],
            ':id' => $id
        ));
        if (empty($data)) {
            show_json(0, '地址未找到');
        }
        pdo_update('elapp_shop_member_address', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        pdo_update('elapp_shop_member_address', array('isdefault' => 1), array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        show_json(1);
    }

    /**
     * 删除字符串中的空格,提取手机号码
     * @param $string mobile 含有unicode编码的手机号码
     * @return string
     */
    private function extractNumber($string) {
        $string = preg_replace('# #', '', $string);
        preg_match('/\d{11}/', $string, $result);
        return (string)$result[0];
    }

    function submit() {
        global $_W, $_GPC;
        @session_start();
        $member = m('member')->getMember($_W['openid']);
        $id = intval($_GPC['id']);
		$prid = intval($_GPC['prid']);
        $data = $_GPC['addressdata'];
        $addresdata['mobile'] = $this->extractNumber($data['mobile']); //去除手机号中的空格
        $areas = explode(' ', $data['areas']);
        $addresdata['province'] = $areas[0];
        $addresdata['city'] = $areas[1];
        $addresdata['area'] = $areas[2];
		$addresdata['address'] = $data['address'];
		$addresdata['realname'] = $data['realname'];
        $addresdata['street'] = trim($data['street']);
        $addresdata['datavalue'] = trim($data['datavalue']);
        $addresdata['streetdatavalue'] = trim($data['streetdatavalue']);
		//验证身份证号码
		if(!empty(trim($data['idnumber']))){
			$idnumber = trim($data['idnumber']);
			$isIdnumber = m('member')->isIdCard($idnumber);
			if(empty($isIdnumber)){
				return show_json(0, '身份证号码错误');
			}
			$gender = m('member')->get_sex($idnumber);//性别
			$birthday = m('member')->get_birthday($idnumber);//生日
			$age = m('member')->get_age($idnumber);//年龄
		}
        #绑定手机
        $mobile = trim($data['mobile']);
        $verifycode = trim($data['verifycode']);
        $pwd = trim($data['pwd']);
        $key = '__elapp_shop_member_verifycodesession_' . $_W['uniacid'] . '_' . $mobile;
        if( !isset($_SESSION[$key]) ||  $_SESSION[$key]!==$verifycode || !isset($_SESSION['verifycodesendtime']) || $_SESSION['verifycodesendtime']+12000<time()){
            show_json(0, '验证码错误或已过期');
        }
        unset($_SESSION[$key]);

        //会员基础信息
        $memberarr = array(
            'realname' => trim($data['realname']),
            'gender' => $gender,
            'idnumber' => trim($data['idnumber']),
            'weixin' => trim($data['weixin']),
            'birthyear' => intval($birthday['birthyear']),
            'birthmonth' => intval($birthday['birthmonth']),
            'birthday' => intval($birthday['birthday']),
            'province' => $addresdata['province'],
            'city' => $addresdata['city'],
            'datavalue' => $addresdata['datavalue'],
            'mobile' => trim($data['mobile']),
            'nickname' => trim($data['nickname']),
            'avatar' => trim($data['avatar'])
        );
        //病历基础信息
        $patientinfoarr = array(
            'patientName' => trim($data['realname']),//姓名
            'patientSex' => $gender == 1?1:2,//性别
            'patientIdNo' => $isIdnumber?$idnumber:'',//身份证号码
            'patientAge' => trim($age),//年龄
            'patientPhone' => trim($data['mobile']), //电话号码
            'patientIdNoType' => $isIdnumber?0:'', //证件类型
            'illDesc' => $data['illDesc'],//主诉
            'historyOfAllergy' => trim($data['historyOfAllergy']), //是否药物过敏史
            'allergyDetail' => $data['allergyDetail'], //过敏史详情
            'historyOfSickness' => trim($data['historyOfSickness']), //是否有既往病史
            'sicknessDetail' => $data['sicknessDetail'], //往病史详情
            'liver' => trim($data['liver']), //肝功能
            'renal' => trim($data['renal']), //肾功能
            'pregnancy' => trim($data['pregnancy']), //妊娠哺乳
            'images' => is_array($data['images']) ? iserializer($data['images']) : iserializer(array())//复诊凭证
        );
		if (empty($_W['shopset']['app']['isclose']) && !empty($_W['shopset']['app']['openbind']) || !empty($_W['shopset']['wap']['open'])) {
			//unset($memberarr['mobile']);
		}
		//更新会员信息
		pdo_update('elapp_shop_member', $memberarr, array('openid' => $_W['openid'], 'uniacid' => $_W['uniacid']));

		if (!empty($this->member['uid'])) {
			$mcdata = $_GPC['mcdata'];
			unset($mcdata['credit1']);
			unset($mcdata['credit2']);
			m('member')->mc_update($this->member['uid'], $mcdata);
		}

        $post = $addresdata;
        $post['id'] = $id;
        $post['is_from_wx'] = $_GPC['is_from_wx'];
        if($this->is_repeated_address($post)){
            return show_json(0, '此地址已经添加过');
        }
         /* if(empty($data['mobile'])){
            return show_json(0, '请填写手机号');
        }
       */
        $area_set = m("util")->get_area_config_set();
        if ($area_set["new_area"] && $area_set["address_street"] && empty($addresdata["street"])) {
            $other = array("境外地区", "台湾", "澳门", "香港");
            if (!in_array($addresdata["province"], $other)) {
                return show_json(0, "请选择所在街道");
            }
        }
        // 默认地址
        $isdefault = 1;//intval($data['isdefault']);
        unset($addresdata['isdefault']);

        unset($addresdata['areas']);
        $addresdata['openid'] = $_W['openid'];
        $addresdata['uniacid'] = $_W['uniacid'];
		$address = pdo_fetch('select * from ' . tablename('elapp_shop_member_address') . ' where isdefault=1 and openid=:openid and uniacid=:uniacid limit 1 ', array( ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
		$id = $address['id'];
        if (empty($id)) {
            $addresscount = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_member_address') . ' where openid=:openid and deleted=0 and `uniacid` = :uniacid ', array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
            if ($addresscount <= 0) {
                $addresdata['isdefault'] = 1;
            }
            pdo_insert('elapp_shop_member_address', $addresdata);
            $id = pdo_insertid();
        } else {
            //修改地址后置空经纬度-》同城配送
            $addresdata['lng']='';
            $addresdata['lat']='';
            pdo_update('elapp_shop_member_address', $addresdata, array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        }
        // 更新默认地址
        /* if(!empty($isdefault)){
            pdo_update('elapp_shop_member_address', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
            pdo_update('elapp_shop_member_address', array('isdefault' => 1), array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        } */

		//更新病历		
		$patientinfoarr['openid'] = $_W['openid'];
        $patientinfoarr['uniacid'] = $_W['uniacid'];
		if (empty($prid)) {
            $patientInfocount = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_prescription_patientinfo') . ' where openid=:openid and deleted=0 and `uniacid` = :uniacid ', array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
            if ($patientInfocount <= 0) {
                $patientinfoarr['isdefault'] = 1;
            }
            pdo_insert('elapp_shop_prescription_patientinfo', $patientinfoarr);
            $prid = pdo_insertid();
        } else {
            pdo_update('elapp_shop_prescription_patientinfo', $patientinfoarr, array('id' => $prid, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        }

        $salt = m('account')->getSalt();
        $mdata = array(
            'mobile'=>$mobile,
            'pwd'=>md5($pwd.$salt),
            'salt'=>$salt,
            'mobileverify'=>1
        );
        m('bind')->update($member['id'],$mdata);
//        m('account')->setLogin($member['id']); todo [low] [23-04-18] 这里线上出现500，但是本地不存在问题，暂时注释掉
//        $sendtime = $_SESSION['verifycodesendtime'];
//        if(empty($sendtime) || $sendtime+60<time()){
//            $endtime = 0;
//        }else{
//            $endtime = 60 - (time() - $sendtime);
//        }
        show_json(1, array('addressid' => $id));
    }

    function delete() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $data = pdo_fetch('select id,isdefault from ' . tablename('elapp_shop_member_address') . ' where  id=:id and openid=:openid and deleted=0 and uniacid=:uniacid  limit 1', array(
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':id' => $id
        ));
        if (empty($data)) {
            show_json(0, '地址未找到');
        }
        pdo_update('elapp_shop_member_address', array('deleted' => 1), array('id' => $id));

        //如果删除默认地址
        if ($data['isdefault'] == 1) {
            //将最近添加的地址设置成默认的
            pdo_update('elapp_shop_member_address', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid'], 'id' => $id));
            $data2 = pdo_fetch('select id from ' . tablename('elapp_shop_member_address') . ' where openid=:openid and deleted=0 and uniacid=:uniacid order by id desc limit 1', array(
                ':uniacid' => $_W['uniacid'],
                ':openid' => $_W['openid']
            ));
            if (!empty($data2)) {
                pdo_update('elapp_shop_member_address', array('isdefault' => 1), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid'], 'id' => $data2['id']));
                show_json(1, array('defaultid' => $data2['id']));
            }
        }
        show_json(1);
    }

    function selector() {
        global $_W, $_GPC;
        $area_set = m('util')->get_area_config_set();
        $new_area = intval($area_set['new_area']);
        $address_street = intval($area_set['address_street']);
        $condition = ' and openid=:openid and deleted=0 and  `uniacid` = :uniacid  ';
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $sql = 'SELECT * FROM ' . tablename('elapp_shop_member_address') . ' where 1 ' . $condition . ' ORDER BY isdefault desc, id DESC ';
        $list = pdo_fetchall($sql, $params);
        include $this->template();
        exit;
    }


    function getselector() {
        global $_W, $_GPC;
        $condition = ' and openid=:openid and deleted=0 and  `uniacid` = :uniacid  ';
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $keywords = $_GPC['keywords'];
        if (!empty($keywords)) {
            $condition .= ' AND (`realname` LIKE :keywords OR `mobile` LIKE :keywords OR `province` LIKE :keywords OR `city` LIKE :keywords OR `area` LIKE :keywords OR `address` LIKE :keywords OR `street` LIKE :keywords)';
            $params[':keywords'] = '%' . trim($keywords) . '%';
        }
        $sql = 'SELECT *  FROM ' . tablename('elapp_shop_member_address') . ' where 1 ' . $condition . ' ORDER BY isdefault desc, id DESC ';
        $list = pdo_fetchall($sql, $params);
        foreach($list as &$item) {
            $item['editurl']=mobileUrl('member/address/post',array('id'=>$item['id']));
        }
        unset($item);
        if(count($list)>0){
            show_json(1,array("list"=>$list));
        }else{
            show_json(0);
        }
    }

    /**
     * 验证地址是否重复添加
     * @param $post
     * @return bool
     */
    private function is_repeated_address($post){
        global $_W;
        if(empty($post['is_from_wx']) || $post['id']){
            return false;
        }
        if(empty($post['province']) || empty($post['city']) || empty($post['area'])){
            return false;
        }
        $condition = 'uniacid=:uniacid and openid=:openid and realname=:realname and mobile=:mobile and mobile=:mobile and province=:province and city=:city and area=:area and address=:address and deleted=0';
        $params = [
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':realname' => $post['realname'],
            ':mobile' => $post['mobile'],
            ':province' => $post['province'],
            ':city' => $post['city'],
            ':area' => $post['area'],
            ':address' => $post['address'],
        ];
        $address = pdo_fetch("SELECT id FROM " . tablename('elapp_shop_member_address') . " where {$condition} limit 1",$params);
        if($address){
            return true;
        }
        return false;
    }
}