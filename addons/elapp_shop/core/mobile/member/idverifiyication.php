<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;
use app\core\model\member\MemberVerifyModel;

class IdverifyicationController extends MobileLoginPage
{
    function main()
    {
        global $_W, $_GPC;
        //todo 
        //1. sql 查询是否有实名认证
        $this->list();
    }

    public function list(){
        global $_W,$_GPC;
        include $this->template('member/idverifiycation_list');
    }

    public function detail(){
        $param = request()->param();
        $memberVerifyResult = [];
        if (!empty($param['id'])) {
            if (empty($param['idcard'])) { //编辑进来只有id
                $memberVerifyResult = (new MemberVerifyModel())->where(['id'=>$param['id']])->findOrEmpty()->toArray();
            } else { //兼容由银行卡提现的情况
                $memberVerifyResult = $param;
            }
        }
        include $this->template('member/idverifiyication');
    }

    function getList()
    {
        $memberVerifyResult = (new MemberVerifyModel())->where(['member_id' => $this->memberId])->select()->toArray();
        return $this->succeed('操作成功', $memberVerifyResult);
    }
}