<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;

class MsyssetController extends MobileLoginPage {
	public function main(){
		global $_W, $_GPC;
		$openid = $_W['openid'];
		$uniacid = $_W['uniacid'];
		$member = m('member')->getMember($openid);
		$mSysSet = iunserializer($member['mSysSet']);
		$hascommission = false;
		if (p('commission')) {
			$cset = p('commission')->getSet();
			$hascommission = !empty($cset['level']);
		}
		if ($_W['ispost']) {
			$type = trim($_GPC['type']);
			if (empty($type)) {
				show_json(0, '参数错误');
			}
			$checked = intval($_GPC['checked']);
			if (empty($checked)) {
				$mSysSet[$type] = 1;
			} else {
				unset($mSysSet[$type]);
			}
			pdo_update('elapp_shop_member', array('mSysSet' => iserializer($mSysSet)), array('openid' => $openid, 'uniacid' => $uniacid));
			show_json(1);
		}
		include $this->template();
	}
}