<?php
namespace app\controller\member;
use app\controller\MobileLoginPage;
use app\plugin\prescription\core\logic\PrescriptionPatientinfoLogic;

class PatientinfoController extends MobileLoginPage {
    function main() {
        global $_W, $_GPC, $_S;
        if(p('prescription')){
            $prescription = p('prescription');
            $prescription_set = $prescription->getSet();//调用互联网医院接口信息
        }
        $pindex = intval($_GPC['page']);
        $psize = 20;
        $condition = ' and openid=:openid and deleted=0 and  `uniacid` = :uniacid  ';
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $sql = 'SELECT COUNT(*) FROM ' . tablename('elapp_shop_prescription_patientinfo') . " where 1 $condition";
        $total = pdo_fetchcolumn($sql, $params);
        $sql = 'SELECT * FROM ' . tablename('elapp_shop_prescription_patientinfo') . ' where 1 ' . $condition . ' ORDER BY `id` DESC';
        if ($pindex != 0) {
            $sql .= 'LIMIT ' . ($pindex - 1) * $psize . ',' . $psize;
        }
        $list = pdo_fetchall($sql, $params);
        include $this->template();
    }

    function post() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        if(p('prescription')){
            $prescription = p('prescription');
            $prescription_set = $prescription->getSet();//调用互联网医院接口信息
        }
        $protocol_set = array();
        $protocol_set['protocolIsOpen'] = $prescription_set['protocolIsOpen'];
        if (empty($prescription_set['protocolTitle'])) {
            $protocol_set['protocolTitle'] = '互联网医院服务协议';
        } else {
            $protocol_set['protocolTitle'] = $prescription_set['protocolTitle'];
        }
        $is_from_wx = $_GPC['is_from_wx'];
        if($is_from_wx){            
            $wx_name = $_GPC['patientName'];
            $wx_mobile = $_GPC['patientPhone'];
            $wx_address_info = array(                                
                'patientName'  => $wx_name,
                'patientPhone'  => $wx_mobile,
            );
        }
        if(!empty($id) || !empty($wx_address_info)){
            if(empty($wx_address_info)){
                $patientInfo = pdo_fetch('select * from ' . tablename('elapp_shop_prescription_patientinfo') . ' where id=:id and openid=:openid and uniacid=:uniacid limit 1 ', array(':id' => $id, ':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
                $patientInfo['images'] = unserialize($patientInfo['images']);//复诊凭证反序列化 
                $tp = $patientInfo['images'];//临时赋值给前端loop用
            }else{
                $patientInfo = $wx_address_info;
            }
            //遍历凭证
            foreach ($patientInfo['images'] as $v){
                $images = $v;
            }
            $show_data = 1;
        }
        include $this->template();
    }

    function setdefault() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $data = pdo_fetch('select id from ' . tablename('elapp_shop_prescription_patientinfo') . ' where id=:id and deleted=0 and uniacid=:uniacid limit 1', array(
            ':uniacid' => $_W['uniacid'],
            ':id' => $id
        ));
        if (empty($data)) {
            show_json(0, '病历未找到');
        }
        pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 1), array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        show_json(1);
    }

    /**
     * 删除字符串中的空格,提取手机号码
     *     
     * @param $string mobile 含有unicode编码的手机号码
     * @return string
     */
    private function extractNumber($string){
        $string = preg_replace('# #', '', $string);
        preg_match('/\d{11}/', $string, $result);
        return (string)$result[0];
    }

    function submit() {
        global $_W, $_GPC;
        
        $id = intval($_GPC['id']);
        $data = $_GPC['patientinfodata'];
        $data['patientPhone'] = $this->extractNumber($data['patientPhone']); //去除手机号中的空格
        $data['images'] = is_array($data['images']) ? iserializer($data['images']) : iserializer(array());//复诊凭证
        $post = $data;
        $post['id'] = $id;
        $post['is_from_wx'] = $_GPC['is_from_wx'];
        if($this->is_repeated_PatientInfo($post)){
            return show_json(0, '此病历已经添加过');
        }
        if(empty($data['patientPhone'])){
            return show_json(0, '请填写手机号');
        }
        // 默认病历
        $isdefault = intval($data['isdefault']);
        unset($data['isdefault']);
        $data['openid'] = $_W['openid'];
        $data['uniacid'] = $_W['uniacid'];
        if (empty($id)) {
            $patientInfocount = pdo_fetchcolumn('SELECT count(*) FROM ' . tablename('elapp_shop_prescription_patientinfo') . ' where openid=:openid and deleted=0 and `uniacid` = :uniacid ', array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']));
            if ($patientInfocount <= 0) {
                $data['isdefault'] = 1;
            }
            pdo_insert('elapp_shop_prescription_patientinfo', $data);
            $id = pdo_insertid();
        } else {
            
            pdo_update('elapp_shop_prescription_patientinfo', $data, array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        }
        // 更新默认病历
        if(!empty($isdefault)){
            pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
            pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 1), array('id' => $id, 'uniacid' => $_W['uniacid'], 'openid' => $_W['openid']));
        }
        show_json(1, array('patientinfo_id' => $id));
    }

    function delete() {
        global $_W, $_GPC;
        $id = intval($_GPC['id']);
        $data = pdo_fetch('select id,isdefault from ' . tablename('elapp_shop_prescription_patientinfo') . ' where  id=:id and openid=:openid and deleted=0 and uniacid=:uniacid  limit 1', array(
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':id' => $id
        ));
        if (empty($data)) {
            show_json(0, '病历未找到');
        }
        pdo_update('elapp_shop_prescription_patientinfo', array('deleted' => 1), array('id' => $id));

        //如果删除默认病历
        if ($data['isdefault'] == 1) {
            //将最近添加的病历设置成默认的
            pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 0), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid'], 'id' => $id));
            $data2 = pdo_fetch('select id from ' . tablename('elapp_shop_prescription_patientinfo') . ' where openid=:openid and deleted=0 and uniacid=:uniacid order by id desc limit 1', array(
                ':uniacid' => $_W['uniacid'],
                ':openid' => $_W['openid']
            ));
            if (!empty($data2)) {
                pdo_update('elapp_shop_prescription_patientinfo', array('isdefault' => 1), array('uniacid' => $_W['uniacid'], 'openid' => $_W['openid'], 'id' => $data2['id']));
                show_json(1, array('defaultid' => $data2['id']));
            }
        }
        show_json(1);
    }

    function selector() {
        global $_W, $_GPC;
        $list_result = app(PrescriptionPatientinfoLogic::class)->getPatientinfoList(array('openid' => $_W['openid'], 'deleted' => 0), ['*'], array(), 'isdefault desc, id DESC');
        $list = $list_result['data'];
        $count = $list_result['count'];
        include $this->template("member/patientinfo/selector");
        exit;
    }


    function getselector() {
        global $_W, $_GPC;
        $condition = ' and openid=:openid and deleted=0 and  `uniacid` = :uniacid  ';
        $params = array(':uniacid' => $_W['uniacid'], ':openid' => $_W['openid']);
        $keywords = $_GPC['keywords'];
        if (!empty($keywords)) {
            $condition .= ' AND (`patientName` LIKE :keywords OR `patientPhone` LIKE :keywords)';
            $params[':keywords'] = '%' . trim($keywords) . '%';
        }
        $sql = 'SELECT *  FROM ' . tablename('elapp_shop_prescription_patientinfo') . ' where 1 ' . $condition . ' ORDER BY isdefault desc, id DESC ';
        $list = pdo_fetchall($sql, $params);
        foreach($list as &$item){
            $item['editurl']=mobileUrl('clerk/goods/patientinfo/post',array('id'=>$item['id']));
        }
        unset($item);
        if(count($list)>0){
            show_json(1,array("list"=>$list));
        }else{
            show_json(0);
        }
    }

    /**
     * 验证地址是否重复添加
     * @param $post
     * author
     * @return bool
     */
    private function is_repeated_PatientInfo($post){
        global $_W;
        if(empty($post['is_from_wx']) || $post['id']){
            return false;
        }
        $condition = 'uniacid=:uniacid and openid=:openid and patientName=:patientName and patientPhone=:patientPhone and deleted=0';
        $params = [
            ':uniacid' => $_W['uniacid'],
            ':openid' => $_W['openid'],
            ':patientName' => $post['patientName'],
            ':patientPhone' => $post['patientPhone'],            
        ];
        $PatientInfo = pdo_fetch("SELECT id FROM " . tablename('elapp_shop_prescription_patientinfo') . " where {$condition} limit 1",$params);
        if($PatientInfo){
            return true;
        }
        return false;
    }
}