<?php
namespace app\controller\member;
use app\com\logic\MemberLogic;
use app\com\validate\MemberValidate;
use app\controller\MobileLoginPage;
use app\core\model\member\MemberVerifyModel;
use app\core\model\settle\settleBillModel;
use app\logic\settle\SettleLogic;
use app\model\MemberContractSignModel;
use app\model\MemberModel;
use app\model\SettleWithdrawApplyModel;
use salary\Log;
use salary\Salary;

class IndexController extends MobileLoginPage
{
    public array $notNeedLogin = [
        'userContractNotify', 'singleBankNotify'
    ];
    public function main()
    {
        global $_W, $_GPC;
        $usermembercard = false;
        $member = m("member")->getMember($_W["openid"], true);

        if (p("membercard")) {
            $list_membercard = p("membercard")->get_Mycard("", 0, 100);
            $all_membercard = p("membercard")->get_Allcard(1, 100);
            if (p("membercard") && $list_membercard["total"] <= 0 && $all_membercard["total"] <= 0) {
                $usermembercard = false;
            } else {
                $usermembercard = true;
            }
        }
        $membercardset = p("membercard")->getSet();

        $level = m("member")->getLevel($_W["openid"]);
        if (com("wxcard")) {
            $wxcardupdatetime = intval($member["wxcardupdatetime"]);
            if ($wxcardupdatetime + 86400 < time()) {
                com_run("wxcard::updateMemberCardByOpenid", $_W["openid"]);
                pdo_update("elapp_shop_member", array("wxcardupdatetime" => time()), array("openid" => $_W["openid"]));
            }
        }
        $this->diypage("member");
        $open_creditshop = p("creditshop") && $_W["shopset"]["creditshop"]["centeropen"];
        $params = array(":uniacid" => $_W["uniacid"], ":openid" => $_W["openid"]);
        $merch_plugin = p("merch");
        $merch_data = m("common")->getPluginset("merch");
        if ($merch_plugin && $merch_data["is_openmerch"]) {
            $statics = array(
                "order_0" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and status=0 and (isparent=1 or (isparent=0 and parentid=0)) and paytype<>3 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_1" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and (status=1 or (status=0 and paytype=3)) and isparent=0 and refundid=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_2" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and (status=2 or (status=1 and sendtype>0)) and isparent=0 and refundid=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_4" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and refundstate=1 and isparent=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "cart" => pdo_fetchcolumn("select ifnull(sum(total),0) from " . tablename("elapp_shop_member_cart") . " where uniacid=:uniacid and openid=:openid and deleted=0", $params),
                "favorite" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_member_favorite") . " where uniacid=:uniacid and openid=:openid and deleted=0", $params));
        } else {
            $statics = array(
                "order_0" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and status=0 and isparent=0 and paytype<>3 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_1" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and (status=1 or (status=0 and paytype=3)) and isparent=0 and refundid=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_2" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and (status=2 or (status=1 and sendtype>0)) and isparent=0 and refundid=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_4" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and refundstate=1 and isparent=0 and uniacid=:uniacid and istrade=0 and userdeleted=0", $params),
                "order_5" => pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and uniacid=:uniacid and iscycelbuy=1 and status in(0,1,2)", $params),
                "cart" => pdo_fetchcolumn("select ifnull(sum(total),0) from " . tablename("elapp_shop_member_cart") . " where uniacid=:uniacid and openid=:openid and deleted=0 and selected = 1", $params),
                "favorite" => ($merch_plugin && $merch_data["is_openmerch"] ? pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_member_favorite") . " where uniacid=:uniacid and openid=:openid and deleted=0 and `type`=0", $params) : pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_member_favorite") . " where uniacid=:uniacid and openid=:openid and deleted=0", $params)));
        }
        $newstore_plugin = p("newstore");
        if ($newstore_plugin) {
            $statics["norder_0"] = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and status=0 and isparent=0 and istrade=1 and uniacid=:uniacid", $params);
            $statics["norder_1"] = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and status=1 and isparent=0 and istrade=1 and refundid=0 and uniacid=:uniacid", $params);
            $statics["norder_3"] = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and status=3 and isparent=0 and istrade=1 and uniacid=:uniacid", $params);
            $statics["norder_4"] = pdo_fetchcolumn("select count(*) from " . tablename("elapp_shop_order") . " where openid=:openid and ismr=0 and refundstate=1 and isparent=0 and istrade=1 and uniacid=:uniacid", $params);
        }
        $hascoupon = false;
        $hascouponcenter = false;
        $plugin_coupon = com("coupon");
        if ($plugin_coupon) {
            $time = time();
            $sql = "select count(*) from " . tablename("elapp_shop_coupon_data") . " d";
            $sql .= " left join " . tablename("elapp_shop_coupon") . " c on d.couponid = c.id";
            $sql .= " where d.openid=:openid and d.uniacid=:uniacid and  d.used=0 ";
            $sql .= " and (   (c.timelimit = 0 and ( c.timedays=0 or c.timedays*86400 + d.gettime >=unix_timestamp() ) )  or  (c.timelimit =1 and c.timestart<=" . $time . " && c.timeend>=" . $time . ")) order by d.gettime desc";
            $statics["coupon"] = pdo_fetchcolumn($sql, array(":openid" => $_W["openid"], ":uniacid" => $_W["uniacid"]));
            $pcset = $_W["shopset"]["coupon"];
            if (empty($pcset["closemember"])) {
                $hascoupon = true;
            }
            if (empty($pcset["closecenter"])) {
                $hascouponcenter = true;
            }
            if ($hascoupon) {
                $couponnum = com("coupon")->getCanGetCouponNum($_W["merchid"]);
            }
        }
        $hasglobonus = false;
        $plugin_globonus = p("globonus");
        if ($plugin_globonus) {
            $plugin_globonus_set = $plugin_globonus->getSet();
            $hasglobonus = !empty($plugin_globonus_set["open"]) && !empty($plugin_globonus_set["openmembercenter"]);
        }
        $haslive = false;
        $haslive = p("live");
        if ($haslive) {
            $live_set = $haslive->getSet();
            $haslive = $live_set["ismember"];
        }
        $hasThreen = false;
        $hasThreen = p("threen");
        if ($hasThreen) {
            $plugin_threen_set = $hasThreen->getSet();
            $hasThreen = !empty($plugin_threen_set["open"]) && !empty($plugin_threen_set["threencenter"]);
        }
        $hasauthor = false;
        $plugin_author = p("author");
        if ($plugin_author) {
            $plugin_author_set = $plugin_author->getSet();
            $hasauthor = !empty($plugin_author_set["open"]) && !empty($plugin_author_set["openmembercenter"]);
        }
        $hasabonus = false;
        $plugin_abonus = p("abonus");
        if ($plugin_abonus) {
            $plugin_abonus_set = $plugin_abonus->getSet();
            $hasabonus = !empty($plugin_abonus_set["open"]) && !empty($plugin_abonus_set["openmembercenter"]);
        }

        $hasuserpromote = false;
        $plugin_userpromote = p("userpromote");
        if ($plugin_userpromote) {
            $plugin_userpromote_set = $plugin_userpromote->getSet();
            $hasuserpromote = !empty($plugin_userpromote_set["open"]) && !empty($plugin_userpromote_set["openmembercenter"]);
        }

        $card = m("common")->getSysset("membercard");
        $actionset = m("common")->getSysset("memberCardActivation");
        $haveverifygoods = m("verifygoods")->checkhaveverifygoods($_W["openid"]);
        if (!empty($haveverifygoods)) {
            $verifygoods = m("verifygoods")->getCanUseVerifygoods($_W["openid"]);
        }
        $showcard = 0;
        if (!empty($card)) {
            $membercardid = $member["membercardid"];
            if (!empty($membercardid) && $card["card_id"] == $membercardid) {
                $cardtag = "查看微信会员卡信息";
                $showcard = 1;
            } else {
                if (!empty($actionset["centerget"])) {
                    $showcard = 1;
                    $cardtag = "领取微信会员卡";
                }
            }
        }
        $hasqa = false;
        $plugin_qa = p("qa");
        if ($plugin_qa) {
            $plugin_qa_set = $plugin_qa->getSet();
            if (!empty($plugin_qa_set["showmember"])) {
                $hasqa = true;
            }
        }
        #关于我们系统
        $hasabout = false;
        $plugin_about = p("about");
        if ($plugin_about) {
            $plugin_about_set = $plugin_about->getSet();
            if (!empty($plugin_about_set["showmember"])) {
                $hasabout = true;
            }
        }
        $hassign = false;
        $com_sign = p("sign");
        if ($com_sign) {
            $com_sign_set = $com_sign->getSet();
            if (!empty($com_sign_set["iscenter"]) && !empty($com_sign_set["isopen"])) {
                $hassign = (empty($_W["shopset"]["trade"]["credittext"]) ? "积分" : $_W["shopset"]["trade"]["credittext"]);
                $hassign .= (empty($com_sign_set["textsign"]) ? "签到" : $com_sign_set["textsign"]);
            }
        }
        $hasLineUp = false;
        $lineUp = p("lineup");
        if ($lineUp) {
            $lineUpSet = $lineUp->getSet();
            if (!empty($lineUpSet["isopen"]) && !empty($lineUpSet["mobile_show"])) {
                $hasLineUp = true;
            }
        }
        $wapset = m("common")->getSysset("wap");
        $appset = m("common")->getSysset("app");
        $needbind = false;
        if ((empty($member["mobileverify"]) || empty($member["mobile"])) && (empty($_W["shopset"]["app"]["isclose"]) && !empty($_W["shopset"]["app"]["openbind"]) || !empty($_W["shopset"]["wap"]["open"]) || $hasThreen)) {
            $needbind = true;
        }
        if (p("mmanage")) {
            $roleuser = pdo_fetch("SELECT id, uid, username, status FROM" . tablename("elapp_shop_perm_user") . "WHERE openid=:openid AND uniacid=:uniacid AND status=1 LIMIT 1", array(":openid" => $_W["openid"], ":uniacid" => $_W["uniacid"]));
        }
        $hasFullback = true;
        $ishidden = m("common")->getSysset("fullback");
        if ($ishidden["ishidden"] == true) {
            $hasFullback = false;
        }
        $hasdividend = false;
        $plugin_dividend = p("dividend");
        if ($plugin_dividend) {
            $plugin_dividend_set = $plugin_dividend->getSet();
            if (!empty($plugin_dividend_set["open"]) && !empty($plugin_dividend_set["membershow"])) {
                $hasdividend = true;
            }
        }
        #扶植分红
        $hasmentor = false;
        $plugin_mentor = p("mentor");
        if ($plugin_mentor) {
            $plugin_mentor_set = $plugin_mentor->getSet();
            if (!empty($plugin_mentor_set["open"]) && !empty($plugin_mentor_set["membershow"])) {
                $hasmentor = true;
            }
        }
        include($this->template());
    }


    /**
     * 个人实名认证
     * @return false|float|int|mixed|\Services_JSON_Error|string
     */
    function userVerify()
    {
        if ($this->request->isPost()) {
            $return = [];
            $code = 1;
            $is_user_verify = $is_user_sign = 0;
            $params = (new MemberValidate())->post()->goCheck('UserVerify');
            $user_info = array_diff_key($params, ['is_sign' => $params['is_sign'], 'i' => $params['i']]);
            $isUserVerifyResult = MemberLogic::isUserVerify($this->memberId, $user_info);
            if (!empty($isUserVerifyResult['is_verify'])) { //是否已经实名验证
                $is_user_verify = 1;
            } else {
                $result = (new Salary())->userVerify($user_info);
                $result = json_decode($result, true);
                if (200 == $result['code']) {
                    $return['message'] = '实名认证成功，接下来请签署协议';
                    $is_user_verify = 1;
                    MemberVerifyModel::update(['is_verify' => 1, 'verify_time' => time()], ['idcard' => $params['idcard'], 'realname' => $params['realname']]);
                } else {
                    $return['message'] = '实名认证失败：' . $result['msg'];
                }
            }
            if (1 == $is_user_verify) {
                //是否已经签署协议
                $isUserContractSignResult = MemberLogic::isUserContractSign($params['realname'], $params['idcard']);
                if (!empty($isUserContractSignResult)) {
                    if ('complete' == $isUserContractSignResult['status']) {
                        $is_user_sign = 1;
                        $code = 0;
                        $return['message'] = '您已经实名认证并签署协议，无需再认证';
                    } else if (in_array($isUserContractSignResult['status'], ['waiting', 'dealinq']) || null == $isUserContractSignResult['status']) {
                        $user_sign_result = MemberLogic::userContractDetail(['sn' => $isUserContractSignResult['sn']]);
                        $user_sign_result = json_decode($user_sign_result, true);
                        if ('complete' == $user_sign_result['data']['status']) {
                            $is_user_sign = 1;
                            $code = 0;
                            $return['message'] = '您已经实名认证并签署协议，无需再认证';
                        } else if(in_array($user_sign_result['data']['status'], ['waiting', 'dealinq'])) {
                            $return['message'] = '实名认证成功，请签署协议';
                            $return['sign_url'] = $isUserContractSignResult['sign_url'];
                        }
                    } else {
                        $is_user_sign = -1;
                        $return['message'] = '签署协议已被取消或签署失败';
                    }
                } else {
                    //获取平台协议签署
                    $params['member_id'] = $this->memberId;
                    $userContractSignResult = MemberLogic::userContractSign($params);
                    $userContractSignResult = json_decode($userContractSignResult, true);
                    if (200 == $userContractSignResult['code']) {
                        $return['sign_url'] = $userContractSignResult['data']['sign_url'];
                        $return['sn'] = $userContractSignResult['data']['sn'];
                        $return['message'] = '实名认证成功，请签署协议';
                    } else {
                        $return['message'] = $userContractSignResult['msg'];
                        if ('合同已签署，无需重复签署' == $userContractSignResult['msg']) {
                            $is_user_sign = 1;
                            $code = 0;
                        }
                    }
                }
            }
            $return['is_user_verify'] = $is_user_verify;
            $return['is_user_sign'] = $is_user_sign;
            return $this->succeed($return['message'], $return, $code, 1);
        }
    }

    /**
     * 个人协议签署
     * @param $params array('realname'=>'真实姓名','idcard'=>'身份证号','mobile'=>'手机号码')
     * @return false|float|int|mixed|\Services_JSON_Error|string
     */
    function userContractSign($params = [])
    {
        if ($this->request->isPost()) {
            $params = (new MemberValidate())->post()->goCheck('UserContractSign');
        }
        $params['member_id'] = $this->memberId;
        return MemberLogic::userContractSign($params);
    }

    /**
     * 个人协议签署状态查询
     * @return false|float|int|mixed|\Services_JSON_Error|string
     */
    function userContractDetail()
    {
        $params = (new MemberValidate())->post()->goCheck('UserContractDetail');
        return MemberLogic::userContractDetail(['sn' => $params['sn']]);
    }

    /**
     * 第三方接口回调更改签署状态
     * @return string
     */
    function userContractNotify()
    {
        $content = file_get_contents('php://input');
        Log::write("userContractNotify-content:\n" . $content, 'notice');
        if (empty($content)) return 'fail';
        $params = json_decode(json_decode($content, true)['data'], true);
        if (!empty($params['sn'])) {
            $result = MemberContractSignModel::update(['status' => $params['status'], 'file_url' => $params['file_url'], 'signed_at' => $params['signed_at']], ['sn' => $params['sn']]);
            if ($result) return 'success';
        }
        return 'fail';
    }

    function singleBankNotify()
    {
        $content = file_get_contents('php://input');
        Log::write("singleBankNotify-content:\n" . $content, 'notice');
        if (empty($content)) return 'fail';
        $result = json_decode(json_decode($content, true)['data'], true);
        if (!empty($result['sn'])) {
            $details = [
                'realname' => $result['realname'],
                'idcard' => $result['idcard'],
                'mobile' => $result['mobile'],
                'bank_account' => $result['bank_account'],
                'money' => $result['money'],
                'remark' => $result['remark'],
            ];
            $bill_info = array_diff_key($result, $details);
            $bill_info['details'] = json_encode($details);
            $bill_result = settleBillModel::update($bill_info, ['sn' => $result['sn']]);
            $refuse_result = SettleLogic::applyHandle(['apply_no' => $result['merchant_sn'], 'status' => 3, 'apply_type' => 3], $result['status']);
            if (0 == $refuse_result) return 'success';
        }
        return 'fail';
    }

    /**
     * 实名验证增查改
     * @return void
     * @params array $params('member_id'=>'会员id','realname'=>'真实姓名','idcard'=>'身份证号','mobile'=>'手机号码', 'idcard_front_pic'=>'身份证正面照片','idcard_back_pic'=>'身份证反面照片', 'is_verify'=>'是否验证')
     * @throws \think\db\exception\DataNotFoundException
     * @throws \think\db\exception\DbException
     * @throws \think\db\exception\ModelNotFoundException
     */
    function verify()
    {
        $params = $this->request->param();
        $memberVerifyModel = new MemberVerifyModel();
        if ($this->request->isGet()) {
            if (empty($params['id'])) {
                return $this->fail('请求参数错误');
            }
            $memberVerifyResult = $memberVerifyModel->where(['id' => $params['id']])->find();
            if (empty($memberVerifyResult)) {
                return $this->fail('请求数据不存在');
            }
            $memberVerifyResult = $memberVerifyResult->toArray();
            return $this->succeed('操作成功', $memberVerifyResult);
        }
        if ($this->request->isPost()) {
            $params = (new MemberValidate())->post()->goCheck('UserVerify');
            try {
                $params['member_id'] = $this->memberId;
                if ($params['is_verify'] == 1) $params['verify_time'] = time();
                if (!empty($params['id'])) {
                    $result = $memberVerifyModel->update($params, ['id' => $params['id']]);
                } else {
                    $result = $memberVerifyModel->create($params);
                }
                if ($result) {
                    return $this->succeed('操作成功', $result->toArray());
                } else {
                    return $this->fail('操作失败');
                }
            } catch (\Exception $e) {
                return $this->fail($e->getMessage());
            }
        }

    }
}