<?php
    require '../../../../extend/framework/bootstrap.inc.php';
    require '../../../../addons/elapp_shop/defines.php';
    //require '../../../../extend/framework/common/function.php';
    require __DIR__ . '/../../../../extend/framework/common/function.php';
    global $_W;
    $ordersn = $_GET['outtradeno'];
    $attachs = explode(':', $_GET['attach']);

    if (empty($attachs) || !is_array($attachs)){
        exit;
    }
    $uniacid = $attachs[0];
    $paytype = $attachs[1];

    $url = $_W['siteroot'] . '../../app.php/index/main?i=' . $uniacid;
    if (!empty($ordersn)) {

        if ($paytype == 0) {
            $url = $_W['siteroot'] . '../../app.php/order.pay/complete?i=' . $uniacid . '&ordersn=' . $ordersn .'&type=wechat';
        }
        elseif ($paytype == 1) {
            $url = $_W['siteroot'] . '../../app.php/member.recharge/wechat_complete?i=' . $uniacid . '&logno=' . $ordersn;
        }
        elseif ($paytype == 2) {
            $url = $_W['siteroot'] . '../../app.php/creditshop.detail/wechat_complete?i=' . $uniacid . '&logno=' . $ordersn;
        }
        elseif ($paytype == 3) {
            $url = $_W['siteroot'] . '../../app.php/creditshop.log/wechat_dispatch_complete?i=' . $uniacid . '&logno=' . $ordersn;
        }
        elseif ($paytype == 4) {
            $url = $_W['siteroot'] . '../../app.php/sale.coupon.my/main?i=' . $uniacid ;
        }
        elseif ($paytype == 5) {
            $url = $_W['siteroot'] . '../../app.php/groups.pay/complete?i=' . $uniacid . '&ordersn=' . $ordersn .'&type=wechat';
        }
        elseif ($paytype == 6) {
            $url = $_W['siteroot'] . '../../app.php/threen.register/complete?i=' . $uniacid . '&logno=' . $ordersn .'&type=wechat';
        }
    }

    header('location: ' . $url);
    exit();
