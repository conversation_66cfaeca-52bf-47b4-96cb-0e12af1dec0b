<?php
    require '../../../../extend/framework/bootstrap.inc.php';
    require '../../../../addons/elapp_shop/defines.php';
    require_once IA_ROOT . "/extend/framework/common/function.php";
    $ordersn = str($_GET['out_trade_no']);
    $attachs = explode(':', str($_GET['body']));
    if(empty($ordersn) && !empty($_GET['alipayresult'])){
        $alipayresult=json_decode($_GET['alipayresult'],true);
        $ordersn=$alipayresult['alipay_trade_app_pay_response']['out_trade_no'];
        $change_price=stripos($alipayresult['alipay_trade_app_pay_response']['out_trade_no'],'GJ');
        if($change_price){
            $ordersn=substr($alipayresult['alipay_trade_app_pay_response']['out_trade_no'],0,$change_price);
        }
        $prefix=substr($ordersn, 0, 2);
        if($prefix=='SH' || $prefix=='ME'){
            $order = pdo_fetch('select * from ' . tablename('elapp_shop_order') . ' where  ordersn=:ordersn limit 1', array(':ordersn' => $ordersn));
            if(!empty($order)){
                $url = mobileUrl('order/index/detail', ['id' => $order['id'], 'i' => $order['uniacid']],true);
            }
        }elseif ($prefix=='RC'){
            $log = pdo_fetch('SELECT * FROM ' . tablename('elapp_shop_member_log') . ' WHERE `logno`=:logno  limit 1', array(':logno' => $ordersn));
            if(!empty($log)){
                $url = mobileUrl('member/index/main', ['i' => $log['uniacid']],true);
            }
        }
        header('location: ' . $url);
        exit();
    }

    $get = json_encode($_GET);
    $get = base64_encode($get);

    if (empty($attachs) || !is_array($attachs)){
        exit;
    }
    $uniacid = intval($attachs[0]);
    $paytype = intval($attachs[1]);

    $url = mobileUrl('index/main', ['i' => $uniacid],true);
    if (!empty($ordersn)) {

        if (strexists($ordersn,'CS') && pdo_tableexists('elapp_shop_cashier_pay_log')){
            $cashier = pdo_fetch("SELECT * FROM ".tablename("elapp_shop_cashier_pay_log")." WHERE logno=:logno",array(":logno"=>$ordersn));
            if (!empty($cashier)){
                $uniacid = $cashier['uniacid'];
                $cashierid = $cashier['cashierid'];
                $paytype = 2;
            }
        }
        if ($paytype == 0) {
            $url = mobileUrl('order/pay_alipay/complete', ['i' => $uniacid, 'alidata' => $get],true);
        }elseif ($paytype == 1) {
            $url = mobileUrl('order/pay_alipay/recharge_complete', ['i' => $uniacid, 'alidata' => $get],true);
        }elseif ($paytype == 2) {
            $url = mobileUrl('cashier/pay/success', ['i' => $uniacid, 'cashierid' => $cashierid, 'orderid' => $ordersn],true);
        }elseif ($paytype == 6) {
            $url = mobileUrl('threen/register/threen_complete', ['i' => $uniacid, 'alidata' => $get, 'logno' => $ordersn],true);

        }

    }

    header('location: ' . $url);
    exit();

function str($str){
    $str = str_replace('"', '', $str);
    $str = str_replace("'", '', $str);
    $str = str_replace("=", '', $str);
    return $str;
}