<?php

defined("IN_IA") or exit("Access Denied");
define("ROOT_PATH", IA_ROOT . "/addons/elapp_customerservice/");
define("BEST_CHAT", "messikefu_chat");
define("BEST_CSERVICE", "messikefu_cservice");
define("BEST_CSERVICEGROUP", "messikefu_cservicegroup");
define("BEST_BIAOQIAN", "messikefu_biaoqian");
define("BEST_GROUP", "messikefu_group");
define("BEST_GROUPMEMBER", "messikefu_groupmember");
define("BEST_GROUPCONTENT", "messikefu_groupchat");
define("BEST_FANSKEFU", "messikefu_fanskefu");
define("BEST_ADV", "messikefu_adv");
define("BEST_SANFANSKEFU", "messikefu_sanfanskefu");
define("BEST_SANCHAT", "messikefu_sanchat");
define("BEST_KEFUANDGROUP", "messikefu_kefuandgroup");
define("BEST_PINGJIA", "messikefu_pingjia");
define("BEST_WENZHANG", "messikefu_wenzhang");
define("BEST_KEFUANDCJWT", "messikefu_kefuandcjwt");
define("BEST_ZIDONGHUIFU", "messikefu_zdhf");
define("BEST_XCX", "messikefu_xcx");
define("BEST_XCXCSERVICE", "messikefu_xcxcservice");
define("BEST_XCXFANSKEFU", "messikefu_xcxfanskefu");
define("BEST_XCXCHAT", "messikefu_xcxchat");
define("BEST_XCXAUTO", "messikefu_xcxauto");
define("BEST_FROMCK", "messikefu_fromck");
class Elapp_customerserviceModuleProcessor extends WeModuleProcessor
{
	public function respond()
	{
		global $_W, $_G;
		include_once ROOT_PATH . "emoji/emoji.php";
		$type = $this->message["msgtype"];
		$content = $this->message["content"];
		$kefulastcon = preg_replace("/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/", "[无法识别字符]", $content);
		$openid = $this->message["from"];
		$keyword = pdo_fetch("SELECT * FROM " . tablename("rule_keyword") . " WHERE uniacid = {$_W["uniacid"]} AND status = 1 AND content = '{$content}' AND type = 1");
		$keyword2 = pdo_fetch("SELECT * FROM " . tablename("rule_keyword") . " WHERE uniacid = {$_W["uniacid"]} AND status = 1 AND content like '%{$content}%' AND type = 2");
		if (empty($keyword) && empty($keyword2) && $openid != '' && ($type == "text" || $type == "image" || $type == "voice")) {
			$nowhour = intval(date("H", TIMESTAMP));
			$nowhouradd = $nowhour + 1;
			$condition = "weid = {$_W["uniacid"]} AND content != '{$openid}' AND ctype = 1 AND cangzh = 1 AND ((iszx = 0 AND (\r\n\t\t\t\t(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR \r\n\t\t\t\t(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))\r\n\t\t\t)";
			$zhouji = date("w");
			if ($zhouji == "1") {
				$condition .= " AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "2") {
				$condition .= " AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "3") {
				$condition .= " AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "4") {
				$condition .= " AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "5") {
				$condition .= " AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "6") {
				$condition .= " AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			if ($zhouji == "0") {
				$condition .= " AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0))";
			}
			$condition .= " OR (iszx = 1 AND isrealzx = 1))";
			$orderby = " ORDER BY nowjdnum ASC";
			$cservicelist = pdo_fetchall("SELECT * FROM " . tablename(BEST_CSERVICE) . " WHERE " . $condition);
			$kefuopenids = "(";
			foreach ($cservicelist as $k => $v) {
				$kefuopenids .= "'" . $v["content"] . "',";
			}
			$kefuopenids .= "'ddd')";
			$hasjd = pdo_fetch("SELECT kefuopenid FROM " . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W["uniacid"]} AND fansopenid = '{$openid}' AND kefuopenid in " . $kefuopenids . " AND nowjd > 0");
			if (!empty($hasjd)) {
				$cservice = pdo_fetch("SELECT * FROM " . tablename(BEST_CSERVICE) . " WHERE weid = {$_W["uniacid"]} AND content = '{$hasjd["kefuopenid"]}'");
			} else {
				$cservice = pdo_fetchall("SELECT * FROM " . tablename(BEST_CSERVICE) . " WHERE " . $condition . $orderby . " LIMIT 1");
				$cservice = $cservice[0];
			}
			if (!empty($cservice)) {
				$hasfanskefu = pdo_fetch("SELECT * FROM " . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W["uniacid"]} AND fansopenid = '{$openid}' AND kefuopenid = '{$cservice["content"]}'");
				if (empty($hasfanskefu)) {
					$datafanskefu["weid"] = $_W["uniacid"];
					$datafanskefu["fansopenid"] = $openid;
					$datafanskefu["kefuopenid"] = $cservice["content"];
					$account_api = WeAccount::create();
					$info = $account_api->fansQueryInfo($_W["fans"]["from_user"]);
					$datafanskefu["fansavatar"] = $info["headimgurl"];
					$datafanskefu["fansnickname"] = str_replace("'", "''", $info["nickname"]);
					$datafanskefu["kefuavatar"] = tomedia($cservice["thumb"]);
					$datafanskefu["kefunickname"] = $cservice["name"];
					pdo_insert(BEST_FANSKEFU, $datafanskefu);
					$hasfanskefu = pdo_fetch("SELECT * FROM " . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W["uniacid"]} AND fansopenid = '{$openid}' AND kefuopenid = '{$cservice["content"]}'");
				}
				if ($cservice["iszx"] == 1) {
					if ($cservice["isrealzx"] == 0) {
						$notonlinemsg = !empty($cservice["notonline"]) ? $cservice["notonline"] : "客服不在线哦！";
						return $this->respText($notonlinemsg);
					}
				} elseif ($cservice["lingjie"] == 1) {
					$nowhour = intval(date("H", TIMESTAMP));
					if ($nowhour + 1 > $cservice["endhour"] && $nowhour < $cservice["starthour"]) {
						$notonlinemsg = !empty($cservice["notonline"]) ? $cservice["notonline"] : "客服不在线哦！";
						return $this->respText($notonlinemsg);
					}
				} else {
					$nowhour = intval(date("H", TIMESTAMP));
					if ($nowhour < $cservice["starthour"] || $nowhour + 1 > $cservice["endhour"]) {
						$notonlinemsg = !empty($cservice["notonline"]) ? $cservice["notonline"] : "客服不在线哦！";
						return $this->respText($notonlinemsg);
					}
				}
				if ($cservice["isxingqi"] == 1) {
					$notonlinemsg = !empty($cservice["notonline"]) ? $cservice["notonline"] : "客服不在线哦！";
					$zhouji = date("w");
					if ($zhouji == "1") {
						if ($cservice["day1"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "2") {
						if ($cservice["day2"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "3") {
						if ($cservice["day3"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "4") {
						if ($cservice["day4"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "5") {
						if ($cservice["day5"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "6") {
						if ($cservice["day6"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} elseif ($zhouji == "0") {
						if ($cservice["day7"] == 0) {
							return $this->respText($notonlinemsg);
						}
					} else {
						return $this->respText($notonlinemsg);
					}
				}
				if ($type == "text") {
					$data["type"] = 1;
					$content = emoji_docomo_to_unified($content);
					$content = emoji_unified_to_html($content);
					$content = $this->guolv($content);
					$data["content"] = $content;
					if (strpos($data["content"], "span class=")) {
						$tplcon = $data["nickname"] . "给您发送了表情";
					} else {
						$tplcon = $data["content"];
					}
				}
				if ($type == "image") {
					$data["type"] = 4;
					$media_id = $this->message["mediaid"];
					$data["content"] = $this->getmedia($media_id);
					$tplcon = $data["nickname"] . "给您发送了图片";
				}
				if ($type == "voice") {
					$data["type"] = 6;
					$media_id = $this->message["mediaid"];
					$data["content"] = $media_id;
					$tplcon = $data["nickname"] . "给您发送了语音";
				}
				$data["openid"] = $openid;
				$data["nickname"] = $hasfanskefu["fansnickname"];
				$data["avatar"] = $hasfanskefu["fansavatar"];
				$data["toopenid"] = $cservice["content"];
				$data["time"] = $this->message["time"];
				$data["weid"] = $_W["uniacid"];
				$data["fkid"] = $hasfanskefu["id"];
				$data["isck"] = 1;
				$datack["weid"] = $_W["uniacid"];
				$datack["msgid"] = $this->message["msgid"];
				pdo_insert(BEST_FROMCK, $datack);
				$ckid = pdo_insertid();
				if (!empty($ckid)) {
					pdo_insert(BEST_CHAT, $data);
				} else {
					echo "success";
					exit;
				}
				if ($type == "text") {
					$zdhf = pdo_fetch("select * from " . tablename(BEST_ZIDONGHUIFU) . " where weid = {$_W["uniacid"]} AND ((title = '{$content}' AND type = 1) OR (INSTR('{$content}',title) AND type = 2)) ORDER BY paixu DESC");
					if (!empty($zdhf)) {
						$zdhf_kefuids = unserialize($zdhf["kefuids"]);
						if ($zdhf["kefuids"] == '' || in_array($cservice["id"], $zdhf_kefuids)) {
							if ($zdhf["hftype"] == 0) {
								$datajqr["content"] = $zdhf["content"];
							}
							if ($zdhf["hftype"] == 2) {
								$datajqr["content"] = tomedia($zdhf["imgcon"]);
							}
							if ($zdhf["hftype"] == 3) {
								$datajqr["content"] = $zdhf["allcon"];
							}
							$datajqr["weid"] = $_W["uniacid"];
							$datajqr["fkid"] = $hasfanskefu["id"];
							$datajqr["openid"] = $cservice["content"];
							$datajqr["toopenid"] = $data["openid"];
							$datajqr["time"] = $data["time"] + 1;
							$datajqr["nickname"] = $cservice["name"];
							$datajqr["avatar"] = tomedia($cservice["thumb"]);
							$datajqr["type"] = $zdhf["hftype"] == 0 || $zdhf["hftype"] == 3 ? 2 : 4;
							$datajqr["isjqr"] = 1;
							pdo_insert(BEST_CHAT, $datajqr);
							if ($zdhf["hftype"] == 0) {
								$datajqr["content"] = htmlspecialchars_decode($datajqr["content"]);
								$datajqr["content"] = addslashes($datajqr["content"]);
								$datajqr["content"] = urlencode($datajqr["content"]);
								$custom = array("msgtype" => "text", "text" => array("content" => $datajqr["content"]), "touser" => $data["openid"]);
								$account_api = WeAccount::create();
								$account_api->sendCustomNotice($custom);
							}
							if ($zdhf["hftype"] == 2) {
								$fileName = time() . ".jpg";
								$source = file_get_contents($datajqr["content"]);
								file_put_contents(ROOT_PATH . $fileName, $source);
								$account_api = WeAccount::create();
								$access_token = $account_api->getAccessToken();
								$imgurl = "http://api.weixin.qq.com/cgi-bin/media/upload?access_token=" . $access_token . "&type=image";
								$imgres = $this->curl_post2($imgurl, ROOT_PATH . $fileName);
								unlink(ROOT_PATH . $fileName);
								$custom = array("touser" => $data["openid"], "msgtype" => "image", "image" => array("media_id" => $imgres["media_id"]));
								$account_api->sendCustomNotice($custom);
							}
						}
					}
				}
				if ($hasfanskefu["nowjd"] == 0) {
					$datafkup["nowjd"] = 1;
					$datafkup["jdtime"] = TIMESTAMP;
					$otherfanskefus = pdo_fetchall("SELECT id,kefuopenid FROM " . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W["uniacid"]} AND nowjd > 0 AND fansopenid = '{$openid}' AND kefuopenid != '{$cservice["content"]}'");
					foreach ($otherfanskefus as $k => $v) {
						$dataotherfk["nowjd"] = 0;
						$dataotherfk["jdtime"] = 0;
						pdo_update(BEST_FANSKEFU, $dataotherfk, array("id" => $v["id"]));
					}
					$this->updatenowjdnum();
				}
				$guotime = TIMESTAMP - $hasfanskefu["lasttime"];
				if ($this->module["config"]["istplon"] == 1 && $guotime > $this->module["config"]["kefutplminute"]) {
					$tzurl = $this->gettpldomain() . "app/" . str_replace("./", '', $this->createMobileUrl("servicechat", array("toopenid" => $data["openid"])));
					if (!empty($this->module["config"]["tpl_kefu"])) {
						$postdata = array("keyword1" => array("value" => $data["nickname"], "color" => "#ff510"), "keyword2" => array("value" => date("Y-m-d H:i:s", TIMESTAMP), "color" => "#ff510"), "remark" => array("value" => $tplcon, "color" => "#0000CD"));
						$account_api = WeAccount::create();
						$account_api->sendTplNotice($data["toopenid"], $this->module["config"]["tpl_kefu"], $postdata, $tzurl, "#980000");
					} else {
						$concon = $data["nickname"] . "发起了咨询！" . $tplcon . "。";
						$row = array();
						$row["title"] = urlencode("新消息提醒");
						$row["description"] = urlencode($concon);
						$row["picurl"] = $_W["siteroot"] . "/addons/elapp_customerservice/static/tuwen.jpg";
						$row["url"] = $tzurl;
						$news[] = $row;
						$send["touser"] = $data["toopenid"];
						$send["msgtype"] = "news";
						$send["news"]["articles"] = $news;
						$account_api = WeAccount::create();
						$account_api->sendCustomNotice($send);
					}
				}
				$datafkup["notread"] = $hasfanskefu["notread"] + 1;
				$datafkup["guanlinum"] = $hasfanskefu["guanlinum"] + 1;
				$datafkup["fansdel"] = 0;
				$datafkup["kefudel"] = 0;
				$datafkup["lastcon"] = $kefulastcon;
				$datafkup["msgtype"] = $data["type"];
				$datafkup["lasttime"] = $this->message["time"];
				$datafkup["wherefrom"] = 1;
				pdo_update(BEST_FANSKEFU, $datafkup, array("id" => $data["fkid"]));
				$post_url = "https://api.qiumipai.com:2121/?type=newpublish&to=" . $hasfanskefu["kefuopenid"] . "&notread=" . $datafkup["notread"] . "&newavatar=" . $hasfanskefu["fansavatar"] . "&content=" . $data["content"] . "&msgtype=" . $datafkup["msgtype"] . "&toopenid=" . $hasfanskefu["fansopenid"] . "&fkid=" . $hasfanskefu["id"] . "&newnickname=" . $hasfanskefu["fansnickname"];
				load()->func("communication");
				ihttp_request($post_url);
			}
		}
	}
	public function updatenowjdnum()
	{
		global $_W, $_GPC;
		$cservicelist = pdo_fetchall("SELECT id,content FROM " . tablename(BEST_CSERVICE) . " WHERE weid = {$_W["uniacid"]} AND ctype = 1");
		foreach ($cservicelist as $k => $v) {
			$nowjdnum = pdo_fetchcolumn("SELECT COUNT(*) FROM " . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W["uniacid"]} AND nowjd > 0 AND kefuopenid = '{$v["content"]}'");
			if (!empty($nowjdnum)) {
				$nowjdnum = $nowjdnum < 0 ? 0 : $nowjdnum;
				$data["nowjdnum"] = $nowjdnum;
				pdo_update(BEST_CSERVICE, $data, array("id" => $v["id"]));
			}
		}
	}
	public function gettpldomain()
	{
		global $_W, $_GPC;
		return $this->module["config"]["tpldomain"] != '' ? $this->module["config"]["tpldomain"] : $_W["siteroot"];
	}
	public function guolv($content)
	{
		if (!empty($this->module["config"]["mingan"])) {
			$sensitivewordarr = explode("|", $this->module["config"]["mingan"]);
			foreach ($sensitivewordarr as $k => $v) {
				if (!empty($v)) {
					$content = str_replace($v, "***", $content);
				}
			}
		}
		$content = str_replace("\n", "<br>", $content);
		return $content;
	}
	public function curl_post2($url = '', $path = '')
	{
		$curl = curl_init();
		if (class_exists("CURLFile")) {
			curl_setopt($curl, CURLOPT_SAFE_UPLOAD, true);
			$data = array("media" => new CURLFile($path));
		} else {
			curl_setopt($curl, CURLOPT_SAFE_UPLOAD, false);
			$data = array("media" => "@" . $path);
		}
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_USERAGENT, "TEST");
		$result = curl_exec($curl);
		$res = json_decode($result, true);
		return $res;
	}
	public function getmedia($media_id)
	{
		global $_W, $_GPC;
		load()->func("communication");
		$account_api = WeAccount::create();
		$access_token = $account_api->getAccessToken();
		$url = "https://file.api.weixin.qq.com/cgi-bin/media/get?access_token=" . $access_token . "&media_id=" . $media_id;
		$response = ihttp_get($url);
		if (is_error($response)) {
			return "访问公众平台接口失败, 错误: {$response["message"]}";
			exit;
		}
		$result = @json_decode($response["content"], true);
		if (!empty($result["errcode"])) {
			return "访问微信接口错误, 错误代码: {$result["errcode"]}, 错误信息: {$result["errmsg"]}";
			exit;
		}
		$updir = IA_ROOT . "/public/attachment/images/" . $_W["uniacid"] . "/" . date("Y", time()) . "/" . date("m", time()) . "/";
		if (!file_exists($updir)) {
			mkdir($updir, 0777, true);
		}
		$randimgurl = "images/" . $_W["uniacid"] . "/" . date("Y", time()) . "/" . date("m", time()) . "/" . date("YmdHis") . rand(1000, 99999) . ".jpg";
		$targetName = IA_ROOT . "/public/attachment/" . $randimgurl;
		$fp = @fopen($targetName, "wb");
		@fwrite($fp, $response["content"]);
		@fclose($fp);
		if (file_exists($targetName)) {
			$this->mkThumbnail($targetName, 640, null, $targetName);
			if ($this->module["config"]["isqiniu"] == 1) {
				$remotestatus = $this->doQiuniu($randimgurl, true);
				if (is_error($remotestatus)) {
					return "远程附件上传失败，请检查配置并重新上传";
					exit;
				} else {
					return $this->module["config"]["qiniuurl"] . "/" . $randimgurl;
				}
			} elseif ($this->module["config"]["isqiniu"] == 3) {
				if (!empty($_W["setting"]["remote"]["type"])) {
					load()->func("file");
					$remotestatus = file_remote_upload($randimgurl);
					if (is_error($remotestatus)) {
						return "远程附件上传失败，请检查配置并重新上传";
						exit;
					} else {
						return tomedia($randimgurl);
					}
				}
			} else {
				return tomedia($randimgurl);
			}
		} else {
			return "上传失败";
		}
	}
	public function doQiuniu($filename, $auto_delete_local = true)
	{
		global $_W;
		$qiniuaccesskey = $this->module["config"]["isqiniu"] == 1 ? $this->module["config"]["qiniuaccesskey"] : $_W["setting"]["remote"]["qiniu"]["accesskey"];
		$qiniusecretkey = $this->module["config"]["isqiniu"] == 1 ? $this->module["config"]["qiniusecretkey"] : $_W["setting"]["remote"]["qiniu"]["secretkey"];
		$qiniubucket = $this->module["config"]["isqiniu"] == 1 ? $this->module["config"]["qiniubucket"] : $_W["setting"]["remote"]["qiniu"]["bucket"];
		load()->func("file");
		require_once IA_ROOT . "/extend/framework/library/qiniu/autoload.php";
		$auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey);
		$config = new Qiniu\Config();
		$uploadmgr = new Qiniu\Storage\UploadManager($config);
		$putpolicy = Qiniu\base64_urlSafeEncode(json_encode(array("scope" => $qiniubucket . ":" . $filename)));
		$uploadtoken = $auth->uploadToken($qiniubucket, $filename, 3600, $putpolicy);
		list($ret, $err) = $uploadmgr->putFile($uploadtoken, $filename, ATTACHMENT_ROOT . "/" . $filename);
		if ($auto_delete_local) {
			file_delete($filename);
		}
		if ($err !== null) {
			$resarr["error"] = 1;
			$resarr["message"] = "远程附件上传失败，请检查配置并重新上传";
			die(json_encode($resarr));
		} else {
			return true;
		}
	}
	public function mkThumbnail($src, $width = null, $height = null, $filename = null)
	{
		if (!isset($width) && !isset($height)) {
			return false;
		}
		if (isset($width) && $width <= 0) {
			return false;
		}
		if (isset($height) && $height <= 0) {
			return false;
		}
		$size = getimagesize($src);
		if (!$size) {
			return false;
		}
		list($src_w, $src_h, $src_type) = $size;
		$src_mime = $size["mime"];
		switch ($src_type) {
			case 1:
				$img_type = "gif";
				break;
			case 2:
				$img_type = "jpeg";
				break;
			case 3:
				$img_type = "png";
				break;
			case 15:
				$img_type = "wbmp";
				break;
			default:
				return false;
		}
		if (!isset($width)) {
			$width = $src_w * ($height / $src_h);
		}
		if (!isset($height)) {
			$height = $src_h * ($width / $src_w);
		}
		$imagecreatefunc = "imagecreatefrom" . $img_type;
		$src_img = $imagecreatefunc($src);
		$dest_img = imagecreatetruecolor($width, $height);
		imagecopyresampled($dest_img, $src_img, 0, 0, 0, 0, $width, $height, $src_w, $src_h);
		$imagefunc = "image" . $img_type;
		if ($filename) {
			$imagefunc($dest_img, $filename);
		} else {
			header("Content-Type: " . $src_mime);
			$imagefunc($dest_img);
		}
		imagedestroy($src_img);
		imagedestroy($dest_img);
		return true;
	}
}