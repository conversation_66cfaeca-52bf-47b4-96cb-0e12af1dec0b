<?php
global $_W, $_GPC;
$cservicelist = pdo_fetchall("SELECT id,content,name,thumb FROM ".tablename(BEST_CSERVICE)." WHERE weid = {$_W['uniacid']} AND ctype = 1 ORDER BY displayorder ASC");
$operation = empty($_GPC['op']) ? 'display' : $_GPC['op'];
if($operation == 'display'){
	$beginToday=mktime(0,0,0,date('m'),date('d'),date('Y'));
	$endToday=mktime(0,0,0,date('m'),date('d')+1,date('Y'))-1;
	
	
	$sdefaultDate = date("Y-m-d");
	$first = 1;
	$w = date('w',strtotime($sdefaultDate));
	$week_start=date('Y-m-d',strtotime("$sdefaultDate -".($w ? $w - $first : 6).' days'));
	
	$beginThisweek = strtotime("$sdefaultDate -".($w ? $w - $first : 6).' days');
	$endThisweek = strtotime("$week_start +6 days");
	
	$beginThismonth=mktime(0,0,0,date('m'),1,date('Y'));
	$endThismonth=mktime(23,59,59,date('m'),date('t'),date('Y'));
	
	
	$nowhour = intval(date("H",TIMESTAMP));
	$nowhouradd = $nowhour+1;
	$condition = "weid = {$_W['uniacid']} AND ((iszx = 0 AND (
		(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR 
		(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))
	)";
	$zhouji = date("w");
	if($zhouji == "1" ){
		$condition .= " AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "2" ){
		$condition .= " AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "3" ){
		$condition .= " AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "4" ){
		$condition .= " AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "5" ){
		$condition .= " AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "6" ){
		$condition .= " AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	if($zhouji == "0" ){
		$condition .= " AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0))";
	}
	$condition .= " OR (iszx = 1 AND isrealzx = 1))";
	
	foreach($cservicelist as $k=>$v){
		$iszxcservice = pdo_fetch("SELECT id FROM ".tablename(BEST_CSERVICE)." WHERE ".$condition." AND id = {$v['id']}");
		$cservicelist[$k]['online'] = empty($iszxcservice) ? 0 : 1;
		
		$toadyjd = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginToday} AND time < {$endToday} AND openid = '{$v['content']}' GROUP BY fkid");
		$cservicelist[$k]['todayjdnum'] = count($toadyjd);
		
		$weekjd = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginThisweek} AND time < {$endThisweek} AND openid = '{$v['content']}' GROUP BY fkid");
		$cservicelist[$k]['weekjdnum'] = count($weekjd);
		
		$monthjd = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginThismonth} AND time < {$endThismonth} AND openid = '{$v['content']}' GROUP BY fkid");
		$cservicelist[$k]['monthjdnum'] = count($monthjd);
		
		
		$toadyhf = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginToday} AND time < {$endToday} AND openid = '{$v['content']}'");
		$cservicelist[$k]['todayhfnum'] = count($toadyhf);
		
		$weekhf = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginThisweek} AND time < {$endThisweek} AND openid = '{$v['content']}'");
		$cservicelist[$k]['weekhfnum'] = count($weekhf);
		
		$monthhf = pdo_fetchall("SELECT id FROM ".tablename(BEST_CHAT)." WHERE weid = {$_W['uniacid']} AND time > {$beginThismonth} AND time < {$endThismonth} AND openid = '{$v['content']}'");
		$cservicelist[$k]['monthhfnum'] = count($monthhf);
	}
}
include $this->template('web/tongji');
?>