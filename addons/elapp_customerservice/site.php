<?php
use framework\classes\account\WeModuleSite;
defined('IN_IA') or exit('Access Denied');
define('ROOT_PATH', IA_ROOT . '/addons/elapp_customerservice/');
define('MD_ROOT', '../addons/elapp_customerservice/');
define('EC_STATIC_ROOT', '../addons/elapp_customerservice/static');
define('EC_STATIC_ROOT_P', '../addons/elapp_customerservice_plugin_p/static');
define('NEWSTATIC_ROOT', '../addons/elapp_customerservice/newstatic');
define('BEST_CHAT', 'messikefu_chat');
define('BEST_CSERVICE', 'messikefu_cservice');
define('BEST_CSERVICEGROUP', 'messikefu_cservicegroup');
define('BEST_BIAOQIAN', 'messikefu_biaoqian');
define('BEST_GROUP', 'messikefu_group');
define('BEST_DOMAIN', 'http://127.0.0.1/domain.php');
define('BEST_GROUPMEMBER', 'messikefu_groupmember');
define('BEST_GROUPCONTENT', 'messikefu_groupchat');
define('BEST_FANSKEFU', 'messikefu_fanskefu');
define('BEST_ADV', 'messikefu_adv');
define('BEST_SANFANSKEFU', 'messikefu_sanfanskefu');
define('BEST_SANCHAT', 'messikefu_sanchat');
define('BEST_KEFUANDGROUP', 'messikefu_kefuandgroup');
define('BEST_PINGJIA', 'messikefu_pingjia');
define('BEST_WENZHANG', 'messikefu_wenzhang');
define('BEST_KEFUANDCJWT', 'messikefu_kefuandcjwt');
define('BEST_ZIDONGHUIFU', 'messikefu_zdhf');
define('BEST_XCX', 'messikefu_xcx');
define('BEST_XCXCSERVICE', 'messikefu_xcxcservice');
define('BEST_XCXFANSKEFU', 'messikefu_xcxfanskefu');
define('BEST_XCXCHAT', 'messikefu_xcxchat');
define('BEST_TISHI', '1、小程序客服功能须授权才能使用，请联系负责人处理！！！2、请选择任意公众号后台操作小程序客服功能！！！');
define('BEST_XCXAUTO', 'messikefu_xcxauto');
define('BEST_ZHUIZONG', 'messikefu_zhuizong');
define('BEST_GROUPVOICEDU', 'messikefu_groupvoicedu');
define('BEST_KUAIJIE', 'messikefu_kuaijie');
class Elapp_customerserviceModuleSite extends WeModuleSite
{
	public function __construct()
	{
	}
	public function doMobileShenqingqun()
	{
		global $_W, $_GPC;
		if (empty($_W['fans']['from_user'])) {
			$message = '请在微信浏览器中打开！';
			include $this->template('error');
			exit;
		}
		$groupid = intval($_GPC['groupid']);
		if (empty($groupid)) {
			$message = '参数传输错误！';
			include $this->template('error');
			exit;
		}
		$hasgroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$groupid}");
		if (empty($hasgroup)) {
			$message = '不存在该群聊！';
			include $this->template('error');
			exit;
		}
		if ($hasgroup['maxnum'] != 0) {
			$ingroupnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid}");
			if ($ingroupnum >= $hasgroup['maxnum']) {
				$message = '该群聊人数已满！';
				include $this->template('error');
				exit;
			}
		}
		$hasshenqing = pdo_fetch('SELECT id FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND groupid = {$groupid}");
		if (!empty($hasshenqing)) {
			$message = '您已申请加入该群聊！';
			include $this->template('error');
			exit;
		}
		$iscservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND ctype = 1 AND content = '{$_W['fans']['from_user']}'");
		if (!empty($iscservice)) {
			$data['nickname'] = $iscservice['name'];
			$data['avatar'] = tomedia($iscservice['thumb']);
			$data['type'] = 2;
		} else {
			$account_api = WeAccount::create();
			$info = $account_api->fansQueryInfo($_W['fans']['from_user']);
			if ($info['subscribe'] == 1) {
				$data['avatar'] = $info['headimgurl'];
				$data['nickname'] = $info['nickname'];
			} else {
				$fan = mc_oauth_userinfo();
				$data['avatar'] = $fan['headimgurl'];
				$data['nickname'] = $fan['nickname'];
			}
			$data['type'] = 1;
		}
		$data['groupid'] = $groupid;
		$data['weid'] = $_W['uniacid'];
		$data['openid'] = $_W['fans']['from_user'];
		if ($hasgroup['isshenhe'] == 1) {
			$data['status'] = 1;
		}
		if ($hasgroup['autotx'] == 1) {
			$data['txkaiguan'] = 1;
		}
		pdo_insert(BEST_GROUPMEMBER, $data);
		if ($hasgroup['isshenhe'] == 0) {
			$senddata = array("openid" => $hasgroup['admin'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('guanligroup', array("groupid" => $groupid))), "first" => $data['nickname'] . '申请加入' . $hasgroup['groupname'] . '！', "keyword1" => $data['nickname']);
			$this->sendtplmsg($senddata);
		}
		$message = '提交申请成功！';
		include $this->template('shenqingqun');
	}
	public function sendtplmsg($senddata)
	{
		global $_GPC, $_W;
		if ($this->module['config']['istplon'] == 1) {
			if ($this->module['config']['tpl_kefu'] != '' && $senddata['wherefrom'] != 1) {
				$postdata = array("keyword1" => array("value" => $senddata['keyword1'], "color" => "#ff510"), "keyword2" => array("value" => date('Y-m-d H:i:s', TIMESTAMP), "color" => "#ff510"), "remark" => array("value" => $senddata['first'], "color" => "#0000CD"));
				$account_api = WeAccount::create();
				$account_api->sendTplNotice($senddata['openid'], $this->module['config']['tpl_kefu'], $postdata, $senddata['url'], '#980000');
			} else {
				$row = array();
				$row['title'] = urlencode('新消息提醒');
				$row['description'] = urlencode($senddata['first']);
				$row['picurl'] = $_W['siteroot'] . '/static/application/elapp_customerservice/static/tuwen.jpg';
				$row['url'] = $senddata['url'];
				$news[] = $row;
				$send['touser'] = $senddata['openid'];
				$send['msgtype'] = 'news';
				$send['news']['articles'] = $news;
				$account_api = WeAccount::create();
				$account_api->sendCustomNotice($send);
			}
		}
	}
	public function guolv($content)
	{
		if (!empty($this->module['config']['mingan'])) {
			$sensitivewordarr = explode('|', $this->module['config']['mingan']);
			foreach ($sensitivewordarr as $k => $v) {
				if (!empty($v)) {
					$content = str_replace($v, '***', $content);
				}
			}
		}
		$content = nl2br($content);
		return $content;
	}
	public function doMobileQdadmin()
	{
		include_once ROOT_PATH . 'inc/mobile/qdadmin.php';
	}
	public function doMobileXcxqdadmin()
	{
		include_once ROOT_PATH . 'inc/mobile/xcxqdadmin.php';
	}
	public function doMobileGroupcenter()
	{
		global $_GPC, $_W;
		$op = trim($_GPC['op']);
		if ($op == 'search') {
			if (empty($_W['fans']['from_user'])) {
				$resarr['error'] = 1;
				$resarr['msg'] = '请在微信浏览器中打开！';
				echo json_encode($resarr);
				exit;
			}
			$qunname = trim($_GPC['qunname']);
			if (empty($qunname)) {
				$resarr['error'] = 1;
				$resarr['msg'] = '请输入群名称查询！';
				echo json_encode($resarr);
				exit;
			}
			$group = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE groupname like '%{$qunname}%' AND weid = {$_W['uniacid']}");
			if (empty($group)) {
				$resarr['error'] = 1;
				$resarr['msg'] = '没有这个群聊！';
				echo json_encode($resarr);
				exit;
			} else {
				if ($group['admin'] == $_W['fans']['from_user']) {
					$groupbtn = '<div class="buttons"><a href="' . $this->createMobileUrl('guanligroup', array("groupid" => $group['id'])) . '">管理群</a></div>';
				} else {
					$groupbtn = '';
				}
				$resarr['error'] = 0;
				$resarr['html'] = '<div class="item flex">
										<img src="' . tomedia($group['thumb']) . '">
										<a href="' . $this->createMobileUrl('groupchatdetail', array("groupid" => $group['id'])) . '" style="flex:1;color:#666;">
											<div class="text textellipsis1">' . $group['groupname'] . '</div>
										</a>
										' . $groupbtn . '
									</div>';
				echo json_encode($resarr);
				exit;
			}
		} else {
			if (empty($_W['fans']['from_user'])) {
				$message = '请在微信浏览器中打开！';
				include $this->template('error');
				exit;
			}
			$iscservice = pdo_fetch('SELECT id FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$_W['fans']['from_user']}' AND ctype = 1");
			if ($this->module['config']['groupshow'] == 0) {
				$grouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} ORDER BY time DESC");
			} else {
				$groupmember = pdo_fetchall('SELECT groupid FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}'");
				$groupidarr = array();
				foreach ($groupmember as $k => $v) {
					$groupidarr[] = $v['groupid'];
				}
				$grouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} AND id in (" . implode(',', $groupidarr) . ') ORDER BY time DESC');
			}
			$this->module['config']['shareurl'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('groupcenter'));
			include $this->template('groupcenter');
		}
	}
	public function doMobileGuanligroup()
	{
		global $_GPC, $_W;
		if (empty($_W['fans']['from_user'])) {
			$message = '请在微信浏览器中打开！';
			include $this->template('error');
			exit;
		}
		$groupid = intval($_GPC['groupid']);
		$isgroupadmin = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$groupid} AND admin = '{$_W['fans']['from_user']}'");
		if (empty($isgroupadmin)) {
			$message = '你不是管理员，不能管理该群聊！';
			include $this->template('error');
			exit;
		}
		$op = trim($_GPC['op']);
		if ($op == '') {
			$groupmemberlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND openid != '{$_W['fans']['from_user']}' AND isdel = 0 ORDER BY status ASC,id DESC");
			include $this->template('guanligroup');
		} elseif ($op == 'del') {
			$groupid = intval($_GPC['groupid']);
			$id = intval($_GPC['memberid']);
			$groupmember = pdo_fetch('SELECT openid FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE id = {$id} AND isdel = 0");
			if (empty($groupmember)) {
				$resarr['error'] = 1;
				$resarr['msg'] = '不存在该用户记录！';
				echo json_encode($resarr);
				exit;
			}
			pdo_update(BEST_GROUPMEMBER, array("isdel" => 1), array("groupid" => $groupid, "openid" => $groupmember['openid']));
			$resarr['error'] = 0;
			$resarr['msg'] = '操作成功！';
			echo json_encode($resarr);
			exit;
		} elseif ($op == 'shenhe') {
			$groupid = intval($_GPC['groupid']);
			$id = intval($_GPC['memberid']);
			$groupmember = pdo_fetch('SELECT openid FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE id = {$id}");
			pdo_update(BEST_GROUPMEMBER, array("status" => 1, "intime" => TIMESTAMP), array("id" => $id));
			$senddata = array("openid" => $groupmember['openid'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('groupchatdetail', array("groupid" => $groupid))), "first" => "入群提醒", "keyword1" => "您已被审核进群");
			$this->sendtplmsg($senddata);
			$resarr['error'] = 0;
			$resarr['msg'] = '操作成功！';
			echo json_encode($resarr);
			exit;
		}
	}
	public function doMobileGrouptongzhi()
	{
		global $_GPC, $_W;
		$id = intval($_GPC['id']);
		$type = intval($_GPC['type']);
		$isin = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND id = {$id} AND status = 1");
		if (empty($isin)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '你不属于该群聊！';
			echo json_encode($resArr);
			exit;
		}
		$data['txkaiguan'] = $type;
		pdo_update(BEST_GROUPMEMBER, $data, array("id" => $id));
		$resArr['error'] = 1;
		$resArr['msg'] = '操作成功！';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileGroupchatdetail()
	{
		global $_GPC, $_W;
		if (empty($_W['fans']['from_user'])) {
			$message = '请在微信浏览器中打开！';
			include $this->template('error');
			exit;
		}
		$groupid = intval($_GPC['groupid']);
		$group = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE id = {$groupid}");
		if (empty($group)) {
			$message = '不存在' . $group['groupname'] . '！';
			include $this->template('error');
			exit;
		}
		$isin = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND groupid = {$groupid} AND status = 1 AND isdel = 0");
		if (empty($isin)) {
			$hasshenqing = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND status = 0 AND groupid = {$groupid} AND isdel = 0");
			if (!empty($hasshenqing)) {
				$message = $group['groupname'] . '须审核才能入群，您已提交申请，请等待审核！';
				include $this->template('error');
				exit;
			} else {
				pdo_delete(BEST_GROUPMEMBER, array("groupid" => $groupid, "openid" => $_W['fans']['from_user']));
				if ($group['maxnum'] != 0) {
					$ingroupnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND isdel = 0");
					if ($ingroupnum >= $group['maxnum']) {
						$message = $group['groupname'] . '人数已满！';
						include $this->template('error');
						exit;
					}
				}
				$iscservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND ctype = 1 AND content = '{$_W['fans']['from_user']}'");
				if (!empty($iscservice)) {
					$data['nickname'] = $iscservice['name'];
					$data['avatar'] = tomedia($iscservice['thumb']);
					$data['type'] = 2;
				} else {
					$account_api = WeAccount::create();
					$info = $account_api->fansQueryInfo($_W['fans']['from_user']);
					if ($info['subscribe'] == 1) {
						$data['avatar'] = $info['headimgurl'];
						$data['nickname'] = $info['nickname'];
					} else {
						$fan = mc_oauth_userinfo();
						$data['avatar'] = $fan['headimgurl'];
						$data['nickname'] = $fan['nickname'];
					}
					$data['type'] = 1;
				}
				$data['groupid'] = $groupid;
				$data['weid'] = $_W['uniacid'];
				$data['openid'] = $_W['fans']['from_user'];
				if ($group['autotx'] == 1) {
					$data['txkaiguan'] = 1;
				}
				if ($group['isshenhe'] == 1) {
					$data['status'] = 1;
					$data['intime'] = TIMESTAMP;
					pdo_insert(BEST_GROUPMEMBER, $data);
					$isin = pdo_fetch('SELECT id,intime,avatar,nickname,txkaiguan FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND groupid = {$groupid} AND status = 1 AND isdel = 0");
				} else {
					pdo_insert(BEST_GROUPMEMBER, $data);
					$senddata = array("openid" => $group['admin'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('guanligroup', array("groupid" => $groupid))), "first" => $data['nickname'] . '申请加入' . $group['groupname'] . '！', "keyword1" => $data['nickname']);
					$this->sendtplmsg($senddata);
					$message = $group['groupname'] . '必须审核才能入群，您已提交申请，请等待审核！';
					include $this->template('error');
					exit;
				}
			}
		}
		$allmemberlist = pdo_fetchall('SELECT openid FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE groupid = {$groupid} AND openid != '{$_W['fans']['from_user']}' AND status = 1 AND isdel = 0");
		$allpeople = count($allmemberlist);
		$allmember = '';
		foreach ($allmemberlist as $k => $v) {
			$allmember .= $v['openid'] . $groupid . '|';
		}
		$allmember = substr($allmember, 0, -1);
		$timestamp = TIMESTAMP;
		$quickcon = empty($group['quickcon']) ? '' : explode('|', $group['quickcon']);
		if ($group['autoreply']) {
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $group['autoreply'], $array2);
			if (!empty($array2[0])) {
				foreach ($array2[0] as $kk => $vv) {
					if (!empty($vv)) {
						$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
						$group['autoreply'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $group['autoreply']);
					}
				}
			}
		}
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE groupid = {$groupid} AND weid = {$_W['uniacid']} AND time >= {$isin['intime']}");
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		$groupcontent = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND time >= {$isin['intime']} ORDER BY time ASC LIMIT " . $nowjl . ',' . $psize);
		$chatcontime = 0;
		foreach ($groupcontent as $k => $v) {
			if ($v['time'] - $chatcontime > 7200) {
				$groupcontent[$k]['time'] = $v['time'];
			} else {
				$groupcontent[$k]['time'] = '';
			}
			if ($v['openid'] != $_W['fans']['from_user']) {
				$groupcontent[$k]['class'] = 'left';
			} else {
				$groupcontent[$k]['class'] = 'right';
			}
			$chatcontime = $v['time'];
			$groupcontent[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $v['content']);
			$groupcontent[$k]['content'] = $this->guolv($groupcontent[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $groupcontent[$k]['content'], $array2);
			if (!empty($array2[0]) && ($v['type'] == 1 || $v['type'] == 2)) {
				foreach ($array2[0] as $kk => $vv) {
					if (!empty($vv)) {
						$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
						$groupcontent[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $groupcontent[$k]['content']);
					}
				}
			}
			if ($v['type'] == 5) {
				$donetime = $timestamp - $v['time'];
				if ($donetime >= 24 * 3600 * 3) {
					unset($groupcontent[$k]);
				} else {
					$hasgroupvoicedu = pdo_fetch('SELECT id FROM ' . tablename(BEST_GROUPVOICEDU) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND groupid = {$groupid} AND gchatid = {$v['id']}");
					if (!empty($hasgroupvoicedu)) {
						$groupcontent[$k]['hasgroupvoicedu'] = 1;
					}
				}
			}
		}
		include $this->template('groupdetail');
	}
	public function doMobileDugroupvoice()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$groupid = intval($_GPC['groupid']);
		$chatcon = trim($_GPC['gchatcon']);
		$groupchat = pdo_fetch('SELECT id,time FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND content = '{$chatcon}' AND type = 5");
		if (empty($groupchat)) {
			$resArr['error'] = 1;
			$resArr['message'] = '暂无这条群聊语音！';
			echo json_encode($resArr);
			exit;
		}
		$hasgroupvoicedu = pdo_fetch('SELECT id FROM ' . tablename(BEST_GROUPVOICEDU) . " WHERE weid = {$_W['uniacid']} AND openid = '{$openid}' AND groupid = {$groupid} AND gchatid = {$groupchat['id']}");
		$nowtime = TIMESTAMP;
		if (empty($hasgroupvoicedu)) {
			$data = array("weid" => $_W['uniacid'], "groupid" => $groupid, "gchatid" => $groupchat['id'], "openid" => $openid, "content" => $chatcon, "time" => $nowtime);
			pdo_insert(BEST_GROUPVOICEDU, $data);
		}
		$donetime = $nowtime - 24 * 3600 * 3;
		$nextvoice = pdo_fetch('SELECT content FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND openid != '{$openid}' AND groupid = {$groupid} AND time > {$donetime} AND time > {$groupchat['time']} AND type = 5 ORDER BY time ASC");
		$nextvoicecon = empty($nextvoice) ? '' : $nextvoice['content'];
		$resArr['error'] = 0;
		$resArr['content'] = $nextvoicecon;
		$resArr['message'] = '读取语音成功！';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileGroupchatajax()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$groupid = intval($_GPC['groupid']);
		$isin = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$openid}' AND groupid = {$groupid} AND status = 1 AND isdel = 0");
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE groupid = {$groupid} AND weid = {$_W['uniacid']} AND time >= {$isin['intime']}");
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		if ($total > $pindex * $psize) {
			$tolimit = $psize;
		} else {
			$tolimit = $psize - ($pindex * $psize - $total);
		}
		$groupcontent = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND time >= {$isin['intime']} ORDER BY time ASC LIMIT " . $nowjl . ',' . $tolimit);
		$chatcontime = 0;
		foreach ($groupcontent as $k => $v) {
			if ($v['time'] - $chatcontime > 7200) {
				$groupcontent[$k]['time'] = $v['time'];
			} else {
				$groupcontent[$k]['time'] = '';
			}
			$chatcontime = $v['time'];
			$groupcontent[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $v['content']);
			$groupcontent[$k]['content'] = $this->guolv($groupcontent[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $groupcontent[$k]['content'], $array2);
			if (!empty($array2[0]) && ($v['type'] == 1 || $v['type'] == 2)) {
				foreach ($array2[0] as $kk => $vv) {
					if (!empty($vv)) {
						$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
						$groupcontent[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $groupcontent[$k]['content']);
					}
				}
			}
			if ($v['type'] == 5) {
				$donetime = $timestamp - $v['time'];
				if ($donetime >= 24 * 3600 * 3) {
					unset($groupcontent[$k]);
				}
			}
		}
		$html = '';
		foreach ($groupcontent as $k => $v) {
			$htmltime = !empty($v['time']) ? '<div class="time text-c">' . date('Y-m-d H:i:s', $v['time']) . '</div>' : '';
			if ($v['openid'] != $openid) {
				$class = 'left';
				$conhtml = '<div class="groupnickname n-left">' . $v['nickname'] . '</div><div class="con flex1 flex" style="margin-top:0.4rem;">';
			} else {
				$class = 'right';
				$conhtml = '<div class="con flex1 flex">';
			}
			if ($v['type'] == 3 || $v['type'] == 4) {
				$chatconhtml = '<div class="concon"><img src="' . $v['content'] . '" class="sssbbb" /></div>';
			} else {
				if ($v['type'] == 5) {
					if ($v['hasyuyindu'] == 0 && $openid == $v['toopenid']) {
						$weidu = '<span class="weidu">未读</span>';
					} else {
						$weidu = '';
					}
					$chatconhtml = '<div class="concon voiceplay flex">
									<img src="' . NEWSTATIC_ROOT . '/icon/voice2.png" class="voice2" />
									' . $weidu . '
									<div class="flex1"></div>
								</div>';
				} else {
					$chatconhtml = '<div class="concon">' . $v['content'] . '</div>';
				}
			}
			$html .= $htmltime . '<div class="' . $class . ' flex">
									<img src="' . $v['avatar'] . '" class="avatar" />
									' . $conhtml . '
										<div class="triangle-' . $class . '"></div>
										' . $chatconhtml . '
										<div class="flex1"></div>
									</div>
								</div>';
		}
		echo $html;
		exit;
	}
	public function doMobileTuichuqun()
	{
		global $_GPC, $_W;
		$groupid = intval($_GPC['groupid']);
		$openid = $_W['fans']['from_user'];
		if ($groupid == 0 || $openid == '') {
			$resArr['error'] = 1;
			$resArr['msg'] = '参数传输错误！';
			echo json_encode($resArr);
			exit;
		}
		$group = pdo_fetch('SELECT groupname,admin FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$groupid}");
		if ($group['admin'] == $openid) {
			$resArr['error'] = 1;
			$resArr['msg'] = '管理员不能退群！';
			echo json_encode($resArr);
			exit;
		}
		$has = pdo_fetch('SELECT id,nickname FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND openid = '{$openid}'");
		if (empty($has)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '您不是群成员！';
			echo json_encode($resArr);
			exit;
		}
		$datag['isdel'] = 1;
		pdo_update(BEST_GROUPMEMBER, $datag, array("weid" => $_W['uniacid'], "groupid" => $groupid, "openid" => $openid));
		$concon = $has['nickname'] . '退出了' . $group['groupname'] . '！';
		$send['touser'] = $group['admin'];
		$send['msgtype'] = 'text';
		$send['text'] = array("content" => urlencode($concon));
		$acc = WeAccount::create($_W['uniacid']);
		$res = $acc->sendCustomNotice($send);
		$resArr['error'] = 1;
		$resArr['msg'] = '退出成功！';
		echo json_encode($resArr);
		exit;
	}
	public function doWebAdv()
	{
		include_once ROOT_PATH . 'inc/web/adv.php';
	}
	public function doWebKehu()
	{
		include_once ROOT_PATH . 'inc/web/kehu.php';
	}
	public function doWebZaixian()
	{
		include_once ROOT_PATH . 'inc/web/zaixian.php';
	}
	public function doWebCjwt()
	{
		include_once ROOT_PATH . 'inc/web/cjwt.php';
	}
	public function doWebZdhf()
	{
		include_once ROOT_PATH . 'inc/web/zdhf.php';
	}
	public function doWebTongji()
	{
		include_once ROOT_PATH . 'inc/web/tongji.php';
	}
	public function doWebXcx()
	{
		include_once ROOT_PATH . 'inc/web/xcx.php';
	}
	public function doWebXcxcservice()
	{
		include_once ROOT_PATH . 'inc/web/xcxcservice.php';
	}
	public function checkSignature($xcx, $echostr)
	{
		$token = $xcx['token'];
		$signature = $_GET['signature'];
		$timestamp = $_GET['timestamp'];
		$nonce = $_GET['nonce'];
		$tmpArr = array($token, $timestamp, $nonce);
		sort($tmpArr, SORT_STRING);
		$tmpStr = implode($tmpArr);
		$tmpStr = sha1($tmpStr);
		if ($tmpStr == $signature) {
			pdo_update(BEST_XCX, array("status" => 1), array("id" => $xcx['id']));
			echo $echostr;
			exit;
		} else {
			pdo_update(BEST_XCX, array("status" => 2), array("id" => $xcx['id']));
			echo false;
			exit;
		}
	}
	public function doMobileXcxjt()
	{
		global $_GPC, $_W;
		$xcxid = intval($_GPC['id']);
		$xcx = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCX) . " WHERE id = {$xcxid}");
		if (isset($_GET['echostr'])) {
			$this->checkSignature($xcx, $_GET['echostr']);
		} else {
			$this->responseMsg();
		}
	}
	public function testwri($txt)
	{
		$myfile = fopen('xcx.txt', 'w') or die('Unable to open file!');
		fwrite($myfile, $txt);
		fclose($myfile);
	}
	public function responseMsg()
	{
		$postStr = file_get_contents('php://input');
		if (!empty($postStr) && is_string($postStr)) {
			$postArr = json_decode($postStr, true);
			if (!empty($postArr['MsgType']) && $postArr['MsgType'] == 'text') {
				$this->addxcxchat($postArr);
			} elseif (!empty($postArr['MsgType']) && $postArr['MsgType'] == 'image') {
				$this->addxcxchat($postArr);
			} else {
				if ($postArr['MsgType'] == 'event' && $postArr['Event'] == 'user_enter_tempsession') {
					$this->addxcxfanskefu($postArr);
				} else {
					exit('aaa');
				}
			}
		} else {
			echo 'success';
			exit;
		}
	}
	public function addxcxchat($postArr)
	{
		global $_GPC, $_W;
		include_once ROOT_PATH . 'emoji/emoji.php';
		$xcx = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCX) . " WHERE uniacid = {$_W['uniacid']} AND gh_id = '{$postArr['ToUserName']}'");
		$fansopenid = $postArr['FromUserName'];
		$gh_id = $postArr['ToUserName'];
		$has = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$fansopenid}' AND nowkefu = 1 AND gh_id = '{$gh_id}'");
		$cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXCSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$has['kefuopenid']}' AND xcxid = {$xcx['id']}");
		$kefuopenid = $cservice['content'];
		if (!empty($has)) {
			if ($postArr['MsgType'] == 'text') {
				$content = emoji_docomo_to_unified($postArr['Content']);
				$content = emoji_unified_to_html($content);
				$data['content'] = $content;
				$dataup['lastcon'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $postArr['Content']);
			}
			if ($postArr['MsgType'] == 'image') {
				$picres = $this->xcximgsave($postArr['PicUrl']);
				if ($picres != '') {
					$data['content'] = $picres;
				} else {
					$data['content'] = $postArr['PicUrl'];
				}
				$data['mediaId'] = $postArr['MediaId'];
				$dataup['lastcon'] = '[图片消息]';
			}
			$data['weid'] = $_W['uniacid'];
			$data['fkid'] = $has['id'];
			$data['openid'] = $fansopenid;
			$data['toopenid'] = $kefuopenid;
			$data['msgtype'] = $postArr['MsgType'];
			$data['gh_id'] = $gh_id;
			$data['time'] = $postArr['CreateTime'];
			$data['msgid'] = $postArr['MsgId'];
			pdo_insert(BEST_XCXCHAT, $data);
			$dataup['notread'] = $has['notread'] + 1;
			$dataup['lasttime'] = $postArr['CreateTime'];
			$dataup['msgtype'] = $postArr['MsgType'];
			pdo_update(BEST_XCXFANSKEFU, $dataup, array("id" => $has['id']));
			$hasauto = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXAUTO) . " WHERE weid = {$_W['uniacid']} AND kfid = {$cservice['id']} AND iszdhf = 1 AND zdhftype = 1 AND zdhftitle = '{$data['content']}'");
			if (empty($hasauto)) {
				$hasauto = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXAUTO) . " WHERE weid = {$_W['uniacid']} AND kfid = {$cservice['id']} AND iszdhf = 1 AND zdhftype = 0 AND INSTR('{$data['content']}',zdhftitle)");
			}
			if (!empty($hasauto)) {
				if ($hasauto['msgtype'] == 'text') {
					$chatcontent = $hasauto['title'];
					$addres = $this->addxcxchat2($has['fansopenid'], $hasauto['title'], $hasauto['msgtype'], $has['gh_id']);
				}
				if ($hasauto['msgtype'] == 'image') {
					$chatcontent = $hasauto['thumb'] = tomedia($hasauto['thumb']);
					$addres = $this->addxcxchat2($has['fansopenid'], $hasauto['thumb'], $hasauto['msgtype'], $has['gh_id']);
				}
				if ($hasauto['msgtype'] == 'link') {
					$zdconlink['title'] = $hasauto['title'];
					$zdconlink['description'] = $hasauto['description'];
					$zdconlink['url'] = $hasauto['url'];
					$zdconlink['thumb_url'] = tomedia($hasauto['thumb_url']);
					$addres = $this->addxcxchat2($has['fansopenid'], $zdconlink, $hasauto['msgtype'], $has['gh_id']);
				}
				if ($addres['errcode'] == '0') {
					if ($hasauto['msgtype'] == 'link') {
						$datachat['title'] = $hasauto['title'];
						$datachat['description'] = $hasauto['description'];
						$datachat['url'] = $hasauto['url'];
						$datachat['thumb_url'] = tomedia($hasauto['thumb_url']);
					} else {
						$datachat['content'] = $chatcontent;
					}
					$datachat['openid'] = $has['kefuopenid'];
					$datachat['toopenid'] = $has['fansopenid'];
					$datachat['gh_id'] = $has['gh_id'];
					$datachat['time'] = $addres['time'];
					$datachat['weid'] = $_W['uniacid'];
					$datachat['fkid'] = $has['id'];
					$datachat['msgtype'] = $hasauto['msgtype'];
					pdo_insert(BEST_XCXCHAT, $datachat);
				}
			}
			$newnickname = '[' . $xcx['name'] . ']' . $has['fansnickname'];
			$newavatar = $has['fansavatar'];
			$notread = $has['notread'] + 1;
			$post_url = 'https://api.qiumipai.com:2121/?type=xcxpublish&to=' . $has['kefuopenid'] . '&notread=' . $notread . '&newavatar=' . $newavatar . '&content=' . $data['content'] . '&msgtype=' . $data['msgtype'] . '&toopenid=' . $has['fansopenid'] . '&fkid=' . $has['id'] . '&newnickname=' . $newnickname;
			load()->func('communication');
			ihttp_request($post_url);
			$this->xcxtzkefu($kefuopenid, $has['lasttime'], $has['id'], $dataup['lastcon'], $xcx['name'], $has['fansnickname']);
		}
	}
	public function xcximgsave($picUrl)
	{
		global $_W, $_GPC;
		$updir = IA_ROOT . '/public/attachment/images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/';
		if (!file_exists($updir)) {
			mkdir($updir, 0777, true);
		}
		$randimgurl = 'images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.jpg';
		$targetName = '/public/attachment/' . $randimgurl;
		$ch = curl_init($picUrl);
		$fp = fopen($targetName, 'wb');
		curl_setopt($ch, CURLOPT_FILE, $fp);
		curl_setopt($ch, CURLOPT_HEADER, 0);
		curl_exec($ch);
		curl_close($ch);
		fclose($fp);
		if (file_exists($targetName)) {
			$img_info = getimagesize($targetName);
			if ($img_info[0] > 640) {
				$this->mkThumbnail($targetName, 640, null, $targetName);
			}
			if ($this->module['config']['isqiniu'] == 1) {
				$remotestatus = $this->doQiuniu($randimgurl, true);
				if (is_error($remotestatus)) {
					return 0;
					exit;
				} else {
					return $this->module['config']['qiniuurl'] . '/' . $randimgurl;
					exit;
				}
			} elseif ($this->module['config']['isqiniu'] == 3) {
				if (!empty($_W['setting']['remote']['type'])) {
					load()->func('file');
					$remotestatus = file_remote_upload($randimgurl, true);
					if (is_error($remotestatus)) {
						return 0;
						exit;
					} else {
						return tomedia($randimgurl);
						exit;
					}
				}
			}
			return tomedia($randimgurl);
			exit;
		} else {
			return 0;
			exit;
		}
	}
	public function doMobileServicechatajaxxcx()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE id = {$fkid}");
		$fkidlist = pdo_fetchall('SELECT id FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$hasfanskefu['fansopenid']}' AND gh_id = '{$hasfanskefu['gh_id']}'");
		$fkarr = array();
		foreach ($fkidlist as $k => $v) {
			$fkarr[] = $v['id'];
		}
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_XCXCHAT) . ' WHERE fkid in (' . implode(',', $fkarr) . ')');
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		if ($total > $pindex * $psize) {
			$tolimit = $psize;
		} else {
			$tolimit = $psize - ($pindex * $psize - $total);
		}
		$chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_XCXCHAT) . ' WHERE fkid in (' . implode(',', $fkarr) . ') ORDER BY time ASC LIMIT ' . $nowjl . ',' . $tolimit);
		$timestamp = TIMESTAMP;
		$chatcontime = 0;
		foreach ($chatcon as $k => $v) {
			if ($v['openid'] != $hasfanskefu['fansopenid']) {
				$newfk = pdo_fetch('SELECT kefuavatar FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE id = {$v['fkid']}");
				$chatcon[$k]['kefuavatar'] = $newfk['kefuavatar'];
			}
			if ($v['time'] - $chatcontime > 7200) {
				$chatcon[$k]['time'] = $v['time'];
			} else {
				$chatcon[$k]['time'] = '';
			}
		}
		$html = '';
		foreach ($chatcon as $k => $v) {
			$htmltime = !empty($v['time']) ? '<div class="time text-c">' . date('Y-m-d H:i:s', $v['time']) . '</div>' : '';
			if ($v['openid'] == $hasfanskefu['fansopenid']) {
				if ($v['msgtype'] == 'image') {
					$chatconhtml = '<div class="concon"><img src="' . $v['content'] . '" class="sssbbb" /></div>';
				}
				if ($v['msgtype'] == 'text') {
					$chatconhtml = '<div class="concon">' . $v['content'] . '</div>';
				}
				$iimmgg = $hasfanskefu['fansavatar'] != '' ? $hasfanskefu['fansavatar'] : '/static/application/elapp_customerservice/static/xcx.png';
				$html .= $htmltime . '<div class="left flex">
										<img src="' . $iimmgg . '" class="avatar" />
										<div class="con flex flex1">
											<div class="triangle-left"></div>
											' . $chatconhtml . '
											<div class="flex1"></div>
										</div>
									</div>';
			} else {
				if ($v['msgtype'] == 'image') {
					$chatconhtml = '<div class="concon"><img src="' . $v['content'] . '" class="sssbbb" /></div>';
				}
				if ($v['msgtype'] == 'text') {
					$chatconhtml = '<div class="concon">' . $v['content'] . '</div>';
				}
				if ($v['msgtype'] == 'link') {
					$chatconhtml = '<div class="concon">
										<a href="' . $v['url'] . '">
										<div class="tuwen">
											<div class="tuwen-title">' . $v['title'] . '</div>
											<div class="tuwen-bottom flex">
												<div class="tuwen-des">' . $v['description'] . '</div>
												<img src="' . $v['thumb_url'] . '" class="tuwen-img" />
											</div>
										</div>
										</a>
									</div>';
				}
				$html .= '<div class="right flex">
							<img src="' . $v['kefuavatar'] . '" class="avatar" />
							<div class="con flex flex1">
								<div class="triangle-right"></div>
								' . $chatconhtml . '
								<div class="flex1"></div>
							</div>
						</div>';
			}
		}
		echo $html;
		exit;
	}
	public function doMobileMychatxcx()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$ssopenid = $_GPC['ssopenid'];
		if ($ssopenid != '' && empty($openid)) {
			$openid = $ssopenid;
		}
		$operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';
		if ($operation == 'display') {
			$psize = 20;
			$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE kefuopenid = '{$openid}' AND lastcon != ''");
			$allpage = ceil($total / $psize) + 1;
			$page = intval($_GPC['page']);
			$pindex = max(1, $page);
			$chatlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE kefuopenid = '{$openid}' AND lastcon != '' ORDER BY notread DESC,lasttime DESC LIMIT " . ($pindex - 1) * $psize . ',' . $psize);
			foreach ($chatlist as $kk => $vv) {
				$xcxres = pdo_fetch('SELECT name FROM ' . tablename(BEST_XCX) . " WHERE gh_id = '{$vv['gh_id']}'");
				$biaoqian = pdo_fetch('SELECT name FROM ' . tablename(BEST_BIAOQIAN) . " WHERE kefuopenid = '{$vv['kefuopenid']}' AND fensiopenid = '{$vv['fansopenid']}'");
				$vv['fansnickname'] = $vv['fansnickname'] == '' ? '用户' : $vv['fansnickname'];
				if (!empty($biaoqian)) {
					$chatlist[$kk]['fansnickname'] = '[' . $xcxres['name'] . '][' . $biaoqian['name'] . ']' . $vv['fansnickname'];
				} else {
					$chatlist[$kk]['fansnickname'] = '[' . $xcxres['name'] . ']' . $vv['fansnickname'];
				}
			}
			$isajax = intval($_GPC['isajax']);
			if ($isajax == 1) {
				$html = '';
				foreach ($chatlist as $kk => $vv) {
					if ($vv['msgtype'] == 'text') {
						$con = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $vv['lastcon']);
					} else {
						$con = '<span style="color:#900;">[图片消息]</span>';
					}
					$avatar = $vv['fansavatar'] != '' ? $vv['fansavatar'] : MD_ROOT . 'static/xcx.png';
					$mychatbadge = $vv['notread'] > 0 ? '<span class="mychatbadge">' . $vv['notread'] . '</span>' : '';
					$html .= '<div class="item flex textellipsis1 fkid' . $vv['id'] . '">
								<a href="' . $this->createMobileUrl('xcxchat', array("fkid" => $vv['id'], "ssopenid" => $openid)) . '" class="flex tohref textellipsis1 flex1">
									<img src="' . $avatar . '">' . $mychatbadge . '
									<div class="text textellipsis1 flex1">
										<div class="name textellipsis1">' . $vv['fansnickname'] . '</div>
										<div class="lastmsg textellipsis1">' . $con . '</div>
									</div>
								</a>
								<div class="timedo">
									<div class="time">' . $this->getChatTimeStr($vv['lasttime']) . '</div>
									<div class="dodel" data-fkid="' . $vv['id'] . '">删除</div>
								</div>
							</div>';
				}
				echo $html;
				exit;
			}
			$allwei = pdo_fetchcolumn('SELECT SUM(notread) FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND kefuopenid = '{$openid}'");
			$allwei = empty($allwei) ? 0 : $allwei;
			include $this->template('mychatxcx');
		} elseif ($operation == 'delete') {
			$fkid = intval($_GPC['fkid']);
			pdo_delete(BEST_XCXCHAT, array("fkid" => $fkid));
			pdo_delete(BEST_XCXFANSKEFU, array("id" => $fkid));
			$resArr['error'] = 0;
			$resArr['message'] = '恭喜您，删除聊天记录成功！';
			echo json_encode($resArr, true);
			exit;
		}
	}
	public function doMobileAjaxallwei()
	{
		global $_GPC, $_W;
		$openid = $_W['fans']['from_user'];
		$allwei = pdo_fetchcolumn('SELECT SUM(notread) FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND kefuopenid = '{$openid}'");
		echo $allwei = empty($allwei) ? 0 : $allwei;
	}
	public function getChatTimeStr($addTime)
	{
		$nowTime = TIMESTAMP;
		if ($addTime > $nowTime) {
			return '';
		}
		$timeStr = '';
		$addTime = explode(',', date('Y,n,j,w,a,h,i,y', $addTime));
		$nowTime = explode(',', date('Y,n,j,w,a,h,i,y', $nowTime));
		$dayPerMonthAddTime = $this->getDayPerMonth($addTime[0]);
		$week = array(0 => "星期日", 1 => "星期一", 2 => "星期二", 3 => "星期三", 4 => "星期四", 5 => "星期五", 6 => "星期六");
		if ($addTime[0] == $nowTime[0] && $addTime[1] == $nowTime[1] && $addTime[2] == $nowTime[2]) {
			if ($addTime[4] == 'am') {
				$timeStr .= ' 上午';
			} elseif ($addTime[4] == 'pm') {
				$timeStr .= ' 下午';
			}
			$timeStr .= $addTime[5] . ':' . $addTime[6];
		} else {
			if ($addTime[0] == $nowTime[0] && $addTime[1] == $nowTime[1] && $addTime[2] == $nowTime[2] - 1 || $addTime[0] == $nowTime[0] && $nowTime[1] - $addTime[1] == 1 && $dayPerMonthAddTime[$addTime[1]] == $addTime[2] && $nowTime[2] == 1 || $nowTime[0] - $addTime[0] == 1 && $addTime[1] == 12 && $addTime[2] == 31 && $nowTime[1] == 1 && $nowTime[2] == 1) {
				$timeStr .= '昨天 ' . $addTime[5] . ':' . $addTime[6] . ' ';
			} else {
				if ($addTime[0] == $nowTime[0] && $addTime[1] == $nowTime[1] && $nowTime[2] - $addTime[2] < 7 || ($addTime[0] == $nowTime[0] && $nowTime[1] - $addTime[1] == 1 && $dayPerMonthAddTime[$addTime[1]] - $addTime[2] + $nowTime[2] < 7 || $nowTime[0] - $addTime[0] == 1 && $addTime[1] == 12 && $nowTime[1] == 1 && 31 - $addTime[2] + $nowTime[2] < 7)) {
					$timeStr .= $week[$addTime[3]];
				} else {
					$timeStr .= $addTime[7] . '/' . $addTime[1] . '/' . $addTime[2];
				}
			}
		}
		return $timeStr;
	}
	public function getDayPerMonth($year)
	{
		$arr = array(1 => 31, 3 => 31, 4 => 30, 5 => 31, 6 => 30, 7 => 31, 8 => 31, 9 => 30, 10 => 31, 11 => 30, 12 => 31);
		if ($year % 4 == 0 && $year % 100 != 0 || $year % 400 == 0) {
			$arr[2] = 29;
		} else {
			$arr[2] = 28;
		}
		return $arr;
	}
	public function doMobileXcxchat()
	{
		global $_GPC, $_W;
		include_once ROOT_PATH . 'qqface.php';
		$fkid = intval($_GPC['fkid']);
		$openid = $_W['fans']['from_user'];
		$ssopenid = $_GPC['ssopenid'];
		if ($ssopenid != '' && empty($openid)) {
			$openid = $ssopenid;
		}
		if (empty($openid)) {
			$message = '请在微信浏览器中打开！';
			include $this->template('error');
			exit;
		}
		$cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXCSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$openid}'");
		if (empty($cservice)) {
			$message = '你不是客服身份，请联系管理员查看具体信息！';
			include $this->template('error');
			exit;
		}
		$hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE id = {$fkid}");
		$xcxres = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCX) . " WHERE gh_id = '{$hasfanskefu['gh_id']}'");
		$fkidlist = pdo_fetchall('SELECT id FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$hasfanskefu['fansopenid']}' AND gh_id = '{$hasfanskefu['gh_id']}'");
		$fkarr = array();
		foreach ($fkidlist as $k => $v) {
			$fkarr[] = $v['id'];
		}
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_XCXCHAT) . ' WHERE fkid in (' . implode(',', $fkarr) . ')');
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		$chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_XCXCHAT) . ' WHERE fkid in (' . implode(',', $fkarr) . ') ORDER BY time ASC LIMIT ' . $nowjl . ',' . $psize);
		$chatcontime = 0;
		foreach ($chatcon as $k => $v) {
			if ($v['openid'] != $hasfanskefu['fansopenid']) {
				$newfk = pdo_fetch('SELECT kefuavatar FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE id = {$v['fkid']}");
				$chatcon[$k]['kefuavatar'] = $newfk['kefuavatar'];
			}
			if ($v['time'] - $chatcontime > 7200) {
				$chatcon[$k]['time'] = $v['time'];
			} else {
				$chatcon[$k]['time'] = '';
			}
			$chatcon[$k]['content'] = qqface_convert_html($chatcon[$k]['content']);
			$chatcon[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcon[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $chatcon[$k]['content'], $array2);
			if (!empty($array2[0]) && $v['msgtype'] == 'text') {
				foreach ($array2[0] as $kk => $vv) {
					if (!empty($vv) && strpos($vv, 'https://res.wx.qq.com') === false) {
						$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
						$chatcon[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $chatcon[$k]['content']);
					}
				}
			}
			$chatcontime = $v['time'];
		}
		$kefuauto = empty($cservice['kefuauto']) ? '' : explode('|', $cservice['kefuauto']);
		pdo_update(BEST_XCXFANSKEFU, array("notread" => 0), array("id" => $hasfanskefu['id']));
		$biaoqian = pdo_fetch('SELECT * FROM ' . tablename(BEST_BIAOQIAN) . " WHERE kefuopenid = '{$openid}' AND fensiopenid = '{$hasfanskefu['fansopenid']}'");
		include $this->template('newservicechatxcx');
	}
	public function doMobileAddchatxcx()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$type = intval($_GPC['type']);
		$chatcontent = trim($_GPC['content']);
		if (empty($chatcontent)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请输入对话内容！';
			echo json_encode($resArr);
			exit;
		}
		$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE id = {$fkid}");
		if ($type == 2) {
			$msgtype = 'text';
			$dataup['lastcon'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcontent);
		}
		if ($type == 3) {
			$msgtype = 'image';
			$chatcontent = tomedia($chatcontent);
			$dataup['lastcon'] = '[图片消息]';
		}
		$addres = $this->addxcxchat2($fanskefu['fansopenid'], $chatcontent, $msgtype, $fanskefu['gh_id']);
		if ($addres['errcode'] == '40001') {
			$addres = $this->addxcxchat2($fanskefu['fansopenid'], $chatcontent, $msgtype, $fanskefu['gh_id'], 1);
		}
		if ($addres['errcode'] != '0') {
			$resArr['error'] = 1;
			$resArr['msg'] = $this->getwxerrormsg($addres);
			echo json_encode($resArr);
			exit;
		} else {
			$data['openid'] = $fanskefu['kefuopenid'];
			$data['toopenid'] = $fanskefu['fansopenid'];
			$data['gh_id'] = $fanskefu['gh_id'];
			$data['time'] = $addres['time'];
			$data['content'] = $chatcontent;
			$data['weid'] = $_W['uniacid'];
			$data['fkid'] = $fkid;
			$data['msgtype'] = $msgtype;
			pdo_insert(BEST_XCXCHAT, $data);
			$dataup['lasttime'] = $data['time'];
			$dataup['msgtype'] = $msgtype;
			pdo_update(BEST_XCXFANSKEFU, $dataup, array("id" => $fkid));
			if ($type == 2) {
				$resArr['content'] = '<div class="concon">' . $chatcontent . '</div>';
			} else {
				$resArr['content'] = '<div class="concon"><img src="' . $chatcontent . '" class="sssbbb" /></div>';
			}
			$resArr['error'] = 0;
			$resArr['msg'] = '';
			$resArr['datetime'] = date('Y-m-d H:i:s', $data['time']);
			echo json_encode($resArr);
			exit;
		}
	}
	public function getwxerrormsg($addres)
	{
		if ($addres['errcode'] == '45047') {
			$errmsg = '客服接口下行条数超过上限！';
		} elseif ($addres['errcode'] == '48001') {
			$errmsg = 'API 功能未授权，请确认小程序已获得该接口！';
		} elseif ($addres['errcode'] == '45015') {
			$errmsg = '回复时间超过限制！';
		} elseif ($addres['errcode'] == '40003') {
			$errmsg = '不合法的 OpenID，请开发者确认 OpenID 是否是其他小程序的 OpenID！';
		} elseif ($addres['errcode'] == '40002') {
			$errmsg = '不合法的凭证类型！';
		} elseif ($addres['errcode'] == '40001') {
			$errmsg = '获取 access_token 时 AppSecret 错误，或者 access_token 无效。请开发者认真比对 AppSecret 的正确性，或查看是否正在为恰当的小程序调用接口！';
		} elseif ($addres['errcode'] == '-1') {
			$errmsg = '系统繁忙，此时请开发者稍候再试！';
		} else {
			$errmsg = $addres['errmsg'];
		}
		return $errmsg;
	}
	public function xcxtzkefu($openid, $lasttime, $fkid, $content, $xcxname, $fansnickname)
	{
		global $_GPC, $_W;
		$guotime = TIMESTAMP - $lasttime;
		if ($guotime > $this->module['config']['kefutplminute']) {
			$senddata = array("openid" => $openid, "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('xcxchat', array("fkid" => $fkid, "ssopenid" => $openid))), "first" => $xcxname . '[小程序]的用户' . $fansnickname . '向你发起了咨询！', "keyword1" => $xcxname . '[小程序]的用户' . $fansnickname);
			$this->sendtplmsg($senddata);
		}
	}
	public function addxcxchat2($touser, $content, $msgtype, $gh_id, $isnewtoken = 0)
	{
		global $_GPC, $_W;
		$nowtime = TIMESTAMP;
		$xcx = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCX) . " WHERE uniacid = {$_W['uniacid']} AND gh_id = '{$gh_id}'");
		if ($isnewtoken == 1) {
			$access_token = $this->get_xcx_accessToken($xcx['appid'], $xcx['secret']);
			if ($access_token == 'error') {
				$resArr['error'] = 1;
				$resArr['msg'] = '获取AccessToken失败！';
				echo json_encode($resArr);
				exit;
			}
			$dataup['access_token'] = $access_token;
			$dataup['guoqitime'] = $nowtime + 3600;
			pdo_update(BEST_XCX, $dataup, array("id" => $xcx['id']));
		} else {
			if ($xcx['guoqitime'] < TIMESTAMP) {
				$access_token = $this->get_xcx_accessToken($xcx['appid'], $xcx['secret']);
				if ($access_token == 'error') {
					$resArr['error'] = 1;
					$resArr['msg'] = '获取AccessToken失败！';
					echo json_encode($resArr);
					exit;
				}
				$dataup['access_token'] = $access_token;
				$dataup['guoqitime'] = $nowtime + 3600;
				pdo_update(BEST_XCX, $dataup, array("id" => $xcx['id']));
			} else {
				$access_token = $xcx['access_token'];
			}
		}
		if ($msgtype == 'text') {
			$data = array("touser" => $touser, "msgtype" => "text", "text" => array("content" => $content));
		}
		if ($msgtype == 'image') {
			$fileName = time() . '.jpg';
			$source = file_get_contents($content);
			file_put_contents('../addons/elapp_customerservice/' . $fileName, $source);
			$imgurl = 'http://api.weixin.qq.com/cgi-bin/media/upload?access_token=' . $access_token . '&type=image';
			$imgres = $this->curl_post2($imgurl, '../addons/elapp_customerservice/' . $fileName);
			unlink('../addons/elapp_customerservice/' . $fileName);
			$data = array("touser" => $touser, "msgtype" => "image", "image" => array("media_id" => $imgres['media_id']));
		}
		if ($msgtype == 'link') {
			$data = array("touser" => $touser, "msgtype" => "link", "link" => array("title" => $content['title'], "description" => $content['description'], "url" => $content['url'], "thumb_url" => $content['thumb_url']));
		}
		$url = 'https://api.weixin.qq.com/cgi-bin/message/custom/send?access_token=' . $access_token;
		$json = json_encode($data, JSON_UNESCAPED_UNICODE);
		$kefutokehures = $this->curl_post($url, $json);
		$kefutokehures = json_decode($kefutokehures, true);
		$kefutokehures['time'] = $nowtime;
		return $kefutokehures;
	}
	public function addxcxfanskefu($postArr)
	{
		global $_GPC, $_W;
		$fansopenid = $postArr['FromUserName'];
		$sessionform = $postArr['SessionFrom'];
		$sessionformarr = explode('-', $sessionform);
		$jhtext = $sessionformarr[0];
		$jhtext = $jhtext == 0 ? '' : $jhtext;
		$fansnickname = $sessionformarr[1];
		$fansavatar = $sessionformarr[2];
		$gh_id = $postArr['ToUserName'];
		$xcx = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCX) . " WHERE uniacid = {$_W['uniacid']} AND gh_id = '{$gh_id}'");
		$nowxcxfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND nowkefu = 1 AND fansopenid = '{$fansopenid}' AND gh_id = '{$gh_id}'");
		if (!empty($nowxcxfanskefu)) {
			$nowkefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXCSERVICE) . " WHERE weid = {$_W['uniacid']} AND xcxid = {$xcx['id']} AND concont = '{$nowxcxfanskefu['kefuopenid']}'");
			$isnowfilter = !empty($nowkefu) ? 1 : 0;
		} else {
			$isnowfilter = 0;
		}
		if ($isnowfilter == 1) {
			$isfromnow = 1;
			$condition = "weid = {$_W['uniacid']} AND xcxid = {$xcx['id']} AND content = '{$nowxcxfanskefu['kefuopenid']}'";
		} else {
			$condition = "weid = {$_W['uniacid']} AND xcxid = {$xcx['id']} AND jhtext = '{$jhtext}'";
			$isfromnow = 0;
			$nowhour = intval(date('H', TIMESTAMP));
			$nowhouradd = $nowhour + 1;
			$condition .= " AND (\r\n\t\t\t\t(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR \r\n\t\t\t\t(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))\r\n\t\t\t)";
			$zhouji = date('w');
			if ($zhouji == '1') {
				$condition .= ' AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '2') {
				$condition .= ' AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '3') {
				$condition .= ' AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '4') {
				$condition .= ' AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '5') {
				$condition .= ' AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '6') {
				$condition .= ' AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			if ($zhouji == '0') {
				$condition .= ' AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0)';
			}
			$allqzvals = pdo_fetchcolumn('SELECT SUM(gzhqzval) FROM ' . tablename(BEST_XCXCSERVICE) . ' WHERE ' . $condition);
			$allqznum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_XCXCSERVICE) . ' WHERE ' . $condition);
			$pjval = intval($allqzvals / $allqznum);
			$condition .= " AND gzhqzval <= {$pjval}";
		}
		$cservice = pdo_fetchall('SELECT * FROM ' . tablename(BEST_XCXCSERVICE) . ' WHERE ' . $condition . ' ORDER BY gzhqzval ASC LIMIT 1');
		$cservice = $cservice[0];
		if (!empty($cservice)) {
			pdo_update(BEST_XCXFANSKEFU, array("nowkefu" => 0), array("fansopenid" => $fansopenid, "gh_id" => $gh_id));
			if ($isfromnow == 0) {
				$dataqz['gzhqzval'] = $cservice['gzhqzval'] + 1;
				pdo_update(BEST_XCXCSERVICE, $dataqz, array("id" => $cservice['id']));
			}
			$kefuopenid = $cservice['content'];
			$has = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE weid = {$_W['uniacid']} AND kefuopenid = '{$kefuopenid}' AND fansopenid = '{$fansopenid}' AND gh_id = '{$gh_id}'");
			if (empty($has)) {
				$data['weid'] = $_W['uniacid'];
				$data['fansopenid'] = $fansopenid;
				$data['fansnickname'] = $fansnickname;
				$data['fansavatar'] = $fansavatar;
				$data['kefuopenid'] = $kefuopenid;
				$data['kefuavatar'] = tomedia($cservice['thumb']);
				$data['kefunickname'] = $cservice['name'];
				$data['msgtype'] = $postArr['Event'];
				$data['gh_id'] = $gh_id;
				$data['createtime'] = $postArr['CreateTime'];
				$data['sessionfrom'] = $postArr['SessionFrom'];
				pdo_insert(BEST_XCXFANSKEFU, $data);
			}
			if (!empty($has) && !empty($fansnickname) && !empty($fansavatar) && ($fansnickname != $has['fansnickname'] || $fansavatar != $has['fansavatar'])) {
				$dataup['fansnickname'] = $fansnickname;
				$dataup['fansavatar'] = $fansavatar;
				pdo_update(BEST_XCXFANSKEFU, $dataup, array("id" => $has['id']));
			}
			$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_XCXFANSKEFU) . " WHERE kefuopenid = '{$kefuopenid}' AND fansopenid = '{$fansopenid}'");
			pdo_update(BEST_XCXFANSKEFU, array("nowkefu" => 1), array("id" => $fanskefu['id']));
			if ($cservice['autoreply'] != '' && $fanskefu['lastcon'] != '') {
				$this->addxcxchat2($fanskefu['fansopenid'], $cservice['autoreply'], 'text', $fanskefu['gh_id']);
			}
		} else {
			$this->addxcxchat2($fansopenid, '客服不在线哦~~', 'text', $xcx['gh_id']);
		}
	}
	public function doMobileFinishjdxcx()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$dataupfk['nowkefu'] = 0;
		pdo_update(BEST_XCXFANSKEFU, $dataupfk, array("id" => $fkid));
		$ssopenid = $_GPC['ssopenid'];
		$resArr['url'] = $this->createMobileUrl('mychatxcx', array("ssopenid" => $ssopenid));
		$resArr['error'] = 0;
		$resArr['msg'] = '结束接待成功';
		echo json_encode($resArr);
		exit;
	}
	public function get_xcx_accessToken($appid, $appsecret)
	{
		$url = 'https://api.weixin.qq.com/cgi-bin/token?grant_type=client_credential&appid=' . $appid . '&secret=' . $appsecret;
		$result = $this->curl_get_https($url);
		$res = json_decode($result, true);
		if ($res) {
			return $res['access_token'];
		} else {
			return 'error';
		}
	}
	public function curl_get_https($url)
	{
		$curl = curl_init();
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_HEADER, 0);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_SSL_VERIFYPEER, false);
		curl_setopt($curl, CURLOPT_SSL_VERIFYHOST, true);
		$tmpInfo = curl_exec($curl);
		curl_close($curl);
		return $tmpInfo;
	}
	public function curl_post($url, $data = array())
	{
		$ch = curl_init();
		curl_setopt($ch, CURLOPT_URL, $url);
		curl_setopt($ch, CURLOPT_SSL_VERIFYPEER, FALSE);
		curl_setopt($ch, CURLOPT_SSL_VERIFYHOST, FALSE);
		if (!empty($data)) {
			curl_setopt($ch, CURLOPT_POST, 1);
			curl_setopt($ch, CURLOPT_POSTFIELDS, $data);
		}
		curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($ch, CURLOPT_FOLLOWLOCATION, 1);
		curl_setopt($ch, CURLOPT_TIMEOUT, 20);
		$rs = curl_exec($ch);
		if (curl_errno($ch)) {
			$s = '{"success": false,"msg":"' . curl_error($ch) . '" }';
			return $s;
		} else {
			curl_close($ch);
			return $rs;
		}
	}
	public function curl_post2($url = "", $path = "")
	{
		$curl = curl_init();
		if (class_exists('CURLFile')) {
			curl_setopt($curl, CURLOPT_SAFE_UPLOAD, true);
			$data = array("media" => new CURLFile($path));
		} else {
			curl_setopt($curl, CURLOPT_SAFE_UPLOAD, false);
			$data = array("media" => '@' . $path);
		}
		curl_setopt($curl, CURLOPT_URL, $url);
		curl_setopt($curl, CURLOPT_POST, 1);
		curl_setopt($curl, CURLOPT_POSTFIELDS, $data);
		curl_setopt($curl, CURLOPT_RETURNTRANSFER, 1);
		curl_setopt($curl, CURLOPT_USERAGENT, 'TEST');
		$result = curl_exec($curl);
		$res = json_decode($result, true);
		return $res;
	}
	public function checkmain($url)
	{
		global $_GPC, $_W;
		$yzip = str_replace('https://', '', $url);
		$yzip = str_replace('http://', '', $yzip);
		$yzip = str_replace('/', '', $yzip);
		$ip = gethostbyname($yzip);
		/*$con = $this->get_url_content(BEST_DOMAIN);
		$con = json_decode($con, true);
		if (!empty($ip)) {
			if (in_array($url, $con) || in_array($ip, $con)) {
				$cando = 1;
			} else {
				$cando = 1;//0
			}
		} else {
			if (in_array($url, $con)) {
				$cando = 1;
			} else {
				$cando = 1;//0
			}
		}*/
		if ($_W['account']['type_name'] != '公众号' && $_W['account']['type_name'] != '微信公众号') {
			$cando = 1;//0
		}
        $cando = 1;
		return $cando;
	}
	public function get_url_content($url)
	{
		if (function_exists('curl_init')) {
			$ch = curl_init();
			$timeout = 30;
			curl_setopt($ch, CURLOPT_URL, $url);
			curl_setopt($ch, CURLOPT_RETURNTRANSFER, 1);
			curl_setopt($ch, CURLOPT_CONNECTTIMEOUT, $timeout);
			$file_contents = curl_exec($ch);
			curl_close($ch);
		} else {
			$is_auf = ini_get('allow_url_fopen') ? true : false;
			if ($is_auf) {
				$file_contents = file_get_contents($url);
			}
		}
		return $file_contents;
	}
	public function randCharNumber($length)
	{
		$str = '0123456789abcdefghijklmnopqrstuvwxyzABCDEFGHIJKLMNOPQRSTUVWXYZ';
		$tmp = '';
		for ($i = 0; $i < $length; $i++) {
			$tmp .= $str[mt_rand(0, 61)];
		}
		return $tmp;
	}
	public function doWebYouhua()
	{
		global $_GPC, $_W;
		$days = intval($_GPC['days']);
		$days2 = intval($_GPC['days2']);
		$count = $count2 = 0;
		if ($days > 0) {
			$endtime = TIMESTAMP - $days * 24 * 3600;
			$count = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND time <= {$endtime}");
			$list = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND time <= {$endtime}");
			foreach ($list as $k => $v) {
				if ($v['type'] != 1 && $v['type'] != 2) {
					$this->doQiuniudel($v['content']);
				}
				pdo_delete(BEST_CHAT, array("id" => $v['id']));
			}
		}
		if ($days2 > 0) {
			$endtime2 = TIMESTAMP - $days2 * 24 * 3600;
			$count2 = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND time <= {$endtime2}");
			$list2 = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND time <= {$endtime2}");
			foreach ($list2 as $k => $v) {
				if ($v['type'] == 3) {
					$this->doQiuniudel($v['content']);
				}
				pdo_delete(BEST_GROUPCONTENT, array("id" => $v['id']));
			}
		}
		$resArr['msg'] = '共删除' . $count . '条客服功能记录，' . $count2 . '条群聊功能记录。';
		echo json_encode($resArr);
		exit;
	}
	public function exportexcel($data = array(), $title = array(), $header, $footer, $filename = "report")
	{
		header('Content-type:application/octet-stream');
		header('Accept-Ranges:bytes');
		header('Content-type:application/vnd.ms-excel');
		header('Content-Disposition:attachment;filename=' . $filename . '.xls');
		header('Pragma: no-cache');
		header('Expires: 0');
		$header = iconv('UTF-8', 'GB2312', $header);
		echo $header;
		if (!empty($title)) {
			foreach ($title as $k => $v) {
				$title[$k] = iconv('UTF-8', 'GB2312', $v);
			}
			$title = implode('	', $title);
			echo "{$title}\r\n";
		}
		if (!empty($data)) {
			foreach ($data as $key => $val) {
				foreach ($val as $ck => $cv) {
					$data[$key][$ck] = iconv('UTF-8', 'GB2312', $cv);
				}
				$data[$key] = implode('	', $data[$key]);
			}
			echo implode('
', $data);
		}
		echo '
';
		$footer = iconv('UTF-8', 'GB2312', $footer);
		echo '

';
		echo $footer;
	}
	public function doWebCservice()
	{
		include_once ROOT_PATH . 'inc/web/cservice.php';
	}
	public function doWebGroup()
	{
		include_once ROOT_PATH . 'inc/web/group.php';
	}
	public function mkdirs($dir, $mode = 0777)
	{
		if (is_dir($dir) || @mkdir($dir, $mode)) {
			return TRUE;
		}
		if (!$this->mkdirs(dirname($dir), $mode)) {
			return FALSE;
		}
		return @mkdir($dir, $mode);
	}
	public function doWebCservicegroup()
	{
		global $_GPC, $_W;
		$operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';
		if ($operation == 'display') {
			if (!empty($_GPC['displayorder'])) {
				foreach ($_GPC['displayorder'] as $id => $displayorder) {
					pdo_update(BEST_CSERVICEGROUP, array("displayorder" => $displayorder), array("id" => $id));
				}
				message('客服组排序更新成功！', $this->createWebUrl('cservicegroup', array("op" => "display")), 'success');
			}
			$cservicegrouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} ORDER BY displayorder ASC");
			foreach ($cservicegrouplist as $k => $v) {
				$cservicegrouplist[$k]['servicegroupurl'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('groupchat', array("id" => $v['id'])));
				$cservicegrouplist[$k]['servicegroupurl2'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('groupchatzd', array("id" => $v['id'])));
			}
			include $this->template('web/cservicegroup');
		} elseif ($operation == 'post') {
			$id = intval($_GPC['id']);
			$othergroup = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND id != {$id}");
			if (!empty($id)) {
				$cservicegroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . ' WHERE id = :id AND weid = :weid', array(":id" => $id, ":weid" => $_W['uniacid']));
			}
			if (checksubmit('submit')) {
				if (empty($_GPC['name'])) {
					message('抱歉，请输入客服组名称！');
				}
				$data = array("weid" => $_W['uniacid'], "name" => trim($_GPC['name']), "typename" => trim($_GPC['typename']), "thumb" => $_GPC['thumb'], "displayorder" => intval($_GPC['displayorder']), "ishow" => intval($_GPC['ishow']), "sanbs" => trim($_GPC['sanbs']), "sanremark" => trim($_GPC['sanremark']), "bsid" => intval($_GPC['bsid']), "fid" => intval($_GPC['fid']), "notext" => trim($_GPC['notext']));
				if (!empty($id)) {
					pdo_update(BEST_CSERVICEGROUP, $data, array("id" => $id, "weid" => $_W['uniacid']));
				} else {
					pdo_insert(BEST_CSERVICEGROUP, $data);
				}
				message('操作成功！', $this->createWebUrl('cservicegroup', array("op" => "display")), 'success');
			}
			include $this->template('web/cservicegroup');
		} elseif ($operation == 'delete') {
			$id = intval($_GPC['id']);
			$cservicegroup = pdo_fetch('SELECT id FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE id = {$id}");
			if (empty($cservicegroup)) {
				message('抱歉，该客服组不存在或是已经被删除！', $this->createWebUrl('cservicegroup', array("op" => "display")), 'error');
			}
			pdo_delete(BEST_CSERVICEGROUP, array("id" => $id));
			pdo_delete(BEST_KEFUANDGROUP, array("groupid" => $id));
			message('删除客服组成功！', $this->createWebUrl('cservicegroup', array("op" => "display")), 'success');
		}
	}
	public function doMobileKefucenter()
	{
		include_once ROOT_PATH . 'inc/mobile/kefucenter.php';
	}
	public function doMobileKefulogin()
	{
		include_once ROOT_PATH . 'inc/mobile/kefulogin.php';
	}
	public function doMobileSearchkefus()
	{
		include_once ROOT_PATH . 'inc/mobile/searchkefus.php';
	}
	public function doMobileChosekefu()
	{
		global $_W, $_GPC;
		$iscservice = pdo_fetch('SELECT id FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$_W['fans']['from_user']}' AND ctype = 1");
		$nowtime = TIMESTAMP;
		$advlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_ADV) . " WHERE weid = '{$_W['uniacid']}' AND (endtime > {$nowtime} OR endtime = 0) AND groupid = 0 AND isdadi = 0 ORDER BY displayorder ASC");
		if (empty($advlist)) {
			$advlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_ADV) . " WHERE weid = '{$_W['uniacid']}' AND isdadi = 1 ORDER BY displayorder ASC");
		}
		$nowhour = intval(date('H', TIMESTAMP));
		$nowhouradd = $nowhour + 1;
		$condition = "weid = {$_W['uniacid']} AND ((iszx = 0 AND (\r\n\t\t\t(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR \r\n\t\t\t(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))\r\n\t\t)";
		$zhouji = date('w');
		if ($zhouji == '1') {
			$condition .= ' AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '2') {
			$condition .= ' AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '3') {
			$condition .= ' AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '4') {
			$condition .= ' AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '5') {
			$condition .= ' AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '6') {
			$condition .= ' AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '0') {
			$condition .= ' AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		$condition .= ' OR (iszx = 1 AND isrealzx = 1))';
		if ($this->module['config']['suiji'] == 1) {
			$orderby = ' ORDER BY rand()';
		} else {
			$orderby = ' ORDER BY displayorder ASC';
		}
		$cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . ' WHERE ' . $condition . $orderby);
		foreach ($cservicelist as $k => $v) {
			$kefuandgroup = pdo_fetch('SELECT id FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE kefuid = {$v['id']}");
			if (!empty($kefuandgroup)) {
				unset($cservicelist[$k]);
			}
		}
		$cservicegrouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND ishow = 1 AND fid = 0 ORDER BY displayorder ASC");
		$this->module['config']['shareurl'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('chosekefu'));
		if ($this->module['config']['chosekefutem'] == 0) {
			include $this->template('chosekefu');
		}
		if ($this->module['config']['chosekefutem'] == 1 || $this->module['config']['chosekefutem'] == 2) {
			include $this->template('chosekefu2');
		}
	}
	public function doMobileSanchat()
	{
		include_once ROOT_PATH . 'inc/mobile/sanchat.php';
	}
	public function doMobileGroupchatzd()
	{
		global $_W, $_GPC;
		if (empty($_W['fans']['from_user'])) {
			if (empty($_COOKIE['kflatitude'])) {
				$ipurl = 'https://apis.map.qq.com/ws/location/v1/ip?ip=' . $_W['clientip'] . '&key=' . $this->module['config']['mapkey'];
				$ipres = file_get_contents($ipurl);
				$ipres = json_decode($ipres, true);
				$latitude = $ipres['result']['location']['lat'] . random(4, 1);
				$longitude = $ipres['result']['location']['lng'] . random(4, 1);
				setcookie('kflatitude', $latitude, time() + 3600 * 24 * 7);
				setcookie('kflongitude', $longitude, time() + 3600 * 24 * 7);
			} else {
				$latitude = $_COOKIE['kflatitude'];
				$longitude = $_COOKIE['kflongitude'];
			}
			$jiamistr = $this->get_lang() . $this->browse_info() . $this->get_os() . $latitude . $longitude;
			$openid = md5($jiamistr);
		} else {
			$openid = $_W['fans']['from_user'];
		}
		$id = intval($_GPC['id']);
		$cservicegroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$id}");
		$kefuandgroup = pdo_fetchall('SELECT kefuid FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE weid = {$_W['uniacid']} AND groupid = {$cservicegroup['id']}");
		$kefuids = array(0);
		foreach ($kefuandgroup as $k => $v) {
			$kefuids[] = $v['kefuid'];
		}
		$nowhour = intval(date('H', TIMESTAMP));
		$nowhouradd = $nowhour + 1;
		$condition = "weid = {$_W['uniacid']} AND ctype = 1 AND id in (" . implode(',', $kefuids) . ") AND iszx = 0 AND (\r\n\t\t\t(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR \r\n\t\t\t(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))\r\n\t\t)";
		$zhouji = date('w');
		if ($zhouji == '1') {
			$condition .= ' AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '2') {
			$condition .= ' AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '3') {
			$condition .= ' AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '4') {
			$condition .= ' AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '5') {
			$condition .= ' AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '6') {
			$condition .= ' AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		if ($zhouji == '0') {
			$condition .= ' AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0)';
		}
		$condition2 = "weid = {$_W['uniacid']} AND ctype = 1 AND id in ( " . implode(',', $kefuids) . ') AND iszx = 1 AND isrealzx = 1';
		$qudao = trim($_GPC['qudao']);
		$goodsid = intval($_GPC['goodsid']);
		$merch = intval($_GPC['merch']);
		if ($qudao == 'elapp' && $goodsid > 0) {
			$condition .= " AND (jhtext = '' OR jhtext = {$goodsid} OR jhtext = '{$goodsid}')";
			$condition2 .= " AND (jhtext = '' OR jhtext = {$goodsid} OR jhtext = '{$goodsid}')";
		}
		$cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . ' WHERE ' . $condition);
		$cservicelist2 = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . ' WHERE ' . $condition2);
		if (empty($cservicelist) && empty($cservicelist2)) {
			$message = $cservicegroup['notext'] == '' ? '暂没有客服哦' : $cservicegroup['notext'];
			include $this->template('error');
			exit;
		}
		$kefuopenids = '(';
		foreach ($cservicelist as $k => $v) {
			$kefuopenids .= '\'' . $v['content'] . '\',';
		}
		foreach ($cservicelist2 as $k => $v) {
			$kefuopenids .= '\'' . $v['content'] . '\',';
		}
		$kefuopenids .= '\'ddd\')';
		$hasjd = pdo_fetch('SELECT kefuopenid FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$openid}' AND kefuopenid in " . $kefuopenids . ' AND nowjd > 0');
		if (!empty($hasjd)) {
			$toopenid = $hasjd['kefuopenid'];
		} else {
			$cservice = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content != '{$openid}' AND ctype = 1 AND content in " . $kefuopenids . ' ORDER BY nowjdnum ASC LIMIT 1');
			$cservice = $cservice[0];
			$toopenid = $cservice['content'];
		}
		if (empty($toopenid)) {
			$message = $cservicegroup['notext'] == '' ? '暂没有客服哦' : $cservicegroup['notext'];
			include $this->template('error');
			exit;
		} else {
			header('Location: ' . $this->createMobileUrl('chat', array("toopenid" => $toopenid, "qudao" => $qudao, "goodsid" => $goodsid, "merch" => $merch)));
		}
	}
	public function doMobileGroupchat()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$id = intval($_GPC['id']);
		$qudao = trim($_GPC['qudao']);
		$goodsid = intval($_GPC['goodsid']);
		$merch = intval($_GPC['merch']);
		if ($qudao == 'elapp' && $goodsid > 0 && pdo_tableexists('elapp_shop_goods')) {
			$cservicegroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE sanbs = '{$qudao}' AND weid = {$_W['uniacid']} AND bsid = {$merch}");
		} else {
			$cservicegroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$id}");
		}
		$nowtime = TIMESTAMP;
		$advlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_ADV) . " WHERE weid = '{$_W['uniacid']}' AND (endtime > {$nowtime} OR endtime = 0) AND groupid = {$id} AND isdadi = 0 ORDER BY displayorder ASC");
		if (empty($advlist)) {
			$advlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_ADV) . " WHERE weid = '{$_W['uniacid']}' AND isdadi = 1 ORDER BY displayorder ASC");
		}
		$kefuandgroup = pdo_fetchall('SELECT kefuid FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE weid = {$_W['uniacid']} AND groupid = {$cservicegroup['id']}");
		$kefuids = array(0);
		foreach ($kefuandgroup as $k => $v) {
			$kefuids[] = $v['kefuid'];
		}
		$nowhour = intval(date('H', TIMESTAMP));
		$nowhouradd = $nowhour + 1;
		$condition = "weid = {$_W['uniacid']} AND id in (" . implode(',', $kefuids) . ") AND ((iszx = 0 AND (\r\n\t\t\t(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR \r\n\t\t\t(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))\r\n\t\t)";
		$zhouji = date('w');
		if ($zhouji == '1') {
			$condition .= ' AND ((day1 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '2') {
			$condition .= ' AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '3') {
			$condition .= ' AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '4') {
			$condition .= ' AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '5') {
			$condition .= ' AND ((day5 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '6') {
			$condition .= ' AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		if ($zhouji == '0') {
			$condition .= ' AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0))';
		}
		$condition .= ' OR (iszx = 1 AND isrealzx = 1))';
		if ($this->module['config']['suiji'] == 1) {
			$orderby = ' ORDER BY rand()';
		} else {
			$orderby = ' ORDER BY displayorder ASC';
		}
		if ($qudao == 'elapp' && $goodsid > 0) {
			$condition .= " AND (jhtext = '' OR jhtext = {$goodsid} OR jhtext = '{$goodsid}')";
		}
		$cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . ' WHERE ' . $condition . $orderby);
		$cservicegrouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND ishow = 1 AND fid = {$cservicegroup['id']} ORDER BY displayorder ASC");
		$this->module['config']['shareurl'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('groupchat', array("id" => $id, "qudao" => $qudao, "goodsid" => $goodsid, "merch" => $merch)));
		if ($this->module['config']['chosekefutem'] == 0) {
			include $this->template('groupchat');
		}
		if ($this->module['config']['chosekefutem'] == 1 || $this->module['config']['chosekefutem'] == 2) {
			include $this->template('groupchat2');
		}
	}
	public function doMobileGetchatbigimg()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$con = trim($_GPC['con']);
		$imglist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND weid = {$_W['uniacid']} AND (type = 3 OR type = 4) ORDER BY time ASC");
		$oneimg = pdo_fetch('SELECT id FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND content = '{$con}' AND weid = {$_W['uniacid']} AND (type = 3 OR type = 4)");
		$myid = $oneimg['id'];
		if (!empty($imglist)) {
			$imglistval = '';
			$nowindex = 0;
			foreach ($imglist as $k => $v) {
				if ($v['id'] == $myid) {
					$nowindex = $k;
				}
				$imglistval .= $v['content'] . ',';
			}
			$imglistval = substr($imglistval, 0, -1);
			$resArr['error'] = 0;
			$resArr['message'] = $imglistval;
			$resArr['index'] = $nowindex;
			echo json_encode($resArr);
			exit;
		} else {
			$resArr['error'] = 1;
			$resArr['message'] = '';
			echo json_encode($resArr);
			exit;
		}
	}
	public function doMobileGetchatbigimgxcx()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$con = trim($_GPC['con']);
		$imglist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_XCXCHAT) . " WHERE fkid = {$fkid} AND weid = {$_W['uniacid']} AND msgtype = 'image' ORDER BY time ASC");
		$oneimg = pdo_fetch('SELECT id FROM ' . tablename(BEST_XCXCHAT) . " WHERE fkid = {$fkid} AND content = '{$con}' AND weid = {$_W['uniacid']} AND msgtype = 'image'");
		$myid = $oneimg['id'];
		if (!empty($imglist)) {
			$imglistval = '';
			$nowindex = 0;
			foreach ($imglist as $k => $v) {
				if ($v['id'] == $myid) {
					$nowindex = $k;
				}
				$imglistval .= $v['content'] . ',';
			}
			$imglistval = substr($imglistval, 0, -1);
			$resArr['error'] = 0;
			$resArr['message'] = $imglistval;
			$resArr['index'] = $nowindex;
			echo json_encode($resArr);
			exit;
		} else {
			$resArr['error'] = 1;
			$resArr['message'] = '';
			echo json_encode($resArr);
			exit;
		}
	}
	public function doMobileGetgroupchatbigimg()
	{
		global $_W, $_GPC;
		$groupid = intval($_GPC['groupid']);
		$myin = pdo_fetch('SELECT intime FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE groupid = {$groupid} AND openid = '{$_W['fans']['from_user']}'");
		$imglist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE weid = {$_W['uniacid']} AND groupid = {$groupid} AND type = 3 AND time >= {$myin['intime']} ORDER BY time ASC");
		$con = trim($_GPC['con']);
		$oneimg = pdo_fetch('SELECT id FROM ' . tablename(BEST_GROUPCONTENT) . " WHERE groupid = {$groupid} AND content = '{$con}' AND weid = {$_W['uniacid']} AND type = 3");
		$myid = $oneimg['id'];
		if (!empty($imglist)) {
			$imglistval = '';
			$nowindex = 0;
			foreach ($imglist as $k => $v) {
				if ($v['id'] == $myid) {
					$nowindex = $k;
				}
				$imglistval .= $v['content'] . ',';
			}
			$imglistval = substr($imglistval, 0, -1);
			$resArr['error'] = 0;
			$resArr['message'] = $imglistval;
			$resArr['index'] = $nowindex;
			echo json_encode($resArr);
			exit;
		} else {
			$resArr['error'] = 1;
			$resArr['message'] = '';
			echo json_encode($resArr);
			exit;
		}
	}
	public function doMobileGetsanchatbigimg()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$fkid2 = intval($_GPC['fkid2']);
		$myid = intval($_GPC['myid']);
		$imglist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_SANCHAT) . " WHERE (sanfkid = {$fkid} OR sanfkid = {$fkid2}) AND weid = {$_W['uniacid']} AND type = 2 ORDER BY time ASC");
		if (!empty($imglist)) {
			$imglistval = '';
			$nowindex = 0;
			foreach ($imglist as $k => $v) {
				if ($v['id'] == $myid) {
					$nowindex = $k;
				}
				$imglistval .= $v['content'] . ',';
			}
			$imglistval = substr($imglistval, 0, -1);
			$resArr['error'] = 0;
			$resArr['message'] = $imglistval;
			$resArr['index'] = $nowindex;
			echo json_encode($resArr);
			exit;
		} else {
			$resArr['error'] = 1;
			$resArr['message'] = '';
			echo json_encode($resArr);
			exit;
		}
	}
	public function doMobileWenzhangdetail()
	{
		global $_W, $_GPC;
		$id = intval($_GPC['id']);
		$wenzhang = pdo_fetch('SELECT * FROM ' . tablename(BEST_WENZHANG) . " WHERE weid = {$_W['uniacid']} AND id = {$id}");
		if (empty($wenzhang)) {
			$message = '不存在该项目！';
			include $this->template('error');
			exit;
		}
		$wenzhang['des'] = htmlspecialchars_decode($wenzhang['des']);
		include $this->template('wenzhangdetail');
	}
	public function doMobileChat()
	{
		include_once ROOT_PATH . 'inc/mobile/echat.php';
	}
	public function updatenowjdnum()
	{
		global $_W, $_GPC;
		$cservicelist = pdo_fetchall('SELECT id,content FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND ctype = 1");
		foreach ($cservicelist as $k => $v) {
			$nowjdnum = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND nowjd > 0 AND kefuopenid = '{$v['content']}'");
			if (!empty($nowjdnum)) {
				$nowjdnum = $nowjdnum < 0 ? 0 : $nowjdnum;
				$data['nowjdnum'] = $nowjdnum;
				pdo_update(BEST_CSERVICE, $data, array("id" => $v['id']));
			}
		}
	}
	public function doMobileChatajax()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE id = {$fkid}");
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND weid = {$_W['uniacid']} AND fansdel = 0");
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		if ($total > $pindex * $psize) {
			$tolimit = $psize;
		} else {
			$tolimit = $psize - ($pindex * $psize - $total);
		}
		$chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND weid = {$_W['uniacid']} AND fansdel = 0 ORDER BY time ASC LIMIT " . $nowjl . ',' . $tolimit);
		$chatcontime = 0;
		foreach ($chatcon as $k => $v) {
			if ($v['time'] - $chatcontime > 7200) {
				$chatcon[$k]['time'] = $v['time'];
			} else {
				$chatcon[$k]['time'] = '';
			}
			$chatcontime = $v['time'];
			$chatcon[$k]['content'] = $this->guolv($v['content']);
			$chatcon[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcon[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $chatcon[$k]['content'], $array2);
			if (!empty($array2[0]) && ($v['type'] == 1 || $v['type'] == 2)) {
				if ($v['isjqr'] == 1 || $v['istuwen'] == 1) {
					$chatcon[$k]['content'] = htmlspecialchars_decode($v['content']);
				} else {
					foreach ($array2[0] as $kk => $vv) {
						if (!empty($vv)) {
							$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
							$chatcon[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $chatcon[$k]['content']);
						}
					}
				}
			}
		}
		$html = '';
		foreach ($chatcon as $k => $v) {
			$htmltime = !empty($v['time']) ? '<div class="time text-c">' . date('Y-m-d H:i:s', $v['time']) . '</div>' : '';
			if ($v['openid'] != $hasfanskefu['fansopenid']) {
				$class = 'left';
				$avatar = $hasfanskefu['kefuavatar'];
			} else {
				$class = 'right';
				$avatar = $hasfanskefu['fansavatar'];
			}
			if ($v['type'] == 3 || $v['type'] == 4) {
				$chatconhtml = '<div class="concon"><img src="' . $v['content'] . '" class="sssbbb" /></div>';
			} else {
				if ($v['type'] == 5 || $v['type'] == 6) {
					if ($v['hasyuyindu'] == 0 && $hasfanskefu['kefuopenid'] == $v['toopenid']) {
						$weidu = '<span class="weidu">未读</span>';
					} else {
						$weidu = '';
					}
					$chatconhtml = '<div class="concon voiceplay flex" data-con="' . $chatcon[$k]['content'] . '" data-id="' . $chatcon[$k]['id'] . '">
									<img src="' . NEWSTATIC_ROOT . '/icon/voice2.png" class="voice2" />
									' . $weidu . '
									<div class="flex1"></div>
								</div>';
				} else {
					if ($v['type'] == 7) {
						$add_arr = explode(',', $v['content']);
						$chatconhtml = '<div class="concon toaddress flex" data-lat="' . $add_arr[0] . '" data-lng="' . $add_arr[1] . '" data-name="' . $add_arr[2] . '" data-address="' . $add_arr[3] . '">
								<img src="' . NEWSTATIC_ROOT . '/icon/map.png" class="map" />
								<div class="mapadd">' . $add_arr[3] . '</div>
								<div class="flex1"></div>
							</div>';
					} else {
						$chatconhtml = '<div class="concon">' . $v['content'] . '</div>';
					}
				}
			}
			$html .= $htmltime . '<div class="' . $class . ' flex">
									<img src="' . $avatar . '" class="avatar" />
									<div class="con flex flex1">
										<div class="triangle-' . $class . '"></div>
										' . $chatconhtml . '
										<div class="flex1"></div>
									</div>
								</div>';
		}
		echo $html;
		exit;
	}
	public function doMobileAddbiaoqian()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$ssopenid = $_GPC['ssopenid'];
		if ($ssopenid != '' && empty($openid)) {
			$openid = $ssopenid;
		}
		if (empty($openid)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '未获取到您的客服信息！';
			echo json_encode($resArr);
			exit;
		}
		$name = trim($_GPC['content']);
		if (empty($name)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请填写标签内容！';
			echo json_encode($resArr);
			exit;
		}
		$realname = trim($_GPC['realname']);
		$telphone = trim($_GPC['telphone']);
		$toopenid = trim($_GPC['toopenid']);
		$has = pdo_fetch('SELECT * FROM ' . tablename(BEST_BIAOQIAN) . " WHERE kefuopenid = '{$openid}' AND fensiopenid = '{$toopenid}' AND weid = {$_W['uniacid']}");
		if ($has) {
			pdo_update(BEST_BIAOQIAN, array("name" => $name, "realname" => $realname, "telphone" => $telphone), array("kefuopenid" => $openid, "fensiopenid" => $toopenid, "weid" => $_W['uniacid']));
		} else {
			$data['weid'] = $_W['uniacid'];
			$data['kefuopenid'] = $openid;
			$data['fensiopenid'] = $toopenid;
			$data['name'] = $name;
			$data['realname'] = $realname;
			$data['telphone'] = $telphone;
			pdo_insert(BEST_BIAOQIAN, $data);
		}
		$resArr['error'] = 0;
		$resArr['msg'] = '恭喜你添加用户标签成功！';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileAddpingjia()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		if (empty($openid)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请在微信端评价！';
			echo json_encode($resArr);
			exit;
		}
		$pingtype = intval($_GPC['pingtype']);
		if ($pingtype <= 0) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请选择评价类型！';
			echo json_encode($resArr);
			exit;
		}
		$toopenid = trim($_GPC['toopenid']);
		$has = pdo_fetch('SELECT * FROM ' . tablename(BEST_PINGJIA) . " WHERE kefuopenid = '{$toopenid}' AND fensiopenid = '{$openid}' AND weid = {$_W['uniacid']}");
		if ($has) {
			$data['pingtype'] = $pingtype;
			$data['content'] = $_GPC['content'];
			$data['time'] = TIMESTAMP;
			pdo_update(BEST_PINGJIA, $data, array("kefuopenid" => $toopenid, "fensiopenid" => $openid, "weid" => $_W['uniacid']));
		} else {
			$data['weid'] = $_W['uniacid'];
			$data['kefuopenid'] = $toopenid;
			$data['fensiopenid'] = $openid;
			$data['pingtype'] = $pingtype;
			$data['time'] = TIMESTAMP;
			$data['content'] = $_GPC['content'];
			pdo_insert(BEST_PINGJIA, $data);
		}
		$resArr['error'] = 0;
		$resArr['msg'] = '恭喜你评价成功！';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileUpdatefans()
	{
		global $_W, $_GPC;
		$toopenid = trim($_GPC['toopenid']);
		$fklast = pdo_fetchall('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$toopenid}'");
		$account_api = WeAccount::create();
		$access_token = $account_api->getAccessToken();
		$url = 'https://api.weixin.qq.com/cgi-bin/user/info?access_token=' . $access_token . '&openid=' . $toopenid . '&lang=zh_CN';
		$response = ihttp_get($url);
		$response = json_decode($response['content'], true);
		if ($response['subscribe'] == 1) {
			$dataup['fansavatar'] = $response['headimgurl'];
			foreach ($fklast as $k => $v) {
				pdo_update(BEST_FANSKEFU, $dataup, array("id" => $v['id']));
			}
			$resArr['message'] = '更新成功！';
			$resArr['error'] = 0;
		} else {
			$resArr['error'] = 1;
			$resArr['message'] = '粉丝未关注公众号，不能同步！';
		}
		echo json_encode($resArr, true);
		exit;
	}
	public function doMobileJcbd()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND id = {$fkid}");
		if (empty($fanskefu)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '不存在该记录！';
			echo json_encode($resArr);
			exit;
		}
		$dataup['bdopenid'] = '';
		pdo_update(BEST_FANSKEFU, $dataup, array("id" => $fkid));
		$resArr['error'] = 0;
		$resArr['msg'] = '解除绑定成功！';
		echo json_encode($resArr, true);
		exit;
	}
	public function doMobileZhuanjie()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		if (empty($openid)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请在微信浏览器中打开！';
			echo json_encode($resArr);
			exit;
		}
		$toopenid = trim($_GPC['toopenid']);
		if (empty($toopenid)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '获取用户数据失败！';
			echo json_encode($resArr);
			exit;
		}
		$content = trim($_GPC['content']);
		if (empty($content)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请选择要转接的客服！';
			echo json_encode($resArr);
			exit;
		}
		$hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$toopenid}' AND kefuopenid = '{$content}'");
		$zhuanjiekefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$content}'");
		if (empty($hasfanskefu)) {
			$datafanskefu['weid'] = $_W['uniacid'];
			$datafanskefu['fansopenid'] = $toopenid;
			$datafanskefu['kefuopenid'] = $content;
			$account_api = WeAccount::create();
			$fansuser = $account_api->fansQueryInfo($toopenid);
			if (empty($fansuser)) {
				$datafanskefu['fansavatar'] = tomedia($this->module['config']['defaultavatar']);
				$datafanskefu['fansnickname'] = '匿名用户';
			} else {
				$datafanskefu['fansavatar'] = empty($fansuser['headimgurl']) ? tomedia($this->module['config']['defaultavatar']) : $fansuser['headimgurl'];
				$datafanskefu['fansnickname'] = empty($fansuser['nickname']) ? '匿名用户' : $fansuser['nickname'];
			}
			$datafanskefu['kefuavatar'] = tomedia($zhuanjiekefu['thumb']);
			$datafanskefu['kefunickname'] = $zhuanjiekefu['name'];
			pdo_insert(BEST_FANSKEFU, $datafanskefu);
			$fkid = pdo_insertid();
		} else {
			$fkid = $hasfanskefu['id'];
		}
		$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE id = {$fkid}");
		pdo_update(BEST_FANSKEFU, array("nowjd" => 0), array("fansopenid" => $toopenid));
		pdo_update(BEST_FANSKEFU, array("nowjd" => 1), array("id" => $fkid));
		$datachat['weid'] = $_W['uniacid'];
		$datachat['fkid'] = $fkid;
		$datachat['openid'] = $content;
		$datachat['toopenid'] = $toopenid;
		$datachat['content'] = '<span class="red">系统提醒：</span><span class="hui">已转接至' . $zhuanjiekefu['name'] . '为您继续提供服务！</span>';
		$datachat['nickname'] = $zhuanjiekefu['name'];
		$datachat['avatar'] = tomedia($zhuanjiekefu['thumb']);
		$datachat['type'] = 1;
		$datachat['time'] = TIMESTAMP;
		pdo_insert(BEST_CHAT, $datachat);
		$tplcon = '已转接至' . $zhuanjiekefu['name'] . '！';
		$senddata = array("openid" => $content, "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('servicechat', array("toopenid" => $toopenid))), "first" => $tplcon, "keyword1" => $zhuanjiekefu['name'], "wherefrom" => 1);
		$this->sendtplmsg($senddata);
		$resArr['error'] = 0;
		$resArr['toopenid'] = $content;
		$resArr['msg'] = '转接成功';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileServicechat()
	{
		include_once ROOT_PATH . 'inc/mobile/eservicechat.php';
	}
	public function doMobileFinishjd()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$dataupfk['nowjd'] = 0;
		$dataupfk['jdtime'] = 0;
		pdo_update(BEST_FANSKEFU, $dataupfk, array("id" => $fkid));
		$id = intval($_GPC['id']);
		$cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND id = {$id}");
		$dataupkefu['nowfkid'] = 0;
		$dataupkefu['nowjdnum'] = $cservice['nowjdnum'] - 1;
		pdo_update(BEST_CSERVICE, $dataupkefu, array("id" => $cservice['id']));
		$resArr['url'] = $this->createMobileUrl('kefucenter');
		$resArr['error'] = 0;
		$resArr['msg'] = '结束会话成功';
		echo json_encode($resArr);
		exit;
	}
	public function doMobileServicechatajax()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE id = {$fkid}");
		$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND kefudel = 0");
		$page = intval($_GPC['page']);
		$pindex = max(1, $page);
		$psize = 10;
		$allpage = ceil($total / $psize) + 1;
		$nowjl = $total - $pindex * $psize;
		if ($nowjl < 0) {
			$nowjl = 0;
		}
		if ($total > $pindex * $psize) {
			$tolimit = $psize;
		} else {
			$tolimit = $psize - ($pindex * $psize - $total);
		}
		$chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$fkid} AND kefudel = 0 ORDER BY time ASC LIMIT " . $nowjl . ',' . $tolimit);
		$chatcontime = 0;
		foreach ($chatcon as $k => $v) {
			if ($v['time'] - $chatcontime > 7200) {
				$chatcon[$k]['time'] = $v['time'];
			} else {
				$chatcon[$k]['time'] = '';
			}
			$chatcontime = $v['time'];
			$chatcon[$k]['content'] = $this->guolv($v['content']);
			$chatcon[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcon[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $chatcon[$k]['content'], $array2);
			if (!empty($array2[0]) && ($v['type'] == 1 || $v['type'] == 2)) {
				if ($v['isjqr'] == 1 || $v['istuwen'] == 1) {
					$chatcon[$k]['content'] = htmlspecialchars_decode($v['content']);
				} else {
					foreach ($array2[0] as $kk => $vv) {
						if (!empty($vv)) {
							$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
							$chatcon[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $chatcon[$k]['content']);
						}
					}
				}
			}
		}
		$html = '';
		foreach ($chatcon as $k => $v) {
			$htmltime = !empty($v['time']) ? '<div class="time text-c">' . date('Y-m-d H:i:s', $v['time']) . '</div>' : '';
			if ($v['openid'] != $hasfanskefu['kefuopenid']) {
				$class = 'left';
				$avatar = $hasfanskefu['fansavatar'];
			} else {
				$class = 'right';
				$avatar = $hasfanskefu['kefuavatar'];
			}
			if ($v['type'] == 3 || $v['type'] == 4) {
				$chatconhtml = '<div class="concon"><img src="' . $v['content'] . '" class="sssbbb" /></div>';
			} else {
				if ($v['type'] == 5 || $v['type'] == 6) {
					if ($v['hasyuyindu'] == 0 && $hasfanskefu['kefuopenid'] == $v['toopenid']) {
						$weidu = '<span class="weidu">未读</span>';
					} else {
						$weidu = '';
					}
					$chatconhtml = '<div class="concon voiceplay flex" data-con="' . $chatcon[$k]['content'] . '" data-id="' . $chatcon[$k]['id'] . '">
									<img src="' . NEWSTATIC_ROOT . '/icon/voice2.png" class="voice2" />
									' . $weidu . '
									<div class="flex1"></div>
								</div>';
				} else {
					if ($v['type'] == 7) {
						$add_arr = explode(',', $chatcon[$k]['content']);
						$chatconhtml = '<div class="concon toaddress flex" data-lat="' . $add_arr[0] . '" data-lng="' . $add_arr[1] . '" data-name="' . $add_arr[2] . '" data-address="' . $add_arr[3] . '">
								<img src="' . NEWSTATIC_ROOT . '/icon/map.png" class="map" />
								<div class="mapadd">' . $add_arr[3] . '</div>
								<div class="flex1"></div>
							</div>';
					} else {
						$chatconhtml = '<div class="concon">' . $v['content'] . '</div>';
					}
				}
			}
			$html .= $htmltime . '<div class="' . $class . ' flex">
									<img src="' . $avatar . '" class="avatar" />
									<div class="con flex flex1">
										<div class="triangle-' . $class . '"></div>
										' . $chatconhtml . '
										<div class="flex1"></div>
									</div>
								</div>';
		}
		echo $html;
		exit;
	}
	public function doMobilezhuizong()
	{
		include_once ROOT_PATH . 'inc/mobile/zhuizong.php';
	}
	public function doMobileAllshare()
	{
		global $_W, $_GPC;
		include_once ROOT_PATH . 'qqface.php';
		$openid = $_W['fans']['from_user'];
		if (empty($openid)) {
			$message = '请在微信浏览器中打开！';
			include $this->template('error');
			exit;
		}
		$cservice = pdo_fetch('SELECT id,thumb FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$openid}'");
		if (empty($cservice)) {
			$message = '您不是客服！';
			include $this->template('error');
			exit;
		}
		if ($this->module['config']['issharemsg'] == 0) {
			$message = '暂未开通客户记录共享功能，如需要请联系管理员在基本设置中开启！';
			include $this->template('error');
			exit;
		}
		if ($this->module['config']['sharetype'] == 1) {
			$isingroup = pdo_fetch('SELECT id FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE kefuid = {$cservice['id']}");
			if (empty($isingroup)) {
				$message = '系统开启了客服组内共享功能，您不属于任何客服组！';
				include $this->template('error');
				exit;
			}
		}
		$toopenid = trim($_GPC['toopenid']);
		if ($this->module['config']['sharetype'] == 0) {
			$allfanskefu = pdo_fetchall('SELECT id FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$toopenid}'");
			$fkids = '(';
			foreach ($allfanskefu as $k => $v) {
				$fkids .= $v['id'] . ',';
			}
			$fkids = substr($fkids, 0, -1) . ')';
		} else {
			$allingroup = pdo_fetchall('SELECT groupid FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE kefuid = {$cservice['id']}");
			$allingrouparr = array();
			foreach ($allingroup as $kk => $vv) {
				$allingrouparr[] = $vv['groupid'];
			}
			$groupcservice = pdo_fetchall('SELECT b.content FROM ' . tablename(BEST_KEFUANDGROUP) . ' as a,' . tablename(BEST_CSERVICE) . " as b WHERE a.weid = {$_W['uniacid']} AND a.groupid in (" . implode(',', $allingrouparr) . ') AND a.kefuid = b.id AND b.ctype = 1');
			$groupcservicearr = '(';
			foreach ($groupcservice as $kk => $vv) {
				$groupcservicearr .= '\'' . $vv['content'] . '\',';
			}
			$groupcservicearr = substr($groupcservicearr, 0, -1) . ')';
			$allfanskefu = pdo_fetchall('SELECT id FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$toopenid}' AND kefuopenid in {$groupcservicearr}");
			$fkids = '(';
			foreach ($allfanskefu as $k => $v) {
				$fkids .= $v['id'] . ',';
			}
			$fkids = substr($fkids, 0, -1) . ')';
		}
		$chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND fkid in {$fkids} AND kefudel = 0 AND fansdel = 0 ORDER BY time ASC");
		$timestamp = TIMESTAMP;
		foreach ($chatcon as $k => $v) {
			if ($v['openid'] != $openid) {
				$chatcon[$k]['class'] = 'left';
				$chatcon[$k]['avatar'] = $v['avatar'];
			} else {
				$chatcon[$k]['class'] = 'right';
				$chatcon[$k]['avatar'] = tomedia($cservice['thumb']);
			}
			$chatcon[$k]['content'] = $this->guolv($v['content']);
			$chatcon[$k]['content'] = qqface_convert_html($chatcon[$k]['content']);
			$chatcon[$k]['content'] = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcon[$k]['content']);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $chatcon[$k]['content'], $array2);
			if (!empty($array2[0]) && ($v['type'] == 1 || $v['type'] == 2)) {
				if ($v['isjqr'] == 1 || $v['istuwen'] == 1) {
					$chatcon[$k]['content'] = htmlspecialchars_decode($v['content']);
				} else {
					foreach ($array2[0] as $kk => $vv) {
						if (!empty($vv) && strpos($vv, 'https://res.wx.qq.com') === false) {
							$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
							$chatcon[$k]['content'] = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $chatcon[$k]['content']);
						}
					}
				}
			}
			if ($v['type'] == 5 || $v['type'] == 6) {
				$donetime = $timestamp - $v['time'];
				if ($donetime >= 24 * 3600 * 3) {
					unset($chatcon[$k]);
				}
			}
		}
		$imglist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND fkid in {$fkids} AND (type = 3 OR type = 4) ORDER BY time DESC");
		include $this->template('allshare');
	}
	public function doMobileAddgroupchat()
	{
		global $_W, $_GPC;
		if (!checksubmit('submit')) {
			exit;
		}
		include_once ROOT_PATH . 'emoji/emoji.php';
		$groupid = intval($_GPC['groupid']);
		$group = pdo_fetch('SELECT * FROM ' . tablename(BEST_GROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$groupid}");
		$isgmember = pdo_fetch('SELECT id FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND openid = '{$_W['fans']['from_user']}' AND groupid = {$groupid}");
		if (empty($isgmember)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '您不是群成员！';
			echo json_encode($resArr);
			exit;
		}
		if ($group['jinyan'] == 1 && $_W['fans']['from_user'] != $group['admin']) {
			$resArr['error'] = 1;
			$resArr['msg'] = '该群只能管理员发言！';
			echo json_encode($resArr);
			exit;
		}
		$chatcontent = trim($_GPC['content']);
		if (empty($chatcontent)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请输入对话内容！';
			echo json_encode($resArr);
			exit;
		}
		$chatcontent = emoji_docomo_to_unified($chatcontent);
		$chatcontent = emoji_unified_to_html($chatcontent);
		$data['openid'] = $_W['fans']['from_user'];
		$data['groupid'] = $groupid;
		$data['time'] = TIMESTAMP;
		$data['content'] = $chatcontent;
		$data['weid'] = $_W['uniacid'];
		$data['type'] = intval($_GPC['type']);
		$iscservice = pdo_fetch('SELECT name,thumb FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$data['openid']}'");
		if (!empty($iscservice)) {
			$data['avatar'] = tomedia($iscservice['thumb']);
			$data['nickname'] = $iscservice['name'];
		} else {
			$data['avatar'] = empty($_W['fans']['tag']['avatar']) ? tomedia($this->module['config']['defaultavatar']) : $_W['fans']['tag']['avatar'];
			$data['nickname'] = empty($_W['fans']['tag']['nickname']) ? '匿名用户' : str_replace('\'', '\'\'', $_W['fans']['tag']['nickname']);
		}
		pdo_insert(BEST_GROUPCONTENT, $data);
		if ($data['type'] == 3) {
			$tplcon = '图片消息';
		} elseif ($data['type'] == 5) {
			$tplcon = '语音消息';
		} else {
			if (strpos($data['content'], 'span class=')) {
				$tplcon = '表情消息';
			} else {
				$tplcon = $data['content'];
			}
		}
		$guotime = TIMESTAMP - $group['lasttime'];
		if ($this->module['config']['isgrouptplon'] == 1 && $group['isfs'] == 1 && $guotime > $this->module['config']['grouptplminute']) {
			$allgroupmember = pdo_fetchall('SELECT * FROM ' . tablename(BEST_GROUPMEMBER) . " WHERE weid = {$_W['uniacid']} AND isdel = 0 AND groupid = {$data['groupid']} AND openid != '{$data['openid']}' AND status = 1 AND txkaiguan = 1");
			foreach ($allgroupmember as $k => $v) {
				$senddata = array("openid" => $v['openid'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('groupchatdetail', array("groupid" => $data['groupid']))), "first" => $data['nickname'] . '在[' . $group['groupname'] . ']中发了新消息！', "keyword1" => $data['nickname']);
				$this->sendtplmsg($senddata);
			}
		}
		pdo_update(BEST_GROUP, array("lasttime" => TIMESTAMP), array("id" => $data['groupid']));
		$resArr['error'] = 0;
		$resArr['msg'] = '';
		$resArr['content'] = $this->doReplacecon($data['content'], $data['type']);
		$resArr['datetime'] = date('Y-m-d H:i:s', $data['time']);
		$resArr['nickname'] = $data['nickname'];
		$resArr['avatar'] = $data['avatar'];
		echo json_encode($resArr);
		exit;
	}
	public function doMobileXiaxian()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$type = trim($_GPC['type']);
		if ($type == 'fans') {
			$data['fszx'] = 0;
			$data['kefunotread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
		if ($type == 'kefu') {
			$data['kfzx'] = 0;
			$data['notread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
	}
	public function doMobileShangxian()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$type = trim($_GPC['type']);
		if ($type == 'fans') {
			$data['fszx'] = 1;
			$data['kefunotread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
		if ($type == 'kefu') {
			$data['kfzx'] = 1;
			$data['notread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
	}
	public function gettpldomain()
	{
		global $_W, $_GPC;
		return $this->module['config']['tpldomain'] != '' ? $this->module['config']['tpldomain'] : $_W['siteroot'];
	}
	public function doMobileAddchat()
	{
		global $_W, $_GPC;
		if (!checksubmit('submit')) {
			exit;
		}
		include_once ROOT_PATH . 'emoji/emoji.php';
		$chatcontent = trim($_GPC['content']);
		$lastcon = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcontent);
		if (empty($chatcontent)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请输入对话内容！';
			echo json_encode($resArr);
			exit;
		}
		$cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND ctype = 1 AND content = '{$_GPC['toopenid']}'");
		if ($cservice['iszx'] == 1) {
			if ($cservice['isrealzx'] == 0) {
				$notonlinemsg = !empty($cservice['notonline']) ? $cservice['notonline'] : '客服不在线哦！';
				$resArr['error'] = 1;
				$resArr['msg'] = $notonlinemsg;
				echo json_encode($resArr);
				exit;
			}
		} elseif ($cservice['lingjie'] == 1) {
			$nowhour = intval(date('H', TIMESTAMP));
			if ($nowhour + 1 > $cservice['endhour'] && $nowhour < $cservice['starthour']) {
				$notonlinemsg = !empty($cservice['notonline']) ? $cservice['notonline'] : '客服不在线哦！';
				$resArr['error'] = 1;
				$resArr['msg'] = $notonlinemsg;
				echo json_encode($resArr);
				exit;
			}
		} else {
			$nowhour = intval(date('H', TIMESTAMP));
			if ($nowhour < $cservice['starthour'] || $nowhour + 1 > $cservice['endhour']) {
				$notonlinemsg = !empty($cservice['notonline']) ? $cservice['notonline'] : '客服不在线哦！';
				$resArr['error'] = 1;
				$resArr['msg'] = $notonlinemsg;
				echo json_encode($resArr);
				exit;
			}
		}
		if ($cservice['isxingqi'] == 1) {
			$notonlinemsg = !empty($cservice['notonline']) ? $cservice['notonline'] : '客服不在线哦！';
			$zhouji = date('w');
			if ($zhouji == '1') {
				if ($cservice['day1'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '2') {
				if ($cservice['day2'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '3') {
				if ($cservice['day3'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '4') {
				if ($cservice['day4'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '5') {
				if ($cservice['day5'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '6') {
				if ($cservice['day6'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} elseif ($zhouji == '0') {
				if ($cservice['day7'] == 0) {
					$resArr['error'] = 1;
					$resArr['msg'] = $notonlinemsg;
					echo json_encode($resArr);
					exit;
				}
			} else {
				$resArr['error'] = 1;
				$resArr['msg'] = $notonlinemsg;
				echo json_encode($resArr);
				exit;
			}
		}
		$chatcontent = emoji_docomo_to_unified($chatcontent);
		$chatcontent = emoji_unified_to_html($chatcontent);
		$data['fkid'] = intval($_GPC['fkid']);
		$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND id = {$data['fkid']}");
		$data['openid'] = $fanskefu['fansopenid'];
		$data['nickname'] = $fanskefu['fansnickname'];
		$data['avatar'] = $fanskefu['fansavatar'];
		$ishei = pdo_fetch('SELECT id FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$data['openid']}' AND ishei = 1");
		if (!empty($ishei)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '您暂时不能咨询！';
			echo json_encode($resArr);
			exit;
		}
		$data['toopenid'] = trim($_GPC['toopenid']);
		$data['time'] = TIMESTAMP;
		$data['content'] = $chatcontent;
		$data['weid'] = $_W['uniacid'];
		$type = intval($_GPC['type']);
		$data['type'] = $type;
		if ($type == 3 || $type == 4) {
			$tplcon = $data['nickname'] . '发送了图片';
		} elseif ($type == 5 || $type == 6) {
			$tplcon = $data['nickname'] . '发送了语音';
		} elseif ($type == 7) {
			$tplcon = $data['nickname'] . '发送了位置';
		} elseif (strpos($data['content'], 'span class=')) {
			$tplcon = $data['nickname'] . '发送了表情';
		} else {
			$tplcon = $data['content'];
		}
		$tplcon = $this->guolv($tplcon);
		$tplcon = preg_replace('/<br\\s*?\\/??>/i', '', $tplcon);
		pdo_insert(BEST_CHAT, $data);
		$chatid = pdo_insertid();
		$zdhf = pdo_fetch('select * from ' . tablename(BEST_ZIDONGHUIFU) . " where weid = {$_W['uniacid']} AND ((title = '{$chatcontent}' AND type = 1) OR (INSTR('{$chatcontent}',title) AND type = 2)) ORDER BY paixu DESC");
		if (!empty($zdhf)) {
			$zdhf_kefuids = unserialize($zdhf['kefuids']);
			if ($zdhf['kefuids'] == '' || in_array($cservice['id'], $zdhf_kefuids)) {
				$resArr['jqr'] = 1;
				$jqrtime = $data['time'] + 1;
				$resArr['jqrtime'] = date('Y-m-d H:i:s', $jqrtime);
				$resArr['hftype'] = $zdhf['hftype'];
				if ($zdhf['hftype'] == 0) {
					$datajqr['content'] = $zdhf['content'];
					$resArr['jqrcontent'] = $zdhf['content'];
				}
				if ($zdhf['hftype'] == 2) {
					$resArr['jqrcontent'] = $datajqr['content'] = tomedia($zdhf['imgcon']);
				}
				if ($zdhf['hftype'] == 3) {
					$datajqr['content'] = $zdhf['allcon'];
					$resArr['jqrcontent'] = htmlspecialchars_decode($zdhf['allcon']);
				}
				$resArr['jqravatar'] = tomedia($cservice['thumb']);
				$datajqr['weid'] = $_W['uniacid'];
				$datajqr['fkid'] = intval($_GPC['fkid']);
				$datajqr['openid'] = trim($_GPC['toopenid']);
				$datajqr['toopenid'] = $data['openid'];
				$datajqr['time'] = $jqrtime;
				$datajqr['nickname'] = $cservice['name'];
				$datajqr['avatar'] = tomedia($cservice['thumb']);
				$datajqr['type'] = $zdhf['hftype'] == 0 || $zdhf['hftype'] == 3 ? 2 : 4;
				$datajqr['isjqr'] = 1;
				pdo_insert(BEST_CHAT, $datajqr);
			}
		}
		$guotime = TIMESTAMP - $fanskefu['lasttime'];
		if ($guotime > $this->module['config']['kefutplminute'] && $cservice['ispczx'] == 0) {
			$senddata = array("openid" => $data['toopenid'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('servicechat', array("ssopenid" => $data['toopenid'], "toopenid" => $data['openid']))), "first" => $tplcon, "keyword1" => $data['nickname']);
			$this->sendtplmsg($senddata);
		}
		if ($fanskefu['nowjd'] == 0) {
			$dataup_fanskefu['nowjd'] = 1;
			$dataup_fanskefu['jdtime'] = TIMESTAMP;
			$otherfanskefus = pdo_fetchall('SELECT id,kefuopenid FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND nowjd > 0 AND fansopenid = '{$openid}' AND kefuopenid != '{$cservice['content']}'");
			foreach ($otherfanskefus as $k => $v) {
				$dataotherfk['nowjd'] = 0;
				$dataotherfk['jdtime'] = 0;
				pdo_update(BEST_FANSKEFU, $dataotherfk, array("id" => $v['id']));
			}
			$this->updatenowjdnum();
		}
		$dataup_fanskefu['notread'] = $fanskefu['notread'] + 1;
		$dataup_fanskefu['guanlinum'] = $fanskefu['guanlinum'] + 1;
		$dataup_fanskefu['wherefrom'] = 0;
		$dataup_fanskefu['fansdel'] = 0;
		$dataup_fanskefu['kefudel'] = 0;
		$dataup_fanskefu['lastcon'] = $lastcon;
		$dataup_fanskefu['msgtype'] = $type;
		$dataup_fanskefu['lasttime'] = TIMESTAMP;
		pdo_update(BEST_FANSKEFU, $dataup_fanskefu, array("id" => $fanskefu['id']));
		$resArr['error'] = 0;
		$resArr['msg'] = '';
		$resArr['content'] = $this->doReplacecon($data['content'], $data['type'], $chatid);
		$resArr['datetime'] = date('Y-m-d H:i:s', $data['time']);
		$resArr['chatid'] = $chatid;
		echo json_encode($resArr);
		exit;
	}
	public function doMobileDonotread()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$type = trim($_GPC['type']);
		if ($type == 'fans') {
			$data['kefunotread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
		if ($type == 'kefu') {
			$data['notread'] = 0;
			pdo_update(BEST_FANSKEFU, $data, array("id" => $fkid));
		}
	}
	public function doMobileDonotreadxcx()
	{
		global $_W, $_GPC;
		$fkid = intval($_GPC['fkid']);
		$data['notread'] = 0;
		pdo_update(BEST_XCXFANSKEFU, $data, array("id" => $fkid));
	}
	public function doMobileAddchat2()
	{
		global $_W, $_GPC;
		if (!checksubmit('submit')) {
			exit;
		}
		include_once ROOT_PATH . 'emoji/emoji.php';
		$chatcontent = trim($_GPC['content']);
		$kefulastcon = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $chatcontent);
		if (empty($chatcontent)) {
			$resArr['error'] = 1;
			$resArr['msg'] = '请输入对话内容！';
			echo json_encode($resArr);
			exit;
		}
		$chatcontent = emoji_docomo_to_unified($chatcontent);
		$chatcontent = emoji_unified_to_html($chatcontent);
		$data['openid'] = $_W['fans']['from_user'];
		$touser = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$data['openid']}'");
		$data['nickname'] = $touser['name'];
		$data['avatar'] = tomedia($touser['thumb']);
		$data['toopenid'] = trim($_GPC['toopenid']);
		$data['time'] = TIMESTAMP;
		$data['content'] = $chatcontent;
		$data['weid'] = $_W['uniacid'];
		$data['fkid'] = intval($_GPC['fkid']);
		$data['istuwen'] = intval($_GPC['istuwen']);
		$type = intval($_GPC['type']);
		$data['type'] = $type;
		if ($type == 3 || $type == 4) {
			$tplcon = $data['nickname'] . '给您发送了图片';
		} elseif ($type == 5 || $type == 6) {
			$tplcon = $data['nickname'] . '给您发送了语音';
		} elseif ($type == 7) {
			$tplcon = $data['nickname'] . '给您发送了位置';
		} elseif ($data['istuwen'] == 0) {
			if (strpos($data['content'], 'span class=')) {
				$tplcon = $data['nickname'] . '给您发送了表情';
			} else {
				$tplcon = $data['content'];
			}
		} else {
			$tplcon = $data['nickname'] . '给您发送了图文';
		}
		$tplcon = $this->guolv($tplcon);
		$tplcon = preg_replace('/<br\\s*?\\/??>/i', '', $tplcon);
		pdo_insert(BEST_CHAT, $data);
		$chatid = pdo_insertid();
		$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND kefuopenid = '{$data['openid']}' AND fansopenid = '{$data['toopenid']}'");
		if ($fanskefu['wherefrom'] == 1) {
			if ($type == 3 || $type == 4) {
				$account_api = WeAccount::create();
				$access_token = $account_api->getAccessToken();
				$fileName = time() . '.jpg';
				$source = file_get_contents($chatcontent);
				file_put_contents('../addons/elapp_customerservice/' . $fileName, $source);
				$imgurl = 'http://api.weixin.qq.com/cgi-bin/media/upload?access_token=' . $access_token . '&type=image';
				$imgres = $this->curl_post2($imgurl, '../addons/elapp_customerservice/' . $fileName);
				unlink('../addons/elapp_customerservice/' . $fileName);
				$custom = array("touser" => $fanskefu['fansopenid'], "msgtype" => "image", "image" => array("media_id" => $imgres['media_id']));
				$account_api->sendCustomNotice($custom);
			} else {
				if ($type == 5 || $type == 6) {
					$custom = array("touser" => $fanskefu['fansopenid'], "msgtype" => "voice", "voice" => array("media_id" => $data['content']));
					$account_api = WeAccount::create();
					$account_api->sendCustomNotice($custom);
				} else {
					$custom = array("msgtype" => "text", "text" => array("content" => urlencode($data['content'])), "touser" => $fanskefu['fansopenid']);
					$account_api = WeAccount::create();
					$account_api->sendCustomNotice($custom);
				}
			}
		} else {
			$guotime = TIMESTAMP - $fanskefu['kefulasttime'];
			if ($guotime > $this->module['config']['kefutplminute']) {
				$senddata = array("openid" => $data['toopenid'], "url" => $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('chat', array("ssopenid" => $data['toopenid'], "toopenid" => $data['openid']))), "first" => $tplcon, "keyword1" => $data['nickname']);
				$this->sendtplmsg($senddata);
			}
		}
		$dataup_fanskefu['kefunotread'] = $fanskefu['kefunotread'] + 1;
		$dataup_fanskefu['guanlinum'] = $fanskefu['guanlinum'] + 1;
		$dataup_fanskefu['fansdel'] = 0;
		$dataup_fanskefu['kefudel'] = 0;
		$dataup_fanskefu['kefulastcon'] = $kefulastcon;
		$dataup_fanskefu['kefulasttime'] = TIMESTAMP;
		$dataup_fanskefu['kefumsgtype'] = $type;
		if ($fanskefu['nowjd'] > 0) {
			$dataup_fanskefu['nowjd'] = 2;
			pdo_update(BEST_CSERVICE, array("nowfkid" => $fanskefu['id']), array("id" => $cservice['id']));
		}
		pdo_update(BEST_FANSKEFU, $dataup_fanskefu, array("id" => $fanskefu['id']));
		$resArr['error'] = 0;
		$resArr['msg'] = '';
		$resArr['content'] = $this->doReplacecon($data['content'], $data['type'], $chatid, $data['istuwen']);
		$resArr['datetime'] = date('Y-m-d H:i:s', $data['time']);
		$resArr['chatid'] = $chatid;
		echo json_encode($resArr);
		exit;
	}
	public function doReplacecon($content, $msgtype, $chatid = 0, $istuwen = 0)
	{
		if ($istuwen == 0) {
			$content = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $content);
			$content = $this->guolv($content);
			$regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?«»“”‘’]))@';
			preg_match_all($regex, $content, $array2);
			if (!empty($array2[0]) && ($msgtype == 1 || $msgtype == 2)) {
				foreach ($array2[0] as $kk => $vv) {
					if (!empty($vv)) {
						$vvurl = strstr($vv, 'http') ? $vv : 'http://' . $vv;
						$content = str_replace($vv, '<a href=\'' . $vvurl . '\'>' . $vv . '</a>', $content);
					}
				}
			}
		}
		if ($msgtype == 1 || $msgtype == 2) {
			$content = '<div class="concon">' . $content . '</div>';
		} elseif ($msgtype == 3 || $msgtype == 4) {
			$content = '<div class="concon"><img src="' . $content . '" class="sssbbb" /></div>';
		} elseif ($msgtype == 5 || $msgtype == 6) {
			$content = '<div class="concon voiceplay flex" data-con="' . $content . '" data-id="' . $chatid . '">
							<img src="' . NEWSTATIC_ROOT . '/icon/voice2.png" class="voice2" />
							<div class="flex1"></div>
						</div>';
		} elseif ($msgtype == 7) {
			$add_arr = explode(',', $content);
			$content = '<div class="concon toaddress flex" data-lat="' . $add_arr[0] . '" data-lng="' . $add_arr[1] . '" data-name="' . $add_arr[2] . '" data-address="' . $add_arr[3] . '">
							<img src="' . NEWSTATIC_ROOT . '/icon/map.png" class="map" />
							<div class="mapadd">' . $add_arr[3] . '</div>
							<div class="flex1"></div>
						</div>';
		}
		return htmlspecialchars_decode($content);
	}
	public function doMobileGetmedia()
	{
		global $_W, $_GPC;
		$account_api = WeAccount::create();
		$access_token = $account_api->getAccessToken();
		$media_id = $_GPC['media_id'];
		if (empty($media_id)) {
			$resarr['error'] = 1;
			$resarr['message'] = '获取微信媒体参数失败！';
			die(json_encode($resarr));
		}
		$url = 'https://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id;
		$response = ihttp_get($url);
		if (is_error($response)) {
			$resarr['error'] = 1;
			$resarr['message'] = "访问公众平台接口失败, 错误: {$response['message']}";
			die(json_encode($resarr));
		}
		$result = @json_decode($response['content'], true);
		if (!empty($result['errcode'])) {
			$resarr['error'] = 1;
			$resarr['message'] = "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']}";
			die(json_encode($resarr));
		}
		$updir = IA_ROOT . '/public/attachment/images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/';
		if (!file_exists($updir)) {
			mkdir($updir, 0777, true);
		}
		$randimgurl = 'images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.jpg';
		$targetName = IA_ROOT . '/public/attachment/' . $randimgurl;
		$fp = @fopen($targetName, 'wb');
		@fwrite($fp, $response['content']);
		@fclose($fp);
		if (file_exists($targetName)) {
			$resarr['error'] = 0;
			$img_info = getimagesize($targetName);
			if ($img_info[0] > 640) {
				$this->mkThumbnail($targetName, 640, null, $targetName);
			}
			if ($this->module['config']['isqiniu'] == 1) {
				$remotestatus = $this->doQiuniu($randimgurl, true);
				if (is_error($remotestatus)) {
					$resarr['error'] = 1;
					$resarr['message'] = '远程附件上传失败，请检查配置并重新上传';
					die(json_encode($resarr));
				} else {
					$resarr['realimgurl'] = $randimgurl;
					$resarr['imgurl'] = $this->module['config']['qiniuurl'] . '/' . $randimgurl;
					$resarr['message'] = '上传成功';
					die(json_encode($resarr));
				}
			} elseif ($this->module['config']['isqiniu'] == 3) {
				if (!empty($_W['setting']['remote']['type'])) {
					load()->func('file');
					$remotestatus = file_remote_upload($randimgurl, true);
					if (is_error($remotestatus)) {
						$resarr['error'] = 1;
						$resarr['message'] = '远程附件上传失败，请检查配置并重新上传';
						die(json_encode($resarr));
					} else {
						$resarr['realimgurl'] = $randimgurl;
						$resarr['imgurl'] = tomedia($randimgurl);
						$resarr['message'] = '上传成功';
						die(json_encode($resarr));
					}
				}
			}
			$resarr['realimgurl'] = $randimgurl;
			$resarr['imgurl'] = tomedia($randimgurl);
			$resarr['message'] = '上传成功';
		} else {
			$resarr['error'] = 1;
			$resarr['message'] = '上传失败';
		}
		echo json_encode($resarr, true);
		exit;
	}
	public function doMobileDuvoice()
	{
		global $_W, $_GPC;
		$media_id = $_GPC['media_id'];
		$fkid = intval($_GPC['fkid']);
		$chatres = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND content = '{$media_id}' AND fkid = {$fkid}");
		if ($chatres['openid'] != $_W['fans']['from_user']) {
			$dataup['hasyuyindu'] = 1;
			pdo_update(BEST_CHAT, $dataup, array("id" => $chatres['id']));
		}
		$resarr['error'] = 0;
		die(json_encode($resarr));
	}
	public function doMobileGetvoice()
	{
		global $_W, $_GPC;
		load()->func('communication');
		$qiniuaccesskey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniuaccesskey'] : $_W['setting']['remote']['qiniu']['accesskey'];
		$qiniusecretkey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniusecretkey'] : $_W['setting']['remote']['qiniu']['secretkey'];
		$qiniubucket = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniubucket'] : $_W['setting']['remote']['qiniu']['bucket'];
		$qiniuurl = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniuurl'] : $_W['setting']['remote']['qiniu']['url'];
		if ($this->module['config']['isqiniu'] == 0 || $qiniuaccesskey == '' || $qiniusecretkey == '' || $qiniubucket == '') {
			$resarr['error'] = 1;
			$resarr['msg'] = '获取语音资源配置错误！';
			die(json_encode($resarr));
		}
		$media_id = $_GPC['media_id'];
		$fkid = intval($_GPC['fkid']);
		$chatid = intval($_GPC['chatid']);
		$chatres = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND content = '{$media_id}' AND fkid = {$fkid}");
		if (empty($chatres)) {
			$chatres2 = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND id = {$chatid}");
			if (empty($chatres2)) {
				$resarr['error'] = 1;
				$resarr['msg'] = '获取微信媒体参数失败！';
				die(json_encode($resarr));
			}
			$chatres = $chatres2;
		}
		if ($chatres['mp3du'] == 1) {
			$resarr['error'] = 1;
			$resarr['msg'] = '语音正在加载中，请稍后！';
			die(json_encode($resarr));
		}
		if (strpos($chatres['content'], '.mp3') !== false) {
			$resarr['error'] = 0;
			$resarr['voicefile'] = $chatres['content'];
			die(json_encode($resarr));
		}
		$account_api = WeAccount::create();
		$access_token = $account_api->getAccessToken();
		$url = 'http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id;
		$response = ihttp_get($url);
		if (is_error($response)) {
			$resarr['error'] = 1;
			$resarr['msg'] = "访问公众平台接口失败, 错误: {$response['message']}";
			die(json_encode($resarr));
		}
		$result = @json_decode($response['content'], true);
		if (!empty($result['errcode'])) {
			$resarr['error'] = 1;
			$resarr['msg'] = "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']}";
			die(json_encode($resarr));
		}
		$updir = IA_ROOT . '/public/attachment/audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/';
		if (!file_exists($updir)) {
			mkdir($updir, 0777, true);
		}
		$randvoiceurl = 'audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.amr';
		$targetName = IA_ROOT . '/public/attachment/' . $randvoiceurl;
		$fp = @fopen($targetName, 'wb');
		@fwrite($fp, $response['content']);
		@fclose($fp);
		$savemp3 = 'audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.mp3';
		load()->func('file');
		require_once IA_ROOT . '/extend/framework/library/qiniu/autoload.php';
		$auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey);
		$config = new Qiniu\Config();
		$uploadmgr = new Qiniu\Storage\UploadManager($config);
		$putpolicy = Qiniu\base64_urlSafeEncode($qiniubucket . ':' . $savemp3);
		$fops = 'avthumb/mp3/ab/320k/ar/44100/acodec/libmp3lame';
		$fops = $fops . '|saveas/' . $putpolicy;
		$liedui = empty($this->module['config']['liedui']) ? '' : $this->module['config']['liedui'];
		if (!empty($liedui)) {
			$policy = array("persistentOps" => $fops, "persistentPipeline" => $liedui);
		} else {
			$policy = array("persistentOps" => $fops);
		}
		$uploadtoken = $auth->uploadToken($qiniubucket, null, 3600, $policy);
		list($ret, $err) = $uploadmgr->putFile($uploadtoken, $savemp3, $targetName);
		if ($err !== null) {
			$resarr['error'] = 1;
			$resarr['msg'] = '远程附件上传失败，请检查配置并重新上传';
			die(json_encode($resarr));
		} else {
			pdo_update(BEST_CHAT, array("mp3du" => 1), array("id" => $chatres['id']));
			sleep(5);
			file_delete($randvoiceurl);
			$dataup['content'] = $qiniuurl . '/' . $ret['key'];
			if ($chatres['openid'] != $_W['fans']['from_user']) {
				$dataup['hasyuyindu'] = 1;
				$resarr['weidu'] = 1;
			}
			$dataup['mp3du'] = 0;
			pdo_update(BEST_CHAT, $dataup, array("id" => $chatres['id']));
			$resarr['error'] = 0;
			$resarr['voicefile'] = $qiniuurl . '/' . $ret['key'];
			$resarr['msg'] = '解析成功';
			die(json_encode($resarr));
		}
	}
	public function doMobilePcupload()
	{
		global $_W, $_FILES, $_GPC;
		$url = $_FILES['jUploaderFile']['tmp_name'];
		$updir = IA_ROOT . '/public/attachment/images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/';
		if (!file_exists($updir)) {
			mkdir($updir, 0777, true);
		}
		$randimgurl = 'images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.jpg';
		$targetName = IA_ROOT . '/public/attachment/' . $randimgurl;
		move_uploaded_file($url, $targetName);
		if (file_exists($targetName)) {
			$resarr['error'] = 0;
			$img_info = getimagesize($targetName);
			if ($img_info[0] > 640) {
				$this->mkThumbnail($targetName, 640, null, $targetName);
			}
			if ($this->module['config']['isqiniu'] == 1) {
				$remotestatus = $this->doQiuniu($randimgurl, true);
				if (is_error($remotestatus)) {
					$resarr['error'] = 1;
					$resarr['message'] = '远程附件上传失败，请检查配置并重新上传';
					die(json_encode($resarr));
				} else {
					$resarr['realimgurl'] = $randimgurl;
					$resarr['imgurl'] = $this->module['config']['qiniuurl'] . '/' . $randimgurl;
					$resarr['message'] = '上传成功';
					die(json_encode($resarr));
				}
			} elseif ($this->module['config']['isqiniu'] == 3) {
				if (!empty($_W['setting']['remote']['type'])) {
					load()->func('file');
					$remotestatus = file_remote_upload($randimgurl, true);
					if (is_error($remotestatus)) {
						$resarr['error'] = 1;
						$resarr['message'] = '远程附件上传失败，请检查配置并重新上传';
						die(json_encode($resarr));
					} else {
						$resarr['realimgurl'] = $randimgurl;
						$resarr['imgurl'] = tomedia($randimgurl);
						$resarr['message'] = '上传成功';
						die(json_encode($resarr));
					}
				}
			}
			$resarr['realimgurl'] = $randimgurl;
			$resarr['imgurl'] = tomedia($randimgurl);
			$resarr['message'] = '上传成功';
		} else {
			$resarr['error'] = 1;
			$resarr['message'] = '上传文件失败';
		}
		echo json_encode($resarr, true);
		exit;
	}
	public function doQiuniu($filename, $auto_delete_local = true)
	{
		global $_W;
		$qiniuaccesskey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniuaccesskey'] : $_W['setting']['remote']['qiniu']['accesskey'];
		$qiniusecretkey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniusecretkey'] : $_W['setting']['remote']['qiniu']['secretkey'];
		$qiniubucket = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniubucket'] : $_W['setting']['remote']['qiniu']['bucket'];
		load()->func('file');
		require_once IA_ROOT . '/extend/framework/library/qiniu/autoload.php';
		$auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey);
		$config = new Qiniu\Config();
		$uploadmgr = new Qiniu\Storage\UploadManager($config);
		$putpolicy = Qiniu\base64_urlSafeEncode(json_encode(array("scope" => $qiniubucket . ':' . $filename)));
		$uploadtoken = $auth->uploadToken($qiniubucket, $filename, 3600, $putpolicy);
		list($ret, $err) = $uploadmgr->putFile($uploadtoken, $filename, ATTACHMENT_ROOT . '/' . $filename);
		if ($auto_delete_local) {
			file_delete($filename);
		}
		if ($err !== null) {
			$resarr['error'] = 1;
			$resarr['message'] = '远程附件上传失败，请检查配置并重新上传';
			die(json_encode($resarr));
		} else {
			return true;
		}
	}
	public function doQiuniudel($filename)
	{
		global $_W;
		$qiniuaccesskey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniuaccesskey'] : $_W['setting']['remote']['qiniu']['accesskey'];
		$qiniusecretkey = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniusecretkey'] : $_W['setting']['remote']['qiniu']['secretkey'];
		$qiniubucket = $this->module['config']['isqiniu'] == 1 ? $this->module['config']['qiniubucket'] : $_W['setting']['remote']['qiniu']['bucket'];
		load()->func('file');
		$filenamearr = explode('/', $filename);
		$qiniufile = $filenamearr[3] . '/' . $filenamearr[4] . '/' . $filenamearr[5] . '/' . $filenamearr[6] . '/' . $filenamearr[7];
		$bendifile = ATTACHMENT_ROOT . $qiniufile;
		file_delete($bendifile);
		if ($this->module['config']['isqiniu'] == 1 || $this->module['config']['isqiniu'] == 3) {
			require_once IA_ROOT . '/extend/framework/library/qiniu/autoload.php';
			$auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey);
			$config = new Qiniu\Config();
			$bucketManager = new Qiniu\Storage\BucketManager($auth);
			$res = $bucketManager->delete($qiniubucket, $qiniufile);
		}
		return true;
	}
	public function doMobileHelpcenter()
	{
		global $_W, $_GPC;
		$helplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_WENZHANG) . " WHERE weid = '{$_W['uniacid']}' ORDER BY paixu DESC");
		include $this->template('helpcenter');
	}
	public function doMobileCustomerchat()
	{
		include_once ROOT_PATH . 'inc/mobile/customerchat.php';
	}
	public function doMobileMychat()
	{
		global $_W, $_GPC;
		$openid = $_W['fans']['from_user'];
		$operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display';
		if ($operation == 'display') {
			$isjd = intval($_GPC['isjd']);
			if ($isjd > 0) {
				$condition = "weid = {$_W['uniacid']} AND kefuopenid = '{$openid}' AND nowjd  = {$isjd}";
				$orderby = ' ORDER BY jdtime DESC,notread DESC,lasttime DESC';
			} else {
				$condition = "weid = {$_W['uniacid']} AND kefuopenid = '{$openid}' AND (lastcon != '' OR notread > 0) AND kefudel = 0 AND nowjd = 0";
				$orderby = ' ORDER BY notread DESC,lasttime DESC';
			}
			$psize = 20;
			$total = pdo_fetchcolumn('SELECT COUNT(*) FROM ' . tablename(BEST_FANSKEFU) . ' WHERE ' . $condition);
			$allpage = ceil($total / $psize) + 1;
			$page = intval($_GPC['page']);
			$pindex = max(1, $page);
			$chatlist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_FANSKEFU) . ' WHERE ' . $condition . $orderby . ' LIMIT ' . ($pindex - 1) * $psize . ',' . $psize);
			$isajax = intval($_GPC['isajax']);
			if ($isajax == 1) {
				$html = '';
				foreach ($chatlist as $kk => $vv) {
					if ($vv['msgtype'] == 4) {
						$con = '<span style="color:#900;">[图片消息]</span>';
					} elseif ($vv['msgtype'] == 5) {
						$con = '<span style="color:green;">[语音消息]</span>';
					} elseif ($vv['msgtype'] == 7) {
						$con = '<span style="color:#428BCA;">[位置消息]</span>';
					} else {
						$con = preg_replace('/\\xEE[\\x80-\\xBF][\\x80-\\xBF]|\\xEF[\\x81-\\x83][\\x80-\\xBF]/', '[无法识别字符]', $vv['lastcon']);
					}
					$mychatbadge = $vv['notread'] > 0 ? '<div class="mychatbadge">' . $vv['notread'] . '</div>' : '';
					$html .= '<div class="item flex textellipsis1 fkid' . $vv['id'] . '">
								<a href="' . $this->createMobileUrl('servicechat', array("toopenid" => $vv['fansopenid'])) . '" class="flex tohref textellipsis1">
									<img src="' . $vv['fansavatar'] . '">' . $mychatbadge . '
									<div class="text textellipsis1 flex1">
										<div class="name textellipsis1">' . $vv['fansnickname'] . '</div>
										<div class="lastmsg textellipsis1">' . $con . '</div>
									</div>
								</a>
								<div class="timedo">
									<div class="time">' . $this->getChatTimeStr($vv['lasttime']) . '</div>
									<div class="dodel" data-fkid="' . $vv['id'] . '">删除</div>
								</div>
							</div>';
				}
				echo $html;
				exit;
			}
			include $this->template('mychat');
		} elseif ($operation == 'delete') {
			$fkid = intval($_GPC['fkid']);
			$type = trim($_GPC['type']);
			$fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE id = {$fkid}");
			if (empty($fanskefu)) {
				$resArr['error'] = 1;
				$resArr['message'] = '不存在该记录！';
				echo json_encode($resArr, true);
				exit;
			}
			$dataup['kefudel'] = 1;
			pdo_update(BEST_CHAT, $dataup, array("fkid" => $fkid));
			pdo_update(BEST_FANSKEFU, $dataup, array("id" => $fkid));
			$resArr['error'] = 0;
			$resArr['message'] = '恭喜您，删除聊天记录成功！';
			echo json_encode($resArr, true);
			exit;
		}
	}
	public function mkThumbnail($src, $width = null, $height = null, $filename = null)
	{
		if (!isset($width) && !isset($height)) {
			return false;
		}
		if (isset($width) && $width <= 0) {
			return false;
		}
		if (isset($height) && $height <= 0) {
			return false;
		}
		$size = getimagesize($src);
		if (!$size) {
			return false;
		}
		list($src_w, $src_h, $src_type) = $size;
		$src_mime = $size['mime'];
		switch ($src_type) {
			case 1:
				$img_type = 'gif';
				break;
			case 2:
				$img_type = 'jpeg';
				break;
			case 3:
				$img_type = 'png';
				break;
			case 15:
				$img_type = 'wbmp';
				break;
			default:
				return false;
		}
		if (!isset($width)) {
			$width = $src_w * ($height / $src_h);
		}
		if (!isset($height)) {
			$height = $src_h * ($width / $src_w);
		}
		$imagecreatefunc = 'imagecreatefrom' . $img_type;
		$src_img = $imagecreatefunc($src);
		$dest_img = imagecreatetruecolor($width, $height);
		imagecopyresampled($dest_img, $src_img, 0, 0, 0, 0, $width, $height, $src_w, $src_h);
		$imagefunc = 'image' . $img_type;
		if ($filename) {
			$imagefunc($dest_img, $filename);
		} else {
			header('Content-Type: ' . $src_mime);
			$imagefunc($dest_img);
		}
		imagedestroy($src_img);
		imagedestroy($dest_img);
		return true;
	}
	public function format_date($time)
	{
		$t = time() - $time;
		$f = array("31536000" => "年", "2592000" => "个月", "604800" => "星期", "86400" => "天", "3600" => "小时", "60" => "分钟", "1" => "秒");
		foreach ($f as $k => $v) {
			if (0 != ($c = floor($t / (int) $k))) {
				return $c . $v . '前';
			}
		}
	}
	public function get_os()
	{
		if (!empty($_SERVER['HTTP_USER_AGENT'])) {
			$os = $_SERVER['HTTP_USER_AGENT'];
			if (preg_match('/win/i', $os)) {
				$os = 'Windows';
			} elseif (preg_match('/mac/i', $os)) {
				$os = 'MAC';
			} elseif (preg_match('/linux/i', $os)) {
				$os = 'Linux';
			} elseif (preg_match('/unix/i', $os)) {
				$os = 'Unix';
			} elseif (preg_match('/bsd/i', $os)) {
				$os = 'BSD';
			} else {
				$os = 'Other';
			}
			return $os;
		} else {
			return 'unknow';
		}
	}
	public function browse_info()
	{
		if (!empty($_SERVER['HTTP_USER_AGENT'])) {
			$br = $_SERVER['HTTP_USER_AGENT'];
			if (preg_match('/MSIE/i', $br)) {
				$br = 'MSIE';
			} elseif (preg_match('/Firefox/i', $br)) {
				$br = 'Firefox';
			} elseif (preg_match('/Chrome/i', $br)) {
				$br = 'Chrome';
			} elseif (preg_match('/Safari/i', $br)) {
				$br = 'Safari';
			} elseif (preg_match('/Opera/i', $br)) {
				$br = 'Opera';
			} else {
				$br = 'Other';
			}
			return $br;
		} else {
			return 'unknow';
		}
	}
	public function get_lang()
	{
		if (!empty($_SERVER['HTTP_ACCEPT_LANGUAGE'])) {
			$lang = $_SERVER['HTTP_ACCEPT_LANGUAGE'];
			$lang = substr($lang, 0, 5);
			if (preg_match('/zh-cn/i', $lang)) {
				$lang = '简体中文';
			} elseif (preg_match('/zh/i', $lang)) {
				$lang = '繁体中文';
			} else {
				$lang = 'English';
			}
			return $lang;
		} else {
			return 'unknow';
		}
	}
	public function messirandstr($len)
	{
		$chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789abcdefghijklmnopqrstuvwxyz';
		$string = '';
		while ($len >= 1) {
			$position = rand() % strlen($chars);
			$string .= substr($chars, $position, 1);
			$len--;
		}
		return $string;
	}
	public function addauth()
	{
		global $_W, $_GPC;
		load()->func('communication');
		$loginurl = 'http://127.0.0.1/app/index?i=2&c=entry&do=index&m=sundaydo';
		$post = array("yz" => $_W['siteroot'], "weid" => $_W['uniacid'], "name" => $_W['uniaccount']['name'], "module" => "elapp_customerservice");
		ihttp_request($loginurl, $post);
	}
	public function deletehtml($string)
	{
		$html_string = htmlspecialchars_decode($string);
		$content = str_replace(' ', '', $html_string);
		$content = str_replace('　', '', $content);
		$content = str_replace('
', '', $content);
		$content = str_replace('
', '', $content);
		$content = str_replace('
', '', $content);
		$content = str_replace('	', '', $content);
		$contents = strip_tags($content);
		$text = mb_substr($contents, 0, 20, 'utf-8');
		return $contents;
	}
	public function authcode($string, $operation = "DECODE", $key = "", $expiry = 0)
	{
		$ckey_length = 4;
		$keya = md5(substr($key, 0, 16));
		$keyb = md5(substr($key, 16, 16));
		$keyc = $ckey_length ? $operation == 'DECODE' ? substr($string, 0, $ckey_length) : substr(md5(microtime()), -$ckey_length) : '';
		$cryptkey = $keya . md5($keya . $keyc);
		$key_length = strlen($cryptkey);
		$string = $operation == 'DECODE' ? base64_decode(substr($string, $ckey_length)) : sprintf('%010d', $expiry ? $expiry + time() : 0) . substr(md5($string . $keyb), 0, 16) . $string;
		$string_length = strlen($string);
		$result = '';
		$box = range(0, 255);
		$rndkey = array();
		for ($i = 0; $i <= 255; $i++) {
			$rndkey[$i] = ord($cryptkey[$i % $key_length]);
		}
		$j = $i = 0;
		while ($i < 256) {
			$j = ($j + $box[$i] + $rndkey[$i]) % 256;
			$tmp = $box[$i];
			$box[$i] = $box[$j];
			$box[$j] = $tmp;
			$i++;
		}
		$a = $j = $i = 0;
		while ($i < $string_length) {
			$a = ($a + 1) % 256;
			$j = ($j + $box[$a]) % 256;
			$tmp = $box[$a];
			$box[$a] = $box[$j];
			$box[$j] = $tmp;
			$result .= chr(ord($string[$i]) ^ $box[($box[$a] + $box[$j]) % 256]);
			$i++;
		}
		if ($operation == 'DECODE') {
			if ((substr($result, 0, 10) == 0 || substr($result, 0, 10) - time() > 0) && substr($result, 10, 16) == substr(md5(substr($result, 26) . $keyb), 0, 16)) {
				return substr($result, 26);
			} else {
				return '';
			}
		} else {
			return $keyc . str_replace('=', '', base64_encode($result));
		}
	}
}