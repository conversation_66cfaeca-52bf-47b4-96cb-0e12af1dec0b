{template 'common/header'}
{if $cando == 1}
<ul class="nav nav-tabs">
	<li {if $index == 'index'}class="active"{/if}><a href="{php echo $this->createWebUrl('xcx',array('op' =>'auto','id'=>$xcxid))}">[{$xcx['name']}]的自动回复列表</a></li>
	<li {if $index == 'editauto'}class="active"{/if}><a href="{php echo $this->createWebUrl('xcx',array('op' =>'addauto','xcxid'=>$xcxid))}">添加自动回复</a></li>
	{if !empty($autoid) &&  $operation == 'editauto'}<li  class="active"><a href="{php echo $this->createWebUrl('xcx',array('op' =>'addauto','autoid'=>$autoid,'id'=>$id))}">编辑小程序客服</a></li>{/if}
</ul>

{if $index == 'index'}
<div class="main panel panel-default">
	<div class="panel-body table-responsive">
		<form action="" method="post">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th style="width:10%;">优先级</th>
					<th style="width:25%;">规则名</th>
					<th style="width:15%;">回复类型</th>
					<th style="width:15%;">回复内容类型</th>
					<th style="text-align:right;">操作</th>
				</tr>
			</thead>
			<tbody>
				{loop $allauto $row}
				<tr>
					<td><input class="form-control" name="paixu[{$row['id']}]" value="{$row['paixu']}" type="text" /></td>
					<td>{$row['name']}</td>
					<td>
						{if $row['hftype'] == 1}用户打开客服界面时回复{/if}
						{if $row['hftype'] == 2}用户触发关键词时回复{/if}
						{if $row['hftype'] == 3}用户发送消息时回复{/if}
					</td>
					<td>
						{if $row['gjctype'] == 1}文本回复{/if}
						{if $row['gjctype'] == 2}文本回复(跳转小程序){/if}
						{if $row['gjctype'] == 3}图片回复{/if}
						{if $row['gjctype'] == 4}图文回复{/if}
					</td>
					<td style="text-align:right;">
						<a href="{php echo $this->createWebUrl('xcx', array('op' => 'editauto', 'xcxid' => $id))}" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>&nbsp;&nbsp;
						<a href="{php echo $this->createWebUrl('xcx', array('op' => 'delauto', 'id' => $row['id']))}" onclick="return confirm('确认删除吗？');return false;" class="btn btn-default btn-sm" title="删除"><i class="fa fa-times"></i></a>
					</td>
				</tr>
				{/loop}
				<tr>
					<td></td>
					<td colspan="4">
						<input name="submit" class="btn btn-primary" value="提交" type="submit">
						<input name="token" value="{$_W['token']}" type="hidden">
					</td>
				</tr>
			</tbody>
		</table>
		</form>
	</div>
</div>
<script>
	require(['bootstrap'],function($){
		$('.btn').hover(function(){
			$(this).tooltip('show');
		},function(){
			$(this).tooltip('hide');
		});
	});
</script>
{elseif $operation == 'post'}

<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" onsubmit='return formcheck()'>
		<input type="hidden" name="id" value="{$cservice['id']}" />
		<div class="panel panel-default">
			<div class="panel-heading">
				小程序客服设置
			</div>
			<div class="panel-body">
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">排序</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="displayorder" class="form-control" value="{$cservice['displayorder']}" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服名称</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="name" class="form-control" value="{$cservice['name']}" />
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">所属小程序</label>
					<div class="col-sm-9 col-xs-12">
						<select name="xcxid" class="form-control">
							<option value="">--请选择--</option>
							{loop $xcxlist $prow}
								<option value="{$prow['id']}" {if $cservice['xcxid'] == $prow['id']}selected="selected"{/if}>{$prow['name']}</option>
							{/loop}
						</select>
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服头像</label>
					<div class="col-sm-9 col-xs-12">
						{php echo tpl_form_field_image('thumb', $cservice['thumb'])}
						<span class="help-block" style="color:#900;">为了保证美观，请上传180*180尺寸的图片</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服内容</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="content" class="form-control" value="{$cservice['content']}" /><br />
						<div class="alert alert-danger" role="alert">微信客服内容填openid，<a href="index.php?c=mc&a=fans" target="_blank" style="color:green;">快速获取openid</a></div>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服快捷消息</label>
					<div class="col-sm-7 col-xs-12">
						<textarea class="form-control" style="height:100px;" name="kefuauto">{$cservice['kefuauto']}</textarea>
						<span class="help-block" style="color:#900;">多个可用|隔开</span>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">快捷消息提交设置</label>
					<div class="col-sm-9 col-xs-12">
						<label for="isautosub1" class="radio-inline"><input name="isautosub" value="1" id="isautosub1" {if $cservice['isautosub'] == 1}checked="true"{/if} type="radio"> 进入输入框</label>
						&nbsp;&nbsp;&nbsp;
						<label for="isautosub2" class="radio-inline"><input name="isautosub" value="0" id="isautosub2" {if $cservice['isautosub'] == 0}checked="true"{/if} type="radio"> 自动发送</label>
						<span class="help-block"></span>
					</div>
				</div>

			</div>
		</div>
		<div class="form-group col-sm-12">
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			<input type="hidden" name="token" value="{$_W['token']}" />
		</div>
	</form>
</div>
{/if}
{else}
	<div class="alert alert-danger">{php echo urldecode(BEST_TISHI)}</div>
{/if}
{template 'common/footer'}