{template 'common/header'}
<ul class="nav nav-tabs">
	<li {if $operation == 'display'}class="active"{/if}><a href="{php echo $this->createWebUrl('chatlist', array('op' => 'display'))}">聊天管理</a></li>
</ul>
{if $operation == 'display'}
<div class="main">
	<div class="panel panel-info">
		<div class="panel-heading">筛选</div>
		<div class="panel-body">
			<form action="./index.php" method="get" class="form-horizontal" role="form" id="form1">
				<input type="hidden" name="c" value="site" />
				<input type="hidden" name="a" value="entry" />
				<input type="hidden" name="m" value="elapp_customerservice" />
				<input type="hidden" name="do" value="chatlist" />
				<div class="form-group">
					<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">选择客服</label>
					<div class="col-xs-12 col-sm-8 col-lg-9">
						<select name="kefuopenid" class='form-control'>
							<option value="0">--请选择客服--</option>
							{loop $cservicelist $grow}
							<option value="{$grow['content']}" {if $_GPC['kefuopenid'] == $grow['content']}selected{/if}>
								{$grow['name']} -- (未读{$grow['weidu']}条)
							</option>
							{/loop}
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-2 col-md-2 col-lg-1 control-label">时间范围</label>
					<div class="col-sm-3 col-xs-12">
						{php echo tpl_form_field_daterange('time', array('starttime'=>date('Y-m-d', $starttime),'endtime'=>date('Y-m-d', $endtime)));}
					</div>
					<div class="col-sm-7 col-xs-12">
						<button class="btn btn-default"><i class="fa fa-search"></i> 搜索</button>
						<button name="export" value="export" class="btn btn-primary"><i class="fa fa-download"></i> 导出聊天记录</button>
						<button type="button" class="btn btn-default">总记录数：{$total}</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	
	<div class="panel panel-default">
		<div class="panel-heading">聊天记录</div>
		<div class="panel-body table-responsive">
			<table class="table table-hover table-striped table-condensed">
				<thead>
					<tr>
						<th style="width:25%;">客户</th>
						<th style="width:35%;">最新消息</th>
						<th style="width:15%;">删除情况</th>
						<th style="text-align:right;">操作</th>
					</tr>
				</thead>
				<tbody>
				{loop $fanslist $row}
				<tr>
					<td>
						<div><img src="{$row['fansavatar']}" width="30" height="30" style="border-radius:100%;" /></div>
						<div style="margin-top:5px;"><span class="label label-default">{$row['fansnickname']}</span></div>
					</td>
					<td>
						<div>
							{if $row['msgtype'] == 4}
								[图片消息]
							{else if $row['msgtype'] == 5}
								[语音消息]
							{else}
								{php echo preg_replace('/\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]/', '[无法识别字符]', $row['lastcon'])}
							{/if}
						</div>
						<div style="margin-top:5px;"><span class="label label-default">{php echo date("Y-m-d H:i:s",$row['lasttime'])}</span></div>
					</td>
					<td>
						{if $row['fansdel'] == 1}<span class="label label-danger">用户删除</span>{/if}
						{if $row['kefudel'] == 1}<span class="label label-danger">客服删除</span>{/if}
					</td>
					<td style="text-align:right;">
						<button type="button" class="btn btn-success btn-sm" data-toggle="modal" data-target="#formModal{$row['id']}">聊天记录</button>&nbsp;&nbsp;
						<a href="{php echo $this->createWebUrl('chatlist', array('op' => 'delete', 'id' => $row['id'],'kefuopenid'=>$kefuopenid))}" onclick="return confirm('确认要删除聊天记录吗？');return false;" class="btn btn-default btn-sm" title="删除"><i class="fa fa-times"></i></a>
					</td>
				</tr>
				{/loop}
				</tbody>
			</table>
			
			{loop $fanslist $row}
				<div class="modal fade" id="formModal{$row['id']}" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">
					<div class="modal-dialog modal-lg">
						<div class="modal-content">
							<div class="modal-header">
								<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
								<h4 class="modal-title">聊天记录</h4>
							</div>
							<div class="modal-body table-responsive" style="max-height:400px;overflow-y:auto;">					
								<table class="table table-hover table-striped table-condensed">
									<thead>
										<tr>
											<th style="width:15%;">昵称</th>
											<th>聊天内容</th>
											<th style="width:15%;">时间</th>
											<th style="width:10%;text-align:right;">操作</th>
										</tr>
									</thead>
									<tbody>
										{loop $row['chat'] $rowchat}
											<tr class="trre{$rowchat['id']}">
												<td>
													{if $rowchat['openid'] == $row['fansopenid']}
														<span class="label label-success">{$row['fansnickname']}</span>
													{else}
														<span class="label label-info">{$row['kefunickname']}</span>
													{/if}
												</td>
												<td style="overflow:auto;text-overflow:initial;white-space:inherit;">
													{if $rowchat['type'] == 3 || $rowchat['type'] == 4}
														<img src="{$rowchat['content']}" onclick="showimage('{$rowchat['content']}');" style="max-width:100px;cursor:pointer;" />
													{elseif $rowchat['type'] == 5 || $rowchat['type'] == 6}
														<span class="label label-success">[语音消息]</span>
													{else}
														{$rowchat['content']}
													{/if}
												</td>
												<td>
													<span class="label label-info">{php echo date("Y-m-d H:i:s",$rowchat['time'])}</span>
												</td>
												<td style="text-align:right;">
													<a data-id="{$rowchat['id']}" class="btn btn-default btn-sm deldu" title="删除"><i class="fa fa-times"></i></a>
												</td>
											</tr>
										{/loop}
									</tbody>
								</table>
							</div>
						</div>
					</div>
				</div>
			{/loop}
		</div>
	</div>
	{$pager}
</div>
{/if}
<div id="ShowImage_Form" tabindex="-2" class="modal fade" tabindex="-1" role="dialog" aria-labelledby="myModalLabel" aria-hidden="true">     <div class="modal-dialog modal-lg">
	<div class="modal-content">    
		<div class="modal-header">
			<button type="button" class="close" data-dismiss="modal"><span aria-hidden="true">&times;</span><span class="sr-only">Close</span></button>
			<h4 class="modal-title">图片信息</h4>
		</div>
		<div class="modal-body">
			<div id="img_show"></div>
		</div>
	</div>
	</div>
</div>
<script type="text/javascript">
$(function(){
	$('.deldu').click(function(){
		if(confirm("确定要删除这条聊天记录吗？")){
			var chatid = $(this).attr('data-id');
			$.ajax({
				url:"{php echo $this->createWebUrl('chatlist')}",
				data:{
					id:chatid,
					op:'deletedu',
				},
				dataType:'json',
				type:'post',        
				success:function(data){
					if(data.error == 1){
						alert(data.msg);
					}else{
						$('.trre'+chatid).remove();
					}
				},
			});
		}
	});
})
function showimage(source)
 {
	 $("#ShowImage_Form").find("#img_show").html("<image src='"+source+"' class='carousel-inner img-responsive img-rounded' />");
	 $("#ShowImage_Form").modal();
 }
</script>
{template 'common/footer'}