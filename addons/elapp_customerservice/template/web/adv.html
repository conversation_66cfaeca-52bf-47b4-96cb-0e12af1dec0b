{template 'common/header'}

<ul class="nav nav-tabs">
	<li {if $operation == 'display'} class="active" {/if}><a href="{php echo $this->createWebUrl('adv',array('op' =>'display'))}">幻灯片</a></li>
	<li{if empty($adv['id']) && $operation == 'post'} class="active" {/if}><a href="{php echo $this->createWebUrl('adv',array('op' =>'post'))}">添加幻灯片</a></li>
	{if !empty($adv['id']) &&  $operation == 'post'}<li  class="active"><a href="{php echo $this->createWebUrl('adv',array('op' =>'post','id'=>$adv['id']))}">编辑幻灯片</a></li>{/if}
</ul>

{if $operation == 'display'}
<div class="main panel panel-default">
	<div class="panel-body table-responsive">
		<form action="" method="post">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th style="width:8%;">排序</th>
					<th>到期时间</th>
					<th>标题</th>
					<th>连接</th>
					<th style="text-align:right;">操作</th>
				</tr>
			</thead>
			<tbody>
				{loop $list $adv}
				<tr>
					<td><input class="form-control" name="displayorder[{$adv['id']}]" value="{$adv['displayorder']}" type="text"></td>
					<td>{if $adv['endtime'] > 0}{php echo date("Y-m-d H:i:s",$adv['endtime'])}{else}无{/if}</td>
					<td><span class="label label-success">{$adv['advname']}</span></td>
					<td>{$adv['link']}</td>
					<td style="text-align:right;">
						<a href="{php echo $this->createWebUrl('adv', array('op' => 'post', 'id' => $adv['id']))}" class="btn btn-default btn-sm" data-toggle="tooltip" data-placement="top" title="修改"><i class="fa fa-edit"></i></a>
						<a href="{php echo $this->createWebUrl('adv', array('op' => 'delete', 'id' => $adv['id']))}"class="btn btn-default btn-sm" onclick="return confirm('此操作不可恢复，确认删除？');return false;" data-toggle="tooltip" data-placement="top" title="删除"><i class="fa fa-times"></i></a>
					</td>
				</tr>
				{/loop}
				<tr>
					<td colspan="5">
						<input name="submit" class="btn btn-primary" value="提交" type="submit">
						<input name="token" value="{$_W['token']}" type="hidden">
					</td>
				</tr>
			</tbody>
		</table>
		</form>
	</div>
</div>
<script>
	require(['bootstrap'],function($){
		$('.btn').hover(function(){
			$(this).tooltip('show');
		},function(){
			$(this).tooltip('hide');
		});
	});
</script>
{elseif $operation == 'post'}

<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" onsubmit='return formcheck()'>
		<input type="hidden" name="id" value="{$adv['id']}" />
		<div class="panel panel-default">
			<div class="panel-heading">
				幻灯片设置
			</div>
			<div class="panel-body">
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">排序</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="displayorder" class="form-control" value="{$adv['displayorder']}" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">所属客服组</label>
					<div class="col-sm-9 col-xs-12">
						<select name="groupid" class="form-control">
							<option value="0" {if $adv['groupid'] == 0}selected="selected"{/if}>--无--</option>
							{loop $grouplist $row}
								<option value="{$row['id']}" {if $adv['groupid'] == $row['id']}selected="selected"{/if}>{$row['name']}</option>
							{/loop}
						</select>
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">标题</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" id='advname' name="advname" class="form-control" value="{$adv['advname']}" />
					</div>
				</div>
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">幻灯片图片</label>
					<div class="col-sm-9 col-xs-12">
						{php echo tpl_form_field_image('thumb', $adv['thumb'], '', array('extras' => array('text' => 'readonly')))}
						<span class="help-block" style="color:#900;">推荐尺寸640*235</span>
					</div>					
				</div>
				<div class="form-group" id="link">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">幻灯片连接</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="link" class="form-control" value="{$adv['link']}" />
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">到期时间</label>
					<div class="col-sm-9 col-xs-12">
						{php echo tpl_form_field_date('endtime',date('Y-m-d H:i:s',$adv['endtime']),true);}
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">是否打底</label>
					<div class="col-sm-9 col-xs-12">
						<label class='radio-inline'>
							<input type='radio' name='isdadi' value='1' {if $adv['isdadi']==1}checked{/if} /> 是
						</label>
						<label class='radio-inline'>
							<input type='radio' name='isdadi' value='0' {if $adv['isdadi']==0}checked{/if} /> 否
						</label>
						<span class="help-block" style="color:#900;">*打底表示当前无幻灯片时候显示时候，会展示保持页面美观</span>
					</div>
				</div>
			</div>
		</div>
		<div class="form-group col-sm-12">
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			<input type="hidden" name="token" value="{$_W['token']}" />
		</div>
	</form>
</div>
{/if}
{template 'common/footer'}