{template 'common/header'}
<ul class="nav nav-tabs">
	<li><a href="{php echo $this->createWebUrl('cservice', array('op' => 'display'))}">客服管理</a></li>
	<li><a href="{php echo $this->createWebUrl('cservice', array('op' => 'post'))}">添加客服</a></li>
	<li {if $operation == 'display'}class="active"{/if}><a href="{php echo $this->createWebUrl('cservicegroup', array('op' => 'display'))}">客服组管理</a></li>
	<li {if $operation == 'post'}class="active"{/if}><a href="{php echo $this->createWebUrl('cservicegroup', array('op' => 'post'))}">{if !$id}添加客服组{else}编辑客服组{/if}</a></li>
</ul>
{if $operation == 'post'}
<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" id="form1">
		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#basic" role="tab" data-toggle="tab">基本设置</a></li>
			<li role="presentation"><a href="#dsf" role="tab" data-toggle="tab">接入第三方</a></li>
		</ul>
		
		<div class="tab-content main">
			<div role="tabpanel" class="tab-pane active panel panel-default" id="basic">
				<div class="panel-heading">
					{if !$id}添加客服组{else}编辑客服组{/if}
				</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服组名称</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="name" class="form-control" value="{$cservicegroup['name']}" />
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">上级客服组</label>
						<div class="col-sm-9 col-xs-12">
							<select name="fid" class="form-control">
								<option value="0">无上级</option>
								{loop $othergroup $row}
								<option value="{$row['id']}" {if $row['id'] == $cservicegroup['fid']}selected="selected"{/if}>{$row['name']}</option>
								{/loop}
							</select>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">类别名称</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="typename" class="form-control" value="{$cservicegroup['typename']}" />
							<span class="help-block" style="color:#900;">原（客服组）文字</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服组头像</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_image('thumb', $cservicegroup['thumb'])}
							<span class="help-block" style="color:#900;">为了保证美观，请上传180*180尺寸的图片</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">是否前端显示</label>
						<div class="col-sm-9 col-xs-12">
							<label for="ishow1" class="radio-inline"><input name="ishow" value="1" id="ishow1" {if $cservicegroup['ishow'] == 1}checked="checked"{/if} type="radio"> 是</label>
							&nbsp;&nbsp;&nbsp;
							<label for="ishow2" class="radio-inline"><input name="ishow" value="0" id="ishow2" {if $cservicegroup['ishow'] == 0}checked="checked"{/if} type="radio"> 否</label>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">无客服提示语</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="notext" class="form-control" value="{$cservicegroup['notext']}" />
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">排序</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="displayorder" class="form-control" value="{$cservicegroup['displayorder']}" />
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-default" id="dsf">
				<div class="panel-heading">
					{if !$id}添加客服组{else}编辑客服组{/if}
				</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">第三方标识</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="sanbs" class="form-control" value="{$cservicegroup['sanbs']}" />
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">接入说明</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="sanremark" class="form-control" value="{$cservicegroup['sanremark']}" />
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">聚合id</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="bsid" class="form-control" value="{$cservicegroup['bsid']}" />
							<span class="help-block" style="color:#900;">例如某商品属于某个商家，则聚合id填商家id，否则可填0</span>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		<div class="form-group col-sm-12">
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			<input type="hidden" name="id" value="{$cservicegroup['id']}" />
			<input type="hidden" name="token" value="{$_W['token']}" />
		</div>
	</form>
</div>

{elseif $operation == 'display'}
<div class="main">
	<form action="" method="post">
	<div class="panel panel-default">
		<div class="panel-body table-responsive">
			<div class="alert alert-danger">对接第三方应用时，只需随便选择以下客服组中一个的嵌入代码即可。</div>
			<table class="table table-hover">
				<thead>
					<tr>
						<th style="width:8%;">排序</th>
						<th style="width:40%;">客服组链接</th>
						<th style="width:30%;">客服组信息</th>
						<th style="text-align:right;">操作</th>
					</tr>
				</thead>
				<tbody>
				{loop $cservicegrouplist $row}
				<tr>
					<td><input class="form-control" name="displayorder[{$row['id']}]" value="{$row['displayorder']}" type="text" /></td>
					<td>
						<div><span style="color:#900;">直接访问模式：</span><input class="form-control" name="servicegroupurl" value="{$row['servicegroupurl']}" type="text" /></div>
						<div style="margin-top:5px;"><span style="color:#900;">自动分配模式：</span><input class="form-control" name="servicegroupurl" value="{$row['servicegroupurl2']}" type="text" /></div>
					</td>
					<td>
						<div><img src="{php echo tomedia($row['thumb']);}" width="50" height="50" /></div>
						<div>{$row['name']} {if $row['ishow'] == 1}<span style="color:red;">(前端显示)</span>{/if}</div>
						<div style="margin-top:5px;">接入第三方：{php echo $row['sanremark'] != '' ? $row['sanremark'] : '无';}</div>
					</td>
					<td style="text-align:right;">
						<a href="{php echo $this->createWebUrl('cservicegroup', array('op' => 'post', 'id' => $row['id']))}" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>&nbsp;&nbsp;
						<a href="{php echo $this->createWebUrl('cservicegroup', array('op' => 'delete', 'id' => $row['id']))}" onclick="return confirm('确认要删除吗？');return false;" class="btn btn-default btn-sm" title="删除"><i class="fa fa-times"></i></a>
					</td>
				</tr>
				{/loop}
				<tr>
					<td></td>
					<td colspan="3">
						<input name="submit" class="btn btn-primary" value="提交" type="submit">
						<input name="token" value="{$_W['token']}" type="hidden">
					</td>
				</tr>
				</tbody>
			</table>
		</div>
	</div>
	</form>
</div>
{/if}
{template 'common/footer'}