{template 'common/header'}
{if $cando == 1}
<ul class="nav nav-tabs">
	<li {if $operation == 'display'} class="active" {/if}><a href="{php echo $this->createWebUrl('xcxcservice',array('op' =>'display'))}">小程序客服</a></li>
	<li{if empty($cservice['id']) && $operation == 'post'} class="active" {/if}><a href="{php echo $this->createWebUrl('xcxcservice',array('op' =>'post'))}">添加小程序客服</a></li>
	{if !empty($cservice['id']) &&  $operation == 'post'}<li  class="active"><a href="{php echo $this->createWebUrl('xcxcservice',array('op' =>'post','id'=>$cservice['id']))}">编辑小程序客服</a></li>{/if}
</ul>

{if $operation == 'display'}
<div class="main">
	<div class="panel panel-info">
		<div class="panel-heading">筛选</div>
		<div class="panel-body">
			<form action="./index.php" method="get" class="form-horizontal" role="form" id="form1">
				<input name="c" value="site" type="hidden">
				<input name="a" value="entry" type="hidden">
				<input name="m" value="elapp_customerservice" type="hidden">
				<input name="do" value="xcxcservice" type="hidden">
				<div class="form-group">
					<label class="col-xs-12 col-sm-2 control-label">选择小程序</label>
					<div class="col-xs-12 col-sm-4">
						<select name="xcxid" class="form-control">
							<option value="0">--请选择小程序--</option>
							{loop $xcxlist $row}
							<option value="{$row['id']}" {if $_GPC['xcxid'] == $row['id']}selected="selected"{/if}>{$row['name']}</option>
							{/loop}
						</select>
					</div>
					<div class="col-xs-12 col-sm-4">
						<button class="btn btn-default"><i class="fa fa-search"></i> 搜索</button>
						<button type="button" class="btn btn-default">总记录数：{php echo count($list);}</button>
					</div>
				</div>
			</form>
		</div>
	</div>
	
	<div class="panel panel-default">
		<div class="panel-body table-responsive">
			<form action="" method="post">
			<table class="table table-hover">
				<thead class="navbar-inner">
					<tr>
						<th style="width:8%;">排序</th>
						<th style="width:25%;">客服名称</th>
						<th style="width:10%;">客服头像</th>
						<th style="width:25%;">聚合信息</th>
						<th style="text-align:right;">操作</th>
					</tr>
				</thead>
				<tbody>
					{loop $list $row}
					<tr>
						<td><input class="form-control" name="displayorder[{$row['id']}]" value="{$row['displayorder']}" type="text" /></td>
						<td>{$row['name']}</td>
						<td><img src="{php echo tomedia($row['thumb']);}" width="50" height="50" /></td>
						<td>
							<div>名称：{$row['jhname']}</div>
							<div style="margin-top:5px;">内容：{$row['jhtext']}</div>
						</td>
						<td style="text-align:right;">
							<a href="{php echo $this->createWebUrl('xcxcservice', array('op' => 'msg', 'id' => $row['id']))}" class="btn btn-success btn-sm">聊天记录</a>&nbsp;&nbsp;
							<a href="{php echo $this->createWebUrl('xcxcservice', array('op' => 'sucai', 'id' => $row['id']))}" class="btn btn-info btn-sm">素材管理</a>&nbsp;&nbsp;
							<a href="{php echo $this->createWebUrl('xcxcservice', array('op' => 'post', 'id' => $row['id']))}" class="btn btn-default btn-sm" title="编辑"><i class="fa fa-edit"></i></a>&nbsp;&nbsp;
							<a href="{php echo $this->createWebUrl('xcxcservice', array('op' => 'delete', 'id' => $row['id']))}" onclick="return confirm('删除客服同时将删除微信客服的全部聊天记录，确认吗？');return false;" class="btn btn-default btn-sm" title="删除"><i class="fa fa-times"></i></a>
						</td>
					</tr>
					{/loop}
					<tr>
						<td></td>
						<td colspan="4">
							<input name="submit" class="btn btn-primary" value="提交" type="submit">
							<input name="token" value="{$_W['token']}" type="hidden">
						</td>
					</tr>
				</tbody>
			</table>
			</form>
		</div>
	</div>
</div>
<script>
	require(['bootstrap'],function($){
		$('.btn').hover(function(){
			$(this).tooltip('show');
		},function(){
			$(this).tooltip('hide');
		});
	});
</script>
{elseif $operation == 'post'}
<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" onsubmit='return formcheck()'>
		<input type="hidden" name="id" value="{$cservice['id']}" />
		
		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#basic" role="tab" data-toggle="tab">基本设置</a></li>
			<li role="presentation"><a href="#gzh" role="tab" data-toggle="tab">常用设置</a></li>
			<li role="presentation"><a href="#zaixian" role="tab" data-toggle="tab">在线时间设置</a></li>
		</ul>
		
		
		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active panel panel-default main" id="basic">
				<div class="panel-heading">小程序客服设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">排序</label>
						<div class="col-sm-2 col-xs-12">
							<input type="text" name="displayorder" class="form-control" value="{$cservice['displayorder']}" />
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服名称</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="name" class="form-control" value="{$cservice['name']}" />
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">所属小程序</label>
						<div class="col-sm-9 col-xs-12">
							<select name="xcxid" class="form-control">
								<option value="">--请选择--</option>
								{loop $xcxlist $prow}
									<option value="{$prow['id']}" {if $cservice['xcxid'] == $prow['id']}selected="selected"{/if}>{$prow['name']}</option>
								{/loop}
							</select>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服头像</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_image('thumb', $cservice['thumb'])}
							<span class="help-block" style="color:#900;">为了保证美观，请上传180*180尺寸的图片</span>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label"><span style="color:red">*</span>客服内容</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="content" class="form-control" value="{$cservice['content']}" /><br />
							<div class="alert alert-danger" role="alert">微信客服内容填openid，即需要成为客服的粉丝的编号，<a href="index.php?c=mc&a=fans" target="_blank" style="color:green;">快速获取openid</a></div>
						</div>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane panel panel-default main" id="gzh">
				<div class="panel-heading">小程序客服设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服欢迎语</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" style="height:100px;" name="autoreply">{$cservice['autoreply']}</textarea>
							<span class="help-block" style="color:#900;">欢迎语指用户进入会话事件时候触发</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服快捷消息</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" style="height:100px;" name="kefuauto">{$cservice['kefuauto']}</textarea>
							<span class="help-block" style="color:#900;">多个可用|隔开</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">权重值</label>
						<div class="col-sm-7 col-xs-12">							
							<input type="text" name="gzhqzval" disabled class="form-control" value="{$cservice['gzhqzval']}" />
							<span class="help-block" style="color:#900;">*权重值表示该客服是否优先接收到客服咨询，权重值越高表示接收的事件次数越多。系统机制是按同一小程序的所有客服权重平均值算法随机分配。</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">聚合内容</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="jhtext" class="form-control" value="{$cservice['jhtext']}" /><br />
							<div class="help-block" style="color:red;">*聚合内容是指例如同一个商家需要N个客服，那么聚合内容就填商家的id。同时小程序前端代码里面咨询客服的组件contact-button里，session-from的值带上商家id即可。</div>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">聚合名称</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="jhname" class="form-control" value="{$cservice['jhname']}" /><br />
							<div class="help-block" style="color:red;">*给聚合内容取个名称方便识别。</div>
						</div>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane panel panel-default main" id="zaixian">
				<div class="panel-heading">小程序客服设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服在线时间是否存在零节点</label>
						<div class="col-sm-9 col-xs-12">
							<label class='radio-inline'>
								<input type='radio' name='lingjie' value='1' {if $cservice['lingjie']==1}checked{/if} /> 是
							</label>
							<label class='radio-inline'>
								<input type='radio' name='lingjie' value='0' {if $cservice['lingjie']==0}checked{/if} /> 否
							</label>
							<span class="help-block" style="color:red;">零节点是指跨过0点的时间，例如13到3点</span>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服在线时间</label>
						<div class="col-sm-2 col-xs-12">
							<div class="input-group">
								<span class="input-group-addon">从</span>
								<input class="form-control" name="starthour" value="{$cservice['starthour']}" type="text">
								<span class="input-group-addon">点</span>
							</div>
						</div>
						<div class="col-sm-2 col-xs-12">
							<div class="input-group">
								<span class="input-group-addon">到</span>
								<input class="form-control" name="endhour" value="{$cservice['endhour']}" type="text">
								<span class="input-group-addon">点</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">在线星期制</label>
						<div class="col-sm-9 col-xs-12">
							<label class='radio-inline'>
								<input type='radio' name='isxingqi' value='1' {if $cservice['isxingqi']==1}checked{/if} /> 开启
							</label>
							<label class='radio-inline'>
								<input type='radio' name='isxingqi' value='0' {if $cservice['isxingqi']==0}checked{/if} /> 关闭
							</label>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">选择星期几在线</label>
						<div class="col-sm-9 col-xs-12">
							<label class='checkbox-inline'>
								<input type='checkbox' name='day1' {if $cservice['day1']==1}checked{/if} value='1' /> 星期一在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day2' {if $cservice['day2']==1}checked{/if} value='1' /> 星期二在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day3' {if $cservice['day3']==1}checked{/if} value='1' /> 星期三在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day4' {if $cservice['day4']==1}checked{/if} value='1' /> 星期四在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day5' {if $cservice['day5']==1}checked{/if} value='1' /> 星期五在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day6' {if $cservice['day6']==1}checked{/if} value='1' /> 星期六在线
							</label>
							<label class='checkbox-inline'>
								<input type='checkbox' name='day7' {if $cservice['day7']==1}checked{/if} value='1' /> 星期日在线
							</label>
						</div>
					</div>
				</div>
			</div>
		</div>
		
		
		<div class="form-group col-sm-12">
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			<input type="hidden" name="token" value="{$_W['token']}" />
		</div>
	</form>
</div>
{/if}
{else}
	<div class="alert alert-danger">{php echo urldecode(BEST_TISHI)}</div>
{/if}
{template 'common/footer'}