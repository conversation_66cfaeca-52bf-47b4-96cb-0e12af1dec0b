{template 'common/header'}

<ul class="nav nav-tabs">
	<li {if $operation == 'display'} class="active" {/if}><a href="{php echo $this->createWebUrl('cjwt',array('op' =>'display'))}">常见问题列表</a></li>

	<li{if empty($cjwt['id']) && $operation == 'post'} class="active" {/if}><a href="{php echo $this->createWebUrl('cjwt',array('op' =>'post'))}">添加常见问题</a></li>
	{if !empty($cjwt['id']) &&  $operation == 'post'}<li  class="active"><a href="{php echo $this->createWebUrl('cjwt',array('op' =>'post','id'=>$cjwt['id']))}">编辑常见问题</a></li>{/if}

</ul>

{if $operation == 'display'}
<div class="main panel panel-default">
	<div class="panel-body table-responsive">
		<form action="" method="post">
		<table class="table table-hover">
			<thead class="navbar-inner">
				<tr>
					<th style="width:8%;">排序</th>
					<th width="30%">标题</th>
					<th width="20%">发布时间</th>
					<th style="text-align:right;">操作</th>
				</tr>
			</thead>
			<tbody>
				{loop $list $cjwt}
				<tr>
					<td><input class="form-control" name="displayorder[{$cjwt['id']}]" value="{$cjwt['paixu']}" type="text"></td>
					<td><span class="label label-success">{$cjwt['title']}</span></td>
					<td>{php echo date("Y-m-d H:i:s",$cjwt['addtime'])}</td>
					<td style="text-align:right;">
						<a href="{php echo $this->createWebUrl('cjwt', array('op' => 'post', 'id' => $cjwt['id']))}" class="btn btn-default btn-sm" data-toggle="tooltip" data-placement="top" title="修改"><i class="fa fa-edit"></i></a>
						<a href="{php echo $this->createWebUrl('cjwt', array('op' => 'delete', 'id' => $cjwt['id']))}"class="btn btn-default btn-sm" onclick="return confirm('此操作不可恢复，确认删除？');return false;" data-toggle="tooltip" data-placement="top" title="删除"><i class="fa fa-times"></i></a>
					</td>
				</tr>
				{/loop}
				<tr>
					<td colspan="4">
						<input name="submit" class="btn btn-primary" value="提交" type="submit">
						<input name="token" value="{$_W['token']}" type="hidden">
						<span style="color:red;">*排序数值越大越靠前</span>
					</td>
				</tr>
			</tbody>
		</table>
		</form>
	</div>
	{$pager}
</div>
<script>

</script>
{elseif $operation == 'post'}
<div class="main">
	<form action="" method="post" class="form-horizontal form" enctype="multipart/form-data" onsubmit='return formcheck()'>
		<input type="hidden" name="id" value="{$cjwt['id']}" />
		<div class="panel panel-default">
			<div class="panel-heading">常见问题设置</div>
			<div class="panel-body">

				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">标题</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="title" class="form-control" value="{$cjwt['title']}" />
					</div>
				</div>

				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">常见问题内容</label>
					<div class="col-sm-9 col-xs-12">
						{php echo tpl_ueditor('des', $cjwt['des']);}
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">链接</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="url" class="form-control" value="{$cjwt['url']}" />
						<div class="help-block" style="color:red;">*填写连接后将直接跳到该链接页面</div>
					</div>
				</div>
				
				<div class="form-group">
					<label class="col-xs-12 col-sm-3 col-md-2 control-label">排序</label>
					<div class="col-sm-9 col-xs-12">
						<input type="text" name="paixu" class="form-control" value="{$cjwt['paixu']}" />
					</div>
				</div>
			</div>
		</div>
		<div class="form-group col-sm-12">
			<input type="submit" name="submit" value="提交" class="btn btn-primary col-lg-1" />
			<input type="hidden" name="token" value="{$_W['token']}" />
		</div>
	</form>
</div>
<script>
$(function(){

})
</script>
{/if}
{template 'common/footer'}