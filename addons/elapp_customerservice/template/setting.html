{template 'common/header'}
<div class="main">
	<form action="" method="post" class="form-horizontal form" id="form1">
		<ul class="nav nav-tabs" role="tablist">
			<li role="presentation" class="active"><a href="#basic" role="tab" data-toggle="tab">基本设置</a></li>
			<li role="presentation"><a href="#share" role="tab" data-toggle="tab">分享设置</a></li>
			<li role="presentation"><a href="#tplmsg" role="tab" data-toggle="tab">消息提醒设置</a></li>
			<li role="presentation"><a href="#allshare" role="tab" data-toggle="tab">共享设置</a></li>
			<li role="presentation"><a href="#color" role="tab" data-toggle="tab">色调设置</a></li>
			<li role="presentation"><a href="#nofollow" role="tab" data-toggle="tab">未关注设置</a></li>
			<li role="presentation"><a href="#qunliao" role="tab" data-toggle="tab">群聊设置</a></li>
			<li role="presentation"><a href="#fujian" role="tab" data-toggle="tab">七牛云存储</a></li>
			<!--<li role="presentation"><a href="#tuling" role="tab" data-toggle="tab">图灵机器人</a></li>-->
			<li role="presentation"><a href="#youhua" role="tab" data-toggle="tab">系统优化</a></li>
			<li role="presentation"><a href="#footer" role="tab" data-toggle="tab">底部菜单设置</a></li>
		</ul>
		
		<div class="tab-content">
			<div role="tabpanel" class="tab-pane active panel panel-info main" id="basic">
				<div class="panel-heading">基本设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服中心标题</label>
						<div class="col-sm-9 col-xs-12">
							<input name="title" class="form-control" value="{$settings['title']}" type="text">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">腾讯地图key</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="mapkey" class="form-control" value="{$settings['mapkey']}">
							<span class="help-block" style="color:red;">用于获取访客当前地区 <a href="http://lbs.qq.com/console/key.html" target="_blank" style="color:green;">点击申请key,申请请选择浏览器类型</a></span>
						</div>
					</div>	

					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">模板消息域名</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="tpldomain" class="form-control" value="{$settings['tpldomain']}">
							<span class="help-block" style="color:red;">默认使用当前域名，如果设置则使用设置的域名，必须带http或https，结尾以'/'结束</span>
						</div>
					</div>					
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">样式模板</label>
						<div class="col-sm-9 col-xs-12">
							<label for="chosekefutem0" class="radio-inline"><input name="chosekefutem" value="0" id="chosekefutem0" {if $settings['chosekefutem'] == 0}checked="true"{/if} type="radio"> 传统样式</label>
							&nbsp;&nbsp;&nbsp;
							<label for="chosekefutem1" class="radio-inline"><input name="chosekefutem" value="1" id="chosekefutem1" {if $settings['chosekefutem'] == 1}checked="true"{/if} type="radio"> 宫格样式</label>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">语音功能</label>
						<div class="col-sm-9 col-xs-12">
							<label for="voiceon0" class="radio-inline"><input name="voiceon" value="0" id="voiceon0" {if $settings['voiceon'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							&nbsp;&nbsp;&nbsp;
							<label for="voiceon1" class="radio-inline"><input name="voiceon" value="1" id="voiceon1" {if $settings['voiceon'] == 1}checked="true"{/if} type="radio"> 开启（七牛永久存储）</label>
							&nbsp;&nbsp;&nbsp;
							<label for="voiceon2" class="radio-inline"><input name="voiceon" value="2" id="voiceon2" {if $settings['voiceon'] == 2}checked="true"{/if} type="radio"> 开启（微信内置3天有效期）</label>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服随机展示</label>
						<div class="col-sm-9 col-xs-12">
							<label for="suiji1" class="radio-inline"><input name="suiji" value="1" id="suiji1" {if $settings['suiji'] == 1}checked="true"{/if} type="radio"> 开启</label>
							&nbsp;&nbsp;&nbsp;
							<label for="suiji0" class="radio-inline"><input name="suiji" value="0" id="suiji0" {if $settings['suiji'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							<span class="help-block" style="color:#900;">客服随机展示指的是在客服列表页面，或者客服组下面的客服列表页面，每次点开页面客服都随机排列</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">客服绑定模式</label>
						<div class="col-sm-9 col-xs-12">
							<label for="bdmodel2" class="radio-inline"><input name="bdmodel" value="1" id="bdmodel2" {if $settings['bdmodel'] == 1}checked="true"{/if} type="radio"> 开启</label>
							&nbsp;&nbsp;&nbsp;
							<label for="bdmodel1" class="radio-inline"><input name="bdmodel" value="0" id="bdmodel1" {if $settings['bdmodel'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							<span class="help-block" style="color:#900;">开启客服绑定模式后，如果客户有对应绑定的客服，那该客户只能和该客服聊天，否则会进行绑定操作。</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">敏感词过滤</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" name="mingan" style="height:100px;">{$settings['mingan']}</textarea>
							<span class="help-block" style="color:#900;">多个敏感词可用|隔开</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">版权信息</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" name="copyright" style="height:100px;">{$settings['copyright']}</textarea>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">前端管理员</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" name="admins" style="height:100px;">{$settings['admins']}</textarea>
							<span class="help-block" style="color:#900;">填入Openid，多个管理员openid可用|隔开</span>
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="share">
				<div class="panel-heading">分享设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">分享标题</label>
						<div class="col-sm-7 col-xs-12">
							<input name="sharetitle" class="form-control" value="{$settings['sharetitle']}" type="text">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">分享图片</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_image('sharethumb', $settings['sharethumb'], '', array('extras' => array('text' => 'readonly')))}
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">分享描述</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" name="sharedes">{$settings['sharedes']}</textarea>
						</div>
					</div>
				</div>
			</div>

			<div role="tabpanel" class="tab-pane panel panel-info main" id="tplmsg">
				<div class="panel-heading">消息提醒设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">消息提醒</label>
						<div class="col-sm-9 col-xs-12">
							<input type="text" name="tpl_kefu" class="form-control" value="{$settings['tpl_kefu']}"/>
							<div class="help-block">咨询处理通知 OPENTM414445930</div>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">开启消息提醒</label>
						<div class="col-sm-9 col-xs-12">
							<label for="istplon1" class="radio-inline"><input name="istplon" value="1" id="istplon1" {if $settings['istplon'] == 1}checked="true"{/if} type="radio"> 是</label>
							&nbsp;&nbsp;&nbsp;
							<label for="istplon2" class="radio-inline"><input name="istplon" value="0" id="istplon2" {if $settings['istplon'] == 0}checked="true"{/if} type="radio"> 否</label>
							<span class="help-block"></span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">消息提醒时间间隔</label>
						<div class="col-sm-2 col-xs-12">
							<div class="input-group">
								<input class="form-control" name="kefutplminute" value="{$settings['kefutplminute']}" type="text">
								<span class="input-group-addon">秒</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">开启群聊消息提醒</label>
						<div class="col-sm-9 col-xs-12">
							<label for="isgrouptplon1" class="radio-inline"><input name="isgrouptplon" value="1" id="isgrouptplon1" {if $settings['isgrouptplon'] == 1}checked="true"{/if} type="radio"> 是</label>
							&nbsp;&nbsp;&nbsp;
							<label for="isgrouptplon2" class="radio-inline"><input name="isgrouptplon" value="0" id="isgrouptplon2" {if $settings['isgrouptplon'] == 0}checked="true"{/if} type="radio"> 否</label>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">群聊消息提醒时间间隔</label>
						<div class="col-sm-2 col-xs-12">
							<div class="input-group">
								<input class="form-control" name="grouptplminute" value="{$settings['grouptplminute']}" type="text">
								<span class="input-group-addon">秒</span>
							</div>
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="allshare">
				<div class="panel-heading">共享设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">共享聊天记录</label>
						<div class="col-sm-9 col-xs-12">
							<label for="issharemsg1" class="radio-inline"><input name="issharemsg" value="1" id="issharemsg1" {if $settings['issharemsg'] == 1}checked="true"{/if} type="radio"> 开启</label>
							&nbsp;&nbsp;&nbsp;
							<label for="issharemsg2" class="radio-inline"><input name="issharemsg" value="0" id="issharemsg2" {if $settings['issharemsg'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							<span class="help-block"></span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">聊天记录共享类型</label>
						<div class="col-sm-9 col-xs-12">
							<label for="sharetype1" class="radio-inline"><input name="sharetype" value="1" id="sharetype1" {if $settings['sharetype'] == 1}checked="true"{/if} type="radio"> 客服组内共享</label>
							&nbsp;&nbsp;&nbsp;
							<label for="sharetype2" class="radio-inline"><input name="sharetype" value="0" id="sharetype2" {if $settings['sharetype'] == 0}checked="true"{/if} type="radio"> 全部客服共享</label>
							<span class="help-block"></span>
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="color">
				<div class="panel-heading">色调设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">聊天界面背景颜色</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_color('bgcolor',$settings['bgcolor']);}
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">界面主色调</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_color('temcolor',$settings['temcolor']);}
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">文字色调</label>
						<div class="col-sm-9 col-xs-12">
							{if $settings['textcolor']}
							{php echo tpl_form_field_color('textcolor',$settings['textcolor']);}
							{else}
							{php echo tpl_form_field_color('textcolor','#ffffff');}
							{/if}
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="nofollow">
				<div class="panel-heading">未关注设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">未关注用户头像</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_image('defaultavatar', $settings['defaultavatar'], '', array('extras' => array('text' => 'readonly')))}
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">未关注提醒</label>
						<div class="col-sm-9 col-xs-12">
							<label for="isshowwgz1" class="radio-inline"><input name="isshowwgz" value="1" id="isshowwgz1" {if $settings['isshowwgz'] == 1}checked="true"{/if} type="radio"> 开启</label>
							&nbsp;&nbsp;&nbsp;
							<label for="isshowwgz2" class="radio-inline"><input name="isshowwgz" value="0" id="isshowwgz2" {if $settings['isshowwgz'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							<span class="help-block"></span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">未关注说明</label>
						<div class="col-sm-7 col-xs-12">
							<textarea class="form-control" name="unfollowtext">{$settings['unfollowtext']}</textarea>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">公众号二维码</label>
						<div class="col-sm-9 col-xs-12">
							{php echo tpl_form_field_image('followqrcode', $settings['followqrcode'], '', array('extras' => array('text' => 'readonly')))}
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="qunliao">
				<div class="panel-heading">群聊设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">前端展示群聊</label>
						<div class="col-sm-9 col-xs-12">
							<label class="checkbox-inline"><input name="isgroupon" value="1" {if $settings['isgroupon'] == 1}checked="true"{/if} type="checkbox"> 是</label>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">群聊中心展示</label>
						<div class="col-sm-9 col-xs-12">
							<label for="groupshow1" class="radio-inline"><input name="groupshow" value="1" id="groupshow1" {if $settings['groupshow'] == 1}checked="true"{/if} type="radio">粉丝已加入群聊</label>
							&nbsp;&nbsp;&nbsp;
							<label for="groupshow2" class="radio-inline"><input name="groupshow" value="0" id="groupshow2" {if $settings['groupshow'] == 0}checked="true"{/if} type="radio">所有群聊</label>
						</div>
					</div>
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">群聊内人数</label>
						<div class="col-sm-9 col-xs-12">
							<label class="checkbox-inline"><input name="ishowgroupnum" value="1" {if $settings['ishowgroupnum'] == 1}checked="true"{/if} type="checkbox"> 展示</label>
						</div>
					</div>
				</div>
			</div>
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="fujian">
				<div class="panel-heading">七牛云存储设置</div>
				<div class="panel-body">
					<div class="remote-qiniu">
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">七牛云存储开关</label>
							<div class="col-sm-9 col-xs-12">
								<label for="isqiniu1" class="radio-inline"><input name="isqiniu" value="1" id="isqiniu1" {if $settings['isqiniu'] == 1}checked="true"{/if} type="radio"> 开启</label>
								&nbsp;&nbsp;&nbsp;
								<label for="isqiniu2" class="radio-inline"><input name="isqiniu" value="0" id="isqiniu2" {if $settings['isqiniu'] == 0}checked="true"{/if} type="radio"> 关闭</label>
								&nbsp;&nbsp;&nbsp;
								<label for="isqiniu3" class="radio-inline"><input name="isqiniu" value="3" id="isqiniu3" {if $settings['isqiniu'] == 3}checked="true"{/if} type="radio"> 使用系统统一配置</label>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">Accesskey</label>
							<div class="col-sm-9 col-xs-12">
								<input name="qiniuaccesskey" class="form-control" value="{$settings['qiniuaccesskey']}" placeholder="" type="text">
								<span class="help-block">用于签名的公钥</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">Secretkey</label>
							<div class="col-sm-9 col-xs-12">
								<input name="qiniusecretkey" class="form-control encrypt" value="{$settings['qiniusecretkey']}" placeholder="" type="text">
								<span class="help-block">用于签名的私钥</span>
							</div>
						</div>
						<div class="form-group" id="qiniubucket">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">Bucket</label>
							<div class="col-sm-9 col-xs-12">
								<input name="qiniubucket" class="form-control" value="{$settings['qiniubucket']}" placeholder="" type="text">
								<span class="help-block">请保证bucket为可公共读取的</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">Url</label>
							<div class="col-sm-9 col-xs-12">
								<input name="qiniuurl" class="form-control" value="{$settings['qiniuurl']}" placeholder="" type="text">
								<span class="help-block" style="color:red;">七牛支持用户自定义访问域名。注：url开头加http://或https://结尾不加 ‘/’例：http://abc.com</span>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label"></label>
							<div class="col-sm-9 col-xs-12">
								<button name="button" type="button" class="btn btn-info js-checkremoteqiniu" value="check">测试配置（无需保存）</button>
							</div>
						</div>
						<div class="form-group">
							<label class="col-xs-12 col-sm-3 col-md-2 control-label">语音转mp3列队</label>
							<div class="col-sm-3 col-xs-12">
								<input name="liedui" class="form-control encrypt" value="{$settings['liedui']}" placeholder="" type="text">
								<span class="help-block">请填写列队名称</span>
							</div>
						</div>
					</div>
					<script type="text/javascript">
					$('.js-checkremoteqiniu').on('click', function(){
						var key = $.trim($(':text[name="qiniuaccesskey"]').val());
						if (key == '') {
							alert('请填写Accesskey');
							return false;
						}
						var secret = $.trim($(':text[name="qiniusecretkey"]').val());
						if (secret == '') {
							alert('请填写Secretkey');
							return false;
						}
						var param = {
							'accesskey' : $.trim($(':text[name="qiniuaccesskey"]').val()),
							'secretkey' : $.trim($(':text[name="qiniusecretkey"]').val()),
							'url'  : $.trim($(':text[name="qiniuurl"]').val()),
							'bucket' :  $.trim($(':text[name="qiniubucket"]').val()),
							'district' :  $.trim($('[name="qiniuurl"]').val())
						};
		
						$.post("/web.php/system.attachment?do=qiniu&",param, function(data) {
							var data = $.parseJSON(data);
							if(data.message.errno == 0) {
								alert('配置成功');
								return false;
							}
							if(data.message.errno < 0) {
								alert(data.message.message);
								return false;
							}
						});
					});
					</script>
				</div>
			</div>
			
			<!--<div role="tabpanel" class="tab-pane panel panel-default" id="tuling">
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">图灵机器人的KEY</label>
						<div class="col-sm-7 col-xs-12">
							<input name="tulingkey" class="form-control" value="{$settings['tulingkey']}" type="text">
							<div class="help-block">如果没有申请KEY，请留空！！你也可以去<a href="http://www.tuling123.com/" target="_blank" style="text-decoration:underline;color:#FF0000">图灵</a>网站申请自己独立的KEY</div>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">是否开启</label>
						<div class="col-sm-9 col-xs-12">
							<label for="istulingon1" class="radio-inline"><input name="istulingon" value="1" id="istulingon1" {if $settings['istulingon'] == 1}checked="true"{/if} type="radio"> 是</label>
							&nbsp;&nbsp;&nbsp;
							<label for="istulingon2" class="radio-inline"><input name="istulingon" value="0" id="istulingon2" {if $settings['istulingon'] == 0}checked="true"{/if} type="radio"> 否</label>
						</div>
					</div>
				</div>
			</div>-->

			<div role="tabpanel" class="tab-pane panel panel-info main" id="youhua">
				<div class="panel-heading">系统优化</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-1 col-md-1 control-label"></label>
						<div class="col-sm-4 col-xs-12">
							<div class="input-group">
								<span class="input-group-addon">清除</span>
								<input class="form-control" name="days" id="days" value="14" type="text">
								<span class="input-group-addon">天前所有客服功能数据</span>
							</div>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-1 col-md-1 control-label"></label>
						<div class="col-sm-4 col-xs-12">
							<div class="input-group">
								<span class="input-group-addon">清除</span>
								<input class="form-control" name="days2" id="days2" value="14" type="text">
								<span class="input-group-addon">天前所有群聊功能数据</span>
							</div>
						</div>
					</div>

					<div class="form-group">
						<label class="col-xs-12 col-sm-1 col-md-1 control-label"></label>
						<div class="col-sm-4 col-xs-12">
							<button type="button" class="btn btn-success doyouhua">确定优化</button>
						</div>
					</div>
				</div>
			</div>
			
			
			<div role="tabpanel" class="tab-pane panel panel-info main" id="footer">
				<div class="panel-heading">底部菜单设置</div>
				<div class="panel-body">
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单1文字</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footertext1" class="form-control" value="{if empty($settings['footertext1'])}客服{else}{$settings['footertext1']}{/if}" type="text">
							<span class="help-block" style="color:#900;">不超过4个汉字</span>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单1图标</label>
						<div class="col-sm-3 col-xs-12">
							{php echo tpl_form_field_image('footer1thumb', $settings['footer1thumb'], '', array('extras' => array('text' => 'readonly')))}
							<span class="help-block" style="color:#900;">建议尺寸80*80，不设置则使用默认</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单2文字</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footertext2" class="form-control" value="{if empty($settings['footertext2'])}群聊{else}{$settings['footertext2']}{/if}" type="text">
							<span class="help-block" style="color:#900;">不超过4个汉字</span>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单2图标</label>
						<div class="col-sm-3 col-xs-12">
							{php echo tpl_form_field_image('footer2thumb', $settings['footer2thumb'], '', array('extras' => array('text' => 'readonly')))}
							<span class="help-block" style="color:#900;">建议尺寸80*80，不设置则使用默认</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">开启菜单3</label>
						<div class="col-sm-3 col-xs-12">
							<label for="footer4on1" class="radio-inline"><input name="footer4on" value="0" id="footer4on1" {if $settings['footer4on'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							&nbsp;&nbsp;&nbsp;
							<label for="footer4on2" class="radio-inline"><input name="footer4on" value="1" id="footer4on2" {if $settings['footer4on'] == 1}checked="true"{/if} type="radio"> 开启</label>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单3文字</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footertext3" class="form-control" value="{$settings['footertext3']}" type="text">
							<span class="help-block" style="color:#900;">建议不要超过4个汉字</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单3图标</label>
						<div class="col-sm-3 col-xs-12">
							{php echo tpl_form_field_image('footer4thumb', $settings['footer4thumb'], '', array('extras' => array('text' => 'readonly')))}
							<span class="help-block" style="color:#900;">建议尺寸80*80</span>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单3连接</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footer4url" class="form-control" value="{$settings['footer4url']}" type="text">
						</div>
					</div>
					
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">开启菜单4</label>
						<div class="col-sm-3 col-xs-12">
							<label for="footer5on1" class="radio-inline"><input name="footer5on" value="0" id="footer5on1" {if $settings['footer5on'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							&nbsp;&nbsp;&nbsp;
							<label for="footer5on2" class="radio-inline"><input name="footer5on" value="1" id="footer5on2" {if $settings['footer5on'] == 1}checked="true"{/if} type="radio"> 开启</label>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单4文字</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footertext4" class="form-control" value="{$settings['footertext4']}" type="text">
							<span class="help-block" style="color:#900;">建议不要超过4个汉字</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单4图标</label>
						<div class="col-sm-3 col-xs-12">
							{php echo tpl_form_field_image('footer5thumb', $settings['footer5thumb'], '', array('extras' => array('text' => 'readonly')))}
							<span class="help-block" style="color:#900;">建议尺寸80*80</span>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单4连接</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footer5url" class="form-control" value="{$settings['footer5url']}" type="text">
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">开启菜单5</label>
						<div class="col-sm-3 col-xs-12">
							<label for="footer6on1" class="radio-inline"><input name="footer6on" value="0" id="footer6on1" {if $settings['footer6on'] == 0}checked="true"{/if} type="radio"> 关闭</label>
							&nbsp;&nbsp;&nbsp;
							<label for="footer6on2" class="radio-inline"><input name="footer6on" value="1" id="footer6on2" {if $settings['footer6on'] == 1}checked="true"{/if} type="radio"> 开启</label>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单5文字</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footertext5" class="form-control" value="{$settings['footertext5']}" type="text">
							<span class="help-block" style="color:#900;">建议不要超过4个汉字</span>
						</div>
					</div>
					
					<div class="form-group">
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单5图标</label>
						<div class="col-sm-3 col-xs-12">
							{php echo tpl_form_field_image('footer6thumb', $settings['footer6thumb'], '', array('extras' => array('text' => 'readonly')))}
							<span class="help-block" style="color:#900;">建议尺寸80*80</span>
						</div>
						
						<label class="col-xs-12 col-sm-3 col-md-2 control-label">菜单5连接</label>
						<div class="col-sm-3 col-xs-12">
							<input name="footer6url" class="form-control" value="{$settings['footer6url']}" type="text">
						</div>
					</div>
				</div>
			</div>
						

			<div class="form-group col-sm-12">
				<input name="submit" type="submit" value="提交" class="btn btn-primary">
				<input type="hidden" name="token" value="{$_W['token']}" />
			</div>
		</div>
	</form>
</div>
<script type="text/javascript">
$(function(){
	$(".doyouhua").click(function(){
		var r=confirm("确定要优化吗？删除数据后不能恢复！")
		if (r==true){
			$.ajax({
				url:"{php echo $this->createWebUrl('youhua')}",
				data:{
					days:$("#days").val(),
					days2:$("#days2").val(),
				},
				dataType:'json',
				type:'post',        
				success:function(data){
					alert(data.msg);
				},
			});
		}else{
		}

	});
})
</script>
{template 'common/footer'}