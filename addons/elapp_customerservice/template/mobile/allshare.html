<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>共享聊天记录</title>	
	<link rel="stylesheet" href="{MD_ROOT}static/iconfont/iconfont.css?v=20171128"/>
	<link rel="stylesheet" href="{MD_ROOT}static/css/weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/newui/css/common.css?v=20180804"/>
	<link rel="stylesheet" href="{MD_ROOT}/emoji/emoji.css"/>
	{php echo register_jssdk(false);}
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	<style>
	body,html{width:100%;height:100%;background:#f5f5f5;}
	
	
	#chatcon .right .con .concon{background:{php echo $this->module["config"]['temcolor']};}
	#messifooter .docomment{background:{php echo $this->module["config"]['temcolor']};}
	#chatcon .right .con .triangle-right{border-left:0.15rem solid {php echo $this->module["config"]['temcolor']};}
	
	.weui-dialog__btn{color:{php echo $this->module["config"]['temcolor']};}
	.weui-photo-browser-modal{z-index:99;}
	</style>
</head>

<body style="background-color:{php echo $this->module['config']['bgcolor']};">
<div class="back2" onclick="history.go(-1)">
	<img src="{NEWSTATIC_ROOT}/icon/back.png" />
</div>
<div id="chatcon" class="messibox flex1">
	{loop $chatcon $row}
		{if !empty($row['time'])}<div class="time text-c">{php echo date('Y-m-d H:i:s',$row['time'])}</div>{/if}
		<div class="{$row['class']} flex">
			<img src="{$row['avatar']}" class="avatar" />
			<div class="con flex flex1">
				<div class="triangle-{$row['class']}"></div>
				{if $row['type'] == 3 || $row['type'] == 4}
				<div class="concon"><img src="{$row['content']}" class="sssbbb" /></div>
				{elseif $row['type'] == 5 || $row['type'] == 6}
					<div class="concon voiceplay flex" data-con="{$row['content']}">
						<img src="{NEWSTATIC_ROOT}/icon/voice2.png" class="voice2" />
						{if $row['hasyuyindu'] == 0 && $openid == $row['toopenid']}<span class="weidu">未读</span>{/if}
						<div class="flex1"></div>
					</div>
				{else}
					<div class="concon">{$row['content']}</div>
				{/if}
				<div class="flex1"></div>
			</div>
		</div>
	{/loop}
</div>
<script src="{MD_ROOT}/static/newui/js/jquery-3.1.1.min.js"></script>
<script src="{MD_ROOT}/static/newui/js/jquery-weui.min.js"></script>
<script type="text/javascript">
function playvoice(serverid,obj){
	wx.downloadVoice({
		serverId: serverid,
		success: function (res) {
			wx.playVoice({
				localId: res.localId, // 需要播放的音频的本地ID，由stopRecord接口获得
			});
			$.ajax({   
				url:"{php echo $this->createMobileUrl('shuaxinyuyin')}",   
				type:'post', 
				data:{
					content:serverid,
				},
				dataType:'json',
				success:function(data){ 
					if(data.error == 0){
						obj.remove();
					}
				}
			});
		}
	});
}
//查看QQ表情结果
function replace_em(str){
	str = str.replace(/\[em_([0-9]*)\]/g,'<img src="{MD_ROOT}/static/arclist/$1.png" style="width:0.4rem;height:0.4rem;margin-left:0.05rem;" border="0" />');
	return str;
}
$(function(){
	$('.concon').each(function(){
		$(this).html(replace_em($(this).html()));
	});
})
</script>
<script type="text/javascript">
wx.ready(function () {
	wx.hideOptionMenu();
	sharedata = {
		title: '{$nickname}的共享聊天记录',
		desc: '{php echo $this->module["config"]["sharedes"]}',
		link: '{php echo $this->module["config"]["shareurl"]}',
		imgUrl: '{php echo tomedia($this->module["config"]["sharethumb"]);}',
		trigger: function (res) {
			//alert('用户点击发送给朋友');
		},
		success: function (res) {
			//alert('已分享');
		},
		cancel: function (res) {
			//alert('已取消');
		},
		fail: function (res) {
			alert("分享失败");
		}
	};
	wx.onMenuShareAppMessage(sharedata);
	wx.onMenuShareTimeline(sharedata);
	wx.onMenuShareQQ(sharedata);
	wx.onMenuShareWeibo(sharedata);
});
</script>
</body>
</html>