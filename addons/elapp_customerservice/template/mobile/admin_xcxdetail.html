<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>和[{$xcxres['name']}]{if $biaoqian}[{$biaoqian['name']}]{/if}{if $hasfanskefu['fansnickname']}{$hasfanskefu['fansnickname']}{else}用户{/if}的对话</title>
	<link rel="stylesheet" href="{MD_ROOT}static/iconfont/iconfont.css?v=20171128"/>
	<link rel="stylesheet" href="{MD_ROOT}static/css/weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/newui/css/common.css?v=20180804"/>
	<link rel="stylesheet" href="{MD_ROOT}/emoji/emoji.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/newui/css/swiper-3.3.1.min.css"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	{php echo register_jssdk(false);}
	<style>
	body,html,#messibox{width:100%;height:100%;background:#f5f5f5;}
	
	#chatcon .right .con .concon{background:{php echo $this->module['config']['temcolor']};}

	#messifooter .docomment{background:{php echo $this->module['config']['temcolor']};margin-left:0.1rem;}
	#chatcon .right .con .triangle-right{border-left:0.15rem solid {php echo $this->module['config']['temcolor']};}
	
	.weui-dialog__btn{color:{php echo $this->module["config"]['temcolor']};}
	.weui-photo-browser-modal{z-index:99;}
	</style>
</head>

<body style="background-color:{php echo $this->module['config']['bgcolor']};">

<div id="messibox" class="flex">
	<div id="chatcon" class="messibox flex1">
		{loop $chatcon $row}
			{if !empty($row['time'])}
			<div class="time text-c">{php echo date('Y-m-d H:i:s',$row['time'])}</div>
			{/if}
			{if $row['openid'] != $openid}
				<div class="left flex">
					{if $hasfanskefu['fansavatar']}
					<img src="{$hasfanskefu['fansavatar']}" class="avatar" />
					{else}
					<img src="{MD_ROOT}static/xcx.png" class="avatar" />
					{/if}
					<div class="con flex flex1">
						<div class="triangle-left"></div>
						{if $row['msgtype'] == 'image'}
							<div class="concon"><img src="{$row['content']}" class="sssbbb" /></div>
						{/if}
						{if $row['msgtype'] == 'text'}
							<div class="concon">{$row['content']}</div>
						{/if}
						<div class="flex1"></div>
					</div>
				</div>
			{else}
				<div class="right flex">
					<img src="{$hasfanskefu['kefuavatar']}" class="avatar" />
					<div class="con flex flex1">
						<div class="triangle-right"></div>
						{if $row['msgtype'] == 'image'}
							<div class="concon"><img src="{$row['content']}" class="sssbbb" /></div>
						{/if}
						{if $row['msgtype'] == 'text'}
							<div class="concon">{$row['content']}</div>
						{/if}
						<div class="flex1"></div>
					</div>
				</div>
			{/if}
		{/loop}
	</div>
</div>
</body>
<script src="{MD_ROOT}/static/newui/js/socket.io.js"></script>
<script src="{MD_ROOT}/static/newui/js/jquery-3.1.1.min.js"></script>
<script src="{MD_ROOT}/static/newui/js/jquery-weui.min.js"></script>
<script src="{MD_ROOT}static/newui/js/swiper.min.js"></script>
<script src="{MD_ROOT}static/js/jquery.form.js"></script>
<script>
$(function(){
	$("#chatcon").on("click",".sssbbb", function() {
		$.ajax({
			url:"{php echo $this->createMobileUrl('getchatbigimgxcx')}",
			data:{
				fkid:{$hasfanskefu['id']},
				con:$(this).attr("src"),
			},
			dataType:'json',
			type:'post',        
			success:function(data){
				if(data.error == 0){
					var imglistjson = data.message.split(",");
					var pb = $.photoBrowser({
						items:imglistjson,
						initIndex:data.index,
					});
					pb.open();  //打开
				}
			},
		});
	});
});
</script>
<script type="text/javascript">
wx.ready(function () {
	wx.hideOptionMenu();
});
</script>
</html>