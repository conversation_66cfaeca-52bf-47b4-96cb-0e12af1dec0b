<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>消息管理{if $allwei > 0}({$allwei}){/if}</title>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/common.css?v=20190507"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	{php echo register_jssdk(false);}
	<style>		
	.chatlist{background:#fff;max-width:7.5rem;}

	.chatlist .item{padding:0.2rem;border-bottom:solid 1px #f1f1f1;position:relative;}
	.chatlist .item .mychatbadge{position:absolute;width:0.4rem;height;0.4rem;line-height:0.4rem;text-align:center;background:red;color:#fff;font-size:0.22rem;border-radius:100%;left:0.9rem;}
	.chatlist .item img{width:0.9rem;height:0.9rem;border-radius:100%;}
	.chatlist .item .text{margin-left:0.2rem;}
	.chatlist .item .text .name{height:0.4rem;line-height:0.4rem;color:#333;font-size:0.3rem;}
	.chatlist .item .text .lastmsg{height:0.4rem;line-height:0.4rem;font-size:0.26rem;margin-top:0.1rem;color:#999;}
	.chatlist .item .text .do{height:0.5rem;}
	.chatlist .item .timedo{margin-left:0.1rem;}
	.chatlist .item .timedo .time{font-size:0.24rem;color:#999;height:0.4rem;line-height:0.4rem;text-align:right;}
	.chatlist .item .timedo .dodel{font-size:0.24rem;color:red;height:0.4rem;line-height:0.4rem;margin-top:0.1rem;text-align:right;}

	.copyright{padding:0.2rem;font-size:0.28rem;color:#666;line-height:0.5rem;}
	.copyright a{color:#E64340;}
	
	.copyright a{color:{php echo $this->module["config"]['temcolor']};}
	
	.weui-dialog__btn{color:{php echo $this->module["config"]['temcolor']};}
	
	.xcx{font-size:0.24rem;color:#fff;background:#900;padding:0 0.1rem;border-radius:0.1rem;margin-right:0.1rem;}
	</style>
</head>

<body style="background:#f5f5f5;">
{if $chatlist}
<div class="chatlist">
	{loop $chatlist $row}
		<div class="item flex textellipsis1 fkid{$row['id']}">
			<a href="{php echo $this->createMobileUrl('xcxchat',array('fkid'=>$row['id'],'ssopenid'=>$openid))}" class="flex tohref textellipsis1 flex1">
				{if $row['fansavatar'] != ""}
				<img src="{$row['fansavatar']}">
				{else}
				<img src="{MD_ROOT}static/xcx.png">
				{/if}
				{if $row['notread'] > 0}
				<div class="mychatbadge">{$row['notread']}</div>
				{/if}

				<div class="text textellipsis1 flex1">
					<div class="name textellipsis1">{$row['fansnickname']}</div>
					<div class="lastmsg textellipsis1">
						{if $row['msgtype'] == 'image'}
							<span style="color:#900;">[图片消息]</span>
						{else}
							{php echo preg_replace('/\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]/', '[无法识别字符]', $row['lastcon'])}
						{/if}
					</div>
				</div>
			</a>
			<div class="timedo">
				<div class="time">
					{if $row['lasttime'] > 0}
						{php echo $this->getChatTimeStr($row['lasttime']);}
					{else}
						无
					{/if}
				</div>
				<div class="dodel" data-fkid="{$row['id']}">删除</div>
			</div>
		</div>
	{/loop}
</div>
{else}		
	<div class="nodata text-c">
		<img src="{NEWSTATIC_ROOT}/nodata.png" />
		<div class="text">暂无记录</div>
	</div>
{/if}
<div class="weui-loadmore hide" style="margin:0.2rem auto;color:#999;">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">正在加载</span>
</div>

<div class="copyright text-c">{php echo nl2br($this->module["config"]['copyright'])}</div>

<script src="{NEWSTATIC_ROOT}/jqueryweui/js/socket.io.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-3.1.1.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-weui.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/fastclick.js"></script>
<script type="text/javascript">
var uid = "{$openid}";
var touid = "";
var fkid = "";
var sendurl = "https://api.qiumipai.com:2121/?type=xcxpublish&to="+touid;
$(function(){
	FastClick.attach(document.body);
	
	// 连接服务端
	var socket = io('https://api.qiumipai.com:2120');
	// 连接后登录
	socket.on('connect', function(){
		socket.emit('login',{'uid':uid,'fkid':fkid});
	});
	
	// 后端推送来消息时
	socket.on('new_msg', function(msg){
		$(".chatlist .item").each(function(){
			$(".fkid"+msg.fkid).remove();
		});
		if(msg.notread > 0){
			var readhtml = '<div class="mychatbadge">'+msg.notread+'</div>';
		}else{
			var readhtml = '';
		}
		var newhref = "{php echo $this->createMobileUrl('xcxchat')}&fkid="+msg.fkid+"&ssopenid={$openid}";
		var newhtml = '<div class="item flex textellipsis1 fkid'+msg.fkid+'">'
							+'<a href="'+newhref+'" class="flex tohref textellipsis1 flex1">'
								+'<img src="'+msg.newavatar+'">'
								+readhtml+'<div class="text textellipsis1">'
									+'<div class="name textellipsis1">'+msg.newnickname+'</div>'
									+'<div class="lastmsg textellipsis1">'
									+msg.lastcon+'</div>'
								+'</div>'
							+'</a>'
							+'<div class="timedo">'
								+'<div class="time"></div>'
								+'<div class="dodel" data-fkid="'+msg.fkid+'">删除</div>'
							+'</div>'
						+'</div>';
		$(".chatlist").prepend(newhtml);
		$.ajax({
			url:"{php echo $this->createMobileUrl('ajaxallwei')}",
			data:{
			},
			dataType:'json',
			type:'post',        
			success:function(data){
				if(data > 0){
					$(document).attr("title","消息管理("+data+")");
				}
			},
		});
	});
	
	$(document).on("click",".dodel", function() {
		var fkid = $(this).attr('data-fkid');
		$.confirm("确定要删除吗？", function() {
			$.ajax({
				url:"{php echo $this->createMobileUrl('mychatxcx')}",
				data:{
					fkid:fkid,
					op:'delete',
				},
				dataType:'json',
				type:'post',        
				success:function(data){
					if(data.error == 1){
						$.alert(data.message);
					}else{
						$(".fkid"+fkid).remove();
					}
				},
			});
		}, function() {

		});
	});
})
//滚动加载
var loading = false;  //状态标记
var count = 2;
$(document.body).infinite().on("infinite", function() {
	if(loading) return;
	loading = true;
	if(count < {$allpage}){
		$('.weui-loadmore').removeClass('hide');
		setTimeout(function() {			
			$.ajax({
				url:"{php echo $this->createMobileUrl('mychatxcx')}",
				data:{
					page:count,
					isajax:1,
				},
				dataType:'html',
				type:'post',        
				success:function(data){
					if(data != ''){
						$('.chatlist').append(data);
						count++;
					}
					loading = false;
				},
			});
		}, 500);   //模拟延迟
	}else{
		$('.weui-loadmore .weui-loading').remove();
		$('.weui-loadmore .weui-loadmore__tips').text('全部数据已经加载完毕');
	}
});
wx.ready(function () {
	wx.hideOptionMenu();
});
</script>
<script type="text/javascript">
window.onpageshow = function(event){
	if (event.persisted) {
		window.location.reload();
	}
}
</script>
</body>
</html>