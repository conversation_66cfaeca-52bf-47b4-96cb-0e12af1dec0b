<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>{$cservice['name']}的客户列表</title>
    <link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/common.css?v=20190507"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	{php echo register_jssdk(false);}
	<style>	
	.chatlist .item{padding:0.2rem;border-bottom:solid 1px #f1f1f1;position:relative;background:#fff;}
	.chatlist .item .mychatbadge{
		position:absolute;width:0.4rem;height;0.4rem;line-height:0.4rem;
		text-align:center;background:red;color:#fff;font-size:0.22rem;
		border-radius:100%;left:0.9rem;
	}
	.chatlist .item a.tohref{-webkit-box-flex:1; -moz-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}	
	.chatlist .item img{width:0.9rem;height:0.9rem;border-radius:100%;}
	.chatlist .item .text{
		margin-left:0.2rem;
	}
	.chatlist .item .text .name{height:0.4rem;line-height:0.4rem;color:#333;font-size:0.3rem;}
	.chatlist .item .text .lastmsg{height:0.4rem;line-height:0.4rem;font-size:0.26rem;margin-top:0.1rem;color:#999;}

	.chatlist .item .timedo{width:1.4rem;margin-left:0.2rem;}
	.chatlist .item .timedo .time{font-size:0.24rem;color:#999;height:0.4rem;line-height:0.4rem;text-align:right;}
	
	.copyright a{color:{php echo $this->module["config"]['temcolor']};}
	</style>
</head>

<body style="background:#f5f5f5;">
{if $fanslist}
<div class="chatlist">
	{loop $fanslist $row}
	<div class="item flex textellipsis1">
		<a href="{php echo $this->createMobileUrl('qdadmin',array('fkid'=>$row['id'],'op'=>'kefuchatdetail'))}" class="flex tohref textellipsis1">
			<img src="{$row['fansavatar']}">
			{if $row['notread'] > 0}
			<div class="mychatbadge">{$row['notread']}</div>
			{/if}
			<div class="text textellipsis1 flex1">
				<div class="name textellipsis1">{$row['fansnickname']}</div>
				<div class="lastmsg textellipsis1">
					{if $row['msgtype'] == 4}
						<span style="color:#900;">[图片消息]</span>
					{else if $row['msgtype'] == 5}
						<span style="color:green;">[语音消息]</span>
					{else}
						{php echo preg_replace('/\xEE[\x80-\xBF][\x80-\xBF]|\xEF[\x81-\x83][\x80-\xBF]/', '[无法识别字符]', $row['lastcon'])}
					{/if}
				</div>
			</div>

			<div class="timedo">
				<div class="time">{if $row['lasttime'] > 0}{php echo $this->getChatTimeStr($row['lasttime']);}{else}暂无时间{/if}</div>
			</div>
		</a>
	</div>
	{/loop}
</div>
{else}		
	<div class="nodata text-c">
		<img src="{NEWSTATIC_ROOT}/nodata.png" />
		<div class="text">暂无任何客户</div>
	</div>
{/if}

<div class="copyright text-c">{php echo nl2br($this->module["config"]['copyright'])}</div>

<div class="weui-loadmore hide" style="margin:0.2rem auto;color:#999;">
	<i class="weui-loading"></i>
	<span class="weui-loadmore__tips">正在加载</span>
</div>

<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-3.1.1.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-weui.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/fastclick.js"></script>
<script type="text/javascript">
$(function(){
	FastClick.attach(document.body);
})

//滚动加载
var loading = false;  //状态标记
var count = 2;
$(document.body).infinite().on("infinite", function() {
	if(loading) return;
	loading = true;
	if(count < {$allpage}){
		$('.weui-loadmore').removeClass('hide');
		setTimeout(function() {			
			$.ajax({
				url:"{php echo $this->createMobileUrl('qdadmin',array('op'=>'kefudetail','content'=>$content))}",
				data:{
					page:count,
					isajax:1,
				},
				dataType:'html',
				type:'post',        
				success:function(data){
					if(data != ''){
						$('.chatlist').append(data);
						count++;
					}
					loading = false;
				},
			});
		}, 500);   //模拟延迟
	}else{
		$('.weui-loadmore .weui-loading').remove();
		$('.weui-loadmore .weui-loadmore__tips').text('全部数据已经加载完毕');
	}
});
wx.ready(function () {
	wx.hideOptionMenu();
});
</script>
</body>
</html>