<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>客服工作台</title>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/common.css?v=20190507"/>
	<script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	<style>
	html { width: 100%; height:100%; overflow:hidden; }
	body { 
		width: 100%;
		height:100%;
		font-family: 'Open Sans', sans-serif;
		background: #092756;
		background: -moz-radial-gradient(0% 100%, ellipse cover, rgba(104,128,138,.4) 10%,rgba(138,114,76,0) 40%),-moz-linear-gradient(top,  rgba(57,173,219,.25) 0%, rgba(42,60,87,.4) 100%), -moz-linear-gradient(-45deg,  #670d10 0%, #092756 100%);
		background: -webkit-radial-gradient(0% 100%, ellipse cover, rgba(104,128,138,.4) 10%,rgba(138,114,76,0) 40%), -webkit-linear-gradient(top,  rgba(57,173,219,.25) 0%,rgba(42,60,87,.4) 100%), -webkit-linear-gradient(-45deg,  #670d10 0%,#092756 100%);
		background: -o-radial-gradient(0% 100%, ellipse cover, rgba(104,128,138,.4) 10%,rgba(138,114,76,0) 40%), -o-linear-gradient(top,  rgba(57,173,219,.25) 0%,rgba(42,60,87,.4) 100%), -o-linear-gradient(-45deg,  #670d10 0%,#092756 100%);
		background: -ms-radial-gradient(0% 100%, ellipse cover, rgba(104,128,138,.4) 10%,rgba(138,114,76,0) 40%), -ms-linear-gradient(top,  rgba(57,173,219,.25) 0%,rgba(42,60,87,.4) 100%), -ms-linear-gradient(-45deg,  #670d10 0%,#092756 100%);
		background: -webkit-radial-gradient(0% 100%, ellipse cover, rgba(104,128,138,.4) 10%,rgba(138,114,76,0) 40%), linear-gradient(to bottom,  rgba(57,173,219,.25) 0%,rgba(42,60,87,.4) 100%), linear-gradient(135deg,  #670d10 0%,#092756 100%);
		filter: progid:DXImageTransform.Microsoft.gradient( startColorstr='#3E1D6D', endColorstr='#092756',GradientType=1 );
	}
	
	.kefumsg{text-align:center;margin-top:0.4rem;}
	.kefumsg .avatar{height:1.5rem;}
	.kefumsg .avatar img{width:1.5rem;height:1.5rem;border-radius:100%;margin:0 auto;}
	.kefumsg .kefuname{font-size:0.32rem;color:#fff;height:0.4rem;line-height:0.4rem;margin-top:0.2rem;}
	
	.kfmodule{
		padding-left:0.2rem;
		margin-top:0.4rem;
	}
	.kfmodule a{
		background:rgba(0,0,0,0.3); 
		border-radius: 0.1rem;
		text-align:center;
		color:#fff;
		font-size:0.26rem;
		width:2.233rem;padding:0.2rem 0;
		margin:0 0.2rem 0.2rem 0;
	}
	
	.sxx{
		position:fixed;
		left:2.75rem;
		bottom:0.5rem;
		width:2rem;
		height:2rem;
		line-height:2rem;
		background:#f5f5f5; 
		text-shadow:0 1px 1px rgba(255, 255, 255, 0.75);
		border: 1px solid #e6e6e6; 
		font-size:0.34rem;
		border-radius:100%;
		color:#333;
		text-align:center;
	}
	
	.search{
		padding:0 0.2rem;margin-top:0.4rem;
	}
	.search input{
		flex:1;background:#fff;border:none;border-radius:0.05rem;padding:0.1rem;
		font-size:0.28rem;color:#999;height:0.5rem;line-height:0.4rem;
	}
	.search button{
		width:1.4rem;text-align:center;color:#fff;font-size:0.28rem;
		margin-left:0.15rem;
		background: rgba(0,0,0,0.3);
		border: 0.01rem solid rgba(0,0,0,0.3);
		border-radius: 0.05rem;
	}
	
	.search-box{
		position:relative;
	}
	#searchres{
		position:absolute;z-index:999;padding:0.2rem;background:#fff;left:0.2rem;width:5.3rem;
		margin-top:0.05rem;border-radius:0.05rem;display:none;
	}
	
	.search-box .nosearchtext{
		font-size:0.26rem;color:#333;text-align:center;
	}
	.search-box .search-item{
		padding:0.1rem 0;border-bottom:dashed 1px #f1f1f1;
	}
	.search-box .search-item img{
		width:0.5rem;height:0.5rem;border-radius:100%;margin-right:0.2rem;
	}
	.search-box .search-item a{
		font-size:0.28rem;color:#333;height:0.5rem;line-height:0.5rem;
	}
	.weui-dialog__btn{
		color:#333;
	}
	</style>
</head>

<body ontouchstart>

<div class="kefumsg flex">
	<div class="flex1">
		<div class="avatar"><img src="{php echo tomedia($iscservice['thumb']);}" /></div>
		<div class="kefuname">{$iscservice['name']}</div>
	</div>
</div>

<form action="{php echo $this->createMobileUrl('kefucenter',array('op'=>'search'))}" method="post" id="searchform">
	<div class="search-box">
		<div class="search flex">
			<input type="text" name="nickname" placeholder="输入粉丝昵称搜索" />
			<button type="submit">搜索</button>
		</div>
		<div id="searchres"></div>
	</div>
</form>

<div class="kfmodule flex">
	<a href="{php echo $this->createMobileUrl('mychat')}">已接待 ({$total1})</a>
	<a href="{php echo $this->createMobileUrl('mychat',array('isjd'=>1))}">未接待 ({$total2})</a>
	<a href="{php echo $this->createMobileUrl('mychat',array('isjd'=>2))}">接待中 ({$total3})</a>
	<!--
	<a href="{php echo $this->createMobileUrl('kefucenter',array('op'=>'kehu'))}">客户管理</a>
	<a href="{php echo $this->createMobileUrl('kefucenter',array('op'=>'huanying'))}">欢迎语句</a>
	<a href="{php echo $this->createMobileUrl('kefucenter',array('op'=>'kuaijie'))}">快捷语句</a>
	<a href="{php echo $this->createMobileUrl('kefucenter',array('op'=>'time'))}">工作时间</a>
	<a href="{php echo $this->createMobileUrl('kefucenter',array('op'=>'liuyan'))}">留言管理</a>
	-->
	<div class="flex1"></div>
</div>

{if $iscservice['iszx'] == 1}
	{if $iscservice['isrealzx'] == 1}		
		<div class="sxx" onclick="xiaxian($(this));">手动离线</div>
	{else}
		<div class="sxx" onclick="xiaxian($(this));">手动上线</div>
	{/if}
{/if}

<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-3.1.1.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-weui.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/fastclick.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery.form.js"></script>
<script type="text/javascript">
var issubmit = false;  //状态标记
$("#searchform").ajaxForm({
	beforeSubmit:function(){
		if(issubmit){
			$.alert("请不要重复提交");
			return false;
		}else{
			issubmit = true;
		}
	},
	type: "POST",
	dataType:"json",
	success: function(data) {
		if(data.error == 1){
			$.alert(data.msg);
		}else{
			$("#searchres").show().html(data.html);
		}
		issubmit = false;
	},
});

$(function(){
	FastClick.attach(document.body);
})

function xiaxian(obj){
	$.ajax({
		url:"{php echo $this->createMobileUrl('kefucenter',array('op'=>'sxx'))}",
		data:{
		},
		dataType:'json',
		type:'post',        
		success:function(data){
			if(data.error == 0){
				history.go(0);
			}else{
				alert(data.message);
			}
		},
	});
}
</script>
</body>
</html>