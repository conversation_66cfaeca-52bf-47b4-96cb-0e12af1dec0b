<!DOCTYPE html>
<html style="background:#f5f5f5;">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>管理员中心</title>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/common.css?v=20190507"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	<style>
	.kefulist .item{padding:0.2rem;border-bottom:solid 1px #f1f1f1;background:#fff;}
	.kefulist .item img{width:0.8rem;height:0.8rem;border-radius:100%;}
	.kefulist .item .right{
		margin-left:0.2rem;
	}
	.kefulist .item .right .kefuname{
		line-height:0.8rem;
		font-size:0.3rem;
		color:#333;
	}
	.kefulist .item .right .fname{width:1.2rem;line-height:0.8rem;font-size:0.26rem;color:#999;}
	.kefulist .item .right .jtyou{width:0.4rem;height:0.4rem;margin-top:0.2rem;margin-left:0.1rem;}
	
	.search{padding:0.2rem;}
	.search input{flex:1;background:#fff;border:none;border-radius:0.1rem;padding:0.05rem 0.1rem;font-size:0.28rem;color:#999;height:0.5rem;line-height:0.4rem;}
	.search button{width:1.4rem;text-align:center;color:#fff;font-size:0.28rem;background:{php echo $this->module["config"]['temcolor']};border:none;margin-left:0.2rem;border-radius:0.1rem;}

	.copyright a, #footer .now .text{color:{php echo $this->module["config"]['temcolor']};}
	.weui-dialog__btn{color:{php echo $this->module["config"]['temcolor']};}
	</style>
</head>
<body style="padding-bottom:1rem;background:#f5f5f5;">

<form action="{php echo $this->createMobileUrl('qdadmin',array('op'=>'searchfensi'))}" id="form">
	<div class="search flex">
		<input type="text" name="keyword" placeholder="可根据客户昵称、标签、姓名、电话进行模糊搜索" />
		<button type="submit">搜索</button>
	</div>
</form>

<div class="kefulist">
	<div class="nodata text-c">
		<img src="{NEWSTATIC_ROOT}/nodata.png" />
		<div class="text">请搜索粉丝</div>
	</div>
</div>

<div class="copyright text-c">{php echo nl2br($this->module["config"]['copyright'])}</div>

<!--footer-->
<div id="footer" class="flex">
	<div class="item">
		<a href="{php echo $this->createMobileUrl('qdadmin');}">
			<img src="{NEWSTATIC_ROOT}/admin_kf.png" />
			<div class="text">客服列表</div>
		</a>
	</div>

	<div class="item now">
		<img src="{NEWSTATIC_ROOT}/admin_fs.png" />
		<div class="text">粉丝列表</div>
	</div>
</div>

<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-3.1.1.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-weui.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/fastclick.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery.form.js"></script>
<script type="text/javascript">
FastClick.attach(document.body);

$("#form").ajaxForm({
	type: "POST",
	dataType:"json",
	success: function(data) {
		if(data.error == 1){
			$.alert(data.message);
		}else{
			$('.kefulist').html(data.html);
		}
	},
});
</script>			
</body>
</html>