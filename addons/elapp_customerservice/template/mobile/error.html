<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>{$message}</title>
	<link rel="stylesheet" href="{MD_ROOT}static/newui/css/reset.css"/>
    <link rel="stylesheet" href="{MD_ROOT}static/iconfont/iconfont.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{MD_ROOT}static/newui/css/common.css"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
</head>

<body ontouchstart>
<div class="weui-msg">
    <div class="weui-msg__icon-area"><i class="weui-icon-warn weui-icon_msg-primary"></i></div>
		<div class="weui-msg__text-area">
			<h2 class="weui-msg__title">{$message}</h2>
		</div>
    </div>
<script type="text/javascript" src="{MD_ROOT}static/js/jquery-3.1.1.min.js"></script>
<script type="text/javascript" src="{MD_ROOT}static/jqueryweui/js/fastclick.js"></script>
<script type="text/javascript" src="{MD_ROOT}static/jqueryweui/js/jquery-weui.min.js"></script>
<script type="text/javascript">
$(function(){
	FastClick.attach(document.body);
})
</script>
</body>
</html>