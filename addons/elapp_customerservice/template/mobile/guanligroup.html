<!DOCTYPE html>
<html style="background:#f5f5f5;">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="initial-scale=1.0,maximum-scale=1.0,minimum-scale=1,user-scalable=no">
    <meta content="yes" name="apple-mobile-web-app-capable">
    <meta content="black" name="apple-mobile-web-app-status-bar-style">
    <meta content="telephone=no" name="format-detection">
    <meta content="email=no" name="format-detection">
    <title>{$isgroupadmin['groupname']}成员</title>	
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/jqueryweui/css/jquery-weui.min.css"/>
	<link rel="stylesheet" href="{NEWSTATIC_ROOT}/common.css?v=20190507"/>
    <script>
        var deviceWidth = document.documentElement.clientWidth;
        document.documentElement.style.fontSize = deviceWidth / 7.5 + 'px';
    </script>
	{php echo register_jssdk(false);}
	<style>
	.chatlist{background:#fff;}
	.chatlist .item{padding:0.2rem;border-bottom:solid 1px #f1f1f1;}
	.chatlist .item img{width:0.9rem;height:0.9rem;border-radius:100%;}
	.chatlist .item .text{margin-left:0.2rem;}
	.chatlist .item .text .name{height:0.4rem;line-height:0.4rem;color:#333;font-size:0.28rem;margin-top:0.05rem;}
	.chatlist .item .text .jrtime{height:0.4rem;line-height:0.3rem;color:#333;font-size:0.26rem;margin-top:0.1rem;color:#999;}
	.chatlist .item .do{height:0.5rem;margin-top:0.2rem;}
	.chatlist .item .do div{display:inline-block;float:right;margin-left:0.2rem;color:#fff;font-size:0.26rem;height:0.5rem;line-height:0.5rem;padding:0 0.2rem;border-radius:0.05rem;}
	.chatlist .item .do .yichu{background:red;}
	.chatlist .item .do .shenhe{background:#E64340;}
	
	.copyright a{color:{php echo $this->module["config"]['temcolor']};}
	.chatlist .item .do .shenhe{background:{php echo $this->module["config"]['temcolor']};}
	
	.weui-dialog__btn{color:{php echo $this->module["config"]['temcolor']};}
	</style>
</head>

<body style="background:#f5f5f5;padding-bottom;1.2rem;">

<div class="chatlist">
	{if $groupmemberlist}
		{loop $groupmemberlist $row}
			<div class="item flex">
				<img src="{$row['avatar']}">
				<div class="text flex1 textellipsis1">
					<div class="name textellipsis1">{$row['nickname']}</div>
					<div class="jrtime">{if $row['intime'] > 0}{php echo date("Y/m/d H:i:s",$row['intime'])}加入{else}未加入{/if}</div>
				</div>
				<div class="do">
					{if $row['status'] == 1}
					<div class="yichu" attr-id="{$row['id']}">移除</div>
					{else}
					<div class="yichu" attr-id="{$row['id']}">移除</div>
					<div class="shenhe" attr-id="{$row['id']}">审核</div>
					{/if}
				</div>
			</div>
		{/loop}
	{else}
		<div class="nodata text-c">
			<img src="{NEWSTATIC_ROOT}/nodata.png" />
			<div class="text">暂没有成员哦</div>
		</div>
	{/if}
</div>

<div class="copyright text-c">{php echo nl2br($this->module["config"]['copyright'])}</div>

<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-3.1.1.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/jquery-weui.min.js"></script>
<script src="{NEWSTATIC_ROOT}/jqueryweui/js/fastclick.js"></script>
<script type="text/javascript">
$(function(){
	FastClick.attach(document.body);
	
	$('.yichu').click(function(){
		var obj = $(this);
		$.confirm({
			title: '提示',
			text: '真的要移除吗？',
			onOK: function () {
				$.ajax({   
					 url:"{php echo $this->createMobileUrl('guanligroup')}",   
					 type:'post', 
					 data:{
						groupid:{$groupid},
						op:'del',
						memberid:obj.attr('attr-id'),
					 },
					 dataType:'json',
					 success:function(data){   
						if(data.error == 0){
							history.go(0);
						}else{
							$.alert(data.msg);
						}
					 }
				});
			},
			onCancel: function () {
			}
		});
	});
	
	
	$('.shenhe').click(function(){
		var obj = $(this);
		$.confirm({
			title: '提示',
			text: '真的要审核吗？',
			onOK: function () {
				$.ajax({   
					 url:"{php echo $this->createMobileUrl('guanligroup')}",   
					 type:'post', 
					 data:{
						groupid:{$groupid},
						op:'shenhe',
						memberid:obj.attr('attr-id'),
					 },
					 dataType:'json',
					 success:function(data){   
						if(data.error == 0){
							history.go(0);
						}else{
							$.alert(data.msg);
						}
					 }
				});
			},
			onCancel: function () {
			}
		});
	});
})
</script>
<script type="text/javascript">
wx.ready(function () {
	wx.hideOptionMenu();
});
</script>
</body>
</html>