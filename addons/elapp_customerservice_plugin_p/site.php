<?php
 goto QnRPe; hOfrp: define('BEST_XCX', 'messikefu_xcx'); goto iRot6; LP3tq: define('BEST_CSERVICE', 'messikefu_cservice'); goto FcW8K; HPlI2: define('BEST_KEFUANDGROUP', 'messikefu_kefuandgroup'); goto ltHug; xO6MD: define('ROOT_PATH', IA_ROOT . '/addons/elapp_customerservice_plugin_p/'); goto MPSq0; Bho9B: define('BEST_XCXFANSKEFU', 'messikefu_xcxfanskefu'); goto I8czJ; ltHug: define('BEST_BIAOQIAN', 'messikefu_biaoqian'); goto gARAU; JQnbF: define('MD_ROOT_Z', '../addons/elapp_customerservice/'); goto YwE2Y; QnRPe: defined('IN_IA') or exit('Access Denied'); goto xO6MD; iRot6: define('BEST_XCXCSERVICE', 'messikefu_xcxcservice'); goto Bho9B; MPSq0: define('MD_ROOT', '../addons/elapp_customerservice_plugin_p/static'); goto JQnbF; FcW8K: define('BEST_CSERVICEGROUP', 'messikefu_cservicegroup'); goto fDsuX; gARAU: define('BEST_KUAIJIE', 'messikefu_kuaijie'); goto hOfrp; Um6g1: define('BEST_PINGJIA', 'messikefu_pingjia'); goto HPlI2; I8czJ: define('BEST_XCXCHAT', 'messikefu_xcxchat'); goto ZWtKb; ip_qf: define('BEST_ZIDONGHUIFU', 'messikefu_zdhf'); goto xY0QB; fDsuX: define('BEST_FANSKEFU', 'messikefu_fanskefu'); goto Um6g1; YwE2Y: define('NEWSTATIC_ROOT', '../addons/elapp_customerservice/newstatic'); goto qhh90; ZWtKb: define('BEST_XCXAUTO', 'messikefu_xcxauto'); goto ip_qf; qhh90: define('BEST_CHAT', 'messikefu_chat'); goto LP3tq; xY0QB: class Cy163_customerservice_plugin_pModuleSite extends WeModuleSite { public function doMobileKefulogin() { include_once ROOT_PATH . 'inc/mobile/kefulogin.php'; } public function doMobileKefucenter() { include_once ROOT_PATH . 'inc/mobile/kefucenter.php'; } public function doMobileZhuanjie() { goto AweiD; WCTlM: $datafanskefu['kefuopenid'] = $content; goto uxRSv; UBGs5: if (empty($hasfanskefu)) { goto kAD6L; } goto GAqKQ; UF59Z: $datafanskefu['kefunickname'] = $zhuanjiekefu['name']; goto ixCC3; ES9A8: $datachat['nickname'] = $zhuanjiekefu['name']; goto UrDqB; wUb4B: $tplurl = str_replace('elapp_customerservice_plugin_p', 'elapp_customerservice', $tplurl); goto iNrtb; e0eSr: $resArr['error'] = 1; goto XhJ1K; kmP0x: $content = trim($_GPC['content']); goto yLS9x; XhJ1K: $resArr['msg'] = '获取用户数据失败！'; goto UWZn0; ZgvC8: $datafanskefu['fansavatar'] = tomedia($this->module['config']['defaultavatar']); goto yQSQ1; vtaJO: if (!empty($openid)) { goto Pm7yH; } goto rEJrc; Z_HME: $fansuser = $account_api->fansQueryInfo($toopenid); goto Y89EC; ixCC3: pdo_insert(BEST_FANSKEFU, $datafanskefu); goto Wvtyu; iVZUN: $this->sendtplmsg($senddata); goto sfOps; JnTH4: exit; goto OxewN; r1cMj: $resArr['msg'] = '请选择要转接的客服！'; goto p5ef8; cLiD7: $tplcon = '已转接至' . $zhuanjiekefu['name'] . '！'; goto o57GX; Y89EC: if (empty($fansuser)) { goto TDSyI; } goto GCunC; ZqT0W: $zhuanjiekefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND content = '{$content}'"); goto UBGs5; Vtdcd: if (empty($_COOKIE['openid'])) { goto COnxx; } goto FqefT; Snwqo: $datachat['toopenid'] = $toopenid; goto M6LL1; cqWnf: exit; goto c1BPB; p5ef8: echo json_encode($resArr); goto EKZrN; jTtWM: $datachat['fkid'] = $fkid; goto moWPy; FqefT: $_SESSION['openid'] = $_COOKIE['openid']; goto oQwEI; oQwEI: $openid = $_SESSION['openid']; goto UlrYo; iNrtb: $senddata = array("openid" => $content, "url" => $tplurl, "first" => $tplcon, "keyword1" => $zhuanjiekefu['name'], "wherefrom" => 1); goto iVZUN; moWPy: $datachat['openid'] = $content; goto Snwqo; GAqKQ: $fkid = $hasfanskefu['id']; goto hy9lF; ytjON: if (!empty($toopenid)) { goto pyFv4; } goto e0eSr; kcw4W: $resArr['msg'] = '转接成功'; goto lnL_G; wk5yj: pdo_update(BEST_FANSKEFU, array("nowjd" => 0), array("fansopenid" => $toopenid)); goto iClGL; Owc98: $datafanskefu['kefuavatar'] = tomedia($zhuanjiekefu['thumb']); goto UF59Z; LlJ9r: $datafanskefu['weid'] = $_W['uniacid']; goto ApNJs; uxRSv: $account_api = WeAccount::create(); goto Z_HME; VSB5U: FQuue: goto Owc98; dvy8f: kAD6L: goto LlJ9r; xqkVc: $datachat['weid'] = $_W['uniacid']; goto jTtWM; AweiD: global $_W, $_GPC; goto qIQ_o; FIptR: ZlL87: goto vtaJO; GCunC: $datafanskefu['fansavatar'] = empty($fansuser['headimgurl']) ? tomedia($this->module['config']['defaultavatar']) : $fansuser['headimgurl']; goto LQYsf; ApNJs: $datafanskefu['fansopenid'] = $toopenid; goto WCTlM; kofA4: $toopenid = trim($_GPC['toopenid']); goto ytjON; hAOcy: echo json_encode($resArr); goto JnTH4; yLS9x: if (!empty($content)) { goto zPV8o; } goto oyP2P; LQYsf: $datafanskefu['fansnickname'] = empty($fansuser['nickname']) ? '匿名用户' : $fansuser['nickname']; goto wZR2V; cuuSC: exit; goto DacIL; UrDqB: $datachat['avatar'] = tomedia($zhuanjiekefu['thumb']); goto K_Oh3; bqe2j: $datachat['time'] = TIMESTAMP; goto ZOSRR; DhOgA: zPV8o: goto nOTTv; rEJrc: $resArr['error'] = 1; goto Ii4Uu; lsdpq: $resArr['toopenid'] = $content; goto kcw4W; UlrYo: COnxx: goto FIptR; nOTTv: $hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$toopenid}' AND kefuopenid = '{$content}'"); goto ZqT0W; Ii4Uu: $resArr['msg'] = '请在微信浏览器中打开！'; goto hAOcy; y2gTB: TDSyI: goto ZgvC8; OxewN: Pm7yH: goto kofA4; EKZrN: exit; goto DhOgA; hy9lF: goto nNsFP; goto dvy8f; yQSQ1: $datafanskefu['fansnickname'] = '匿名用户'; goto VSB5U; iClGL: pdo_update(BEST_FANSKEFU, array("nowjd" => 1), array("id" => $fkid)); goto xqkVc; lnL_G: echo json_encode($resArr); goto cqWnf; UWZn0: echo json_encode($resArr); goto cuuSC; K_Oh3: $datachat['type'] = 1; goto bqe2j; Wvtyu: $fkid = pdo_insertid(); goto hepPt; DacIL: pyFv4: goto kmP0x; M6LL1: $datachat['content'] = '<span class="red">系统提醒：</span><span class="hui">已转接至' . $zhuanjiekefu['name'] . '为您继续提供服务！</span>'; goto ES9A8; mbDYa: if (!empty($openid)) { goto ZlL87; } goto Vtdcd; o57GX: $tplurl = $this->gettpldomain() . 'app/' . str_replace('./', '', $this->createMobileUrl('servicechat', array("toopenid" => $toopenid))); goto wUb4B; oyP2P: $resArr['error'] = 1; goto r1cMj; ZOSRR: pdo_insert(BEST_CHAT, $datachat); goto cLiD7; hepPt: nNsFP: goto gL7sE; sfOps: $resArr['error'] = 0; goto lsdpq; wZR2V: goto FQuue; goto y2gTB; gL7sE: $fanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE id = {$fkid}"); goto wk5yj; qIQ_o: $openid = $_SESSION['openid']; goto mbDYa; c1BPB: } public function doMobileFinishjd() { goto L0x1K; J0AtW: $id = intval($_GPC['id']); goto BRPHo; L0x1K: global $_W, $_GPC; goto cO9dq; v3dH0: goto KGLGf; goto QKBej; qRJVH: $dataupkefu['nowjdnum'] = $cservice['nowjdnum'] - 1; goto swEAz; BNeZ5: if (empty($lastjd)) { goto Ba4oU; } goto SWu7K; gj2EW: $dataupkefu['nowfkid'] = 0; goto qRJVH; SWu7K: $resArr['url'] = $this->createMobileUrl('kefucenter', array("toopenid" => $lastjd[0]['fansopenid'], "isjd" => $isjd)); goto v3dH0; BRPHo: $cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND id = {$id}"); goto gj2EW; BpxsP: echo json_encode($resArr); goto EH43t; CS7eq: $resArr['msg'] = '结束会话成功！'; goto BpxsP; cO9dq: $fkid = intval($_GPC['fkid']); goto xOiwM; QKBej: Ba4oU: goto e7ryK; swEAz: pdo_update(BEST_CSERVICE, $dataupkefu, array("id" => $cservice['id'])); goto XhV_Z; I8V4u: $lastjd = pdo_fetchall('SELECT fansopenid FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND kefuopenid = '{$cservice['content']}' AND nowjd = {$isjd} ORDER BY jdtime ASC LIMIT 1"); goto BNeZ5; x0qKM: pdo_update(BEST_FANSKEFU, $dataupfk, array("id" => $fkid)); goto J0AtW; xOiwM: $dataupfk['nowjd'] = 0; goto Rcb8E; lppq8: $resArr['error'] = 0; goto CS7eq; E4fOc: KGLGf: goto lppq8; EH43t: exit; goto Gxr9P; e7ryK: $resArr['url'] = $this->createMobileUrl('kefucenter', array("isjd" => $isjd)); goto E4fOc; Rcb8E: $dataupfk['jdtime'] = 0; goto x0qKM; XhV_Z: $isjd = intval($_GPC['isjd']); goto I8V4u; Gxr9P: } public function doMobileFinishjdxcx() { goto YZJGC; YZJGC: global $_W, $_GPC; goto QCJ1k; X0Uzg: $dataupfk['nowkefu'] = 0; goto PO8N6; z83gd: if (!($fkid == 0)) { goto oMO0v; } goto sDO5I; t6WQ9: exit; goto RQJ6O; sDO5I: $resArr['error'] = 1; goto Zf8PH; XXZ5d: $resArr['url'] = $this->createMobileUrl('kefucenter', array("isxcx" => 1)); goto WOLG5; WOLG5: $resArr['error'] = 0; goto Qw3zk; Qw3zk: $resArr['msg'] = '结束接待成功！'; goto N8sCr; QCJ1k: $fkid = intval($_GPC['fkid']); goto z83gd; bki6l: exit; goto Qttvd; hQvmY: echo json_encode($resArr); goto bki6l; Qttvd: oMO0v: goto X0Uzg; N8sCr: echo json_encode($resArr); goto t6WQ9; PO8N6: pdo_update(BEST_XCXFANSKEFU, $dataupfk, array("id" => $fkid)); goto XXZ5d; Zf8PH: $resArr['msg'] = '参数错误！'; goto hQvmY; RQJ6O: } public function doMobilePcupload() { goto e2mh3; ovl2D: $resarr['error'] = 1; goto snIaz; HPR21: zez4C: goto Ksabt; Boiv3: $this->mkThumbnail($targetName, 640, null, $targetName); goto aJd7t; Sb2lw: $resarr['message'] = '远程附件上传失败，请检查配置并重新上传'; goto mWlBL; mWlBL: die(json_encode($resarr)); goto VD57i; z3dm4: $resarr['imgurl'] = tomedia($randimgurl); goto oM9Wv; Pkuxp: load()->func('file'); goto yV6vg; kiaQM: $resarr['realimgurl'] = $randimgurl; goto z3dm4; kI5Zd: $resarr['imgurl'] = tomedia($randimgurl); goto fdz_K; KA5p_: $resarr['realimgurl'] = $randimgurl; goto xrzNQ; QzKfa: if (is_error($remotestatus)) { goto hRqaT; } goto KA5p_; ZuQdI: kra65: goto EKGsQ; RR7UP: die(json_encode($resarr)); goto gJL4S; qDysQ: $resarr['message'] = '远程附件上传失败，请检查配置并重新上传'; goto RR7UP; e2mh3: global $_W, $_FILES, $_GPC; goto uiBuO; Q5ixK: goto OL1yG; goto Yn0Wc; uHdBQ: exit; goto VY5MK; dnHxQ: echo json_encode($resarr, true); goto uHdBQ; fdz_K: $resarr['message'] = '上传成功'; goto uGTm3; taiIM: $img_info = getimagesize($targetName); goto LhjN9; yV6vg: $remotestatus = file_remote_upload($randimgurl, true); goto ZEyrA; LhjN9: if (!($img_info[0] > 640)) { goto qLQHR; } goto Boiv3; ZEyrA: if (is_error($remotestatus)) { goto L44Ee; } goto kiaQM; PG4u1: move_uploaded_file($url, $targetName); goto zezTT; FtT2P: pEIyP: goto UsR2W; qtUar: $resarr['error'] = 1; goto Sb2lw; GX8HU: goto xExXK; goto Hw0_Q; oM9Wv: $resarr['message'] = '上传成功'; goto pL0j1; iHZJK: die(json_encode($resarr)); goto Zie4V; HFB4P: $updir = '../attachment/images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/'; goto ZhskN; Cik5l: $resarr['imgurl'] = $qiniuurl . '/' . $randimgurl; goto B35hs; Yn0Wc: hUSfJ: goto pADgl; UsR2W: $resarr['realimgurl'] = $randimgurl; goto kI5Zd; Zie4V: goto DXams; goto gVwma; GSrK8: $randimgurl = 'images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.jpg'; goto geL2V; gVwma: hRqaT: goto nZ28U; fuq4d: XSC76: goto FtT2P; ZhskN: if (file_exists($updir)) { goto PaLuJ; } goto BwsBl; Ksabt: $remotestatus = $this->doQiuniu($randimgurl, true); goto QzKfa; BwsBl: mkdir($updir, 0777, true); goto X5XVU; zkeSC: goto pEIyP; goto ZuQdI; ddz9A: $isqiniu = $this->getmoduleconfig('isqiniu'); goto Z8tvt; gJL4S: DXams: goto zkeSC; pL0j1: die(json_encode($resarr)); goto GX8HU; uGTm3: OL1yG: goto dnHxQ; snIaz: $resarr['message'] = '上传文件失败'; goto Q5ixK; nZ28U: $resarr['error'] = 1; goto qDysQ; geL2V: $targetName = '../attachment/' . $randimgurl; goto PG4u1; CarJB: if ($isqiniu == 3) { goto kra65; } goto ebUUJ; VD57i: xExXK: goto fuq4d; B35hs: $resarr['message'] = '上传成功'; goto iHZJK; Z8tvt: if ($isqiniu == 1) { goto zez4C; } goto CarJB; Hw0_Q: L44Ee: goto qtUar; EKGsQ: if (empty($_W['setting']['remote']['type'])) { goto XSC76; } goto Pkuxp; X5XVU: PaLuJ: goto GSrK8; aJd7t: qLQHR: goto ddz9A; xrzNQ: $qiniuurl = $this->getmoduleconfig('qiniuurl'); goto Cik5l; pADgl: $resarr['error'] = 0; goto taiIM; ebUUJ: goto pEIyP; goto HPR21; uiBuO: $url = $_FILES['jUploaderFile']['tmp_name']; goto HFB4P; zezTT: if (file_exists($targetName)) { goto hUSfJ; } goto ovl2D; VY5MK: } public function doMobileJietuupload() { goto JKWBK; OK_s_: die(json_encode($resarr)); goto RAlSC; AVnhn: $resarr['error'] = 1; goto LZyLW; uQMFs: H_JWg: goto Qr1Uo; b8d2t: $this->mkThumbnail($targetName, 640, null, $targetName); goto KTxSB; J90H8: FTPI8: goto PtvlY; S5e6c: $randimgurl = 'images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.jpg'; goto b2Bbv; xC3pn: if (is_error($remotestatus)) { goto yn6cQ; } goto DI8Kx; ElkYD: $remotestatus = $this->doQiuniu($randimgurl, true); goto xC3pn; K2aPv: UWpFh: goto S5e6c; KxtsD: $resarr['message'] = '上传成功'; goto OK_s_; C_Cqk: goto Ozuy6; goto H7BQg; PtvlY: LHqpp: goto zoDVM; Qr1Uo: $resarr['error'] = 1; goto tmhFM; BgQk3: $resarr['error'] = 1; goto biwZf; teeIX: goto pOfVS; goto pVzfF; OYyuu: Stczm: goto qtTol; JKWBK: global $_W, $_GPC; goto oMjzu; K6W1f: goto Vf7CF; goto f8LXg; pVzfF: m99pH: goto xsuJ0; bq5Lf: $resarr['realimgurl'] = $randimgurl; goto ny0oU; RAlSC: goto FTPI8; goto uQMFs; ydyla: $base64_body = substr(strstr($base64_url, ','), 1); goto RC2CB; Um1Z3: $resarr['message'] = '上传成功'; goto bLW4n; oNZIt: if (file_exists($updir)) { goto UWpFh; } goto k8vG8; J3lit: load()->func('file'); goto WFPPf; zoDVM: Ozuy6: goto IOdda; WFPPf: $remotestatus = file_remote_upload($randimgurl, true); goto f0u9U; cnY1A: $img_info = getimagesize($targetName); goto hYyDC; LisWS: goto Ozuy6; goto OYyuu; bMdJh: $resarr['imgurl'] = tomedia($randimgurl); goto Um1Z3; WZ56L: Vf7CF: goto LisWS; id9Hk: $qiniuurl = $this->getmoduleconfig('qiniuurl'); goto O1s6x; SJP0a: file_put_contents($targetName, $base64_data); goto EZOsZ; bLW4n: pOfVS: goto RsMJR; LZyLW: $resarr['message'] = '远程附件上传失败，请检查配置并重新上传'; goto koN1X; k8vG8: mkdir($updir, 0777, true); goto K2aPv; oMjzu: $base64_url = $_GPC['img']; goto ydyla; RC2CB: $base64_data = base64_decode($base64_body); goto qI3n3; EZOsZ: if (file_exists($targetName)) { goto m99pH; } goto BgQk3; JyVZO: exit; goto t79C2; xsuJ0: $resarr['error'] = 0; goto cnY1A; p7yJC: $resarr['message'] = '上传成功'; goto z6Ln3; qtTol: if (empty($_W['setting']['remote']['type'])) { goto LHqpp; } goto J3lit; IOdda: $resarr['realimgurl'] = $randimgurl; goto bMdJh; b2Bbv: $targetName = '../attachment/' . $randimgurl; goto SJP0a; DI8Kx: $resarr['realimgurl'] = $randimgurl; goto id9Hk; RsMJR: echo json_encode($resarr, true); goto JyVZO; KTxSB: qd1HX: goto oh38z; tmhFM: $resarr['message'] = '远程附件上传失败，请检查配置并重新上传'; goto zRaIN; ny0oU: $resarr['imgurl'] = tomedia($randimgurl); goto KxtsD; koN1X: die(json_encode($resarr)); goto WZ56L; f8LXg: yn6cQ: goto AVnhn; z6Ln3: die(json_encode($resarr)); goto K6W1f; hYyDC: if (!($img_info[0] > 640)) { goto qd1HX; } goto b8d2t; FGoZR: if ($isqiniu == 3) { goto Stczm; } goto C_Cqk; O1s6x: $resarr['imgurl'] = $qiniuurl . '/' . $randimgurl; goto p7yJC; oh38z: $isqiniu = $this->getmoduleconfig('isqiniu'); goto B38K0; biwZf: $resarr['message'] = '上传文件失败'; goto teeIX; H7BQg: jIYVv: goto ElkYD; f0u9U: if (is_error($remotestatus)) { goto H_JWg; } goto bq5Lf; qI3n3: $updir = '../attachment/images/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/'; goto oNZIt; B38K0: if ($isqiniu == 1) { goto jIYVv; } goto FGoZR; zRaIN: die(json_encode($resarr)); goto J90H8; t79C2: } public function doQiuniu($filename, $auto_delete_local = true) { goto ko6Zx; lpV3h: file_delete($filename); goto UKyWV; JtOLm: $uploadtoken = $auth->uploadToken($qiniubucket, $filename, 3600, $putpolicy); goto CFLuO; JdgUo: return true; goto gFv0r; q1ysy: $config = new Qiniu\Config(); goto TsVuK; keYon: require_once IA_ROOT . '/extend/framework/library/qiniu/autoload.php'; goto e84fj; P6F23: $isqiniu = $this->getmoduleconfig('isqiniu'); goto GL10_; i0cY0: $qiniubucket = $isqiniu == 1 ? $qiniubucket : $_W['setting']['remote']['qiniu']['bucket']; goto diT5Y; ytDH3: $resarr['message'] = '远程附件上传失败，请检查配置并重新上传'; goto VRCRh; yetlR: yxIEG: goto ZI0D5; GL10_: $qiniuaccesskey = $this->getmoduleconfig('qiniuaccesskey'); goto xj8A0; VRCRh: die(json_encode($resarr)); goto gxyl5; DNx34: $putpolicy = Qiniu\base64_urlSafeEncode(json_encode(array("scope" => $qiniubucket . ':' . $filename))); goto JtOLm; Y4lkz: $qiniusecretkey = $isqiniu == 1 ? $qiniusecretkey : $_W['setting']['remote']['qiniu']['secretkey']; goto i0cY0; Kc6CH: if ($err !== null) { goto yxIEG; } goto JdgUo; gFv0r: goto ETj8t; goto yetlR; UKyWV: MRfBk: goto Kc6CH; diT5Y: load()->func('file'); goto keYon; ZI0D5: $resarr['error'] = 1; goto ytDH3; PJZtR: if (!$auto_delete_local) { goto MRfBk; } goto lpV3h; TsVuK: $uploadmgr = new Qiniu\Storage\UploadManager($config); goto DNx34; ko6Zx: global $_W; goto P6F23; gxyl5: ETj8t: goto I9wvt; CFLuO: list($ret, $err) = $uploadmgr->putFile($uploadtoken, $filename, ATTACHMENT_ROOT . '/' . $filename); goto PJZtR; e84fj: $auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey); goto q1ysy; Qe1qy: $qiniubucket = $this->getmoduleconfig('qiniubucket'); goto AWy8g; AWy8g: $qiniuaccesskey = $isqiniu == 1 ? $qiniuaccesskey : $_W['setting']['remote']['qiniu']['accesskey']; goto Y4lkz; xj8A0: $qiniusecretkey = $this->getmoduleconfig('qiniusecretkey'); goto Qe1qy; I9wvt: } public function mkThumbnail($src, $width = null, $height = null, $filename = null) { goto YYg38; vJoGD: $height = $src_h * ($width / $src_w); goto PCzIw; ByKzD: $width = $src_w * ($height / $src_h); goto MSpdY; B9fQE: if (!(isset($width) && $width <= 0)) { goto ZAt41; } goto aRC5L; UcVEy: header('Content-Type: ' . $src_mime); goto GffU8; gsRiK: if (isset($height)) { goto SAbdu; } goto vJoGD; Ty0BS: imagedestroy($dest_img); goto r9i97; UGIkH: $size = getimagesize($src); goto wzdGf; lksSJ: imagecopyresampled($dest_img, $src_img, 0, 0, 0, 0, $width, $height, $src_w, $src_h); goto eAMJI; ATYF0: okRDv: goto hABCd; AIQYg: if (!(isset($height) && $height <= 0)) { goto kcx85; } goto WqEvv; cuas0: if (isset($width)) { goto h2_WI; } goto ByKzD; DdEBI: goto XMv_P; goto uYj51; aRC5L: return false; goto D8Jln; MSpdY: h2_WI: goto gsRiK; kayjG: return false; goto k1PO1; GffU8: $imagefunc($dest_img); goto DdEBI; MPyd_: $src_img = $imagecreatefunc($src); goto pZxjv; r9i97: return true; goto N609x; Y1bpF: XMv_P: goto V_fTg; YYg38: if (!(!isset($width) && !isset($height))) { goto cczYc; } goto cIXjZ; uYj51: DuU09: goto b9yKD; b9yKD: $imagefunc($dest_img, $filename); goto Y1bpF; oWdQj: $src_mime = $size['mime']; goto MhsZF; UcJNz: $imagecreatefunc = 'imagecreatefrom' . $img_type; goto MPyd_; pZxjv: $dest_img = imagecreatetruecolor($width, $height); goto lksSJ; MhsZF: switch ($src_type) { case 1: $img_type = 'gif'; goto VncSo; case 2: $img_type = 'jpeg'; goto VncSo; case 3: $img_type = 'png'; goto VncSo; case 15: $img_type = 'wbmp'; goto VncSo; default: return false; } goto ATYF0; hABCd: VncSo: goto cuas0; eAMJI: $imagefunc = 'image' . $img_type; goto VmKxA; D8Jln: ZAt41: goto AIQYg; PCzIw: SAbdu: goto UcJNz; k1PO1: SB8ml: goto XQMHD; WqEvv: return false; goto SyKNY; wzdGf: if ($size) { goto SB8ml; } goto kayjG; azw1r: cczYc: goto B9fQE; VmKxA: if ($filename) { goto DuU09; } goto UcVEy; V_fTg: imagedestroy($src_img); goto Ty0BS; SyKNY: kcx85: goto UGIkH; cIXjZ: return false; goto azw1r; XQMHD: list($src_w, $src_h, $src_type) = $size; goto oWdQj; N609x: } public function getmoduleconfig($key) { goto Ex6K_; LQFQ_: $settings = unserialize($setting['settings']); goto M_89O; M_89O: return $settings[$key]; goto IVNvA; Ex6K_: global $_W, $_GPC; goto OFPcK; OFPcK: $setting = pdo_fetch('SELECT settings FROM ' . tablename('uni_account_modules') . " WHERE uniacid = {$_W['uniacid']} AND module = 'elapp_customerservice'"); goto LQFQ_; IVNvA: } public function doMobileDuvoice() { goto dpdpD; JOWDJ: pdo_update(BEST_CHAT, $dataup, array("id" => $chatres['id'])); goto EUz2t; vUNu2: die(json_encode($resarr)); goto YYdcI; w6vwF: if (!($chatres['openid'] != $openid)) { goto sTIjn; } goto J0Syz; k5hhv: $resarr['error'] = 0; goto vUNu2; J0Syz: $dataup['hasyuyindu'] = 1; goto JOWDJ; WR5hP: $fkid = intval($_GPC['fkid']); goto nE3z3; EUz2t: sTIjn: goto k5hhv; fuUfF: $media_id = $_GPC['media_id']; goto WR5hP; dpdpD: global $_W, $_GPC; goto spsOp; nE3z3: $chatres = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND content = '{$media_id}' AND fkid = {$fkid}"); goto w6vwF; spsOp: $openid = $_SESSION['openid']; goto fuUfF; YYdcI: } public function doMobileGetvoice() { goto LpUa0; JBhi_: mkdir($updir, 0777, true); goto Ypdsx; ofui6: dMAMP: goto gyQHN; ELHrX: $policy = array("persistentOps" => $fops); goto D4EXn; AXzdS: $fkid = intval($_GPC['fkid']); goto fxyHU; B9zdJ: $chatres = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND content = '{$media_id}' AND fkid = {$fkid}"); goto SkWqs; Qomj0: rjfqs: goto lq63z; dIXL8: if (!empty($liedui)) { goto kM12D; } goto ELHrX; LpUa0: global $_W, $_GPC; goto ZF33Z; F80M4: aLlHd: goto ogMnr; QASDz: $resarr['error'] = 1; goto IJK2Y; RFmXS: $qiniubucket = $isqiniu == 1 ? $qiniubucket : $_W['setting']['remote']['qiniu']['bucket']; goto l_4F6; x4RZo: $resarr['voicefile'] = $qiniuurl . '/' . $ret['key']; goto zq1fK; rlQxf: die(json_encode($resarr)); goto keULW; l_4F6: $qiniuurl = $isqiniu == 1 ? $qiniuurl : $_W['setting']['remote']['qiniu']['url']; goto vDvC3; nG7HU: $access_token = $account_api->getAccessToken(); goto DJ24r; SJNxk: if ($err !== null) { goto S_o_k; } goto criCS; Em7Vu: $resarr['error'] = 0; goto x4RZo; vDvC3: if (!($isqiniu == 0 || $qiniuaccesskey == '' || $qiniusecretkey == '' || $qiniubucket == '')) { goto dMAMP; } goto QASDz; G6JrA: $resarr['msg'] = '远程附件上传失败，请检查配置并重新上传'; goto rlQxf; fjV1B: $isqiniu = $this->getmoduleconfig('isqiniu'); goto ovAVM; y85V2: kM12D: goto Wu2NC; S6Z2e: $fp = @fopen($targetName, 'wb'); goto HaWOd; TXD4R: $auth = new Qiniu\Auth($qiniuaccesskey, $qiniusecretkey); goto i3tLn; OadCe: $resarr['msg'] = "访问微信接口错误, 错误代码: {$result['errcode']}, 错误信息: {$result['errmsg']}"; goto RhYFu; pVXP_: $dataup['hasyuyindu'] = 1; goto acHO6; BKHjz: $resarr['msg'] = '语音正在加载中，请稍后！'; goto Tzrk4; mFLMD: Pfv9X: goto bjyD1; acHO6: $resarr['weidu'] = 1; goto F80M4; tFTBV: die(json_encode($resarr)); goto ofui6; peZgK: $qiniuaccesskey = $isqiniu == 1 ? $qiniuaccesskey : $_W['setting']['remote']['qiniu']['accesskey']; goto tC_01; O9aRr: @fclose($fp); goto kS5V6; fX8fx: die(json_encode($resarr)); goto Uf103; b7SGH: sleep(5); goto oq4RI; d9zAG: $uploadmgr = new Qiniu\Storage\UploadManager($config); goto kOlhJ; DJ24r: $url = 'http://file.api.weixin.qq.com/cgi-bin/media/get?access_token=' . $access_token . '&media_id=' . $media_id; goto TfZbw; Mc_T6: $dataup['content'] = $qiniuurl . '/' . $ret['key']; goto E4b7s; ZBXlm: tFI3j: goto QD1K_; kbB7y: goto how6Y; goto YEyAN; TXc4M: $liedui = $this->getmoduleconfig('liedui'); goto NC4IM; kOlhJ: $putpolicy = Qiniu\base64_urlSafeEncode($qiniubucket . ':' . $savemp3); goto AfqY_; s7Z73: load()->func('communication'); goto fjV1B; cxIfx: $resarr['error'] = 1; goto bA6dE; OPdmv: load()->func('file'); goto Fcmf_; criCS: pdo_update(BEST_CHAT, array("mp3du" => 1), array("id" => $chatres['id'])); goto b7SGH; v4Rld: $resarr['error'] = 0; goto MkVBN; Tzrk4: die(json_encode($resarr)); goto SBXBL; tC_01: $qiniusecretkey = $isqiniu == 1 ? $qiniusecretkey : $_W['setting']['remote']['qiniu']['secretkey']; goto RFmXS; u6ciz: $updir = '../attachment/audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/'; goto pu80c; gyQHN: $media_id = $_GPC['media_id']; goto AXzdS; TfZbw: $response = ihttp_get($url); goto caeDK; YEyAN: S_o_k: goto kgxRq; SBXBL: D10f1: goto j9l6R; Bb6Zy: $chatres2 = pdo_fetch('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE weid = {$_W['uniacid']} AND id = {$chatid}"); goto JG4a6; lq63z: $account_api = WeAccount::create(); goto nG7HU; s4h0T: $qiniuurl = $this->getmoduleconfig('qiniuurl'); goto peZgK; j9l6R: if (!(strpos($chatres['content'], '.mp3') !== false)) { goto rjfqs; } goto v4Rld; Uf103: g9Wza: goto w_O8h; nJ3lt: $fops = $fops . '|saveas/' . $putpolicy; goto TXc4M; JG4a6: if (!empty($chatres2)) { goto Pfv9X; } goto cxIfx; keULW: how6Y: goto UWC02; RhYFu: die(json_encode($resarr)); goto CQnLH; YFMxI: die(json_encode($resarr)); goto mFLMD; w_O8h: $result = @json_decode($response['content'], true); goto lmsRZ; MkVBN: $resarr['voicefile'] = $chatres['content']; goto cBEQ5; bA6dE: $resarr['msg'] = '获取微信媒体参数失败！'; goto YFMxI; NC4IM: $liedui = empty($liedui) ? '' : $liedui; goto dIXL8; fxyHU: $chatid = intval($_GPC['chatid']); goto B9zdJ; CQnLH: RU33P: goto u6ciz; DCs2o: $resarr['error'] = 1; goto BKHjz; QD1K_: if (!($chatres['mp3du'] == 1)) { goto D10f1; } goto DCs2o; GjBDi: e16nH: goto tkZGm; caeDK: if (!is_error($response)) { goto g9Wza; } goto g77Sw; oOigv: $qiniubucket = $this->getmoduleconfig('qiniubucket'); goto s4h0T; Ew46i: $qiniusecretkey = $this->getmoduleconfig('qiniusecretkey'); goto oOigv; dAsGT: $randvoiceurl = 'audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.amr'; goto bhESn; AviU8: die(json_encode($resarr)); goto kbB7y; lmsRZ: if (empty($result['errcode'])) { goto RU33P; } goto FM7WC; zq1fK: $resarr['msg'] = '解析成功'; goto AviU8; HaWOd: @fwrite($fp, $response['content']); goto O9aRr; bhESn: $targetName = '../attachment/' . $randvoiceurl; goto S6Z2e; Fcmf_: require_once IA_ROOT . '/extend/framework/library/qiniu/autoload.php'; goto TXD4R; IJK2Y: $resarr['msg'] = '获取语音资源配置错误！'; goto tFTBV; g77Sw: $resarr['error'] = 1; goto fG5Vy; cBEQ5: die(json_encode($resarr)); goto Qomj0; ADKen: pdo_update(BEST_CHAT, $dataup, array("id" => $chatres['id'])); goto Em7Vu; SkWqs: if (!empty($chatres)) { goto tFI3j; } goto Bb6Zy; kS5V6: $savemp3 = 'audios/' . $_W['uniacid'] . '/' . date('Y', time()) . '/' . date('m', time()) . '/' . date('YmdHis') . rand(1000, 9999) . '.mp3'; goto OPdmv; FM7WC: $resarr['error'] = 1; goto OadCe; kgxRq: $resarr['error'] = 1; goto G6JrA; UiwML: list($ret, $err) = $uploadmgr->putFile($uploadtoken, $savemp3, $targetName); goto SJNxk; oq4RI: file_delete($randvoiceurl); goto Mc_T6; fG5Vy: $resarr['msg'] = "访问公众平台接口失败, 错误: {$response['message']}"; goto fX8fx; ZF33Z: $openid = $_SESSION['openid']; goto s7Z73; E4b7s: if (!($chatres['openid'] != $openid)) { goto aLlHd; } goto pVXP_; pu80c: if (file_exists($updir)) { goto hXlkW; } goto JBhi_; D4EXn: goto e16nH; goto y85V2; i3tLn: $config = new Qiniu\Config(); goto d9zAG; tkZGm: $uploadtoken = $auth->uploadToken($qiniubucket, null, 3600, $policy); goto UiwML; ovAVM: $qiniuaccesskey = $this->getmoduleconfig('qiniuaccesskey'); goto Ew46i; bjyD1: $chatres = $chatres2; goto ZBXlm; AfqY_: $fops = 'avthumb/mp3/ab/320k/ar/44100/acodec/libmp3lame'; goto nJ3lt; Wu2NC: $policy = array("persistentOps" => $fops, "persistentPipeline" => $liedui); goto GjBDi; ogMnr: $dataup['mp3du'] = 0; goto ADKen; Ypdsx: hXlkW: goto dAsGT; UWC02: } public function doWebQianru() { goto mPgnC; hb9v9: $cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND ctype = 1 ORDER BY displayorder ASC"); goto Ohv5o; eZtXn: $qrset = array("qrsetbgcolor" => trim($_GPC['qrsetbgcolor']), "qrsettextcolor" => trim($_GPC['qrsettextcolor']), "qrsettext" => trim($_GPC['qrsettext']), "qrsettextsize" => intval($_GPC['qrsettextsize']), "qrwidth" => intval($_GPC['qrwidth']), "qrbottom" => intval($_GPC['qrbottom']), "qrright" => intval($_GPC['qrright']), "qrsetlineheight" => intval($_GPC['qrsetlineheight']), "qrphonetu" => trim($_GPC['qrphonetu']), "qrbottom2" => intval($_GPC['qrbottom2']), "qrright2" => intval($_GPC['qrright2']), "qrwidth2" => intval($_GPC['qrwidth2'])); goto epvUY; mW4Yz: $id = intval($_GPC['id']); goto AGZes; dDzwe: if ($operation == 'display') { goto Ir7QM; } goto IV9xm; FBxyL: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; goto dDzwe; ATNH9: goto Pt0cT; goto ug6XN; ug6XN: iPVkQ: goto mW4Yz; IV9xm: if ($operation == 'qianru') { goto iPVkQ; } goto iJu5M; i_W4t: goto Pt0cT; goto BISOp; BISOp: Ir7QM: goto hb9v9; NoR0D: nWxNv: goto ATNH9; Y__iv: goto Pt0cT; goto mDGXS; mPgnC: global $_GPC, $_W; goto FBxyL; x2u0C: message('设置成功！', referer(), 'success'); goto fl_8q; AGZes: $row = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND ctype = 1 AND id = {$id}"); goto nepoa; mDGXS: pvZ58: goto xjjB1; fl_8q: Pt0cT: goto ZOIfG; ZOIfG: include $this->template('web/qianru'); goto ZhE1z; xjjB1: $id = intval($_GPC['id']); goto eZtXn; iJu5M: if ($operation == 'qrset') { goto pvZ58; } goto i_W4t; Ohv5o: foreach ($cservicelist as $k => $v) { goto mKiVR; SqFr_: goto ZQfks; goto KO71f; d_7c2: $scripturl = $_W['siteroot'] . 'app.php/index?i=' . $_W['uniacid'] . '&c=entry&do=index&m=elapp_customerservice_plugin_p&toopenid=' . $v['content']; goto HSe_W; e6L_o: rLnIj: goto hrm_1; KJ_TT: $cservicelist[$k]['scripthtml'] = ''; goto QLjh0; P4PPl: $cservicelist[$k]['scripthtml2'] = htmlentities('<a href="' . $scripturl2 . '" style="z-index:10001;position:absolute;right:' . $qrset['qrright2'] . 'px;bottom:' . $qrset['qrbottom2'] . 'px;"><img style="height:auto;width:' . $qrset['qrwidth2'] . 'px;" src="' . tomedia($qrset['qrphonetu']) . '" /></a>'); goto EsMWm; mTPEl: $scripturl2 = $_W['siteroot'] . 'app.php/index?i=' . $_W['uniacid'] . '&c=entry&do=chat&m=elapp_customerservice&toopenid=' . $v['content']; goto P4PPl; MVd5Y: $cservicelist[$k]['scripthtml'] = htmlspecialchars($scripthtml); goto mTPEl; KO71f: AhkJC: goto LSRfy; LSRfy: $scripthtml = '<script type="text/javascript" src="' . $_W['siteroot'] . 'addons/elapp_customerservice_plugin_p/static/qianru.js"></script>
									<script type="text/javascript">
										document.getElementById("kfiframe").src="' . $scripturl . '";
										var xfkefu = document.getElementById("xfkefu");
										xfkefu.style.background = "' . $qrset['qrsetbgcolor'] . '";
										xfkefu.style.color = "' . $qrset['qrsettextcolor'] . '";
										xfkefu.innerText = "' . $qrset['qrsettext'] . '";
										xfkefu.style.fontSize = "' . $qrset['qrsettextsize'] . 'px";
										xfkefu.style.lineHeight = xfkefu.style.paddingTop = xfkefu.style.paddingBottom = "' . $qrset['qrsetlineheight'] . 'px";
										xfkefu.style.width = "' . $qrset['qrwidth'] . 'px";
										xfkefu.style.bottom = "' . $qrset['qrbottom'] . 'px";
										xfkefu.style.right = "' . $qrset['qrright'] . 'px";
									</script>
									'; goto MVd5Y; mKiVR: $qrset = unserialize($v['qrmsg']); goto d_7c2; QLjh0: $cservicelist[$k]['scripthtml2'] = ''; goto SqFr_; EsMWm: ZQfks: goto e6L_o; HSe_W: if (!empty($qrset)) { goto AhkJC; } goto KJ_TT; hrm_1: } goto NoR0D; epvUY: $data['qrmsg'] = serialize($qrset); goto YAGpC; nepoa: $row['qrset'] = unserialize($row['qrmsg']); goto Y__iv; YAGpC: pdo_update(BEST_CSERVICE, $data, array("id" => $id)); goto x2u0C; ZhE1z: } public function doWebCservice() { goto UIvme; urxIK: O86zg: goto Waac5; slCx_: $resArr['msg'] = '不存在该客服！'; goto TN0CC; ncein: $resArr['msg'] = '该用户名已被占用！'; goto YFOjn; fOIRO: $id = intval($_GPC['id']); goto PNK61; H2pPV: $resArr['msg'] = '用户名不得为空！'; goto BpZ8L; dKxPe: pdo_update(BEST_CSERVICE, $data, array("id" => $id)); goto os2yg; qyOJw: exit; goto cGtYA; TNqzQ: if (!empty($cservice)) { goto lAL_e; } goto Y7eDY; Lp9SJ: if ($operation == 'changeuser') { goto OTmxK; } goto CaDt6; Ptk7Q: $data['username'] = $user; goto xCH9s; IReWA: $resArr['msg'] = '修改' . $cservice['name'] . '用户名成功！'; goto FOs_X; oUSsx: echo json_encode($resArr); goto cIhmE; w08T0: $cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND id= {$id} AND ctype = 1"); goto QdPDl; n6bDp: goto iPsUC; goto Xjf7W; lwHW3: include $this->template('web/cservice'); goto q4XMc; Ry6Du: $resArr['error'] = 1; goto YlxLt; kSGFo: exit; goto xEoe7; UIvme: global $_GPC, $_W; goto EPAFV; w1Eup: exit; goto W6UeJ; ZJAM0: iPsUC: goto cvQ7Q; Gj5C5: OTmxK: goto iQXMc; Xjf7W: uPgwb: goto ODbtu; os2yg: $resArr['error'] = 0; goto Yk2gp; q4XMc: goto iPsUC; goto Gj5C5; IrJDz: qG71O: goto Wafsi; EPAFV: $operation = !empty($_GPC['op']) ? $_GPC['op'] : 'display'; goto DXRJC; wbYTo: exit; goto ZJAM0; jj9Ko: exit; goto urxIK; YlxLt: $resArr['msg'] = '不存在该客服！'; goto DvCOS; cIhmE: exit; goto IrJDz; IT8Rp: if (!empty($user)) { goto O86zg; } goto UAITb; BpZ8L: echo json_encode($resArr); goto jj9Ko; r3vt9: lAL_e: goto z3ZFY; vVRYW: if (!empty($pwd)) { goto qG71O; } goto W3rCu; X3wxt: if (empty($hasuser)) { goto hwKIi; } goto xoBB6; YFOjn: echo json_encode($resArr); goto w1Eup; CaDt6: if ($operation == 'changepwd') { goto cB83U; } goto n6bDp; UczdI: $user = trim($_GPC['user']); goto IT8Rp; Waac5: $hasuser = pdo_fetch('SELECT id FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND username = '{$user}' AND ctype = 1"); goto X3wxt; Y7eDY: $resArr['error'] = 1; goto slCx_; DXRJC: if ($operation == 'display') { goto uPgwb; } goto Lp9SJ; QdPDl: if (!empty($cservice)) { goto GhBVS; } goto Ry6Du; wrEO2: echo json_encode($resArr); goto wbYTo; W3rCu: $resArr['error'] = 1; goto BcTUW; z3ZFY: $data['pwd'] = sha1($pwd); goto dKxPe; W6UeJ: hwKIi: goto w08T0; TN0CC: echo json_encode($resArr); goto amWI8; DvCOS: echo json_encode($resArr); goto kSGFo; xoBB6: $resArr['error'] = 1; goto ncein; xCH9s: pdo_update(BEST_CSERVICE, $data, array("id" => $id)); goto fP0t2; amWI8: exit; goto r3vt9; BcTUW: $resArr['msg'] = '密码不得为空！'; goto oUSsx; fP0t2: $resArr['error'] = 0; goto IReWA; Wafsi: $cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND id= {$id} AND ctype = 1"); goto TNqzQ; FOs_X: echo json_encode($resArr); goto qyOJw; cGtYA: goto iPsUC; goto zXeRn; UAITb: $resArr['error'] = 1; goto H2pPV; zXeRn: cB83U: goto fOIRO; PNK61: $pwd = trim($_GPC['pwd']); goto vVRYW; iQXMc: $id = intval($_GPC['id']); goto UczdI; ODbtu: $cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = '{$_W['uniacid']}' AND ctype = 1 ORDER BY displayorder ASC"); goto lwHW3; xEoe7: GhBVS: goto Ptk7Q; Yk2gp: $resArr['msg'] = '修改' . $cservice['name'] . '密码成功！'; goto wrEO2; cvQ7Q: } public function doWebCgroup() { goto TpKWa; n1xwI: include $this->template('web/cgroup'); goto vEXjZ; MzhxT: $cservicegrouplist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} ORDER BY displayorder ASC"); goto Ob3Ht; TpKWa: global $_W, $_GPC; goto MzhxT; Ob3Ht: foreach ($cservicegrouplist as $k => $v) { $cservicegrouplist[$k]['servicegroupurl'] = $_W['siteroot'] . 'app/' . str_replace('./', '', $this->createMobileUrl('index', array("groupid" => $v['id']))); PJozE: } goto WzrBT; WzrBT: y2XYS: goto n1xwI; vEXjZ: } public function doMobileIndex() { goto F2Hpx; nV1Ds: if (!($zhouji == '5')) { goto sxSkw; } goto JpmOb; nSh2T: $datafanskefu['kefuopenid'] = $cservice['content']; goto UyBw1; WwEWY: $suiji = $this->getmoduleconfig('suiji'); goto R8TS2; Jq1WF: $latitude = $_COOKIE['kflatitude']; goto B6Fto; u67GZ: $ipres = file_get_contents($ipurl); goto GUj8S; rPSAw: $kefuids = array(0); goto OlmZn; R8TS2: if ($suiji == 1) { goto XqG2V; } goto ZkQ5N; DQWjc: $jiamistr = $this->get_lang() . $this->browse_info() . $this->get_os() . $latitude . $longitude; goto jbA2N; LcFT9: $longitude = $ipres['result']['location']['lng'] . random(4, 1); goto FfXOq; yyhcQ: if (!($zhouji == '3')) { goto UDRFi; } goto EKnfl; EYW4a: if (!($zhouji == '0')) { goto NmoiX; } goto CErMd; keC9_: $fangkearr['gzhname'] = $_W['account']['name']; goto UF4dx; ggSZS: $fkid = $hasfanskefu['id']; goto jCCIY; Lqxrn: $nowhouradd = $nowhour + 1; goto OgItr; C0EeQ: $ipurl = 'https://apis.map.qq.com/ws/location/v1/ip?ip=' . $_W['clientip'] . '&key=' . $mapkey; goto u67GZ; r5nl0: if (!($zhouji == '2')) { goto e0TOm; } goto BiClk; OgItr: $condition = "weid = {$_W['uniacid']} AND ctype = 1 AND ((iszx = 0 AND (
					(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR 
					(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))
				)"; goto VxJvJ; m8v21: setcookie('kflongitude', $longitude, time() + 3600 * 24 * 7); goto M2Dtt; Sg3Pz: $fangkearr = array("lang" => $this->get_lang(), "browse" => $this->browse_info(), "os" => $this->get_os(), "ip" => $_W['clientip'], "laiyuan" => $_SERVER['HTTP_REFERER'], "latitude" => $ipres['result']['location']['lat'], "longitude" => $ipres['result']['location']['lng'], "nation" => $ipres['result']['ad_info']['nation'], "province" => $ipres['result']['ad_info']['province'], "city" => $ipres['result']['ad_info']['city'], "district" => $ipres['result']['ad_info']['district'], "gzhname" => $_W['account']['name']); goto Favky; jFbv2: cldQ4: goto r5nl0; OdcG0: It9lQ: goto ggSZS; K3knE: $cservice = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICE) . " WHERE weid = {$_W['uniacid']} AND ctype = 1 AND content = '{$toopenid}'"); goto pSQxN; HvHT8: $condition .= ' AND ((day6 = 1 AND isxingqi = 1) OR isxingqi = 0))'; goto soCIn; ZWxFw: $datafanskefuup['kefunotread'] = 0; goto gHqKu; QBZT8: $chatcontime = 0; goto bflKJ; h8l80: h10Pz: goto zo81l; zo81l: $ipurl = 'https://apis.map.qq.com/ws/location/v1/ip?ip=' . $_W['clientip'] . '&key=' . $mapkey; goto cCMPM; UyBw1: $datafanskefu['fansavatar'] = tomedia($defaultavatar); goto vH7EM; BiClk: $condition .= ' AND ((day2 = 1 AND isxingqi = 1) OR isxingqi = 0))'; goto FTyfF; h90JR: $datafanskefu['fansopenid'] = $openid; goto nSh2T; OlmZn: foreach ($kefuandgroup as $k => $v) { $kefuids[] = $v['kefuid']; UKYCY: } goto BEsHo; s63kk: $defaultavatar = $this->getmoduleconfig('defaultavatar'); goto RzD35; UOK68: $latitude = $ipres['result']['location']['lat'] . random(4, 1); goto LcFT9; soCIn: g3psO: goto EYW4a; kW_6p: NmoiX: goto oZuk4; wX3K4: $orderby = ' ORDER BY rand()'; goto w8i69; g2vqa: if (empty($toopenid)) { goto wW7zO; } goto K3knE; JHr1O: if (!empty($cservicegroup)) { goto qETcZ; } goto At9pJ; uKyj0: $cservicegroup = pdo_fetch('SELECT * FROM ' . tablename(BEST_CSERVICEGROUP) . " WHERE weid = {$_W['uniacid']} AND id = {$groupid}"); goto FcZF_; jwu1m: lK4HM: goto Hvx7q; bVnBe: $fangkearr['laiyuan'] = $_SERVER['HTTP_REFERER']; goto keC9_; F5VYp: if ($fangkearr['latitude'] == '' || $fangkearr['ip'] != $_W['clientip']) { goto h10Pz; } goto bVnBe; gZ2D_: goto It9lQ; goto suHpq; Lgt64: XnNKo: goto ZWxFw; hYnsO: preg_match_all($regex, $cservice['autoreply'], $array2); goto ZC_OE; tjmN3: if (!empty($toopenid)) { goto yi2Ah; } goto bfU8h; L51gx: qETcZ: goto AQg7K; ngJK8: $title = '和' . $cservice['name'] . '的对话'; goto YiL2C; jcBAR: include $this->template('pcchat'); goto f2pfe; EKnfl: $condition .= ' AND ((day3 = 1 AND isxingqi = 1) OR isxingqi = 0))'; goto rKDeg; VdJZ_: $cservicelist = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CSERVICE) . ' WHERE ' . $condition . $orderby); goto JHr1O; DIZ1o: if (empty($hasfanskefu)) { goto tHWKS; } goto TrQci; CErMd: $condition .= ' AND ((day7 = 1 AND isxingqi = 1) OR isxingqi = 0))'; goto kW_6p; At9pJ: foreach ($cservicelist as $k => $v) { goto sTxQ6; mkXbq: unset($cservicelist[$k]); goto MWv11; n5Ari: pdJDq: goto G1tgr; MWv11: NW2FG: goto n5Ari; CzAdz: if (empty($kefuandgroup)) { goto NW2FG; } goto mkXbq; sTxQ6: $kefuandgroup = pdo_fetch('SELECT id FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE kefuid = {$v['id']}"); goto CzAdz; G1tgr: } goto G8e0t; FTyfF: e0TOm: goto yyhcQ; cCMPM: $ipres = file_get_contents($ipurl); goto s8TQr; RzD35: if (empty($_COOKIE['kflatitude'])) { goto zC_YG; } goto Jq1WF; LCNhj: $kefuandgroup = pdo_fetchall('SELECT kefuid FROM ' . tablename(BEST_KEFUANDGROUP) . " WHERE weid = {$_W['uniacid']} AND groupid = {$cservicegroup['id']}"); goto rPSAw; sPtTK: L62MS: goto Mlcce; oZuk4: $condition .= ' OR (iszx = 1 AND isrealzx = 1))'; goto WwEWY; suHpq: tHWKS: goto C0EeQ; XqUbQ: XqG2V: goto wX3K4; BEsHo: bHh4k: goto T0YDe; ZZ2Gb: goto jc2BW; goto onU3M; FcZF_: if (!empty($cservicegroup)) { goto JcLkK; } goto B12VC; AQg7K: $title = '请选择客服'; goto t6OWn; bfU8h: $groupid = intval($_GPC['groupid']); goto uKyj0; kyDBz: jDf2D: goto nV1Ds; DHrk0: $condition = "weid = {$_W['uniacid']} AND id in (" . implode(',', $kefuids) . ") AND ((iszx = 0 AND (
					(lingjie = 0 AND endhour >= {$nowhouradd} AND starthour <= {$nowhour}) OR 
					(lingjie = 1 AND (starthour < {$nowhouradd} OR endhour > {$nowhour}))
				)"; goto sPtTK; Ku3j4: if (!($zhouji == '4')) { goto jDf2D; } goto oVZcW; T0YDe: $nowhour = intval(date('H', TIMESTAMP)); goto IfezH; bQ5Dq: $datafanskefu['kefunickname'] = $cservice['name']; goto wfnP9; t8Qmp: goto m3Z3O; goto XqUbQ; ro8bP: $ipurl = 'https://apis.map.qq.com/ws/location/v1/ip?ip=' . $_W['clientip'] . '&key=' . $mapkey; goto dTCYS; ZkQ5N: $orderby = ' ORDER BY displayorder ASC'; goto t8Qmp; F2Hpx: global $_W, $_GPC; goto s6xES; S9ROH: $hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$openid}' AND kefuopenid = '{$toopenid}'"); goto OdcG0; UF4dx: goto BUryL; goto h8l80; pSQxN: $auto = explode('|', $cservice['fansauto']); goto D9__G; oXicK: $toopenid = trim($_GPC['toopenid']); goto tjmN3; Gvq24: $ipres = json_decode($ipres, true); goto UOK68; M2Dtt: jc2BW: goto DQWjc; ZC_OE: if (empty($array2[0])) { goto lK4HM; } goto acNnU; afKU3: $chatcon = pdo_fetchall('SELECT * FROM ' . tablename(BEST_CHAT) . " WHERE fkid = {$hasfanskefu['id']} AND weid = {$_W['uniacid']} AND type != 5 AND type != 6 ORDER BY time ASC"); goto QBZT8; TrQci: $fangkearr = unserialize($hasfanskefu['fangke']); goto F5VYp; oVZcW: $condition .= ' AND ((day4 = 1 AND isxingqi = 1) OR isxingqi = 0))'; goto kyDBz; D9__G: $hasfanskefu = pdo_fetch('SELECT * FROM ' . tablename(BEST_FANSKEFU) . " WHERE weid = {$_W['uniacid']} AND fansopenid = '{$openid}' AND kefuopenid = '{$toopenid}'"); goto DIZ1o; MRhOj: $fangkearr = array("lang" => $this->get_lang(), "browse" => $this->browse_info(), "os" => $this->get_os(), "ip" => $_W['clientip'], "laiyuan" => $_SERVER['HTTP_REFERER'], "latitude" => $ipres['result']['location']['lat'], "longitude" => $ipres['result']['location']['lng'], "nation" => $ipres['result']['ad_info']['nation'], "province" => $ipres['result']['ad_info']['province'], "city" => $ipres['result']['ad_info']['city'], "district" => $ipres['result']['ad_info']['district'], "gzhname" => $_W['account']['name']); goto B_WHq; QMeMA: $datafanskefu['kefuavatar'] = tomedia($cservice['thumb']); goto bQ5Dq; YiL2C: wW7zO: goto jcBAR; Hvx7q: E8MEU: goto afKU3; Gj8ql: gXxd2: goto jwu1m; s8TQr: $ipres = json_decode($ipres, true); goto MRhOj; Favky: $datafanskefu['weid'] = $_W['uniacid']; goto h90JR; bflKJ: foreach ($chatcon as $k => $v) { goto kq3GE; aE9bl: $chatcon[$k]['time'] = ''; goto RPv2s; HrwIY: foreach ($array2[0] as $kk => $vv) { goto gPjVP; gPjVP: if (empty($vv)) { goto MNkUs; } goto SQ6KW; SQ6KW: $chatcon[$k]['content'] = str_replace($vv, '<a href=\'' . $vv . '\'>' . $vv . '</a>', $chatcon[$k]['content']); goto LlExL; LlExL: MNkUs: goto uD_Te; uD_Te: FQc_3: goto naSjE; naSjE: } goto wZWcj; Amr0P: pw43Z: goto tKH0D; Y7uOg: so9F2: goto dQMCn; z2E9e: SX2st: goto Zphyf; YsEnC: $chatcon[$k]['content'] = $this->guolv($chatcon[$k]['content']); goto SkARe; wZWcj: z4Vrh: goto z2E9e; OW2RI: preg_match_all($regex, $chatcon[$k]['content'], $array2); goto yKUKl; SkARe: $regex = '@(?i)\\b((?:[a-z][\\w-]+:(?:/{1,3}|[a-z0-9%])|www\\d{0,3}[.]|[a-z0-9.\\-]+[.][a-z]{2,4}/)(?:[^\\s()<>]+|\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\))+(?:\\(([^\\s()<>]+|(\\([^\\s()<>]+\\)))*\\)|[^\\s`!()\\[\\]{};:\'".,<>?