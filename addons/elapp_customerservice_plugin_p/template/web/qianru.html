{template 'common/header'}

<ul class="nav nav-tabs">
	<li {if $operation == 'display'}class="active"{/if}><a href="{php echo $this->createWebUrl('qianru')}">嵌入代码</a></li>
	{if $operation == 'qianru'}
		<li class="active"><a href="###">设置嵌入代码</a></li>
	{/if}
</ul>

{if $operation == 'display'}
<div class="main">
	<div class="panel panel-default">
		<div class="panel-body table-responsive">
			<div class="alert alert-danger" role="alert">
				请将嵌入代码复制到html文件里的body结束标签之前！
			</div>
			<table class="table table-hover">
				<thead>
					<tr>
						<th style="width:10%;">客服名称</th>
						<th style="width:10%;">客服头像</th>
						<th>嵌入代码</th>
						<th style="width:10%;text-align:right;">操作</th>
					</tr>
				</thead>
				<tbody>
					{loop $cservicelist $row}
					<tr>
						<td>{$row['name']}</td>
						<td><img src="{php echo tomedia($row['thumb']);}" width="50" height="50" /></td>
						<td>
							{if !empty($row['scripthtml'])}
								<span style="color:red;">PC端嵌入代码：</span>{php echo nl2br($row['scripthtml'])}
								<br />
								<span style="color:red;">手机端嵌入代码：</span>{php echo nl2br($row['scripthtml2'])}
							{else}
								未操作嵌入设置
							{/if}
						</td>
						<td style="text-align:right;">
							<a href="{php echo $this->createWebUrl('qianru',array('op'=>'qianru','id'=>$row['id']))}" class="btn btn-info btn-sm">嵌入设置</a>
						</td>
					</tr>
					{/loop}
				</tbody>
			</table>
		</div>
	</div>
</div>
{elseif $operation == 'qianru'}
<div class="main">
	<form action="" method="post" class="form-horizontal form">
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">背景色</label>
			<div class="col-sm-8 col-xs-12">
				{php echo tpl_form_field_color('qrsetbgcolor',$row['qrset']['qrsetbgcolor']);}
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">文字色</label>
			<div class="col-sm-8 col-xs-12">
				{php echo tpl_form_field_color('qrsettextcolor',$row['qrset']['qrsettextcolor']);}
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">文字</label>
			<div class="col-sm-4 col-xs-12">
				<input name="qrsettext" class="form-control" value="{$row['qrset']['qrsettext']}" type="text">
			</div>
			
			<div class="col-sm-6 col-xs-12">
				<div class="input-group">
					<span class="input-group-addon">文字大小</span>
					<input class="form-control" name="qrsettextsize" value="{$row['qrset']['qrsettextsize']}" type="text">
					<span class="input-group-addon">px,行高</span>
					<input class="form-control" name="qrsetlineheight" value="{$row['qrset']['qrsetlineheight']}" type="text">
					<span class="input-group-addon">px</span>
				</div>
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">单位设置</label>
			<div class="col-sm-10 col-xs-12">
				<div class="input-group">
					<span class="input-group-addon">宽度</span>
					<input class="form-control" name="qrwidth" value="{$row['qrset']['qrwidth']}" type="text">
					<span class="input-group-addon">px,距离页面底部</span>
					<input class="form-control" name="qrbottom" value="{$row['qrset']['qrbottom']}" type="text">
					<span class="input-group-addon">px,距离页面右侧</span>
					<input class="form-control" name="qrright" value="{$row['qrset']['qrright']}" type="text">
					<span class="input-group-addon">px</span>
				</div>
			</div>
		</div>
		
		
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">嵌入手机端</label>
			<div class="col-sm-5 col-xs-12">
				{php echo tpl_form_field_image('qrphonetu', $row['qrset']['qrphonetu'], '', array('extras' => array('text' => 'readonly')))}
			</div>
			<div class="col-sm-5 col-xs-12">
				<div class="input-group">
					<span class="input-group-addon">图片宽度</span>
					<input class="form-control" name="qrwidth2" value="{$row['qrset']['qrwidth2']}" type="text">
					<span class="input-group-addon">px</span>
				</div>
			</div>
		</div>
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label">手机端距离</label>
			<div class="col-sm-10 col-xs-12">
				<div class="input-group">
					<span class="input-group-addon">px,距离页面底部</span>
					<input class="form-control" name="qrbottom2" value="{$row['qrset']['qrbottom2']}" type="text">
					<span class="input-group-addon">px,距离页面右侧</span>
					<input class="form-control" name="qrright2" value="{$row['qrset']['qrright2']}" type="text">
					<span class="input-group-addon">px</span>
				</div>
			</div>
		</div>
		
		<div class="form-group">
			<label class="col-xs-12 col-sm-2 col-md-2 control-label"></label>
			<div class="col-sm-10 col-xs-12">
				<input name="op" value="qrset" type="hidden">
				<input name="id" value="{$row['id']}" type="hidden">
				<input name="submit" class="btn btn-primary" value="提交" type="submit">
				<input name="token" value="{$_W['token']}" type="hidden">
			</div>
		</div>
	</form>
</div>
{/if}
{template 'common/footer'}