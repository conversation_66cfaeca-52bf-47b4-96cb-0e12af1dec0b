{template 'common/header'}
<ul class="nav nav-tabs">
	<li {if $operation == 'display'}class="active"{/if}><a href="{php echo $this->createWebUrl('cservice', array('op' => 'display'))}">客服中心</a></li>
</ul>

{if $operation == 'display'}
<div class="main">
	<div class="panel panel-default">
		<div class="panel-body table-responsive">
			<table class="table table-hover">
				<thead>
					<tr>
						<th style="width:10%;">客服名称</th>
						<th style="width:10%;">客服头像</th>
						<th style="width:10%;">登录账号</th>
						<th style="width:10%;">登录密码</th>
					</tr>
				</thead>
				<tbody>
					{loop $cservicelist $row}
					<tr>
						<td>{$row['name']}</td>
						<td><img src="{php echo tomedia($row['thumb']);}" width="50" height="50" /></td>
						<td>
							<input type="text" data-id="{$row['id']}" name="changeuser" class="form-control changeuser" value="{$row['username']}" />
						</td>
						<td>
							<input type="password" data-id="{$row['id']}" name="changepwd" class="form-control changepwd" value="" placeholder="修改密码请直接操作" />
						</td>
					</tr>
					{/loop}
				</tbody>
			</table>
		</div>
	</div>
</div>
<script type="text/javascript">
$('.changeuser').change(function(){
	if(confirm("确定要修改用户名吗？")){
		var id = $(this).attr('data-id');
		$.ajax({
			url:"{php echo $this->createWebUrl('cservice')}",
			data:{
				id:id,
				op:'changeuser',
				user:$(this).val(),
			},
			dataType:'json',
			type:'post',        
			success:function(data){
				alert(data.msg);
			},
		});
	}
});
$('.changepwd').change(function(){
	if(confirm("确定要修改密码吗？")){
		var id = $(this).attr('data-id');
		$.ajax({
			url:"{php echo $this->createWebUrl('cservice')}",
			data:{
				id:id,
				op:'changepwd',
				pwd:$(this).val(),
			},
			dataType:'json',
			type:'post',        
			success:function(data){
				alert(data.msg);
			},
		});
	}
});
</script>
{/if}
{template 'common/footer'}