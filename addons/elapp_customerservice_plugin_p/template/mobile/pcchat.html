<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>{$title}</title>
<link rel="shortcut icon" href="{MD_ROOT}/favicon.ico">
<style>
*, *:before, *:after {
	box-sizing: border-box;
}
body, html {
	height: 100%;
	overflow: hidden;
}
body, ul {
	margin: 0;
	padding: 0;
}
body {
	color: #4d4d4d;
	font: 14px/1.4em 'Helvetica Neue', Helvetica, 'Microsoft Yahei', Arial, sans-serif;
	background: #f5f5f5;
}
audio{max-width:100%;}
.flex{
	display: box;              /* OLD - Android 4.4- */
	display: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;      /* TWEENER - IE 10 */
	display: -webkit-flex;     /* NEW - Chrome */
	display: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */
	
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	-o-flex-direction: row;
	flex-direction: row;
}

.flex1{-webkit-box-flex:1; -moz-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}
.textellipsis2{
	word-break:break-all;
	display:-webkit-box;
	-webkit-line-clamp:2;
	-webkit-box-orient:vertical;
	overflow:hidden;
}
ul {
	list-style: none;
}
.hide{display:none;}
.blackbg {position: fixed;top: 0;left: 0;right: 0;bottom: 0;background: #000;opacity: 0.3;z-index: 555;}



.m-list li{padding:10px;border-bottom:1px solid #f7f7f7;cursor:pointer;}
.m-list li:hover{background-color:#f1f1f1;}
.m-list li.active{background-color:#f1f1f1;}
.m-list .avatar{border-radius:2px;width:30px;height:30px;}
.m-list .name{display:inline-block;float:right;font-size:12px;width:120px;height:30px;line-height:30px;overflow:hidden;margin:0;}
.notread{
	position:absolute;
	z-index:9999;
	display:block;
	width:16px;
	height:16px;
	line-height:16px;
	text-align:center;
	background:red;color:#fff;
	font-size:10px;border-radius:100%;
	margin-left: 9%;
}


.m-text{height:10pc;border-top:1px solid #ddd}
.m-text textarea{padding:10px;height:100%;width:100%;border:none;outline:0;font-family:Micrsofot Yahei;resize:none}
.m-message{padding:10px 15px;overflow-y:scroll}
.m-message li{margin-bottom:15px}
.m-message .time{margin:7px 0;text-align:center}
.m-message .time>span{display:inline-block;padding:0 18px;font-size:9pt;color:#9FA8B1;border-radius:2px;background-color:#dcdcdc}
.m-message .avatar{float:left;margin:0 10px 0 0;border-radius:3px}
.m-message .text{display:inline-block;position:relative;padding:0 10px;max-width:calc(100% - 40px);min-height:30px;line-height:2.5;font-size:9pt;text-align:left;word-break:break-all;background-color:#fff;border-radius:4px}
.m-message .text:before{content:" ";position:absolute;top:9px;right:100%;border:6px solid transparent;border-right-color:#fff}
.m-message .self{text-align:right}
.m-message .self .avatar{float:right;margin:0 0 0 10px}
.m-message .self .text{background-color:#b2e281}
.m-message .self .text:before{right:inherit;left:100%;border-right-color:transparent;border-left-color:#b2e281}


.m-message .avatar{width:40px;height:40px;}
.m-message .text{max-width: calc(100% - 50px);font-size:14px;}
.m-message .text img{margin:10px auto;max-width:14pc;}
.m-message .text .voice2{
	width:20px;height:20px;margin-right:5px;
}
.m-message .text .weidu{
	height:20px;line-height:20px;color:red;
}

.sendtype{z-index:999;position:absolute;margin-top:7.5pc;right:0.5pc;width:100%;height:2pc;}
.sendtype .item{
	padding-right:1pc;height:2pc;line-height:2pc;
	font-size:13px;cursor:pointer;color:#8a8a8a;
}
.sendtype .item img{width:1.2pc;height:1.2pc;margin-top:0.4pc;margin-right:0.4pc;}
.sendtype .now{color:#000;}
.sendtype .m-button,.sendtype .m-button-hui{background:#2e3238;color:#f4f4f4;height:2pc;line-height:2pc;padding:0 1pc;display:inline-block;float:left;border:none;border-radius:5px;font-size:14px;cursor:pointer;}
.sendtype .m-button-hui{background:#ccc;}

.menuitem{
	position:absolute;height:2pc;
}
.menuitem .item{
	width:2.5pc;text-align:center;height:2pc;
	cursor:pointer;float:left;color:#666;
}
.menuitem .item img{
	width:1.5pc;height:1.5pc;margin-top:0.25pc;
}


.quickwrapper{position:absolute;z-index:999;background:#fff;width:19pc;height:14pc;overflow-y:auto;border:solid 1px #ccc;margin-top:-14pc;display:none;}
.quickwrapper div{height:auto;font-size:0.9pc;cursor:pointer;border-bottom:dashed 1px #ccc;padding:0.5pc;}

.facewrapper{position:absolute;z-index:999;background:#fff;width:19pc;height:14pc;overflow-y:auto;border:solid 1px #ccc;margin-top:-14pc;display:none;}
.facewrapper .faceitem{display:block;width:2.5pc;height:2.5pc;float:left;padding:0.45pc;cursor:pointer;}

.qrcodewrapper{position:absolute;z-index:999;background:#fff;width:15pc;height:14pc;border:solid 1px #ccc;margin-top:-14pc;display:none;}
.qrcodewrapper img{width:15pc;height:14pc;}

.m-list li.new{background:#FD7B23;}
.m-text textarea{padding-top:2pc;}

#chat {
	width: auto;
	height:100%;
	overflow:hidden;
}
#chat .main,#chat .sidebar{height:100%;}
#chat .sidebar{float:left;width:200px;color:#9FA8B1;background-color:#fff;border-right:solid 1px #ddd;}
#chat .main{position:relative;overflow:hidden;background-color:#f8f8f8;}
#chat .m-text{position:absolute;width:100%;bottom:0;left:0}
#chat .m-message{height:calc(100% - 10pc)}
.m-list li.active{background-color:#2F2F2F;color:#fff;}
.chosekefu{font-size: 24px;height: 2pc;line-height: 2pc;text-align: center;}
</style>
</head>
<body>
<div id="yuyindiv" style="display:none;"></div>

<div id="chat">
{if empty($toopenid)}
	<div class="sidebar" style="overflow-y: scroll;">
		<div class="m-list">
			<ul>
				{loop $cservicelist $row}
					{if $row['content'] == $toopenid}
						<li class="active">
							<img class="avatar" src="{php echo tomedia($row['thumb'])}">
							<p class="name">{$row['name']}</p>
						</li>
					{else}
						<a href="{php echo $this->createMobileUrl('index',array('toopenid'=>$row['content']))}" style="color:#9FA8B1;">
						<li>
							<img class="avatar" src="{php echo tomedia($row['thumb'])}">
							<p class="name">{$row['name']}</p>
						</li>
						</a>
					{/if}
				{/loop}
			</ul>
		</div>
	</div>
{/if}


<div class="main">
	
	<div class="m-message">
		{if !empty($toopenid)}
		{if $this->module['config']['adv']}
			<div style="padding:5px;">{php echo htmlspecialchars_decode($this->module['config']['adv']);}</div>
		{/if}
		<ul>
			<li><p class="time"><span>客服在线时间：{$cservice['starthour']}点至{$cservice['endhour']}点</span></p></li>
			{if $cservice['autoreply']}	
			<li>
				<div class="main">
					<img class="avatar" src="{php echo tomedia($cservice['thumb']);}" width="30" height="30">
					<div class="text">{php echo str_replace("\n","<br/>",$cservice['autoreply']);}</div>
				</div>
			</li>
			{/if}
					
			{if $chatcon}
				{loop $chatcon $row}
					<li>
						<p class="time"><span>{php echo date('Y-m-d H:i:s',$row['time'])}</span></p>
						<div {if $row['openid'] == $openid}class="main self"{else}class="main"{/if}>
							{if $row['openid'] != $openid}
							<img src="{$hasfanskefu['kefuavatar']}" class="avatar" alt=""/>
							{else}
							<img src="{$hasfanskefu['fansavatar']}" class="avatar" alt=""/>
							{/if}
							<div class="text">
								{if $row['type'] == 3 || $row['type'] == 4}
								<img src="{$row['content']}" class="sssbbb" />
								{else}
									{$row['content']}
								{/if}
							</div>
						</div>
					</li>
				{/loop}
			{/if}
		</ul>
		{else}
		<div class="chosekefu">请选择一个客服咨询。</div>
		{/if}
	</div>

	<div class="m-text">
		<textarea id="textarea" onkeydown="KeyDown(event)" placeholder="说点什么吧..."></textarea>
	</div>
	<div class="quickwrapper">
		{if !empty($auto)}
			{loop $auto $row}
				<div class="cando">{$row}</div>
			{/loop}
		{else}
			<div>暂无快捷消息</div>
		{/if}
	</div>
	<div class="facewrapper">
		<?php 
			for($i=1;$i<=32;$i++){
				echo '<img class="faceitem" src="../addons/elapp_customerservice/static/arclist/'.$i.'.png" data-emstr="[em_'.$i.']" />';
			}
		?>
	</div>
	<div class="qrcodewrapper">
		<img src="{php echo tomedia($cservice['kefuqrcode'])}" />
	</div>
	<div class="menuitem">
		<div class="item qqface">
			<img src="{NEWSTATIC_ROOT}/icon/face.png" />
		</div>
		
		<div class="item quick">
			<img src="{NEWSTATIC_ROOT}/icon/kuaijie.png" />
		</div>
		
		{if $cservice['iskefuqrcode'] == 1}
		<div class="item qrcode">
			<img src="{NEWSTATIC_ROOT}/icon/wechat.png" />
		</div>
		{/if}
	
		<div class="item upimg" id="upimg">
			<img src="{NEWSTATIC_ROOT}/icon/upimg.png" />
		</div>
	</div>
	
	<div class="sendtype flex">
		<div class="flex1"></div>
		<div class="item flex now" data-id="1">
			<img src="{NEWSTATIC_ROOT}/icon/radio1.png" />
			<div class="item-text flex1">按Enter键发送</div>
		</div>
		<div class="item flex" data-id="2">
			<img src="{NEWSTATIC_ROOT}/icon/radio2.png" />
			<div class="item-text flex1">按Ctrl+Enter键发送</div>
		</div>
		<input type="hidden" id="sendtype" value="1" />
		{if empty($toopenid)}
		<button type="button" class="m-button-hui">发送</button>
		{else}
		<button type="button" class="m-button">发送</button>
		{/if}
	</div>
</div>
</div>
<script src="{MD_ROOT_Z}static/newui/js/jquery-3.1.1.min.js"></script>
<script src="{MD_ROOT}/jUploader.js"></script>
<script src="{MD_ROOT_Z}static/newui/js/socket.io.js"></script>
<script>
var newMessageRemind = {
	_step: 0,
	_title: document.title,
	_timer: null,
	//显示新消息提示
	show:function(){
		var temps = newMessageRemind._title.replace("【　　　】", "").replace("【新消息】", "");
		newMessageRemind._timer = setTimeout(function() {
			newMessageRemind.show();
			//这里写Cookie操作
			newMessageRemind._step++;
			if (newMessageRemind._step == 3) { newMessageRemind._step = 1 };
			if (newMessageRemind._step == 1) { document.title = "【　　　】" + temps };
			if (newMessageRemind._step == 2) { document.title = "【新消息】" + temps };
		}, 800);
		return [newMessageRemind._timer, newMessageRemind._title];
	},
	//取消新消息提示
	clear: function(){
		clearTimeout(newMessageRemind._timer );
		document.title = newMessageRemind._title;
		//这里写Cookie操作
	}
};
var cansend = 1;
var uid = "{$openid}";
var touid = "{$toopenid}";
var fkid = "{$fkid}";
var sendurl = "https://api.qiumipai.com:2121/?type=newpublish&to="+touid;
$(function(){
	$('.text').each(function(){
		$(this).html(replace_em($(this).html()));
	});
	
	$(".m-message").animate({scrollTop:10000000},300);
	
	/*$.ajax({
		url:"{php echo $this->createMobileUrl('shangxian')}",
		data:{
			fkid:{$hasfanskefu['id']},
			type:'fans',
		},
		dataType:'json',
		type:'post',        
		success:function(data){
		},
	});*/
	
	// 连接服务端
	var socket = io('https://api.qiumipai.com:2120');
	// 连接后登录
	socket.on('connect', function(){
		socket.emit('login',{'uid':uid,'fkid':fkid});
	});
	/*socket.on('reconnect', function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('shangxian')}",
			data:{
				fkid:{$hasfanskefu['id']},
				type:'fans',
			},
			dataType:'json',
			type:'post',        
			success:function(data){
			},
		});
	});*/
	// 后端推送来消息时
	socket.on('new_msg', function(msg){
		if(msg.toopenid == touid){
			var content = replace_em(msg.content);
			var returnmsg = '<li>'
								+'<p class="time"><span>'+msg.datetime+'</span></p>'
								+'<div class="main">'
										+'<img src="{$hasfanskefu['kefuavatar']}" class="avatar" width="30" height="30" />'
									+'<div class="text">'+content+'</div>'
								+'</div>'
							+'</li>';
			$('.m-message ul').append(returnmsg);
			$(".m-message").animate({scrollTop:10000000},300);
			$.ajax({
				url:"{php echo $this->createMobileUrl('donotread')}",
				data:{
					fkid:fkid,
					type:'fans',
				},
				dataType:'json',
				type:'post',        
				success:function(data){
				},
			});
			playtixiang();
		}
	});
	
	$(".qqface").click(function(){
		$(".facewrapper").toggle();
		$(".quickwrapper").hide();
		$(".qrcodewrapper").hide();
	});
	
	$(".qrcode").click(function(){
		$(".qrcodewrapper").toggle();
		$(".facewrapper").hide();
		$(".quickwrapper").hide();
	});

	$(".facewrapper img").click(function(){
		$("#textarea").val($("#textarea").val()+$(this).attr("data-emstr"));
		$(".facewrapper").hide();
	});
	
	$(".quick").click(function(){
		$(".quickwrapper").toggle();
		$(".facewrapper").hide();
		$(".qrcodewrapper").hide();
	});
	
	
	$(".quickwrapper").on('click','.cando',function(){
		addchat($(this).text(),1);
		$(".quickwrapper").hide();
	});
	
	$(".sendtype .item").click(function(){
		$(".sendtype .item").removeClass('now');
		$('.radioimg').attr('src','{NEWSTATIC_ROOT}/icon/radio2.png');
		$(this).find('img').attr('src','{NEWSTATIC_ROOT}/icon/radio1.png');
		$(this).addClass('now');
		$("#sendtype").val($(this).attr('data-id'));
	});	
	
	$(document).on('click','.m-button',function(){　　　　//动态事件绑定  为页面所有的dd添加一个事件 包括新增的节点
        addchat($("#textarea").val(),1);
    });
	
	
	$.jUploader.setDefaults({
		cancelable: false, // 可取消上传
		allowedExtensions: ['jpg', 'png', 'gif','jpeg'], // 只允许上传图片
		messages: {
			upload: '上传',
			cancel: '取消',
			emptyFile: "{file} 为空，请选择一个文件.",
			invalidExtension: "{file} 后缀名不合法. 只有 {extensions} 是允许的.",
			onLeave: "文件正在上传，如果你现在离开，上传将会被取消。"
		}
	});
	
	$.jUploader({
		button: 'upimg', // 这里设置按钮id
		action: "{php echo $this->createMobileUrl('pcupload')}", // 这里设置上传处理接口
		onUpload: function (fileName) {
		},
		// 上传完成事件
		onComplete: function (fileName, response) {
			if (response.error == 0) {
				addchat(response.imgurl,4);
			} else {
				alert(response.message);
			}
		},
	});
	
	$(document).click(function(){
		newMessageRemind.clear();
	});
	
})

//查看QQ表情结果
function replace_em(str){
	str = str.replace(/\[em_([0-9]*)\]/g,'<img src="{MD_ROOT_Z}/static/arclist/$1.png" style="width:1.5pc;" border="0" />');
	return str;
}

//回车键
function KeyDown(event){
	if (event.ctrlKey && event.keyCode == 13 && $("#sendtype").val() == 2){
		event.returnValue=false;
		event.cancel = true;
		addchat($("#textarea").val(),1);
	}
	
	if (event.keyCode == 13 && $("#sendtype").val() == 1){
		event.returnValue=false;
		event.cancel = true;
		addchat($("#textarea").val(),1);
	}
}

function playtixiang(){
	newMessageRemind.show();
	$("#yuyindiv").append('<audio src="{MD_ROOT}/tixing.mp3" autoplay="autoplay" controls="controls">亲 您的浏览器不支持html5的audio标签</audio>');
}

//发送消息到数据库
function addchat(content,type){
	if(cansend == 1){
		cansend = 0;
		$.ajax({   
			 url:"{php echo $this->createMobileUrl('addchat')}",   
			 type:'post', 
			 data:{
				toopenid:touid,
				content:content,
				fkid:{$fkid},
				qudao:'',
				goodsid:0,
				type:type,
			 },
			 dataType:'json',
			 success:function(data){   
				if(data.error == 0){	
					var chatcon = replace_em(data.content);
					var returnmsg = '<li>'
										+'<p class="time"><span>'+data.datetime+'</span></p>'
										+'<div class="main self">'
												+'<img src="{$hasfanskefu['fansavatar']}" class="avatar" width="30" height="30" />'
											+'<div class="text">'+chatcon+'</div>'
										+'</div>'
									+'</li>';
					$('.m-message ul').append(returnmsg);
					$(".m-message").animate({scrollTop:10000000},300);
					$("#textarea").val("");
					
					if(data.jqr == 1){
						if(data.hftype == 0 || data.hftype == 3){
							data.jqrcontent = data.jqrcontent;
						}else{
							data.jqrcontent = '<img src="'+data.jqrcontent+'" class="sssbbb" />';
						}		
						var jrqmsg = '<li>'
										+'<p class="time"><span>'+data.jqrtime+'</span></p>'
										+'<div class="main">'
												+'<img src="'+data.jqravatar+'" class="avatar" width="30" height="30" />'
											+'<div class="text">'+data.jqrcontent+'</div>'
										+'</div>'
									+'</li>';
						$('.m-message ul').append(jrqmsg);
						$(".m-message").animate({scrollTop:10000000},300);
					}
					
					$.ajax({   
						url:sendurl,  
						type:'get', 
						data:{
							content:content,
							msgtype:type,
							toopenid:uid,
							newavatar:'{$hasfanskefu['fansavatar']}',
							newnickname:'{$hasfanskefu['fansnickname']}',
							datetime:data.datetime
						},
						dataType:'jsonp',
						success:function(data){ 
						}
					});
				}else{
					alert(data.msg);
				}
				cansend = 1;
			 }
		});
	}
}
</script>
</body>
</html>