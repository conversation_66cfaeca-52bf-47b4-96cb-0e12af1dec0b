<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml"><head>
<meta http-equiv="Content-Type" content="text/html; charset=UTF-8">
<title>{if $toopenid}和{$fanskefu['fansnickname']}的对话{else}客服工作平台{/if}</title>
<link rel="shortcut icon" href="{MD_ROOT}/favicon.ico">
<style>
*, *:before, *:after {
	box-sizing: border-box;
}
body, html {
	height: 100%;
	overflow: hidden;
}
body, ul {
	margin: 0;
	padding: 0;
}
body {
	color: #4d4d4d;
	font: 14px/1.4em 'Helvetica Neue', Helvetica, 'Microsoft Yahei', Aria<PERSON>, sans-serif;
	background: #f5f5f5;
}
audio{max-width:100%;}
.flex{
	display: box;              /* OLD - Android 4.4- */
	display: -webkit-box;      /* OLD - iOS 6-, Safari 3.1-6 */
	display: -moz-box;         /* OLD - Firefox 19- (buggy but mostly works) */
	display: -ms-flexbox;      /* TWEENER - IE 10 */
	display: -webkit-flex;     /* NEW - Chrome */
	display: flex;             /* NEW, Spec - Opera 12.1, Firefox 20+ */
	
	-webkit-box-orient: horizontal;
	-webkit-flex-direction: row;
	-moz-flex-direction: row;
	-ms-flex-direction: row;
	-o-flex-direction: row;
	flex-direction: row;
}

.flex1{-webkit-box-flex:1; -moz-box-flex:1;-webkit-flex:1;-ms-flex:1;flex:1;}
.textellipsis2{
	word-break:break-all;
	display:-webkit-box;
	-webkit-line-clamp:2;
	-webkit-box-orient:vertical;
	overflow:hidden;
}
ul {
	list-style: none;
}
.hide{display:none;}
.blackbg {position: fixed;top: 0;left: 0;right: 0;bottom: 0;background: #000;opacity: 0.3;z-index: 555;}
.sharediv{position:absolute;z-index:666;width:50%;left:25%;height:70%;top:15%;overflow-y:scroll;background:#f1f1f1;}

.zhuanjiediv{
	position:absolute;z-index:666;width:30%;left:35%;top:15%;background:#fff;
}
.zhuanjiediv .title{
	background:#fff;height:40px;line-height:40px;text-align:center;font-size:16px;margin-bottom:10px;
}
.zhuanjiediv .othercserviceitem{
	height:40px;line-height:40px;font-size:14px;overflow:hidden;text-align:center;
	background:#f1f1f1;margin:0 20px 2px 20px;
}
.zhuanjiediv .nowother{
	background:#1989fa;color:#fff;
}
.zhuanjiediv #zhuanjiebtn{
	background:#07c160;color:#fff;height:40px;line-height:40px;text-align:center;font-size:14px;
	margin:15px 20px;
}


#chat {
	width: auto;
	height:100%;
	overflow:hidden;
}
#chat .main,#chat .sidebar{height:100%;}
#chat .sidebar{float:left;width:200px;color:#9FA8B1;background-color:#fff;border-right:solid 1px #ddd;}
#chat .main{position:relative;overflow:hidden;background-color:#f8f8f8;}
#chat .m-text{position:absolute;width:100%;bottom:0;left:0}
#chat .m-message{height:calc(100% - 10pc)}

#chat .zhumsg{float:left;width:50px;color:#f4f4f4;background-color:#2F2F2F;height:100%;text-align:center;padding:20px 0 1% 0;position:relative;}
#chat .zhumsg .zhumsg-avatar{width:30px;height:30px;border-radius:100%;margin-bottom:20px;}
#chat .zhumsg .tuichu{position:absolute;bottom:0;width:30px;height:30px;left:10px;bottom:10px;cursor:pointer;}

#chat .zhumsg a{color:#f4f4f4;text-decoration:none;display:block;}
#chat .zhumsg .jied{cursor:pointer;border-bottom:solid 1px #000;padding:10px 0;}
#chat .zhumsg .jied .text{font-size:14px;}
#chat .zhumsg .jied .num{font-size:12px;}
#chat .zhumsg .now{background:#000;}

#chat .fangke{
	width:15%;
	background-color:#fff;
	height:100%;
	float:left;
	border-right:solid 1px #ddd;
	padding:20px;
}
#chat .fangke .fangke-title{
	font-size:15px;
	color:#4B535F;
	border-bottom:solid 1px #ddd;
	padding-bottom:5px;
	margin-bottom:20px;
}

#chat .fangke .goods{
	text-decoration:none;margin-bottom:40px;color:#666;
}
#chat .fangke .goods img{
	width:60px;height:60px;
}
#chat .fangke .goods .goodstitle{
	font-size:14px;
}
#chat .fangke .goods .goodsprice{
	color:#E64340;font-size:20px;font-weight:bold;margin-top:5px;
}
#chat .fangke .biaoqianform{
	margin-bottom:5px;
}
#chat .fangke .biaoqianform input{
	border:solid 1px #ddd;
	border-radius:3px;
	padding:5px 0;
	text-indent:5px;
	display:block;
	width:100%;
}
#chat .fangke .biaoqianform button{
	margin-top:10px;
	background:#2e3238;
	color:#f4f4f4;
	border:none;
	border-radius:3px;
	display:block;
	width:100%;
	height:1.8pc;
	line-height:1.8pc;
}
#chat .fangke .fkitem{font-size:14px;margin-bottom:5px;}
#chat .fangke .fkitem span{color:#4B535F;}
#chat .fangke .fkitem strong{color:#333;margin-left:5px;}

.m-list li{padding:10px;border-bottom:1px solid #f7f7f7;cursor:pointer;position:relative;height:50px;}
.m-list li:hover{background-color:#f1f1f1;}
.m-list li.active{background-color:#2F2F2F;color:#fff;}
.m-list .avatar{border-radius:100%;width:30px;height:30px;}
.m-list .name{display:inline-block;float:right;font-size:12px;width:120px;height:30px;line-height:30px;overflow:hidden;margin:0;}
.notread{
	position:absolute;
	z-index:9999;
	display:block;
	width:16px;
	height:16px;
	line-height:16px;
	text-align:center;
	background:red;color:#fff;
	font-size:10px;border-radius:100%;
	margin-left: 10%;
	margin-top: -23%;
}

.mlistmore{
	padding: 10px;border-bottom: 1px solid #f7f7f7;
    cursor: pointer;position: relative;text-align:center;background:#f1f1f1;
}

.m-text{height:10pc;border-top:1px solid #ddd}
.m-text textarea{padding:10px;height:100%;width:100%;border:none;outline:0;font-family:Micrsofot Yahei;resize:none}
.m-message{padding:10px 15px;overflow-y:scroll}
.m-message li{margin-bottom:15px}
.m-message .time{margin:7px 0;text-align:center}
.m-message .time>span{display:inline-block;padding:0 18px;font-size:9pt;color:#9FA8B1;border-radius:2px;background-color:#dcdcdc}
.m-message .avatar{float:left;margin:0 10px 0 0;border-radius:3px}
.m-message .text{
	display:inline-block;position:relative;padding:10px;max-width:calc(100% - 40px);min-height:30px;
	line-height:14px;font-size:9pt;text-align:left;word-break:break-all;background-color:#fff;border-radius:4px;
}
.m-message .text:before{content:" ";position:absolute;top:9px;right:100%;border:6px solid transparent;border-right-color:#fff}
.m-message .self{text-align:right}
.m-message .self .avatar{float:right;margin:0 0 0 10px}
.m-message .self .text{background-color:#b2e281}
.m-message .self .text:before{right:inherit;left:100%;border-right-color:transparent;border-left-color:#b2e281}

.search{padding:0.5pc 0;position:relative;border-bottom:1px solid #f7f7f7;background:#f5f5f5;}
.search input{display:block;border:none;border-radius:0;height:1.8pc;line-height:1.8pc;text-indent:5px;width:90%;margin:0 auto;}
.search .searchcon{position:absolute;z-index:99;background:#f1f1f1;width:90%;left:5%;border-radius:0.3pc;padding:0.3pc 0;margin-top:2px;max-height:20pc;overflow-y:scroll;}
.search .searchcon a{text-decoration:none;}
.search .searchcon .name{color:#333;height:2pc;line-height:2pc;overflow:hidden;font-size:0.32pc;padding:0 0.2pc;}



.chosekefu{font-size: 24px;height: 2pc;line-height: 2pc;text-align: center;}
.m-message .avatar{width:40px;height:40px;}
.m-message .text{max-width: calc(100% - 50px);font-size:14px;}
.m-message .text img{margin:0 auto;max-width:14pc;}
.m-message .text .voice2,.m-message .text .map{
	width:20px;height:20px;margin-right:5px;
}

.m-message .text .weidu{
	height:20px;line-height:20px;color:red;
}
.m-message .text .mapadd{
	height:20px;line-height:20px;
}

.sendtype{z-index:999;position:absolute;margin-top:7.5pc;right:0.5pc;width:100%;height:2pc;}
.sendtype .item{
	padding-right:1pc;height:2pc;line-height:2pc;
	font-size:13px;cursor:pointer;color:#8a8a8a;
}
.sendtype .item img{width:1.2pc;height:1.2pc;margin-top:0.4pc;margin-right:0.4pc;}
.sendtype .now{color:#000;}
.sendtype .m-button,.sendtype .m-button-hui{background:#2e3238;color:#f4f4f4;height:2pc;line-height:2pc;padding:0 1pc;display:inline-block;float:left;border:none;border-radius:5px;font-size:14px;cursor:pointer;}
.sendtype .m-button-hui{background:#ccc;}

.menuitem{
	position:absolute;height:2pc;
}
.menuitem .item{
	width:2.5pc;text-align:center;height:2pc;
	cursor:pointer;float:left;color:#666;
}
.menuitem .item img{
	width:1.5pc;height:1.5pc;margin-top:0.25pc;
}

.facewrapper{position:absolute;z-index:999;background:#fff;width:19pc;height:14pc;overflow-y:auto;border:solid 1px #ccc;margin-top:-14pc;display:none;}
.facewrapper .faceitem{display:block;width:2.5pc;height:2.5pc;float:left;padding:0.45pc;cursor:pointer;}

.m-list li.new{background:#FD7B23;}
.m-text textarea{padding-top:2pc;}

.hide{display:none;}

.finishjd,.allshare,.tongbu,.finishjd-xcx,.zhuanjie{
    color: #f4f4f4;
    border: none;
    border-radius: 3px;
    width: 100%;
    height: 1.8pc;
    line-height: 1.8pc;
	text-align:center;
	cursor:pointer;
}
.finishjd,.finishjd-xcx{
	background:#d9534f;
	margin-bottom: 10px;
}
.allshare{
	background:#5bc0de;
	margin-bottom: 10px;
}
.tongbu{
	background:#f0ad4e;
	margin-bottom: 10px;
}
.zhuanjie{
	background:#07c160;
	margin-bottom: 15px;
}
.duquvoice{
	background:#000;
	opacity:0.5;
	color:#fff;
	padding:20px;
	font-size:16px;
	position:absolute;
	top:100px;
	z-index:999;
	border-radius:10px;
}

.voiceplay{width:100px;cursor:pointer;}

.fangke{
	overflow-y:scroll;
}
.fanke-tab{
	height:30px;height:30px;line-height:30px;
	border:solid 1px #000;border-right:none;
}
.fanke-tab .fanke-tab-item{
	flex:1;border-right:solid 1px #000;text-align:center;font-size:14px;
	cursor:pointer;
}
.fanke-tab .now-item{
	background:#000;color:#fff;
}

.quickwrapper .cando{
	border:solid 1px #ccc;margin-bottom:8px;text-align:center;cursor:pointer;
	padding:3px;
}
.quickwrapper .cando img{
	max-width:100%;
}


.jietu{position:absolute;z-index:666;width:30%;left:35%;top:20%;background:#fff;}
.jietu .con{padding:20px;text-align:center;max-height:300px;overflow-y:scroll;}
.jietu .con img{max-width:100%;margin:0 auto;}
.jietu .btns{height:30px;line-height:30px;font-size:14px;width:100%;}
.jietu .btns .leftbtn{width:50%;background:#f1f1f1;text-align:center;float:left;}
.jietu .btns .rightbtn{width:50%;background:#000;text-align:center;float:right;color:#fff;}
</style>
</head>
<body>

<div id="yuyindiv" style="display:none;"></div>
<div id="voicecon" style="display:none;">
	<audio id="audio" src="{MD_ROOT_Z}/newstatic/bai.mp3"></audio>
</div>
<div class="duquvoice hide"></div>

<div class="sharediv hide">

</div>
<div class="zhuanjiediv hide">
	<div class="title">请选择要对接的客服</div>
	{if $othercservice}
		{loop $othercservice $orow}
			<div class="othercserviceitem" data-con="{$orow['content']}">{$orow['name']}</div>
		{/loop}
	{else}
		<div class="noadata">暂无客服</div>
	{/if}
	<div id="zhuanjiebtn">确定</div>
</div>
<div class="blackbg hide"></div>

<div id="chat">
	<div class="zhumsg">
		<img src="{$_SESSION['cservicavatar']}" class="zhumsg-avatar" />
		{if $isxcx == 0}
			{if $isjd == 0}
				<div class="jied now">
					<div class="text">已接待</div>
					<div class="num">{$total1}</div>
				</div>
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>1))}">
				<div class="jied">
					<div class="text">未接待</div>
					<div class="num">{$total2}</div>
				</div>
				</a>
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>2))}">
				<div class="jied">
					<div class="text">接待中</div>
					<div class="num">{$total3}</div>
				</div>
				</a>
			{/if}
			
			{if $isjd == 1}
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>0))}">
				<div class="jied">
					<div class="text">已接待</div>
					<div class="num">{$total1}</div>
				</div>
				</a>
				<div class="jied now">
					<div class="text">未接待</div>
					<div class="num">{$total2}</div>
				</div>
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>2))}">
				<div class="jied">
					<div class="text">接待中</div>
					<div class="num">{$total3}</div>
				</div>
				</a>
			{/if}
			
			{if $isjd == 2}
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>0))}">
				<div class="jied">
					<div class="text">已接待</div>
					<div class="num">{$total1}</div>
				</div>
				</a>
				<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>1))}">
				<div class="jied">
					<div class="text">未接待</div>
					<div class="num">{$total2}</div>
				</div>
				</a>
				<div class="jied now">
					<div class="text">接待中</div>
					<div class="num">{$total3}</div>
				</div>
			{/if}
			<a href="{php echo $this->createMobileUrl('kefucenter',array('isxcx'=>1))}">
			<div class="jied">
				<div class="text">小程序</div>
				<div class="num">{$totalxcx}</div>
			</div>
			</a>
		{else}
			<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>0))}">
			<div class="jied">
				<div class="text">已接待</div>
				<div class="num">{$total1}</div>
			</div>
			</a>
			<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>1))}">
			<div class="jied">
				<div class="text">未接待</div>
				<div class="num">{$total2}</div>
			</div>
			</a>
			<a href="{php echo $this->createMobileUrl('kefucenter',array('isjd'=>2))}">
			<div class="jied">
				<div class="text">接待中</div>
				<div class="num">{$total3}</div>
			</div>
			</a>
			<div class="jied now">
				<div class="text">小程序</div>
				<div class="num">{$totalxcx}</div>
			</div>
		{/if}
		<img src="{MD_ROOT}/loginout.png" class="tuichu" />
	</div>
	
	<div class="sidebar" style="overflow-y: scroll;">
		{if $isxcx == 0}
		<div class="search">
			<input type="text" name="searchtext" placeholder="输入客户信息搜索" />
			<div class="searchcon hide"></div>
		</div>
		<div class="m-list">
			<ul>
				{loop $fanslist $row}
					{if $toopenid == $row['fansopenid']}
					<li class="active">
						<img class="avatar" src="{$row['fansavatar']}" />
						<p class="name">{$row['fansnickname']}</p>
						{if $row['notread'] > 0}<!--<span class="notread">{$row['notread']}</span>-->{/if}
					</li>
					{else}
					<a data-openid="openid{$row['fansopenid']}" href="{$row['url']}" style="color:#9FA8B1;">
					<li>
						<img class="avatar" src="{$row['fansavatar']}" />
						<p class="name">{$row['fansnickname']}</p>
						{if $row['notread'] > 0}<span class="notread">{$row['notread']}</span>{/if}
					</li>
					</a>
					{/if}
				{/loop}
			</ul>
		</div>
			{if $allpage > 2}
			<div class="mlistmore">加载更多>></div>
			{/if}
		{else}
		<div class="m-list">
			<ul>
				{loop $fanslist $row}
					{if $toopenid == $row['fansopenid']}
					<li class="active">
						{if $row['fansavatar'] != ""}
						<img src="{$row['fansavatar']}" class="avatar">
						{else}
						<img src="{MD_ROOT_Z}static/xcx.png" class="avatar">
						{/if}
						<p class="name">{$row['fansnickname']}</p>
						{if $row['notread'] > 0}<!--<span class="notread">{$row['notread']}</span>-->{/if}
					</li>
					{else}
					<a data-openid="openid{$row['fansopenid']}" href="{$row['url']}" style="color:#9FA8B1;">
					<li>
						{if $row['fansavatar'] != ""}
						<img src="{$row['fansavatar']}" class="avatar">
						{else}
						<img src="{MD_ROOT_Z}static/xcx.png" class="avatar">
						{/if}
						<p class="name">{$row['fansnickname']}</p>
						{if $row['notread'] > 0}<span class="notread">{$row['notread']}</span>{/if}
					</li>
					</a>
					{/if}
				{/loop}
			</ul>
		</div>
		{/if}
	</div>
	
	{if !empty($toopenid)}
	<div class="fangke">
		{if $isxcx == 0}
			{if $fanskefu['nowjd'] > 0}
			<div class="finishjd">结束接待</div>
			{/if}
			<div class="allshare">共享聊天</div>
			<div class="tongbu">更新用户信息</div>
			<div class="zhuanjie">客服转接</div>
		{else}
			{if $fanskefu['nowkefu'] == 1}
			<div class="finishjd-xcx">结束接待</div>
			{/if}
		{/if}

		<div class="fanke-tab flex">
			<div class="fanke-tab-item now-item">快捷</div>
			<div class="fanke-tab-item">标签</div>
			<div class="fanke-tab-item">更多</div>
			{if !empty($goods)}
			<div class="fanke-tab-item">商品</div>
			{/if}
		</div>
		
		<div class="fanke-con">
			<div class="fangke-title" style="margin-top:20px;">快捷消息</div>
			<div class="quickwrapper">
				{if $isxcx == 0}
					{if !empty($auto)}
						{loop $auto $fansrow}
							<div class="cando" data-type='{$fansrow["kjtype"]}' data-allcon='{$fansrow["allcon"]}' data-con='{php echo tomedia($fansrow["thumb"])}'>
								{if $fansrow["kjtype"] == 0}{php echo nl2br($fansrow['con'])}{/if}
								{if $fansrow["kjtype"] == 1}<img src='{php echo tomedia($fansrow["thumb"])}' />{/if}
								{if $fansrow["kjtype"] == 2}{php echo htmlspecialchars_decode($fansrow["allcon"])}{/if}
							</div>
						{/loop}
					{else}
						<div>暂无快捷消息</div>
					{/if}
				{else}
					{if !empty($auto)}
						{loop $auto $fansrow}
							<div class="cando" data-type='0'>{$fansrow}</div>
						{/loop}
					{else}
						<div>暂无快捷消息</div>
					{/if}
				{/if}
			</div>
		</div>
	
		<div class="fanke-con hide">
			<div class="fangke-title" style="margin-top:20px;">用户标签</div>
			<div class="biaoqianform">
				<input type="text" id="bqname" name="name" placeholder="填写标签内容" value="{$biaoqian['name']}" />
			</div>
			<div class="biaoqianform">
				<input type="text" id="realname" name="realname" placeholder="填写用户姓名" value="{$biaoqian['realname']}" />
			</div>
			<div class="biaoqianform">
				<input type="text" id="telphone" name="telphone" placeholder="填写用户手机" value="{$biaoqian['telphone']}" />
				<button type="button" id="bqbuttn">提交</button>
			</div>
		</div>
		
		<div class="fanke-con hide">
			<div class="fangke-title" style="margin-top:20px;">更多信息</div>
			{if $isxcx == 0}
				<div class="fkitem"><span>操作系统</span><strong>{$fangkemsg['os']}</strong></div>
				<div class="fkitem"><span>浏览器</span><strong>{$fangkemsg['browse']}</strong></div>
				<div class="fkitem"><span>语言</span><strong>{$fangkemsg['lang']}</strong></div>
				<div class="fkitem"><span>地区</span><strong>{$fangkemsg['province']}{$fangkemsg['city']}</strong></div>
				<div class="fkitem"><span>坐标</span><strong>{$fangkemsg['latitude']},{$fangkemsg['longitude']}</strong></div>
				<div class="fkitem"><span>IP地址</span><strong>{$fangkemsg['ip']}</strong></div>
				<div class="fkitem"><span>公众号</span><strong>{$fangkemsg['gzhname']}</strong></div>
			{else}
				<div class="fkitem"><span>小程序</span><strong>{$xcxres['name']}</strong></div>
			{/if}
		</div>
		
		{if !empty($goods)}
		<div class="fanke-con hide">
			<div class="fangke-title">商品信息</div>
			<a class="goods flex" href="{$goods['url']}">
				<img src="{$goods['thumb']}" />
				<div class="flex1 textellipsis2" style="margin-left:10px;">
					<div class="goodstitle textellipsis2">{$goods['title']}</div>
					<div class="goodsprice">{$goods['price']}</div>
				</div>
			</a>
		</div>
		{/if}
	</div>
	{/if}
	
	<div class="main">
		<div class="m-message">
			{if !empty($toopenid)}
				{if $isxcx == 0}
					<ul>
					{loop $chatcon $row}
						<li>
							{if !empty($row['time'])}<p class="time"><span>{php echo date('Y-m-d H:i:s',$row['time'])}</span></p>{/if}
							<div {if $row['openid'] == $openid}class="main self"{else}class="main"{/if}>
								{if $row['openid'] == $openid}
								<img src="{$fanskefu['kefuavatar']}" class="avatar" alt=""/>
								{else}
								<img src="{$fanskefu['fansavatar']}" class="avatar" alt=""/>
								{/if}
								<div class="text">
									{if $row['type'] == 3 || $row['type'] == 4}
									<img src="{$row['content']}" class="sssbbb" />
									{elseif $row['type'] == 5 || $row['type'] == 6}
									<div class="concon voiceplay flex" data-con="{$row['content']}" data-id="{$row['id']}">
										<img src="{NEWSTATIC_ROOT}/icon/voice2.png" class="voice2" />
										{if $row['hasyuyindu'] == 0 && $openid == $row['toopenid']}
										<span class="weidu">未读</span>
										{/if}
										<div class="flex1"></div>
									</div>
									{elseif $row['type'] == 7}
									<div class="concon toaddress flex">
										<img src="{NEWSTATIC_ROOT}/icon/map.png" class="map" />
										<div class="mapadd">{$row['address'][3]}</div>
										<div class="flex1"></div>
									</div>
									{else}
										{$row['content']}
									{/if}
								</div>
							</div>
						</li>
					{/loop}
					</ul>
				{else}
					<ul>
					{loop $chatcon $row}
						<li>
							{if !empty($row['time'])}<p class="time"><span>{php echo date('Y-m-d H:i:s',$row['time'])}</span></p>{/if}
							<div {if $row['openid'] == $openid}class="main self"{else}class="main"{/if}>
								{if $row['openid'] == $openid}
								<img src="{$row['kefuavatar']}" class="avatar" alt=""/>
								{else}
								<img src="{$row['fansavatar']}" class="avatar" alt=""/>
								{/if}
								<div class="text">
									{if $row['msgtype'] == 'image'}
									<img src="{$row['content']}" class="sssbbb" />
									{/if}
									{if $row['msgtype'] == 'text'}
										{$row['content']}
									{/if}
								</div>
							</div>
						</li>
					{/loop}
					</ul>				
				{/if}
			{else}
			<div class="chosekefu">请选择一个客户进行回答。</div>
			{/if}
		</div>
		<div class="m-text">
			<textarea id="textarea" onkeydown="KeyDown(event)" placeholder="说点什么吧..."></textarea>
		</div>

		<div class="facewrapper">
			<?php 
				for($i=1;$i<=32;$i++){
					echo '<img class="faceitem" src="../addons/elapp_customerservice/static/arclist/'.$i.'.png" data-emstr="[em_'.$i.']" />';
				}
			?>
		</div>
		<div class="menuitem">
			{if $isxcx == 0}
			<div class="item qqface">
				<img src="{NEWSTATIC_ROOT}/icon/face.png" />
			</div>
			{/if}
			<div class="item upimg" id="upimg">
				<img src="{NEWSTATIC_ROOT}/icon/upimg.png" />
			</div>
		</div>
		<div class="sendtype flex">
			<div class="flex1"></div>
			<div class="item flex now" data-id="1">
				<img src="{NEWSTATIC_ROOT}/icon/radio1.png" class="radioimg" />
				<div class="item-text flex1">按Enter键发送</div>
			</div>
			<div class="item flex" data-id="2">
				<img src="{NEWSTATIC_ROOT}/icon/radio2.png" class="radioimg" />
				<div class="item-text flex1">按Ctrl+Enter键发送</div>
			</div>
			<input type="hidden" id="sendtype" value="1" />
			{if empty($toopenid)}
			<button type="button" class="m-button-hui">发送</button>
			{else}
			<button type="button" class="m-button">发送</button>
			{/if}
		</div>
	</div>
</div>

<div id="outerdiv" style="position:fixed;top:0;left:0;background:rgba(0,0,0,0.7);z-index:2;width:100%;height:100%;display:none;">
    <div id="innerdiv" style="position:absolute;">
        <img id="bigimg" style="border:5px solid #fff;" src="" />
    </div>
</div>
<div class="jietu hide">
	<div class="con">
		<img src="" />
	</div>
	<div class="btns">
		<div class="leftbtn">取消</div>
		<div class="rightbtn">确认发送</div>
	</div>
</div> 
<script src="{MD_ROOT_Z}static/newui/js/jquery-3.1.1.min.js"></script>
<script src="{MD_ROOT}/jUploader.js"></script>
<script src="{MD_ROOT_Z}static/newui/js/socket.io.js"></script>
{if !empty($toopenid)}
<script>
var newMessageRemind = {
	_step: 0,
	_title: document.title,
	_timer: null,
	//显示新消息提示
	show:function(){
		var temps = newMessageRemind._title.replace("【　　　】", "").replace("【新消息】", "");
		newMessageRemind._timer = setTimeout(function() {
			newMessageRemind.show();
			//这里写Cookie操作
			newMessageRemind._step++;
			if (newMessageRemind._step == 3) { newMessageRemind._step = 1 };
			if (newMessageRemind._step == 1) { document.title = "【　　　】" + temps };
			if (newMessageRemind._step == 2) { document.title = "【新消息】" + temps };
		}, 800);
		return [newMessageRemind._timer, newMessageRemind._title];
	},
	//取消新消息提示
	clear: function(){
		clearTimeout(newMessageRemind._timer );
		document.title = newMessageRemind._title;
		//这里写Cookie操作
	}
};
var cansend = 1;
var uid = "{$openid}";
var touid = "{$toopenid}";
var fkid = "{$fkid}";
var isxcx = {$isxcx};
var sendurl = "https://api.qiumipai.com:2121/?type=newpublish&to="+touid+'&version=2';
$(function(){
	//var martop = ($(window).height()-$("#chat").height())/2 + 'px';
	//$("#chat").css("marginTop",martop);
	$('.text').each(function(){
		$(this).html(replace_em($(this).html()));
	});
		
	$(".m-message").scrollTop(1000000);
	
	/*$.ajax({
		url:"{php echo $this->createMobileUrl('shangxian')}",
		data:{
			fkid:{$fkid},
			type:'kefu',
		},
		dataType:'json',
		type:'post',        
		success:function(data){
		},
	});*/
	
	// 连接服务端
	var socket = io('https://api.qiumipai.com:2120');
	// 连接后登录
	socket.on('connect', function(){
		socket.emit('login',{'uid':uid,'fkid':fkid});
	});
	
	/*socket.on('reconnect', function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('shangxian')}",
			data:{
				fkid:{$fkid},
				type:'kefu',
			},
			dataType:'json',
			type:'post',        
			success:function(data){
			},
		});
	});*/
	// 后端推送来消息时
	socket.on('new_msg', function(msg){
		if(msg.fromtype != 'xcx'){
			if(msg.toopenid == touid){
				var content = replace_em(msg.content);
				var returnmsg = '<li>'
									+'<p class="time"><span>'+msg.datetime+'</span></p>'
									+'<div class="main">'
											+'<img src="{$fanskefu['fansavatar']}" class="avatar" width="30" height="30" />'
										+'<div class="text">'+content+'</div>'
									+'</div>'
								+'</li>';
				$('.m-message ul').append(returnmsg);
				$(".m-message").scrollTop(1000000);
				$.ajax({
					url:"{php echo $this->createMobileUrl('donotread')}",
					data:{
						fkid:{$fkid},
						type:'kefu',
					},
					dataType:'json',
					type:'post',        
					success:function(data){
					},
				});
			}else{
				var dataopenid = "openid"+msg.toopenid;
				$.ajax({   
					url:"{php echo $this->createMobileUrl('kefucenter')}",   
					type:'post', 
					data:{
						op:'gettype',
						toopenid:msg.toopenid,
						isjd:{$isjd},
					},
					dataType:'json',
					success:function(data){   
						if(data.error == 0){
							$(".m-list ul a").each(function(){
								if($(this).attr('data-openid') == dataopenid){
									$(this).remove();
								}
							});
							var newhref = "{php echo $this->createMobileUrl('kefucenter')}&toopenid="+msg.toopenid+"&isjd="+data.isjd+"&weid={$fanskefu['weid']}";
							var newhtml = '<a data-openid="'+dataopenid+'" href="'+newhref+'" style="color:#9FA8B1;">'
											+'<li>'
												+'<img class="avatar" src="'+msg.newavatar+'" width="30" height="30">'
												+'<p class="name">'+msg.newnickname+'</p>'
												+'<span class="notread">1</span>'
											+'</li>'
											+'</a>';
							$(".m-list ul").prepend(newhtml); 
						}else{
							if(data.isjd == 1){
								var nowjdnum = parseInt($(".jied").eq(1).find('.num').text())+1;
								$(".jied").eq(1).find('.num').text(nowjdnum);
							}else{
								var nowjdnum = parseInt($(".jied").eq(2).find('.num').text())+1;
								$(".jied").eq(2).find('.num').text(nowjdnum);
							}
						}
					}
				});
			}
		}else{
			if(msg.toopenid == touid && isxcx == 1){
				var content = replace_em(msg.content);
				var returnmsg = '<li>'
									+'<p class="time"><span>'+msg.datetime+'</span></p>'
									+'<div class="main">'
											+'<img src="'+msg.newavatar+'" class="avatar" width="30" height="30" />'
										+'<div class="text">'+content+'</div>'
									+'</div>'
								+'</li>';
				$('.m-message ul').append(returnmsg);
				$(".m-message").scrollTop(1000000);
				$.ajax({
					url:"{php echo $this->createMobileUrl('donotread2')}",
					data:{
						fkid:{$fkid},
					},
					dataType:'json',
					type:'post',        
					success:function(data){
					},
				});
			}
			if(msg.toopenid != touid){
				if(isxcx == 1){
					var dataopenid = "openid"+msg.toopenid;
					$(".m-list ul a").each(function(){
						if($(this).attr('data-openid') == dataopenid){
							$(this).remove();
						}
					});
					var newhref = "{php echo $this->createMobileUrl('kefucenter',array('isxcx'=>1))}&toopenid="+msg.toopenid;
					var newhtml = '<a data-openid="'+dataopenid+'" href="'+newhref+'" style="color:#9FA8B1;">'
									+'<li>'
										+'<img class="avatar" src="'+msg.newavatar+'" width="30" height="30">'
										+'<p class="name">'+msg.newnickname+'</p>'
										+'<span class="notread">1</span>'
									+'</li>'
									+'</a>';
					$(".m-list ul").prepend(newhtml); 
				}else{
					var nowjdnum = parseInt($(".jied").eq(3).find('.num').text())+1;
					$(".jied").eq(3).find('.num').text(nowjdnum);
				}
			}
		}
		playtixiang();
	});
	
	$(".sssbbb").click(function(){  
		var _this = $(this);//将当前的pimg元素作为_this传入函数  
		imgShow("#outerdiv", "#innerdiv", "#bigimg", _this);  
	});
	
	$(".finishjd").click(function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('finishjd');}",   
			type:'post', 
			data:{
				fkid:{$fanskefu['id']},
				id:{$cservice['id']},
				isjd:{$isjd},
			},
			dataType:'json',
			success:function(data){
				if (data.error == 1) {
					alert(data.msg);
				}else{
					window.location.href = data.url;
				}
			}
		});
	});
	
	$(".finishjd-xcx").click(function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('finishjdxcx');}",   
			type:'post', 
			data:{
				fkid:{$fanskefu['id']},
			},
			dataType:'json',
			success:function(data){
				if (data.error == 1) {
					alert(data.msg);
				}else{
					window.location.href = data.url;
				}
			}
		});
	});
	
	$(".allshare").click(function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('kefucenter');}",   
			type:'post', 
			data:{
				op:'allshare',
				toopenid:'{$fanskefu["fansopenid"]}',
				weid:{$fanskefu["weid"]},
			},
			dataType:'json',
			success:function(data){
				if (data.error == 1) {
					alert(data.msg);
				}else{
					$(".sharediv").html(data.html);
					$(".sharediv,.blackbg").removeClass("hide");
				}
			}
		});
	});
	
	$(".zhuanjie").click(function(){
		$(".zhuanjiediv,.blackbg").removeClass("hide");
	});
	
	$(".othercserviceitem").click(function(){
		$(".othercserviceitem").removeClass("nowother");
		$(this).addClass("nowother");
	});
	
	$('#zhuanjiebtn').click(function(){
		$.ajax({   
			 url:"{php echo $this->createMobileUrl('zhuanjie')}",   
			 type:'post', 
			 data:{
				toopenid:touid,
				content:$('.zhuanjiediv .nowother').attr('data-con'),
			 },
			 dataType:'json',
			 success:function(data){   
				if(data.error == 0){
					alert(data.msg);
					$(".zhuanjiediv,.blackbg").addClass("hide");
					$.ajax({   
						url:sendurl,   
						type:'get', 
						data:{
							zhuanjie:1,
							toopenid:data.toopenid,
							content:'',
							msgtype:0,
						},
						dataType:'jsonp',
						success:function(data){ 
						}
					});	
				}else{
					alert(data.msg);
				}
			 }
		});
	});
	
	$(".tongbu").click(function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('kefucenter');}",   
			type:'post', 
			data:{
				op:'tongbu',
				toopenid:'{$fanskefu["fansopenid"]}',
				weid:{$fanskefu["weid"]},
			},
			dataType:'json',
			success:function(data){
				if (data.error == 1) {
					alert(data.message);
				}else{
					history.go(0);
				}
			}
		});
	});
	
	$(".blackbg").click(function(){
		$(".sharediv,.blackbg,.jietu,.zhuanjiediv").addClass("hide");
	});
	
	$(".jietu .btns .leftbtn").click(function(){
		$(".blackbg,.jietu").addClass("hide");
	});
	
	$(".jietu .btns .rightbtn").click(function(){
		$.ajax({
			url:"{php echo $this->createMobileUrl('jietuupload');}",   
			type:'post', 
			data:{
				img:$(".jietu .con img").attr('src'),
			},
			dataType:'json',
			success:function(data){
				if (data.error == 1) {
					alert(data.message);
				}else{
					addchat(data.imgurl,3);
					$(".blackbg,.jietu").addClass("hide");
				}
			}
		});
	});
	
	$(".jied").click(function(){
		$(".jied").removeClass('now');
		$(this).addClass('now');
	});
	
	$(".qqface").click(function(){
		$(".facewrapper").toggle();
	});

	$(".facewrapper img").click(function(){
		$("#textarea").val($("#textarea").val()+$(this).attr("data-emstr"));
		$(".facewrapper").hide();
	});
	
	$(".quickwrapper").on('click','.cando',function(){		
		if($(this).attr('data-type') == 0 || $(this).attr('data-type') == 2){
			if($(this).attr('data-type') == 0){
				addchat($(this).text(),2);
			}else{
				addchat($(this).attr('data-allcon'),2,1);
			}
		}else{
			addchat($(this).attr('data-con'),3);
		}
	});
	
	{if $voiceon != 1}
	$(document).on('click','.voiceplay',function(){
		var windowWidth = document.documentElement.clientWidth;
		var windowHeight = document.documentElement.clientHeight;
		var popupHeight = $(".duquvoice").height();
		var popupWidth = $(".duquvoice").width();
		
		var objvoice = $(this);
		var media_id = objvoice.attr('data-con');
		var chatid = objvoice.attr('data-id');
		if(media_id.indexOf(".mp3") == -1){
			alert("请在手机上读取语音！");
		}else{
			$.ajax({
				url:"{php echo $this->createMobileUrl('duvoice');}",   
				type:'post', 
				data:{
					fkid:{$fanskefu['id']},
					media_id:media_id,
				},
				beforeSend:function(){
					$(".duquvoice").css({
						"top": (windowHeight-popupHeight)/2,
						"left": (windowWidth-popupWidth)/2
					}).removeClass('hide').text('语音加载中');
					document.getElementById('audio').play();
					document.getElementById('audio').pause();
				},
				dataType:'json',
				success:function(data){
					objvoice.children('.weidu').remove();
					playMusic(media_id);
				}
			});
		}
	});
	{/if}
	
	{if $voiceon != 2}
	$(document).on('click','.voiceplay',function(){
		var windowWidth = document.documentElement.clientWidth;
		var windowHeight = document.documentElement.clientHeight;
		var popupHeight = $(".duquvoice").height();
		var popupWidth = $(".duquvoice").width();

		var objvoice = $(this);
		var media_id = objvoice.attr('data-con');
		var chatid = objvoice.attr('data-id');
		if(media_id.indexOf(".mp3") == -1){
			$.ajax({
				url:"{php echo $this->createMobileUrl('getvoice');}",   
				type:'post', 
				data:{
					fkid:{$fanskefu['id']},
					media_id:media_id,
					chatid:chatid,
				},
				beforeSend:function(){
					$(".duquvoice").css({
						"top": (windowHeight-popupHeight)/2,
						"left": (windowWidth-popupWidth)/2
					}).removeClass('hide').text('语音加载中');
					document.getElementById('audio').play();
					document.getElementById('audio').pause();
				},
				dataType:'json',
				success:function(data){
					if (data.error == 1) {
						alert(data.msg);
						$(".duquvoice").addClass('hide');
					}else{
						objvoice.attr('data-con',data.voicefile);
						if (data.weidu == 1){
							objvoice.children('.weidu').remove();
						}
						playMusic(data.voicefile);
					}
					
				}
			});
		}else{
			$.ajax({
				url:"{php echo $this->createMobileUrl('duvoice');}",   
				type:'post', 
				data:{
					fkid:{$fanskefu['id']},
					media_id:media_id,
				},
				beforeSend:function(){
					$(".duquvoice").css({
						"top": (windowHeight-popupHeight)/2,
						"left": (windowWidth-popupWidth)/2
					}).removeClass('hide').text('语音加载中');
					document.getElementById('audio').play();
					document.getElementById('audio').pause();
				},
				dataType:'json',
				success:function(data){
					objvoice.children('.weidu').remove();
					playMusic(media_id);
				}
			});
		}
	});
	{/if}
	
	function playMusic(path) {
		var timestamp = (new Date()).getTime();
      	path = path+'?time='+timestamp+Math.random();
		var audioEle = document.getElementById('audio');
		audioEle.src = path;
		$(".duquvoice").text('语音播放中');
		audioEle.pause();
		audioEle.currentTime = 0;
		audioEle.play();
		audioEle.addEventListener('error', function (e) {
			$(".duquvoice").addClass("hide");
			alert('加载语音失败，请重新尝试！');
		});	
		audioEle.addEventListener('ended', function () {
			$(".duquvoice").addClass("hide");
		}, false);
	}
	
	$(".sendtype .item").click(function(){
		$(".sendtype .item").removeClass('now');
		$('.radioimg').attr('src','{NEWSTATIC_ROOT}/icon/radio2.png');
		$(this).find('img').attr('src','{NEWSTATIC_ROOT}/icon/radio1.png');
		$(this).addClass('now');
		$("#sendtype").val($(this).attr('data-id'));
	});	
	$(document).on('click','.m-button',function(){　　　　//动态事件绑定  为页面所有的dd添加一个事件 包括新增的节点
        addchat($("#textarea").val(),2);
    });
	
	$.jUploader.setDefaults({
		cancelable: false, // 可取消上传
		allowedExtensions: ['jpg', 'png', 'gif','jpeg'], // 只允许上传图片
		messages: {
			upload: '上传',
			cancel: '取消',
			emptyFile: "{file} 为空，请选择一个文件.",
			invalidExtension: "{file} 后缀名不合法. 只有 {extensions} 是允许的.",
			onLeave: "文件正在上传，如果你现在离开，上传将会被取消。"
		}
	});
	
	$.jUploader({
		button: 'upimg', // 这里设置按钮id
		action: "{php echo $this->createMobileUrl('pcupload')}", // 这里设置上传处理接口
		onUpload: function (fileName) {
		},
		// 上传完成事件
		onComplete: function (fileName, response) {
			if (response.error == 0) {
				addchat(response.imgurl,3);
			} else {
				alert(response.message);
			}
		},
	});
	
	$("input[name='searchtext']").keyup(function(){
		var searchtext = $(this).val();
		$.ajax({   
			url:"{php echo $this->createMobileUrl('kefucenter')}",   
			type:'post', 
			data:{
				op:'search',
				searchtext:searchtext
			},
			dataType:'html',
			success:function(data){   
				if(data == ''){
					$(".searchcon").addClass('hide');
				}else{
					$(".searchcon").removeClass('hide').html(data);
				}
			}
		});
	});
	
	$(".fanke-tab-item").click(function(){
		var fkindex = $(this).index();
		$(".fanke-tab-item").removeClass("now-item");
		$(this).addClass("now-item");
		
		$(".fanke-con").addClass("hide");
		$(".fanke-con").eq(fkindex).removeClass("hide");
	});
	
	
	
	$("#bqbuttn").click(function(){
		$.ajax({   
			url:"{php echo $this->createMobileUrl('addbiaoqian')}",   
			type:'post', 
			data:{
				name:$("#bqname").val(),
				realname:$("#realname").val(),
				telphone:$("#telphone").val(),
				toopenid:'{$toopenid}',
			},
			dataType:'json',
			success:function(data){   
				alert(data.msg);
			}
		});
	});
	
	$(".tuichu").click(function(){
		if(confirm("确认退出登录吗?")){
		　　window.location.href = "{php echo $this->createMobileUrl('kefucenter',array('op'=>'tuichu'))}";
		}
	});
	
	$(document).click(function(){
		newMessageRemind.clear();
	});
})

var input = document.getElementById('textarea');
var imgReader = function( item ){
    var file = item.getAsFile(),
        reader = new FileReader();

    // 读取文件后将其显示在网页中
    reader.onload = function( e ){
        //var img = new Image();
        //img.src = e.target.result;
		//document.body.appendChild( img );
		$(".jietu .con img").attr('src',e.target.result);
        $(".jietu,.blackbg").removeClass('hide');
    };
    // 读取文件
    reader.readAsDataURL( file );
};
input.addEventListener( 'paste', function( event ){
    // 添加到事件对象中的访问系统剪贴板的接口
    var clipboardData = event.clipboardData,
        i = 0,
        items, item, types;

    if( clipboardData ){
        items = clipboardData.items;

        if( !items ){
            return;
        }

        item = items[0];
        // 保存在剪贴板中的数据类型
        types = clipboardData.types || [];

        for( ; i < types.length; i++ ){
            if( types[i] === 'Files' ){
                item = items[i];
                break;
            }
        }

        // 判断是否为图片数据
        if( item && item.kind === 'file' && item.type.match(/^image\//i) ){
            // 读取该图片            
            imgReader( item );
        }
    }
});


//查看QQ表情结果
function replace_em(str){
	str = str.replace(/\[em_([0-9]*)\]/g,'<img src="{MD_ROOT_Z}/static/arclist/$1.png" style="width:1.5pc;" border="0" />');
	return str;
}

function imgShow(outerdiv, innerdiv, bigimg, _this){  
	var src = _this.attr("src");//获取当前点击的pimg元素中的src属性  
	$(bigimg).attr("src", src);//设置#bigimg元素的src属性  
  
	$("<img/>").attr("src", src).on('load',function(){
		var windowW = $(window).width();//获取当前窗口宽度  
		var windowH = $(window).height();//获取当前窗口高度  
		var realWidth = this.width;//获取图片真实宽度  
		var realHeight = this.height;//获取图片真实高度  
		var imgWidth, imgHeight;  
		var scale = 0.8;//缩放尺寸，当图片真实宽度和高度大于窗口宽度和高度时进行缩放  
		  
		if(realHeight>windowH*scale) {//判断图片高度  
			imgHeight = windowH*scale;//如大于窗口高度，图片高度进行缩放  
			imgWidth = imgHeight/realHeight*realWidth;//等比例缩放宽度  
			if(imgWidth>windowW*scale) {//如宽度扔大于窗口宽度  
				imgWidth = windowW*scale;//再对宽度进行缩放  
			}  
		} else if(realWidth>windowW*scale) {//如图片高度合适，判断图片宽度 
			imgWidth = windowW*scale;//如大于窗口宽度，图片宽度进行缩放  
			imgHeight = imgWidth/realWidth*realHeight;//等比例缩放高度  
		} else {//如果图片真实高度和宽度都符合要求，高宽不变  
			imgWidth = realWidth;  
			imgHeight = realHeight;  
		}  
		$(bigimg).css("width",imgWidth);//以最终的宽度对图片缩放  
		  
		var w = (windowW-imgWidth)/2;//计算图片与窗口左边距  
		var h = (windowH-imgHeight)/2;//计算图片与窗口上边距  
		$(innerdiv).css({"top":h, "left":w});//设置#innerdiv的top和left属性  
		$(outerdiv).fadeIn("fast");//淡入显示#outerdiv及.pimg  
	});  

	  
	$(outerdiv).click(function(){//再次点击淡出消失弹出层  
		$(this).fadeOut("fast");  
	});
}

//回车键
function KeyDown(event){
	if (event.ctrlKey && event.keyCode == 13 && $("#sendtype").val() == 2){
		event.returnValue=false;
		event.cancel = true;
		addchat($("#textarea").val(),2);
	}
	
	if (event.keyCode == 13 && $("#sendtype").val() == 1){
		event.returnValue=false;
		event.cancel = true;
		addchat($("#textarea").val(),2);
	}
}

function playtixiang(){
	newMessageRemind.show();
	var audiohtml = '<audio autoplay="autoplay" controls>'
		  +'<source src="{MD_ROOT}/tixing.ogg" type="audio/ogg">'
		  +'<source src="{MD_ROOT}/tixing.mp3" type="audio/mpeg">'
		  +'<source src="{MD_ROOT}/tixing.wav" type="audio/wav">'
		+'</audio>';
	$("#yuyindiv").html(audiohtml);
}

{if $isxcx == 0}
function addchat(content,type,istuwen = 0){
	if(cansend == 1){
		cansend = 0;
		$.ajax({   
			 url:"{php echo $this->createMobileUrl('kefucenter')}",   
			 type:'post', 
			 data:{
				op:'addchat',
				toopenid:touid,
				content:content,
				fkid:{$fkid},
				qudao:'',
				goodsid:0,
				type:type,
				weid:{$fanskefu['weid']},
				istuwen:istuwen,
			 },
			 dataType:'json',
			 success:function(data){   
				if(data.error == 0){
					var chatcon = replace_em(data.content);
					var returnmsg = '<li>'
										+'<p class="time"><span>'+data.datetime+'</span></p>'
										+'<div class="main self">'
												+'<img src="{$fanskefu['kefuavatar']}" class="avatar" width="30" height="30" />'
											+'<div class="text">'+chatcon+'</div>'
										+'</div>'
									+'</li>';
					$('.m-message ul').append(returnmsg);
					$(".m-message").scrollTop(1000000);
					$("#textarea").val("");
					$.ajax({   
						url:sendurl,   
						type:'get', 
						data:{
							content:content,
							msgtype:type,
							toopenid:uid,
							chatid:data.chatid,
							istuwen:istuwen,
							datetime:data.datetime
						},
						dataType:'jsonp',
						success:function(data){ 
						}
					});
				}else{
					alert(data.msg);
				}
				cansend = 1;
			}
		});
	}
}
{else}
function addchat(content,type){
	if(cansend == 1){
		cansend = 0;
		$.ajax({   
			 url:"{php echo $this->createMobileUrl('addchatxcx')}",   
			 type:'post', 
			 data:{
				content:content,
				fkid:{$fkid},
				type:type,
				weid:{$fanskefu['weid']},
			 },
			 dataType:'json',
			 success:function(data){   
				if(data.error == 0){
					var chatcon = replace_em(data.content);
					var returnmsg = '<li>'
										+'<p class="time"><span>'+data.datetime+'</span></p>'
										+'<div class="main self">'
												+'<img src="{$fanskefu['kefuavatar']}" class="avatar" width="30" height="30" />'
											+'<div class="text">'+chatcon+'</div>'
										+'</div>'
									+'</li>';
					$('.m-message ul').append(returnmsg);
					$(".m-message").scrollTop(1000000);
					$("#textarea").val("");
				}else{
					alert(data.msg);
				}
				cansend = 1;
			}
		});
	}
}
{/if}
</script>
{else}
<script>
var cansend = 1;
var uid = "{$openid}";
var touid = "";
var fkid = "";
var sendurl = "https://api.qiumipai.com:2121/?type=newpublish&to="+touid+'&version=2';
$(function(){
	
	// 连接服务端
	var socket = io('https://api.qiumipai.com:2120');
	// 连接后登录
	socket.on('connect', function(){
		socket.emit('login',{'uid':uid,'fkid':fkid});
	});
	
	// 后端推送来消息时
	socket.on('new_msg', function(msg){
		if(msg.fromtype == 'gzh'){
			var dataopenid = "openid"+msg.toopenid;
			$.ajax({   
				url:"{php echo $this->createMobileUrl('kefucenter')}",   
				type:'post', 
				data:{
					op:'gettype',
					toopenid:msg.toopenid,
					isjd:{$isjd},
				},
				dataType:'json',
				success:function(data){   
					if(data.error == 0){
						$(".m-list ul a").each(function(){
							if($(this).attr('data-openid') == dataopenid){
								$(this).remove();
							}
						});
						var newhref = "{php echo $this->createMobileUrl('kefucenter')}&toopenid="+msg.toopenid+"&isjd="+data.isjd;
						var newhtml = '<a data-openid="'+dataopenid+'" href="'+newhref+'" style="color:#9FA8B1;">'
										+'<li>'
											+'<img class="avatar" src="'+msg.newavatar+'" width="30" height="30">'
											+'<p class="name">'+msg.newnickname+'</p>'
											+'<span class="notread">1</span>'
										+'</li>'
										+'</a>';
						$(".m-list ul").prepend(newhtml); 
					}else{
						if(data.isjd == 1){
							var nowjdnum = parseInt($(".jied").eq(1).find('.num').text())+1;
							$(".jied").eq(1).find('.num').text(nowjdnum);
						}else{
							var nowjdnum = parseInt($(".jied").eq(2).find('.num').text())+1;
							$(".jied").eq(2).find('.num').text(nowjdnum);
						}
					}
				}
			});
		}
		
		if(msg.to == uid && msg.fromtype == 'xcx'){
			if($(".jied").eq(3).hasClass('now')){
				var dataopenid = "openid"+msg.toopenid;
				$(".m-list ul a").each(function(){
					if($(this).attr('data-openid') == dataopenid){
						$(this).remove();
					}
				});
				var newhref = "{php echo $this->createMobileUrl('kefucenter',array('isxcx'=>1))}&toopenid="+msg.toopenid;
				var newhtml = '<a data-openid="'+dataopenid+'" href="'+newhref+'" style="color:#9FA8B1;">'
								+'<li>'
									+'<img class="avatar" src="'+msg.newavatar+'" width="30" height="30">'
									+'<p class="name">'+msg.newnickname+'</p>'
									+'<span class="notread">1</span>'
								+'</li>'
								+'</a>';
				$(".m-list ul").prepend(newhtml); 
			}else{
				var nowjdnum = parseInt($(".jied").eq(3).find('.num').text())+1;
				$(".jied").eq(3).find('.num').text(nowjdnum);
			}
		}
		playtixiang();
	});
	
	$(".tuichu").click(function(){
		if(confirm("确认退出登录吗?")){
		　　window.location.href = "{php echo $this->createMobileUrl('kefucenter',array('op'=>'tuichu'))}";
		}
	});
	
	$("input[name='searchtext']").keyup(function(){
		var searchtext = $(this).val();
		$.ajax({   
			url:"{php echo $this->createMobileUrl('kefucenter')}",   
			type:'post', 
			data:{
				op:'search',
				searchtext:searchtext
			},
			dataType:'html',
			success:function(data){   
				if(data == ''){
					$(".searchcon").addClass('hide');
				}else{
					$(".searchcon").removeClass('hide').html(data);
				}
			}
		});
	});
});

function playtixiang(){
	newMessageRemind.show();
	var audiohtml = '<audio autoplay="autoplay" controls>'
		  +'<source src="{MD_ROOT}/tixing.ogg" type="audio/ogg">'
		  +'<source src="{MD_ROOT}/tixing.mp3" type="audio/mpeg">'
		  +'<source src="{MD_ROOT}/tixing.wav" type="audio/wav">'
		+'</audio>';
	$("#yuyindiv").html(audiohtml);
}
</script>
{/if}
<script>
var count = 2;
$(function(){
	$(".mlistmore").click(function(){
		$.ajax({   
			url:"{php echo $this->createMobileUrl('kefucenter')}",   
			type:'post', 
			data:{
				op:'mlistmore',
				page:count,
				isjd:{$isjd}
			},
			dataType:'html',
			success:function(data){   
				if(data == ''){
					$(".mlistmore").addClass('hide');
				}else{
					$(".m-list ul").append(data);
				}
				count++;
			}
		});
	});
})
</script>
</body>
</html>