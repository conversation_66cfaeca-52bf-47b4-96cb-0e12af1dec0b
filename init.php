<?php
//todo 各入口的init，后期交由框架统一处理，利用实际的对象类、request类，中间件init add by lvbaocheng 2024/1/15
// 处理 Access Denied
defined('IN_IA') or define('IN_IA', true);
define('IMS_FAMILY', "x");
define('IA_ROOT', __DIR__ . '');

define('TIMESTAMP', time());
define('DEVELOPMENT', false);
define('ATTACHMENT_ROOT', IA_ROOT . '/public/attachment/');
define('DATA_ROOT', IA_ROOT . '/public/data/application/');//二维码、海报等生成目录 如：data/application/shop/qrcode
define('STATIC_ROOT', '/static/application/');//静态资源目录
require IA_ROOT . '/data/config.php';
require_once IA_ROOT . '/addons/elapp_shop/defines.php';
require_once IA_ROOT . '/extend/framework/classes/loader.class.php';
require_once IA_ROOT . '/extend/framework/common/const.inc.php';
require_once IA_ROOT . "/extend/framework/common/function.php";

//加载类
load()->func('global');
load()->func('compat');
load()->func('compat.biz');
load()->classs('account');
load()->model('cache');
load()->model('account');
load()->model('setting');
load()->model('module');
load()->func('pdo');
load()->library('agent');
load()->classs('db');
load()->func('communication');
load()->model('attachment');
load()->func('safe');

define('CLIENT_IP', getip());

//定义基础config
$_GPC = [
    'preview' => '',
    'merchid' => 0,
    'copartner_id' => 0,
    'supplyid' => 0,
    'liveid' => 0,
];

// 安全获取request参数，避免ThinkPHP未完全初始化时的问题
$params = [];
try {
    if (function_exists('request') && request() !== null) {
        $params = request()->param();
        if (!is_array($params)) {
            $params = [];
        }
    }
} catch (Exception $e) {
    $params = [];
}

ihtmlspecialchars($params);
$_GPC = array_merge($_GPC,$params);

// 安全获取cookie
$cplen = strlen($config['cookie']['pre']);
try {
    if (function_exists('request') && request() !== null) {
        $cookies = request()->cookie();
        if (is_array($cookies)) {
            foreach ($cookies as $key => $value) {
                if ($config['cookie']['pre'] == substr($key, 0, $cplen)) {
                    $_GPC[substr($key, $cplen)] = $value;
                }
            }
        }
    }
} catch (Exception $e) {
    // 如果ThinkPHP的request()不可用，回退到原生PHP方式
    foreach ($_COOKIE as $key => $value) {
        if ($config['cookie']['pre'] == substr($key, 0, $cplen)) {
            $_GPC[substr($key, $cplen)] = $value;
        }
    }
}

$_S = [
    'plugin' => ''
];

// 安全获取request相关信息
$request_info = [
    'isajax' => false,
    'ispost' => false,
    'sitescheme' => 'http',
    'script_name' => '',
    'siteurl' => '',
    'root' => '/',
    'siteroot' => '/',
    'domain' => '/',
    'pathinfo' => ''
];

try {
    if (function_exists('request') && request() !== null) {
        $req = request();
        $request_info['isajax'] = $req->isAjax() ?: false;
        $request_info['ispost'] = $req->isPost() ?: false;
        $request_info['sitescheme'] = $req->scheme() ?: 'http';
        $request_info['script_name'] = $req->server('PHP_SELF') ?: '';
        $request_info['siteurl'] = $req->url(true) ?: '';
        $request_info['root'] = $req->root(true) . '/' ?: '/';
        $request_info['siteroot'] = $req->domain() . '/' ?: '/';
        $request_info['domain'] = $req->domain() . '/' ?: '/';
        $request_info['pathinfo'] = $req->pathinfo() ?: '';
    }
} catch (Exception $e) {
    // 如果ThinkPHP的request()不可用，使用原生PHP方式
    $request_info['ispost'] = $_SERVER['REQUEST_METHOD'] === 'POST';
    $request_info['script_name'] = $_SERVER['PHP_SELF'] ?? '';
    $request_info['sitescheme'] = (isset($_SERVER['HTTPS']) && $_SERVER['HTTPS'] === 'on') ? 'https' : 'http';
    $host = $_SERVER['HTTP_HOST'] ?? 'localhost';
    $request_info['siteroot'] = $request_info['sitescheme'] . '://' . $host . '/';
    $request_info['domain'] = $request_info['siteroot'];
    $request_info['root'] = $request_info['siteroot'];
    $request_info['siteurl'] = $request_info['siteroot'] . ltrim($_SERVER['REQUEST_URI'] ?? '', '/');
    $request_info['pathinfo'] = $_SERVER['PATH_INFO'] ?? '';
}

$_W = [
    'timestamp' => TIMESTAMP,
    'clientip' => CLIENT_IP,
    'uniacid' => !empty($_GPC['__uniacid']) ? $_GPC['__uniacid'] : (!empty($_GPC['i']) ? $_GPC['i'] : intval($_GPC['weid'] ?? 0)),
    'acid' => !empty($_GPC['acid']) ? $_GPC['acid'] : (!empty($_GPC['i']) ? $_GPC['i'] : 0),
    'uid' => !empty($_GPC['uid']) ? $_GPC['uid'] : (!empty($_GPC['__uid']) ? $_GPC['__uid'] : 0),
    'config' => $config,
    'charset' => $config['setting']['charset'],
    'isajax' => $request_info['isajax'],
    'ispost' => $request_info['ispost'],
    'sitescheme' => $request_info['sitescheme'],
    'script_name' => $request_info['script_name'],
    'siteurl' => $request_info['siteurl'],
    'root' => $request_info['root'],
    'siteroot' => $request_info['siteroot'],
    'domain' => $request_info['domain'],
    'setting' => [
        'upload' => $config['upload']
    ],
    'routes' => !empty($request_info['pathinfo']) ? explode('/', $request_info['pathinfo'])[0] : '',
    //'action' => explode('/', request()->pathinfo())[1],
];

// 如果系统配置开启了https，强制所有使用https，否则http调用接口会失败
if (isset($_W['config']['setting']['https']) && $_W['config']['setting']['https'] == 1) {
    $_W['ishttps'] = true;
    $_W['sitescheme'] = 'https';
    //  ["siteurl"] => string(49) "http://localhost/app.php/member.cart?i=1&v=172732"
    $_W['siteurl'] = str_replace('http://', 'https://', $_W['siteurl']);
    //  ["root"] => string(25) "http://localhost/app.php/"
    $_W['root'] = str_replace('http://', 'https://', $_W['root']);
    //  ["siteroot"] => string(17) "http://localhost/"
    $_W['siteroot'] = str_replace('http://', 'https://', $_W['siteroot']);
    //  ["domain"] => string(17) "http://localhost/"
    $_W['domain'] = str_replace('http://', 'https://', $_W['domain']);
}

$GLOBALS["_S"] = $_W['shopset']  = empty(m('cache')->getArray('globalset')) ? m('common')->setGlobalSet() : m('cache')->getArray('globalset');
if (!empty($_W['routes'])) {
    $routes = explode('.', $_W['routes']);
    $_W['isplugin'] = $isplugin = is_dir(ELAPP_SHOP_PLUGIN . $routes[0]);
    $_S['plugin'] = $_W['plugin'] = $isplugin ? $routes[0] : '';
}
//处理请求解析为 PHP 数组赋值给$_GPC['__input']
if (!$_W['isajax']) {
    $input = file_get_contents('php://input');
    if (!empty($input)) {
        $__input = @json_decode($input, true);
        if (!empty($__input)) {
            $_GPC['__input'] = $__input;
            $_W['isajax'] = true;
        }
    }
    unset($input, $__input);
}
$_W['os'] = Agent::deviceType();
if (Agent::DEVICE_MOBILE == $_W['os']) {
    $_W['os'] = 'mobile';
} elseif (Agent::DEVICE_DESKTOP == $_W['os']) {
    $_W['os'] = 'windows';
} else {
    $_W['os'] = 'unknown';
}

$_W['container'] = Agent::browserType();
if (Agent::MICRO_MESSAGE_YES == Agent::isMicroMessage()) {
    $_W['container'] = 'wechat';
    if (Agent::MICRO_WXWORK_YES == Agent::isWxWork()) {
        $_W['container'] = 'workwechat';
    }    
    if(Agent::MICRO_WXAPP_YES == Agent::isWeChatMiniProgram()){
        $_W['container'] = 'wxapp';
    }
} elseif (Agent::BROWSER_TYPE_ANDROID == $_W['container']) {
    $_W['container'] = 'android';
} elseif (Agent::BROWSER_TYPE_IPAD == $_W['container']) {
    $_W['container'] = 'ipad';
} elseif (Agent::BROWSER_TYPE_IPHONE == $_W['container']) {
    $_W['container'] = 'iphone';
} elseif (Agent::BROWSER_TYPE_IPOD == $_W['container']) {
    $_W['container'] = 'ipod';
}else {
    $_W['container'] = 'unknown';
}
if ('wechat' == $_W['container'] || 'baidu' == $_W['container']) {
    $_W['platform'] = 'account';
}

//此处参数会影响权限，路由匹配规则
$c_a = explode('.', $_W['routes']);

// 安全获取pathinfo中的do参数
$do = '';
try {
    if (function_exists('request') && request() !== null) {
        $pathinfo_parts = explode('/', request()->pathinfo());
        $do = isset($pathinfo_parts[1]) ? $pathinfo_parts[1] : '';
    }
} catch (Exception $e) {
    // 如果ThinkPHP的request()不可用，尝试从其他地方获取
    if (!empty($_SERVER['PATH_INFO'])) {
        $pathinfo_parts = explode('/', $_SERVER['PATH_INFO']);
        $do = isset($pathinfo_parts[1]) ? $pathinfo_parts[1] : '';
    }
}

$_W['controller'] = $controller = !empty($_GPC['c']) ? $_GPC['c'] : (!empty($c_a[1]) ? $c_a[0] : '');
$_W['action'] = $action = !empty($_GPC['a']) ? $_GPC['a'] : (!empty($c_a[1]) ? $c_a[1] : $c_a[0]);
//$_W['action'] = $action = !empty($_GPC['a']) ? $_GPC['a'] : (!empty($_W['action']) ? $_W['action'] : $c_a[0]);
$_W['do'] = $do = !empty($_GPC['do']) ? $_GPC['do'] : (!empty($do) ? $do : '');
header('Content-Type: text/html; charset=' . $_W['charset']);

//获取缓存
if (!in_array($_W['config']['setting']['cache'], array('mysql', 'memcache', 'redis'))) {
    $_W['config']['setting']['cache'] = 'mysql';
}
load()->func('cache');
setting_load();

$_W['attachurl'] = $_W['attachurl_remote'] = attachment_set_attach_url();