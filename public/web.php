<?php

namespace web;

use think\App;
use think\helper;

error_reporting(0);
define('APP_PATH', __DIR__ . '/../application/web/');

require __DIR__ . '/../vendor/autoload.php';
include __DIR__.'/../vendor/topthink/framework/src/helper.php';


// 执行HTTP应用并响应
$http = (new App())->http;

// 加载应用配置文件
config(\think\facade\Config::load(__DIR__ . '/../application/web/config/app.php', 'app'));

require __DIR__ . '/../application/web/init_start.php';

$mobule = substr(basename(__FILE__), 0, strrpos(basename(__FILE__), '.'));

$response = $http->path(APP_PATH)->name($mobule)->run();

$response->send();

$http->end($response);