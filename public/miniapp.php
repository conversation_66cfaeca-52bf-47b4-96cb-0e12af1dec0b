<?php

namespace app;

use think\App;
use think\helper;

define('APP_PATH', __DIR__ . '/../addons/elapp_shop/plugin/app/');

require __DIR__ . '/../vendor/autoload.php';
include __DIR__.'/../vendor/topthink/framework/src/helper.php';
error_reporting(0);
require_once __DIR__ . '/request_logger.php';
log_web_request();
// 执行HTTP应用并响应
$http = (new App())->http;

// 加载应用配置文件
config(\think\facade\Config::load(__DIR__ . '/../wap/config/app.php', 'app'));

require __DIR__ . '/../wap/init_miniapp.php';
$mobule = substr(basename(__FILE__), 0, strrpos(basename(__FILE__), '.'));

$response = $http->path(APP_PATH)->name($mobule)->run();

$response->send();

$http->end($response);