<?php
/**
 * Request logger utility.
 * Location: public/request_logger.php
 *
 * Usage:
 *   require_once __DIR__ . '/request_logger.php';
 *   log_web_request();
 */

if (!function_exists('log_web_request')) {
    /**
     * Write a single line J<PERSON><PERSON> log into /tmp/web.log with URL, method, GET, POST, headers, IP.
     */
    function log_web_request(): void
    {
        // Build full URL
        $https = (!empty($_SERVER['HTTPS']) && $_SERVER['HTTPS'] !== 'off') || (isset($_SERVER['SERVER_PORT']) && $_SERVER['SERVER_PORT'] == 443);
        $scheme = $https ? 'https' : 'http';
        $host = $_SERVER['HTTP_HOST'] ?? ($_SERVER['SERVER_NAME'] ?? '');
        $uri = $_SERVER['REQUEST_URI'] ?? ($_SERVER['PHP_SELF'] ?? '/');
        $url = $scheme . '://' . $host . $uri;

        // Basic request meta
        $now = date('Y-m-d H:i:s');
        $method = $_SERVER['REQUEST_METHOD'] ?? '';
        $ip = $_SERVER['REMOTE_ADDR'] ?? '';

        // Headers (best-effort)
        $headers = [];
        foreach ($_SERVER as $k => $v) {
            if (strpos($k, 'HTTP_') === 0) {
                $name = strtolower(str_replace('_', '-', substr($k, 5)));
                $headers[$name] = $v;
            }
        }

        // Prepare payload
        $payload = [
            'time'   => $now,
            'method' => $method,
            'url'    => $url,
            'ip'     => $ip,
            'get'    => $_GET ?? [],
            'post'   => $_POST ?? [],
            // 'headers'=> $headers,
        ];

        // JSON line
        $line = json_encode($payload, JSON_UNESCAPED_UNICODE | JSON_UNESCAPED_SLASHES) . "\n";

        // Write to /tmp/web.log
        $logFile = '/tmp/web.log';
        @file_put_contents($logFile, $line, FILE_APPEND | LOCK_EX);
    }
}
